import logging
import os
import socket
import threading
import time

import celery
import django_query_prefixer
from celery import Task, shared_task, signals
from celery.signals import after_task_publish, before_task_publish
from prometheus_client import CollectorRegistry, write_to_textfile
from prometheus_client.multiprocess import MultiProcessCollector

logger = logging.getLogger("rodoviaria")

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bp.settings")


class RodoviariaTask(Task):
    def before_start(self, task_id, args, kwargs):
        django_query_prefixer.set_prefix("task", f"{__name__}.{self.name}")

    def after_return(self, status, retval, task_id, args, kwargs, einfo):
        django_query_prefixer.remove_prefix("task")


app = celery.Celery("bp", task_cls=RodoviariaTask)
app.config_from_object("bp.settings_celery")
app.conf.imports = ("instrumentation.honeycomb.celery",)
app.autodiscover_tasks()


@shared_task(queue="bp_ping")
def ping_task():
    return "pong"


@after_task_publish.connect
def log_task_published(sender, headers, body, routing_key, **kwargs):
    if routing_key == "bp_link_trechos_classes":
        logger.info("Task published", extra={"task_id": headers["id"], "task_name": sender})


@before_task_publish.connect
def add_published_timestamp(headers, **kwargs):
    headers["timestamp"] = time.time()


metrics_dir = os.getenv("PROMETHEUS_METRICS_DIR", "/tmp")  # noqa: S108
metrics_interval = int(os.getenv("PROMETHEUS_METRICS_INTERVAL", "30"))


@signals.worker_init.connect
def start_prometheus_collector(*args, **kwargs):
    if "PROMETHEUS_METRICS_DIR" not in os.environ:
        return

    thread = threading.Thread(target=_flush_metrics)
    thread.daemon = True
    thread.start()


@signals.worker_shutdown.connect
def delete_prometheus_metrics(*args, **kwargs):
    filepath = _get_metrics_filepath()
    try:
        os.unlink(filepath)
    except FileNotFoundError:
        ...


def _flush_metrics():
    registry = CollectorRegistry()
    MultiProcessCollector(registry)

    while True:
        time.sleep(metrics_interval)
        filepath = _get_metrics_filepath()
        write_to_textfile(filepath, registry)


def _get_metrics_filepath():
    return os.path.join(metrics_dir, f"hostname-{socket.gethostname()}-pid-{os.getpid()}.prom")


@app.on_after_finalize.connect
def setup_periodic_tasks(sender, **kwargs):
    from rodoviaria import cron

    cron.setup()

import datetime
import decimal
import json
import logging
import os
import uuid

import celery
from beeline import traced
from django.conf import settings
from django.utils.functional import Promise as DjangoPromise
from kombu.serialization import register
from kombu.utils import json as kombu_json

logger = logging.getLogger("rodoviaria")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bp.settings")
buserdjango_app = celery.Celery("buserdjango", set_as_current=False)


class Config:
    broker_url = settings.BUSERDJANGO_CELERY_BROKER_URL
    task_always_eager = not broker_url

    timezone = settings.TIME_ZONE
    worker_prefetch_multiplier = 1
    worker_proc_alive_timeout = 30.0
    task_acks_late = 1
    task_eager_propagates = True
    worker_enable_remote_control = False
    worker_send_task_events = False
    task_time_limit = 1790
    task_annotations = {"*": {"expires": 12 * 60 * 60}}  # TTL de 1 dias
    task_serializer = "legacy_json"


buserdjango_app.config_from_object(Config)
buserdjango_app.conf.imports = ("instrumentation.honeycomb.celery",)


@buserdjango_app.task(
    queue="mkp_atualiza_trecho",
    name="core.service.rodoviaria_link_trecho_classe_svc.atualiza_trecho",
)
def atualiza_trecho(trecho_classe_id, to_close, preco=None, vagas=None, motivo_unmatch=None):
    # Essa é a assinatura da função do buser_django que vai atualizar o trecho
    # raise NotImplementedError("Implementação se encontra no buser_django")
    log_params = f"{trecho_classe_id=}, {preco=}, {vagas=}, {to_close=}, {motivo_unmatch=}"
    logger.info(
        "[ATUALIZAÇÃO DE TRECHO VIA RABBIT] função atualiza_trecho rodando do lado do buser_django %s", log_params
    )


@buserdjango_app.task(
    queue="mkp_atualiza_trecho_unico",
    name="core.service.rodoviaria_link_trecho_classe_svc.atualiza_trecho_unico",
)
def atualiza_trecho_unico(trecho_classe_id, to_close, preco=None, vagas=None, motivo_unmatch=None):
    # Essa é a assinatura da função do buser_django que vai atualizar o trecho
    # raise NotImplementedError("Implementação se encontra no buser_django")
    log_params = f"{trecho_classe_id=}, {preco=}, {vagas=}, {to_close=}, {motivo_unmatch=}"
    logger.info(
        "[ATUALIZAÇÃO DE TRECHO VIA RABBIT] função atualiza_trecho rodando do lado do buser_django %s", log_params
    )


@buserdjango_app.task(
    queue="mkp_atualiza_trecho",
    name="core.service.rodoviaria_link_trecho_classe_svc.atualiza_trechos_raw",
)
def atualiza_trechos_raw(origem_id, destino_id, data_str, trechos_raw, company_internal_id):
    logger.info(
        "[ATUALIZAÇÃO DE TRECHO VIA RABBIT] função atualiza_trechos_raw rodando do lado do buser_django",
        extra={
            "origem_id": origem_id,
            "destino_id": destino_id,
            "data_str": data_str,
            "trechos_raw": trechos_raw,
            "company_internal_id": company_internal_id,
        },
    )


@buserdjango_app.task(
    queue="mkp_atualiza_trecho_raw",
    name="core.service.rodoviaria_link_trecho_classe_svc.atualiza_trecho_raw",
)
def atualiza_trecho_raw(origem_id, destino_id, data_str, trechos_raw, company_internal_id):
    logger.info(
        "[ATUALIZAÇÃO DE TRECHO VIA RABBIT] função atualiza_trechos_raw rodando do lado do buser_django",
        extra={
            "origem_id": origem_id,
            "destino_id": destino_id,
            "data_str": data_str,
            "trechos_raw": trechos_raw,
            "company_internal_id": company_internal_id,
        },
    )


@buserdjango_app.task(
    queue="mkp_atualiza_trecho",
    name="core.service.rodoviaria_link_trecho_classe_svc.batch_atualiza_trechos",
)
def batch_atualiza_trechos(trechos_raw, company_internal_id):
    logger.info(
        "[ATUALIZAÇÃO DE TRECHO VIA RABBIT] função batch_atualiza_trechos rodando do lado do buser_django",
        extra={
            "trechos_raw": trechos_raw,
            "company_internal_id": company_internal_id,
        },
    )


@buserdjango_app.task(
    queue="mkp_atualiza_trecho_batch",
    name="core.service.rodoviaria_link_trecho_classe_svc.atualiza_trecho_batch",
)
def atualiza_trecho_batch(trechos_raw, company_internal_id):
    logger.info(
        "[ATUALIZAÇÃO DE TRECHO VIA RABBIT] função batch_atualiza_trechos rodando do lado do buser_django",
        extra={
            "trechos_raw": trechos_raw,
            "company_internal_id": company_internal_id,
        },
    )


class LegacyJSONEncoder(json.JSONEncoder):
    def default(
        self,
        o,
        dates=(datetime.datetime, datetime.date),
        times=(datetime.time,),
        textual=(decimal.Decimal, uuid.UUID, DjangoPromise),
        isinstance=isinstance,
        datetime=datetime.datetime,
        text_t=str,
    ):
        reducer = getattr(o, "__json__", None)
        if reducer is not None:
            return reducer()
        else:
            if isinstance(o, dates):
                if not isinstance(o, datetime):
                    o = datetime(o.year, o.month, o.day, 0, 0, 0, 0)
                r = o.isoformat()
                if r.endswith("+00:00"):
                    r = r[:-6] + "Z"
                return r
            elif isinstance(o, times):
                return o.isoformat()
            elif isinstance(o, textual):
                return text_t(o)

            return super().default(o)


_default_encoder = LegacyJSONEncoder


@traced("bp.legacy_dumps")
def legacy_dumps(s, _dumps=json.dumps, cls=None, **kwargs):
    """Serialize object to json string."""
    return _dumps(s, cls=cls or _default_encoder, **kwargs)


# def legacy_loads(s, _loads=json.loads, decode_bytes=True):
#     """Deserialize json from string."""
#     if isinstance(s, memoryview):
#         s = s.tobytes().decode("utf-8")
#     elif isinstance(s, bytearray):
#         s = s.decode("utf-8")
#     elif decode_bytes and isinstance(s, bytes):
#         s = s.decode("utf-8")

#     return _loads(s)


register(
    "legacy_json",
    legacy_dumps,
    kombu_json.loads,
    content_type="application/json",
    content_encoding="utf-8",
)

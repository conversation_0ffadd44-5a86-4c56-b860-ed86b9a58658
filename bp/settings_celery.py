import zlib

import kombu.compression
import kombu.serialization
import orjson
from decouple import config
from django.conf import settings
from kombu.utils import json as kombu_json

broker_url = settings.CELERY_BROKER_URL
task_always_eager = not broker_url

timezone = settings.TIME_ZONE
worker_prefetch_multiplier = 1
worker_proc_alive_timeout = 30.0
task_acks_late = 1
task_eager_propagates = True
worker_enable_remote_control = False
worker_send_task_events = False
task_time_limit = 1790
result_extended = True
result_chord_join_timeout = 60
result_chord_retry_interval = 5 * 60
result_expires = 36 * 60 * 60  # TTL de 36 horas
task_annotations = {"*": {"expires": 4 * 24 * 60 * 60}}  # TTL de 4 dias

result_backend = settings.RESULT_BACKEND

# https://docs.celeryproject.org/en/stable/userguide/configuration.html#worker-lost-wait
worker_lost_wait = config("CELERY_WORKER_LOST_WAIT", default=120, cast=int)

result_compression = "maybe-gzip"

# Redbeat
redbeat_redis_url = config("CELERY_REDBEAT_REDIS_URL", default=settings.REDIS_URL)
redbeat_key_prefix = "redbeat:rodoviaria"
beat_max_loop_interval = config("CELERY_BEAT_MAX_LOOP_INTERVAL", cast=int, default=5)

task_serializer = "orjson"  # default task serializer

accept_content = [
    "application/json",
    "json",
    "orjson",  # custom serializer
]


def _orjson_default(obj):
    for t, (marker, encoder) in kombu_json._encoders.items():
        if isinstance(obj, t):
            return encoder(obj) if marker is None else kombu_json._as(marker, encoder(obj))
    raise TypeError


def _orjson_loads(payload: str):
    obj = orjson.loads(payload)
    if isinstance(obj, dict):
        return kombu_json.object_hook(obj)
    return obj


kombu.serialization.register(
    "orjson",
    lambda v: orjson.dumps(v, default=_orjson_default, option=orjson.OPT_PASSTHROUGH_DATETIME),
    _orjson_loads,
    content_type="orjson",
)


def _try_gzip_decoder(payload):
    try:
        return zlib.decompress(payload)
    except zlib.error:
        return payload


kombu.compression.register(zlib.compress, _try_gzip_decoder, content_type="maybe-gzip")

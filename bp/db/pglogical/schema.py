import logging

from django.db.backends.postgresql.schema import DatabaseSchemaEditor
from django.db.transaction import TransactionManagementError

logger = logging.getLogger(__name__)


class PglogicalDatabaseSchemaEditor(DatabaseSchemaEditor):
    # Django does not support PostgreSQL database schemas but pglogical require full qualified
    # names for objects.
    sql_create_table = 'CREATE TABLE "public".%(table)s (%(definition)s)'
    sql_rename_table = 'ALTER TABLE "public".%(old_table)s RENAME TO %(new_table)s'
    sql_retablespace_table = 'ALTER TABLE "public".%(table)s SET TABLESPACE %(new_tablespace)s'
    sql_delete_table = "DROP TABLE %(table)s CASCADE"

    sql_create_column = 'ALTER TABLE "public".%(table)s ADD COLUMN %(column)s %(definition)s'
    sql_alter_column = 'ALTER TABLE "public".%(table)s %(changes)s'
    sql_alter_column_type = "ALTER COLUMN %(column)s TYPE %(type)s"
    sql_alter_column_null = "ALTER COLUMN %(column)s DROP NOT NULL"
    sql_alter_column_not_null = "ALTER COLUMN %(column)s SET NOT NULL"
    sql_alter_column_default = "ALTER COLUMN %(column)s SET DEFAULT %(default)s"
    sql_alter_column_no_default = "ALTER COLUMN %(column)s DROP DEFAULT"
    sql_alter_column_no_default_null = sql_alter_column_no_default
    sql_alter_column_collate = "ALTER COLUMN %(column)s TYPE %(type)s%(collation)s"
    sql_delete_column = 'ALTER TABLE "public".%(table)s DROP COLUMN %(column)s CASCADE'
    sql_rename_column = 'ALTER TABLE "public".%(table)s RENAME COLUMN %(old_column)s TO %(new_column)s'
    sql_update_with_default = 'UPDATE "public".%(table)s SET %(column)s = %(default)s WHERE %(column)s IS NULL'

    sql_unique_constraint = "UNIQUE (%(columns)s)%(deferrable)s"
    sql_check_constraint = "CHECK (%(check)s)"
    sql_delete_constraint = 'ALTER TABLE "public".%(table)s DROP CONSTRAINT %(name)s'
    sql_constraint = "CONSTRAINT %(name)s %(constraint)s"

    sql_create_check = 'ALTER TABLE "public".%(table)s ADD CONSTRAINT %(name)s CHECK (%(check)s)'
    sql_delete_check = sql_delete_constraint

    sql_create_unique = 'ALTER TABLE "public".%(table)s ADD CONSTRAINT %(name)s UNIQUE (%(columns)s)%(deferrable)s'
    sql_delete_unique = sql_delete_constraint

    sql_create_fk = (
        'ALTER TABLE "public".%(table)s ADD CONSTRAINT %(name)s FOREIGN KEY (%(column)s) '
        'REFERENCES "public".%(to_table)s (%(to_column)s)%(deferrable)s'
    )
    sql_create_inline_fk = None

    sql_create_unique_index = 'CREATE UNIQUE INDEX %(name)s ON "public".%(table)s (%(columns)s)%(include)s%(condition)s'

    sql_create_pk = 'ALTER TABLE "public".%(table)s ADD CONSTRAINT %(name)s PRIMARY KEY (%(columns)s)'
    sql_delete_pk = sql_delete_constraint

    sql_create_sequence = 'CREATE SEQUENCE "public".%(sequence)s'
    sql_delete_sequence = 'DROP SEQUENCE IF EXISTS "public".%(sequence)s CASCADE'
    sql_set_sequence_max = "SELECT setval('\"public\".%(sequence)s', MAX(%(column)s)) FROM %(table)s"
    sql_set_sequence_owner = 'ALTER SEQUENCE "public".%(sequence)s OWNED BY %(table)s.%(column)s'

    sql_create_index = (
        'CREATE INDEX %(name)s ON "public".%(table)s%(using)s (%(columns)s)%(include)s%(extra)s%(condition)s'
    )
    sql_create_index_concurrently = (
        'CREATE INDEX CONCURRENTLY "public".%(name)s ON "public".%(table)s%(using)s '
        "(%(columns)s)%(include)s%(extra)s%(condition)s"
    )
    sql_delete_index = 'DROP INDEX IF EXISTS "public".%(name)s'
    sql_delete_index_concurrently = 'DROP INDEX CONCURRENTLY IF EXISTS "public".%(name)s'

    # Setting the constraint to IMMEDIATE to allow changing data in the same
    # transaction.
    sql_create_column_inline_fk = (
        'CONSTRAINT %(name)s REFERENCES "public".%(to_table)s(%(to_column)s)%(deferrable)s'
        '; SET CONSTRAINTS "public".%(namespace)s%(name)s IMMEDIATE'
    )
    # Setting the constraint to IMMEDIATE runs any deferred checks to allow
    # dropping it in the same transaction.
    sql_delete_fk = (
        'SET CONSTRAINTS "public".%(name)s IMMEDIATE; ALTER TABLE "public".%(table)s DROP CONSTRAINT %(name)s'
    )

    sql_delete_procedure = 'DROP FUNCTION "public".%(procedure)s(%(param_types)s)'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def execute(self, sql, params=()):
        # Don't perform the transactional DDL check if SQL is being collected
        # as it's not going to be executed anyway.
        if not self.collect_sql and self.connection.in_atomic_block and not self.connection.features.can_rollback_ddl:
            raise TransactionManagementError(
                "Executing DDL statements while in a transaction on databases "
                "that can't perform a rollback is prohibited."
            )
        # Account for non-string statement objects.
        sql = str(sql).replace("'", "''")

        cmd, _ = sql.upper().strip().split(" ", 1)
        if cmd not in ("SELECT", "INSERT", "UPDATE", "DELETE"):
            sql = f"SELECT pglogical.replicate_ddl_command('{sql}', '{{default}}')"

        # Log the command we're running, then run it
        logger.debug("%s; (params %r)", sql, params, extra={"params": params, "sql": sql})
        if self.collect_sql:
            ending = "" if sql.rstrip().endswith(";") else ";"
            if params is not None:
                self.collected_sql.append((sql % tuple(map(self.double_quote_value, params))) + ending)
            else:
                self.collected_sql.append(sql + ending)
        else:
            with self.connection.cursor() as cursor:
                if params is not None:
                    sql = sql % tuple(map(self.double_quote_value, params))
                cursor.execute(sql)

    def double_quote_value(self, value):
        return self.quote_value(value).replace("'", "''")

"""
ASGI config for bp project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/howto/deployment/asgi/
"""

import os

from django.core.asgi import get_asgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bp.settings")

from django.conf import settings

if settings.HONEYCOMB_API_KEY:
    import instrumentation.honeycomb

    instrumentation.honeycomb.init()

application = get_asgi_application()

import logging

from cid.locals import get_cid

from commons.sanitization import sanitize_data
from rodoviaria.api import get_current_integration

integration_request_logger = logging.getLogger("rodoviaria.integration_request")


class CorrelationIdContextFilter(logging.Filter):
    def filter(self, record):
        record.correlation_id = get_cid()
        return True


class CurrentIntegrationContextFilter(logging.Filter):
    def filter(self, record):
        if integration := get_current_integration():
            record.integration = integration
        return True


def log_integration_request(
    *, integration, company_id, url, method, payload, params, response, include_response_body=False, error_message=None
):
    extra = {
        "api": integration,
        "company_id": company_id,
        "url": url,
        "method": method,
        "log_type": "request_log",
        "status": response.status_code,
        "elapsed": response.elapsed.total_seconds(),
        "error_message": error_message,
    }
    payload = sanitize_data(payload)

    # TODO tech-debt: esse try/except j<PERSON> <PERSON> feito no BaseRequest de cada integração
    try:
        response_data = response.json()
    except Exception:
        response_data = "no response"

    log_message = (
        f"{integration}: response of {method} on {url} with json {payload} and params {params} had status code"
        f" {response.status_code} and response: {response_data}"
    )

    if response.ok and not include_response_body:
        short_log_message = f"{integration}: response of {method} on {url} had status code {response.status_code}"
        log_message = short_log_message

    # faz sentido guardar o payload enviado e a resposta recebida sempre?
    integration_request_logger.info(
        log_message,
        extra=extra,
    )


def log_integration_request_exception(*, integration, company_id, url, method, payload, error_message):
    extra = {
        "api": integration,
        "company_id": company_id,
        "url": url,
        "method": method,
        "log_type": "request_log",
        "error_message": error_message,
    }
    payload = sanitize_data(payload)

    log_message = f"{integration}: response of {method} on {url} with json {payload} failed: {error_message}"

    # faz sentido guardar o payload enviado e a resposta recebida sempre?
    integration_request_logger.info(
        log_message,
        extra=extra,
    )

# def test_metrics_view_not_authorized(rf: RequestFactory):
#     request = rf.get("/metrics")

#     with pytest.raises(Http404):
#         views.prometheus_metrics(request)


# def test_metrics_view_ok(rf: RequestFactory):
#     request = rf.get("/metrics", HTTP_AUTHORIZATION=settings.PROMETHEUS_METRICS_AUTH)
#     response = views.prometheus_metrics(request)

#     assert response.status_code == 200
#     assert response.content.startswith(b"# HELP python_gc_objects_collected_total")

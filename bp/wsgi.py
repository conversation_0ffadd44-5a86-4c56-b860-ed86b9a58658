"""
WSGI config for bp project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bp.settings")

try:
    # TODO: apagar inicialização do beeline em código WSGI
    from uwsgidecorators import postfork

    @postfork
    def init_beeline():
        from django.conf import settings

        if settings.HONEYCOMB_API_KEY:
            import instrumentation.honeycomb

            instrumentation.honeycomb.init()

except ImportError:
    pass

application = get_wsgi_application()

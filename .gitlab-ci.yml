stages:
    - build
    - test
    - deploy-test
    - deploy-prod

variables:
  DOCKER_BUILDKIT: 1
  IMAGE_NAME: rodoviaria
  CI_IMAGE: 557869972717.dkr.ecr.us-east-2.amazonaws.com/rodoviaria:ci_${CI_COMMIT_SHA}
  RELEASE_IMAGE_BRANCH: 557869972717.dkr.ecr.us-east-2.amazonaws.com/rodoviaria:b_${CI_COMMIT_REF_SLUG}

.kaniko_config: &kaniko_config
  - mkdir -p /kaniko/.docker
  - |
    cat <<EOF > /kaniko/.docker/config.json
    {
        "credHelpers": {
            "557869972717.dkr.ecr.us-east-2.amazonaws.com": "ecr-login"
        },
        "auths": {
            "https://index.docker.io/v1/": {
                "auth": "$DOCKER_HUB_AUTH"
            }
        }
    }
    EOF

build:
  stage: build
  tags: [k8s]
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  interruptible: true
  script:
    - *kaniko_config
    - >-
      /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${CI_IMAGE}"
      --destination "${RELEASE_IMAGE_BRANCH}"
      --build-arg VERSION=${CI_COMMIT_SHA:0:10}
      --cache
      --cache-copy-layers
      --reproducible

lint_backend:
  stage: test
  tags: [k8s]
  needs: ['build']
  interruptible: true
  image: $CI_IMAGE
  variables:
    GIT_STRATEGY: none
  script:
    - cd /app
    - inv lint

lint_pyright:
  stage: test
  tags: [k8s]
  needs: ['build']
  interruptible: true
  image: $CI_IMAGE
  variables:
    GIT_STRATEGY: none
  script:
    - cd /app
    - apt update && apt install git -y
    - inv pyrightdiff
  allow_failure: true

test_backend:
  stage: test
  tags: [k8s]
  needs: ['build']
  interruptible: true
  variables:
    GIT_STRATEGY: none
    POSTGRES_USER: buser
    POSTGRES_DB: buser
    POSTGRES_PASSWORD: buser
    DB_USER: buser
    DB_NAME: buser
    DB_PASS: buser
    DB_HOST: localhost
    RODOVIARIA_DB_HOST: localhost
    # MEMCACHED_HOST: localhost:11211
    DB_PORT: 5432
  services:
    - name: postgis/postgis:14-3.2
      command: ["postgres", "-c", "fsync=off", "-c", "synchronous_commit=off", "-c", "full_page_writes=off", "-c", "autovacuum=off"]
    - name: memcached
  image: $CI_IMAGE
  script:
    - cd /app
    - wait-for-it $DB_HOST:$DB_PORT
    - pytest
        --numprocesses 8
        --reuse-db
        --verbose
        --durations=10
        --cov
        --cov-report=term
        --cov-report=html:./htmlcov/
        --cov-report=xml:./coverage.xml
        --junitxml=./test_report.xml
        --override-ini cache_dir=$CI_PROJECT_DIR/.pytest-cache
        --migrateci-storage commons.storage.MigrateCIStorage
        --migrateci-location rodoviaria
        --migrateci-verbose 1
  coverage: '/^TOTAL.*\s+(\d+\.?\d*\%)$/'
  artifacts:
    name: rodoviaria_coverage_${CI_COMMIT_SHA:0:10}
    when: always
    paths:
      - htmlcov/
    reports:
      junit: test_report.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
  cache:
    - paths:
      - .pytest-cache/

.deploy_backend_test:
  stage: deploy-test
  tags: [k8s]
  needs: [test_backend]
  image: ecarrara/deploytools
  before_script:
    - python3 -m pip install --upgrade pip setuptools wheel
    - python3 -m pip install --user ansible-core==2.16
    - python3 -m pip freeze | grep ansible
  environment:
    name: test/backend
  script:
    - chmod 0600 $SSH_DEPLOY_KEY
    - cd devops
    - ANSIBLE_HOST_KEY_CHECKING=False RODOVIARIA_ENVIRONMENT=test ansible-playbook --private-key $SSH_DEPLOY_KEY -i hosts/test_aws_ec2.yml -u ubuntu deploy.yml
    - ./scripts/honeycomb-marker.sh backend

deploy_backend_test:
  extends: .deploy_backend_test
  only: [dev]

deploy_backend_branch:
  extends: .deploy_backend_test
  except: [dev]
  when: manual

.release_test_docker_image:
  stage: deploy-test
  image: buserbrasil/aws-kubectl:latest
  tags: [k8s]
  variables:
    AWS_DEFAULT_REGION: us-east-2
  script:
    - MANIFEST=$(aws ecr batch-get-image --repository-name $IMAGE_NAME --image-ids imageTag=ci_${CI_COMMIT_SHA} --output json | python3 -c 'import sys, json; print(json.load(sys.stdin)["images"][0]["imageManifest"])')
    - aws ecr put-image --repository-name $IMAGE_NAME --image-tag test --image-manifest "$MANIFEST"

release_test_docker_image:
  extends: .release_test_docker_image
  needs: [deploy_backend_test]
  only: [dev]

release_branch_docker_image:
  extends: .release_test_docker_image
  needs: [deploy_backend_branch]
  except: [dev]

.deploy_celery_test:
  stage: deploy-test
  tags: [k8s]
  needs: [test_backend]
  image: ecarrara/deploytools
  environment:
    name: test/celery
  script:
    - chmod 0600 $SSH_DEPLOY_KEY
    - cd devops
    - ANSIBLE_HOST_KEY_CHECKING=False RODOVIARIA_ENVIRONMENT=test ansible-playbook --private-key $SSH_DEPLOY_KEY -i hosts/test_celery_aws_ec2.yml -u ubuntu deploy_celery.yml
    - ./scripts/honeycomb-marker.sh celery

deploy_celery_test:
  extends: .deploy_celery_test
  only: [dev]

deploy_celery_branch:
  extends: .deploy_celery_test
  except: [dev]
  when: manual

deploy_backend_prod:
  stage: deploy-prod
  when: manual
  tags: [k8s]
  only: [dev]
  needs: [test_backend]
  image: ecarrara/deploytools
  before_script:
    - pip install --user ansible
  environment:
    name: prod/backend
  script:
    - chmod 0600 $SSH_DEPLOY_KEY
    - cd devops
    - ANSIBLE_HOST_KEY_CHECKING=False RODOVIARIA_ENVIRONMENT=prod ansible-playbook --private-key $SSH_DEPLOY_KEY -i hosts/prod_aws_ec2.yml -u ubuntu deploy.yml
    - ./scripts/honeycomb-marker.sh backend

release_prod_docker_image:
  stage: deploy-prod
  tags: [k8s]
  only: [dev]
  image: buserbrasil/aws-kubectl:latest
  needs: [deploy_backend_prod]
  variables:
    AWS_DEFAULT_REGION: us-east-2
  script:
    - MANIFEST=$(aws ecr batch-get-image --repository-name $IMAGE_NAME --image-ids imageTag=ci_${CI_COMMIT_SHA} --output json | python3 -c 'import sys, json; print(json.load(sys.stdin)["images"][0]["imageManifest"])')
    - aws ecr put-image --repository-name $IMAGE_NAME --image-tag prod --image-manifest "$MANIFEST"

deploy_celery_prod:
  stage: deploy-prod
  tags: [k8s]
  when: manual
  only: [dev]
  needs: [test_backend]
  image: ecarrara/deploytools
  before_script:
    - pip install --user ansible
  environment:
    name: prod/celery
  script:
    - chmod 0600 $SSH_DEPLOY_KEY
    - cd devops
    - ANSIBLE_HOST_KEY_CHECKING=False RODOVIARIA_ENVIRONMENT=prod ansible-playbook --private-key $SSH_DEPLOY_KEY -i hosts/prod_celery_aws_ec2.yml -u ubuntu deploy_celery.yml
    - ./scripts/honeycomb-marker.sh celery

SANITIZE_KEYS = frozenset(
    {
        "Senha",
        "senha",
        "password",
    }
)
MASK = "[Filtered]"


def sanitize_data(data: dict) -> dict:
    """
    Mascara dados sensíveis em um dicionário, substituindo o valor de chaves específicas
    por uma máscara.

    Args:
        data (dict): Um dicionário que possui informações sensíveis.

    Returns:
        dict: O dicionário sanitizado.

    Example:
    >>> data = {
    ...     "Usuario": "buser",
    ...     "Senha": "minhasenhasecreta",
    ... }
    >>> masked_data = sanitize_data(data)
    >>> print(masked_data)
    {
        "Usuario": "buser",
        "Senha": "[Filtered]",
    }
    """
    if not isinstance(data, dict):
        return data

    sanitized = {}
    for key, value in data.items():
        if isinstance(value, dict):
            sanitized[key] = sanitize_data(value)
        elif isinstance(value, list):
            sanitized[key] = [sanitize_data(payload) for payload in value]
        elif key in SANITIZE_KEYS:
            sanitized[key] = MASK
        else:
            sanitized[key] = value

    return sanitized

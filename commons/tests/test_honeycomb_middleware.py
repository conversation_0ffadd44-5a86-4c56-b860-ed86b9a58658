from unittest import mock

from django.urls.resolvers import <PERSON>solver<PERSON><PERSON>


def test_honeycomb_middleware_view_not_decorated(rf, settings):
    from commons.middleware import honeycomb_middleware

    settings.HONEYCOMB_SAMPLE_RATE = 10

    def view(request): ...

    get_response = mock.MagicMock()
    middleware = honeycomb_middleware(get_response)

    request = rf.get("/")
    request.resolver_match = ResolverMatch(view, [], {})

    with mock.patch("random.random", return_value=0.09):
        middleware.process_view(request, view, [], {})
        middleware(request)

    get_response.assert_called_once_with(request)
    assert request.__honeycomb_sampled__ == (True, 10)


def test_honeycomb_middleware_view_decorated(rf):
    from commons.middleware import honeycomb_middleware

    def view(request): ...

    get_response = mock.MagicMock()
    middleware = honeycomb_middleware(get_response)

    request = rf.get("/")
    request.resolver_match = ResolverMatch(view, [], {})
    request.resolver_match.func.__honeycomb_sample_rate = 1  # type: ignore

    middleware.process_view(request, view, [], {})
    middleware(request)

    get_response.assert_called_once_with(request)
    assert request.__honeycomb_sampled__ == (True, 1)

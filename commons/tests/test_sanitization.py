import pytest

from commons.sanitization import MASK, sanitize_data


@pytest.mark.parametrize(
    "data,expected",
    [
        (
            {
                "username": "buser",
                "<PERSON><PERSON>": "senhasecreta",
            },
            {
                "username": "buser",
                "Senha": MASK,
            },
        ),
        (
            {
                "nested": {"chave": {"Senha": "senhasecreta", "outrachave": "valor"}},
            },
            {
                "nested": {"chave": {"Senha": MASK, "outrachave": "valor"}},
            },
        ),
        (
            ["field", 1],
            ["field", 1],
        ),
        (
            {
                "listVendasXmlEnvio": [
                    {
                        "IdSessaoOp": "C13B854E8844",
                        "PassagemXml": [
                            {"IdEstabelecimento": 64, "Telefone1": "11999990000"},
                            {"IdSessaoOp": 17, "Telefone1": {"IdSessaoOp": 42, "Telefone2": "11999990000"}},
                        ],
                    },
                ],
            },
            {
                "listVendasXmlEnvio": [
                    {
                        "IdSessaoOp": "C13B854E8844",
                        "PassagemXml": [
                            {"IdEstabelecimento": 64, "Telefone1": "11999990000"},
                            {"IdSessaoOp": 17, "Telefone1": {"IdSessaoOp": 42, "Telefone2": "11999990000"}},
                        ],
                    }
                ]
            },
        ),
    ],
)
def test_sanitize_data(data, expected):
    assert sanitize_data(data) == expected

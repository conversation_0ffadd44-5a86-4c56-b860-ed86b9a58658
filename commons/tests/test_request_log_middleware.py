import logging

import pytest
from django.contrib.auth.models import AnonymousUser
from django.http import HttpResponse, HttpResponseNotFound
from django.urls import ResolverMatch

from commons.middleware import request_log_middleware


@pytest.fixture(autouse=True, scope="module")
def setup():
    logger = logging.getLogger("rodoviaria")
    # Esse hack é necessário para a fixtura caplog funcionar.
    logger.propagate = True
    yield
    logger.propagate = False


@pytest.mark.parametrize("status_code", [200, 500])
def test_log_request(caplog, rf, status_code):
    def mock_view(request):
        return HttpResponse("testing", status=status_code)

    middleware = request_log_middleware(mock_view)
    request = rf.get("/test-view", HTTP_USER_AGENT="pytest")
    request.user = AnonymousUser()
    request.correlation_id = "cid"  # o middleware do django-cid define essa propriedade
    request.resolver_match = ResolverMatch(mock_view, args=(), kwargs={}, route="/test-view")
    middleware(request)

    log = caplog.records[0]
    assert log.method == "GET"
    assert log.status_code == status_code
    assert isinstance(log.duration, float)
    assert log.path == "/test-view"
    assert log.user_id is None
    assert log.user_agent == "pytest"
    assert log.route == "/test-view"
    assert log.message == f"GET /test-view {status_code}"
    assert log.correlation_id == "cid"


def test_log_path_not_found(rf, caplog):
    request = rf.get("/not-found")
    request.user = AnonymousUser()
    request.resolver_match = None
    request.correlation_id = None

    middleware = request_log_middleware(lambda _: HttpResponseNotFound())
    middleware(request)

    log = caplog.records[0]
    assert log.route is None
    assert log.path == "/not-found"
    assert log.status_code == 404


@pytest.mark.parametrize("method", ["POST", "GET"])
def test_log_query_params(rf, method, caplog):
    request = rf.generic(method, "/test?single=true&array=1&array=2")
    request.user = AnonymousUser()
    request.correlation_id = None
    middleware = request_log_middleware(lambda _: HttpResponse("ok"))
    middleware(request)

    log = caplog.records[0]
    assert log.method == method
    assert log.params == {"single": ["true"], "array": ["1", "2"]}
    assert log.message == f"{method} /test?single=true&array=1&array=2 200"

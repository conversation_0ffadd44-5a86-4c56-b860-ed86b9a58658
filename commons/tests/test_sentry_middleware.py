from unittest import mock

from commons.middleware import sentry_middleware


def test_set_correlation_id(rf):
    request = rf.get("/test")
    request.correlation_id = "cid"

    with mock.patch("commons.middleware.sentry_sdk.set_tag") as mock_set_tag:
        middleware = sentry_middleware(lambda _: None)
        middleware(request)

    mock_set_tag.assert_called_with("correlation_id", "cid")


def test_not_set_correlation_id_when_is_none(rf):
    request = rf.get("/test")
    request.correlation_id = None

    with mock.patch("commons.middleware.sentry_sdk.set_tag") as mock_set_tag:
        middleware = sentry_middleware(lambda _: None)
        middleware(request)

    mock_set_tag.assert_not_called()

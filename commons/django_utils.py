from django.core.exceptions import ValidationError
from django.core.paginator import Paginator


def error_str(ex):
    if isinstance(ex, ValidationError):
        return "; ".join(ex.messages)
    else:
        return str(ex)


def paginator_sort_by(queryset, sort_by, rows_per_page, page, descending):
    if sort_by:
        sort_by = sort_by.replace(".", "__")
        if descending:
            sort_by = f"-{sort_by}"
        queryset = queryset.order_by(sort_by)
    paginator = Paginator(queryset, per_page=rows_per_page)
    page = paginator.get_page(page)
    return page.object_list, paginator.count, paginator.num_pages

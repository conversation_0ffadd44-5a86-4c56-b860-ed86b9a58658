import logging
import random
import sys

from memoize import DEFAULT_TIMEOUT, Memoizer

logger = logging.getLogger("rodoviaria")


class LoggingMemoizer(Memoizer):
    def __init__(self, sample=0.1):
        self.sample = sample
        super().__init__()

    def set(self, key, value, timeout=DEFAULT_TIMEOUT):
        if random.random() < self.sample:  # noqa: S311
            logger.info("key %s size=%s timeout=%s", key, sys.getsizeof(value), timeout)
        return super().set(key, value)


class AsyncLoggingMemoizer(LoggingMemoizer):
    async def set(self, key, value, timeout=DEFAULT_TIMEOUT):
        if random.random() < self.sample:  # noqa: S311
            logger.info("key %s size=%s timeout=%s", key, sys.getsizeof(value), timeout)
        return await super().set(key, value)


# Memoizer instance
_logging_memoizer = LoggingMemoizer(sample=0.1)
_async_logging_memoizer = AsyncLoggingMemoizer(sample=0.1)

# Public objects
memoize_with_log = _logging_memoizer.memoize
async_memoize_with_log = _async_logging_memoizer.memoize

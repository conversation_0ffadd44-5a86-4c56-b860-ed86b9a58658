from datetime import datetime
from functools import lru_cache

import dateutil.parser
from django.utils import timezone
from zoneinfo import ZoneInfo

DIAS_SEMANA = [
    "domingo",
    "segunda-feira",
    "terça-feira",
    "quarta-feira",
    "quinta-feira",
    "sexta-feira",
    "sábado",
]


@lru_cache
def _default_timezone():
    # Otimização para evitar descobrir a timezone em toda chamada. O
    # `get_current_timezone` que acontece internamente é desnecessário enquanto
    # não mudamos a timezone do Django com `timezone.activate`.
    return timezone.get_default_timezone()


def to_default_tz(d):
    """Não utilizar essa função. Dê preferência por utilizar o
    to_default_tz_required que está tipado e levanta exceção em caso de None"""
    if d is None:
        return None
    if isinstance(d, str):
        d = dateutil.parser.parse(d)
    if isinstance(d, datetime):
        tz = _default_timezone()
        if timezone.is_naive(d):
            return timezone.make_aware(d, timezone=tz)
        else:
            return d.astimezone(timezone.get_default_timezone())
    else:
        return d


def to_default_tz_required(d: str | datetime) -> datetime:
    date_time_with_tz = to_default_tz(d)
    if date_time_with_tz is None:
        raise ValueError("d não pode ser None")
    return date_time_with_tz


def to_tz(d: datetime, tz: ZoneInfo.tzname) -> datetime:
    # TODO: tech-debt objeto None não pode invocar to_tz.
    # Melhor levantar uma exceção do que retornar tipo inconsistente
    # por enquanto está tipado errado
    if d is None:
        return None
    ptz = ZoneInfo(tz)
    return d.astimezone(ptz) if timezone.is_aware(d) else d.replace(tzinfo=ptz)


def timedelta_to_milliseconds(td):
    if td is None:
        return None
    return int(td.total_seconds() * 1000)


def replace_timezone(d: datetime | None, tz: str):
    if d is None:
        return None
    ptz = None
    if tz:
        ptz = ZoneInfo(tz)
    return d.replace(tzinfo=ptz)


def today():
    return timezone.localtime(timezone.now(), timezone=_default_timezone()).date()


def now():
    return timezone.now()


def today_midnight():
    return to_default_tz(midnight(datetime.now()))


def midnight(dt):
    return dt.replace(hour=0, minute=0, second=0, microsecond=0)

import logging
from datetime import datetime, timedelta
from functools import cache, wraps

from beeline import traced
from pybreaker import STATE_CLOSED, CircuitBreaker, CircuitBreakerError, CircuitBreakerListener, CircuitRedisStorage

from commons import redis
from rodoviaria.service.exceptions import RodoviariaConnectionError

buserlogger = logging.getLogger("rodoviaria.circuit_breaker")


class LogListener(CircuitBreakerListener):
    "Listener usado pra logar eventos de circuit breaker"

    def state_change(self, cb, old_state, new_state):
        buserlogger.info(
            "circuitbreaker %s state changed: %s -> %s",
            cb.name,
            old_state.name,
            new_state.name,
            extra={"current_state": cb.current_state},
        )

    def failure(self, cb, exception: Exception):
        buserlogger.info("circuitbreaker failure", extra={"error_message": str(exception)})


listener = LogListener()


class FakeLock:
    def acquire(self, blocking=True, timeout=-1):
        return True

    __enter__ = acquire

    def release(self): ...

    def __exit__(self, t, v, tb):
        self.release()


class LockLessCircuitBraker(CircuitBreaker):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._lock = FakeLock()


@cache
@traced("circuit_breaker._integracao_circuit_breaker")
def _integracao_circuit_breaker(
    namespace, fail_max=2, reset_timeout=20, expected_exceptions=(RodoviariaConnectionError,)
):
    circuit_breaker = LockLessCircuitBraker(
        fail_max=fail_max,
        reset_timeout=reset_timeout,
        exclude=[lambda e: not isinstance(e, expected_exceptions)],
        state_storage=CircuitRedisStorage(STATE_CLOSED, redis.get_master_client(), namespace=namespace),
        name=namespace,
    )
    circuit_breaker.add_listener(listener)
    return circuit_breaker


def circuit_method(fail_max=2, reset_timeout=20, expected_exceptions=(RodoviariaConnectionError,)):
    """Cria um decorator que faz a função decorada ter um :class:`pybreaker.CircuitBreaker` atrelado a ela.

    `Circuit breakers <<https://martinfowler.com/bliki/CircuitBreaker.html>>`_ evitam que chamadas
    que provavelmente vão falhar sejam executadas.

    Exemplo de uso::

        @circuit_method(max_fail=2)
        def kabum():
            response = requests.get("http://httpbin.org/status/503")
            response.raise_for_status()

        kabum() # raises RequestException
        kabum() # raises RequestException
        kabum() # raises CircuitBreakerError

    Args:
       fail_max: Número máximo de falhas consecutivas, após `fail_max` falhas o circuito transita para o estado OPEN.
       reset_timeout: Após `reset_timeout` segundos o circuit volta para o estado CLOSED se estiver em
       OPEN ou HALF_OPEN.
       expected_exceptions: Exceptions esperadas, que fazem o circuit fechar.
    """

    def decorator(method):
        @wraps(method)
        def wrapper(ref, *args, **kwargs):
            namespace = f"{ref!r}_{method.__name__}"
            circuit_breaker = _integracao_circuit_breaker(namespace, fail_max, reset_timeout, expected_exceptions)
            decorated_method = circuit_breaker(method)
            try:
                return decorated_method(ref, *args, **kwargs)
            except CircuitBreakerError as exc:
                remaining_seconds = 0
                opened_at = circuit_breaker._state_storage.opened_at
                if opened_at:
                    timeout = timedelta(seconds=circuit_breaker.reset_timeout)
                    delta = opened_at + timeout - datetime.utcnow()
                    remaining_seconds = delta.total_seconds()
                raise MyCircuitBreakerError(f"{str(exc)}. Please slow down!", remaining_seconds) from exc

        return wrapper

    return decorator


class MyCircuitBreakerError(CircuitBreakerError):
    def __init__(self, message: str, remaining_seconds: int):
        self.remaining_seconds = remaining_seconds
        super().__init__(message)

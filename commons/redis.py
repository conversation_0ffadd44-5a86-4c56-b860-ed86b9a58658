from functools import wraps

from beeline import traced
from django.core.cache import caches
from django_redis.pool import ConnectionFactory as DjangoConnectionFactory
from redis.client import Redis
from redis.exceptions import LockError

from commons.utils import build_key

TIMEOUT_MESSAGE = "Unable to acquire lock within the time specified"


class ConnectionFactory(DjangoConnectionFactory):
    """
    Connection factory que evita a conexão real com FAKE=True nas options.
    """

    def __init__(self, options):
        if options.get("FAKE"):
            self.get_connection = self._get_connection_fake
        super().__init__(options)

    def _get_connection_fake(self, params):
        return self.redis_client_cls(**self.redis_client_cls_kwargs)


@traced("redis.get_master_client")
def get_master_client() -> Redis:
    return caches["redis"].client.get_client()


def set_key(key, value, timeout, key_prefix=None):
    if not key:
        return
    if not key_prefix:
        key_prefix = ""
    caches["redis"].set(key=f"{key_prefix}{key}", value=value, timeout=timeout)


def get_key(key, key_prefix=None):
    if not key:
        return
    if not key_prefix:
        key_prefix = ""
    return caches["redis"].get(key=f"{key_prefix}{key}")


def delete_key(key, key_prefix=None):
    if not key:
        return
    if not key_prefix:
        key_prefix = ""
    return caches["redis"].delete(key=f"{key_prefix}{key}")


def set_many(data, timeout, *, key_prefix=None):
    if not data:
        return
    if key_prefix:
        data = {f"{key_prefix}{key}": value for key, value in data.items()}
    caches["redis"].set_many(data, timeout)


def get_many(keys, *, key_prefix=None):
    if not keys:
        return {}
    if key_prefix:
        keys = (f"{key_prefix}{key}" for key in keys)
    response = caches["redis"].get_many(keys)

    if key_prefix:
        response = {key[len(key_prefix) :]: value for key, value in response.items()}
    return response


def lock(key, *, max_wait_time=60, expire=600, redis_factory=get_master_client, except_timeout: bool = False):
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            cache_key = build_key(f, key, args, kwargs)
            try:
                with redis_factory().lock(cache_key, timeout=expire, blocking_timeout=max_wait_time):
                    return f(*args, **kwargs)
            except LockError as err:
                if not except_timeout or not str(err) == TIMEOUT_MESSAGE:
                    raise err

        return wrapper

    return decorator

import importlib
import logging
import random
from functools import lru_cache, wraps
from math import ceil

from celery.utils.log import get_task_logger
from django.conf import settings

from commons.circuit_breaker import MyCircuitBreakerError
from commons.token_bucket import NotEnoughTokens

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")
deploy_script = None


class DefaultQueueNames:
    ROTAS = "bp_descobrir_rotas"
    FETCH_ROTAS = "bp_fetch_rotas"
    LINK_TRECHOS_CLASSES = "bp_link_trechos_classes"
    UPDATE_PRICE_QUEUE_NAME = "bp_link_trechos_classes_atualiza_preco"
    HOT_UPDATE_PRICE_QUEUE_NAME = "bp_link_trechos_classes_atualiza_preco_hot"
    UPDATE_TOP_DIVERGENCIAS_QUEUE_NAME = "bp_link_trechos_classes_atualiza_top_divergencias"
    POS_COMPRA_UPDATE_PRICE = "bp_update_price_pos_compra"
    TRECHOS_VENDIDOS = "bp_trechos_vendidos"
    ROTINAS = "bp_rotinas"
    ADD_PAX = "bp_add_pax"
    ADD_PAX_HIBRIDO = "bp_add_pax_hibrido"
    CANCELA_PASSAGEM = "bp_cancela_passagem"
    STAFF_UPDATE_LINK = "bp_staff_update_link"
    CADASTROS_VEXADO = "bp_cadastros_vexado"
    FETCH_DATA_LIMITE = "bp_fetch_data_limite"
    REMANEJAMENTO = "bp_remanejamento"
    DESBLOQUEAR_POLTRONAS = "bp_desbloquear_poltrona"
    COMMIT_CALLBACK = "bp_commit_callback"
    MESSENGER = "bp_messenger"
    DESCOBRIR_OPERACAO = "bp_descobrir_operacao"
    BUSCAR_VIAGENS_SEARCH = "bp_buscar_viagens_search"
    ATUALIZA_PRECO_CHECKOUT = "bp_atualiza_preco_checkout"
    BUSCAR_PRECO_TRECHO_VENDIDO = "bp_buscar_preco_trecho_vendido"
    BUSCAR_DADOS_BPE = "bp_buscar_dados_bpe"

    class Hibrido:
        CANCELA_GRUPO_CLASSE = "bp_cancela_grupo_classe_hibrido"
        LINK_TRECHO_CLASSE = "bp_link_trecho_classe_hibrido"


class DefaultRateLimits:
    FETCH_ROTAS = "10/m"
    ADD_PAX = "30/m"
    ADD_PAX_HIBRIDO = "3/m"
    CANCELA_PASSAGEM = "30/m"
    LINK_TRECHOS_CLASSES = "120/m"
    CADASTROS_VEXADO = "30/m"
    ADD_MULTIPLE_PAX_HIBRIDO = "5/m"
    REMANEJAMENTO = "3/m"
    CANCELA_PASSAGENS_COM_ERRO = "10/m"
    MESSENGER = "15/m"
    BUSCAR_VIAGENS_SEARCH = "100/m"
    BUSCAR_DADOS_BPE = "30/m"

    class TrechosVendidos:
        RATE_LIMIT = "140/m"
        LOW_RATE_LIMIT = "70/m"

    class FetchDataLimite:
        RATE_LIMIT = "140/m"
        LOW_RATE_LIMIT = "140/m"

    class LinkTrechosClasses:
        HIGH_RATE_LIMIT = "150/m"
        RATE_LIMIT = "130/m"
        LOW_RATE_LIMIT = "100/m"

    class Praxio:
        ROTAS = "140/m"
        ROTINAS = "140/m"

    class Totalbus:
        ROTAS = "140/m"
        ROTINAS = "140/m"

    class Vexado:
        ROTAS = "10/m"
        ROTINAS = "140/m"

    class GuichePass:
        ROTAS = "140/m"
        ROTINAS = "140/m"

    class Smartbus:
        ROTAS = "140/m"
        ROTINAS = "140/m"

    class Eulabs:
        ROTAS = "140/m"
        ROTINAS = "140/m"


def get_exponential_backoff_interval(
    factor: int,
    retries: int,
    maximum: int,
    full_jitter: bool = False,
    offset: int = 0,
) -> int:
    """Calculate the exponential backoff wait time."""
    # Will be zero if factor equals 0
    countdown = min(maximum, factor * int(1.1**retries))
    # Full jitter according to
    # https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/
    if full_jitter:
        if offset < countdown:
            countdown = random.randrange(offset, countdown + 1)  # noqa: S311
        else:
            countdown = random.randrange(offset, offset + countdown + 1)  # noqa: S311
    # Adjust according to maximum wait time and account for negative values.
    return max(offset, countdown)


def retry(
    exceptions_type: tuple,
    max_retries: int = 50,
    min_delay: int = 20,
    max_factor_delay: int = 1800,
    factor=15,
    ignore_not_enough_token_count=False,
):
    def decorator(task):
        @wraps(task)
        def wrapper(*args, **kwargs):
            try:
                return task(*args, **kwargs)
            except exceptions_type as ex:
                otask = _get_original(task)
                offset = min_delay
                if isinstance(ex, (NotEnoughTokens, MyCircuitBreakerError)) and ex.remaining_seconds > 0:
                    offset = ceil(ex.remaining_seconds)
                if ignore_not_enough_token_count and isinstance(ex, NotEnoughTokens):
                    otask.request.retries -= 1
                countdown = get_exponential_backoff_interval(
                    factor=factor,
                    retries=otask.request.retries,
                    maximum=max_factor_delay,
                    full_jitter=True,
                    offset=offset,
                )
                task_logger.warning(
                    "Tentativa numero %s. Tentando novamente em %ss Exception: %s",
                    str(otask.request.retries + 1),
                    str(countdown),
                    str(ex),
                )
                return otask.retry(max_retries=max_retries, countdown=countdown)

        return wrapper

    return decorator


def _get_original(task):
    return getattr(importlib.import_module(task.__module__), task.__name__)


@lru_cache
def is_deployed_queue(queue_name):
    global deploy_script

    if deploy_script is None:
        devops_dir = settings.BASE_DIR / "devops"
        deploy_script = (devops_dir / "deploy_celery.yml").read_text()
        deploy_script += (devops_dir / "celery_company_queues.txt").read_text()
    _is_deployed_queue = queue_name in deploy_script
    if not _is_deployed_queue:
        logger.warning("fila não deployada: %s", queue_name)
        task_logger.warning("fila não deployada: %s", queue_name)
    return _is_deployed_queue

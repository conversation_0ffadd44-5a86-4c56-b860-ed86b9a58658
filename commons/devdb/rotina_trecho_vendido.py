from commons.devdb.rotina import (
    ROTINA_ROTA_INTEGRADA_GRAN_EXPRESS,
    ROTINA_ROTA_NAO_INTEGRADA_GRAN_EXPRESS,
)
from commons.devdb.trecho_vendido import (
    TRECHOS_VENDIDOS_ROTA_INTEGRADA_GRAN_EXPRESS,
    TRECHOS_VENDIDOS_ROTA_NAO_INTEGRADA_GRAN_EXPRESS,
)
from rodoviaria.models import RotinaTrechoVendido

id_incrementer = 31359281
ROTINA_TRECHO_VENDIDO_ROTA_NAO_INTEGRADA_GRAN_EXPRESS = []
for rotina in ROTINA_ROTA_NAO_INTEGRADA_GRAN_EXPRESS:
    for trecho_vendido in TRECHOS_VENDIDOS_ROTA_NAO_INTEGRADA_GRAN_EXPRESS:
        ROTINA_TRECHO_VENDIDO_ROTA_NAO_INTEGRADA_GRAN_EXPRESS.append(
            RotinaTrechoVendido(
                pk=id_incrementer,
                rotina_id=rotina.pk,
                trecho_vendido_id=trecho_vendido.pk,
                datetime_ida_trecho_vendido=rotina.datetime_ida,
                created_at="2024-02-06T00:00:12.128645-03:00",
            )
        )
        id_incrementer += 1

id_incrementer = 30355473
ROTINA_TRECHO_VENDIDO_ROTA_INTEGRADA_GRAN_EXPRESS = []
for rotina in ROTINA_ROTA_INTEGRADA_GRAN_EXPRESS:
    for trecho_vendido in TRECHOS_VENDIDOS_ROTA_INTEGRADA_GRAN_EXPRESS:
        ROTINA_TRECHO_VENDIDO_ROTA_INTEGRADA_GRAN_EXPRESS.append(
            RotinaTrechoVendido(
                pk=id_incrementer,
                rotina_id=rotina.pk,
                trecho_vendido_id=trecho_vendido.pk,
                datetime_ida_trecho_vendido=rotina.datetime_ida,
                created_at="2024-02-06T00:00:12.128645-03:00",
            )
        )
        id_incrementer += 1


ROTINA_TRECHO_VENDIDO_DATA = (
    ROTINA_TRECHO_VENDIDO_ROTA_NAO_INTEGRADA_GRAN_EXPRESS + ROTINA_TRECHO_VENDIDO_ROTA_INTEGRADA_GRAN_EXPRESS
)

from rodoviaria.models.core import Integracao

INTEGRACOES_DATA = [
    Integracao(
        pk=1,
        name="praxio",
        versao="1.0",
        use_low_rate_limit=False,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Integracao(
        pk=2,
        name="totalbus",
        versao="1.0",
        use_low_rate_limit=False,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Integracao(
        pk=3,
        name="guichepass",
        versao="1.0",
        use_low_rate_limit=False,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Integracao(
        pk=4,
        name="vexado",
        versao="1.0",
        use_low_rate_limit=False,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Integracao(
        pk=5,
        name="eulabs",
        versao="1.0",
        use_low_rate_limit=False,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Integracao(
        pk=1007,
        name="ti_sistemas",
        versao="1.0",
        use_low_rate_limit=False,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
]

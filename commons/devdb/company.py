from rodoviaria.models.core import Company

COMPANY_GRAN_EXPRESS = Company(
    pk=12,
    modelo_venda=Company.ModeloVenda.MARKETPLACE,
    integracao_id=1,  # Praxio,
    company_internal_id=4234,
    company_external_id=6002,
    name="Gran Express",
    url_base="https://oci-parceiros2.praxioluna.com.br/Autumn",
    created_at="2024-01-05T20:27:42.456466+00:00",
    updated_at="2024-01-05T20:27:42.456466+00:00",
    features=[
        "itinerario",
        "buscar_servico",
        "bpe",
        "add_pax_staff",
        "active",
        "atualizar_preco_checkout",
        "auto_integra_operacao",
    ],
    previous_features=None,
    previous_features_updated_at="2024-01-05T20:27:42.456466+00:00",
    max_percentual_divergencia=0,
)
COMPANY_DATA = [COMPANY_GRAN_EXPRESS]

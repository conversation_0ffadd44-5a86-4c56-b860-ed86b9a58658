from decimal import Decimal

from rodoviaria.models.core import Passagem

PASSAGEM_GRAN_EXPRESS_REPASSADO_DATA_VIAGEM = Passagem(
    pk=9324,
    trechoclasse_integracao_id=15430,  # TODO: Definir ID,
    company_integracao_id=12,
    buseiro_internal_id=7534,
    travel_internal_id=9688124,
    poltrona_external_id=2,
    localizador="NDOKASVIAGEMSNVKO",
    pedido_external_id="gldmflamsdfp.fmkdmfspdlfr",
    status=Passagem.Status.CONFIRMADA,
    valor_cheio=Decimal("110"),
    bpe_em_contingencia=False,
    created_at="2024-01-02T10:47:12-03:00",
    updated_at="2024-01-02T10:48:12",
    erro=None,
    erro_cancelamento=None,
    datetime_cancelamento=None,
    numero_passagem="4234234",
    preco_rodoviaria=Decimal("110"),
)
PASSAGEM_GRAN_EXPRESS_REPASSADO_DATA_COMPRA = Passagem(
    pk=9323,
    trechoclasse_integracao_id=15429,  # TODO: Definir ID,
    company_integracao_id=12,
    buseiro_internal_id=7534,
    travel_internal_id=9688123,
    poltrona_external_id=2,
    localizador="GDFFCOMPRAFDFS323",
    pedido_external_id="fsadasdagrjyhyj.gfsdfysdvcx",
    status=Passagem.Status.CONFIRMADA,
    valor_cheio=Decimal("110"),
    bpe_em_contingencia=False,
    created_at="2024-01-02T10:47:12-03:00",
    updated_at="2024-01-02T10:48:12",
    erro=None,
    erro_cancelamento=None,
    datetime_cancelamento=None,
    numero_passagem="64523423",
    preco_rodoviaria=Decimal("140"),
)

PASSAGEM_GRAN_EXPRESS = [
    PASSAGEM_GRAN_EXPRESS_REPASSADO_DATA_COMPRA,
    PASSAGEM_GRAN_EXPRESS_REPASSADO_DATA_VIAGEM,
    Passagem(
        pk=9325,
        trechoclasse_integracao_id=1623,  # TODO: Definir ID,
        company_integracao_id=12,
        buseiro_internal_id=4231,
        poltrona_external_id=2,
        travel_internal_id=8888,
        localizador="SOASKDPASDOMW",
        pedido_external_id="kmfkdmfolmrlw.sdlapsldpwls",
        status=Passagem.Status.CONFIRMADA,
        valor_cheio=Decimal("110"),
        bpe_em_contingencia=False,
        created_at="2024-01-02T10:47:12-03:00",
        updated_at="2024-01-02T10:48:12",
        erro=None,
        erro_cancelamento=None,
        datetime_cancelamento=None,
        numero_passagem="64534234",
        preco_rodoviaria=Decimal("110"),
    ),
]


PASSAGEM = PASSAGEM_GRAN_EXPRESS + [
    Passagem(
        pk=9321,
        trechoclasse_integracao_id=1623,  # TODO trocar o id depois que criar outros trechos classes
        company_integracao_id=12,
        buseiro_internal_id=1829,
        poltrona_external_id=1,
        travel_internal_id=9999,
        localizador="XSIB231XA",
        pedido_external_id="dkanskdnasoknde.aknsdoansinfiemsa",
        status=Passagem.Status.CONFIRMADA,
        valor_cheio=Decimal("110"),
        bpe_qrcode="http://bpe.qrcode/23123018312308021",
        bpe_monitriip_code="0930192031902390129039123",
        bpe_em_contingencia=False,
        created_at="2024-01-02T10:47:12-03:00",
        updated_at="2024-01-02T10:48:12",
        erro=None,
        erro_cancelamento=None,
        datetime_cancelamento=None,
        numero_passagem="**********",
        id_estabelecimento_external=None,
        multa=None,
        plataforma="4",
        provider_data={},
        preco_base=Decimal("100"),
        taxa_embarque=Decimal("5"),
        seguro=Decimal("0"),
        pedagio=Decimal("5"),
        outras_taxas=Decimal("0"),
        outros_tributos=Decimal("0"),
        preco_rodoviaria=Decimal("110"),
        preco_poltrona=Decimal("110"),
        desconto=Decimal("0"),
        chave_bpe="23123018312308021",
        numero_bpe="321",
        serie_bpe="394",
        protocolo_autorizacao="31231231232312",
        numero_bilhete="42312312",
        data_autorizacao="2024-01-02T10:48:04-03:00",
        prefixo="**********",
        linha="Sao Paulo x Rio de Janeiro",
        cnpj="62.950.905/0001-80",
        inscricao_estadual="812.318.531.540",
        endereco_empresa="Avenida Morvan Dias de Figueiredo 3177",
        nome_agencia="Jaqueira",
        tipo_emissao="NORMAL",
        tipo_taxa_embarque="qr_code",
        codigo_taxa_embarque="31231521312",
        numero_bilhete_embarque="42312312412312",
        origem="Sao Paulo",
        destino="Rio de Janeiro",
        data_hora_partida="2024-01-25T23:00:00",
    ),
    Passagem(
        pk=9322,
        trechoclasse_integracao_id=1623,  # TODO: Definir ID,
        company_integracao_id=12,
        buseiro_internal_id=4231,
        poltrona_external_id=2,
        travel_internal_id=8888,
        localizador="FESAD231ASD",
        pedido_external_id="fdfsrsdasdas.grdasdwsdasd",
        status=Passagem.Status.CANCELADA,
        valor_cheio=Decimal("110"),
        bpe_qrcode="http://bpe.qrcode/5342312312312",
        bpe_monitriip_code="5341231231242536575452342",
        bpe_em_contingencia=False,
        created_at="2024-01-02T10:47:12-03:00",
        updated_at="2024-01-02T10:48:12",
        erro=None,
        erro_cancelamento=None,
        datetime_cancelamento="2024-01-02T10:48:12-03:00",
        numero_passagem="*********",
        preco_rodoviaria=Decimal("110"),
    ),
    Passagem(
        pk=9326,
        trechoclasse_integracao_id=1623,  # TODO: Definir ID,
        company_integracao_id=12,
        buseiro_internal_id=4231,
        poltrona_external_id=2,
        travel_internal_id=8888,
        localizador="SOASKDPASDOMW",
        pedido_external_id="kmfkdmfolmrlw.sdlapsldpwls",
        status=Passagem.Status.ERRO,
        valor_cheio=Decimal("110"),
        bpe_em_contingencia=False,
        created_at="2024-01-02T10:47:12-03:00",
        updated_at="2024-01-02T10:48:12",
        erro="Nao foi possivel concluir a compra",
        erro_cancelamento=None,
        datetime_cancelamento=None,
        numero_passagem=None,
        preco_rodoviaria=Decimal("110"),
    ),
    Passagem(
        pk=9327,
        trechoclasse_integracao_id=1623,  # TODO: Definir ID,
        company_integracao_id=12,
        buseiro_internal_id=1829,
        poltrona_external_id=1,
        travel_internal_id=9999,
        localizador="FDSFDFWER3423",
        pedido_external_id="dsadasdasd.gdfsadfsada",
        status=Passagem.Status.CONFIRMADA,
        valor_cheio=Decimal("110"),
        bpe_qrcode="http://bpe.qrcode/543523643634324",
        bpe_monitriip_code="354152346234534",
        bpe_em_contingencia=True,
        created_at="2024-01-02T10:47:12-03:00",
        updated_at="2024-01-02T10:48:12",
        erro=None,
        erro_cancelamento=None,
        datetime_cancelamento=None,
        numero_passagem="**********",
        id_estabelecimento_external=None,
        multa=None,
        plataforma="4",
        provider_data={},
        preco_base=Decimal("100"),
        taxa_embarque=Decimal("5"),
        seguro=Decimal("0"),
        pedagio=Decimal("5"),
        outras_taxas=Decimal("0"),
        outros_tributos=Decimal("0"),
        preco_rodoviaria=Decimal("110"),
        preco_poltrona=Decimal("110"),
        desconto=Decimal("0"),
        chave_bpe="543523643634324",
        numero_bpe="321",
        serie_bpe="394",
        protocolo_autorizacao="54324123123123",
        numero_bilhete="4123421",
        data_autorizacao="2024-01-02T10:48:04-03:00",
        prefixo="**********",
        linha="Sao Paulo x Rio de Janeiro",
        cnpj="62.950.905/0001-80",
        inscricao_estadual="812.318.531.540",
        endereco_empresa="Avenida Morvan Dias de Figueiredo 3177",
        nome_agencia="Jaqueira",
        tipo_emissao="NORMAL",
        tipo_taxa_embarque="qr_code",
        codigo_taxa_embarque="31231521312",
        numero_bilhete_embarque="42312312412312",
        origem="Sao Paulo",
        destino="Rio de Janeiro",
        data_hora_partida="2024-01-25T23:00:00",
    ),
    Passagem(
        pk=9328,
        trechoclasse_integracao_id=1623,  # TODO: Definir ID,
        company_integracao_id=12,
        buseiro_internal_id=1829,
        poltrona_external_id=1,
        travel_internal_id=9999,
        localizador="JNFOKANSKDASKON",
        pedido_external_id="lmkspdaspdmll.ldmlsampdlms",
        status=Passagem.Status.CANCELADA,
        valor_cheio=Decimal("110"),
        bpe_qrcode="http://bpe.qrcode/83182378127387",
        bpe_monitriip_code="312587654378987654",
        bpe_em_contingencia=True,
        created_at="2024-01-02T10:47:12-03:00",
        updated_at="2024-01-02T10:48:12",
        erro=None,
        erro_cancelamento=None,
        datetime_cancelamento=None,
        numero_passagem="**********",
        id_estabelecimento_external=None,
        multa=None,
        plataforma="4",
        provider_data={},
        preco_base=Decimal("100"),
        taxa_embarque=Decimal("5"),
        seguro=Decimal("0"),
        pedagio=Decimal("5"),
        outras_taxas=Decimal("0"),
        outros_tributos=Decimal("0"),
        preco_rodoviaria=Decimal("110"),
        preco_poltrona=Decimal("110"),
        desconto=Decimal("0"),
        chave_bpe="83182378127387",
        numero_bpe="321",
        serie_bpe="394",
        protocolo_autorizacao="54324123123123",
        numero_bilhete="4123421",
        data_autorizacao="2024-01-02T10:48:04-03:00",
        prefixo="**********",
        linha="Sao Paulo x Rio de Janeiro",
        cnpj="62.950.905/0001-80",
        inscricao_estadual="812.318.531.540",
        endereco_empresa="Avenida Morvan Dias de Figueiredo 3177",
        nome_agencia="Jaqueira",
        tipo_emissao="NORMAL",
        tipo_taxa_embarque="qr_code",
        codigo_taxa_embarque="*********",
        numero_bilhete_embarque="6745234234234",
        origem="Sao Paulo",
        destino="Rio de Janeiro",
        data_hora_partida="2024-01-25T23:00:00",
    ),
]

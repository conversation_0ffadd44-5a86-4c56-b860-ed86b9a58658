from rodoviaria.models.core import TrechoClasse

TRECHOCLASSE_DATA = [
    TrechoClasse(
        pk=1523,
        trechoclasse_internal_id=1,
        grupo_id=1523,
        origem_id=1,
        destino_id=4,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        preco_rodoviaria=150,
        provider_data="{}",
        datetime_ida="2024-02-06T01:30:00-03:00",
        external_datetime_ida="2024-02-06T01:00:00-03:00",
        active=True,
    ),
    TrechoClasse(
        pk=1623,
        trechoclasse_internal_id=2,
        grupo_id=1523,
        origem_id=4,
        destino_id=2,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        preco_rodoviaria=170,
        provider_data="{}",
        datetime_ida="2024-02-06T01:30:00-03:00",
        external_datetime_ida="2024-02-06T01:00:00-03:00",
        active=True,
    ),
    TrechoClasse(
        pk=1542,
        trechoclasse_internal_id=3,
        grupo_id=1523,
        origem_id=2,
        destino_id=5,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        preco_rodoviaria=180,
        provider_data="{}",
        datetime_ida="2024-02-06T01:30:00-03:00",
        external_datetime_ida="2024-02-06T01:00:00-03:00",
        active=True,
    ),
    TrechoClasse(
        pk=1239,
        trechoclasse_internal_id=4,
        grupo_id=1523,
        origem_id=4,
        destino_id=3,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        preco_rodoviaria=110,
        provider_data="{}",
        datetime_ida="2024-02-06T01:30:00-03:00",
        external_datetime_ida="2024-02-06T01:00:00-03:00",
        active=True,
    ),
    TrechoClasse(
        pk=1192,
        trechoclasse_internal_id=5,
        grupo_id=1523,
        origem_id=1,
        destino_id=5,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        preco_rodoviaria=110,
        provider_data="{}",
        datetime_ida="2024-02-06T01:30:00-03:00",
        external_datetime_ida="2024-02-06T01:00:00-03:00",
        active=True,
    ),
    TrechoClasse(
        pk=15429,
        trechoclasse_internal_id=150154,
        grupo_id=1524,
        origem_id=5,
        destino_id=4,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        preco_rodoviaria=140,
        provider_data="{}",
        datetime_ida="2024-02-06T01:30:00-03:00",
        external_datetime_ida="2024-02-06T01:00:00-03:00",
        active=True,
    ),
    TrechoClasse(
        pk=15430,
        trechoclasse_internal_id=150155,
        grupo_id=1523,
        origem_id=5,
        destino_id=4,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        preco_rodoviaria=180,
        provider_data="{}",
        datetime_ida="2024-02-06T01:30:00-03:00",
        external_datetime_ida="2024-02-06T01:00:00-03:00",
        active=True,
    ),
]

from commons.devdb.company import COMPANY_GRAN_EXPRESS
from rodoviaria.models.core import Cidade, CidadeInternal

CIDADES_INTERAL_GRAN_EXPRESS_DATA = [
    CidadeInternal(
        pk=1750,
        name="Comodoro",
        uf="MT",
        sigla="DOR",
        timezone="America/Cuiaba",
        city_code_ibge=5103304,
        uf_code_ibge=51,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=3767,
        name="Campos de Júlio",
        uf="MT",
        sigla="CJL",
        timezone="America/Cuiaba",
        city_code_ibge=5102686,
        uf_code_ibge=51,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=1411,
        name="Sapezal",
        uf="MT",
        sigla="CEZ",
        timezone="America/Cuiaba",
        city_code_ibge=5107875,
        uf_code_ibge=51,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
]

CIDADES_INTERNAL_DATA = CIDADES_INTERAL_GRAN_EXPRESS_DATA + [
    CidadeInternal(
        pk=15,
        name="Belo Horizonte",
        uf="MG",
        sigla="BHZ",
        timezone="America/Sao_Paulo",
        city_code_ibge=3106200,
        uf_code_ibge=31,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=17,
        name="Ipatinga",
        uf="MG",
        sigla="IPA",
        timezone="America/Sao_Paulo",
        city_code_ibge=3131307,
        uf_code_ibge=31,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=20,
        name="Ribeirão Preto",
        uf="SP",
        sigla="RAO",
        timezone="America/Sao_Paulo",
        city_code_ibge=3543402,
        uf_code_ibge=35,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=39,
        name="Rio de Janeiro",
        uf="RJ",
        sigla="RIO",
        timezone="America/Sao_Paulo",
        city_code_ibge=3304557,
        uf_code_ibge=33,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=16,
        name="São Paulo",
        uf="SP",
        sigla="SAO",
        timezone="America/Sao_Paulo",
        city_code_ibge=3550308,
        uf_code_ibge=35,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=35,
        name="São José dos Campos",
        uf="SP",
        sigla="SJK",
        timezone="America/Sao_Paulo",
        city_code_ibge=3549904,
        uf_code_ibge=35,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=5554,
        name="Senador José Bento",
        uf="MG",
        sigla="SNE",
        timezone="America/Sao_Paulo",
        city_code_ibge=3165800,
        uf_code_ibge=31,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    CidadeInternal(
        pk=95,
        name="Niterói",
        uf="RJ",
        sigla="NTR",
        timezone="America/Sao_Paulo",
        city_code_ibge=3303302,
        uf_code_ibge=33,
        updated_at="2024-01-05T07:00:00-03:00",
    ),
]

GRAN_EXPRESS_CIDADES_DATA = [
    Cidade(
        pk=6624,
        id_external="75",
        name="Comodoro - Mt -",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=1750,
        timezone="America/Cuiaba",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=6678,
        id_external="151",
        name="Campos De Julio - Mt -",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=3767,
        timezone="America/Cuiaba",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=6626,
        id_external="78",
        name="Sapezal - Mt -",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=1411,
        timezone="America/Cuiaba",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
]
CIDADES_DATA = GRAN_EXPRESS_CIDADES_DATA + [
    Cidade(
        pk=1,
        id_external="X",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=15,
        name="Belo Horizonte",
        timezone="America/Sao_Paulo",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=2,
        id_external="X",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=17,
        name="Ipatinga",
        timezone="America/Sao_Paulo",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=3,
        id_external="X",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=20,
        name="Ribeirão Preto",
        timezone="America/Sao_Paulo",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=4,
        id_external="X",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=39,
        name="Rio de Janeiro",
        timezone="America/Sao_Paulo",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=5,
        id_external="X",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=16,
        name="São Paulo",
        timezone="America/Sao_Paulo",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=6,
        id_external="X",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=35,
        name="São José dos Campos",
        timezone="America/Sao_Paulo",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=7,
        id_external="X",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=5554,
        name="Senador José Bento",
        timezone="America/Sao_Paulo",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Cidade(
        pk=8,
        id_external="X",
        company_id=COMPANY_GRAN_EXPRESS.pk,
        cidade_internal_id=95,
        name="Niterói",
        timezone="America/Sao_Paulo",
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
]

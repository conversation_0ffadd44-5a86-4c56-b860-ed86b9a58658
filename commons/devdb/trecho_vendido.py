from datetime import timedelta

from commons.devdb.rota import ROTA_INTEGRADA_GRAN_EXPRESS, ROTA_NAO_INTEGRADA_GRAN_EXPRESS
from rodoviaria.models import TrechoVendido

TRECHOS_VENDIDOS_ROTA_INTEGRADA_GRAN_EXPRESS = [
    TrechoVendido(
        pk=1177539,
        rota_id=ROTA_INTEGRADA_GRAN_EXPRESS.id,
        id_internal=1172447,
        tipo_assento_id=5786152,
        origem_id=7100,
        destino_id=7102,
        classe="BASICO",
        capacidade_classe=30,
        distancia=155.0,
        duracao=timedelta(hours=2, minutes=45),
        preco=559,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        ativo=True,
    ),
    TrechoVendido(
        pk=1177540,
        rota_id=ROTA_INTEGRADA_GRAN_EXPRESS.id,
        id_internal=1172449,
        tipo_assento_id=5786152,
        origem_id=7100,
        destino_id=7154,
        classe="BASICO",
        capacidade_classe=30,
        distancia=1810.0,
        duracao=timedelta(hours=1),
        preco=259,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        ativo=True,
    ),
    TrechoVendido(
        pk=1177538,
        rota_id=ROTA_INTEGRADA_GRAN_EXPRESS.id,
        id_internal=1172448,
        tipo_assento_id=5786152,
        origem_id=7102,
        destino_id=7154,
        classe="BASICO",
        capacidade_classe=30,
        distancia=1810.0,
        duracao=timedelta(hours=1),
        preco=159,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        ativo=True,
    ),
]
TRECHOS_VENDIDOS_ROTA_NAO_INTEGRADA_GRAN_EXPRESS = [
    TrechoVendido(
        pk=1179297,
        rota_id=ROTA_NAO_INTEGRADA_GRAN_EXPRESS.id,
        id_internal=None,
        tipo_assento_id=5786152,
        origem_id=7100,
        destino_id=7102,
        classe="BASICO",
        capacidade_classe=30,
        distancia=1810.0,
        duracao=timedelta(hours=1),
        preco=559,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        ativo=True,
    ),
    TrechoVendido(
        pk=1179296,
        rota_id=ROTA_NAO_INTEGRADA_GRAN_EXPRESS.id,
        id_internal=None,
        tipo_assento_id=5786152,
        origem_id=7100,
        destino_id=7154,
        classe="BASICO",
        capacidade_classe=30,
        distancia=1810.0,
        duracao=timedelta(hours=1),
        preco=259,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        ativo=True,
    ),
    TrechoVendido(
        pk=1179298,
        rota_id=ROTA_NAO_INTEGRADA_GRAN_EXPRESS.id,
        id_internal=None,
        tipo_assento_id=5786152,
        origem_id=7102,
        destino_id=7154,
        classe="BASICO",
        capacidade_classe=30,
        distancia=1810.0,
        duracao=timedelta(hours=1),
        preco=159,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        ativo=True,
    ),
]
TRECHOS_VENDIDOS_DATA = TRECHOS_VENDIDOS_ROTA_NAO_INTEGRADA_GRAN_EXPRESS + TRECHOS_VENDIDOS_ROTA_INTEGRADA_GRAN_EXPRESS

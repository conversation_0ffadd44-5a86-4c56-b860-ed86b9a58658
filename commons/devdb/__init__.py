from .checkpoint import CH<PERSON><PERSON><PERSON><PERSON><PERSON>_DATA
from .cidade import <PERSON>IDADES_DATA, CIDADES_INTERNAL_DATA
from .company import COMPANY_DATA
from .grupo import GRUPO_DATA
from .integracao import INTEGRACOES_DATA
from .local_embarque import LOCALEMBARQUE_DATA
from .login import LOGIN_DATA
from .passagens import PASSAG<PERSON>
from .rota import ROTA_DATA
from .rotina import ROTINA_DATA
from .rotina_trecho_vendido import ROTINA_TRECHO_VENDIDO_DATA
from .tipo_assento import TIPOS_ASSENTOS_DATA
from .trecho_classe import TRECHOCLASSE_DATA
from .trecho_vendido import TRECHOS_VENDIDOS_DATA

ALL_DATA = [
    INTEGRACOES_DATA,
    COMPANY_DATA,
    LOGIN_DATA,
    CIDADES_INTERNAL_DATA,
    CIDADES_DATA,
    LOCALEMBARQUE_DATA,
    GRUPO_DATA,
    TRECHOCLASSE_DATA,
    PASSAGEM,
    TIPOS_ASSENTOS_DATA,
    ROTA_DATA,
    CHECKPOINT_DATA,
    TRECHOS_VENDIDOS_DATA,
    ROTINA_DATA,
    ROTINA_TRECHO_VENDIDO_DATA,
]

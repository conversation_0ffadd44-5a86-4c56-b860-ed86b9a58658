from datetime import timedelta

from commons.devdb.rota import (
    ROTA_INTEGRADA_GRAN_EXPRESS,
    ROTA_NAO_INTEGRADA_GRAN_EXPRESS,
)
from rodoviaria.models import Checkpoint

CHECKPOINTS_INTEGRADA_GRAN_EXPRESS = [
    Checkpoint(
        pk=3020834,
        rota_id=ROTA_INTEGRADA_GRAN_EXPRESS.pk,
        local_id=7100,
        idx=0,
        internal_id=None,
        departure="2024-06-18T06:20:00-03:00",
        arrival=None,
        distancia_km=None,
        duracao=timedelta(hours=0),
        tempo_embarque=timedelta(minutes=0),
        id_external="75",
        nickname="Comodoro - Mt",
        name="Comodoro - Mt",
        uf=None,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Checkpoint(
        pk=3020835,
        rota_id=ROTA_INTEGRADA_GRAN_EXPRESS.pk,
        local_id=7154,
        idx=1,
        internal_id=None,
        departure="2024-06-18T08:05:00-03:00",
        arrival="2024-06-18T07:55:00-03:00",
        distancia_km=95,
        duracao=timedelta(hours=1, minutes=35),
        tempo_embarque=timedelta(minutes=10),
        id_external="151",
        nickname="Campos De Julio - Mt",
        name="Campos De Julio - Mt",
        uf=None,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Checkpoint(
        pk=3020836,
        rota_id=ROTA_INTEGRADA_GRAN_EXPRESS.pk,
        local_id=7102,
        idx=2,
        internal_id=None,
        departure=None,
        arrival="2024-06-18T09:05:00-03:00",
        distancia_km=60,
        duracao=timedelta(hours=1),
        tempo_embarque=timedelta(hours=0),
        id_external="78",
        nickname="Sapezal - Mt",
        name="Sapezal - Mt",
        uf=None,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
]

CHECKPOINTS_NAO_INTEGRADA_GRAN_EXPRESS = [
    Checkpoint(
        pk=3135661,
        rota_id=ROTA_NAO_INTEGRADA_GRAN_EXPRESS.pk,
        local_id=7100,
        idx=0,
        internal_id=None,
        departure="2024-06-18T06:30:00-03:00",
        arrival=None,
        distancia_km=None,
        duracao=timedelta(hours=0),
        tempo_embarque=timedelta(minutes=0),
        id_external="75",
        nickname="Comodoro - Mt",
        name="Comodoro - Mt",
        uf=None,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Checkpoint(
        pk=3135662,
        rota_id=ROTA_NAO_INTEGRADA_GRAN_EXPRESS.pk,
        local_id=7154,
        idx=1,
        internal_id=None,
        departure="2024-06-18T08:00:00-03:00",
        arrival="2024-06-18T07:50:00-03:00",
        distancia_km=80,
        duracao=timedelta(hours=1, minutes=20),
        tempo_embarque=timedelta(minutes=10),
        id_external="151",
        nickname="Campos De Julio - Mt",
        name="Campos De Julio - Mt",
        uf=None,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
    Checkpoint(
        pk=3135663,
        rota_id=ROTA_NAO_INTEGRADA_GRAN_EXPRESS.pk,
        local_id=7102,
        idx=2,
        internal_id=None,
        departure=None,
        arrival="2024-06-18T09:00:00-03:00",
        distancia_km=60,
        duracao=timedelta(hours=1),
        tempo_embarque=timedelta(hours=0),
        id_external="78",
        nickname="Sapezal - Mt",
        name="Sapezal - Mt",
        uf=None,
        created_at="2024-01-05T07:00:00-03:00",
        updated_at="2024-01-05T07:00:00-03:00",
    ),
]
CHECKPOINT_DATA = CHECKPOINTS_NAO_INTEGRADA_GRAN_EXPRESS + CHECKPOINTS_INTEGRADA_GRAN_EXPRESS

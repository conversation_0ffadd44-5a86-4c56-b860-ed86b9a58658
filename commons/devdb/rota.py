from commons.devdb.company import COMPANY_GRAN_EXPRESS
from rodoviaria.models import Rota

ROTA_NAO_INTEGRADA_GRAN_EXPRESS = Rota(
    pk=16584,
    company_id=COMPANY_GRAN_EXPRESS.pk,
    id_hash="COMSPZL0636712a59870127dabe95bc50512060",
    id_internal=None,
    id_external="108934",
    ativo=True,
    data_limite=None,
    created_at="2024-02-06T00:00:12.128645-03:00",
    updated_at="2024-02-06T00:00:12.128645-03:00",
    provider_data="""[
    {
        "DataBloqSemPassageiro": "0001-01-01T00:00:00",
        "HoraBloqSemPassageiro": null,
        "IDViagem": 108934,
        "DataPartida": "2024-06-06T05:30:00",
        "HoraPartida": "0530",
        "Sentido": 0,
        "Plataforma": "",
        "HoraChegada": null,
        "ControlaPoltrona": 0,
        "ControlaPassageiros": 0,
        "ControlaCliente": 0,
        "Localidade": {
            "ListEstabelecimentos": null,
            "BilheteEmbW2i": 0,
            "IDLocalidade": 75,
            "Descricao": "COMODORO - MT",
            "Sigla": "COM",
            "IdRegiao": null,
            "Uf": "MT",
            "IdEstabelecimento": 0,
            "IdCidade": 5103304,
            "TxEmbIdoso50": 1,
            "TxEmbIdoso100": 1,
            "TxEmbPasseLivre": 1,
            "PedagioIdoso100": 1,
            "PedagioIdoso50": 1,
            "Codigo": 0,
            "AgenciasCargas": null,
            "LastUpdate": null,
            "TxPedagioPasseLivre": null,
            "CodigoSgltar": 0,
            "TxEmb50Idoso50": 0,
            "TxEmb50Def50": 0,
            "TxEmb50Estud50": 0,
            "TxEmb50Jov50": 0,
            "TxEmbIdosoDef": 0
        },
        "DataChegada": "0001-01-01T00:00:00",
        "Obs": null,
        "HoraTolerancia": null,
        "DataExibirViagem": "0001-01-01T00:00:00",
        "HoraExibirViagem": null,
        "QtdPoltronaBloqueio": 0,
        "Comentario": ""
    },
    {
        "DataBloqSemPassageiro": "0001-01-01T00:00:00",
        "HoraBloqSemPassageiro": null,
        "IDViagem": 108934,
        "DataPartida": "2024-06-06T07:00:00",
        "HoraPartida": "0700",
        "Sentido": 0,
        "Plataforma": "",
        "HoraChegada": null,
        "ControlaPoltrona": 0,
        "ControlaPassageiros": 0,
        "ControlaCliente": 0,
        "Localidade": {
            "ListEstabelecimentos": null,
            "BilheteEmbW2i": 0,
            "IDLocalidade": 151,
            "Descricao": "CAMPOS DE JULIO - MT",
            "Sigla": "CPJ",
            "IdRegiao": null,
            "Uf": "MT",
            "IdEstabelecimento": 0,
            "IdCidade": 5102686,
            "TxEmbIdoso50": 0,
            "TxEmbIdoso100": 0,
            "TxEmbPasseLivre": 0,
            "PedagioIdoso100": 0,
            "PedagioIdoso50": 0,
            "Codigo": 0,
            "AgenciasCargas": null,
            "LastUpdate": null,
            "TxPedagioPasseLivre": null,
            "CodigoSgltar": 0,
            "TxEmb50Idoso50": 0,
            "TxEmb50Def50": 0,
            "TxEmb50Estud50": 0,
            "TxEmb50Jov50": 0,
            "TxEmbIdosoDef": 0
        },
        "DataChegada": "0001-01-01T00:00:00",
        "Obs": null,
        "HoraTolerancia": null,
        "DataExibirViagem": "0001-01-01T00:00:00",
        "HoraExibirViagem": null,
        "QtdPoltronaBloqueio": 0,
        "Comentario": ""
    },
    {
        "DataBloqSemPassageiro": "0001-01-01T00:00:00",
        "HoraBloqSemPassageiro": null,
        "IDViagem": 108934,
        "DataPartida": "2024-06-06T08:00:00",
        "HoraPartida": "0800",
        "Sentido": 0,
        "Plataforma": "",
        "HoraChegada": null,
        "ControlaPoltrona": 0,
        "ControlaPassageiros": 0,
        "ControlaCliente": 0,
        "Localidade": {
            "ListEstabelecimentos": null,
            "BilheteEmbW2i": 0,
            "IDLocalidade": 78,
            "Descricao": "SAPEZAL - MT",
            "Sigla": "SPZL",
            "IdRegiao": null,
            "Uf": "MT",
            "IdEstabelecimento": 0,
            "IdCidade": 5107875,
            "TxEmbIdoso50": 0,
            "TxEmbIdoso100": 0,
            "TxEmbPasseLivre": 0,
            "PedagioIdoso100": 1,
            "PedagioIdoso50": 1,
            "Codigo": 0,
            "AgenciasCargas": null,
            "LastUpdate": null,
            "TxPedagioPasseLivre": null,
            "CodigoSgltar": 0,
            "TxEmb50Idoso50": 0,
            "TxEmb50Def50": 0,
            "TxEmb50Estud50": 0,
            "TxEmb50Jov50": 0,
            "TxEmbIdosoDef": 0
        },
        "DataChegada": "0001-01-01T00:00:00",
        "Obs": null,
        "HoraTolerancia": null,
        "DataExibirViagem": "0001-01-01T00:00:00",
        "HoraExibirViagem": null,
        "QtdPoltronaBloqueio": 0,
        "Comentario": ""
    }
]""",
)
ROTA_INTEGRADA_GRAN_EXPRESS = Rota(
    pk=13060,
    company_id=COMPANY_GRAN_EXPRESS.pk,
    id_hash="COMSPZLe5d2a1d71c9596d635576952307e339d",
    id_internal=83767,
    id_external="107209",
    ativo=True,
    data_limite="2024-07-30",
    created_at="2024-02-06T00:00:12.128645-03:00",
    updated_at="2024-02-06T00:00:12.128645-03:00",
    provider_data="""[
    {
        "DataBloqSemPassageiro": "0001-01-01T00:00:00",
        "HoraBloqSemPassageiro": null,
        "IDViagem": 107209,
        "DataPartida": "2024-06-18T05:20:00",
        "HoraPartida": "0520",
        "Sentido": 0,
        "Plataforma": "",
        "HoraChegada": null,
        "ControlaPoltrona": 0,
        "ControlaPassageiros": 0,
        "ControlaCliente": 0,
        "Localidade": {
            "ListEstabelecimentos": null,
            "BilheteEmbW2i": 0,
            "IDLocalidade": 75,
            "Descricao": "COMODORO - MT",
            "Sigla": "COM",
            "IdRegiao": null,
            "Uf": "MT",
            "IdEstabelecimento": 0,
            "IdCidade": 5103304,
            "TxEmbIdoso50": 1,
            "TxEmbIdoso100": 1,
            "TxEmbPasseLivre": 1,
            "PedagioIdoso100": 1,
            "PedagioIdoso50": 1,
            "Codigo": 0,
            "AgenciasCargas": null,
            "LastUpdate": null,
            "TxPedagioPasseLivre": null,
            "CodigoSgltar": 0,
            "TxEmb50Idoso50": 0,
            "TxEmb50Def50": 0,
            "TxEmb50Estud50": 0,
            "TxEmb50Jov50": 0,
            "TxEmbIdosoDef": 0
        },
        "DataChegada": "0001-01-01T00:00:00",
        "Obs": null,
        "HoraTolerancia": null,
        "DataExibirViagem": "0001-01-01T00:00:00",
        "HoraExibirViagem": null,
        "QtdPoltronaBloqueio": 0,
        "Comentario": ""
    },
    {
        "DataBloqSemPassageiro": "0001-01-01T00:00:00",
        "HoraBloqSemPassageiro": null,
        "IDViagem": 107209,
        "DataPartida": "2024-06-18T07:05:00",
        "HoraPartida": "0705",
        "Sentido": 0,
        "Plataforma": "",
        "HoraChegada": null,
        "ControlaPoltrona": 0,
        "ControlaPassageiros": 0,
        "ControlaCliente": 0,
        "Localidade": {
            "ListEstabelecimentos": null,
            "BilheteEmbW2i": 0,
            "IDLocalidade": 151,
            "Descricao": "CAMPOS DE JULIO - MT",
            "Sigla": "CPJ",
            "IdRegiao": null,
            "Uf": "MT",
            "IdEstabelecimento": 0,
            "IdCidade": 5102686,
            "TxEmbIdoso50": 0,
            "TxEmbIdoso100": 0,
            "TxEmbPasseLivre": 0,
            "PedagioIdoso100": 0,
            "PedagioIdoso50": 0,
            "Codigo": 0,
            "AgenciasCargas": null,
            "LastUpdate": null,
            "TxPedagioPasseLivre": null,
            "CodigoSgltar": 0,
            "TxEmb50Idoso50": 0,
            "TxEmb50Def50": 0,
            "TxEmb50Estud50": 0,
            "TxEmb50Jov50": 0,
            "TxEmbIdosoDef": 0
        },
        "DataChegada": "0001-01-01T00:00:00",
        "Obs": null,
        "HoraTolerancia": null,
        "DataExibirViagem": "0001-01-01T00:00:00",
        "HoraExibirViagem": null,
        "QtdPoltronaBloqueio": 0,
        "Comentario": ""
    },
    {
        "DataBloqSemPassageiro": "0001-01-01T00:00:00",
        "HoraBloqSemPassageiro": null,
        "IDViagem": 107209,
        "DataPartida": "2024-06-18T08:05:00",
        "HoraPartida": "0805",
        "Sentido": 0,
        "Plataforma": "",
        "HoraChegada": null,
        "ControlaPoltrona": 0,
        "ControlaPassageiros": 0,
        "ControlaCliente": 0,
        "Localidade": {
            "ListEstabelecimentos": null,
            "BilheteEmbW2i": 0,
            "IDLocalidade": 78,
            "Descricao": "SAPEZAL - MT",
            "Sigla": "SPZL",
            "IdRegiao": null,
            "Uf": "MT",
            "IdEstabelecimento": 0,
            "IdCidade": 5107875,
            "TxEmbIdoso50": 0,
            "TxEmbIdoso100": 0,
            "TxEmbPasseLivre": 0,
            "PedagioIdoso100": 1,
            "PedagioIdoso50": 1,
            "Codigo": 0,
            "AgenciasCargas": null,
            "LastUpdate": null,
            "TxPedagioPasseLivre": null,
            "CodigoSgltar": 0,
            "TxEmb50Idoso50": 0,
            "TxEmb50Def50": 0,
            "TxEmb50Estud50": 0,
            "TxEmb50Jov50": 0,
            "TxEmbIdosoDef": 0
        },
        "DataChegada": "0001-01-01T00:00:00",
        "Obs": null,
        "HoraTolerancia": null,
        "DataExibirViagem": "0001-01-01T00:00:00",
        "HoraExibirViagem": null,
        "QtdPoltronaBloqueio": 0,
        "Comentario": ""
    }
]""",
)
ROTA_DATA = [ROTA_NAO_INTEGRADA_GRAN_EXPRESS, ROTA_INTEGRADA_GRAN_EXPRESS]

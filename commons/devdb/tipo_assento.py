from commons.devdb.company import COMPANY_GRAN_EXPRESS
from rodoviaria.models import TipoAssento

TIPOS_ASSENTOS_GRAN_EXPRESS_DATA = [
    TipoAssento(
        pk=5786152,
        company_id=COMPANY_GRAN_EXPRESS.pk,
        tipo_assento_parceiro="BASICO",
        tipo_assento_buser_preferencial=TipoAssento.TipoAssentoBuser.EXECUTIVO,
        tipos_assentos_buser_match=[
            TipoAssento.TipoAssentoBuser.EXECUTIVO,
            TipoAssento.TipoAssentoBuser.SEMI_LEITO,
        ],
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
    )
]

TIPOS_ASSENTOS_DATA = TIPOS_ASSENTOS_GRAN_EXPRESS_DATA

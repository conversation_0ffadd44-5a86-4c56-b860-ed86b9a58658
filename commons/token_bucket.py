import logging
from collections import namedtuple

from celery.utils.log import get_task_logger
from constance import config as const
from prometheus_client import Gauge
from simple_token_bucket import NotEnoughTokens, SimpleTokenBucket
from simple_token_bucket.backends.redis import RedisBackend

from commons import django_utils, redis
from commons.memoize import memoize_with_log
from rodoviaria.models import Company, Integracao
from rodoviaria.models.eulabs import EulabsLogin
from rodoviaria.models.guichepass import GuichepassLogin
from rodoviaria.models.praxio import PraxioLogin
from rodoviaria.models.smartbus import SmartbusLogin
from rodoviaria.models.ti_sistemas import TiSistemasLogin
from rodoviaria.models.totalbus import TotalbusLogin
from rodoviaria.models.vexado import VexadoLogin

task_logger = get_task_logger(__name__)
TokenBucketConfig = namedtuple("TokenBucketConfig", ["bucket_size", "refresh_interval"])
CompanyClientMap = namedtuple("CompanyClientMap", ["client_str", "company_name"])

NotEnoughTokens = NotEnoughTokens

integracao_login_map = {
    Integracao.API.PRAXIO: PraxioLogin,
    Integracao.API.EULABS: EulabsLogin,
    Integracao.API.TOTALBUS: TotalbusLogin,
    Integracao.API.VEXADO: VexadoLogin,
    Integracao.API.GUICHE: GuichepassLogin,
    Integracao.API.TI_SISTEMAS: TiSistemasLogin,
    Integracao.API.SMARTBUS: SmartbusLogin,
}

adamantina = CompanyClientMap(client_str="1be788c3-b594-4760-ae4c-3f746f6bb0c5", company_name="Expresso Adamantina")
exp_nordeste = CompanyClientMap(client_str="f04c905e-c14d-4aee-b948-7a37bed64f73", company_name="Expresso Nordeste")
grupo_juina = CompanyClientMap(client_str="JUINA_VR", company_name="Gran Express")

# rate limit específico por cliente
cliente_config_map = {
    adamantina.client_str: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_ADAMANTINA, refresh_interval=const.REFRESH_INTERVAL_ADAMANTINA
    ),
    exp_nordeste.client_str: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_EXP_NORDESTE, refresh_interval=const.REFRESH_INTERVAL_EXP_NORDESTE
    ),
    grupo_juina.client_str: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_GRUPO_JUINA, refresh_interval=const.REFRESH_INTERVAL_GRUPO_JUINA
    ),
}

# rate limits default por integração
integracao_config_map = {
    Integracao.API.PRAXIO: lambda: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_PRAXIO, refresh_interval=const.REFRESH_INTERVAL_PRAXIO
    ),
    Integracao.API.TOTALBUS: lambda: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_TOTALBUS, refresh_interval=const.REFRESH_INTERVAL_TOTALBUS
    ),
    Integracao.API.EULABS: lambda: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_EULABS, refresh_interval=const.REFRESH_INTERVAL_EULABS
    ),
    Integracao.API.VEXADO: lambda: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_VEXADO, refresh_interval=const.REFRESH_INTERVAL_VEXADO
    ),
    Integracao.API.GUICHE: lambda: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_GUICHE, refresh_interval=const.REFRESH_INTERVAL_GUICHE
    ),
    Integracao.API.SMARTBUS: lambda: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_SMARTBUS, refresh_interval=const.REFRESH_INTERVAL_SMARTBUS
    ),
    Integracao.API.TI_SISTEMAS: lambda: TokenBucketConfig(
        bucket_size=const.BUCKET_SIZE_TI_SISTEMAS, refresh_interval=const.REFRESH_INTERVAL_TI_SISTEMAS
    ),
}
"""
    o racional aqui é 140/m ao todo. Gordurinha de 20% pra operações sync, dá 240/m.
    73/m pra fluxos especiais (FETCH_ROTAS, ADD_PAX, CANCELA_PASSAGEM, REMANEJAMENTO, CANCELA_PASSAGENS_COM_ERRO)
    sobra 167/m pra atualização periódica da operação. Na prática, queremos (167 - BS)/60 >= BS/RI e BS >= concurrency
    uma possível solução com cucurrency 10 é BS=15 e RI=6
"""
buserlogger = logging.getLogger("rodoviaria.token_bucket")


# prometheus gauges
_gauge = Gauge("rodoviaria_token_bucket_tokens", "Available tokens", ["name"], multiprocess_mode="min")


class NullTokenBucket(SimpleTokenBucket):
    def __init__(self, *args, **kwargs):
        pass

    def try_get_token(self):
        return True


null_token_bucket = NullTokenBucket()


class TokenBucket(SimpleTokenBucket):
    def __init__(self, name: str, bucket_size: int, refresh_interval: int = 60):
        _gauge.labels(name=name).set(bucket_size)
        super().__init__(
            name=name,
            bucket_size=bucket_size,
            refresh_interval=refresh_interval,
            backend=RedisBackend(redis.get_master_client()),
            listener=self._listener_callback,
        )

    @classmethod
    def from_company_internal_id(cls, company_internal_id, modelo_venda):
        integracao, login = _get_company_integracao_and_login(company_internal_id, modelo_venda)
        # não dá pra ter token bucket se não for integrada ou a integracao não estiver configurada para tal
        if not login:
            return null_token_bucket
        bucket_name = f"{integracao.name}:{login.cliente}"
        config = _get_token_bucket_config(login.cliente, integracao.name)
        return cls(
            name=bucket_name,
            bucket_size=config.bucket_size,
            refresh_interval=config.refresh_interval,
        )

    @classmethod
    def from_client(
        cls,
        login: (
            PraxioLogin | EulabsLogin | TotalbusLogin | VexadoLogin | GuichepassLogin | SmartbusLogin | TiSistemasLogin
        ),
    ):
        integracao = login.company.integracao
        # não dá pra ter token bucket se não for integrada ou a integracao não estiver configurada para tal
        if not login or not login.cliente:
            return null_token_bucket
        bucket_name = f"{integracao.name}:{login.cliente}"
        config = _get_token_bucket_config(login.cliente, integracao.name)
        return cls(
            name=bucket_name,
            bucket_size=config.bucket_size,
            refresh_interval=config.refresh_interval,
        )

    def try_get_token(self, raises=True):
        success = super().try_get_token(raises)
        if success:
            buserlogger.debug("TokenBucket: sucesso ao obter token!", extra={"bucket_name": self.name})
        return success

    def _listener_callback(self, n: int):
        _gauge.labels(name=self.name).set(n)


@memoize_with_log(timeout=12 * 60 * 60)
def _get_company_integracao_and_login(company_internal_id: int, modelo_venda: Company.ModeloVenda):
    company = Company.objects.select_related("integracao").get(
        company_internal_id=company_internal_id, modelo_venda=modelo_venda
    )
    integracao = company.integracao
    LoginModel = integracao_login_map.get(integracao.name)
    if not LoginModel:
        task_logger.info("TokenBucket: %s ainda não aceita token bucket", integracao.name)
        return integracao, None
    login = LoginModel.objects.get(company=company)
    return integracao, login


def _get_token_bucket_config(cliente: str, integracao: str):
    config = cliente_config_map.get(cliente)
    if not config:
        try:
            config_obj = integracao_config_map[integracao]
            config = config_obj()
        except KeyError as exc:
            raise TokenBucketConfigError(
                f"Configuração de token bucket não encontrada para {cliente=} ou {integracao=}"
            ) from exc
    return config


def get_token_bucket_robust(company_internal_id, modelo_venda=Company.ModeloVenda.MARKETPLACE):
    try:
        token_bucket = TokenBucket.from_company_internal_id(company_internal_id, modelo_venda)
    except TokenBucketConfigError as ex:
        task_logger.error(
            "TokenBucket: Erro na configuração do TokenBucket. django_utils.error_str(ex)=%s",
            django_utils.error_str(ex),
        )
        token_bucket = null_token_bucket
    return token_bucket


class TokenBucketConfigError(Exception):
    pass

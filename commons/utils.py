import inspect
import re
import unicodedata


class RegexEqual:
    def __init__(self, pattern):
        self._pattern = pattern

    def __eq__(self, other):
        return re.match(self._pattern, other) is not None

    def __repr__(self):
        return self._pattern


def only_numbers(s):
    if not s:
        return s
    return re.sub("[^0-9]", "", s)


def build_key(f, key, args, kwargs):
    kwdized = kwdize(f, args, kwargs)
    key = key.format(**kwdized)
    key = re.sub(r"\s+", "", key)
    return key


def kwdize(f, args, kwargs):
    fargs = inspect.getfullargspec(f).args
    kwdized = dict(zip(fargs, args))
    kwdized.update(kwargs)
    return kwdized


def is_running_on_celery():
    from bp.buserdjango_celery import buserdjango_app
    from bp.celery import app

    return app.current_worker_task is not None or buserdjango_app.current_worker_task is not None


def strip_punctuation(phrase):
    if not phrase:
        return phrase
    normalized_name = "".join(c for c in unicodedata.normalize("NFD", phrase) if unicodedata.category(c) != "Mn")
    return normalized_name.lower().strip()


def str_contains(str, substring):
    if str is None or substring is None:
        return False
    str_normalized = strip_punctuation(str).upper()
    substring_normalized = strip_punctuation(substring).upper()
    return substring_normalized in str_normalized

import json
from datetime import date, datetime
from decimal import Decimal, FloatOperation, getcontext

from django.core.serializers import json as json_django
from pydantic import BaseModel


def patch_encoder(encoder):
    old_default = encoder.default

    def encoder_wrapped(self, o):
        if isinstance(o, (date, datetime)):
            return o.isoformat()

        if isinstance(o, Decimal):
            return str(o)

        if isinstance(o, BaseModel):
            return o.dict()

        return old_default(self, o)

    encoder.default = encoder_wrapped


def setup_json_encoder():
    c = getcontext()
    c.traps[FloatOperation] = True

    patch_encoder(json.JSONEncoder)
    patch_encoder(json_django.DjangoJSONEncoder)

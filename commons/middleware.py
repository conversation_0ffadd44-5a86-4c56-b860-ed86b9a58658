import logging
import random
import time

import sentry_sdk
from django.conf import settings
from django.http.response import JsonResponse
from sentry_sdk import capture_exception

from commons.circuit_breaker import MyCircuitBreakerError
from commons.django_utils import error_str
from commons.log_svc import log_reserva_exception
from rodoviaria.api import get_current_integration
from rodoviaria.service.exceptions import (
    RodoviariaBaseException,
    RodoviariaConnectionError,
    RodoviariaOTAException,
    RodoviariaUnsupportedFeatureException,
)

logger = logging.getLogger("rodoviaria.request")


def process_exception(exception, request):
    url = request.build_absolute_uri()

    if isinstance(exception, RodoviariaConnectionError):
        dict_error = {"error": error_str(exception), "error_type": "connection_error"}
        log_reserva_exception("ConnectionError", extra=dict_error | {"url": url})
        return JsonResponse(dict_error, status=504)

    if isinstance(exception, RodoviariaOTAException):
        dict_error = {"error": error_str(exception), "error_type": "ota_exception"}
        log_reserva_exception("RodoviariaOTAException", extra=dict_error | {"url": url})
        return JsonResponse(dict_error, status=500)

    if isinstance(exception, NotImplementedError):
        dict_error = {"error": error_str(exception), "error_type": "not_implemented"}
        log_reserva_exception("NotImplementedError", extra=dict_error | {"url": url})
        return JsonResponse(dict_error, status=501)

    if isinstance(exception, RodoviariaUnsupportedFeatureException):
        dict_error = {"error": error_str(exception), "error_type": "unsupported_feature"}
        log_reserva_exception("RodoviariaUnsupportedFeatureException", extra=dict_error | {"url": url})
        return JsonResponse(dict_error, status=422)

    if isinstance(exception, RodoviariaBaseException):
        dict_error = {"error": error_str(exception), "error_type": "base_exception"}
        log_reserva_exception(error_str(exception), extra=dict_error | {"url": url})
        if exception.message != "A poltrona já foi selecionada":
            capture_exception(exception)
        return JsonResponse(dict_error, status=500)

    if isinstance(exception, MyCircuitBreakerError):
        dict_error = {"error": error_str(exception), "error_type": "connection_error", "message": error_str(exception)}
        return JsonResponse(dict_error, status=429)

    dict_error = {"error": error_str(exception), "error_type": "unknown_exception"}
    log_reserva_exception(error_str(exception), extra=dict_error | {"url": url})
    capture_exception(exception)
    return JsonResponse(dict_error, status=500)


def error_middleware(get_response):
    def middleware(request):
        return get_response(request)

    def _process_exception(request, exception):
        return process_exception(exception, request=request)

    middleware.process_exception = _process_exception
    return middleware


def request_log_middleware(get_response):
    def middleware(request):
        t = time.time()
        response = get_response(request)
        duration = time.time() - t

        try:
            route = request.resolver_match.route
        except AttributeError:
            route = None

        try:
            content_length = int(response.get("Content-Length"))
        except (ValueError, TypeError):
            content_length = None

        attrs = {
            "method": request.method,
            "status_code": response.status_code,
            "path": request.path,
            "route": route,
            "duration": duration,
            "user_id": request.user.id,
            "params": request.GET,
            "length": content_length,
            "user_agent": request.headers.get("User-Agent"),
            "correlation_id": request.correlation_id,
        }
        if current_integration := get_current_integration():
            attrs["integration"] = current_integration

        logger.info(
            "%s %s %d",
            request.method,
            request.get_full_path(),
            response.status_code,
            extra=attrs,
        )

        return response

    return middleware


def sentry_middleware(get_response):
    def middleware(request):
        cid = request.correlation_id
        if cid:
            sentry_sdk.set_tag("correlation_id", cid)
        return get_response(request)

    return middleware


class honeycomb_middleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)

    def process_view(self, request, view_func, view_args, view_kwargs):
        sample_rate = getattr(view_func, "__honeycomb_sample_rate", settings.HONEYCOMB_SAMPLE_RATE)
        if sample_rate is not None:
            sampled = random.random() < 1 / sample_rate  # noqa: S311
            request.__honeycomb_sampled__ = sampled, sample_rate
        return None

import warnings

from django.core.cache import cache, caches
from django.core.cache.backends.memcached import BaseMemcachedCache

try:
    import mockcache
except ImportError:
    pass
else:

    class MockcacheCache(BaseMemcachedCache):
        def __init__(self, server, params):
            super().__init__(
                "mockserver",
                params,
                library=mockcache,
                value_not_found_exception=ValueError,
            )

        def get(self, key, default=None, version=None):
            key = self.make_key(key, version=version)
            self.validate_key(key)
            val = self._cache.get(key)
            if val is None:
                return default
            return val


def getmc():
    warnings.warn("getmc called", DeprecationWarning, stacklevel=2)
    return cache._cache


def getmc_pymemcache():
    return caches["pymemcache"]

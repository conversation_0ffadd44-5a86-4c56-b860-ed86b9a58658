from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django_cryptography import fields
from model_bakery import baker, random_gen

# o django-cryptoghaphy cria o campo dinamicamente mas o model-bakery
# precisa importar para armazenar o gerador configurado.
fields.EncryptedCharField = fields.get_encrypted_field(Char<PERSON>ield, wasinstance=False)
baker.generators.add("django_cryptography.fields.EncryptedCharField", random_gen.gen_string)

import pytest

from request_context.context import (
    OutsideRequestError,
    add_to_request_context,
    clean_request_context,
    get_current_request,
    get_from_request_context,
    request_cycle_context,
    set_current_request,
)


@pytest.fixture(autouse=True)
def clean_context():
    clean_request_context()


def test_current_request(rf):
    request = rf.get("/api/testing")
    set_current_request(request)
    assert request == get_current_request()


def test_add_to_request_context(rf):
    request = rf.get("/api/testing")
    set_current_request(request)

    add_to_request_context(integration="eulabs")
    assert get_from_request_context("integration") == "eulabs"


def test_add_to_request_context_outside_request():
    with pytest.raises(OutsideRequestError):
        add_to_request_context(integration="eulabs")


def test_request_cycle_context(rf, mocker):
    set_current_request = mocker.patch("request_context.context.set_current_request")
    clean_request_context = mocker.patch("request_context.context.clean_request_context")

    request = rf.get("/api/testing")
    with request_cycle_context(request):
        pass

    set_current_request.assert_called_with(request)
    clean_request_context.asset_called_once()

from django.http import HttpResponse

from request_context.middleware import request_context_middleware


def test_request_context_middleware(rf, mocker):
    request_cycle_context = mocker.patch("request_context.middleware.request_cycle_context")

    request = rf.get("/api/testing")
    middleware = request_context_middleware(lambda _: HttpResponse())
    middleware(request)

    request_cycle_context.assert_called_with(request=request)

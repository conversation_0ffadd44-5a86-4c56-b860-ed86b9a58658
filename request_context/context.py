from contextlib import contextmanager
from contextvars import ContextVar

_request_context = ContextVar("request_context", default={})
_REQUEST_KEY = "request"


def add_to_request_context(**info):
    context = _request_context.get()
    if _REQUEST_KEY not in context:
        raise OutsideRequestError

    context.update(info)
    _request_context.set(context)


def get_from_request_context(key, default=None):
    context = _request_context.get()
    value = context.get(key, default)
    return value


@contextmanager
def request_cycle_context(request):
    set_current_request(request)
    yield
    clean_request_context()


def set_current_request(request):
    context = _request_context.get()
    context[_REQUEST_KEY] = request
    _request_context.set(context)


def get_current_request():
    context = _request_context.get()
    return context.get(_REQUEST_KEY)


def clean_request_context():
    _request_context.set({})


class OutsideRequestError(RuntimeError):
    pass

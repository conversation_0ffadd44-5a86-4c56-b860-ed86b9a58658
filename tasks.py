import inspect
from functools import wraps

from invoke import task


def projtask(func):
    """
    Task para ser executada na raiz do projeto.
    """

    @wraps(func)
    def wrapper(c, *args, **kwargs):
        with c.cd(c.config._project_prefix):
            return func(c, *args, **kwargs)

    wrapper.__signature__ = inspect.signature(func)  # type: ignore
    return task(wrapper)


def _pipcompile(c, *, upgrade=None):
    upgrades = ""
    if upgrade:
        if upgrade == "ALL":
            upgrades = " --upgrade"
        else:
            for package in upgrade.split():
                upgrades += f' --upgrade-package "{package}"'

    cmd = f"pip-compile --generate-hashes --allow-unsafe {upgrades}"
    print(cmd)
    c.run(cmd)


@projtask
def requirements(c, upgrade=None):
    _pipcompile(c, upgrade=upgrade)


@projtask
def celery(c):
    queues = """bp_cron,bp_descobrir_rotas,bp_motorista,bp_ping,bp_trechos_vendidos,
    bp_fetch_rotas,bp_alterar_grupo,bp_cadastros_vexado,bp_staff_update_link,
    bp_link_trecho_classe_hibrido,bp_cancela_grupo_classe_hibrido,bp_desbloquear_poltrona"""
    c.run(f"celery --app bp.celery.app worker --queues {queues} --loglevel INFO --beat")


@projtask
def lint(c):
    c.run("ruff check .")
    c.run("ruff format --diff .")


@projtask
def pyrightdiff(c):
    c.run("git diff dev --name-only | tr '\n' ' ' | xargs pyright")


@projtask
def format(c):
    c.run("ruff check --fix .")
    c.run("ruff format .")


@projtask
def dkdb(c):
    c.run("docker compose up postgres -d")

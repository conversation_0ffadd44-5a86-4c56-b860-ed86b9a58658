import random
from functools import wraps
from typing import cast

import beeline

# monkey patch libs to instrument outgoing requests
from beeline.patch import requests, urllib
from django.conf import settings

from request_context.context import get_current_request


def init():
    beeline.init(
        writekey=settings.HONEYCOMB_API_KEY,
        dataset=settings.HONEYCOMB_DATASET,
        service_name=settings.HONEYCOMB_SERVICE_NAME,
        sample_rate=settings.HONEYCOMB_SAMPLE_RATE,
        sampler_hook=_sampler_hook,
    )


def _sampler_hook(_):
    request = get_current_request()
    sampled = getattr(request, "__honeycomb_sampled__", None)

    if request is None or sampled is None:
        sample_rate = cast(int, settings.HONEYCOMB_SAMPLE_RATE)
        sampled = random.random() < 1 / sample_rate  # noqa: S311
        return sampled, sample_rate
    else:
        return sampled


def sample_view(sample_rate):
    def decorator(fn):
        nonlocal sample_rate
        fn.__honeycomb_sample_rate = sample_rate

        @wraps(fn)
        def wrapper(*args, **kwargs):
            return fn(*args, **kwargs)

        return wrapper

    return decorator

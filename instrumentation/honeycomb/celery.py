import time
from contextlib import ExitStack

import beeline
from beeline.middleware.django import Honey<PERSON><PERSON><PERSON><PERSON><PERSON>
from celery.signals import task_postrun, task_prerun, worker_init, worker_process_init
from django.conf import settings
from django.db import connections

IGNORED_TASK_NAMES = {"celery.chord_unlock"}


@worker_init.connect
@worker_process_init.connect
def init(**kwargs):
    if settings.HONEYCOMB_API_KEY:
        task_prerun.connect(start_celery_trace)
        task_postrun.connect(end_celery_trace)

        import instrumentation.honeycomb

        instrumentation.honeycomb.init()


def start_celery_trace(task_id, task, args, kwargs, **rest_args):
    if task.name in IGNORED_TASK_NAMES:
        return
    routing_key = task.request.delivery_info.get("routing_key")
    exchange = task.request.delivery_info.get("exchange")

    try:
        publised_time = task.request.timestamp
    except AttributeError:
        queued_time = None
    else:
        queued_time = time.time() - publised_time

    task.request.trace = beeline.start_trace(
        context={
            "name": "celery",
            "environment": settings.ENVIRONMENT,
            "celery.task_id": task_id,
            "celery.task_name": task.name,
            "celery.task_queued_time": queued_time,
            "celery.delivery_info.routing_key": routing_key,
            "celery.delivery_info.exchange": exchange,
        }
    )

    task.request._exit_stack = ExitStack()
    db_wrapper = HoneyDBWrapper()
    for connection in connections.all():
        task.request._exit_stack.enter_context(connection.execute_wrapper(db_wrapper))


def end_celery_trace(task, state, **kwargs):
    if task.name in IGNORED_TASK_NAMES:
        return
    task.request._exit_stack.close()
    beeline.add_field("celery.status", state)
    beeline.finish_trace(task.request.trace)

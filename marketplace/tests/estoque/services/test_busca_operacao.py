from dataclasses import dataclass
from datetime import date, datetime, timedelta
from unittest.mock import AsyncMock, patch

import aio_pika
import or<PERSON><PERSON>
import pytest
from asgiref.sync import async_to_sync
from model_bakery import baker

from bp import settings
from commons.dateutils import to_tz
from marketplace.estoque.services import busca_operacao
from marketplace.models import <PERSON>point, Itinerario, LocalEmbarque, Login, Trecho, Viagem
from marketplace.otas.updater import OTAUpdaterTask


@dataclass
class TestUpdaterTask(OTAUpdaterTask):
    id: int
    description: str

    @property
    def integracao(self):
        return "integracao"


@pytest.mark.asyncio
@pytest.mark.django_db
@patch("aio_pika.connect_robust")
@patch("marketplace.estoque.services.busca_operacao.get_updater_from_ota_login")
async def test_producer(mock_get_updater_from_ota_login, mock_connect_robust):
    # Setup
    mock_channel = AsyncMock()
    mock_connection = AsyncMock()
    mock_exchange = AsyncMock()
    mock_connect_robust.return_value = mock_connection
    mock_connection.__aenter__.return_value = mock_connection
    mock_connection.channel.return_value = mock_channel
    mock_channel.default_exchange = mock_exchange

    mock_task = TestUpdaterTask(login_id=2314, id=1313, description="description")

    async def mock_generate_tasks(*args, **kwargs):
        yield mock_task

    mock_updater = AsyncMock()
    mock_updater.generate_tasks = mock_generate_tasks
    mock_get_updater_from_ota_login.return_value = mock_updater

    login = AsyncMock()
    login.queue_name = "test_queue"

    await busca_operacao.busca_operacao(login, date(2024, 9, 17), date(2024, 9, 18), 0)

    mock_connect_robust.assert_called_once_with(settings.CELERY_BROKER_URL)
    mock_channel.default_exchange.publish.assert_called_once()

    args, kwargs = mock_channel.default_exchange.publish.call_args
    published_message = args[0]
    assert isinstance(published_message, aio_pika.Message)
    assert orjson.loads(published_message.body) == {
        "integracao": mock_task.integracao,
        "login_id": mock_task.login_id,
        "id": mock_task.id,
        "description": mock_task.description,
    }
    assert published_message.priority == 0
    assert kwargs["routing_key"] == login.queue_name


@pytest.mark.django_db
@patch("marketplace.estoque.services.busca_operacao.get_adapter")
def test_update_locais_embarque_success(mock_get_adapter):
    login_instance = baker.make(Login)

    mock_get_adapter.return_value.buscar_locais_embarque = AsyncMock(
        return_value=[
            LocalEmbarque(id_parceiro=1, name="São Paulo - SP", login=login_instance, timezone="America/Sao_Paulo"),
            LocalEmbarque(
                id_parceiro=2, name="Rio de Janeiro - RJ", login=login_instance, timezone="America/Sao_Paulo"
            ),
        ]
    )

    async_to_sync(busca_operacao.update_locais_embarque)(login_instance)

    locais_embarque_criados = list(LocalEmbarque.objects.filter(login=login_instance).order_by("name"))

    assert len(locais_embarque_criados) == 2

    assert locais_embarque_criados[0].name == "Rio de Janeiro - RJ"
    assert locais_embarque_criados[0].timezone == "America/Sao_Paulo"
    assert locais_embarque_criados[0].id_parceiro == "2"
    assert locais_embarque_criados[0].login == login_instance

    assert locais_embarque_criados[1].name == "São Paulo - SP"
    assert locais_embarque_criados[1].timezone == "America/Sao_Paulo"
    assert locais_embarque_criados[1].id_parceiro == "1"
    assert locais_embarque_criados[1].login == login_instance


@pytest.mark.django_db
@patch("marketplace.estoque.services.busca_operacao.get_adapter")
def test_update_locais_embarque_empty_response(mock_get_adapter):
    login_instance = baker.make(Login)

    mock_get_adapter.return_value.buscar_locais_embarque = AsyncMock(return_value=[])

    async_to_sync(busca_operacao.update_locais_embarque)(login_instance)

    locais_embarque_criados = list(LocalEmbarque.objects.filter(login=login_instance))
    assert len(locais_embarque_criados) == 0


@pytest.fixture
def login():
    return baker.make(Login)


@pytest.fixture
def locais_embarque(login):
    origem = baker.make(
        LocalEmbarque, name="São Paulo (Tietê) - SP", login=login, id_parceiro="123", timezone="America/Sao_Paulo"
    )
    parada = baker.make(
        LocalEmbarque, name="São José dos Campos - SP", login=login, id_parceiro="456", timezone="America/Sao_Paulo"
    )
    destino = baker.make(
        LocalEmbarque, name="Rio de Janeiro - RJ", login=login, id_parceiro="789", timezone="America/Sao_Paulo"
    )
    return [origem, parada, destino]


@pytest.mark.django_db
def test_cria_viagens():
    login = baker.make(Login)

    mesmo_local = baker.make(LocalEmbarque, login=login, id_parceiro=3)
    local_diferente = LocalEmbarque(login=login, id_parceiro=123)

    locais_embarques = [LocalEmbarque(login=login, id_parceiro=3) for _ in range(3)]
    ckp1 = Checkpoint(local=locais_embarques[0], idx=0, duracao=timedelta(minutes=10), distancia=5)
    ckp2 = Checkpoint(local=mesmo_local, idx=1, duracao=timedelta(minutes=10), distancia=5)
    ckp3 = Checkpoint(local=local_diferente, idx=2, duracao=timedelta(minutes=10), distancia=5)

    itinerario = Itinerario.from_checkpoints([ckp1, ckp2, ckp3], login=login)
    viagem = Viagem(
        itinerario=itinerario,
        capacidade=3,
        classe_parceiro="teste",
        id_parceiro=3,
        login=login,
        datetime_ida=to_tz(datetime(2024, 10, 10, 19, 30, 0), "America/Sao_Paulo"),
    )
    trecho = Trecho(
        origem=locais_embarques[1],
        destino=local_diferente,
        viagem=viagem,
        datetime_ida=to_tz(datetime(2024, 10, 10, 19, 30, 0), "America/Sao_Paulo"),
    )

    async_to_sync(busca_operacao.create_viagens)([trecho])

    assert viagem.chave_viagem == (
        f"{login.id}_{viagem.id_parceiro}_{viagem.datetime_ida}_{viagem.classe_parceiro}_{itinerario.id}"
    )
    assert viagem.itinerario_id == itinerario.id

    assert ckp3.local_id == local_diferente.id
    assert ckp3.itinerario_id == itinerario.id

    assert ckp2.local_id == mesmo_local.id
    assert ckp2.itinerario_id == itinerario.id

    assert ckp1.local_id == mesmo_local.id
    assert ckp1.itinerario_id == itinerario.id

    assert trecho.origem_id == mesmo_local.id
    assert trecho.destino_id == local_diferente.id
    assert trecho.viagem_id == viagem.id

import json
from unittest.mock import patch

import pytest
from asgiref.sync import async_to_sync
from model_bakery import baker

from marketplace import views
from marketplace.models import LocalEmbarque, Login


@pytest.mark.django_db
@patch("marketplace.estoque.services.busca_operacao.update_locais_embarque", autospec=True)
def test_update_locais_view_success(mock_update_locais_embarque, rf):
    login = baker.make(Login)
    mock_update_locais_embarque.return_value = baker.prepare(LocalEmbarque, _quantity=2)

    request = rf.post("/api/update-locais/", {"login_id": login.id}, content_type="application/json")

    response = async_to_sync(views.update_locais_view)(request, login_id=login.id)

    assert response.status_code == 200
    assert json.loads(response.content) == {"message": "2 Locais de embarque atualizados com sucesso"}


@pytest.mark.django_db
def test_update_locais_view_login_not_found(rf):
    request = rf.post("/api/update-locais/", {"login_id": 9999}, content_type="application/json")

    response = async_to_sync(views.update_locais_view)(request, login_id=9999)

    assert response.status_code == 404
    assert json.loads(response.content) == {"error": "Login not found"}

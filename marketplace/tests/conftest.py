import pytest
import responses


def pytest_configure():
    from django.test import TestCase

    TestCase.multi_db = True
    TestCase.databases = {"default", "rodoviaria"}


def _adapter_httpx_mock(httpx_mock):
    def add_adapter(method, url, json=None, status=200):
        httpx_mock.add_response(
            method=method,
            json=json,
            url=url,
            status_code=status,
        )

    def assert_call_count(url, count):
        assert len([r for r in httpx_mock.get_requests() if r.url == url]) == count
        return True

    httpx_mock.add = add_adapter
    httpx_mock.assert_call_count = assert_call_count
    return httpx_mock


@pytest.fixture
def http_mock(httpx_mock, requests_mock):
    """Fixture to mock HTTP requests.

    Use httpx_mock if `USE_HTTP_ASYNC` is True, otherwise uses requests_mock.
    """
    ...
    yield _adapter_httpx_mock(httpx_mock)


@pytest.fixture
def requests_mock():
    with responses.RequestsMock() as rsps:
        yield rsps

import pytest
from django.core.exceptions import ValidationError
from model_bakery import baker

from marketplace.commons.utils import get_login
from marketplace.models import Login


@pytest.mark.django_db
def test_get_login_error():
    nome_empresa = "Empresa de teste"
    baker.make(Login, nome_empresa=nome_empresa, _quantity=2)

    with pytest.raises(ValidationError):
        get_login(nome_empresa, None)


@pytest.mark.django_db
def test_get_login_sucess():
    nome_empresa = "Empresa de teste"
    logins = baker.make(Login, nome_empresa=nome_empresa, _quantity=2)

    login = get_login(nome_empresa, logins[0].id)

    assert login == logins[0]

from datetime import timedelta
from http import HTT<PERSON>tatus
from unittest.mock import MagicMock, patch

import httpx
import pytest

from marketplace.otas.exceptions import RodoviariaConnectionError
from marketplace.otas.executors.httpx import HttpxExecutor, HttpxResponse, Request
from marketplace.otas.executors.middlewares import Middleware


@pytest.mark.asyncio
async def test_asend_successful_request():
    mock_response = MagicMock(spec=httpx.Response)
    mock_response.status_code = HTTPStatus.OK
    mock_response.elapsed = timedelta(seconds=1)
    mock_response.json.return_value = {"success": True}

    request = Request(
        method="GET",
        endpoint="endpoint",
        params={"key": "value"},
    )

    with patch("httpx.AsyncClient.request", return_value=mock_response) as mock_request:
        executor = HttpxExecutor(base_url="https://fakeapi.com")

        response = await executor.asend(request)

        mock_request.assert_awaited_once_with(
            request.method,
            request.endpoint,
            params=request.params,
            json=None,
            timeout=None,
            headers=None,
        )
        assert isinstance(response, HttpxResponse)
        assert response.status_code == HTTPStatus.OK
        assert response.elapsed == timedelta(seconds=1)
        assert response.json() == {"success": True}


@pytest.mark.asyncio
async def test_asend_transport_error():
    request = Request(
        method="GET",
        endpoint="endpoint",
    )

    with patch("httpx.AsyncClient.request", side_effect=httpx.TransportError("Connection error")):
        executor = HttpxExecutor(base_url="https://fakeapi.com")

        with pytest.raises(RodoviariaConnectionError):
            await executor.asend(request)


@pytest.mark.asyncio
async def test_asend_with_middlewares():
    class TestMiddleware(Middleware):
        def preprocess_request(self, request):
            request.headers = {"X-Test-Header": "Value"}
            return request

        def process_response(self, response):
            response._inner.json = MagicMock(return_value={"modified": True})
            return response

    mock_response = MagicMock(spec=httpx.Response)
    mock_response.status_code = HTTPStatus.OK
    mock_response.elapsed = timedelta(seconds=1)
    mock_response.json.return_value = {"original": True}

    request = Request(
        method="GET",
        endpoint="endpoint",
        params={"key": "value"},
    )

    with patch("httpx.AsyncClient.request", return_value=mock_response) as mock_request:
        executor = HttpxExecutor(base_url="https://fakeapi.com", middlewares=[TestMiddleware()])

        response = await executor.asend(request)

        mock_request.assert_awaited_once_with(
            request.method,
            request.endpoint,
            params=request.params,
            json=None,
            timeout=None,
            headers={"X-Test-Header": "Value"},
        )
        assert isinstance(response, HttpxResponse)
        assert response.status_code == HTTPStatus.OK
        assert response.elapsed == timedelta(seconds=1)
        assert response.json() == {"modified": True}

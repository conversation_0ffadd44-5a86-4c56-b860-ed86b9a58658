import re
from datetime import date, datetime
from decimal import Decimal as D
from http import HTTPStatus

import pytest
from model_bakery import baker

from marketplace.models import Integracao, LocalEmbarque, Login
from marketplace.otas.eulabs.adapter import EulabsAdapter

# importa do rodoviaria para não ter que duplicar o mock
from rodoviaria.tests.eulabs.mock_data_response import mock_sectionals, mock_travels


@pytest.mark.asyncio
async def test_buscar_trechos_success(httpx_mock):
    integracao = baker.prepare(Integracao, name="Eulabs")
    login_instance = baker.prepare(
        Login,
        integracao=integracao,
        nome_empresa="Empresa Teste",
        id_empresa_parceiro=1234,
        base_url="https://fakeapi.com",
        payload={"api_id": "test_id", "api_key": "test_key"},
    )

    adapter = EulabsAdapter(login=login_instance)

    httpx_mock.add_response(
        method="GET",
        url=re.compile(adapter.client.endpoint("road/travels/search.*")),
        json=mock_travels.corridas,
        status_code=HTTPStatus.OK,
    )

    origem = baker.prepare(LocalEmbarque, id_parceiro=1)
    destino = baker.prepare(LocalEmbarque, id_parceiro=2)
    data_viagem = date(2024, 9, 15)

    result = await adapter.buscar_trechos(origem, destino, data_viagem)

    assert len(result) == 2

    primeiro_dado_trecho = result[0]
    assert primeiro_dado_trecho.preco == D(str(mock_travels.corridas[0]["items"][0]["tariffs"][0]["price_promotional"]))
    assert primeiro_dado_trecho.vagas == mock_travels.corridas[0]["items"][0]["tariffs"][0]["category"]["free_seats"]
    assert primeiro_dado_trecho.trecho.origem == origem
    assert primeiro_dado_trecho.trecho.destino == destino
    expected_datetime = datetime.fromisoformat(mock_travels.corridas[0]["items"][0]["datetime_departure"])
    assert primeiro_dado_trecho.trecho.viagem.datetime_ida == expected_datetime
    assert primeiro_dado_trecho.trecho.viagem.id_parceiro == mock_travels.corridas[0]["id"]
    assert (
        primeiro_dado_trecho.trecho.viagem.classe_parceiro
        == mock_travels.corridas[0]["items"][0]["tariffs"][0]["category"]["description"]
    )

    segundo_dado_trecho = result[1]
    assert segundo_dado_trecho.preco == D(str(mock_travels.corridas[0]["items"][0]["tariffs"][1]["price_promotional"]))
    assert segundo_dado_trecho.vagas == mock_travels.corridas[0]["items"][0]["tariffs"][1]["category"]["free_seats"]
    assert segundo_dado_trecho.trecho.origem == origem
    assert segundo_dado_trecho.trecho.destino == destino
    expected_datetime_segundo = datetime.fromisoformat(mock_travels.corridas[0]["items"][0]["datetime_departure"])
    assert segundo_dado_trecho.trecho.viagem.datetime_ida == expected_datetime_segundo
    assert segundo_dado_trecho.trecho.viagem.id_parceiro == mock_travels.corridas[0]["id"]
    assert (
        segundo_dado_trecho.trecho.viagem.classe_parceiro
        == mock_travels.corridas[0]["items"][0]["tariffs"][1]["category"]["description"]
    )


@pytest.mark.django_db
@pytest.mark.asyncio
async def test_get_sectional_success(httpx_mock):
    locais = mock_sectionals.locais[:2]
    integracao = baker.prepare(Integracao, name="Eulabs")
    login_instance = baker.prepare(
        Login,
        integracao=integracao,
        nome_empresa="Empresa Teste",
        id_empresa_parceiro=1234,
        base_url="https://fakeapi.com",
        payload={"api_id": "test_id", "api_key": "test_key"},
    )

    adapter = EulabsAdapter(login=login_instance)

    httpx_mock.add_response(
        method="GET",
        url=re.compile(adapter.client.endpoint("sectionals.*")),
        json=locais,
        status_code=HTTPStatus.OK,
    )

    results = await adapter.buscar_locais_embarque()

    assert results[0].id_parceiro == locais[0]["id"]
    assert results[0].name == locais[0]["description"]

    assert results[1].id_parceiro == locais[1]["id"]
    assert results[1].name == locais[1]["description"]

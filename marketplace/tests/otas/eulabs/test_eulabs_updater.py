import re
from datetime import date, datetime, timedelta
from http import HTTPStatus

import pytest
from model_bakery import baker

from commons.dateutils import to_tz
from marketplace.models import Integracao, Login
from marketplace.otas.eulabs.output import TravelSummary
from marketplace.otas.eulabs.updater import E<PERSON>bsUpdater, EulabsUpdaterTask

# importa do rodoviaria para não ter que duplicar o mock
from rodoviaria.tests.eulabs.mock_data_response import (
    mock_summary,
    mock_summary_list,
    mock_travels_detail,
)


@pytest.mark.asyncio
async def test_generate_tasks(httpx_mock):
    integracao = baker.prepare(Integracao, name="Eula<PERSON>")
    login_instance = baker.prepare(
        Login,
        id=16,
        integracao=integracao,
        nome_empresa="Empresa Teste",
        id_empresa_parceiro=1234,
        base_url="https://fakeapi.com",
        payload={"api_id": "test_id", "api_key": "test_key"},
    )
    updater = EulabsUpdater(login_instance)
    httpx_mock.add_response(
        method="GET",
        url=re.compile(updater.client.endpoint("road/travels/summary.*")),
        json=mock_summary_list.uma_viagem,
        status_code=HTTPStatus.OK,
    )

    tasks = [task async for task in updater.generate_tasks(date(2024, 9, 17), date(2024, 9, 17))]

    assert len(tasks) == 1
    assert tasks[0].integracao == "eulabs"
    viagem = mock_summary_list.uma_viagem[0]
    assert tasks[0].departure_date == viagem["departure_date"]
    assert tasks[0].departure_time == viagem["departure_time"]
    assert tasks[0].travel_id == viagem["id"]
    assert tasks[0].login_id == 16


@pytest.mark.asyncio
@pytest.mark.django_db
async def test_process_task(httpx_mock):
    login = Login(id=1, payload={"rate_limit": 120})
    updater = EulabsUpdater(login)

    travel_summary = TravelSummary(**mock_summary_list.uma_viagem[0])
    integracao = baker.prepare(Integracao, name="Eulabs")
    login_instance = baker.prepare(
        Login,
        id=16,
        integracao=integracao,
        nome_empresa="Empresa Teste",
        id_empresa_parceiro=1234,
        base_url="https://fakeapi.com",
        payload={"api_id": "test_id", "api_key": "test_key"},
    )
    updater = EulabsUpdater(login_instance)
    httpx_mock.add_response(
        method="GET",
        url=re.compile(updater.client.endpoint(f"road/travels/summary/{travel_summary.id}")),
        json=mock_summary.itinerario,
        status_code=HTTPStatus.OK,
    )
    httpx_mock.add_response(
        method="GET",
        url=re.compile(updater.client.endpoint(f"road/travels/detail/{travel_summary.id}")),
        json=mock_travels_detail.trechos_vendidos,
        status_code=HTTPStatus.OK,
    )

    viagem = mock_summary_list.uma_viagem[0]
    task = EulabsUpdaterTask(
        login_id=login.id,
        departure_date=viagem["departure_date"],
        departure_time=viagem["departure_time"],
        travel_id=viagem["id"],
    )

    result = await updater.process_task(task)

    assert len(result) == len(mock_travels_detail.trechos_vendidos)

    # asserts no itinerário
    itinerario = result[0].viagem.itinerario
    assert len(itinerario._checkpoints) == len(mock_summary.itinerario)
    assert itinerario.itinerario_hash == "-".join(
        [f"{cp.local.id_parceiro}_{cp.duracao}" for cp in itinerario._checkpoints]
    )

    # asserts nos checkpoints
    for travel_leg, checkpoint in zip(mock_summary.itinerario, itinerario._checkpoints):
        assert checkpoint.local.id_parceiro == travel_leg["seccional_id"]
        assert checkpoint.local.name == f"{travel_leg['seccional_name']} - {travel_leg['uf_acronym']}"
    assert itinerario._checkpoints[0].duracao == timedelta(0)
    assert itinerario._checkpoints[0].distancia == 0
    assert itinerario._checkpoints[0].idx == 0
    last_duracao = datetime.fromisoformat(mock_summary.itinerario[-1]["local_exit"]) - datetime.fromisoformat(
        mock_summary.itinerario[-2]["local_exit"]
    )
    last_distancia = mock_summary.itinerario[-1]["total_km"] - mock_summary.itinerario[-2]["total_km"]
    assert itinerario._checkpoints[-1].duracao == last_duracao
    assert itinerario._checkpoints[-1].distancia == last_distancia
    assert itinerario._checkpoints[-1].idx == len(itinerario._checkpoints) - 1

    # asserts nas viagens e trechos
    for travel_detail, trecho in zip(mock_travels_detail.trechos_vendidos, result):
        assert trecho.origem.id_parceiro == travel_detail["origin_id"]
        assert trecho.destino.id_parceiro == travel_detail["destination_id"]

        assert trecho.viagem.datetime_ida == to_tz(
            datetime.fromisoformat(f"{viagem['departure_date']} {viagem['departure_time']}"), trecho.origem.timezone
        )
        assert trecho.viagem.login == login_instance
        assert trecho.viagem.itinerario == itinerario
        assert trecho.viagem.id_parceiro == viagem["id"]
        assert trecho.viagem.classe_parceiro == travel_detail["class_description"]
        assert trecho.viagem.capacidade == travel_detail["capacity"]

    ultimo_trecho = result[-1]
    ultimo_trecho_leg = next(
        leg for leg in mock_summary.itinerario if leg["seccional_id"] == ultimo_trecho.origem.id_parceiro
    )
    assert ultimo_trecho.datetime_ida == to_tz(
        datetime.fromisoformat(ultimo_trecho_leg["local_exit"]), ultimo_trecho.origem.timezone
    )

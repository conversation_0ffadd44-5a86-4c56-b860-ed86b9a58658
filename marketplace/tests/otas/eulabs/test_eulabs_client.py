import re
from copy import deepcopy
from datetime import date
from http import HTTPStatus

import pytest
from dacite import from_dict
from model_bakery import baker
from pytest_httpx import HTTPXMock

from marketplace.models import Inte<PERSON>cao, Login
from marketplace.otas.eulabs import EulabsLogin
from marketplace.otas.eulabs.client import EulabsClient
from marketplace.otas.eulabs.output import Sectional, TravelData, TravelDetail, TravelLeg, TravelSummary
from marketplace.otas.exceptions import RodoviariaConnectionError, RodoviariaTooManyRequestsError
from marketplace.otas.executors.middlewares.cloudflare_middleware import CloudflareHTTPStatus

# importa do rodoviaria para não ter que duplicar o mock
from rodoviaria.tests.eulabs.mock_data_response import (
    mock_sectionals,
    mock_summary,
    mock_summary_list,
    mock_travels,
    mock_travels_detail,
)


@pytest.fixture
def eulabs_client():
    """Fixture to set up Login, Integracao, and EulabsClient without persisting to the database."""
    integracao = baker.prepare(Integracao, name="Eula<PERSON>")
    login_instance = baker.prepare(
        Login,
        integracao=integracao,
        nome_empresa="Empresa Teste",
        id_empresa_parceiro=1234,
        base_url="https://fakeapi.com",
        payload={"api_id": "test_id", "api_key": "test_key"},
    )
    eulabs_login = EulabsLogin(login=login_instance)
    eulabs_client = EulabsClient(login=eulabs_login)
    return eulabs_client


@pytest.mark.asyncio
async def test_travels_search_with_login_middleware(eulabs_client: EulabsClient, httpx_mock: HTTPXMock):
    httpx_mock.add_response(
        method="GET",
        url=re.compile(eulabs_client.endpoint("road/travels/search.*")),
        json=mock_travels.corridas,
        status_code=HTTPStatus.OK,
    )

    origem_id = 1
    destino_id = 2
    data_viagem = date(2024, 9, 15)

    response = await eulabs_client.travels_search(origem_id, destino_id, data_viagem)

    assert isinstance(response[0], TravelData)

    request = httpx_mock.get_request()
    assert request.headers["X-Eucatur-Api-Id"] == "test_id"
    assert request.headers["X-Eucatur-Api-Key"] == "test_key"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "cloudflare_http_status",
    [
        CloudflareHTTPStatus.CLOUDFLARE_EMPTY_RESPONSE,
        CloudflareHTTPStatus.CLOUDFLARE_OFFLINE,
        CloudflareHTTPStatus.CLOUDFLARE_HANDSHAKE_TIMEOUT,
        CloudflareHTTPStatus.CLOUDFLARE_UNREACHABLE,
        CloudflareHTTPStatus.CLOUDFLARE_CONNECTION_TIMEOUT,
        CloudflareHTTPStatus.CLOUDFLARE_SSL_FAILED,
        CloudflareHTTPStatus.CLOUDFLARE_SSL_INVALID,
    ],
)
async def test_travels_search_cloudflare_error_handling(
    eulabs_client: EulabsClient, httpx_mock: HTTPXMock, cloudflare_http_status: CloudflareHTTPStatus
):
    httpx_mock.add_response(
        method="GET",
        url=re.compile(eulabs_client.endpoint("road/travels/search.*")),
        status_code=cloudflare_http_status,
    )

    origem_id = 1
    destino_id = 2
    data_viagem = date(2024, 9, 15)

    with pytest.raises(
        RodoviariaConnectionError, match=f"{cloudflare_http_status.value} {cloudflare_http_status.description}"
    ):
        await eulabs_client.travels_search(origem_id, destino_id, data_viagem)


@pytest.mark.asyncio
async def test_travels_search_too_many_requests(eulabs_client: EulabsClient, httpx_mock: HTTPXMock):
    httpx_mock.add_response(
        method="GET",
        url=re.compile(eulabs_client.endpoint("road/travels/search.*")),
        status_code=HTTPStatus.NOT_ACCEPTABLE,
    )

    origem_id = 1
    destino_id = 2
    data_viagem = date(2024, 9, 15)

    with pytest.raises(RodoviariaTooManyRequestsError, match="406 too many requests"):
        await eulabs_client.travels_search(origem_id, destino_id, data_viagem)


@pytest.mark.asyncio
async def test_travels_search_success(eulabs_client: EulabsClient, httpx_mock: HTTPXMock):
    httpx_mock.add_response(
        method="GET",
        url=re.compile(eulabs_client.endpoint("road/travels/search.*")),
        json=mock_travels.corridas,
        status_code=HTTPStatus.OK,
    )
    origem_id = 1
    destino_id = 2
    data_viagem = date(2024, 9, 15)

    response = await eulabs_client.travels_search(origem_id, destino_id, data_viagem)
    expected_response_raw = deepcopy(mock_travels.corridas)
    for travel in expected_response_raw:
        for item in travel["items"]:
            item["_class"] = item.pop("class")
    assert response == [from_dict(TravelData, corrida) for corrida in expected_response_raw]

    request = httpx_mock.get_request()
    assert dict(request.url.params) == {
        "origin_sectional_id": str(origem_id),
        "destiny_sectional_id": str(destino_id),
        "departure_date": data_viagem.strftime("%Y-%m-%d"),
    }

    assert request.headers["X-Eucatur-Api-Id"] == "test_id"
    assert request.headers["X-Eucatur-Api-Key"] == "test_key"


@pytest.mark.asyncio
async def test_list_road_travels_summary(eulabs_client: EulabsClient, httpx_mock: HTTPXMock):
    """Testa a função list_road_travels_summary."""
    httpx_mock.add_response(
        url=re.compile(eulabs_client.endpoint("road/travels/summary.*")),
        status_code=HTTPStatus.OK,
        json=mock_summary_list.viagens,
    )

    start_date = date(2024, 9, 1)
    end_date = date(2024, 9, 30)
    response = await eulabs_client.list_road_travels_summary(start_date, end_date)

    assert len(response) == 4
    assert isinstance(response[0], TravelSummary)
    assert response[0].id == 1988028
    assert response[0].line_code == "16126"
    assert response[0].departure_date == "2022-07-14"


@pytest.mark.asyncio
async def test_get_road_travel_summary(eulabs_client: EulabsClient, httpx_mock: HTTPXMock):
    """Testa a função get_road_travel_summary."""
    travel_id = 72131
    httpx_mock.add_response(
        url=re.compile(eulabs_client.endpoint(f"road/travels/summary/{travel_id}")),
        status_code=HTTPStatus.OK,
        json=mock_summary.itinerario,
    )

    response = await eulabs_client.get_road_travel_summary(travel_id)

    assert len(response) == 5
    assert isinstance(response[0], TravelLeg)
    assert response[0].seccional_id == 15
    assert response[0].seccional_code == "0235"


@pytest.mark.asyncio
async def test_get_road_travel_detail(eulabs_client: EulabsClient, httpx_mock: HTTPXMock):
    """Testa a função get_road_travel_detail."""
    travel_id = 89878
    httpx_mock.add_response(
        url=re.compile(eulabs_client.endpoint(f"road/travels/detail/{travel_id}")),
        status_code=HTTPStatus.OK,
        json=mock_travels_detail.trechos_vendidos,
    )

    response = await eulabs_client.get_road_travel_detail(travel_id)

    assert len(response) == 7
    assert isinstance(response[0], TravelDetail)
    assert response[0].origin_id == 15
    assert response[0].origin_name == "OURO PRETO RODOVIÁRIA"
    assert response[0].class_description == "EXECUTIVO"


@pytest.mark.asyncio
async def test_sectionals(eulabs_client: EulabsClient, httpx_mock: HTTPXMock):
    """Testa a função sectionals."""
    httpx_mock.add_response(
        url=re.compile(eulabs_client.endpoint("sectionals.*")),
        status_code=HTTPStatus.OK,
        json=mock_sectionals.locais,
    )

    response = await eulabs_client.sectionals()

    assert len(response) == 144
    assert isinstance(response[0], Sectional)

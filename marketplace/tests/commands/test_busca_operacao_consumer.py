from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from django.core.management import call_command
from model_bakery import baker

from bp import settings
from marketplace.models import Login, Priority


@pytest.mark.django_db
@patch("marketplace.management.commands.busca_operacao_consumer.get_updater_from_ota_login")
@patch("aio_pika.connect_robust")
@patch("marketplace.management.commands.busca_operacao_consumer.process_task")
def test_worker_command(mock_process_task, mock_connect_robust, mock_get_updater, mocker):
    # Setup
    login = baker.make(
        Login, id=1, nome_empresa="empresa", integracao__name="integracao", payload={"qos": 50, "rate_limit": 120}
    )

    # Mock RabbitMQ connection and channel
    mock_rabbitmq_conn = AsyncMock()
    mock_channel = AsyncMock()
    mock_queue = AsyncMock()

    mock_connect_robust.return_value = mock_rabbitmq_conn
    mock_rabbitmq_conn.channel.return_value = mock_channel
    mock_channel.declare_queue.return_value = mock_queue

    # Mock the consume method
    mock_queue.consume.return_value = AsyncMock()

    # Mock the process_task function
    mock_process_task.return_value = AsyncMock()

    # Mock the OTAUpdater
    mock_updater = MagicMock()
    mock_get_updater.return_value = mock_updater

    # Mock asyncio.Future to prevent blocking
    mocker.patch("asyncio.Future", return_value=AsyncMock()())

    # Call the command
    call_command("busca_operacao_consumer", nome_empresa=login.nome_empresa)

    # Assertions
    mock_connect_robust.assert_called_once_with(settings.CELERY_BROKER_URL)
    mock_channel.set_qos.assert_called_once_with(prefetch_count=50)
    mock_channel.declare_queue.assert_called_once_with(
        "mktplace_integracao_empresa", durable=True, arguments={"x-max-priority": Priority.HIGH}
    )
    mock_queue.consume.assert_called_once_with(mock_process_task.return_value)

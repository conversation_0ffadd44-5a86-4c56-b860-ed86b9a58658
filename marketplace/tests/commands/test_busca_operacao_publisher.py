from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

import pytest
import time_machine
from django.core.management import call_command
from model_bakery import baker
from zoneinfo import ZoneInfo

from marketplace.models import CronogramaAtualizacao, Login, Priority

agora = datetime(2022, 10, 19, 16, 43, 34, 0, tzinfo=ZoneInfo("America/Sao_Paulo"))


@pytest.mark.django_db
@patch("marketplace.estoque.services.busca_operacao.busca_operacao")
@time_machine.travel(agora)
def test_command_calls_producer(mock_producer):
    login = baker.make(Login, id=1, integracao__name="eulabs")
    cronograma = baker.make(
        CronogramaAtualizacao,
        hora=agora.hour,
        login=login,
        tipo_atualizacao=CronogramaAtualizacao.TipoAtualizacao.SEMANA,
    )
    mock_producer.return_value = AsyncMock(return_value=None)

    start_date = agora.date()
    end_date = start_date + timedelta(days=7)
    expected_priority = Priority.HIGH

    call_command("busca_operacao_publisher")

    cronograma.refresh_from_db()
    mock_producer.assert_awaited_once_with(login, start_date, end_date, expected_priority)
    assert isinstance(cronograma.last_run_at, datetime)

from datetime import datetime, timed<PERSON><PERSON>
from enum import IntEnum

from asgiref.sync import sync_to_async
from celery.utils.log import get_task_logger
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone
from django_cryptography.fields import encrypt

from commons.dateutils import to_tz
from commons.memoize import async_memoize_with_log
from commons.utils import strip_punctuation
from rodoviaria.models.core import CidadeInternal

task_logger = get_task_logger(__name__)


class AutoFieldAbstract(models.Model):
    id = models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")

    class Meta:
        abstract = True


class Integracao(models.Model):
    class API(models.TextChoices):
        TOTALBUS = "totalbus"
        PRAXIO = "praxio"
        VEXADO = "vexado"
        GUICHE = "guichepass"
        EULABS = "eulabs"
        SMARTBUS = "smartbus"
        TI_SISTEMAS = "ti_sistemas"

    name = models.TextField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class Login(models.Model):
    integracao = models.ForeignKey(Integracao, null=True, on_delete=models.SET_NULL)
    nome_empresa = models.TextField()
    id_empresa_parceiro = models.IntegerField(db_index=True, null=True, blank=True)
    base_url = models.URLField()
    payload = models.JSONField(null=True, blank=True, default=dict)
    encrypted_payload = encrypt(models.JSONField(null=True, blank=True, default=dict))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.integracao} - {self.nome_empresa}"

    @property
    def queue_name(self):
        return f"mktplace_{self.integracao.name}_{self.nome_empresa}".replace(" ", "_").replace("-", "_")

    @property
    def rate_limit(self):
        # TODO - se fizer sentido ser por login mesmo, adicionar modelo
        return 60


class Priority(IntEnum):
    HIGH = 2
    MEDIUM = 1
    LOW = 0


class CronogramaAtualizacao(models.Model):
    class TipoAtualizacao(models.TextChoices):
        SEMANA = "semana"
        MES = "mes"
        SEMESTRE = "semestre"

    login = models.ForeignKey(to=Login, on_delete=models.CASCADE)
    operacao = "descobrir_operacao"
    tipo_atualizacao = models.TextField(choices=TipoAtualizacao.choices)
    hora = models.IntegerField()
    dia_do_mes = models.IntegerField(null=True)
    dia_da_semana = models.IntegerField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_run_at = models.DateTimeField(null=True)

    @property
    def priority(self):
        return {
            CronogramaAtualizacao.TipoAtualizacao.SEMANA: Priority.HIGH,
            CronogramaAtualizacao.TipoAtualizacao.MES: Priority.MEDIUM,
            CronogramaAtualizacao.TipoAtualizacao.SEMESTRE: Priority.LOW,
        }[self.tipo_atualizacao]

    def calcula_inicio_e_fim(self) -> tuple[datetime, datetime]:
        hoje = timezone.now().date()

        proxima_semana = hoje + timedelta(days=7)
        if self.tipo_atualizacao == CronogramaAtualizacao.TipoAtualizacao.SEMANA:
            return (hoje, proxima_semana)

        proximo_mes = hoje + timedelta(days=30)
        if self.tipo_atualizacao == CronogramaAtualizacao.TipoAtualizacao.MES:
            return (proxima_semana + timedelta(days=1), proximo_mes)

        proximo_semestre = hoje + timedelta(days=30 * 6)
        return (proximo_mes + timedelta(days=1), proximo_semestre)

    def should_run_now(self):
        agora = to_tz(timezone.now(), "America/Sao_Paulo")
        dia_da_semana_valido = not self.dia_da_semana or self.dia_da_semana == agora.dow
        dia_do_mes_valido = not self.dia_do_mes or self.dia_do_mes == agora.day
        hora_valida = self.hora == agora.hour
        nao_rodou_recente = not self.last_run_at or self.last_run_at < agora - timedelta(minutes=30)

        return dia_da_semana_valido and dia_do_mes_valido and hora_valida and nao_rodou_recente


class LocalEmbarque(AutoFieldAbstract):
    class TimeZone(models.TextChoices):
        AMAZONIA = "America/Manaus"
        NORONHA = "America/Noronha"
        ACRE = "America/Rio_Branco"
        BRASILIA = "America/Sao_Paulo"

    id_parceiro = models.CharField(max_length=30)
    name = models.CharField(max_length=100, null=True, blank=True)
    login = models.ForeignKey(Login, on_delete=models.CASCADE)
    timezone = models.CharField(null=True, blank=True, max_length=256, choices=TimeZone.choices)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ["login", "id_parceiro"]

    @classmethod
    async def with_timezone(cls, city_normalized_name: str, uf_normalized_acronym: str, **kwargs):
        timezones = await get_timezones()
        key = _make_key(city_normalized_name, uf_normalized_acronym)
        timezone = timezones.get(key)

        if not timezone:
            raise ValidationError(f"Local {key} kwargs={kwargs} sem timezone")

        return cls(timezone=timezone, **kwargs)

    def __hash__(self):
        return hash(self.id) if self.id else hash((self.login_id, self.id_parceiro))

    def __eq__(self, other):
        if not isinstance(other, LocalEmbarque):
            return False

        if self.id and other.id:
            return self.id == other.id

        return (self.id_parceiro, self.login_id) == (other.id_parceiro, other.login_id)


UTC_TIMEZONE_MAP = {
    -5: LocalEmbarque.TimeZone.ACRE,
    -4: LocalEmbarque.TimeZone.AMAZONIA,
    -3: LocalEmbarque.TimeZone.BRASILIA,
    -2: LocalEmbarque.TimeZone.NORONHA,
}


@async_memoize_with_log(60 * 30)
async def get_timezones() -> dict[str, str]:
    cidades_internas = CidadeInternal.objects.values("name", "uf", "timezone")
    cidades_internas = await sync_to_async(list)(cidades_internas)

    return {
        _make_key(strip_punctuation(cidade["name"]), strip_punctuation(cidade["uf"])): cidade["timezone"]
        for cidade in cidades_internas
    }


def _make_key(city_normalized_name: str, uf_normalized_acronym) -> str:
    return f"{city_normalized_name} - {uf_normalized_acronym}"


class Itinerario(AutoFieldAbstract):
    login = models.ForeignKey(Login, on_delete=models.CASCADE)
    itinerario_hash = models.TextField(db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = [
            ("login", "itinerario_hash"),
        ]

    @property
    def checkpoints(self):
        return self.checkpoint_set.all().order("idx")

    @classmethod
    def from_checkpoints(cls, checkpoints, **kwargs):
        itinerario_hash = "-".join([f"{cp.local.id_parceiro}_{cp.duracao}" for cp in checkpoints])
        obj = cls(itinerario_hash=itinerario_hash, **kwargs)
        obj._checkpoints = checkpoints
        idx = 0
        for checkpoint in checkpoints:
            checkpoint.idx = idx
            checkpoint.itinerario = obj
            idx += 1
        return obj


class Checkpoint(AutoFieldAbstract):
    itinerario = models.ForeignKey(Itinerario, on_delete=models.CASCADE)
    local = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE)
    idx = models.IntegerField()
    duracao = models.DurationField()
    distancia = models.DecimalField(decimal_places=2, max_digits=12)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = [
            ("itinerario", "idx"),
        ]


class Viagem(AutoFieldAbstract):
    login = models.ForeignKey(Login, on_delete=models.CASCADE)
    itinerario = models.ForeignKey(Itinerario, on_delete=models.CASCADE)
    id_parceiro = models.TextField(db_index=True)
    chave_viagem = models.CharField(max_length=100, unique=True, db_index=True)
    classe_parceiro = models.TextField()
    datetime_ida = models.DateTimeField()
    capacidade = models.IntegerField()
    ativo = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def trechos(self):
        return self.trecho_set.all()

    def set_chave_viagem(self):
        if self.chave_viagem:
            return

        self.chave_viagem = (
            f"{self.login_id}_{self.id_parceiro}_{self.datetime_ida}_{self.classe_parceiro}_{self.itinerario.id}"
        )

    def __hash__(self):
        return hash(self.id) if self.id else hash(self.chave_viagem)

    def __eq__(self, other):
        if not isinstance(other, Viagem):
            return False

        if self.id and other.id:
            return self.id == other.id

        return self.chave_viagem == other.chave_viagem


class Trecho(AutoFieldAbstract):
    origem = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE, related_name="+")
    destino = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE, related_name="+")
    viagem = models.ForeignKey(Viagem, on_delete=models.CASCADE)
    datetime_ida = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    # datetime_chegada ???

    class Meta:
        unique_together = [
            ("viagem", "origem", "destino"),
        ]


class DadoTrecho(AutoFieldAbstract):
    trecho = models.ForeignKey(Trecho, on_delete=models.CASCADE)
    vagas = models.IntegerField()
    preco = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

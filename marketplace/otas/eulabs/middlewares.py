from marketplace.otas.eulabs import EulabsLogin
from marketplace.otas.executors import Request
from marketplace.otas.executors.middlewares import Middleware


class LoginEulabsMiddleware(Middleware):
    def __init__(self, login: EulabsLogin) -> None:
        self.login = login
        super().__init__()

    def preprocess_request(self, request: Request) -> Request:
        request.headers = request.headers or {}
        request.headers["X-Eucatur-Api-Id"] = str(self.login.api_id)
        request.headers["X-Eucatur-Api-Key"] = self.login.api_key
        return super().preprocess_request(request)

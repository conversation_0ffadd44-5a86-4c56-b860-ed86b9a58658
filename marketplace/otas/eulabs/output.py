from dataclasses import dataclass, field


@dataclass
class FreeSeatsBenefits:
    type: str
    free: int
    amount: float


@dataclass
class Category:
    seat_map_id: int
    vehicle_type_id: int
    description: str
    short_description: str
    category_sat: str
    initial_seat: int
    final_seat: int
    free_seats: int


@dataclass
class Tariff:
    tariff: float
    insurance: float
    toll: float
    boarding_fee: float
    ferry: float
    additional: float
    price_promotional: float
    calculation: float
    referential: float
    resource_discount: float
    total_km: float
    category: Category


@dataclass
class TravelClass:
    id: int
    long_name: str
    short_name: str


@dataclass
class Company:
    id: int
    code: str
    name: str
    logo: str


@dataclass
class Item:
    id: int
    road_item_id: int
    gateway_id: int
    gateway_type: str
    station_id_origin: int
    station_id_destiny: int
    service_travel: str
    reference_travel: str
    datetime_departure: str
    datetime_arrival: str
    duration: str
    free_seats: int
    tariff: float
    insurance: float
    fee: float
    travel_item_toll: float
    price: float
    price_promotional: float
    tax: float
    _class: TravelClass = field(metadata={"name": "class"})
    company: Company
    line_code: str
    direction: str
    vehicle_id: int
    tariffs: list[Tariff]


@dataclass
class TravelData:
    key: str
    id: int
    origin_sectional_id: int
    destiny_sectional_id: int
    datetime_departure: str
    datetime_arrival: str
    duration: str
    free_seats: int
    price: float
    price_promotional: float
    free_seats_benefits: list[FreeSeatsBenefits]
    items: list[Item]


@dataclass
class TravelSummary:
    id: int
    line_code: str
    departure_date: str
    departure_time: str
    description: str
    company_id: int
    schedule_id: int


@dataclass
class TravelLeg:
    arrival_zone: str
    departure_time_zone: str
    local_arrival_date_time: str
    local_exit: str
    seccional_code: str
    seccional_id: int
    seccional_name: str
    stop_time: str
    total_time: str
    total_km: float
    uf_acronym: str


@dataclass
class TravelDetail:
    origin_id: int
    origin_name: str
    destination_id: int
    destination_name: str
    _class: str = field(metadata={"name": "class"})
    seats: int
    capacity: int
    class_description: str


@dataclass
class Sectional:
    id: int
    code: str
    description: str
    uf_acronym: str

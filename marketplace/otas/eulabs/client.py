from datetime import date
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>

from dacite import from_dict

from marketplace.otas import BaseClient
from marketplace.otas.eulabs import EulabsLogin
from marketplace.otas.eulabs.middlewares import LoginEulabsMiddleware
from marketplace.otas.eulabs.output import Sectional, TravelData, TravelDetail, TravelLeg, TravelSummary
from marketplace.otas.executors import Request
from marketplace.otas.executors.httpx import HttpxExecutor, HttpxResponse
from marketplace.otas.executors.middlewares.cloudflare_middleware import CloudFlareMiddleware


class EulabsClient(BaseClient):
    DEFAULT_TIMEOUT = 90

    def __init__(self, login: EulabsLogin):
        self.login: EulabsLogin = login
        self.executor = HttpxExecutor(
            base_url=login.base_url, middlewares=[LoginEulabsMiddleware(self.login), CloudFlareMiddleware()]
        )
        self.base_url = self.login.base_url
        super().__init__()

    async def travels_search(self, origem_id: int, destino_id: int, data: date) -> list[TravelData]:
        request = Request(
            method=HTTPMethod.GET,
            endpoint="road/travels/search",
            params={
                "origin_sectional_id": origem_id,
                "destiny_sectional_id": destino_id,
                "departure_date": data.strftime("%Y-%m-%d"),
            },
        )

        response: HttpxResponse = await self.executor.asend(request)
        response._inner.raise_for_status()
        response_json = response.json()

        response_data = []
        for travel in response_json:
            for item in travel["items"]:
                item["_class"] = item.pop("class")
            response_data.append(from_dict(data_class=TravelData, data=travel))

        return response_data

    async def list_road_travels_summary(self, start_date: date, end_date: date) -> list[TravelSummary]:
        params = {
            "initial_departure_date": start_date.strftime("%Y-%m-%d"),
            "final_departure_date": end_date.strftime("%Y-%m-%d"),
        }
        request = Request(method=HTTPMethod.GET, endpoint="road/travels/summary", params=params)

        response = await self.executor.asend(request)
        response._inner.raise_for_status()

        return [TravelSummary(**item) for item in response.json()]

    async def get_road_travel_summary(self, travel_id) -> list[TravelLeg]:
        request = Request(method=HTTPMethod.GET, endpoint=f"road/travels/summary/{travel_id}")

        response = await self.executor.asend(request)
        response._inner.raise_for_status()

        return [TravelLeg(**item) for item in response.json()]

    async def get_road_travel_detail(self, travel_id) -> list[TravelDetail]:
        request = Request(method=HTTPMethod.GET, endpoint=f"road/travels/detail/{travel_id}")

        response = await self.executor.asend(request)
        response._inner.raise_for_status()

        return [TravelDetail(_class=item.pop("class"), **item) for item in response.json()]

    async def sectionals(self) -> list[Sectional]:
        request = Request(method=HTTPMethod.GET, endpoint="sectionals", params={"is_road_station": True})

        response = await self.executor.asend(request)
        response._inner.raise_for_status()

        return [Sectional(**sectional) for sectional in response.json()]

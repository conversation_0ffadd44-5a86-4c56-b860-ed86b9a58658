import asyncio
import logging
from dataclasses import dataclass
from datetime import date, datetime, timedelta
from typing import AsyncGenerator

from dacite import from_dict
from django.utils import timezone

from commons.dateutils import to_tz
from marketplace.models import UTC_TIMEZONE_MAP, Checkpoint, Itinerario, LocalEmbarque, Login, Trecho, Viagem
from marketplace.otas.eulabs import EulabsLogin
from marketplace.otas.eulabs.client import EulabsClient
from marketplace.otas.eulabs.output import TravelDetail, TravelLeg
from marketplace.otas.exceptions import RodoviariaUnprocessableResult
from marketplace.otas.updater import OTAUpdater, OTAUpdaterTask, UpdateCriteria

logger = logging.getLogger("rodoviaria")


@dataclass
class EulabsUpdaterTask(OTAUpdaterTask):
    departure_date: str
    departure_time: str
    travel_id: int

    @property
    def integracao(self):
        return "eulabs"


async def is_viagem_recem_atualizada(task: OTAUpdaterTask) -> bool:
    last_update_limite = to_tz(timezone.now(), "America/Sao_Paulo") - timedelta(minutes=15)
    return not await Viagem.objects.filter(
        id_parceiro=task.travel_id, login_id=task.login_id, updated_at__gt=last_update_limite
    ).aexists()


class EulabsUpdater(OTAUpdater):
    def __init__(self, login: Login, criterias: list[UpdateCriteria] | None = None):
        if criterias is None:
            criterias = [is_viagem_recem_atualizada]
        super().__init__(login, EulabsClient(EulabsLogin(login)), criterias)

    def _get_road_travel_summary(self, travel_id: int) -> list[TravelLeg]:
        return self.limiter.wrap(self.retry_policy(self.client.get_road_travel_summary)(travel_id))

    def _get_road_travel_detail(self, travel_id: int) -> list[TravelDetail]:
        return self.limiter.wrap(self.retry_policy(self.client.get_road_travel_detail)(travel_id))

    async def generate_tasks(self, start_date: date, end_date: date) -> AsyncGenerator[EulabsUpdaterTask, None]:
        for travel_summary in await self.client.list_road_travels_summary(start_date, end_date):
            yield EulabsUpdaterTask(
                departure_date=travel_summary.departure_date,
                departure_time=travel_summary.departure_time,
                travel_id=travel_summary.id,
                login_id=self.login.id,
            )

    def parse(self, message_body: dict) -> EulabsUpdaterTask:
        return from_dict(EulabsUpdaterTask, message_body)

    async def should_process_task(self, task: OTAUpdaterTask) -> tuple[bool, str]:
        for criteria in self.criterias:
            if not await criteria(task):
                return False, str(criteria)
        return True, ""

    async def process_task(self, task: EulabsUpdaterTask) -> list[Trecho]:
        should_process, criteria = await self.should_process_task(task)
        if not should_process:
            logger.info("Process task: Skip task travel_id=%s, criteria=%s", task.travel_id, criteria)
            return

        async with asyncio.TaskGroup() as tg:
            task1 = tg.create_task(self._get_road_travel_summary(task.travel_id))
            task2 = tg.create_task(self._get_road_travel_detail(task.travel_id))

        travel_leg_list = task1.result()
        travel_detail_list = task2.result()

        if not travel_leg_list:
            logger.info("Process task: Lista de travel_leg vazia travel_id=%s", task.travel_id)
            return

        if not travel_detail_list:
            logger.info("Process task: Lista de travel_detail vazia travel_id=%s", task.travel_id)
            return

        checkpoints = _make_checkpoints(travel_leg_list, self.login)
        itinerario = Itinerario.from_checkpoints(checkpoints, login=self.login)
        return _make_viagens(
            travel_detail_list,
            travel_leg_list,
            self.login,
            itinerario,
            task.travel_id,
            task.departure_date,
            task.departure_time,
        )


def _make_viagens(
    travel_detail_list: list[TravelDetail],
    travel_leg_list: list[TravelLeg],
    login: Login,
    itinerario: Itinerario,
    travel_id: int,
    departure_date: str,
    departure_time: str,
) -> list[Viagem]:
    trechos = []
    travel_leg_map = _make_travel_leg_map(travel_leg_list)
    viagem_map = {}

    for travel_detail in travel_detail_list:
        travel_leg_origem = _get_travel_leg(travel_leg_map, travel_detail.origin_id)
        travel_leg_destino = _get_travel_leg(travel_leg_map, travel_detail.destination_id)

        origem = _make_local_embarque(travel_leg_origem, login)
        destino = _make_local_embarque(travel_leg_destino, login)

        datetime_ida_trecho = to_tz(datetime.fromisoformat(travel_leg_origem.local_exit), origem.timezone)
        datetime_ida_travel = to_tz(datetime.fromisoformat(f"{departure_date} {departure_time}"), origem.timezone)

        viagem = _make_viagem(
            login,
            itinerario,
            travel_id,
            datetime_ida_travel,
            travel_detail.class_description,
            travel_detail.capacity,
            viagem_map,
        )
        trecho = Trecho(viagem=viagem, origem=origem, destino=destino, datetime_ida=datetime_ida_trecho)
        trechos.append(trecho)

    return trechos


def _make_viagem(
    login: Login,
    itinerario: Itinerario,
    travel_id: int,
    datetime_ida,
    classe_parceiro: str,
    capacidade: int,
    viagem_map: dict[int, Viagem],
) -> Itinerario:
    try:
        viagem = viagem_map[classe_parceiro]
        if viagem.capacidade != capacidade:
            raise RodoviariaMultipleCapacityClass(travel_id, classe_parceiro)
        return viagem
    except KeyError:
        pass

    viagem_map[classe_parceiro] = Viagem(
        login=login,
        itinerario=itinerario,
        id_parceiro=travel_id,
        datetime_ida=datetime_ida,
        classe_parceiro=classe_parceiro,
        capacidade=capacidade,
    )

    return viagem_map[classe_parceiro]


def _get_travel_leg(travel_leg_map: dict[int, TravelLeg], sectional_id: int) -> TravelLeg:
    try:
        return travel_leg_map[sectional_id]
    except KeyError as err:
        raise RodoviariaTravelLegNotFound(sectional_id) from err


def _make_travel_leg_map(travel_leg_list: list[TravelLeg]) -> dict[int, TravelLeg]:
    return {travel_leg.seccional_id: travel_leg for travel_leg in travel_leg_list}


def _get_timezone(timezone: str) -> LocalEmbarque.TimeZone:
    hour = timezone.split(":")[0]
    return UTC_TIMEZONE_MAP[int(hour) - 3]


def _make_local_embarque(travel_leg: TravelLeg, login: Login) -> LocalEmbarque:
    if travel_leg.arrival_zone != travel_leg.departure_time_zone:
        raise RodoviariaMismatchTimezone(
            travel_leg.arrival_zone, travel_leg.departure_time_zone, travel_leg.seccional_id
        )

    try:
        timezone = _get_timezone(travel_leg.arrival_zone)
    except Exception as err:
        raise RodoviariaNoutFoundTimezone(travel_leg.arrival_zone, travel_leg.seccional_id) from err

    return LocalEmbarque(
        name=f"{travel_leg.seccional_name} - {travel_leg.uf_acronym}",
        id_parceiro=travel_leg.seccional_id,
        login=login,
        timezone=timezone,
    )


def _make_checkpoints(travel_leg_list: list[TravelLeg], login: Login) -> list[Checkpoint]:
    previous_leg = travel_leg_list[0]
    local_embarque = _make_local_embarque(previous_leg, login)
    checkpoints = [
        Checkpoint(
            local=local_embarque,
            idx=0,
            duracao=timedelta(0),
            distancia=0,
        )
    ]

    for current_leg in travel_leg_list[1:]:
        duracao = datetime.fromisoformat(current_leg.local_exit) - datetime.fromisoformat(previous_leg.local_exit)
        distancia = current_leg.total_km - previous_leg.total_km
        local_embarque = _make_local_embarque(current_leg, login)

        checkpoints.append(
            Checkpoint(
                local=local_embarque,
                duracao=duracao,
                distancia=distancia,
            )
        )

        previous_leg = current_leg

    return checkpoints


class RodoviariaTravelLegNotFound(RodoviariaUnprocessableResult):
    def __init__(sectional_id: int):
        return super().__init__(f"Could not find travel leg for sectional_id={sectional_id}")


class RodoviariaMismatchTimezone(RodoviariaUnprocessableResult):
    def __init__(self, timezone_arrival: str, timezone_departure: str, sectional_id: int):
        return super().__init__(
            f"Mismatch timezones! arrival={timezone_arrival} departure={timezone_departure} sectional_id={sectional_id}"
        )


class RodoviariaNoutFoundTimezone(RodoviariaUnprocessableResult):
    def __init__(self, timezone: str, sectional_id: int):
        return super().__init__(f"Not found timezone for {timezone} sectional_id={sectional_id}")


class RodoviariaNotFoundItinerary(RodoviariaUnprocessableResult):
    def __init__(self, origem_sectional_id: int, destino_sectional_id: int):
        return super().__init__(
            f"Not found itinerary for sectional origem_id={origem_sectional_id} destino_id={destino_sectional_id}"
        )


class RodoviariaMultipleCapacityClass(RodoviariaUnprocessableResult):
    def __init__(self, travel_id: int, classe_parceiro: str):
        return super().__init__(f"Class {classe_parceiro} from travel_id={travel_id} with multiple capacity values")

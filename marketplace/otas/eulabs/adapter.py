from datetime import date, datetime
from decimal import Decimal

from marketplace.models import DadoTrecho, LocalEmbarque, Login, Trecho, Viagem
from marketplace.otas import BaseAdapter
from marketplace.otas.eulabs import EulabsLogin
from marketplace.otas.eulabs.client import EulabsClient
from marketplace.otas.eulabs.output import TravelData


class EulabsAdapter(BaseAdapter):
    def __init__(self, login: Login) -> None:
        self.client = EulabsClient(EulabsLogin(login))
        self.login = login
        super().__init__()

    async def buscar_trechos(self, origem: LocalEmbarque, destino: LocalEmbarque, data: date) -> list[DadoTrecho]:
        travel_data_list: list[TravelData] = await self.client.travels_search(
            origem_id=origem.id_parceiro, destino_id=destino.id_parceiro, data=data
        )
        dado_trechos = []
        for travel_data in travel_data_list:
            for item in travel_data.items:
                for tariff in item.tariffs:
                    viagem = Viagem(
                        login=self.login,
                        id_parceiro=travel_data.id,
                        datetime_ida=datetime.fromisoformat(item.datetime_departure),
                        capacidade=tariff.category.free_seats,
                        classe_parceiro=tariff.category.description,
                    )
                    trecho = Trecho(origem=origem, destino=destino, viagem=viagem)
                    dado_trechos.append(
                        DadoTrecho(
                            trecho=trecho,
                            preco=Decimal(str(tariff.price_promotional)),
                            vagas=tariff.category.free_seats,
                        ),
                    )
        return dado_trechos

    async def buscar_locais_embarque(self) -> list[LocalEmbarque]:
        locais = await self.client.sectionals()

        return [
            LocalEmbarque(
                id_parceiro=local.id,
                name=local.description,
                login=self.login,
            )
            for local in locais
        ]

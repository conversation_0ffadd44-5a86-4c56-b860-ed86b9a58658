from enum import IntEnum
from http import HTTPStatus
from typing import Dict

from marketplace.otas.exceptions import RodoviariaConnectionError, RodoviariaTooManyRequestsError
from marketplace.otas.executors import Response
from marketplace.otas.executors.middlewares import Middleware


class CloudflareHTTPStatus(IntEnum):
    """Cloudflare specific HTTP status codes"""

    def __new__(cls, value, phrase, description=""):
        obj = int.__new__(cls, value)
        obj._value_ = value

        obj.phrase = phrase
        obj.description = description
        return obj

    CLOUDFLARE_EMPTY_RESPONSE = (
        520,
        "Cloudflare Empty Response",
        "The origin server returns an empty, unknown, or unexpected response to Cloudflare",
    )
    CLOUDFLARE_OFFLINE = (
        521,
        "Cloudflare Offline or Blocked requests",
        "The origin web server refuses connections from Cloudflare",
    )
    CLOUDFLARE_HANDSHAKE_TIMEOUT = (522, "Cloudflare Timeout", "Cloudflare times out contacting the origin web server")
    CLOUDFLARE_UNREACHABLE = (
        523,
        "Cloudflare Origin is unreachable",
        "<PERSON>flare cannot contact your origin web server",
    )
    CLOUDFLARE_CONNECTION_TIMEOUT = (
        524,
        "Cloudflare Timeout",
        "Cloudflare successfully connected to the origin but did not provide an HTTP response",
    )
    CLOUDFLARE_SSL_FAILED = (
        525,
        "Cloudflare SSL handshake failed",
        "SSL handshake between Cloudflare and the origin web server failed",
    )
    CLOUDFLARE_SSL_INVALID = (
        526,
        "Cloudflare invalid SSL certificate",
        "Cloudflare cannot validate the SSL certificate at your origin web server",
    )


class CloudFlareMiddleware(Middleware):
    CLOUDFLARE_EXCEPTIONS: Dict[int, str] = {status.value: status.description for status in CloudflareHTTPStatus}

    def process_response(self, response: Response) -> Response:
        if response.ok:
            return super().process_response(response)

        status_code = response.status_code

        if status_code == HTTPStatus.NOT_ACCEPTABLE:
            message = f"{status_code} too many requests"
            raise RodoviariaTooManyRequestsError(message)

        if status_code in self.CLOUDFLARE_EXCEPTIONS:
            message = f"{status_code} {self.CLOUDFLARE_EXCEPTIONS[status_code]}"
            raise RodoviariaConnectionError(message)

        return super().process_response(response)

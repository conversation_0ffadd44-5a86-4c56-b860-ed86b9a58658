from datetime import timedel<PERSON>
from typing import Any

import httpx
from asgiref.sync import async_to_sync

from marketplace.otas.exceptions import RodoviariaConnectionError
from marketplace.otas.executors import AsyncExecutor, Request, Response


class HttpxResponse(Response):
    def __init__(self, request: Request, response: httpx.Response):
        super().__init__(request)
        self._inner = response

    def json(self) -> Any:
        return self._inner.json()

    @property
    def status_code(self) -> int:
        return self._inner.status_code

    @property
    def elapsed(self) -> timedelta:
        return self._inner.elapsed


class HttpxExecutor(AsyncExecutor):
    LOGGER_NAME = f"{__name__}.HttpxExecutor"
    DEFAULT_TIMEOUT = 60

    def __init__(self, base_url: str, middlewares=None, timeout=DEFAULT_TIMEOUT, *args, **kwargs):
        super().__init__()
        self._log("Creating session")
        self._client = httpx.AsyncClient(base_url=base_url)
        self.middlewares = middlewares or []
        self.timeout = timeout

    def send(self, request: Request) -> Response:
        return async_to_sync(self.asend)(request)

    async def asend(self, request: Request) -> Response:
        middlewares = self._get_middlewares(request)
        self._log("Request sent %s", request.endpoint, level="debug")

        try:
            for middleware in middlewares:
                request = middleware.preprocess_request(request)

            response = await self._client.request(
                request.method,
                request.endpoint,
                params=request.params,
                json=request.json_params,
                timeout=request.timeout,
                headers=request.headers,
            )

            response = HttpxResponse(request, response)

            for middleware in middlewares:
                response = middleware.process_response(response)

            self._log("Response received %s %s", request.endpoint, response.status_code, level="debug")
            return response
        except httpx.TransportError as exc:
            log_msg = f"'{request.method} {request.endpoint}' data={request.json_params} connection error"
            self._log(message=log_msg, level="warning", exc_info=True)
            raise RodoviariaConnectionError(f"{request.message_prefix} {request.endpoint} connection error") from exc
        except Exception as exc:
            self._log(message="An exception occurred", level="warning", exc_info=True)

            for middleware in middlewares:
                middleware.on_exception(request, exc)

            raise

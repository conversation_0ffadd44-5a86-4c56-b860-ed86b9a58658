import abc
import logging
from datetime import timedel<PERSON>
from http import H<PERSON><PERSON><PERSON><PERSON>, HTTPStatus
from typing import Any

from marketplace.otas.executors.middlewares import Middleware


class Response:
    def __init__(self, request: "Request"):
        self.request = request

    @property
    @abc.abstractmethod
    def status_code(self) -> HTTPStatus: ...

    @property
    @abc.abstractmethod
    def elapsed(self) -> timedelta: ...

    @abc.abstractmethod
    def json(self) -> Any: ...

    @abc.abstractmethod
    def text(self) -> str: ...

    @property
    def ok(self):
        return self.status_code < 400

    def __repr__(self):
        return f"<Response: status_code={self.status_code} request={self.request}>"


class Request:
    middlewares = []
    message_prefix: str = ""

    method: HTTPMethod = HTTPMethod.GET
    endpoint: str = ""
    timeout: int | None = None
    headers: dict[str, str] | None = None
    params: dict[str, Any] | None = None
    json_params: dict[str, Any] | None = None

    def __init__(
        self,
        method: HTTPMethod = HTTPMethod.GET,
        endpoint: str = "",
        timeout: float | None = None,
        headers: dict[str, str] | None = None,
        params: dict[str, Any] | None = None,
        json_params: dict[str, Any] | None = None,
    ):
        self.method = method
        self.endpoint = endpoint
        if headers is not None:
            self.headers = headers
        if params is not None:
            self.params = params
        if json_params is not None:
            self.json_params = json_params
        if timeout is not None:
            self.timeout = timeout

    def add_middleware(self, middleware: Middleware):
        self.middlewares.append(middleware)


class Executor:
    LOGGER_NAME = f"{__name__}.Executor"

    def __init__(self):
        self._logger = logging.getLogger(self.LOGGER_NAME)
        self.middlewares = []

    @abc.abstractmethod
    def send(self, request: Request) -> Response: ...

    def add_middleware(self, middleware: Middleware):
        self.middlewares.append(middleware)

    def _log(self, message, *args, level="info", **kwargs):
        log_fn = getattr(self._logger, level)
        return log_fn(f"[{self.__class__.__name__}] {message}", *args, **kwargs)

    def _get_middlewares(self, request: Request) -> list[Middleware]:
        middlewares = self.middlewares
        if request.middlewares:
            middlewares += request.middlewares
        return middlewares


class AsyncExecutor(Executor):
    @abc.abstractmethod
    async def asend(self, request: Request) -> Response: ...

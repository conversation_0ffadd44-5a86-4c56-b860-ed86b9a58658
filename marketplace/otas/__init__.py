import abc
from datetime import date

import httpx

from marketplace.models import Integracao, LocalEmbarque, Login, Trecho
from marketplace.otas.updater import OTAUpdater


class InvalidConfigurationError(RuntimeError): ...


def get_adapter(login: Login):
    from marketplace.otas.eulabs.adapter import EulabsAdapter

    ADAPTERS_MAP = {Integracao.API.EULABS: EulabsAdapter}
    cls = ADAPTERS_MAP.get(login.integracao.name)
    if cls is None:
        raise InvalidConfigurationError(f'Updater not found for OTA "{login.type}"')
    return cls(login)


def get_updater_from_ota_login(login: Login) -> OTAUpdater:
    from marketplace.otas.eulabs.updater import EulabsUpdater

    UPDATERS_MAP = {"eulabs": EulabsUpdater}
    cls = UPDATERS_MAP.get(login.integracao.name)
    if cls is None:
        raise InvalidConfigurationError(f'Updater not found for OTA "{login.integracao.name}"')

    return cls(login)


class OTALogin:
    def __init__(self, login: Login) -> None:
        self.login = login
        self.integracao = login.integracao
        self.base_url = login.base_url


class BaseClient:
    DEFAULT_TIMEOUT = 60
    base_url = None

    def __init__(self):
        self._client = httpx.AsyncClient()

    def endpoint(self, endpoint: str) -> str:
        return f"{self.base_url}/{endpoint}"


class BaseAdapter:
    @abc.abstractmethod
    async def buscar_trechos(self, origem_id: int, destino_id: int, data: date) -> list[Trecho]: ...

    @abc.abstractmethod
    async def buscar_locais_embarque(self) -> list[LocalEmbarque]: ...

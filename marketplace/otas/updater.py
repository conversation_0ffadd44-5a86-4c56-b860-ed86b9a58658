import asyncio
from abc import ABC, abstractmethod
from dataclasses import asdict, dataclass
from datetime import date
from typing import AsyncGenerator, Awaitable, Callable

import asynciolimiter
import orjson
from tenacity import retry, stop_after_attempt, wait_random_exponential

from marketplace.models import Login, Trecho


@dataclass
class OTAUpdaterTask(ABC):
    login_id: int

    @property
    @abstractmethod
    def integracao(self):
        pass

    def to_json(self):
        task_dict = asdict(self)
        task_dict["integracao"] = self.integracao
        return orjson.dumps(task_dict)


UpdateCriteria = Callable[[OTAUpdaterTask], Awaitable[bool]]


@dataclass
class OTAUpdater(ABC):
    def __init__(self, login: Login, client, criterias: list[UpdateCriteria]):
        self.login = login
        self.client = client
        self.criterias = criterias
        self.limiter = asynciolimiter.StrictLimiter(login.rate_limit)  # TODO: use a global token bucket

        # TODO: parametrize this!
        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=5, max=30),
            stop=stop_after_attempt(3),
            sleep=asyncio.sleep,
        )

    @abstractmethod
    def generate_tasks(self, start_date: date, end_date: date) -> AsyncGenerator[OTAUpdaterTask, None]:
        pass

    @abstractmethod
    async def process_task(self, task: OTAUpdaterTask) -> list[Trecho]:
        pass

    @abstractmethod
    def parse(self, message_body: dict) -> OTAUpdaterTask:
        pass

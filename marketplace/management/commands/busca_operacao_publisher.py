import asyncio

from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand
from django.utils import timezone

from commons.redis import lock
from marketplace.estoque.services import busca_operacao
from marketplace.models import CronogramaAtualizacao

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Dispara tasks de atualização para todos logins"

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options) -> str | None:
        cronogramas = CronogramaAtualizacao.objects.select_related("login", "login__integracao").all()

        for cronograma in cronogramas:
            if not cronograma.should_run_now():
                continue

            busca_operacao_cronograma(cronograma)


@lock("busca_operacao_cronograma_{cronograma.id}", expire=60 * 60)
def busca_operacao_cronograma(cronograma: CronogramaAtualizacao):
    start_date, end_date = cronograma.calcula_inicio_e_fim()
    asyncio.run(busca_operacao.busca_operacao(cronograma.login, start_date, end_date, cronograma.priority))
    cronograma.last_run_at = timezone.now()
    cronograma.save(update_fields=["last_run_at", "updated_at"])

import asyncio

import aio_pika
import orjson
from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from bp import settings
from marketplace.commons.utils import get_login
from marketplace.estoque.services.busca_operacao import create_viagens
from marketplace.models import Login, Priority
from marketplace.otas import get_updater_from_ota_login
from marketplace.otas.updater import OTAUpdater

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Inicia worker para determinado login"

    def add_arguments(self, parser):
        parser.add_argument("--nome_empresa", type=str, help="Nome da empresa", required=True)
        parser.add_argument("--login_id", type=int, help="Login da empresa")

    def handle(self, *args, **options) -> str | None:
        login_id = options["login_id"]
        nome_empresa = options["nome_empresa"]
        login = get_login(nome_empresa, login_id)
        return asyncio.run(worker(login))


async def worker(login: Login):
    rabbitmq_conn = await aio_pika.connect_robust(settings.CELERY_BROKER_URL)

    channel = await rabbitmq_conn.channel()
    qos = login.payload.get("qos", 120)
    await channel.set_qos(prefetch_count=qos)

    queue = await channel.declare_queue(login.queue_name, durable=True, arguments={"x-max-priority": Priority.HIGH})

    updater = get_updater_from_ota_login(login)
    await queue.consume(process_task(updater))

    try:
        # Wait until terminate
        await asyncio.Future()
    finally:
        await rabbitmq_conn.close()


def process_task(updater: OTAUpdater):
    async def process_message(message: aio_pika.abc.AbstractIncomingMessage):
        async with message.process():
            message_body = orjson.loads(message.body)
            task = updater.parse(message_body)
            task_logger.info("received task; task=%s", str(task))

            trechos = await updater.process_task(task)
            await create_viagens(trechos)
            task_logger.info("finished task; task=%s; result=%s", str(task), str(trechos))

    return process_message

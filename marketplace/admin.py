from django.contrib import admin

from .models import CronogramaAtualizacao, Integracao, LocalEmbarque, Login


@admin.register(Integracao)
class IntegracaoAdmin(admin.ModelAdmin):
    list_display = ("name", "created_at")
    search_fields = ("name",)
    list_filter = ("created_at",)
    ordering = ("-created_at",)


@admin.register(Login)
class LoginAdmin(admin.ModelAdmin):
    list_display = ("integracao", "nome_empresa", "id_empresa_parceiro", "base_url", "created_at", "updated_at")
    search_fields = ("nome_empresa", "integracao__name")
    list_filter = ("created_at", "updated_at", "integracao")
    ordering = ("-created_at",)


@admin.register(LocalEmbarque)
class LocalEmbarqueAdmin(admin.ModelAdmin):
    list_display = ("id_parceiro", "name", "login", "created_at", "updated_at")
    search_fields = ("id_parceiro", "name", "login__nome_empresa")
    list_filter = ("created_at", "updated_at", "login")
    ordering = ("-created_at",)


@admin.register(CronogramaAtualizacao)
class CronogramaAtualizacaoAdmin(admin.ModelAdmin):
    list_display = ("login", "tipo_atualizacao", "created_at", "updated_at")
    search_fields = ("id_parceiro", "name", "login__nome_empresa")
    list_filter = ("created_at", "updated_at", "login")
    ordering = ("-created_at",)

    def formfield_for_dbfield(self, db_field, **kwargs):
        if db_field.name in ["dia_do_mes", "dia_da_semana", "last_run_at"]:
            kwargs["required"] = False
        return super().formfield_for_dbfield(db_field, **kwargs)

from django.core.exceptions import ValidationError

from marketplace.models import Login


def get_login(nome_empresa: str, login_id: int | None) -> Login:
    if login_id:
        return Login.objects.select_related("integracao").get(nome_empresa=nome_empresa, id=login_id)

    try:
        return Login.objects.select_related("integracao").get(nome_empresa=nome_empresa)
    except Login.MultipleObjectsReturned as err:
        raise ValidationError(f"A empresa {nome_empresa} tem múltiplos logins. É necessário enviar o ID.") from err

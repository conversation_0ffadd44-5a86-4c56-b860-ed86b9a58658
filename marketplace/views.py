from django.http import JsonResponse
from ninja import NinjaAPI

from marketplace.estoque.services import busca_operacao
from marketplace.models import Login

api = NinjaAPI()


@api.post("/update-locais/")
async def update_locais_view(request, login_id: int):
    try:
        login = await Login.objects.aget(id=login_id)
    except Login.DoesNotExist:
        return JsonResponse({"error": "Login not found"}, status=404)

    locais_atualizados = await busca_operacao.update_locais_embarque(login)
    return JsonResponse(
        {"message": f"{(len(locais_atualizados))} Locais de embarque atualizados com sucesso"}, status=200
    )

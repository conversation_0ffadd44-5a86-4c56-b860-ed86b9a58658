import logging
from datetime import date

import aio_pika

from bp import settings
from marketplace.models import <PERSON>point, Itinerario, LocalEmbarque, Login, Priority, Trecho, Viagem
from marketplace.otas import get_adapter, get_updater_from_ota_login

logger = logging.getLogger("rodoviaria")


async def busca_operacao(login: Login, start_date: date, end_date: date, priority: Priority):
    await task_producer(login, start_date, end_date, priority)


async def create_viagens(trechos: list[Trecho]):
    locais_embarques = await _resolve_locais(trechos)

    await LocalEmbarque.objects.abulk_create(
        locais_embarques,
        update_conflicts=True,
        unique_fields=["login", "id_parceiro"],
        update_fields=["updated_at"],
    )

    itinerario = trechos[0].viagem.itinerario
    await Itinerario.objects.abulk_create(
        [itinerario],
        update_conflicts=True,
        unique_fields=["login", "itinerario_hash"],
        update_fields=["updated_at"],
    )

    await Checkpoint.objects.abulk_create(
        itinerario._checkpoints,
        update_conflicts=True,
        unique_fields=["itinerario", "idx"],
        update_fields=["updated_at"],
    )

    [trecho.viagem.set_chave_viagem() for trecho in trechos]
    viagens = {trecho.viagem for trecho in trechos}
    await Viagem.objects.abulk_create(
        viagens, update_conflicts=True, unique_fields=["chave_viagem"], update_fields=["updated_at"]
    )

    await Trecho.objects.abulk_create(
        trechos,
        update_conflicts=True,
        unique_fields=["origem", "destino", "viagem"],
        update_fields=["updated_at"],
    )


async def _resolve_locais(trechos: list[Trecho]) -> list[LocalEmbarque]:
    # Podem existir diferentes endereços de memória com o "mesmo" local embarque
    # O django não consegue resolver no bulk_create, então precisamos associar o
    # mesmo endereço de memória para todos os locais iguais

    locais_memoria = set(
        [trecho.origem for trecho in trechos]
        + [trecho.destino for trecho in trechos]
        + [checkpoint.local for trecho in trechos for checkpoint in trecho.viagem.itinerario._checkpoints]
    )
    locais_memoria = {local.id_parceiro: local for local in locais_memoria}

    for checkpoint in [checkpoint for trecho in trechos for checkpoint in trecho.viagem.itinerario._checkpoints]:
        checkpoint.local = locais_memoria[checkpoint.local.id_parceiro]

    for trecho in trechos:
        trecho.origem = locais_memoria[trecho.origem.id_parceiro]
        trecho.destino = locais_memoria[trecho.destino.id_parceiro]

    return locais_memoria.values()


async def task_producer(login: Login, start_date: date, end_date: date, priority: Priority):
    rabbitmq_conn = await aio_pika.connect_robust(settings.CELERY_BROKER_URL)
    async with rabbitmq_conn:
        channel = await rabbitmq_conn.channel()

        await channel.declare_queue(
            login.queue_name,
            durable=True,
            arguments={"x-max-priority": Priority.HIGH},
        )

        updater = get_updater_from_ota_login(login)
        async for task in updater.generate_tasks(start_date, end_date):
            await channel.default_exchange.publish(
                aio_pika.Message(body=task.to_json(), priority=priority),
                routing_key=login.queue_name,
            )


async def update_locais_embarque(login: Login):
    locais_embarques: list[LocalEmbarque] = await get_adapter(login).buscar_locais_embarque()
    await LocalEmbarque.objects.abulk_create(locais_embarques, ignore_conflicts=True)
    return locais_embarques

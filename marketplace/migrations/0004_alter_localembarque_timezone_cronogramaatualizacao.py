# Generated by Django 5.1 on 2024-10-07 20:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0003_itinerario_trecho_dadotrecho_viagem_trecho_viagem_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='localembarque',
            name='timezone',
            field=models.CharField(blank=True, choices=[('America/Manaus', 'Amazonia'), ('America/Noronha', 'Noronha'), ('America/Rio_Branco', 'Acre'), ('America/Sao_Paulo', 'Brasilia')], max_length=256, null=True),
        ),
        migrations.CreateModel(
            name='CronogramaAtualizacao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo_atualizacao', models.TextField(choices=[('semana', 'Semana'), ('mes', 'Mes'), ('semestre', 'Semestre')])),
                ('hora', models.IntegerField()),
                ('dia_do_mes', models.IntegerField(null=True)),
                ('dia_da_semana', models.IntegerField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_run_at', models.DateTimeField(null=True)),
                ('login', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.login')),
            ],
        ),
    ]

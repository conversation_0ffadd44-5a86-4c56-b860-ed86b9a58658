# Generated by Django 5.1 on 2024-09-17 13:45

import django.db.models.deletion
import django_cryptography.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Integracao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Login',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome_empresa', models.TextField()),
                ('id_empresa_parceiro', models.IntegerField(blank=True, db_index=True, null=True)),
                ('base_url', models.URLField()),
                ('payload', models.JSONField(blank=True, default=dict, null=True)),
                ('encrypted_payload', django_cryptography.fields.encrypt(models.JSONField(blank=True, default=dict, null=True))),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('integracao', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.integracao')),
            ],
        ),
        migrations.CreateModel(
            name='LocalEmbarque',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_parceiro', models.CharField(max_length=30)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('login', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.login')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]

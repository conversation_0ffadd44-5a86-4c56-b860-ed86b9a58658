# Generated by Django 5.1 on 2024-09-19 13:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='localembarque',
            name='timezone',
            field=models.CharField(blank=True, choices=[('America/Araguaina', 'America/Araguaina'), ('America/Bahia', 'America/Bahia'), ('America/Belem', 'America/Belem'), ('America/Boa_Vista', 'America/Boa_Vista'), ('America/Campo_Grande', 'America/Campo_Grande'), ('America/Cuiaba', 'America/Cuiaba'), ('America/Eirunepe', 'America/Eirunepe'), ('America/Fortaleza', 'America/Fortaleza'), ('America/Maceio', 'America/Maceio'), ('America/Manaus', 'America/Manaus'), ('America/Noronha', 'America/Noronha'), ('America/Porto_Velho', 'America/Porto_Velho'), ('America/Recife', 'America/Recife'), ('America/Rio_Branco', 'America/Rio_Branco'), ('America/Santarem', 'America/Santarem'), ('America/Sao_Paulo', 'America/Sao_Paulo')], max_length=256, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='localembarque',
            unique_together={('login', 'id_parceiro')},
        ),
    ]

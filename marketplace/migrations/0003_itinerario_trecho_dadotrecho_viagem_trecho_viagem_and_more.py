# Generated by Django 5.1 on 2024-09-19 14:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0002_localembarque_timezone_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Itinerario',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('itinerario_hash', models.TextField(db_index=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('login', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.login')),
            ],
            options={
                'unique_together': {('login', 'itinerario_hash')},
            },
        ),
        migrations.CreateModel(
            name='Trecho',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('datetime_ida', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('destino', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='+', to='marketplace.localembarque')),
                ('origem', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='+', to='marketplace.localembarque')),
            ],
        ),
        migrations.CreateModel(
            name='DadoTrecho',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vagas', models.IntegerField()),
                ('preco', models.DecimalField(decimal_places=2, max_digits=12, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('trecho', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.trecho')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Viagem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_parceiro', models.TextField(db_index=True)),
                ('chave_viagem', models.CharField(db_index=True, max_length=100, unique=True)),
                ('classe_parceiro', models.TextField()),
                ('datetime_ida', models.DateTimeField()),
                ('capacidade', models.IntegerField()),
                ('ativo', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('itinerario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.itinerario')),
                ('login', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.login')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='trecho',
            name='viagem',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.viagem'),
        ),
        migrations.CreateModel(
            name='Checkpoint',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('idx', models.IntegerField()),
                ('duracao', models.DurationField()),
                ('distancia', models.DecimalField(decimal_places=2, max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('local', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.localembarque')),
                ('itinerario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.itinerario')),
            ],
            options={
                'unique_together': {('itinerario', 'idx')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='trecho',
            unique_together={('viagem', 'origem', 'destino')},
        ),
    ]

FROM python:3.11-slim-bullseye

# Replace shell with bash so we can source files
RUN rm /bin/sh && ln -s /bin/bash /bin/sh && \
    echo "export LS_OPTIONS='--color=auto'" >>~/.bashrc && \
    echo "eval "\`dircolors\`"" >>~/.bashrc && \
    echo "alias ls='ls \$LS_OPTIONS'" >>~/.bashrc && \
    echo "alias ll='ls \$LS_OPTIONS -l'" >>~/.bashrc && \
    echo "alias l='ls \$LS_OPTIONS -lA'" >>~/.bashrc

RUN apt-get update && \
    apt-get install --no-install-recommends -y wget gpg lsb-release && \
    echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list && \
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc \
      | gpg --dearmor \
      | tee /etc/apt/trusted.gpg.d/apt.postgresql.org.gpg >/dev/null && \
    apt-get update && \
    apt-get install --no-install-recommends -y \
    wait-for-it curl locales build-essential mime-support \
    # Instala o pg_dump para o django-migrations-ci funcionar.
    postgresql-client-14 && \
    apt-get purge -y build-essential && \
    rm -rf /var/lib/apt/lists/*

ENV SHELL=/bin/bash DJANGO_STATIC_ROOT=/dkdata/static HOST=0.0.0.0 PORT=3000

WORKDIR /app

COPY requirements.txt requirements.txt

RUN pip install --upgrade setuptools && pip install --no-cache-dir -r requirements.txt

COPY . .

RUN ./manage.py collectstatic --no-input

EXPOSE 8000

ENV PYTHONPATH=.

CMD "/app/devops/scripts/start.sh"

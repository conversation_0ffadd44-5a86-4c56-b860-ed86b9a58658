# Rodar com o seguinte comando:
#
# docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build

version: '3'
services:

  nginx:
    image: nginx:1.19-alpine
    command: nginx -g 'daemon off; error_log stderr;'
    stop_signal: SIGTERM
    volumes:
      - ./devops/templates/nginx_proxy.conf.j2:/tmp/nginx_proxy.conf.j2:ro
      - ./docker/compose/nginx/proxy_params:/etc/nginx/proxy_params:ro
      - ./docker/compose/nginx/setup-default-site.sh:/docker-entrypoint.d/setup-default-site.sh:ro
    ports:
      - 8000:8000

  django:
    &django
    build: .
    command: >-
      wait-for-it -t 0 postgres:5432 -- wait-for-it -t 0 rabbit:5672 -- wait-for-it -t 0 redis:6379 -- ./docker/compose/django/migrate-and-start.sh
    environment:
      DJANGO_DEBUG: 'false'
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: buser
      DB_USER: buser
      DB_PASS: buser
      RODOVIARIA_DB_HOST: postgres
      RODOVIARIA_DB_PORT: 5432
      RODOVIARIA_DB_NAME: buser_rodoviaria
      RODOVIARIA_DB_USER: buser
      RODOVIARIA_DB_PASS: buser
      CELERY_BROKER_URL: pyamqp://buser:buser@rabbit:5672//
      REDIS_URL: redis://redis:6379/0

  celery:
    <<: *django
    command: >-
      wait-for-it -t 0 django:8000 -- celery -A bp.celery.app worker --loglevel INFO -Q bp_cron,bp_descobrir_rotas,bp_motorista,bp_ping,bp_trechos_vendidos,bp_fetch_rotas,bp_alterar_grupo,bp_cadastros_vexado,bp_staff_update_link,bp_link_trecho_classe_hibrido,bp_cancela_grupo_classe_hibrido,bp_desbloquear_poltrona celery -A bp.celery.app worker --loglevel=INFO -Q bp_rotinas --pool=gevent --concurrency=600 -n rotinas@%h

  celerybeat:
    <<: *django
    command: >-
      wait-for-it -t 0 django:8000 -- celery -A bp.celery.app beat -s /tmp/celerybeat-schedule

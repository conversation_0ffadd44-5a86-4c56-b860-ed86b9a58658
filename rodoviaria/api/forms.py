from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, validator

from commons.dateutils import to_tz

from ..models.core import LocalEmbarque

STATUS_PASSAGEM_MAP = {
    "aprovado": "confirmada",
    "confirmed": "confirmada",
    "bpe_available": "confirmada",
    "canceled": "cancelada",
}


class ListBaseModel(BaseModel):
    __root__: List

    def __getitem__(self, idx: int):
        return self.__root__[idx]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    def dict(self):
        return [local.dict() for local in self.__root__]


class Localidade(BaseModel):
    nome_cidade: str
    external_local_id: str
    external_cidade_id: str
    uf: str
    complemento: Optional[str] = None
    id_cidade_ibge: Optional[int] = None

    @property
    def descricao(self):
        desc = f"{self.nome_cidade} - {self.uf}"
        if self.complemento:
            desc += f" - {self.complemento}"
        return desc


class LocalidadeItinerario(BaseModel):
    name: str = None
    uf: str = None
    nickname: str = None
    id_external: int = None
    rodoviaria_local_id: int = None


class CheckpointsForm(BaseModel):
    local_id: int = None
    cidade_id: int = None
    duracao: int = None
    distancia_km: Decimal = None
    tempo_embarque: int = None
    departure: datetime = None
    arrival: datetime = None
    local: LocalidadeItinerario

    @classmethod
    def from_itinerario(cls, company_rodoviaria_id, checkpoints):
        ids_external = [checkpoint.local.external_local_id for checkpoint in checkpoints]
        qs = LocalEmbarque.objects.select_related("cidade").filter(
            id_external__in=ids_external,
            cidade__company_id=company_rodoviaria_id,
        )
        data = {str(le.id_external): le for le in qs}
        result = []
        for n, checkpoint in enumerate(checkpoints):
            try:
                local_embarque = data[str(checkpoint.local.external_local_id)]
            except KeyError:
                local_embarque_internal_id = None
                cidade_internal_id = None

                # Define timezone default só para o primeiro checkpoint.
                # Para os outros, usa o timezone do checkpoint anterior.
                if n == 0:
                    timezone = "America/Sao_Paulo"

                local = LocalidadeItinerario(
                    name=checkpoint.local.descricao,
                    uf=checkpoint.local.uf,
                    nickname=checkpoint.local.descricao,
                    id_external=checkpoint.local.external_local_id,
                )
            else:
                local_embarque_internal_id = local_embarque.local_embarque_internal_id
                local_embarque_rodoviaria_id = local_embarque.id
                cidade_internal_id = local_embarque.cidade.cidade_internal_id
                # se tiver timezone, usa, se nao tiver, usa o anterior
                timezone_aux = local_embarque.cidade.timezone
                if timezone_aux:
                    timezone = timezone_aux
                elif n == 0:
                    timezone = "America/Sao_Paulo"
                else:
                    pass  # usa o mesmo timezone do checkpoint anterior

                try:
                    name, uf = local_embarque.cidade.name.rsplit(" - ", 1)
                except ValueError:
                    name = local_embarque.cidade.name
                    uf = checkpoint.local.uf
                local = LocalidadeItinerario(
                    name=name,
                    uf=uf,
                    nickname=local_embarque.nickname,
                    id_external=local_embarque.id_external,
                    rodoviaria_local_id=local_embarque_rodoviaria_id,
                )

            if checkpoint == checkpoints[0]:
                next_departure = checkpoint.datetime_ida
                next_arrival = None
            else:
                next_arrival = next_departure + timedelta(seconds=checkpoint.duracao)

            if checkpoint == checkpoints[-1]:
                next_departure = None
            else:
                next_departure += timedelta(seconds=checkpoint.duracao + checkpoint.tempo_embarque)

            obj = cls(
                local_id=local_embarque_internal_id,
                cidade_id=cidade_internal_id,
                duracao=checkpoint.duracao,
                distancia_km=checkpoint.distancia,
                tempo_embarque=checkpoint.tempo_embarque,
                departure=to_tz(next_departure, timezone),
                arrival=to_tz(next_arrival, timezone),
                local=local,
            )

            result.append(obj)
        return result


class ServicoForm(BaseModel):
    """
    Form de retorno quando um serviço encontrado (ou não) na API do parceiro.
    """

    external_id: str
    preco: Decimal
    external_datetime_ida: datetime
    external_datetime_chegada: Optional[datetime] = None
    vagas: int
    classe: Optional[str] = None
    capacidade_classe: Optional[int] = None
    distancia: Optional[Decimal] = None
    classe_reduzida: Optional[str] = None
    key: Optional[str] = None
    external_company_id: Optional[str] = None
    provider_data: Optional[dict] = None
    tipo_veiculo: Optional[int] = None
    desconto: Optional[Decimal] = None
    id_desconto: Optional[int] = None
    linha: Optional[str] = ""
    duracao: Optional[int] = None
    veiculo_andar: Optional[int] = None
    veiculo_id: Optional[int] = None
    rota_external_id: Optional[int] = None
    has_connection: Optional[bool] = False

    @validator("preco")
    def round_preco(cls, v):
        return round(v, 2)


class BuscarServicoForm(BaseModel):
    found: bool = False
    servicos: list[ServicoForm] = None


class RetornoConsultarBilheteForm(BaseModel):
    """
    Padroniza as informações da consulta de bilhete nas API dos parceiros
    """

    integracao: str
    numero_passagem: str | None
    sale_id: int | None
    reserva_id: int | None
    localizador: str | None
    numero_bilhete: str | None
    pedido_external_id: str | None
    status: str | None
    numero_assento: int | None
    primeiro_nome_pax: str | None
    ultimo_nome_pax: str | None
    tipo_documento: str | None
    numero_documento: str | None
    birth: str | None
    bpe_id: str | None
    bpe_public_url: str | None
    data_partida: str | None
    data_chegada: str | None
    origem: str | None
    estado_origem: str | None
    volta_id: str | None
    destino: str | None
    estado_destino: str | None
    duracao: str | None
    empresa_name: str | None
    valor_passagem: str | None
    taxa_de_cancelamento: str | None
    taxa_embarque: str | None
    paradas: str | None

    @validator("status")
    def validate_status(cls, v):
        return STATUS_PASSAGEM_MAP.get(v.lower(), v)


class RetornoItinerario(BaseModel):
    raw: dict | list
    cleaned: dict | list
    parsed: BaseModel


class TrechoVendidoAPI(BaseModel):
    origem_id: int
    destino_id: int
    capacidade: int
    classe: str
    vagas: int = None

    @property
    def preco(self) -> Decimal | None:
        return None

from functools import cached_property

from pydantic import parse_obj_as


class ResponseWrapper:
    def __init__(self, request, response):
        self.request = request
        self.response = response

    @cached_property
    def raw(self):
        return self.response.json()

    @cached_property
    def cleaned(self):
        return self.request.clean_response(self.raw)

    @cached_property
    def parsed(self):
        return self.request.parse_response(self.cleaned)


class ResponseWrapperConfig:
    """Classe temporaria (eu espero), pra manter o mesmo padrão de cleaned e parsed com o BaseRequestConfig"""

    def __init__(self, json, output_model, clean_param=None):
        self.output_model = output_model
        self.json = json
        self.clean_param = clean_param

    @cached_property
    def raw(self):
        return self.json

    @cached_property
    def cleaned(self):
        if self.clean_param:
            return self.json[self.clean_param]
        return self.json

    @cached_property
    def parsed(self):
        return parse_obj_as(self.output_model, self.cleaned)

from rodoviaria.service.exceptions import APIError, RodoviariaException


class TiSistemasAPIError(APIError):
    """
    Generic TiSistemasAPI exceptions
    """

    pass


class TiSistemasEfetuarCompraException(RodoviariaException):
    message = "Erro ao efetuar a compra"


class TiSistemasBloquearPoltronaException(RodoviariaException):
    message = "Erro ao bloquear poltrona"


class TiSistemasSolicitacaoReembolsoException(RodoviariaException):
    message = "Não foi possível efetuar confirmação. Solicitação de reembolso pendente."


class TiSistemasCancelarCompraException(RodoviariaException):
    message = "Erro ao cancelar a compra"


class TiSistemasBlankReponseException(RodoviariaException):
    message = "status_code=400, response=None retry automatico"

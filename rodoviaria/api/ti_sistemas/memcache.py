import logging

from commons.memcache import getmc_pymemcache

logger = logging.getLogger("rodoviaria")


class TiSistemasMC:
    prefix = "TISISTEMAS"
    default_timeout = 1200
    trecho_cache_prefix = "TRECHO"
    poltrona_cache_prefix = "POLTRONA"
    poltrona_indisponivel_cache_prefix = "POLTRONA_INDISPONIVEL"

    @property
    def mc(self):
        return getmc_pymemcache()

    def _poltrona_cache_key(self, trecho_classe_id, poltrona):
        return f"{self.prefix}_{self.poltrona_cache_prefix}_{trecho_classe_id}_{poltrona}"

    def set_poltrona_cache_key(self, trecho_classe_id, poltrona, cache_value):
        return self.mc.add(
            key=self._poltrona_cache_key(trecho_classe_id, poltrona),
            value=cache_value,
            timeout=self.default_timeout,
        )

    def get_poltrona_cache_key(self, trecho_classe_id, poltrona):
        return self.mc.get(key=self._poltrona_cache_key(trecho_classe_id, poltrona))

    def delete_poltrona_cache_key(self, trecho_classe_id, poltrona):
        return self.mc.delete(key=self._poltrona_cache_key(trecho_classe_id, poltrona))

    def _poltrona_indisponivel_cache_key(self, trecho_classe_id):
        return f"{self.prefix}_{self.trecho_cache_prefix}_{trecho_classe_id}_{self.poltrona_indisponivel_cache_prefix}"

    def insert_poltrona_indisponivel_cache(self, trecho_classe_id, poltrona_indisponivel):
        key = self._poltrona_indisponivel_cache_key(trecho_classe_id)
        cache_value = self.get_poltrona_indisponivel_cache(trecho_classe_id)
        cache_value.add(poltrona_indisponivel)
        return self.mc.set(
            key=key,
            value=cache_value,
            timeout=self.default_timeout,
        )

    def set_poltrona_indisponivel_cache(self, trecho_classe_id, cache_value):
        key = self._poltrona_indisponivel_cache_key(trecho_classe_id)
        return self.mc.set(
            key=key,
            value=cache_value,
            timeout=self.default_timeout,
        )

    def get_poltrona_indisponivel_cache(self, trecho_classe_id):
        key = self._poltrona_indisponivel_cache_key(trecho_classe_id)
        cache_value = self.mc.get(key=key)
        cache_value = cache_value if cache_value else set()
        return cache_value

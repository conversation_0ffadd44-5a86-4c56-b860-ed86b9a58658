import logging
from datetime import date, timed<PERSON>ta
from decimal import Decimal as D

from beeline import traced
from celery import chain, group
from celery.app import shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.executors.middlewares.token_bucket_middleware import token_bucket_middleware
from rodoviaria.api.ti_sistemas import endpoints
from rodoviaria.api.ti_sistemas.exceptions import TiSistemasAPIError
from rodoviaria.models.core import (
    Company,
)
from rodoviaria.models.ti_sistemas import TiSistemasLogin
from rodoviaria.service.atualiza_operacao_utils import (
    atualizar_ou_criar_rota,
    atualizar_ou_criar_rotina,
    atualizar_ou_criar_trechos_vendidos,
    finisher_descobrir_operacao,
    finisher_descobrir_operacao_on_error,
    get_chunked_range_buscas,
    inativa_rotas_e_rotinas_empresa,
    log_descobrir_operacao_buscar_rota_servico,
)
from rodoviaria.service.exceptions import RodoviariaConnectionError

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")

DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTAS
DEFAULT_RATE_LIMIT = DefaultRateLimits.Eulabs.ROTAS
MAX_PRECO_TRECHO = D("2500")


DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE


@traced("ti_sistemas.descobrir_operacao.descobrir_operacao")
def descobrir_operacao(
    client: TiSistemasLogin,
    next_days=7,
    shift_days=2,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    company = client.company
    datetime_inicial = timezone.now() + timedelta(days=shift_days)
    datetime_final = datetime_inicial + timedelta(days=next_days)

    parsed_servicos = _buscar_viagens_api(client, company, datetime_inicial.date(), datetime_final.date())

    # Inativa toda a operação da empresa antes da execução e
    # durante a execução as rotas e rotinas serão ativadas novamente.
    # Com isso não precisamos de um callback para inativar a operação antiga
    inativa_rotas_e_rotinas_empresa(company.id, datetime_inicial, datetime_final)

    task = _buscar_e_salvar_rotas_dos_servicos(
        parsed_servicos,
        company,
        queue_name,
        return_task_object,
    )
    return task


def _buscar_viagens_api(client: TiSistemasLogin, company: Company, data_inicial: date, data_final: date):
    buscas = get_chunked_range_buscas(data_inicial, data_final)
    viagens = []
    with token_bucket_middleware.suppress_not_enough_tokens_error():
        for data_inicio, data_fim in buscas:
            parsed_servicos = endpoints.buscar_lista_viagens_request(
                login=client,
                company_external_id=company.company_external_id,
                data_inicio=data_inicio,
                data_fim=data_fim,
            )

            viagens += parsed_servicos

    msg = f"{len(viagens)} possíveis rotas encontradas para empresa rodoviaria_id = {company.id}"
    logger.info(msg)
    return viagens


@traced("ti_sistemas.descobrir_operacao._buscar_e_salvar_rotas_dos_servicos")
def _buscar_e_salvar_rotas_dos_servicos(
    servicos,
    company,
    queue_name,
    return_task_object=False,
):
    tasks = [_buscar_rota_servico.s(company.id, servico.id_viagem).set(queue=queue_name) for servico in servicos]
    _group = group(*tasks)
    chain_task = chain(_group, finisher_descobrir_operacao.si(company.id)).on_error(
        finisher_descobrir_operacao_on_error.s(company.id)
    )
    if return_task_object:
        return chain_task
    chain_task()
    return chain_task


@shared_task(rate_limit=DEFAULT_RATE_LIMIT)
@retry(
    exceptions_type=(
        RodoviariaConnectionError,
        MyCircuitBreakerError,
        TiSistemasAPIError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
@traced("ti_sistemas.descobrir_operacao._buscar_rota_servico")
def _buscar_rota_servico(company_id, id_viagem):
    from rodoviaria.api.ti_sistemas.api import TiSistemasAPI

    company = Company.objects.select_related("integracao").get(id=company_id)
    api = TiSistemasAPI(company)
    itinerario = api.itinerario(id_viagem)
    trechos_vendidos = api.buscar_trechos_vendidos(id_viagem)
    rota = atualizar_ou_criar_rota(company.id, itinerario, id_viagem, create_locais=True)
    datetime_ida_naive = itinerario.parsed[0].datetime_ida
    rotina = atualizar_ou_criar_rotina(rota, datetime_ida_naive, id_viagem, ativo=bool(trechos_vendidos))
    if not trechos_vendidos:
        log_descobrir_operacao_buscar_rota_servico(
            "TI_SISTEMAS",
            id_viagem,
            0,
            rotina.id,
            rotina.ativo,
            rota.id,
            company.id,
            datetime_ida_naive,
        )
        return
    atualizar_ou_criar_trechos_vendidos(company, rotina, trechos_vendidos, itinerario.parsed)
    log_descobrir_operacao_buscar_rota_servico(
        "TI_SISTEMAS",
        id_viagem,
        len(trechos_vendidos),
        rotina.id,
        rotina.ativo,
        rota.id,
        company.id,
        datetime_ida_naive,
    )

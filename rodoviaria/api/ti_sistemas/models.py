import hashlib
import json
from datetime import date, datetime, timedelta
from decimal import Decimal

from pydantic import BaseModel, Field, root_validator, validator

from rodoviaria.api.forms import Localidade, TrechoVendidoAPI


class DateEncoderModel(BaseModel):
    def dict(self, **kwargs):
        return json.loads(self.json(**kwargs))

    class Config:
        json_encoders = {
            date: lambda v: v.strftime("%d-%m-%Y"),
        }


class Local(Localidade):
    @root_validator(pre=True)
    def translate(cls, local):
        descricao_split = local["name"].split(" - ")
        nome_cidade = descricao_split[0]
        complemento = None
        if len(descricao_split) > 1:
            complemento = descricao_split[1]
        return {
            "nome_cidade": nome_cidade,
            "external_local_id": local["id"],
            "uf": local["uf"],
            "complemento": complemento,
            "external_cidade_id": local["id"],
        }


class Viagem(BaseModel):
    id: int
    id_viagem: int = Field(alias="idViagem")
    datetime_ida: datetime = Field(alias="dataHoraPartida")


class Parada(BaseModel):
    id: int
    local: Localidade
    datetime_ida: datetime = Field(alias="dataHoraPartida")
    distancia_total: Decimal = Field(alias="totalKm")
    distancia: Decimal | None
    duracao: int | None = Field(alias="duracao_2")
    tempo_embarque: int | None
    tz: int

    @root_validator(pre=True)
    def validate_all(cls, parada):
        parada["local"] = {
            "nome_cidade": parada["localNome"],
            "external_local_id": parada["localId"],
            "external_cidade_id": parada["localId"],
            "uf": parada["localUf"],
        }
        return parada


class Itinerario(BaseModel):
    __root__: list[Parada]

    @property
    def _sigla_duracao(self):
        return "-".join(f"{p.local.external_local_id}.{p.duracao}" for p in self.__root__)

    @property
    def hash(self):
        inicio = self[0].local.external_local_id
        fim = self[-1].local.external_local_id
        digest = hashlib.md5(self._sigla_duracao.encode()).hexdigest()  # noqa: S324
        return f"{inicio}{fim}{digest}"

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    @validator("__root__")
    def _attach_tempos(cls, v):
        if not v:
            return v

        iti_first = v[0]
        iti_first.duracao = 0
        iti_first.tempo_embarque = 0
        iti_first.distancia = 0

        for iti_anterior, iti_atual in zip(v, v[1:]):
            # TODO: garantir que o datetime_ida é o horario local. A duracao no rodoviaria precisa estar "errado"
            duracao = iti_atual.datetime_ida - iti_anterior.datetime_ida

            if iti_atual == v[-1]:
                # Não tem tempo de embarque no último checkpoint.
                tempo_embarque = timedelta(minutes=0)
            elif duracao <= timedelta(minutes=10):
                # Tempo de embarque em paradas rápidas é o minimo de 1 min.
                tempo_embarque = timedelta(minutes=1)
            else:
                tempo_embarque = timedelta(minutes=10)

            iti_atual.duracao = (duracao - tempo_embarque).total_seconds()
            iti_atual.tempo_embarque = tempo_embarque.total_seconds()
            iti_atual.distancia = iti_atual.distancia_total - iti_anterior.distancia_total

        return v


class Corrida(BaseModel):
    external_id: int = Field(alias="id")
    origem_external_id: int = Field(alias="origin")
    destino_external_id: int = Field(alias="destination")
    datetime_ida: datetime = Field(alias="departure")
    datetime_chegada: datetime = Field(alias="arrival")
    classe: str = Field(alias="service")
    company_external_id: int = Field(alias="busCompany")
    nome_empresa: str = Field(alias="busCompanyName")
    vagas: int = Field(alias="freeSeats")
    preco: Decimal = Field(alias="price")
    preco_pedagio: Decimal = Field(alias="toll")
    tipo_onibus: str = Field(alias="busType")
    mensagem: str = Field(alias="message")


class BuscarCorridasInput(DateEncoderModel):
    origem: str = Field(alias="origin")
    destino: str = Field(alias="destination")
    data: date = Field(alias="date")

    class Config:
        allow_population_by_field_name = True


class PrecoInfo(BaseModel):
    preco_base: Decimal = Field(alias="basePrice")
    preco_seguro: Decimal = Field(alias="insurancePrice")
    imposto: int = Field(alias="taxPrice")
    outros_custos: Decimal = Field(alias="otherPrice")
    preco_pedagio: Decimal = Field(alias="tollPrice")
    taxa_embarque: Decimal = Field(alias="boardingPrice")
    comissao: int = Field(alias="comission")
    preco: Decimal = Field(alias="price")


class Poltrona(BaseModel):
    status: str
    x: int
    y: int
    z: int | None
    numero: str = Field(alias="number")
    descricao: str | None = Field(alias="description")


class Deck(BaseModel):
    numero: int = Field(alias="number")
    poltronas: list[Poltrona] = Field(alias="seats")


class LayoutOnibus(BaseModel):
    decks: list[Deck]

    @property
    def poltronas_map(self):
        return {
            str(p.numero).zfill(2): "ocupada" if p.status == "OCCUPIED" else "livre"
            for deck in self.decks
            for p in deck.poltronas
        }


class LayoutViagem(BaseModel):
    detalhes_viagem: Corrida = Field(alias="travel")
    preco_info: PrecoInfo = Field(alias="priceInfo")
    layout_onibus: LayoutOnibus = Field(alias="busLayout")

    class Config:
        allow_population_by_field_name = True


class MapaViagemInput(DateEncoderModel):
    date_ida: date = Field(alias="date")
    origem_external_id: int = Field(alias="origin")
    destino_external_id: int = Field(alias="destination")
    company_external_id: int = Field(alias="busCompany")
    viagem_id: int = Field(alias="id")

    class Config:
        allow_population_by_field_name = True


class Seat(BaseModel):
    seat_id: int = Field(alias="seatId")
    voucher: int | None = None
    seat: int
    status: str

    class Config:
        allow_population_by_field_name = True


class BasePoltronaOutput(BaseModel):
    order_id: str = Field(alias="orderId")
    seats: list[Seat]

    class Config:
        allow_population_by_field_name = True


class BloquearPoltronaOutput(BasePoltronaOutput): ...


class ConsultarReservaOutput(BasePoltronaOutput): ...


class EfetuarCompraOutput(BasePoltronaOutput): ...


class BloquearPoltronaInput(MapaViagemInput):
    order_id: int = Field(alias="orderId")
    seats: list[int]

    class Config:
        allow_population_by_field_name = True


class Comprador(BaseModel):
    email: str | None = None
    nome: str = Field(alias="name")
    cpf: str
    telefone: str | None = Field(default=None, alias="phone")

    class Config:
        allow_population_by_field_name = True


class EfetuarCompraInput(BaseModel):
    seat_id: int = Field(alias="seatId")
    order_id: int = Field(alias="orderId")
    nome: str = Field(alias="name")
    documento: str = Field(alias="document")
    comprador: Comprador = Field(alias="buierInfo")

    class Config:
        allow_population_by_field_name = True


class BaseReservaInput(BaseModel):
    seat_id: int = Field(alias="seatId")
    order_id: int = Field(alias="orderId")

    class Config:
        allow_population_by_field_name = True


class ConsultarReservaInput(BaseReservaInput): ...


class DesbloquearPoltronaInput(BaseReservaInput): ...


class CancelarCompraInput(BaseReservaInput): ...


class BuscarTrechosVendidosOutput(TrechoVendidoAPI):
    external_id: int = Field(alias="id")
    origem_id: int = Field(alias="origemId")
    destino_id: int = Field(alias="destinoId")
    vagas: int
    classe: str
    capacidade: int
    preco_api: Decimal = Field(alias="preco")

    @property
    def preco(self):
        return self.preco_api

import logging
from datetime import date
from http import H<PERSON><PERSON>ethod, HTTPStatus

from pydantic import parse_obj_as
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from commons.utils import str_contains
from marketplace.otas.exceptions import RodoviariaConnectionError
from rodoviaria.api.executors import Response
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.executors.middlewares import token_bucket_middleware
from rodoviaria.api.ti_sistemas import models
from rodoviaria.api.ti_sistemas.base import BaseRequestConfig
from rodoviaria.api.ti_sistemas.exceptions import (
    TiSistemasBlankReponseException,
    TiSistemasBloquearPoltronaException,
    TiSistemasCancelarCompraException,
    TiSistemasEfetuarCompraException,
    TiSistemasSolicitacaoReembolsoException,
)
from rodoviaria.models.ti_sistemas import TiSistemasLogin
from rodoviaria.service.exceptions import PassengerTicketAlreadyPrintedException, PoltronaJaSelecionadaException

logger = logging.getLogger("rodoviaria")


class BuscarAgenciasConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("rest/bs/agencies")


class BuscarItinerarioConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    def __init__(self, login: TiSistemasLogin, company_external_id: int, id_viagem: int):
        self.company_external_id = company_external_id
        self.id_viagem = id_viagem
        super().__init__(login)

    @property
    def url(
        self,
    ):
        return self.endpoint(f"rest/bs/itineraries/{self.company_external_id}/{self.id_viagem}")


class BuscarListaViagensConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    def __init__(self, login: TiSistemasLogin, company_external_id: int, data_inicio: date, data_fim: date):
        self.company_external_id = company_external_id
        self.data_inicio = data_inicio.strftime("%Y-%m-%d")
        self.data_fim = data_fim.strftime("%Y-%m-%d")
        super().__init__(login)

    @property
    def url(
        self,
    ):
        return self.endpoint(f"rest/bs/travels-info/{self.company_external_id}/{self.data_inicio}/{self.data_fim}")


@retry(
    retry=retry_if_exception_type((TiSistemasBlankReponseException, RodoviariaConnectionError)),
    reraise=True,
    stop=stop_after_attempt(2),
)
def buscar_lista_viagens_request(login: TiSistemasLogin, company_external_id: int, data_inicio: date, data_fim: date):
    executor = get_http_executor()
    request_config = BuscarListaViagensConfig(login, company_external_id, data_inicio, data_fim)
    response = request_config.invoke(
        executor,
    )

    return parse_obj_as(list[models.Viagem], response.json()["list"])


class MapaViagemConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("rest/bs/bus")


class BuscarCorridasConfig(BaseRequestConfig):
    method = HTTPMethod.GET
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("rest/bs/travels")


class BloquearPoltronaConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("rest/bs/bookings")

    def process_response(self, response: Response):
        if response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR:
            payload = response.json()
            message = payload["message"]
            if str_contains(message, "já está ocupado"):
                raise PoltronaJaSelecionadaException(message)
            raise TiSistemasBloquearPoltronaException(message)
        return super().process_response(response)


class DesbloquearPoltronaConfig(BaseRequestConfig):
    method = HTTPMethod.DELETE
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("rest/bs/bookings")


class EfetuarCompraConfig(BaseRequestConfig):
    method = HTTPMethod.PUT
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("rest/bs/bookings")

    def process_response(self, response: Response):
        if response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR:
            payload = response.json()
            message = payload["message"]
            if str_contains(message, "Solicitação de reembolso"):
                raise TiSistemasSolicitacaoReembolsoException(message)
            raise TiSistemasEfetuarCompraException(message)
        return super().process_response(response)


class ConsultarReservaConfig(BaseRequestConfig):
    method = HTTPMethod.GET
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("rest/bs/bookings")


class CancelarCompraConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("rest/bs/refunds")

    def process_response(self, response: Response):
        if response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR:
            payload = response.json()
            message = payload["message"]
            if str_contains(message, "O bilhete desse voucher já foi impresso"):
                raise PassengerTicketAlreadyPrintedException(message)
            if str_contains(message, "O tempo limite para solicitar reembolso"):
                raise PassengerTicketAlreadyPrintedException(message)
            if str_contains(message, "Solicitação de reembolso"):
                raise TiSistemasSolicitacaoReembolsoException(message)
            raise TiSistemasCancelarCompraException(message)
        return super().process_response(response)


class BuscarTrechosVendidosConfig(BaseRequestConfig):
    method = HTTPMethod.GET
    disable_middlewares = {
        token_bucket_middleware,
    }

    def __init__(self, login: TiSistemasLogin, company_external_id: int, id_viagem: int):
        self.company_external_id = company_external_id
        self.id_viagem = id_viagem
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"rest/bs/segments/{self.company_external_id}/{self.id_viagem}")

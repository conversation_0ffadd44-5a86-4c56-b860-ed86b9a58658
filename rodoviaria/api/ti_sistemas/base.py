import logging
from http import HTTPStatus

from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pt<PERSON>, Retry

from rodoviaria.api.auth import BearerCallableAuth
from rodoviaria.api.executors import Request, RequestConfig, Response
from rodoviaria.models.ti_sistemas import TiSistemasLogin
from rodoviaria.service.exceptions import (
    RodoviariaException,
    RodoviariaTimeoutException,
    RodoviariaTooManyRequestsError,
    RodoviariaUnauthorizedError,
)

from .exceptions import TiSistemasBlankReponseException

buserlogger = logging.getLogger("rodoviaria")


class BaseRequestConfig(RequestConfig):
    # Prefix for logging and error messages
    MESSAGE_PREFIX = "tisistemas"

    # See rodoviaria/api/executors/middlewares.py:LoggingMiddleware
    integration_name = "TiSistemasAPI"

    timeout = 60

    http_adapter = HTTPAdapter(
        max_retries=Retry(
            total=3,
            backoff_factor=1,
            raise_on_status=False,
            status_forcelist=[401, 406, 408, 413, 429, 503, 504],
        ),
    )

    def __init__(self, login: TiSistemasLogin):
        self.login = login
        self.auth = BearerCallableAuth(api_key=self.login.auth_key, header_name="I-Auth")

    def endpoint(self, endpoint: str) -> str:
        return f"{self.login.company.url_base}/{endpoint}"

    def preprocess_request(self, request: Request) -> Request:
        self.auth(request)
        return request

    def process_response(self, response: Response):
        if response.ok:
            return response

        generic_error_message = (
            f"{self.MESSAGE_PREFIX} '{response.request.method} {response.request.url}' "
            f"json={response.request.json} params={response.request.params} "
            f"status_code={response.status_code}"
        )

        try:
            payload = response.json()
            error_message = payload.get("message", generic_error_message) if payload else None
        except ValueError:
            error_message = generic_error_message

        status_code = response.status_code

        if response.status_code == HTTPStatus.BAD_REQUEST and payload is None:
            raise TiSistemasBlankReponseException
        elif status_code == HTTPStatus.UNAUTHORIZED or status_code == HTTPStatus.FORBIDDEN:
            raise RodoviariaUnauthorizedError(error_message)  # RodoviariaBaseException
        elif status_code == HTTPStatus.REQUEST_TIMEOUT or (
            status_code == HTTPStatus.BAD_REQUEST and "408 Request Time-out" in error_message
        ):
            raise RodoviariaTimeoutException(generic_error_message)  # RodoviariaBaseException
        elif status_code == HTTPStatus.TOO_MANY_REQUESTS:
            raise RodoviariaTooManyRequestsError(generic_error_message)  # RodoviariaBaseException

        raise RodoviariaException(error_message)

from datetime import timed<PERSON><PERSON>
from json import JSO<PERSON>ecode<PERSON><PERSON><PERSON>
from typing import Any

import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter, Retry
from requests.exceptions import ChunkedEncodingError, ConnectionError, ReadTimeout

from rodoviaria.api.executors import Executor, Request, RequestConfig, Response
from rodoviaria.service.exceptions import RodoviariaConnectionError


class RequestsResponse(Response):
    def __init__(self, request: Request, response: requests.Response):
        super().__init__(request)
        self._inner = response

    def json(self) -> Any:
        try:
            response_json = self._inner.json()
        except (ValueError, JSONDecodeError):
            response_json = None
        return response_json

    @property
    def status_code(self) -> int:
        return self._inner.status_code

    @property
    def elapsed(self) -> timedelta:
        return self._inner.elapsed


class RequestsExecutor(Executor):
    LOGGER_NAME = f"{__name__}.RequestsExecutor"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._log("Creating session")
        self._session = requests.Session()

        # default HTTPAdapter
        adapter = HTTPAdapter(
            max_retries=Retry(
                total=3,
                backoff_factor=1,
                raise_on_status=False,
            )
        )
        self._session.mount("https://", adapter)
        self._session.mount("http://", adapter)

    def send(self, request: RequestConfig, params=None, json=None, timeout: float | None = None) -> Response:
        # Monta o http_adapter especifico dessa RequestConfig caso exista e não tenha sido registrado ainda
        if request.http_adapter is not None:
            if request.url not in self._session.adapters:
                self._session.mount(request.url, request.http_adapter)

        middlewares = self._get_middlewares(request)

        req = Request(
            config=request,
            method=request.method,
            url=request.url,
            headers=request.headers,
            params=params,
            json=json,
            timeout=timeout or request.timeout,
        )
        self._log("Request sent %s", req.url, level="debug")

        try:
            for middleware in middlewares:
                req = middleware.preprocess_request(req)

            req = request.preprocess_request(req)

            response = self._session.request(
                method=req.method,
                url=req.url,
                params=req.params,
                json=req.json,
                timeout=req.timeout,
                headers=req.headers,
            )

            response = RequestsResponse(req, response)

            for middleware in middlewares:
                response = middleware.process_response(response)

            self._log("Response received %s %s", req.url, response.status_code, level="debug")
            return request.process_response(response)
        except (ConnectionError, ReadTimeout, ChunkedEncodingError) as exc:
            log_msg = f"'{req.method} {req.url}' data={req.json} connection error"
            self._log(message=log_msg, level="warning", exc_info=True)
            raise RodoviariaConnectionError(f"{request.MESSAGE_PREFIX} {req.url} connection error") from exc
        except Exception as exc:
            self._log(message="An exception occurred", level="warning", exc_info=True)

            for middleware in middlewares:
                middleware.on_exception(req, exc)

            raise

import abc
import logging
from datetime import timed<PERSON><PERSON>
from http import HTT<PERSON><PERSON>od, HTTPStatus
from typing import Any

from rodoviaria.api.executors.middlewares import Middleware


class Response:
    def __init__(self, request: "Request"):
        self.request = request

    @property
    @abc.abstractmethod
    def status_code(self) -> HTTPStatus: ...

    @property
    @abc.abstractmethod
    def elapsed(self) -> timedelta: ...

    @abc.abstractmethod
    def json(self) -> Any: ...

    @abc.abstractmethod
    def text(self) -> str: ...

    @property
    def ok(self):
        return self.status_code < 400

    def __repr__(self):
        return f"<Response: status_code={self.status_code} request={self.request}>"


class Request:
    def __init__(
        self,
        config: "RequestConfig",
        method: str,
        url: str,
        headers=None,
        params=None,
        json=None,
        timeout=None,
    ):
        self.method = method
        self.url = url
        self.headers = {} if headers is None else headers
        self.params = params
        self.json = json
        self.timeout = timeout
        #: Original RequestConfig object used to create this instance
        #: Do not change the values of `config` instance! Please :pray:
        self.config = config

    def __repr__(self):
        return f"<Request: method={self.method} url={self.url}>"


class RequestConfig:
    method: HTTPMethod = HTTPMethod.GET
    timeout: float | None = None
    headers: dict | None = None

    # requests.HTTPAdapter TODO: ter uma configuração genérica que funcione para todos Executor's
    http_adapter: Any | None = None

    disable_middlewares: set[Middleware] = set()

    MESSAGE_PREFIX = "<none>"

    @property
    def url(self) -> str: ...

    def preprocess_request(self, request: Request) -> Request:
        return request

    def process_response(self, response: Response) -> Response:
        return response

    def invoke(self, executor: "Executor", params=None, json=None, timeout: float | None = None) -> Response:
        return executor.send(self, params=params, json=json, timeout=timeout)

    async def ainvoke(
        self, executor: "AsyncExecutor", params=None, json=None, timeout: float | None = None
    ) -> Response:
        return await executor.asend(self, params=params, json=json, timeout=timeout)


class Executor:
    LOGGER_NAME = f"{__name__}.Executor"

    def __init__(self):
        self._logger = logging.getLogger(self.LOGGER_NAME)
        self.middlewares = []

    @abc.abstractmethod
    def send(self, request: RequestConfig, params=None, json=None, timeout: float | None = None) -> Response: ...

    def add_middleware(self, middleware: Middleware):
        self.middlewares.append(middleware)

    def _log(self, message, *args, level="info", **kwargs):
        log_fn = getattr(self._logger, level)
        return log_fn(f"[{self.__class__.__name__}] {message}", *args, **kwargs)

    def _get_middlewares(self, request: RequestConfig) -> list[Middleware]:
        return [m for m in self.middlewares if m not in request.disable_middlewares]


class AsyncExecutor(Executor):
    @abc.abstractmethod
    async def asend(self, request: RequestConfig, params=None, json=None, timeout: float | None = None) -> Response: ...

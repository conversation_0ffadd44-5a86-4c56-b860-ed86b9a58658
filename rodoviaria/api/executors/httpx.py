from datetime import timed<PERSON><PERSON>
from typing import Any

import httpx
from asgiref.sync import async_to_sync

from rodoviaria.api.executors import AsyncExecutor, Request, RequestConfig, Response
from rodoviaria.service.exceptions import RodoviariaConnectionError


class HttpxResponse(Response):
    def __init__(self, request: Request, response: httpx.Response):
        super().__init__(request)
        self._inner = response

    def json(self) -> Any:
        return self._inner.json()

    @property
    def status_code(self) -> int:
        return self._inner.status_code

    @property
    def elapsed(self) -> timedelta:
        return self._inner.elapsed


class HttpxExecutor(AsyncExecutor):
    LOGGER_NAME = f"{__name__}.HttpxExecutor"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._log("Creating session")
        self._client = httpx.AsyncClient()

    def send(self, request: RequestConfig, params=None, json=None, timeout: float | None = None) -> Response:
        return async_to_sync(self.asend)(request, params, json, timeout)

    async def asend(self, request: RequestConfig, params=None, json=None, timeout: float | None = None) -> Response:
        middlewares = self._get_middlewares(request)

        req = Request(
            config=request,
            method=request.method,
            url=request.url,
            headers=request.headers,
            params=params,
            json=json,
            timeout=timeout or request.timeout,
        )
        self._log("Request sent %s", req.url, level="debug")

        try:
            for middleware in middlewares:
                req = middleware.preprocess_request(req)

            req = request.preprocess_request(req)

            response = await self._client.request(
                method=req.method,
                url=req.url,
                params=req.params,
                json=req.json,
                timeout=req.timeout,
                headers=req.headers,
            )

            response = HttpxResponse(req, response)

            for middleware in middlewares:
                response = middleware.process_response(response)

            self._log("Response received %s %s", req.url, response.status_code, level="debug")
            return request.process_response(response)
        except httpx.TransportError as exc:
            log_msg = f"'{req.method} {req.url}' data={req.json} connection error"
            self._log(message=log_msg, level="warning", exc_info=True)
            raise RodoviariaConnectionError(f"{request.MESSAGE_PREFIX} {req.url} connection error") from exc
        except Exception as exc:
            self._log(message="An exception occurred", level="warning", exc_info=True)

            for middleware in middlewares:
                middleware.on_exception(req, exc)

            raise

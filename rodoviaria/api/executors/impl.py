from contextvars import ContextV<PERSON>

from rodoviaria.api.executors import <PERSON><PERSON><PERSON>xe<PERSON>or, Executor
from rodoviaria.api.executors.httpx import HttpxExecutor
from rodoviaria.api.executors.middlewares.honeycomb_middleware import HoneycombMiddleware
from rodoviaria.api.executors.middlewares.logging_middleware import LoggingMiddleware
from rodoviaria.api.executors.middlewares.prometheus_metrics_middleware import PrometheusMetricsMiddleware
from rodoviaria.api.executors.middlewares.token_bucket_middleware import token_bucket_middleware
from rodoviaria.api.executors.requests import RequestsExecutor

_current_executor: ContextVar[Executor | None] = ContextVar("http_executor", default=None)


def get_http_executor():
    """Returns a context-local `Executor`."""

    executor = _current_executor.get()
    if executor is not None:
        return executor

    executor = RequestsExecutor()
    for middleware in _get_middlewares():
        executor.add_middleware(middleware)
    _current_executor.set(executor)
    return executor


_current_async_executor: ContextVar[AsyncExecutor | None] = ContextVar("http_async_executor", default=None)


def get_async_http_executor() -> AsyncExecutor:
    """Returns a context-local `AsyncExecutor`."""

    executor = _current_async_executor.get()
    if executor is not None:
        return executor

    executor = HttpxExecutor()
    for middleware in _get_middlewares():
        executor.add_middleware(middleware)
    _current_async_executor.set(executor)
    return executor


def _get_middlewares():
    return [
        token_bucket_middleware,
        HoneycombMiddleware(),
        LoggingMiddleware(),
        PrometheusMetricsMiddleware(),
    ]

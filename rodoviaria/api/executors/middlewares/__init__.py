from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from rodoviaria.api.executors import Request, Response


class Middleware:
    def preprocess_request(self, request: Request) -> Request:
        return request

    def process_response(self, response: Response) -> Response:
        return response

    def on_exception(self, request: Request, exception: Exception): ...

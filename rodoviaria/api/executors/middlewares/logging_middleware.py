from __future__ import annotations

from bp.logging import log_integration_request, log_integration_request_exception
from rodoviaria.api.executors import Request, Response
from rodoviaria.api.executors.middlewares import Middleware


class LoggingMiddleware(Middleware):
    def process_response(self, response: Response) -> Response:
        login = getattr(response.request.config, "login", None)
        log_integration_request(
            integration=getattr(response.request.config, "integration_name", None),
            company_id=login.company.id if login is not None else None,
            url=response.request.url,
            method=response.request.method,
            payload=response.request.json,
            params=response.request.params,
            response=response,
            error_message="",  # TODO
        )
        return super().process_response(response)

    def on_exception(self, request: Request, exception: Exception):
        login = getattr(request.config, "login", None)
        log_integration_request_exception(
            integration=getattr(request.config, "integration_name", None),
            company_id=login.company.id if login is not None else None,
            url=request.url,
            method=request.method,
            payload=request.json,
            error_message=str(exception),
        )
        return super().on_exception(request, exception)

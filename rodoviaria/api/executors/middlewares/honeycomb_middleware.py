from __future__ import annotations

from beeline import beeline

from rodoviaria.api.executors import Request, Response
from rodoviaria.api.executors.middlewares import Middleware


class HoneycombMiddleware(Middleware):
    _span = None

    def preprocess_request(self, request: Request) -> Request:
        beeline.add_rollup_field("http_requests_count", 1)
        self._span = beeline.start_span(
            {
                "name": "http_request",
                "url": request.url,
                "method": request.method,
            }
        )
        return super().preprocess_request(request)

    def process_response(self, response: Response) -> Response:
        if self._span:
            self._span.add_context(
                {
                    "status": response.status_code,
                    "elapsed": response.elapsed,
                }
            )
            beeline.finish_span(self._span)
        return super().process_response(response)

    def on_exception(self, request: Request, exception: Exception):
        beeline.add_rollup_field("http_requests_error_count", 1)
        if self._span:
            self._span.add_context(
                {
                    "error": str(exception),
                }
            )
            beeline.finish_span(self._span)
        return super().on_exception(request, exception)

from prometheus_client import Counter, Summary

from rodoviaria.api.executors import Middleware, Response

_labels = ["method", "req_class", "ota", "status", "company_id"]
_requests_counter = Counter("rodoviaria_http_requests", "Request counter.", _labels)
_requests_elapsed_time = Summary("rodoviaria_http_requests_time", "Request elapsed time.", _labels)


class PrometheusMetricsMiddleware(Middleware):
    def process_response(self, response: Response) -> Response:
        login = getattr(response.request.config, "login", None)
        ota = getattr(response.request.config, "integration_name", None)
        method = response.request.method
        req_class = response.request.config.__class__.__name__
        company_id = login.company_id if login is not None else None
        _requests_counter.labels(
            method=method,
            req_class=req_class,
            ota=ota,
            status=response.status_code,
            company_id=company_id,
        ).inc()
        _requests_elapsed_time.labels(
            method=method,
            req_class=req_class,
            ota=ota,
            status=response.status_code,
            company_id=company_id,
        ).observe(response.elapsed.total_seconds())

        return super().process_response(response)

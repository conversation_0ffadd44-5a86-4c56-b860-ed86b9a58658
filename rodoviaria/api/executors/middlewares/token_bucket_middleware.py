import contextlib
import logging
from contextvars import ContextV<PERSON>

from simple_token_bucket import NotEnoughTokens

from commons.token_bucket import TokenBucket
from rodoviaria.api.executors import Middleware, Request

logger = logging.getLogger(__name__)


class TokenBucketMiddleware(Middleware):
    _suppress_not_enough_tokens = ContextVar("TokenBucketMiddleware.suppress_not_enough_tokens", default=False)

    @contextlib.contextmanager
    def suppress_not_enough_tokens_error(self):
        self._suppress_not_enough_tokens.set(True)
        yield
        self._suppress_not_enough_tokens.set(False)

    def preprocess_request(self, request: Request):
        # explicito é melhor do que implicito, eu sei, mas preciso manter compatibilidade
        # com os tokens buckets atuais criados através do "login" disponível nas `RequestConfig`
        login = getattr(request.config, "login", None)
        if login is not None:
            token_bucket = TokenBucket.from_client(login)
            try:
                token_bucket.try_get_token()
            except NotEnoughTokens:
                if not self._suppress_not_enough_tokens.get():
                    raise
        else:
            logger.debug("%s do not have a `login` attribute", request.config)

        return request


token_bucket_middleware = TokenBucketMiddleware()

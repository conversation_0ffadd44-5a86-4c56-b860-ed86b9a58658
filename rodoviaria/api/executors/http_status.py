from enum import IntEnum


class CloudflareHTTPStatus(IntEnum):
    """Cloudflare specific HTTP status codes"""

    def __new__(cls, value, phrase, description=""):
        obj = int.__new__(cls, value)
        obj._value_ = value

        obj.phrase = phrase
        obj.description = description
        return obj

    CLOUDFLARE_EMPTY_RESPONSE = (
        520,
        "Cloudflare Empty Response",
        "The origin server returns an empty, unknown, or unexpected response to Cloudflar<PERSON>",
    )
    CLOUDFLARE_OFFLINE = (
        521,
        "Cloudflare Offline or Blocked requests",
        "The origin web server refuses connections from Cloudflare",
    )
    CLOUDFLARE_HANDSHAKE_TIMEOUT = (522, "Cloudflare Timeout", "Cloudflare times out contacting the origin web server")
    CLOUDFLARE_UNREACHABLE = (
        523,
        "Cloudflare Origin is unreachable",
        "Cloudflare cannot contact your origin web server",
    )
    CLOUDFLARE_CONNECTION_TIMEOUT = (
        524,
        "Cloudflare Timeout",
        "<PERSON>flare successfully connected to the origin but did not provide an HTTP response",
    )
    CLOUDFLARE_SSL_FAILED = (
        525,
        "Cloudflare SSL handshake failed",
        "SSL handshake between Cloudflare and the origin web server failed",
    )
    CLOUDFLARE_SSL_INVALID = (
        526,
        "Cloudflare invalid SSL certificate",
        "Cloudflare cannot validate the SSL certificate at your origin web server",
    )

from logging import Logger
from typing import Any


class ClientWithLogger:
    def __init__(self, config: dict, logger: Logger):
        if "url_base" not in config:
            raise ValueError(f"`config` {config!r} without `url_base`")

        if not isinstance(config["url_base"], (str,)):
            raise TypeError(f'`config["url_base"]` {config["url_base"]!r} must be a str.')

        self.config = config
        self.logger = logger

    def __getattr__(self, name: str) -> Any:
        if name not in self.config:
            raise AttributeError

        return self.config[name]


def get_client_with_logger(client_config: dict, logger: Logger) -> ClientWithLogger:
    """Create a client with a logger instance attached to it.

    Dont ask me why :shrug: I'm just refactoring that to something more clear and legible.

    Args:
        client_config (dict): The client connection configuration.
        logger (logging.Logger): Logger instance.

    Returns:
        ClientWithLogger: A client with a logger attribute :shrug:.
    """

    client = ClientWithLogger(client_config, logger)
    return client

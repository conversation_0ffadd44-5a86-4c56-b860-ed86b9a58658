import json
import logging
from collections import defaultdict
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal as D
from itertools import combinations

from beeline import traced
from celery import chain, group
from celery.app import shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import midnight, to_tz
from commons.memoize import memoize_with_log
from commons.redis import lock
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.executors.middlewares.token_bucket_middleware import token_bucket_middleware
from rodoviaria.api.forms import CheckpointsForm
from rodoviaria.api.vexado import endpoints as endpoints
from rodoviaria.api.vexado import models as models_vexado
from rodoviaria.models.core import (
    Checkpoint,
    LocalEmbarque,
    Rota,
    Rotina,
    RotinaTrechoVendido,
    TaskStatus,
    Tipo<PERSON>o,
    TrechoVendido,
)
from rodoviaria.models.vexado import RotaVexado, VexadoLogin
from rodoviaria.service.atualiza_operacao_utils import finaliza_task_status
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
)
from rodoviaria.service.salva_rotas_bulk_svc import inativa_rotas_sem_rotinas

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")

DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTAS
DEFAULT_RATE_LIMIT = DefaultRateLimits.Vexado.ROTAS
LOG_PREFIX = "[descobrir_rotas_vexado_async]"
BUCKET_SIZE = int(DEFAULT_RATE_LIMIT.split("/")[0])
MAX_PRECO_TRECHO = D("2500")


@traced("vexado.descobrir_operacao.descobrir_operacao")
def descobrir_operacao(
    client,
    next_days=7,
    shift_days=2,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    with token_bucket_middleware.suppress_not_enough_tokens_error():
        rotas_api = endpoints.BuscarRotas(client).send(client.company.company_external_id)

    rotas_ativas_map = _atualiza_rotas(rotas_api, client.company.id)

    task = _buscar_e_salvar_rotas_dos_servicos(
        client,
        rotas_ativas_map,
        next_days,
        shift_days,
        queue_name,
        return_task_object,
    )
    return task


@traced("vexado.descobrir_operacao.descobrir_operacao")
def _atualiza_rotas(rotas_api, company_rodoviaria_id):
    rotas_com_paradas = [r for r in rotas_api.parsed if len(r.itinerario) > 1]
    ids_hashs = [r.itinerario.hash for r in rotas_com_paradas]
    rotas_existentes = Rota.objects.filter(company_id=company_rodoviaria_id, id_hash__in=ids_hashs)
    rotas_existentes = {r.id_hash: r for r in rotas_existentes}
    rotas_to_create = []
    rotas_to_update = []
    checkpoints_to_create = []
    rota_external_ids_map = defaultdict(list)
    for rota_api in rotas_com_paradas:
        rota = rotas_existentes.get(rota_api.itinerario.hash)
        if not rota:
            rota = Rota(
                id_hash=rota_api.itinerario.hash,
                company_id=company_rodoviaria_id,
                provider_data=json.dumps(rota_api.cleaned),
                id_external=rota_api.external_id,
            )
            rotas_to_create.append(rota)
            rotas_existentes[rota_api.itinerario.hash] = rota
            checkpoints_to_create += _make_checkpoints_to_create(rota, rota_api.itinerario)
        else:
            rota.ativo = True
            rota.id_external = rota_api.external_id
            rota.updated_at = timezone.now()
            rotas_to_update.append(rota)
        rota_external_ids_map[rota_api.itinerario.hash].append(rota_api.external_id)
    Rota.objects.bulk_create(rotas_to_create)
    Checkpoint.objects.bulk_create(checkpoints_to_create)
    Rota.objects.bulk_update(rotas_to_update, ["ativo", "id_external", "updated_at"])
    return rota_external_ids_map


def _make_checkpoints_to_create(rota, itinerario):
    rota_checkpoints = []
    cksForm = CheckpointsForm.from_itinerario(
        rota.company_id,
        itinerario,
    )
    for idx, ck in enumerate(cksForm):
        c = Checkpoint(
            rota=rota,
            idx=idx,
            local_id=ck.local.rodoviaria_local_id,
            internal_id=None,
            arrival=ck.arrival,
            departure=ck.departure,
            distancia_km=ck.distancia_km,
            duracao=timedelta(seconds=ck.duracao),
            tempo_embarque=timedelta(seconds=ck.tempo_embarque),
            id_external=ck.local.id_external,
            uf=ck.local.uf,
            name=ck.local.name,
            nickname=ck.local.nickname,
        )
        rota_checkpoints.append(c)
    return rota_checkpoints


@traced("vexado.descobrir_operacao._buscar_e_salvar_rotas_dos_servicos")
def _buscar_e_salvar_rotas_dos_servicos(
    client,
    rotas_ativas_map,
    next_days,
    shift_days,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    tasks = []

    data_inicial = midnight(datetime.today() + timedelta(days=shift_days))
    data_final = midnight(datetime.today() + timedelta(days=shift_days + next_days))
    for rota_hash, rota_external_ids in rotas_ativas_map.items():
        tasks.append(
            _buscar_rotinas_e_trechos_vendidos.s(  # type: ignore
                client.company.id, data_inicial.isoformat(), data_final.isoformat(), rota_hash, rota_external_ids
            ).set(queue=queue_name)
        )

    _group = group(*tasks)
    _chain = chain(
        _group,
        finisher_descobrir_operacao_vexado.si(client.company.id),  # type: ignore
    ).on_error(finisher_descobrir_operacao_vexado_on_error.s(client.company.id))  # type: ignore
    if return_task_object:
        return _chain
    _chain()
    return _chain


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def finisher_descobrir_operacao_vexado(company_id):
    inativa_rotas_sem_rotinas(company_id)
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company_id)


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def finisher_descobrir_operacao_vexado_on_error(context, exception, traceback, company_id):
    inativa_rotas_sem_rotinas(company_id)
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company_id, failure=True)


@shared_task(rate_limit=DEFAULT_RATE_LIMIT)
@retry(
    exceptions_type=(
        RodoviariaConnectionError,
        MyCircuitBreakerError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
@traced("vexado.descobrir_operacao._buscar_rotinas_e_trechos_vendidos")
def _buscar_rotinas_e_trechos_vendidos(
    company_rodoviaria_id: int,
    data_inicial: str,  # isoformat
    data_final: str,  # isoformat
    rota_hash: str,
    rota_external_ids: list[int],
):
    data_inicial = datetime.fromisoformat(data_inicial)
    data_final = datetime.fromisoformat(data_final)
    rota = RotaVexado.objects.get(id_hash=rota_hash, company_id=company_rodoviaria_id)
    first_cp = Checkpoint.objects.filter(rota=rota, idx=0).values("local__cidade__timezone").first()
    first_timezone = "America/Sao_Paulo"
    if first_cp:
        first_timezone = first_cp["local__cidade__timezone"] or first_timezone
    client = _get_client_by_company_rodoviaria_id(rota.company_id)
    rotinas_existentes_map = _rotinas_existentes_map(rota, data_inicial, data_final)
    trechos_vendidos_existentes_map = _trechos_vendidos_existentes_map(rota)

    viagens = _buscar_viagens_da_rota(client, rota_external_ids, data_inicial, data_final)
    itinerario = rota.parsed_data
    trechos_vendidos_api_por_classe, mapa_tipos_assentos = _filter_trechos_vendidos_rota(itinerario, rota.company_id)
    local_embarque_id_map = _map_local_embarque_id(client.company.id, itinerario)
    datetime_ida_checkpoint_map = _map_datetime_ida_checkpoint(itinerario)

    rotina_to_create = []
    rotina_to_update = []
    trechos_vendidos_to_create = []
    trechos_vendidos_to_update = []
    rotina_trecho_vendido_to_create = []
    rotinas_ativas = set()
    trechos_vendidos_ativos = set()
    for viagem in viagens:
        datetime_ida: datetime = to_tz(viagem.datetime_ida, first_timezone)
        rotina = rotinas_existentes_map.get(datetime_ida)
        if not rotina:
            rotina = _rotina_obj(rota, viagem, datetime_ida)
            rotinas_existentes_map[datetime_ida] = rotina
            rotina_to_create.append(rotina)
        else:
            _update_rotina_obj(viagem, rotina)
            rotina_to_update.append(rotina)
        rotinas_ativas.add(rotina.datetime_ida)
        classes = _calcula_classes_viagem(viagem)
        trechos_bloqueados = {(tb.origem.id_external, tb.destino.id_external) for tb in viagem.trechos_bloqueados}
        cidades_bloqueadas = {cb.cidade.id_external for cb in viagem.cidades_bloqueadas}
        for classe, capacidade_classe in classes:
            origens_destinos_vendidos = trechos_vendidos_api_por_classe.get(classe)
            if not origens_destinos_vendidos:
                continue
            for (origem_id, destino_id), preco in origens_destinos_vendidos.items():
                if (
                    (origem_id, destino_id) in trechos_bloqueados
                    or origem_id in cidades_bloqueadas
                    or destino_id in cidades_bloqueadas
                ):
                    continue
                local_embarque_origem_id = local_embarque_id_map[origem_id]
                local_embarque_destino_id = local_embarque_id_map[destino_id]
                key = (local_embarque_origem_id, local_embarque_destino_id, classe, capacidade_classe)
                trecho_vendido = trechos_vendidos_existentes_map.get(key)
                duracao = datetime_ida_checkpoint_map[destino_id] - datetime_ida_checkpoint_map[origem_id]
                distancia = duracao.total_seconds() / 60  # considera média de 60 km/h
                tipo_assento_id = mapa_tipos_assentos[classe]
                if not trecho_vendido:
                    trecho_vendido = _trecho_vendido_obj(
                        rota,
                        classe,
                        capacidade_classe,
                        preco,
                        local_embarque_origem_id,
                        local_embarque_destino_id,
                        duracao,
                        distancia,
                        tipo_assento_id,
                    )
                    trechos_vendidos_to_create.append(trecho_vendido)
                    trechos_vendidos_existentes_map[key] = trecho_vendido
                else:
                    _update_trecho_vendido_obj(preco, trecho_vendido, distancia, tipo_assento_id)
                    trechos_vendidos_to_update.append(trecho_vendido)
                trechos_vendidos_ativos.add(key)
                rotina_trecho_vendido_to_create.append(_rotina_trecho_vendido_obj(rotina, trecho_vendido))

    rotina_to_update += _filter_rotinas_to_inactivate(rotinas_existentes_map, rotinas_ativas)
    trechos_vendidos_to_update += _filter_trechos_vendidos_to_inactivate(
        trechos_vendidos_existentes_map, trechos_vendidos_ativos
    )
    _create_and_update_all(
        rotina_to_create,
        rotina_to_update,
        trechos_vendidos_to_create,
        trechos_vendidos_to_update,
        rotina_trecho_vendido_to_create,
    )


def _create_and_update_all(
    rotina_to_create,
    rotina_to_update,
    trechos_vendidos_to_create,
    trechos_vendidos_to_update,
    rotina_trecho_vendido_to_create,
):
    Rotina.objects.bulk_create(rotina_to_create)
    Rotina.objects.bulk_update(rotina_to_update, ["ativo", "updated_at", "id_external"])
    TrechoVendido.objects.bulk_create(trechos_vendidos_to_create)
    TrechoVendido.objects.bulk_update(
        trechos_vendidos_to_update, ["preco", "distancia", "updated_at", "ativo", "tipo_assento_id"]
    )
    RotinaTrechoVendido.objects.bulk_create(rotina_trecho_vendido_to_create, ignore_conflicts=True)


def _update_rotina_obj(viagem, rotina):
    rotina.ativo = True
    rotina.updated_at = timezone.now()
    rotina.id_external = viagem.id_external


def _rotina_obj(rota, viagem, datetime_ida):
    return Rotina(rota=rota, datetime_ida=datetime_ida, id_external=viagem.id_external)


def _rotina_trecho_vendido_obj(rotina, trecho_vendido):
    return RotinaTrechoVendido(
        trecho_vendido=trecho_vendido, rotina=rotina, datetime_ida_trecho_vendido=rotina.datetime_ida
    )


def _update_trecho_vendido_obj(preco, trecho_vendido, distancia, tipo_assento_id):
    trecho_vendido.preco = preco
    trecho_vendido.distancia = distancia
    trecho_vendido.updated_at = timezone.now()
    trecho_vendido.tipo_assento_id = tipo_assento_id
    trecho_vendido.ativo = D(str(preco)) < MAX_PRECO_TRECHO


def _trecho_vendido_obj(
    rota,
    classe,
    capacidade_classe,
    preco,
    local_embarque_origem_id,
    local_embarque_destino_id,
    duracao,
    distancia,
    tipo_assento_id,
):
    return TrechoVendido(
        rota=rota,
        origem_id=local_embarque_origem_id,
        destino_id=local_embarque_destino_id,
        classe=classe,
        capacidade_classe=capacidade_classe,
        duracao=duracao,
        distancia=distancia,
        preco=preco,
        updated_at=timezone.now(),
        ativo=0 < D(str(preco)) < MAX_PRECO_TRECHO,
        tipo_assento_id=tipo_assento_id,
    )


def _filter_rotinas_to_inactivate(rotinas_existentes_map, rotinas_ativas):
    rotina_to_inactivate = []
    for rotina_nao_encontrada in set(rotinas_existentes_map.keys()).difference(rotinas_ativas):
        rotinas_existentes_map[rotina_nao_encontrada].ativo = False
        rotinas_existentes_map[rotina_nao_encontrada].updated_at = timezone.now()
        rotina_to_inactivate.append(rotinas_existentes_map[rotina_nao_encontrada])
    return rotina_to_inactivate


def _filter_trechos_vendidos_to_inactivate(trechos_vendidos_existentes_map, trechos_vendidos_ativos):
    trechos_vendidos_to_inactivate = []
    for tv_nao_encontrado in set(trechos_vendidos_existentes_map.keys()).difference(trechos_vendidos_ativos):
        trechos_vendidos_existentes_map[tv_nao_encontrado].ativo = False
        trechos_vendidos_existentes_map[tv_nao_encontrado].updated_at = timezone.now()
        trechos_vendidos_to_inactivate.append(trechos_vendidos_existentes_map[tv_nao_encontrado])
    return trechos_vendidos_to_inactivate


def _buscar_viagens_da_rota(client, rota_external_ids, data_inicial, data_final):
    viagens = []
    for rota_external_id in rota_external_ids:
        viagens += (
            endpoints.ListarViagensRota(client, detailed=True)
            .send(
                client.company.company_external_id,
                rota_external_id,
                json={},
                params={
                    "dataPartidaInicio": data_inicial.strftime("%d/%m/%Y"),
                    "dataPartidaFim": data_final.strftime("%d/%m/%Y"),
                    "size": 1000,
                },
            )
            .parsed
        )
    return list(filter(lambda k: k.ativo, viagens))


def _calcula_classes_viagem(viagem: models_vexado.Viagem):
    if viagem.poltronas:
        count_por_classe = defaultdict(int)
        for poltrona in viagem.poltronas:
            count_por_classe[poltrona.classe] += 1
        return list(count_por_classe.items())

    count = 0
    fileiras = (
        viagem.veiculo_dto.mapa_veiculo_dto.fileiras_segundo_andar
        if viagem.andar == 2
        else viagem.veiculo_dto.mapa_veiculo_dto.fileiras_primeiro_andar
    )
    for fileira in fileiras:
        for assento in fileira.assentos:
            if assento.tipo_assento == "NORMAL":
                count += 1
    return [(viagem.tipo_preco.classe, count)]


def _map_datetime_ida_checkpoint(itinerario) -> dict[int, datetime]:
    """Retorna mapa com o datetime_ida de cada checkpoint de uma rota

    output:
        {
            id_external: datetime_ida(2023, 10, 4, 15, 0, tz_info="UTC"),
            ...
        }
    """
    return {cp.local.external_local_id: cp.datetime_ida for cp in itinerario}


@lock("vexado_get_cached_mapa_precos_e_tipos_assentos_{company_rodoviaria_id}", max_wait_time=300)
def _get_cached_mapa_precos_e_tipos_assentos_locked(
    company_rodoviaria_id,
) -> tuple[dict[str, dict[tuple[int, int], str]], dict[str, int]]:
    return _get_cached_mapa_precos_e_tipos_assentos(company_rodoviaria_id)


@memoize_with_log(timeout=60 * 60)
def _get_cached_mapa_precos_e_tipos_assentos(
    company_rodoviaria_id,
) -> tuple[dict[str, dict[tuple[int, int], str]], dict[str, int]]:
    """Retorna mapa dos preços cadastrados no sistema do parceiro

    output:
    (
        {
            "Leito": {
                (origem_id, destino_id): preco,
                ...
            },
            ...
        },
        {"Semi-leito Premium": 111}
    )
    """
    client = _get_client_by_company_rodoviaria_id(company_rodoviaria_id)
    precos = _get_precos_cadastrados(client)
    mapa_precos = defaultdict(dict)
    tipos_assentos = []
    for preco in precos:
        tipos_assentos.append(TipoAssento(company_id=company_rodoviaria_id, tipo_assento_parceiro=preco.classe))
        mapa_precos[preco.classe][(preco.origem_id, preco.destino_id)] = preco.preco
    TipoAssento.objects.bulk_create(tipos_assentos, ignore_conflicts=True)
    mapa_tipos_assentos = {
        ta.tipo_assento_parceiro: ta.id for ta in TipoAssento.objects.filter(company_id=company_rodoviaria_id)
    }
    return mapa_precos, mapa_tipos_assentos


def _get_precos_cadastrados(client) -> list[models_vexado.PrecoClasseSimplificado]:
    response = endpoints.BuscarPrecosEmpresaSimplificado(client).send(client.company.company_external_id)
    precos = response.parsed
    if response.raw.get("totalPagina", 1) > 1:
        for pagina in range(2, response.raw["totalPagina"] + 1):
            precos_pagina = endpoints.BuscarPrecosEmpresaSimplificado(client).send(
                client.company.company_external_id, pagina=pagina
            )
            precos += precos_pagina.parsed
    return precos


def _trechos_vendidos_existentes_map(rota):
    """Retorna mapa com os trechos vendidos de uma rota

    output:
        {
            (origem_id, destino_id, classe, capacidade): TrechoVendido(),
            ...
        }
    """
    trechos_vendidos_existentes = TrechoVendido.objects.filter(rota=rota)
    trechos_vendidos_existentes_map = {
        (tv.origem_id, tv.destino_id, tv.classe, tv.capacidade_classe): tv for tv in trechos_vendidos_existentes
    }
    return trechos_vendidos_existentes_map


def _rotinas_existentes_map(rota: RotaVexado, data_inicial: datetime, data_final: datetime) -> dict[datetime, Rotina]:
    """Retorna mapa com os datetimes_idas das rotinas de uma rota em uma margem de data

    output:
        {
            datetime(2023, 10, 6, 19, 0, tz_info="America/Sao_Paulo"): Rotina,
            ...
        }
    """
    return (
        Rotina.objects.filter(rota=rota, datetime_ida__date__range=[data_inicial, data_final])
        .distinct("datetime_ida")
        .order_by("-datetime_ida")
        .in_bulk(field_name="datetime_ida")
    )


def _filter_trechos_vendidos_rota(itinerario, company_rodoviaria_id):
    """A partir do itinerario de uma rota retorna as combinações de origem e destino que possuem preço
    cadastrado no sistema do parceiro

    output:
        {
            "Leito": {
                (origem_id, destino_id): preco,
                ...
            },
            ...
        }
    """
    mapa_precos_por_classe, mapa_tipos_assentos = _get_cached_mapa_precos_e_tipos_assentos_locked(company_rodoviaria_id)
    origem_destino_combinations = {
        (o.local.external_local_id, d.local.external_local_id) for (o, d) in combinations(itinerario, 2)
    }
    trechos_vendidos_da_rota: dict[str, dict[tuple, str]] = {}
    for classe in mapa_precos_por_classe:
        trechos_vendidos_da_rota[classe] = {
            key: value for key, value in mapa_precos_por_classe[classe].items() if key in origem_destino_combinations
        }
    return trechos_vendidos_da_rota, mapa_tipos_assentos


def _map_local_embarque_id(company_rodoviaria_id, itinerario):
    """Retorna mapa de ids externos de locais de embarque

    output:
        {
            id_external: id_local_embarque,
            ...
        }
    """
    local_external_ids = [cp.local.external_local_id for cp in itinerario]
    locais_embarque = LocalEmbarque.objects.filter(
        cidade__company_id=company_rodoviaria_id, id_external__in=local_external_ids
    )
    return {local.id_external: local.id for local in locais_embarque}


def _get_client_by_company_rodoviaria_id(company_rodoviaria_id):
    return VexadoLogin.objects.select_related("company").get(company_id=company_rodoviaria_id)

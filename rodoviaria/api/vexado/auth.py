import logging

from rodoviaria.api.vexado.services import get_api_token_from_client
from rodoviaria.models import Company, VexadoLogin
from rodoviaria.service.exceptions import RodoviariaLoginNotFoundException

rodovlogger = logging.getLogger("rodoviaria")


class VexadoAuth:
    def __init__(self, client, site, username, password, token: None | dict = None):
        self.client = client
        self.site = site
        self.username = username
        self.password = password
        self._token = token

    @classmethod
    def from_company(cls, company: Company):
        try:
            client: VexadoLogin = VexadoLogin.objects.select_related("company").get(company=company)

        except VexadoLogin.DoesNotExist as ex:
            raise RodoviariaLoginNotFoundException(
                integracao="vexado",
                company_pk=company,
                modelo_venda=company.modelo_venda,
            ) from ex
        return cls(client=client, site=client.site, username=client.user, password=client.password)

    @classmethod
    def from_client(cls, client: VexadoLogin):
        return cls(client=client, site=client.site, username=client.user, password=client.password)

    def get_token(self, force_renew=False):
        if self._token is None or force_renew:
            data = get_api_token_from_client(self.client, force_renew)
            self._token = {"auth": data["access_token"], "new_login": force_renew}
        return self._token

    def __call__(self, r, force_renew=False):
        token = self.get_token(force_renew)
        r.headers["Authorization"] = f"Bearer {token['auth']}"
        r.headers["site"] = self.site
        return r

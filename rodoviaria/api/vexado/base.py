import json
import logging
import re
from http import HTTPStatus
from json import JSONDecodeError

import requests
from pydantic import ValidationError, parse_obj_as
from requests.adapters import HTT<PERSON>dapter, Retry
from requests.exceptions import ConnectionError
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from bp.logging import log_integration_request
from commons.django_utils import error_str
from commons.token_bucket import TokenBucket
from rodoviaria.api.base import ResponseWrapper
from rodoviaria.api.vexado.auth import VexadoAuth
from rodoviaria.api.vexado.exceptions import VexadoAPIError
from rodoviaria.api.vexado.models import Violacao
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaTimeoutException,
    RodoviariaTooManyRequestsError,
    RodoviariaUnauthorizedError,
)

rodovlogger = logging.getLogger("rodoviaria")


class BaseRequest:
    extra_logs = {"api": "VexadoAPI", "log_type": "request_log"}
    timeout = 60
    retry_on_status: list[int] = [401, 406, 408, 412]
    clean_param = None
    token_bucket = False

    def __repr__(self):
        return f"VEXADO_{self.__class__.__name__}_{self.login.company.id}_{self.login.company.modelo_venda}"

    def __init__(self, login):
        self.login = login
        self.auth = VexadoAuth.from_client(self.login)
        self.session = self._build_session()

    def _build_session(self):
        session = requests.Session()
        retry_params = {"total": 3, "backoff_factor": 1, "raise_on_status": False}
        if self.retry_on_status:
            retry_params["status_forcelist"] = self.retry_on_status
        retry_strategy = Retry(**retry_params)
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        session.auth = self.auth
        return session

    @retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
    def _request(self, method, path, params, json, timeout):
        if self.token_bucket:
            token_bucket = TokenBucket.from_client(self.login)
            token_bucket.try_get_token()

        url = f"{self.login.company.url_base}/{path}"

        try:
            response = self.session.request(
                method,
                url,
                params=params,
                json=json,
                timeout=timeout,
            )
        except ConnectionError as ex:
            log_msg = f"vexado '{method} {url}' {json=} {params=} connection error"
            rodovlogger.error(log_msg, extra=self.extra_logs)
            raise RodoviariaConnectionError(log_msg) from ex
        try:
            response_json = response.json()
        except (ValueError, JSONDecodeError):
            response_json = None

        company_id = self.login.company.id

        error_message = None
        try:
            self.raise_for_status(method, url, json, params, response, response_json)
        except (RodoviariaException, RodoviariaConnectionError, RodoviariaUnauthorizedError) as ex:
            error_message = error_str(ex)
            raise
        finally:
            log_integration_request(
                integration="VexadoAPI",
                company_id=company_id,
                url=url,
                method=method,
                payload=json,
                params=params,
                response=response,
                error_message=error_message,
            )

        return response

    def raise_for_status(self, method, url, json, params, response, response_json):
        response_msg = f"vexado '{method} {url}' json={json} params={params} status_code={response.status_code}"

        if response.ok:
            return

        err_msg = self._parse_response(response_json, "erro")
        if response.status_code == HTTPStatus.UNAUTHORIZED:
            self.auth.get_token(True)
            err_msg = self._parse_response(response_json, "message")
            raise RodoviariaUnauthorizedError(err_msg)
        elif response.status_code == HTTPStatus.FORBIDDEN:
            raise RodoviariaUnauthorizedError
        elif response.status_code == HTTPStatus.REQUEST_TIMEOUT:
            raise RodoviariaTimeoutException(err_msg)
        elif response.status_code == HTTPStatus.TOO_MANY_REQUESTS:
            raise RodoviariaTooManyRequestsError

        self.raise_for_response(response, response_json, response_msg)
        raise VexadoAPIError(message=response_msg)

    def _parse_response(self, response_json, key: str):
        if isinstance(response_json, dict):
            return response_json.get(key, "")
        return response_json

    def send(self, params=None, json=None):
        response = self._request(self.method, self.path, params=params, json=json, timeout=self.timeout)
        return ResponseWrapper(self, response)

    def clean_response(self, response):
        if self.clean_param:
            return response[self.clean_param]
        return response

    def parse_response(self, response):
        return parse_obj_as(self.output_model, response)

    def raise_for_response(self, response, response_json, response_msg):
        if response_json is None:
            return

        violacoes = response_json.get("violacoes")
        if not violacoes or not isinstance(violacoes, list):
            msg_error = response_json.get("mensagem")
            if not msg_error:
                return
            raise VexadoAPIError(f"Vexado erro: {self._parse_error_msg(msg_error)}")

        parsed_violacoes = self._parse_violacoes(violacoes)
        message = self._format_error_msg(parsed_violacoes)
        raise VexadoAPIError(message, violacoes=parsed_violacoes)

    def _parse_error_msg(self, mensagem):
        match = re.search(r"mensagem='(.*?)'", mensagem)
        if match:
            return match.group(1)
        return mensagem

    def _format_error_msg(self, parsed_violacoes: list[Violacao]):
        msgs = []
        for violacao in parsed_violacoes:
            if violacao.violacao_dominio and violacao.violacao_dominio["mensagem"]:
                msgs.append(violacao.violacao_dominio["mensagem"])
            elif violacao.mensagem:
                msgs.append(violacao.mensagem)
            else:
                msgs.append(violacao.json(exclude_none=True))
        return ", ".join(msgs)

    def _parse_violacoes(self, violacoes):
        result = []
        for violacao in violacoes:
            try:
                result.append(Violacao.parse_obj(violacao))
            except ValidationError:
                obj = {"mensagem": json.dumps(violacao)}
                result.append(Violacao.parse_obj(obj))
        return result


class BaseRequestCollection(BaseRequest):
    def parse_response(self, response):
        return parse_obj_as(list[self.output_model], response)

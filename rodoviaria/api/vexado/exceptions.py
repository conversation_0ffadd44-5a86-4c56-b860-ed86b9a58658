from typing import Optional

from rodoviaria.api.vexado.models import Violacao
from rodoviaria.service.exceptions import APIError, RodoviariaException

_OptionalViolacoes = Optional[list[Violacao]]


class VexadoAPIError(APIError):
    """Error on Vexado API request."""

    def __init__(self, message, violacoes: _OptionalViolacoes = None):
        super().__init__(message)
        self.violacoes = violacoes if violacoes is not None else []

    @property
    def codigos(self):
        return list(self._iter_codigos())

    def _iter_codigos(self):
        for violacao in self.violacoes:
            if violacao.codigo:
                yield violacao.codigo
            if violacao.violacao_dominio and violacao.violacao_dominio["codigo"]:
                yield violacao.violacao_dominio["codigo"]


class VexadoEfetuarReservaError(RodoviariaException):
    pass

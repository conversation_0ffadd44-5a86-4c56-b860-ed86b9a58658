import hashlib
import logging
import re
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, root_validator, validator

from commons.dateutils import to_default_tz, today_midnight
from rodoviaria.api.forms import Localidade
from rodoviaria.data.transbrasil import MAP_CLASSES_BUSER

logger = logging.getLogger(__name__)


class LoginForm(BaseModel):
    username: str
    password: str
    chaveCaptcha: str = "string"


class BuscarServicoVexadoForm(BaseModel):
    origem: str
    destino: str
    data: date


class CancelarReservasForm(BaseModel):
    empresa_id: str = Field(alias="empresaId")
    motivo: str
    reservas: List[int]


class PassageiroForm(BaseModel):
    # {
    #   "poltronaId": 793227,
    #   "nome": "Leandro",
    #   "documentoComFoto": "22222",
    #   "dtNascimento": "17/10/1995",
    #   "cpfPassageiro": "506.361.200-82",
    #   "valor": "125.25",
    #   "tipoEmissao": "NORMAL",
    #   "tipoDocumento": "RG",
    #   "telefone": "(12) 99160-7865"
    # }
    poltrona_id: int = Field(alias="poltronaId")
    numero_poltrona: int = Field(alias="numeroPoltrona")
    nome: str
    rg: str = Field(alias="documentoComFoto")
    cpf: Optional[str] = Field(alias="cpfPassageiro")
    valor: Decimal
    tipo_emissao = Field(alias="tipoEmissao", default="NORMAL")
    tipo_documento = Field(alias="tipoDocumento", default="RG")
    telefone: Optional[str]

    @validator("cpf")
    def not_empty_cpf(cls, cpf):
        if cpf == "":
            return None
        return cpf


class ReservaForm(BaseModel):
    # {
    #   "passageiroDto": {},
    #   "poltronaId": 793227,
    #   "itinerarioId": 21136,
    #   "idCidadeOrigem": 1,
    #   "idCidadeDestino": 2932,
    #   "valor": "125.25"
    # }
    passageiro_dto: PassageiroForm = Field(alias="passageiroDto")
    poltrona_id: int = Field(alias="poltronaId")
    itinerario_id: int = Field(alias="itinerarioId")
    id_cidade_origem: int = Field(alias="idCidadeOrigem")
    id_cidade_destino: int = Field(alias="idCidadeDestino")
    valor: Decimal
    garantia_preco_id: Optional[str] = Field(alias="garantiaPrecoUuid")


class FormaPagamentoForm(BaseModel):
    # {
    #   "tipo": "Dinheiro",
    #   "valor": 125.25,
    #   "quantidadeParcelas": 1
    # }
    tipo: str = "Dinheiro"
    codigoTipo: str = "DINHEIRO"
    valor: Decimal
    quantidade_parcelas: str = Field(alias="quantidadeParcelas", default=1)


class DadosCompradorForm(BaseModel):
    # {
    #   "cpfComprador": "506.361.200-82",
    #   "nomeComprador": "Leandro",
    #   "telefoneComprador": "(12) 99160-7865"
    # }
    cpf_comprador: str = Field(alias="cpfComprador")
    nome_comprador: Optional[str] = Field(alias="nomeComprador")
    telefone_comprador: Optional[str] = Field(alias="telefoneComprador")


class EfetuarReservasForm(BaseModel):
    """
    >>> data = {
    ...    "cidadeOrigem": 1,
    ...    "cidadeDestino": 2932,
    ...    "empresaId": 5,
    ...    "reservas": [{}],
    ...    "formasPagamentoDto": [{}],
    ...    "dadosCompradorDto": {}
    ...  }
    >>> form = EfetuarReservasForm(**data)
    >>> assert form.cidade_origem_id = 1
    """

    cidade_origem_id: int = Field(alias="cidadeOrigem")
    cidade_destino_id: int = Field(alias="cidadeDestino")
    empresa_id: int = Field(alias="empresaId")
    reservas: list[ReservaForm]
    formas_pagamento_dto: list[FormaPagamentoForm] = Field(alias="formasPagamentoDto")
    dados_comprador_dto: DadosCompradorForm = Field(alias="dadosCompradorDto")


class LocalidadeParada(Localidade):
    @root_validator(pre=True)
    def translate(cls, v: dict):
        return {
            "nome_cidade": v["nome"],
            "external_local_id": v["id"],
            "uf": v["uf"],
            "external_cidade_id": v["id"],
            "id_cidade_ibge": v["codigoIbge"],
        }


class Parada(BaseModel):
    local: LocalidadeParada = Field(alias="cidade")
    datetime_ida: Optional[datetime]
    tempo_embarque: Optional[int]
    duracao: Optional[int]
    distancia: Optional[Decimal]

    @validator("datetime_ida", pre=True)
    def generate_datetime_ida(cls, v):
        if isinstance(v, datetime):
            return v
        return datetime.strptime(v, "%Y-%m-%d %H:%M")

    @validator("duracao", pre=True)
    def _parse_duracao(cls, v):
        splitted_duracao = v.split(":")
        return timedelta(hours=int(splitted_duracao[0]), minutes=int(splitted_duracao[1])).total_seconds()

    @validator("distancia", pre=True)
    def _parse_distancia(cls, v: str):
        distancia_com_ponto = v.replace(",", ".")
        return Decimal(distancia_com_ponto)


class Itinerario(BaseModel):
    """
    "rotaDto": {
        "id": 10,
        "trechosDto": [
            {
                "id": 171,
                "idEmpresa": 5,
                "cidadeDestino": {
                    "id": 1,
                    "uf": "DF",
                    "descricaoUf": "Distrito Federal",
                    "nome": "Brasília",
                    "nomeComUf": "Brasília - DF",
                    "codigoIbge": "5300108"
                },
                "cidadeOrigem": null,
                "cidadeOrigemAlternativa": null,
                "cidadeDestinoAlternativa": null,
                "rotaDto": {},
                "ordem": 1,
                "duracao": "00:20",
                "pontoEmbarque": "RODOVIARIA DE TAGUATINGA",
                "taxaEmbarque": "0",
                "quilometragem": "0,00",
                "conexao": null,
                "habilitadoTrechoAlternativoLimeteEstadual": null
            }
        ]
    }
    """

    __root__: List[Parada]

    @property
    def _sigla_duracao(self):
        return "-".join(f"{p.local.external_local_id}.{p.duracao}" for p in self)

    @property
    def hash(self):
        inicio = self[0].local.external_local_id
        fim = self[-1].local.external_local_id
        digest = hashlib.md5(self._sigla_duracao.encode()).hexdigest()  # noqa: S324
        return f"{inicio}{fim}{digest}"

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    @validator("__root__")
    def _attach_tempos(cls, v):
        if not v:
            return v

        while len(v) > 1 and v[1].duracao == 0:
            v[1].datetime_ida = v[0].datetime_ida
            v = v[1:]

        while len(v) > 1 and v[-1].duracao == 0:
            v = v[:-1]

        if len(v) <= 1:
            return v

        iti_first = v[0]
        iti_first.duracao = 0
        iti_first.tempo_embarque = 0
        iti_first.distancia = Decimal("0")

        iti_last = v[-1]
        iti_last.tempo_embarque = 0

        for iti_i, iti_j in zip(v, v[1:]):
            duracao = timedelta(seconds=iti_j.duracao)
            iti_j.datetime_ida = iti_i.datetime_ida + duracao

            if iti_j == v[-1]:
                # Não tem tempo de embarque no último checkpoint.
                tempo_embarque = timedelta(minutes=0)
            elif duracao <= timedelta(minutes=10):
                # Não tem tempo de embarque em paradas rápidas.
                tempo_embarque = timedelta(minutes=0)
            else:
                tempo_embarque = timedelta(minutes=10)

            iti_j.tempo_embarque = tempo_embarque.total_seconds()
            iti_j.duracao = (duracao - tempo_embarque).total_seconds()
            iti_j.distancia = iti_j.distancia or abs(
                timedelta(seconds=iti_j.duracao).seconds // 60
            )  # estima por 60km/h
            logger.warning(
                "distancia_nula: vexado._attach_tempos",
                extra={"duracao": iti_j.duracao, "distancia": iti_j.distancia},
            )
        return v


class Usuario(BaseModel):
    id: Optional[int]
    nome: str
    email: str
    telefone: str
    cpf: str
    roles: Optional[List[str]]


class Motorista(BaseModel):
    id: Optional[int]
    id_usuario_empresa: int = Field(alias="codigoUsuarioEmpresa")
    cnh_numero: str = Field(alias="numeroRegistroCNH")
    cnh_validade: str = Field(alias="validadeCNH")
    cnh_categoria: str = Field(alias="categoriaCNH")
    cnh_orgao_emissor: str = Field(alias="docIdentidadeEmissor")
    cnh_uf: str = Field(alias="uf")
    antt_numero: str = Field(alias="numeroRegistroANTT")
    antt_validade: str = Field(alias="validadeRegistroANTT")

    @validator("cnh_validade", "antt_validade", pre=True)
    def parse_dates(cls, v):
        try:
            return v.strftime("%d/%m/%Y")
        except AttributeError:
            return v

    class Config:
        allow_population_by_field_name = True


class ViolacaoDominio:
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, value):
        if not isinstance(value, str):
            raise TypeError("string expected")

        match = re.search(r"ViolacaoDominio{(.+?)}", value)
        if match:
            props = match.group(1)
            tipo_match = re.search(r"tipo=(.+?)\b", props)
            codigo_match = re.search(r"codigo='(.+?)'", props)
            mensagem_match = re.search(r"mensagem='(.+?)'", props)

            tipo = tipo_match.group(1) if tipo_match else None
            codigo = codigo_match.group(1) if codigo_match else None
            mensagem = mensagem_match.group(1) if mensagem_match else None

            return {
                "tipo": tipo,
                "codigo": codigo,
                "mensagem": mensagem,
            }

        return None


class Violacao(BaseModel):
    # {
    #   "tipo": "ERRO",
    #   "codigo": "erroReserva",
    #   "mensagem": "ViolacaoDominio{tipo=ERRO, codigo='valorReservaErrado',
    #       mensagem='Valor da reserva informado está errado.'}"
    # }
    tipo: Optional[str]
    codigo: Optional[str]
    mensagem: Optional[str]
    violacao_dominio: Optional[ViolacaoDominio] = Field(alias="mensagem")


class MapaVeiculoForm(BaseModel):
    id_external: int
    has_dois_andares: bool
    quantidade_poltronas_primeiro_andar: int
    quantidade_poltronas_segundo_andar: int
    provider_data: dict


class VeiculoForm(BaseModel):
    descricao: str
    external_mapa_veiculo_id: int
    id_external: int


class AlterarVeiculoForm(BaseModel):
    id: int
    idVeiculo: int
    idEmpresa: int
    andar: int


class SimplifiedViagensForm(BaseModel):
    id_external: int = Field(alias="id")
    datetime_ida: datetime = Field(alias="dataHoraPartida")
    rota_external_id: int = Field(alias="rotaDto")
    tipo_assento: str = Field(alias="tipoPreco")
    veiculo_id: int = Field(alias="veiculoDto")
    veiculo_andar: int = Field(alias="andar")

    @validator("datetime_ida", pre=True)
    def strptime_datetime_ida(cls, v):
        return to_default_tz(datetime.strptime(v, "%d/%m/%Y %H:%M"))

    @validator("tipo_assento")
    def classe_buser(cls, v):
        return MAP_CLASSES_BUSER[v]

    @validator("rota_external_id", pre=True)
    def rota_id_inside_dict(cls, rotaDto):
        return rotaDto["id"]

    @validator("tipo_assento", pre=True)
    def tipo_assento_inside_dict(cls, tipoPreco):
        return tipoPreco["tipoPreco"]

    @validator("veiculo_id", pre=True)
    def veiculo_id_inside_dict(cls, veiculoDto):
        return veiculoDto["id"]


class SimplifiedReserva(BaseModel):
    localizador: int = Field(alias="id")


class SimplifiedReservaList(BaseModel):
    reservas: list[SimplifiedReserva]


class SimplifiedViagensList(BaseModel):
    viagens: List[SimplifiedViagensForm] = Field(alias="itinerarios")


class VexadoToken(BaseModel):
    tokenJwt: str


class VexadoEmpresa(BaseModel):
    id: int
    nome: str = Field(alias="nome")
    nome_fantasia: str = Field(alias="nomeFantasia")

    @property
    def name(self):
        return f"{self.nome} / {self.nome_fantasia}"

    def dict(self, *args, **kwargs):
        return {"id": self.id, "name": self.name}


class VexadoReservaOut(BaseModel):
    """
    >>> model = VexadoReservaOut(reserva=192929)
    >>> assert model.reserva = 192929
    """

    reserva: int


class RotaDetalhada(BaseModel):
    external_id: int
    itinerario: Itinerario
    cleaned: list[dict]

    @root_validator(pre=True)
    def clean_dict(cls, v: dict):
        # gamb para não alterar o provider_data atual das rotas e usar o padrão esperado pelo Itinerario
        pontos_parada = []
        for parada in v["trechosDto"]:
            pontos_parada.append(
                {
                    "cidade": parada["cidadeDestino"],
                    "ordem": parada["ordem"],
                    "duracao": parada["duracao"],
                    "distancia": parada["quilometragem"],
                }
            )
        if pontos_parada:
            pontos_parada[0]["datetime_ida"] = today_midnight().strftime("%Y-%m-%d %H:%M")
        paradas = sorted(pontos_parada, key=lambda d: d["ordem"])
        return {"external_id": v["id"], "itinerario": paradas, "cleaned": paradas}


class Assento(BaseModel):
    tipo_assento: str = Field(alias="tipoAssento")


class FileirasAndar(BaseModel):
    assentos: List[Assento]


class MapaVeiculoDto(BaseModel):
    fileiras_primeiro_andar: List[FileirasAndar] = Field(alias="fileirasPrimeiroAndar")
    fileiras_segundo_andar: List[FileirasAndar] = Field(alias="fileirasSegundoAndar")


class VeiculoDto(BaseModel):
    mapa_veiculo_dto: MapaVeiculoDto = Field(alias="mapaVeiculoDto")


class LocalBloqueado(BaseModel):
    id_external: str = Field(alias="chave")


class TrechoBloqueado(BaseModel):
    origem: LocalBloqueado = Field(alias="trechoOrigem")
    destino: LocalBloqueado = Field(alias="trechoDestino")


class CidadeBloqueada(BaseModel):
    cidade: LocalBloqueado


class TipoPreco(BaseModel):
    classe: str = Field(alias="tipoPreco")


class Poltrona(BaseModel):
    numero: int
    classe: str = Field(alias="tipoPreco")


class Viagem(BaseModel):
    id_external: int = Field(alias="id")
    data_partida: str = Field(alias="dataPartida")
    hora_saida: str = Field(alias="horaSaida")
    datetime_ida: datetime = Field(alias="dataHoraPartida")
    datetime_chegada: datetime = Field(alias="dataHoraChegada")
    veiculo_dto: VeiculoDto = Field(alias="veiculoDto")
    tipo_preco: Optional[TipoPreco] = Field(alias="tipoPreco")
    andar: int
    ativo: bool
    trechos_bloqueados: List[TrechoBloqueado] = Field(alias="trechosBloqueados")
    cidades_bloqueadas: List[CidadeBloqueada] = Field(alias="cidadesBloqueados")
    poltronas: list[Poltrona]

    @validator("datetime_ida", "datetime_chegada", pre=True)
    def parse_datetime(cls, v):
        return datetime.strptime(v, "%d/%m/%Y %H:%M")


class PrecoClasse(BaseModel):
    origem: LocalidadeParada = Field(alias="cidadeOrigem")
    destino: LocalidadeParada = Field(alias="cidadeDestino")
    classe: str = Field(alias="tipoPreco")
    preco: str = Field(alias="valor")


class PrecoClasseSimplificado(BaseModel):
    origem_id: str = Field(alias="idCidadeOrigem")
    destino_id: str = Field(alias="idCidadeDestino")
    classe: str = Field(alias="tipoPreco")
    preco: str = Field(alias="preco")

import re
from datetime import timedelta
from decimal import Decimal


def format_preco(valor: Decimal) -> str:
    q = Decimal("0.01")
    valor = valor.quantize(q)
    valor = f"{valor:.2f}"
    return valor.replace(".", ",")


def fill_duracao(duracao_str):
    if duracao_str:
        tempo = duracao_str.split(":")
        horas = tempo[0]
        minutos = tempo[1] if len(tempo) == 2 else "00"
        return (  # '1:30' vira 13 horas e 30 minutos na Vexado. Então tem que ser '01:30'
            f"{horas.zfill(2)}:{minutos.rjust(2, '0')}"
        )
    return "00:00"


def duracao_to_timedelta(duracao_str: str):
    if not re.match(r"\d\d:([0-5]\d|60)", duracao_str):
        raise ValueError("Formato de duração inadequado")
    horas, minutos = duracao_str.split(":")
    horas = int(horas)
    minutos = int(minutos)
    return timedelta(hours=horas, minutes=minutos)


def timedelta_to_duracao(duracao_timedelta: timedelta):
    horas = duracao_timedelta.total_seconds() // 3600
    minutos = (duracao_timedelta.total_seconds() % 3600) / 60
    days_ahead = horas // 24
    horas = horas % 24
    return fill_duracao(f"{int(horas)}:{int(minutos)}"), days_ahead

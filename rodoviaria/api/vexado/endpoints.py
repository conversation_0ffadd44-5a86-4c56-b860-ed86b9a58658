from commons.memoize import memoize_with_log
from rodoviaria.service.exceptions import PoltronaJaSelecionadaException, RodoviariaTrechoBloqueadoException

from . import models
from .base import BaseRequest, BaseRequestCollection
from .exceptions import VexadoAPIError, VexadoEfetuarReservaError

# Documentação dos endpoints da Vexado: https://api.vexado.com.br/swagger-ui/index.html#


class GetToken(BaseRequest):
    method = "POST"
    path = "auth/signin"
    output_model = models.VexadoToken
    clean_param = "tokenServico"

    @memoize_with_log(1 * 60 * 60)
    def send(self, params=None, json=None):
        return super().send(params, json)


class ListarEmpresas(BaseRequestCollection):
    method = "POST"
    path = "auth/signin"
    clean_param = "empresas"
    output_model = models.VexadoEmpresa


class EfetuarReserva(BaseRequest):
    method = "POST"
    path = "reserva/cadastrar/terminal-venda"
    output_model = models.VexadoReservaOut

    def raise_for_response(self, response, response_json, response_msg):
        if response.status_code == 412:
            violacoes = response_json.get("violacoes")
            if not violacoes or not isinstance(violacoes, list):
                msg_error = response_json.get("mensagem")
                if not msg_error:
                    raise VexadoAPIError(f"Erro desconhecido no endpoint {self.path}")
                raise VexadoAPIError(f"Vexado erro: {self._parse_error_msg(msg_error)}")
            parsed_violacoes = self._parse_violacoes(violacoes)
            message = self._format_error_msg(parsed_violacoes)
            raise VexadoAPIError(message, violacoes=parsed_violacoes)
        elif response.status_code == 500:
            msg = f"Vexado erro: {response_json['mensagem']}"
            raise VexadoEfetuarReservaError(msg)
        elif response.status_code in {502, 504}:
            msg = f"Vexado API erro no endpoint {self.path}"
            if response_json is not None:
                msg += f": status code {response.status_code}; {response.content}"
            raise VexadoEfetuarReservaError(msg)


class BuscarItinerarioCorrida(BaseRequest):
    method = "GET"
    path = "itinerario/{itinerario}/empresa/{id_empresa}"
    output_model = models.Itinerario

    def send(self, itinerario_external_id, company_external_id):
        self.path = self.path.format(itinerario=itinerario_external_id, id_empresa=company_external_id)
        response_itinerario = super().send()

        return response_itinerario

    def clean_response(self, response):
        data_partida = response["dataPartida"]
        horaSaida = response["horaSaida"]
        datetime_ida = f"{data_partida} {horaSaida}"
        pontos_parada = []
        for parada in response["rotaDto"]["trechosDto"]:
            pontos_parada.append(
                {
                    "cidade": parada["cidadeDestino"],
                    "ordem": parada["ordem"],
                    "duracao": parada["duracao"],
                    "distancia": parada["quilometragem"],
                }
            )
        ponto_de_partida = {
            "cidade": response["rotaDto"]["cidadeOrigem"],
            "datetime_ida": datetime_ida,
            "ordem": 0,
        }
        pontos_parada.append(ponto_de_partida)
        return sorted(pontos_parada, key=lambda d: d["ordem"])

    def parse_response(self, response):
        return self.output_model.parse_obj(response)


class GetCidades(BaseRequest):
    method = "GET"
    path = "public/cidades/nome/{nome_cidade}"

    def send(self, nome_cidade):
        self.path = self.path.format(nome_cidade=nome_cidade)
        return super().send()


class CidadesEmpresa(BaseRequestCollection):
    method = "GET"
    path = "public/cidades/empresa/{company_external_id}/buscar-cidades"
    output_model = models.LocalidadeParada

    def send(self, company_external_id):
        self.path = self.path.format(company_external_id=company_external_id)
        return super().send()


class BuscarServico(BaseRequest):
    method = "GET"
    path = "passagem/origem/{origem}/destino/{destino}/dataIda/{data_ida}"
    clean_param = "passagensIda"

    def send(self, origem, destino, data_ida):
        self.path = self.path.format(origem=origem, destino=destino, data_ida=data_ida)
        return super().send()


class RecuperarPedido(BaseRequest):
    method = "GET"
    path = "pedido/{id_pedido}/empresa/{empresa_id}"

    def send(self, id_pedido, empresa_id):
        self.path = self.path.format(id_pedido=id_pedido, empresa_id=empresa_id)
        return super().send()


class BloquearPoltrona(BaseRequest):
    method = "POST"
    path = "bloqueio-poltrona-temporario"

    def raise_for_response(self, response, response_json, response_msg):
        if response.status_code == 412:
            violacoes = response_json.get("violacoes")
            if violacoes and isinstance(violacoes, list):
                parsed_violacoes = self._parse_violacoes(violacoes)
                message = self._format_error_msg(parsed_violacoes)
                if "Não foi possível reservar a poltrona, porque já foi bloqueado" in message:
                    raise PoltronaJaSelecionadaException(message)
                if "bloqueado temporariamente para venda" in message:
                    raise RodoviariaTrechoBloqueadoException(message)
        super().raise_for_response(response, response_json, response_msg)


class DesbloquearPoltrona(BaseRequest):
    method = "POST"
    path = "bloqueio-poltrona-temporario/liberar"


class RetornaPoltronas(BaseRequest):
    method = "GET"
    path = "mapas-veiculos/itinerario/{itinerario}/origem/{origem}/destino/{destino}"

    def send(self, itinerario, origem, destino):
        self.path = self.path.format(itinerario=itinerario, origem=origem, destino=destino)
        return super().send()


class CadastrarVeiculo(BaseRequest):
    method = "POST"
    path = "veiculo/cadastrar"


class AlterarVeiculo(BaseRequest):
    method = "POST"
    path = "itinerario/alterar-veiculo"


class ListarVeiculos(BaseRequest):
    method = "GET"
    path = "veiculo/empresa/{company_external_id}/veiculos"

    def send(self, company_external_id, json, params):
        self.path = self.path.format(company_external_id=company_external_id)
        return super().send(json=json, params=params)


class MapasVeiculos(BaseRequest):
    method = "GET"
    path = "mapas-veiculos/buscar"


class CadastrarPreco(BaseRequest):
    method = "POST"
    path = "preco/cadastrar"


class BuscarPrecos(BaseRequestCollection):
    method = "GET"
    path = "preco/empresa/{company_external_id}/precos"
    output_model = models.PrecoClasse
    clean_param = "precos"
    token_bucket = True

    def send(self, company_external_id, params):
        self.path = self.path.format(company_external_id=company_external_id)
        params["size"] = 1000
        return super().send(params=params)


class BuscarPrecosEmpresaSimplificado(BaseRequestCollection):
    method = "GET"
    path = "itinerario/empresa/{company_external_id}/origem-e-destinos"
    output_model = models.PrecoClasseSimplificado
    clean_param = "trechosItinerarioEmpresas"
    token_bucket = True

    def send(self, company_external_id, pagina=1):
        self.path = self.path.format(company_external_id=company_external_id)
        params = {"limite": 1000, "pagina": pagina}
        return super().send(params=params)


class AlterarPreco(BaseRequest):
    method = "POST"
    path = "preco/alterar"


class BuscarUsuarios(BaseRequest):
    method = "GET"
    path = "usuarios/empresa/{company_external_id}"

    def send(self, company_external_id, params):
        self.path = self.path.format(company_external_id=company_external_id)
        return super().send(params=params)


class AlterarUsuario(BaseRequest):
    method = "POST"
    path = "usuarios/empresa/{company_external_id}/alterar"

    def send(self, company_external_id, json):
        self.path = self.path.format(company_external_id=company_external_id)
        return super().send(json=json)


class CadastrarUsuario(BaseRequest):
    method = "POST"
    path = "usuarios/empresa/{company_external_id}/cadastrar"

    def send(self, company_external_id, json):
        self.path = self.path.format(company_external_id=company_external_id)
        return super().send(json=json)


class AlterarMotorista(BaseRequest):
    method = "POST"
    path = "motorista/empresa/{company_external_id}/alterar"

    def send(self, company_external_id, json):
        self.path = self.path.format(company_external_id=company_external_id)
        return super().send(json=json)


class CadastrarMotorista(BaseRequest):
    method = "POST"
    path = "motorista/empresa/{company_external_id}/cadastrar"

    def send(self, company_external_id, json):
        self.path = self.path.format(company_external_id=company_external_id)
        return super().send(json=json)


class RecuperarMotorista(BaseRequest):
    method = "GET"
    path = "motorista/empresa/{company_external_id}/usuario-empresa/{id_usuario_empresa}"

    def send(self, company_external_id, id_usuario_empresa):
        self.path = self.path.format(company_external_id=company_external_id, id_usuario_empresa=id_usuario_empresa)
        return super().send()


class CancelarReservas(BaseRequest):
    method = "POST"
    path = "reserva/cancelar-reservas"


class ListarReservasViagem(BaseRequest):
    method = "GET"
    path = "reserva/itinerario/{itinerario}/empresa/{company_external_id}/reservas"

    def send(self, itinerario, company_external_id):
        self.path = self.path.format(itinerario=itinerario, company_external_id=company_external_id)
        return super().send()


class ListarReservasViagemIds(BaseRequest):
    method = "GET"
    path = "reserva/itinerario/{itinerario}/empresa/{company_external_id}/reservas/ids"

    def send(self, itinerario, company_external_id):
        self.path = self.path.format(itinerario=itinerario, company_external_id=company_external_id)
        return super().send()


class ListarViagensRota(BaseRequestCollection):
    method = "GET"
    path = "itinerario/empresa/{company_external_id}/rota/{id_rota}/itinerarios"
    clean_param = "itinerarios"
    output_model = models.SimplifiedViagensForm
    token_bucket = True

    def __init__(self, login, detailed=False):
        if detailed:
            self.output_model = models.Viagem
        super().__init__(login)

    def send(self, company_external_id, id_rota, json, params):
        self.path = self.path.format(company_external_id=company_external_id, id_rota=id_rota)
        return super().send(json=json, params=params)


class InativarItinerario(BaseRequest):
    method = "POST"
    path = "itinerario/{company_external_id}/inativar/{itinerario}"

    def send(self, company_external_id, itinerario):
        self.path = self.path.format(company_external_id=company_external_id, itinerario=itinerario)
        return super().send()


class InativarItinerariosBulk(BaseRequest):
    method = "POST"
    path = "itinerario/inativar-em-lote"


class IncluirMotoristasItinerario(BaseRequest):
    method = "POST"
    path = "itinerario/{id_itinerario}/empresa/{company_external_id}/incluir-motoristas"

    def send(self, id_itinerario, company_external_id, params):
        self.path = self.path.format(id_itinerario=id_itinerario, company_external_id=company_external_id)
        return super().send(params=params)


class CadastrarGrupo(BaseRequest):
    method = "POST"
    path = "itinerario/cadastrar"


class CadastrarRota(BaseRequest):
    method = "POST"
    path = "rota/cadastrar"


class BuscarRotas(BaseRequestCollection):
    method = "GET"
    path = "rota/empresa/{company_external_id}/rotas?size=500&from=1"
    output_model = models.RotaDetalhada
    clean_param = "rotas"

    def send(self, company_external_id):
        self.path = self.path.format(company_external_id=company_external_id)
        return super().send()


class CriarTrecho(BaseRequest):
    method = "POST"
    path = "trecho/cadastrar"


class ListarTrechos(BaseRequest):
    method = "GET"
    path = "trecho/empresa/{id_empresa}/rota/{id_rota}/trechos"

    def send(self, id_empresa, id_rota):
        self.path = self.path.format(id_empresa=id_empresa, id_rota=id_rota)
        return super().send()


class AlterarPosicao(BaseRequest):
    method = "POST"
    path = "trecho/{trecho_id}/empresa/{empresa_id}/ordem/{ordem}/alterar-ordem"

    def send(self, trecho_id, empresa_id, ordem):
        self.path = self.path.format(trecho_id=trecho_id, empresa_id=empresa_id, ordem=ordem)
        return super().send()

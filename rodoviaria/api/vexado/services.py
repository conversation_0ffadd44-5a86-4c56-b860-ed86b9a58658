import logging
from http import HTTPStatus
from json import JSONDecodeError
from urllib.parse import urljoin

import jwt
import requests
from requests.exceptions import ConnectionError
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from bp.logging import log_integration_request
from commons.memcache import getmc_pymemcache
from commons.sanitization import sanitize_data
from rodoviaria.models import VexadoLogin
from rodoviaria.service.exceptions import RodoviariaConnectionError, RodoviariaUnauthorizedError

rodovlogger = logging.getLogger("rodoviaria")
CACHEKEY_PREFIX = "VEXADO_TOKEN_COMPANY_"


def get_api_token_from_client(login: VexadoLogin, force_renew: bool = False):
    path = "auth/signin"
    method = "POST"
    url = urljoin(login.company.url_base, path)
    company_id = login.company.id
    json = {"username": login.user, "password": login.password}
    mc = getmc_pymemcache()
    cache_key = f"{CACHEKEY_PREFIX}{company_id}"
    data = mc.get(cache_key)

    if not data or force_renew:
        response = _make_request(method, url, json, company_id)
        response_json = response.json()
        token = response_json["tokenServico"]["tokenJwt"]
        token_dict = jwt.decode(token, options={"verify_signature": False})
        timeout = token_dict["exp"] - token_dict["iat"]
        data = {"access_token": token, "new_login": bool(token)}
        mc.set(key=cache_key, value=data, timeout=timeout)

    return data


def listar_empresas(login: VexadoLogin):
    path = "auth/signin"
    method = "POST"
    url = urljoin(login.company.url_base, path)
    company_id = login.company.id
    json = {"username": login.user, "password": login.password}

    response = _make_request(method, url, json, company_id)
    response_json = response.json()
    empresas_list = response_json["empresas"]
    return empresas_list


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def _make_request(method, url, json, company_id=None):
    extra_logs = {"api": "VexadoAPI", "log_type": "request_log"}

    params = None
    headers = {
        "site": "AdminVexado",
    }
    json_sanitized = sanitize_data(json)
    log_msg = f"vexado '{method} {url}' json={json_sanitized} {params=} connection error"
    try:
        response = requests.request(method=method, url=url, json=json, headers=headers, timeout=60)
    except ConnectionError as ex:
        rodovlogger.error(log_msg, extra=extra_logs)
        raise RodoviariaConnectionError(log_msg) from ex

    log_integration_request(
        integration="VexadoAPI",
        company_id=company_id,
        url=url,
        method=method,
        payload=json_sanitized,
        params=params,
        response=response,
    )

    if response.ok:
        return response

    try:
        response_json = response.json()
    except (ValueError, JSONDecodeError):
        response_json = None

    err_msg = _parse_response(response_json, "message")
    if response.status_code == HTTPStatus.UNAUTHORIZED:
        raise RodoviariaUnauthorizedError(err_msg)
    raise RodoviariaConnectionError(log_msg)


def _parse_response(response_json, key: str):
    if isinstance(response_json, dict):
        return response_json.get(key, "")
    return response_json

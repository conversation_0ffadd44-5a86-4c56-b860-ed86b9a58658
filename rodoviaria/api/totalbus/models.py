import hashlib
import json
import logging
from datetime import date, datetime, time, timedelta
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, StrictStr, root_validator, validator

from rodoviaria.models.core import TrechoClasse

logger = logging.getLogger("rodoviaria")


class ServicoTrecho(BaseModel):
    origem: int  # id da origem
    destino: int  # “localidadeConexaoId”
    data: date  # "2020-01-01"
    servico: int
    preco_conexao: Decimal | None = None
    sequencia: int | None = None
    conexao_id: int | None = Field(alias="conexionCtrlId", default=None)
    conexao_grupo_id: int | None = Field(alias="conexionGrupo", default=None)

    class Config:
        allow_population_by_field_name = True

    @classmethod
    def from_trechoclasse(cls, trecho_classe: TrechoClasse):
        origem_id = trecho_classe.origem.id_external
        destino_id = trecho_classe.destino.id_external
        servico_id = trecho_classe.external_id
        provider_data = json.loads(trecho_classe.provider_data)
        data_corrida = provider_data.get("dataCorrida")
        return cls(origem=origem_id, destino=destino_id, servico=servico_id, data=data_corrida)


class RetornaPoltronasModel(BaseModel):
    origem: int  # id da origem
    destino: int  # “localidadeConexaoId”
    data: date  # "2020-01-01"
    servico: int


class ConfirmarVendaForm(BaseModel):
    nome_passageiro: StrictStr = Field(alias="nomePassageiro")
    transacao: StrictStr
    documento_passageiro: StrictStr = Field(alias="documentoPassageiro")
    tipo_documento_passageiro: Optional[StrictStr] = Field(alias="tipoDocumentoPassageiro")
    telefone: Optional[StrictStr] = "11999999999"
    id_forma_pagamento: Optional[int] = Field(alias="idFormaPagamento")
    forma_pagamento: Optional[str] = Field(alias="formaPagamento")
    valor_desconto: Optional[Decimal] = Field(alias="valorDesconto", gt=0)
    categoria_especial: Optional[int] = Field(alias="categoriaId", default=-1)
    data_nascimento: Optional[str] = Field(alias="dataNascimento")

    @validator("telefone")
    def telefone_valido(cls, v):
        if len(str(v)) < 10:
            return "11999999999"
        return v


class CancelarVendaPorBilheteForm(BaseModel):
    numero_bilhete: StrictStr = Field(alias="numeroBilhete")
    validar_multa: bool = Field(alias="validarMulta")


class CancelarVendaForm(BaseModel):
    transacao: StrictStr
    validar_multa: bool = Field(alias="validarMulta")


class ConsultarBilheteForm(BaseModel):
    data: date  # "2020-06-18"
    numero_sistema: StrictStr = Field(alias="numeroSistema")  # "171007"


class BuscarBilhetePorNumeroSistemaForm(BaseModel):
    data: date  # "2020-06-18"
    numero_sistema: StrictStr = Field(alias="numeroSistema")  # "010000930269"


class BuscarItinerarioCorridaForm(BaseModel):
    data: date  # "2020-01-01",
    servico: int  # 1


class ConexaoModel(BaseModel):
    """
    >>> data = {
    ...     "servicoConexao": "888811",
    ...     "localidadeConexao": "CALDAS NOVAS - GO",
    ...     "localidadeConexaoId": 3649,
    ...     "empresa": "RODE ROTAS",
    ...     "empresaId": 2,
    ...     "rutaId": 842,
    ...     "marcaId": 22,
    ...     "dataCorridaConexao": "2023-12-16",
    ...     "dataSaidaConexao": "2023-12-16",
    ...     "primeiroTrechoPoltronasLivres": 21,
    ...     "primeiroTrechoPoltronasTotal": 42,
    ...     "primeiroTrechoClasse": "EXECUTIVO",
    ...     "primeiroTrechoDataCorrida": "16/12/2023",
    ...     "primeiroTrechoDataSaida": "16/12/2023",
    ...     "primeiroTrechoDataChegada": "16/12/2023",
    ...     "primeiroTrechoHoraSaida": "15:30",
    ...     "primeiroTrechoHoraChegada": "19:59",
    ...     "primeiroTrechoPreco": "111.75",
    ...     "primeiroTrechoPrecoOriginal": "154.31",
    ...     "primeiroTrechoServico": "888810",
    ...     "primeiroTrechoLinha": 3628,
    ...     "primeiroTrechoEmpresa": "RODE ROTAS",
    ...     "primeiroTrechoEmpresaId": 2,
    ...     "primeiroTrechoMarca": 22,
    ...     "primeiroTrechoOrigem": 2772,
    ...     "primeiroTrechoOrigemDescricao": "BRASILIA - DF",
    ...     "primeiroTrechoDestino": 3649,
    ...     "primeiroTrechoDestinoDescricao": "CALDAS NOVAS - GO",
    ...     "primeiroTrechoVende": True,
    ...     "primeiroTrechoIsBpe": True,
    ...     "primeiroTrechoSequencia": 1,
    ...     "segundoTrechoPoltronasLivres": 21,
    ...     "segundoTrechoPoltronasTotal": 42,
    ...     "segundoTrechoClasse": "EXECUTIVO",
    ...     "segundoTrechoDataCorrida": "16/12/2023",
    ...     "segundoTrechoDataSaida": "16/12/2023",
    ...     "segundoTrechoDataChegada": "17/12/2023",
    ...     "segundoTrechoHoraSaida": "20:00",
    ...     "segundoTrechoHoraChegada": "09:15",
    ...     "segundoTrechoPreco": "241.03",
    ...     "segundoTrechoPrecoOriginal": "331.93",
    ...     "segundoTrechoServico": "888811",
    ...     "segundoTrechoLinha": 842,
    ...     "segundoTrechoEmpresa": "RODE ROTAS",
    ...     "segundoTrechoEmpresaId": 2,
    ...     "segundoTrechoMarca": 22,
    ...     "segundoTrechoOrigem": 3649,
    ...     "segundoTrechoOrigemDescricao": "CALDAS NOVAS - GO",
    ...     "segundoTrechoDestino": 18697,
    ...     "segundoTrechoDestinoDescricao": "SAO PAULO (TIETE) - SP",
    ...     "segundoTrechoVende": True,
    ...     "segundoTrechoIsBpe": True,
    ...     "segundoTrechoSequencia": 2,
    ...     "terceiroTrechoVende": False,
    ...     "terceiroTrechoIsBpe": False,
    ...     "vende": True,
    ...     "km": 0.0,
    ...     "cnpj": "18449504000159",
    ...     "conexionCtrlId": 54912,
    ...     "conexionGrupo": 1107393,
    ...     "bpe": True,
    ... }
    >>> parsed = ConexaoModel.parse_obj(data)
    >>> assert parsed.servico_conexao == 888811
    >>> assert parsed.bpe == True
    >>> assert parsed.primeiro_trecho_data_corrida == date(2023, 12, 16)
    """

    servico_conexao: int = Field(alias="servicoConexao")
    localidade_conexao: str = Field(alias="localidadeConexao")
    localidade_conexao_id: int = Field(alias="localidadeConexaoId")
    empresa: str
    empresa_id: int = Field(alias="empresaId")
    ruta_id: int = Field(alias="rutaId")
    marca_id: int = Field(alias="marcaId")
    data_corrida_conexao: date = Field(alias="dataCorridaConexao")
    data_saida_conexao: date = Field(alias="dataSaidaConexao")
    primeiro_trecho_poltronas_livres: int = Field(alias="primeiroTrechoPoltronasLivres")
    primeiro_trecho_poltronas_total: int = Field(alias="primeiroTrechoPoltronasTotal")
    primeiro_trecho_classe: str = Field(alias="primeiroTrechoClasse")
    primeiro_trecho_data_corrida: date = Field(alias="primeiroTrechoDataCorrida")
    primeiro_trecho_data_saida: date = Field(alias="primeiroTrechoDataSaida")
    primeiro_trecho_data_chegada: date = Field(alias="primeiroTrechoDataChegada")
    primeiro_trecho_hora_saida: time = Field(alias="primeiroTrechoHoraSaida")
    primeiro_trecho_hora_chegada: time = Field(alias="primeiroTrechoHoraChegada")
    primeiro_trecho_preco: str = Field(alias="primeiroTrechoPreco")
    primeiro_trecho_preco_original: str = Field(alias="primeiroTrechoPrecoOriginal")
    primeiro_trecho_servico: int = Field(alias="primeiroTrechoServico")
    primeiro_trecho_linha: int = Field(alias="primeiroTrechoLinha")
    primeiro_trecho_empresa: str = Field(alias="primeiroTrechoEmpresa")
    primeiro_trecho_empresa_id: int = Field(alias="primeiroTrechoEmpresaId")
    primeiro_trecho_marca: int = Field(alias="primeiroTrechoMarca")
    primeiro_trecho_origem: int = Field(alias="primeiroTrechoOrigem")
    primeiro_trecho_origem_descricao: str = Field(alias="primeiroTrechoOrigemDescricao")
    primeiro_trecho_destino: int = Field(alias="primeiroTrechoDestino")
    primeiro_trecho_destino_descricao: str = Field(alias="primeiroTrechoDestinoDescricao")
    primeiro_trecho_vende: bool = Field(alias="primeiroTrechoVende")
    primeiro_trecho_is_bpe: bool = Field(alias="primeiroTrechoIsBpe")
    primeiro_trecho_sequencia: int = Field(alias="primeiroTrechoSequencia")
    segundo_trecho_poltronas_livres: int = Field(alias="segundoTrechoPoltronasLivres")
    segundo_trecho_poltronas_total: int = Field(alias="segundoTrechoPoltronasTotal")
    segundo_trecho_classe: str = Field(alias="segundoTrechoClasse")
    segundo_trecho_data_corrida: date = Field(alias="segundoTrechoDataCorrida")
    segundo_trecho_data_saida: date = Field(alias="segundoTrechoDataSaida")
    segundo_trecho_data_chegada: date = Field(alias="segundoTrechoDataChegada")
    segundo_trecho_hora_saida: time = Field(alias="segundoTrechoHoraSaida")
    segundo_trecho_hora_chegada: time = Field(alias="segundoTrechoHoraChegada")
    segundo_trecho_preco: str = Field(alias="segundoTrechoPreco")
    segundo_trecho_preco_original: str = Field(alias="segundoTrechoPrecoOriginal")
    segundo_trecho_servico: int = Field(alias="segundoTrechoServico")
    segundo_trecho_linha: int = Field(alias="segundoTrechoLinha")
    segundo_trecho_empresa: str = Field(alias="segundoTrechoEmpresa")
    segundo_trecho_empresa_id: int = Field(alias="segundoTrechoEmpresaId")
    segundo_trecho_marca: int = Field(alias="segundoTrechoMarca")
    segundo_trecho_origem: int = Field(alias="segundoTrechoOrigem")
    segundo_trecho_origem_descricao: str = Field(alias="segundoTrechoOrigemDescricao")
    segundo_trecho_destino: int = Field(alias="segundoTrechoDestino")
    segundo_trecho_destino_descricao: str = Field(alias="segundoTrechoDestinoDescricao")
    segundo_trecho_vende: bool = Field(alias="segundoTrechoVende")
    segundo_trecho_is_bpe: bool = Field(alias="segundoTrechoIsBpe")
    segundo_trecho_sequencia: int = Field(alias="segundoTrechoSequencia")
    terceiro_trecho_vende: bool = Field(alias="terceiroTrechoVende")
    terceiro_trecho_is_bpe: bool = Field(alias="terceiroTrechoIsBpe")
    vende: bool
    km: int
    cnpj: str
    conexion_ctrl_id: int = Field(alias="conexionCtrlId")
    conexion_grupo: int = Field(alias="conexionGrupo")
    bpe: bool

    @validator(
        "primeiro_trecho_data_corrida",
        "primeiro_trecho_data_saida",
        "primeiro_trecho_data_chegada",
        "segundo_trecho_data_corrida",
        "segundo_trecho_data_saida",
        "segundo_trecho_data_chegada",
        pre=True,
    )
    def parse_to_date_obj(cls, v):
        date_obj = datetime.strptime(v, "%d/%m/%Y")
        return date_obj


class BuscarServicoForm(BaseModel):
    data: date  # "2020-01-01",
    origem: int  # 21847
    destino: int  # 19068


class PadraoBuscarServicoInput(BaseModel):
    data_inicial: datetime = Field(alias="dataInicial")  # "2020-01-01 00:00",
    data_final: datetime = Field(alias="dataFinal")  # "2020-01-01 23:59",
    company_external_id: int = Field(alias="empresa")

    class Config:
        allow_population_by_field_name = True

    def dict(self):
        return {
            "dataInicial": self.data_inicial.strftime("%Y-%m-%d %H:%M"),
            "dataFinal": self.data_final.strftime("%Y-%m-%d %H:%M"),
            "empresa": self.company_external_id,
        }


class Trecho(BaseModel):
    trecho_id: int = Field(alias="trechoId")
    data_inicio: str = Field(alias="dataInicio")
    data_final: str = Field(alias="dataFinal")
    desc_origem: Optional[str] = Field(alias="descOrigem", default=None)
    desc_destino: Optional[str] = Field(alias="descDestino", default=None)
    origem: int
    destino: int
    km_real: str = Field(alias="kmReal")
    sequencia: int
    tempo_trecho: str = Field(alias="tempoTrecho")


class PadraoBuscarServicoOutput(BaseModel):
    external_id: int = Field(alias="servicoId")
    empresa_id: int = Field(alias="empresaId")
    data_saida: str = Field(alias="dataSaida")
    data_chegada: str = Field(alias="dataChegada")
    data_hora_servico: datetime = Field(alias="dataHoraServico")
    trechos: list[Trecho]


class CancelaVendaForm(BaseModel):
    cancelar_venda_params: dict


class ComprarForm(BaseModel):
    confirmar_venda_params: dict


class Localidade(BaseModel):
    nome_cidade: str
    external_local_id: str = Field(alias="id")
    external_cidade_id: str = Field(alias="id")
    uf: str
    complemento: str | None = None
    id_cidade_ibge: int | None = None

    @root_validator(pre=True)
    def parse_nested_objects(cls, values):
        values["nome_cidade"] = values["cidade"].partition("-")[0].rstrip().upper()
        if values["uf"] is None:
            values["uf"] = values["cidade"].partition("-")[2].partition("(")[0].lstrip()
        return values

    @property
    def descricao(self):
        desc = f"{self.nome_cidade} - {self.uf}"
        if self.complemento:
            desc += f" - {self.complemento}"
        return desc

    class Config:
        allow_population_by_field_name = True


class Parada(BaseModel):
    local: Localidade = Field(alias="localidade")
    datetime_ida: datetime
    distancia: Optional[Decimal]
    duracao: Optional[int]
    tempo_embarque: Optional[int]

    @root_validator(pre=True)
    def generate_datetime_ida(cls, v):
        data = v.pop("data")
        hora = v.pop("hora")
        datetime_totalbus = datetime.strptime(f"{data} {hora}", "%Y-%m-%d %H:%M")
        datetime_ida = datetime_totalbus
        v["datetime_ida"] = datetime_ida
        return v


class Itinerario(BaseModel):
    """
    {
        "servico": "1010665",
        "data": "2020-05-26",
        "lsParadas": [{
            "localidade": {
                "id": 2063,
                "cidade": "FORTALEZA - CE",
                "uf": "CE"
            },
            "distancia": "69.7",
            "permanencia": "00:00",
            "data": "2020-05-26",
            "hora": "08:30"
        }]
    }
    """

    __root__: List[Parada]

    @property
    def _sigla_duracao(self):
        return "-".join(f"{p.local.external_local_id}.{p.duracao}" for p in self)

    @property
    def hash(self):
        inicio = self[0].local.external_local_id
        fim = self[-1].local.external_local_id
        digest = hashlib.md5(self._sigla_duracao.encode()).hexdigest()  # noqa: S324
        return f"{inicio}{fim}{digest}"

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    @validator("__root__")
    def _attach_tempos(cls, v):
        if not v:
            return v

        iti_first = v[0]
        iti_first.duracao = 0
        iti_first.tempo_embarque = 0

        iti_last = v[-1]
        iti_last.tempo_embarque = 0
        for iti_anterior, iti_atual in zip(v, v[1:]):
            # é possível que a API tenha a hora errada (exemplo: ponto A: 28/02 18:30, ponto B: 28/02 18:00).
            # Caso a data seja a mesma, pega a diferenca absoluta das datas
            if iti_atual.datetime_ida.date() == iti_anterior.datetime_ida.date():
                duracao = abs(iti_atual.datetime_ida - iti_anterior.datetime_ida)
            else:
                duracao = iti_atual.datetime_ida - iti_anterior.datetime_ida

            # é possível que a API tenha a data errada (exemplo: ponto A: 28/02, ponto B: 22/02). Caso a data seja
            # inferior ao dia do checkpoint anterior, desconsidera a data
            if duracao.days < 0:
                duracao = timedelta(seconds=duracao.seconds)

            if iti_atual == v[-1]:
                # Não tem tempo de embarque no último checkpoint.
                tempo_embarque = timedelta(minutes=0)
            elif duracao <= timedelta(minutes=10):
                # Tempo de embarque em paradas rápidas é o minimo de 1 min.
                tempo_embarque = timedelta(minutes=1)
            else:
                tempo_embarque = timedelta(minutes=10)

            iti_atual.duracao = (duracao - tempo_embarque).total_seconds()
            iti_atual.tempo_embarque = tempo_embarque.total_seconds()

        # joga as distancias um checkpoint pra frente
        # totalbus coloca distancia até o proximo ponto, mas nos usamos distancia desde o ponto anterior
        reversed_v = v[::-1]
        for iti_anterior, iti_atual in zip(reversed_v, reversed_v[1:]):
            iti_anterior.distancia = iti_atual.distancia or abs(
                timedelta(seconds=iti_anterior.duracao).seconds // 60
            )  # estima por 60km/h
            logger.warning(
                "distancia_nula: totalbus._attach_tempos",
                extra={"duracao": iti_anterior.duracao, "distancia": iti_anterior.distancia},
            )
        v[0].distancia = 0

        return v


class FormaPagamento(BaseModel):
    """
    >>> data = {"id": 2, "descricao": "CRÉDITO", "tipoPago": 11}
    >>> data_obj = FormaPagamento(**data)
    >>> assert data_obj.tipo_pago == 11
    """

    id: int
    descricao: str
    tipo_pago: Optional[int] = Field(alias="tipoPago")


class ServicoDetalhado(BaseModel):
    """
    >>> data = {
    ...     "servicoId": 1020,
    ...     "dataHoraServico": "2023-04-11 18:30:00",
    ... }
    >>> data_obj = ServicoDetalhado(**data)
    >>> assert data_obj.external_id == 1020
    >>> assert data_obj.datetime_ida == datetime(2023, 4, 11, 18, 30)
    """

    external_id: int = Field(alias="servicoId")
    datetime_ida: datetime = Field(alias="dataHoraServico")


class LocalEmbarque(BaseModel):
    id: int
    description: str = Field(alias="descripcion")


class ServicoTrechoClasse(BaseModel):
    """
    >>> data = {
    ...     "numservico": 1,
    ...     "horaServico": "06:10:00",
    ...     "origem": {
    ...         "id": 21918,
    ...         "descripcion": "DRACENA - SP"
    ...     },
    ...     "destino": {
    ...         "id": 21915,
    ...         "descripcion": "PACAEMBU - SP"
    ...     }
    ... }
    >>> servico = ServicoTrechoClasse(**data)
    >>> assert servico.rota_id == 1
    >>> assert servico.origem.id == 21918
    """

    external_id: int = Field(alias="numservico")
    hora_servico: None | time = Field(alias="horaServico")
    origem: None | LocalEmbarque
    destino: None | LocalEmbarque

    @property
    def rota_id(self):
        return self.external_id


class ExternalCompany(BaseModel):
    """
    >>> data = {
    ...     "id": 1,
    ...     "nome": "businho",
    ...     "cnpj": "12.345.678/9000-00"
    ... }
    >>> data_obj = ExternalCompany(**data)
    >>> assert data_obj.external_id == 1
    """

    external_id: int = Field(alias="id")
    nome: str
    cnpj: str | None


class LocalTotalbus(BaseModel):
    """
    >>> data = {
    ...         "id": 2063,
    ...         "cidade": "FORTALEZA - CE",
    ...         "sigla": "FOR",
    ...         "uf": "CE",
    ... }
    >>> data_obj = LocalTotalbus(**data)
    >>> assert data_obj.id == 2063
    """

    id: int
    cidade: str
    sigla: str
    uf: str


class Preco(BaseModel):
    tarifa: Decimal
    outros: Decimal
    pedagio: Decimal
    seguro: Decimal
    preco: Decimal
    tarifa_com_pricing: Decimal = Field(alias="tarifaComPricing")
    taxa_embarque: Decimal = Field(alias="taxaEmbarque")
    seguro_w2_i: Decimal = Field(alias="seguroW2I")


class SeguroOpcional(BaseModel):
    id: int | None = None
    km: int | None = None
    valor: Decimal | None = None


class BloquearPoltronaRequest(BaseModel):
    """
    >>> from datetime import date, datetime
    >>> data = {
    ...     "origem": {
    ...         "id": 2063,
    ...         "cidade": "FORTALEZA - CE",
    ...         "sigla": "FOR",
    ...         "uf": "CE",
    ...     },
    ...     "destino": {
    ...         "id": 2201,
    ...         "cidade": "SOBRAL - CE",
    ...         "sigla": "SOL",
    ...         "uf": "CE",
    ...     },
    ...     "data": "2020-11-17",
    ...     "servico": "1010485",
    ...     "assento": "27",
    ...     "duracao": 10,
    ...     "transacao": "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA",
    ...     "preco": {
    ...         "tarifa": "560.84",
    ...         "outros": "0.00",
    ...         "pedagio": "20.86",
    ...         "seguro": "0.00",
    ...         "preco": "560.84",
    ...         "tarifaComPricing": "560.84",
    ...         "taxaEmbarque": "7.60",
    ...         "seguroW2I": "0.00",
    ...     },
    ...     "rutaid": "1445",
    ...     "seguroOpcional": {"id": 41, "km": 225, "valor": 10.00},
    ...     "numOperacion": "010023561934",
    ...     "localizador": "010023561934",
    ...     "boletoId": 10000017060472,
    ...     "empresaCorridaId": 2,
    ...     "dataSaida": "2023-12-28 22:45",
    ...     "dataChegada": "2023-12-29 00:45",
    ...     "dataCorrida": "2023-12-28",
    ...     "classeServicoId": 2,
    ... }
    >>> data_obj = BloquearPoltronaRequest(**data)
    >>> assert data_obj.rota_id == 1445
    >>> assert data_obj.preco.tarifa == Decimal("560.84")
    >>> assert data_obj.data_corrida == date(2023, 12, 28)
    >>> assert data_obj.data_saida == datetime(2023, 12, 28, 22, 45)
    """

    origem: LocalTotalbus
    destino: LocalTotalbus
    data: date
    servico: str
    assento: str
    duracao: int
    transacao: str
    preco: Preco
    rota_id: int = Field(alias="rutaid")
    seguro_opcional: SeguroOpcional | None = Field(alias="seguroOpcional", default=None)
    num_operacion: str = Field(alias="numOperacion")
    localizador: str
    boleto_id: int = Field(alias="boletoId")
    empresa_corrida_id: int = Field(alias="empresaCorridaId")
    data_saida: datetime = Field(alias="dataSaida")
    data_chegada: datetime = Field(alias="dataChegada")
    data_corrida: date = Field(alias="dataCorrida")
    classe_servico_id: int = Field(alias="classeServicoId")


class BloquearPoltronaPrecoOut(BaseModel):
    """
    >>> data =  {
    ...     'tarifaComPricing': "10.00",
    ...     'outros': 5.00,
    ...     'pedagio': Decimal('1.00'),
    ...     'seguro': Decimal('100.00'),
    ...     'taxaEmbarque': Decimal('111.00')
    ... }
    >>> data_obj = BloquearPoltronaPrecoOut(**data)
    >>> assert data_obj.tarifa_com_pricing == Decimal("10.00")
    >>> assert data_obj.outras_tarifas == Decimal("5.00")
    >>> assert data_obj.taxa_embarque == Decimal("111.00")
    """

    tarifa_com_pricing: Decimal = Field(alias="tarifaComPricing")
    outras_tarifas: Decimal = Field(alias="outros")
    pedagio: Decimal
    seguro: Decimal
    taxa_embarque: Decimal = Field(alias="taxaEmbarque")

    class Config:
        allow_population_by_field_name = True


class BloquearPoltronaOut(BaseModel):
    """
    >>> data = {
    ...     'transacao': 'compra',
    ...     'preco': {'tarifaComPricing': Decimal('10.00'),
    ...         'outros': Decimal('5.00'),
    ...         'pedagio': Decimal('1.00'),
    ...         'seguro': Decimal('100.00'),
    ...         'taxaEmbarque': Decimal('10101.00')
    ...     }
    ... }
    >>> data_obj = BloquearPoltronaOut(**data)
    >>> assert data_obj.transacao == "compra"
    """

    # TODO: transacao ser Enum
    transacao: StrictStr
    preco: BloquearPoltronaPrecoOut

    class Config:
        allow_population_by_field_name = True


class BuscarServicosPeriodoIn(BaseModel):
    """
    >>> import json
    >>> data = dict(id_empresa_external=2, start_date="2023-10-11 09:00", end_date="2023-10-13 23:59")
    >>> data_obj = BuscarServicosPeriodoIn(**data)
    >>> assert data_obj.id_empresa_external == 2
    >>> assert data_obj.start_date == datetime(2023, 10, 11, 9, 00)
    >>> assert data_obj.end_date == datetime(2023, 10, 13, 23, 59)
    """

    id_empresa_external: None | int
    start_date: datetime
    end_date: datetime


class BuscaOrigensDestinosModel(BaseModel):
    """
    >>> data = [{
    ...     "origem": {
    ...         "id": 5557,
    ...         "cidade": "FRUTAL - MG",
    ...         "sigla": "FRU",
    ...         "uf": "MG",
    ...         "empresas": "42"
    ...     },
    ...     "destinos": [
    ...         {
    ...             "id": 19118,
    ...             "cidade": "BARRETOS - SP",
    ...             "sigla": "BRS",
    ...             "uf": "SP",
    ...             "empresas": "42"
    ...         },
    ...         {
    ...             "id": 19123,
    ...             "cidade": "COLOMBIA - SP",
    ...             "sigla": "CLM",
    ...             "uf": "SP",
    ...             "empresas": "42"
    ...         },
    ...         {
    ...             "id": 7550,
    ...             "cidade": "PLANURA - MG",
    ...             "sigla": "PLU",
    ...             "uf": "MG",
    ...             "empresas": "42"
    ...         }
    ...     ]
    ... },
    ... {
    ...     "origem": {
    ...         "id": 7550,
    ...         "cidade": "PLANURA - MG",
    ...         "sigla": "PLU",
    ...         "uf": "MG",
    ...         "empresas": "42"
    ...     },
    ...     "destinos": [
    ...         {
    ...             "id": 19118,
    ...             "cidade": "BARRETOS - SP",
    ...             "sigla": "BRS",
    ...             "uf": "SP",
    ...             "empresas": "42"
    ...         },
    ...         {
    ...             "id": 19123,
    ...             "cidade": "COLOMBIA - SP",
    ...             "sigla": "CLM",
    ...             "uf": "SP",
    ...             "empresas": "42"
    ...         },
    ...         {
    ...             "id": 5557,
    ...             "cidade": "FRUTAL - MG",
    ...             "sigla": "FRU",
    ...             "uf": "MG",
    ...             "empresas": "42"
    ...         }
    ...     ]
    ... }]
    >>> from pydantic import parse_obj_as
    >>> form = parse_obj_as(list[BuscaOrigensDestinosModel], data)
    >>> assert form[0].origem.external_local_id == "5557"
    """

    origem: Localidade
    destinos: list[Localidade]


class InfosCacheaveisBloqueioPoltrona(BaseModel):
    """
    >>> from decimal import Decimal
    >>> data = dict(
    ... transacao="bla",
    ... preco=Decimal("10.00"),
    ... preco_conexao=Decimal("3.00"),
    ... origem="2",
    ... destino="3",
    ... is_conexao=False
    ... )
    >>> model = InfosCacheaveisBloqueioPoltrona.parse_obj(data)
    >>> assert model.preco == Decimal("10.00")
    >>> assert model.preco - model.preco_conexao == Decimal("7.00")
    """

    transacao: str
    preco: Decimal
    preco_conexao: Decimal | None = None
    origem: Optional[StrictStr]
    destino: Optional[StrictStr]
    data_hora_partida: Optional[StrictStr]
    is_conexao: bool = False

    @validator("is_conexao")
    def preco_conexao_se_conexao(cls, v, values, **kwargs):
        if v is True and values["preco_conexao"] is None:
            raise ValueError(
                f"Informe o preco da conexao para o trecho origem={values['origem']} destino={values['destino']}"
            )
        return v


class MapaPoltronaItem(BaseModel):
    x: int
    y: int
    disponivel: bool
    numero: str
    categoria_reservada_id: str = Field(alias="categoriaReservadaId", default="-1")


class OrgaoConcedenteItem(BaseModel):
    orgao_concedente_id: int = Field(alias="orgaoConcedenteId")
    desc_orgao: str = Field(alias="descOrgao")
    idade_minima_idoso: int = Field(alias="idadeMinimaIdoso")
    idade_maxima_crianca: int = Field(alias="idadeMaximaCrianca")


class Desconto(BaseModel):
    importe_tarifa: float = Field(alias="importeTarifa")
    importe_pedagio: float = Field(alias="importePedagio")
    importe_seguro: int = Field(alias="importeSeguro")
    importe_taxa_embarque: float = Field(alias="importeTaxaEmbarque")
    importe_outros: int = Field(alias="importeOutros")
    importe_tarifa_seguro: float = Field(alias="importeTarifaSeguro")
    importe_tarifa_taxa: float = Field(alias="importeTarifaTaxa")
    importe_tarifa_pedagio: float = Field(alias="importeTarifaPedagio")
    importe_tarifa_outros: float = Field(alias="importeTarifaOutros")
    importe_tarifa_total: float = Field(alias="importeTarifaTotal")
    importe_desconto: int = Field(alias="importeDesconto")
    pricing_aplicado: str = Field(alias="pricingAplicado")
    cota_obrigatoria: bool = Field(alias="cotaObrigatoria")
    quantidade_cota: int = Field(alias="quantidadeCota")
    assentos_reservados: str = Field(alias="assentosReservados")
    exigir_nome: bool = Field(alias="exigirNome")
    exigir_documento: bool = Field(alias="exigirDocumento")
    exigir_telefone: bool = Field(alias="exigirTelefone")
    exigir_data_nascimento: bool = Field(alias="exigirDataNascimento")
    exigir_endereco: bool = Field(alias="exigirEndereco")
    exigir_email: bool = Field(alias="exigirEmail")
    cliente_pcd: bool = Field(alias="clientePcd")
    nao_permite_venda_mesmo_doc_viagem: bool = Field(..., alias="naoPermiteVendaMesmoDocViagem")
    nao_permite_venda_duas_gratuidades: bool = Field(..., alias="naoPermiteVendaDuasGratuidades")
    nao_aplica_tarifa_minima: bool = Field(alias="naoAplicaTarifaMinima")


class AssentoCategoriaEspecial(BaseModel):
    categoria_id: str = Field(alias="categoriaId")
    desccategoria: str
    disponibilidade_cota: int = Field(alias="disponibilidadeCota")
    gratuidade_crianca: bool = Field(alias="gratuidadeCrianca")
    estudante: bool
    idoso: bool
    # orgao_concedente: List[OrgaoConcedenteItem] = Field(alias='orgaoConcedente')
    # desconto: Desconto
    vende_api: bool = Field(alias="vendeApi")
    seguro_opcional_km: int = Field(alias="seguroOpcionalKm")
    seguro_opcional_valor: int = Field(alias="seguroOpcionalValor")

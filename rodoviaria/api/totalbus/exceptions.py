from rodoviaria.service.exceptions import RodoviariaConnectionError, RodoviariaException


class TempoCancelamentoExcedido(RodoviariaException):
    pass


class TotalbusCancelamentoConnectionError(RodoviariaConnectionError):
    pass


class TotalbusItinerarioNotFound(RodoviariaException):
    message = "Itinerário não encontrado"

    def __init__(self, message=None):
        if message:
            self.message = message

    def __str__(self):
        return self.message


class TotalbusCategoriaNotFound(RodoviariaException):
    message = "Categoria não encontrada"

    def __init__(self, message=None):
        if message:
            self.message = message

    def __str__(self):
        return self.message


class TotalbusEmptyResponseError(RodoviariaException):
    message = "Aconteceu um erro genérico."

    def __init__(self, message=None):
        if message:
            self.message = message

    def __str__(self):
        return self.message

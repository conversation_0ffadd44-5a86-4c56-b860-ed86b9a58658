from requests.auth import HTTPBasicAuth

from rodoviaria.models import Company
from rodoviaria.models.totalbus import TotalbusLogin
from rodoviaria.service.exceptions import RodoviariaLoginNotFoundException


class TotalbusAuth(HTTPBasicAuth):
    def __init__(self, tenant_id, username, password):
        super().__init__(username, password)
        self.tenant_id = tenant_id

    @classmethod
    def from_company(cls, company: Company):
        try:
            login: TotalbusLogin = TotalbusLogin.objects.get(company=company)
        except TotalbusLogin.DoesNotExist as ex:
            raise RodoviariaLoginNotFoundException(
                integracao="totalbus",
                company_pk=company,
                modelo_venda=company.modelo_venda,
            ) from ex
        return cls(tenant_id=login.tenant_id, username=login.user, password=login.password)

    @classmethod
    def from_client(cls, client: TotalbusLogin):
        return cls(tenant_id=client.tenant_id, username=client.user, password=client.password)

    def __call__(self, r):
        super().__call__(r)
        r.headers["X-tenant-id"] = self.tenant_id
        return r

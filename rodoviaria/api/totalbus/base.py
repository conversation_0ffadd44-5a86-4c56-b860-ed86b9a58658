import logging
import re
from http import HTTPStatus

from constance import config as constance
from requests.adapters import HTT<PERSON><PERSON>pter, Retry

from commons.utils import str_contains
from rodoviaria.api.executors import Request, RequestConfig, Response
from rodoviaria.api.totalbus.auth import TotalbusAuth
from rodoviaria.forms.staff_forms import TotalbusNoCompanyLogin
from rodoviaria.models import TotalbusLogin
from rodoviaria.service.exceptions import (
    RodoviariaException,
    RodoviariaTimeoutException,
    RodoviariaTooManyRequestsError,
    RodoviariaUnauthorizedError,
)

rodovlogger = logging.getLogger("rodoviaria")


class BaseRequestConfig(RequestConfig):
    # Prefix for logging and error messages
    MESSAGE_PREFIX = "totalbus"

    # See rodoviaria/api/executors/middlewares.py:LoggingMiddleware
    integration_name = "TotalbusAPI"

    timeout = constance.TOTALBUS_TIMEOUT_DEFAULT

    http_adapter = HTTPAdapter(
        max_retries=Retry(
            total=3,
            backoff_factor=1,
            raise_on_status=False,
            status_forcelist=[401, 406, 408],
        ),
    )

    def __init__(self, login: TotalbusLogin | TotalbusNoCompanyLogin):
        self.login = login
        self.auth = TotalbusAuth.from_client(self.login)  # TODO: memoize this call?

    def endpoint(self, endpoint: str) -> str:
        return f"{self.login.company.url_base}/{endpoint}"

    def preprocess_request(self, request: Request) -> Request:
        self.auth(request)
        return request

    def process_response(self, response: Response):
        if response.ok:
            return response

        generic_error_message = (
            f"{self.MESSAGE_PREFIX} '{response.request.method} {response.request.url}' "
            f"json={response.request.json} params={response.request.params} "
            f"status_code={response.status_code}"
        )

        try:
            payload = response.json()
            error_message = payload.get("message", generic_error_message)
        except ValueError:
            error_message = generic_error_message

        status_code = response.status_code
        # status_code =_response_status_code(resposne)

        if (
            status_code == HTTPStatus.UNAUTHORIZED
            or status_code == HTTPStatus.FORBIDDEN
            or str_contains(error_message, "Ponto de Venda/Usuário/Estação devem ser configurados")
        ):
            raise RodoviariaUnauthorizedError(error_message)  # RodoviariaBaseException
        elif status_code == HTTPStatus.REQUEST_TIMEOUT or (
            status_code == HTTPStatus.BAD_REQUEST and "408 Request Time-out" in error_message
        ):
            rodovlogger.info("(novo) response_status=408 -> RodoviariaTimeoutException")
            raise RodoviariaTimeoutException(generic_error_message)  # RodoviariaBaseException
        elif status_code == HTTPStatus.TOO_MANY_REQUESTS:
            raise RodoviariaTooManyRequestsError(generic_error_message)  # RodoviariaBaseException

        raise RodoviariaException(error_message)


def _response_status_code(response: Response):
    """A API da Totalbus para vários casos retorna o status HTTP 400,
    mas o erro de verdade está no campo error no corpo da resposta."""
    # TODO: não usado para manter compatibilidade com o código antigo, mas deveriamos usar?
    try:
        payload = response.json()
        error = payload.get("error", "")
        if match := re.match(r"(\d+).*", error):
            status_code = match.group(1)
            return int(status_code)
    except ValueError:
        ...
    return response.status_code

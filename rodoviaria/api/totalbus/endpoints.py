import logging
from enum import StrEnum
from http import <PERSON><PERSON>PMethod, HTTPStatus
from json import JSONDecodeError

from constance import config as constance

from commons.utils import str_contains
from rodoviaria.api.executors import Response
from rodoviaria.api.executors.middlewares.token_bucket_middleware import token_bucket_middleware
from rodoviaria.api.totalbus.base import BaseRequestConfig
from rodoviaria.api.totalbus.exceptions import (
    TempoCancelamentoExcedido,
    TotalbusCancelamentoConnectionError,
    TotalbusCategoriaNotFound,
    TotalbusEmptyResponseError,
    TotalbusItinerarioNotFound,
)
from rodoviaria.service.exceptions import (
    CampoObrigatorioException,
    PassengerNotRegistered,
    PassengerTicketAlreadyPrintedException,
    PoltronaExpirada,
    PoltronaIndisponivel,
    PoltronaJaSelecionadaException,
    PoltronaTrocadaException,
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaTrechoBloqueadoException,
    RodoviariaTrechoclasseFactoryException,
    RodoviariaTrechoNotFoundException,
)

logger = logging.getLogger(__name__)


class TotalbusUrlPath(StrEnum):
    # Os paths aqui são uma referência. Conforme os novos endpoints forem implementados, remover dessa lista.
    LOGIN = "autenticacao/login"


class BuscarItinerarioCorridaRequestConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("itinerario/buscarItinerarioCorrida")

    def process_response(self, response: Response):
        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            if "CI-02|ITINERARIO NAO LOCALIZADO" in payload["message"]:
                raise TotalbusItinerarioNotFound
        return super().process_response(response)


class BuscarTodosServicosDisponiveisConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    timeout = constance.TOTALBUS_TIMEOUT_BUSCAR_TODOS_SERVICOS

    @property
    def url(self):
        return self.endpoint("catalogo/buscarServicos")


class BuscaOrigensConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("localidade/buscaOrigem")


class BuscarDestinosConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    def __init__(self, login, origem_external_id: int):
        self.origem_external_id = origem_external_id
        return super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"localidade/buscaDestino/{self.origem_external_id}")


class BuscarOrigensDestinosConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    def __init__(self, login, company_external_id: int):
        self.company_external_id = company_external_id
        return super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"localidade/buscarOrigenDestino/{self.company_external_id}")


class ConsultarEmpresasConfig(BaseRequestConfig):
    method = HTTPMethod.GET
    path = "catalogo/consultarEmpresas"

    @property
    def url(self):
        return self.endpoint("catalogo/consultarEmpresas")


class BuscarFormasPagamentoConfig(BaseRequestConfig):
    method = HTTPMethod.GET
    path = "catalogo/buscarFormasPagamento"

    @property
    def url(self):
        return self.endpoint("catalogo/buscarFormasPagamento")


class BuscarServicosPeriodoConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("integracao/padrao/buscarServicos")

    timeout = constance.TOTALBUS_TIMEOUT_BUSCAR_SERVICOS

    def process_response(self, response: Response):
        if response.ok:
            return super().process_response(response)
        # esse endpoint pode retornar um json vazio
        error_message = None
        try:
            json_response = response.json()
            if json_response:
                error_message = json_response.get("message")
        except (ValueError, JSONDecodeError):
            pass
        if response.status_code == HTTPStatus.BAD_REQUEST:
            if error_message is None:
                raise TotalbusEmptyResponseError("BadRequest sem mensagem de erro. Será realizado retry.")
            if "Data Inicial inválida" in error_message:
                raise RodoviariaException(
                    "Data inválida. Verifique se a data informada está no padrão YYYY-MM-DD HH:mm"
                )
            if "Unable to acquire JDBC Connection" in error_message:
                raise RodoviariaConnectionError(error_message)
        return super().process_response(response)


class BuscarCorridasRequestConfig(BaseRequestConfig):
    method = "POST"
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self) -> str:
        return self.endpoint("consultacorrida/buscaCorrida")

    def process_response(self, response: Response):
        if response.status_code == HTTPStatus.BAD_REQUEST:
            payload = response.json()
            if "Trecho não disponível" in payload["message"] or "TRECHO NAO LOCALIZADO" in payload["message"]:
                raise RodoviariaTrechoNotFoundException

        return super().process_response(response)


class BloquearPoltronaVendaNormalConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("bloqueiopoltrona/bloquearPoltrona")

    def process_response(self, response):
        if response.ok:
            return super().process_response(response)
        error_message = response.json().get("message", None)
        if (
            str_contains(error_message, "Bloqueio não concluído")
            or str_contains(error_message, "já não tem disponibilidade")
            or str_contains(error_message, "Categoria indisponível")
            or str_contains(error_message, "Trecho bloqueado para venda")
        ):
            raise RodoviariaTrechoBloqueadoException
        elif str_contains(error_message, "A poltrona já foi selecionada"):
            raise PoltronaJaSelecionadaException(error_message)
        elif str_contains(error_message, "asientoNoExiste"):
            raise PoltronaIndisponivel

        return super().process_response(response)


class DesbloquearPoltronasConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("desbloqueiopoltrona/desbloquearPoltrona")


class ConfirmarVendaRequestConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    timeout = constance.TOTALBUS_TIMEOUT_CONFIRMAR_VENDA
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("confirmavenda/confirmarVenda")

    def process_response(self, response: Response):
        if response.ok:
            return super().process_response(response)

        error_message = response.json()["message"]
        if str_contains(error_message, "Não foi possível carregar a categoria da corrida"):
            raise TotalbusCategoriaNotFound(error_message)
        elif str_contains(error_message, "Categoria indisponível para esse serviço"):
            raise RodoviariaException(error_message)
        elif str_contains(error_message, "Data nascimento do passageiro é obrigatório"):
            raise CampoObrigatorioException(error_message)
        elif str_contains(error_message, "Error.Bloqueo.Asiento.Ya.Cedido"):
            raise PoltronaIndisponivel
        elif str_contains(error_message, "Boleto já confirmado"):
            raise PoltronaIndisponivel
        elif str_contains(error_message, "Boleto não encontrado"):
            raise PoltronaExpirada
        elif str_contains(error_message, "boletoLiberado"):
            raise PoltronaExpirada

        return super().process_response(response)


class RetornaPoltronasConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("consultaonibus/buscaOnibus")

    def process_response(self, response: Response):
        if response.ok:
            return super().process_response(response)
        error_message = response.json()["message"]
        if str_contains(error_message, "Servico não localizado"):
            raise RodoviariaTrechoclasseFactoryException(trechoclasse_id=None)
        status_code = response.status_code
        if status_code == HTTPStatus.NOT_ACCEPTABLE:
            raise RodoviariaConnectionError(error_message)
        return super().process_response(response)


class CancelarVendaConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return self.endpoint("cancelavenda/cancelarVenda")

    def process_response(self, response: Response):
        try:
            return super().process_response(response)
        except RodoviariaException as ex:
            error_message = ex.message
            mensagens_tempo_excedido = {
                "O tempo permitido para cancelamento de bilhetes desta agência já foi ultrapassado.",
                "Cancelamento não permitido para pois o Limite para Cancelamento excedeu",
            }
            if str_contains(error_message, "O bilhete já foi cancelado.") or str_contains(
                error_message, "Bilhete não encontrado"
            ):
                raise PassengerNotRegistered(error_message) from ex
            elif str_contains(
                error_message, "Não é possível cancelar/devolver um bilhete que possui troca de poltrona"
            ):
                raise PoltronaTrocadaException(error_message) from ex
            elif str_contains(error_message, "Cancelamento não permitido. Considerado embarcado."):
                raise PassengerTicketAlreadyPrintedException(error_message) from ex
            elif error_message in mensagens_tempo_excedido:
                raise TempoCancelamentoExcedido(error_message) from ex
            raise TotalbusCancelamentoConnectionError(error_message) from ex


class CancelarVendaPorBilheteRequestConfig(CancelarVendaConfig):
    @property
    def url(self):
        return self.endpoint("cancelavenda/cancelarVendaPorBoletoId")


class BuscarBilheteRequestConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("consultabilhete/consultarBilhete")

    def process_response(self, response: Response):
        if response.ok:
            return super().process_response(response)

        error_message = response.json()["message"]
        if str_contains(error_message, "Não há bilhete para a consulta enviada"):
            raise RodoviariaException(message=error_message)

        return super().process_response(response)


class ConsultarCategoriaCorridaRequestConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("categoria/consultarCategoriasCorrida")

    def process_response(self, response: Response):
        if response.ok:
            return super().process_response(response)
        error_message = None
        json_response = response.json()
        if json_response:
            error_message = json_response.get("message")
        if (
            error_message
            and response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
            and ("Unable to acquire JDBC Connection" in error_message or "Cannot open connection" in error_message)
        ):
            raise RodoviariaConnectionError(error_message)
        return super().process_response(response)


class ConsultarCategoriaRequestConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("categoria/consultarCategorias")

    def process_response(self, response: Response):
        if response.ok:
            return super().process_response(response)
        return response

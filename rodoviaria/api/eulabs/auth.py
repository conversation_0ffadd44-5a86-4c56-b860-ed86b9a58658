from requests.auth import AuthBase

from rodoviaria.models import Company, EulabsLogin
from rodoviaria.service.exceptions import RodoviariaLoginNotFoundException


class EulabsAuth(AuthBase):
    def __init__(self, api_id: str, api_key: str):
        self.api_id = api_id
        self.api_key = api_key

    @classmethod
    def from_company(cls, company: Company):
        try:
            login: EulabsLogin = EulabsLogin.objects.get(company=company)
        except EulabsLogin.DoesNotExist as ex:
            raise RodoviariaLoginNotFoundException(
                integracao="eucatur",
                company_pk=company.id,
                modelo_venda=company.modelo_venda,
            ) from ex
        return cls(api_id=login.api_id, api_key=login.api_key)

    @classmethod
    def from_client(cls, client: EulabsLogin):
        return cls(api_id=client.api_id, api_key=client.api_key)

    def __call__(self, request):
        request.headers["X-Eucatur-Api-Id"] = self.api_id
        request.headers["X-Eucatur-Api-Key"] = self.api_key

        return request

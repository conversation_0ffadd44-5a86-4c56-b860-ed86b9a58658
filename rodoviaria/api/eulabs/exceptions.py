from rodoviaria.service.exceptions import APIError, PoltronaIndisponivel, RodoviariaException


class EulabsPoltronaIndisponivel(PoltronaIndisponivel):
    pass


class EulabsReservasNaoEncontradasException(RodoviariaException):
    def __init__(self):
        super().__init__("Reservas não encontradas na API")


class EulabsAPIError(APIError):
    status_code = None

    def __init__(self, status_code, message=None):
        super().__init__(message)
        self.status_code = status_code


class EulabsNaoPodeCancelar(EulabsAPIError):
    def __init__(self, status_code, message=None):
        self.message = message
        super().__init__(status_code, message)

    def __str__(self):
        return f"status_code={self.status_code} response_json={self.message}"


class EulabsVendaNaoEncontrada(EulabsAPIError):
    def __init__(self, status_code, message=None):
        super().__init__(status_code, message)

    def __str__(self):
        return f"status_code={self.status_code} response_json={self.message}"


class EulabsSeatingMapError(EulabsAPIError):
    message = "Falha ao obter o mapa de poltronas. Realizando nova tentativa"

    def __init__(self, status_code, message=None):
        if message:
            self.message = message
        super().__init__(status_code, message)

    def __str__(self):
        return f"status_code={self.status_code} response_json={self.message}"


class EulabsTravelKeyNotFound(Exception):
    pass

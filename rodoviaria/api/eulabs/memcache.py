import logging

from commons.memcache import getmc_pymemcache

logger = logging.getLogger("rodoviaria")


class EulabsMC:
    prefix = "EULABS"
    trecho_cache_prefix = "TRECHO"
    poltrona_cache_prefix = "POLTRONA"
    default_timeout = 60 * 15

    @property
    def mc(self):
        return getmc_pymemcache()

    def __init__(self, company_id):
        self.prefix += f"_COMPANY_{company_id}"

    def _trecho_classe_cache_key(self, trecho_classe_id):
        return f"{self.prefix}_{self.trecho_cache_prefix}_{trecho_classe_id}"

    def set_travel_key_cache(self, trecho_classe_id, travel_key):
        return self.mc.add(
            key=self._trecho_classe_cache_key(trecho_classe_id),
            value=travel_key,
            timeout=self.default_timeout,
        )

    def get_travel_key_cache(self, trecho_classe_id):
        return self.mc.get(key=self._trecho_classe_cache_key(trecho_classe_id))

    def _poltrona_cache_key(self, trecho_classe_id, poltrona):
        return f"{self.prefix}_{self.trecho_cache_prefix}_{trecho_classe_id}_{self.poltrona_cache_prefix}_{poltrona}"

    def set_poltrona_key_cache(self, trecho_classe_id, poltrona, seat_key):
        key = self._poltrona_cache_key(trecho_classe_id, poltrona)
        logger.info("[Eulabs Cache] ADD key=%s with value=%s and timeout=%s", key, seat_key, self.default_timeout)
        return self.mc.add(
            key=key,
            value=seat_key,
            timeout=self.default_timeout,
        )

    def get_poltrona_key_cache(self, trecho_classe_id, poltrona):
        key = self._poltrona_cache_key(trecho_classe_id, poltrona)
        value = self.mc.get(key=key)
        logger.info("[Eulabs Cache] GET key=%s returned value=%s", key, value)
        return value

    def delete_poltrona_key_cache(self, trecho_classe_id, poltrona):
        key = self._poltrona_cache_key(trecho_classe_id, poltrona)
        logger.info("[Eulabs Cache] DELETE key=%s", key)
        return self.mc.delete(key=key)

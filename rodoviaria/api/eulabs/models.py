import hashlib
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, StrictStr, root_validator, validator

from rodoviaria.api.forms import Localidade, TrechoVendidoAPI
from rodoviaria.service.exceptions import RodoviariaOverbookingException


def _map_empresas(company_external_id):
    companies_ids = {int(company_external_id)}
    # empresa nova Eucatur com id_external 118 deve ser vendida juntamente com a de id 88126
    if int(company_external_id) == 88126:
        companies_ids = {88126, 118}
    return companies_ids


class BloquearPoltrona(BaseModel):
    selected_seat_key: str = Field(alias="selected_seat_Key")


class OrigemResponseForm(Localidade):
    @root_validator(pre=True)
    def translate(cls, local):
        descricao_split = local["description"].split(" - ")
        nome_cidade = descricao_split[0]
        complemento = None
        if len(descricao_split) > 1:
            complemento = descricao_split[1]
        return {
            "nome_cidade": nome_cidade,
            "external_cidade_id": local["id"],
            "uf": local["uf_acronym"],
            "complemento": complemento,
            "external_local_id": local["id"],
        }


class BuscarCorridasRequestForm(BaseModel):
    origin_sectional_id: int = Field(alias="origem")
    destiny_sectional_id: int = Field(alias="destino")
    departure_date: StrictStr = Field(alias="data")


class BloquearPoltronaRequestForm(BaseModel):
    seat_id: int
    tipo: Optional[str]

    @property
    def request_body(self):
        seat_body = {"seat": self.seat_id}
        if self.tipo:
            seat_body["type"] = self.tipo
            if self.tipo == "deficient":
                # TODO: não podemos vender gratuidade com acompanhante (required_companion) por enquanto.
                # Dependemos do calculo de accop por pax individual.
                seat_body["required_companion"] = False
        return {"items": [seat_body]}


class CondicaoCancelamentoForm(BaseModel):
    """
    "refund_amount": 62.41,
    "status": "authorized",
    "type": [
        "Cancel"
    ],
    "key": "eyJhbGciOiJIUzM4NCIsInR...",
    "allow_refund": false
    """

    extorno_total: Decimal = Field(alias="refund_amount")
    status: str
    tipos_cancelamento: Optional[list[str]] = Field(alias="type", default=None)
    key: str
    extorno_permitido: bool = Field(alias="allow_refund")


class BeneficioForm(BaseModel):
    tipo: str = Field(alias="type")
    vagas: int = Field(alias="free")


class CorridaForm(BaseModel):
    linha: Optional[str]
    datetime_ida: datetime
    external_id: str
    preco_rodoviaria: Decimal
    menor_preco_rodoviaria: Decimal
    provider_data: dict
    datetime_chegada: datetime
    classe: str
    classe_reduzida: str
    capacidade_classe: int
    distancia: str
    rota_external_id: int
    company_external_id: int
    key: str
    vagas: int
    beneficios: Optional[list[BeneficioForm]] = None

    def normalized_dict(cls):
        base_dict = cls.dict()
        base_dict["datetime_ida"] = cls.datetime_ida.strftime("%Y-%m-%dT%H:%M:%S")
        base_dict["datetime_chegada"] = cls.datetime_chegada.strftime("%Y-%m-%dT%H:%M:%S")
        return base_dict


class CorridasForm(BaseModel):
    __root__: List[CorridaForm]

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    @validator("__root__", pre=True)
    def parse(cls, v):
        corridas = []
        for cronograma in v:
            if cronograma.get("items") is None:
                continue
            for item in cronograma["items"]:
                if item.get("tariffs"):
                    for classe in item["tariffs"]:
                        provider_data = item.copy()
                        provider_data["tariffs"] = [classe]
                        provider_data["key"] = cronograma["key"]
                        provider_data["schedule_id"] = cronograma["id"]
                        poltrona_final = classe["category"].get("PoltronaFinal", classe["category"].get("final_seat"))
                        poltrona_inicial = classe["category"].get(
                            "PoltronaInicial", classe["category"].get("initial_seat")
                        )
                        tipo_veiculo = classe["category"].get("TipoVeiculoId") or classe["category"].get(
                            "vehicle_type_id"
                        )
                        classe_reduzida = classe["category"].get("DescricaoReduzida") or classe["category"].get(
                            "short_description"
                        )
                        descricao_classe = classe["category"].get("Descricao") or classe["category"].get("description")
                        vagas = classe["category"].get("free_seats", item.get("free_seats"))
                        corridas.append(
                            {
                                "linha": None,
                                "datetime_ida": item["datetime_departure"],
                                "external_id": f'{item["id"]}/{tipo_veiculo}',
                                "preco_rodoviaria": classe["amount"],
                                "menor_preco_rodoviaria": classe["price_promotional"],
                                "provider_data": provider_data,
                                "datetime_chegada": item["datetime_arrival"],
                                "classe": descricao_classe,
                                "classe_reduzida": classe_reduzida,
                                "capacidade_classe": (poltrona_final - poltrona_inicial + 1),
                                "distancia": classe["total_km"],
                                "rota_external_id": cronograma["id"],
                                "company_external_id": item["company"]["id"],
                                "key": cronograma["key"],
                                "vagas": vagas,
                                "beneficios": cronograma["free_seats_benefits"],
                            }
                        )
        return corridas

    @property
    def dict_list(cls):
        return [viagem.normalized_dict() for viagem in cls.__root__]

    def filter(self, rota_external_id=None, company_external_id=None):
        if rota_external_id:
            rota_external_id = int(rota_external_id)
            self.__root__ = list(filter(lambda c: c.rota_external_id == rota_external_id, self.__root__))
        if company_external_id:
            companies_ids = _map_empresas(company_external_id)
            self.__root__ = list(filter(lambda c: c.company_external_id in companies_ids, self.__root__))


class BeneficiosPermitidosPoltrona(BaseModel):
    tipo: str = Field(alias="type")
    preco: Decimal = Field(alias="price_discount")


class PoltronaForm(BaseModel):
    numero: int = Field(alias="number")
    ocupada: bool = Field(alias="busy")
    classe: str = Field(alias="category")
    exclusiva_mulheres: bool = Field(alias="woman_space")
    preco: Decimal = Field(alias="amount")
    y: Decimal = Field(alias="line")
    x: Decimal = Field(alias="column")
    beneficios_permitidos: list[BeneficiosPermitidosPoltrona] = Field(alias="benefits_values")

    @property
    def lista_beneficios_permitidos(self):
        return [b.tipo for b in self.beneficios_permitidos]


class PoltronasForm(BaseModel):
    __root__: List[PoltronaForm]

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    def set_ocupada(self, numero):
        for p in self.__root__:
            if p.numero == numero:
                p.ocupada = True
                break

    def filter(
        self,
        classe=None,
        ocupada=None,
        exclusiva_mulheres=False,
        preco_lte=None,
        preco_gt=None,
        preco_base_ordenacao=None,
    ):
        def condition(p):
            woman_space_filter = p.exclusiva_mulheres == exclusiva_mulheres
            class_filter = classe is None or p.classe == classe
            busy_filter = ocupada is None or p.ocupada == ocupada
            max_price_filter = preco_lte is None or preco_lte >= p.preco
            min_price_filter = preco_gt is None or preco_gt < p.preco

            return woman_space_filter and class_filter and busy_filter and max_price_filter and min_price_filter

        self.__root__ = list(filter(condition, self.__root__))
        if preco_base_ordenacao:
            self.__root__ = sorted(self.__root__, key=lambda k: (abs(k.preco - preco_base_ordenacao), k.numero))
        else:
            self.__root__ = sorted(self.__root__, key=lambda k: (k.preco, k.numero))

    def filter_por_categoria_especial_external(self, categoria_especial_external):
        self.__root__ = list(
            filter(lambda k: categoria_especial_external in k.lista_beneficios_permitidos, self.__root__)
        )

    def select(self, quantidade):
        if len(self.__root__) < quantidade:
            raise RodoviariaOverbookingException(vagas_disponiveis=len(self.__root__))
        poltronas_selecionadas = []
        max_poltronas_juntas = []
        poltronas_separadas = []
        if quantidade == 1:
            return [self.__root__[0].numero]
        for poltrona in self.__root__:
            if not poltronas_selecionadas:
                poltronas_selecionadas.append(poltrona.numero)
                continue
            ultima = poltronas_selecionadas[-1]
            if poltrona.numero == ultima + 1:
                poltronas_selecionadas.append(poltrona.numero)
            elif len(poltronas_selecionadas) > len(max_poltronas_juntas):
                poltronas_separadas += max_poltronas_juntas
                max_poltronas_juntas = poltronas_selecionadas
                poltronas_selecionadas = [poltrona.numero]
            else:
                poltronas_separadas += poltronas_selecionadas
                poltronas_selecionadas = [poltrona.numero]
            if len(poltronas_selecionadas) == quantidade:
                return poltronas_selecionadas
        poltronas_separadas += poltronas_selecionadas
        poltronas_selecionadas = max_poltronas_juntas
        for poltrona in poltronas_separadas:
            poltronas_selecionadas.append(poltrona)
            if len(poltronas_selecionadas) == quantidade:
                return poltronas_selecionadas
        raise RuntimeError("Erro ao selecionar poltrona")

    def map(self):
        return {str(p.numero).zfill(2): "ocupada" if p.ocupada else "livre" for p in self.__root__}

    def count(self, classe=None):
        return len(list(filter(lambda p: p.classe == classe and not p.ocupada, self.__root__)))

    def lower_price(self):
        if not self.__root__:
            return None

        return min((p.preco for p in self.__root__ if p.preco > 0), default=None)


class BPeForm(BaseModel):
    qrcode: Optional[str] = Field(alias="qrcode", default=None)
    data_autorizacao: Optional[datetime] = Field(alias="authorization", default=None)
    chave: Optional[str] = Field(alias="access_key", default=None)
    autorizacao: Optional[str] = Field(alias="protocol", default=None)
    bpe_url: Optional[str] = Field(alias="public_url", default=None)

    @validator("*", pre=True)
    def parse_datetime(cls, value):
        if value == "":
            return None
        return value


class PrecoForm(BaseModel):
    base: Optional[Decimal] = Field(alias="tariff", default=0)
    embarque: Optional[Decimal] = Field(alias="boarding_fee", default=0)
    seguro: Optional[Decimal] = Field(alias="insurance", default=0)
    pedagio: Optional[Decimal] = Field(alias="toll", default=0)
    outras_taxas: Optional[Decimal] = Field(alias="additional", default=0)
    total: Optional[Decimal] = Field(alias="total", default=0)
    desconto: Optional[Decimal] = Field(alias="discount", default=0)
    balsa: Optional[Decimal] = Field(alias="ferry", default=0)


class TributosForm(BaseModel):
    icms: Optional[Decimal] = Field(alias="icms_value", default=0.0)
    icms_porcentagem: Optional[Decimal] = Field(alias="icms_percentage", default=0.0)
    outros_tributos: Optional[Decimal] = Field(alias="other_taxes_value", default=0.0)
    outros_tributos_porcentagem: Optional[Decimal] = Field(alias="other_taxes_percentage", default=0.0)

    def format(self):
        return (
            f"ICMS {self.icms} ({self.icms_porcentagem}%) OUTROS TRIB:"
            f" {self.outros_tributos} ({self.outros_tributos_porcentagem}%)"
        )


class LinhaForm(BaseModel):
    prefixo: Optional[str] = Field(alias="prefix", default=None)
    descricao: Optional[str] = Field(alias="description", default=None)
    codigo_federal: Optional[str] = Field(alias="federal_code")
    codigo: Optional[str] = Field(alias="code")


class Passageiro(BaseModel):
    primeiro_nome: str = Field(alias="first_name")
    ultimo_nome: str = Field(alias="last_name")
    tipo_documento: str = Field(alias="document_type")
    numero_documento: str = Field(alias="document_number")
    nascimento: str = Field(alias="birth")


class Estado(BaseModel):
    uf: str = Field(alias="acronym")


class Local(BaseModel):
    nome: str = Field(alias="name")
    estado: Estado = Field(alias="state")


class LocalEmbarque(BaseModel):
    nome: str = Field(alias="name")
    local: Local = Field(alias="locality")
    codigo: str = Field(alias="code")


class Empresa(BaseModel):
    nome: str = Field(alias="name")


class TicketEmbarque(BaseModel):
    numero: str = Field(alias="number")


class EmbarqueEletronico(BaseModel):
    ticket_embarque: TicketEmbarque = Field(alias="TicketGate")
    qrcode: str = Field(alias="qrcode_bpe")


class ViagemPassageiro(BaseModel):
    poltrona: int = Field(alias="seat_number")
    linha: LinhaForm = Field(alias="line")
    tributos: TributosForm = Field(alias="tribute")
    status: str
    datetime_inicio_viagem: datetime | None = Field(alias="datetime_start")
    datetime_ida: datetime | None = Field(alias="datetime_departure")
    datetime_chegada: datetime | None = Field(alias="datetime_arrival")
    embarque: LocalEmbarque = Field(alias="boarding")
    desembarque: LocalEmbarque = Field(alias="landing")
    duracao: str = Field(alias="duration")
    empresa: Empresa = Field(alias="centralizing_company")
    ticket_id: str = Field(alias="ticket_item_id")
    embarque_eletronico: Optional[EmbarqueEletronico] = Field(alias="electronic_boarding")
    direcao: int = Field(alias="direction")

    @validator("datetime_chegada", "datetime_inicio_viagem", "datetime_ida", pre=True, always=True)
    def parse_timestamp(cls, value):
        if value == "":
            return None
        return value


class ReservaForm(BaseModel):
    passagem_key: str = Field(alias="key")
    pedido_id: int = Field(alias="sale_id")
    localizador: str = Field(alias="localizer")
    bpe: BPeForm
    preco: PrecoForm
    numero_bilhete: Optional[str] = None
    passageiros: list[Passageiro] = Field(alias="utilizers")
    viagem: ViagemPassageiro = Field(alias="road")

    @root_validator(pre=True)
    def poltrona_inside_dict(cls, reserva):
        custos = {"total": reserva["amount"]}
        for tarifa in reserva.get("components") or []:
            custos[tarifa["type"]] = tarifa["value"]
        reserva["preco"] = custos
        return reserva

    def make_embarque_eletronico(self):
        # eucaturmobile/boarding?type=manual
        # &identification=*********
        # &code=0209
        # &line=40631
        # &departure_travel=12/12/2024 12:30:00
        # &departure=12/12/2024 12:30:00
        # &direction=Ida

        # identification = ticket_item_id
        # code = boarging.code
        # line = line.code ou line.federal_code (o que tiver valor)
        # departure_travel = datetime_start
        # departure = datetime_departure
        # direction = direction (1 = 'Ida' ou 2 = 'Volta')

        # Quando UF (boarding.locality.state.acronym) do embarque for RO, passa a mensagem
        # "GERAR BPE ATE O EMBARQUE, ANTES DO INICIO DA PRESTAÇAO DO SERVIÇO
        # (SEÇAO V, ANEXO XIII, ART 23 $1º, RICMS/RO-DECR.22721/18).
        # APÓS EMBARQUE VER BPE NO APP C/MOTORISTA"
        if not self.viagem.datetime_ida:
            return
        embarque_eletronico = (
            "eucaturmobile/boarding?type=manual"
            f"&identification={self.viagem.ticket_id}"
            f"&code={self.viagem.embarque.codigo}"
            f"&line={self.viagem.linha.codigo or self.viagem.linha.codigo_federal}"
            f"&departure_travel={self.viagem.datetime_inicio_viagem.strftime('%d/%m/%Y %H:%M:%S')}"
            f"&departure={self.viagem.datetime_ida.strftime('%d/%m/%Y %H:%M:%S')}"
            f"&direction={'Ida' if self.viagem.direcao == 1 else 'Volta'}"
        )
        if self.viagem.embarque.local.estado.uf == "RO":
            embarque_eletronico += (
                "&msg=GERAR BPE ATE O EMBARQUE, ANTES DO INICIO DA PRESTAÇAO DO SERVIÇO "
                "(SEÇAO V, ANEXO XIII, ART 23 $1º, RICMS/RO-DECR.22721/18). "
                "APÓS EMBARQUE VER BPE NO APP C/MOTORISTA"
            )
        return embarque_eletronico


class ReservasForm(BaseModel):
    __root__: List[ReservaForm]

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)


class BasePassagemForm(BaseModel):
    """
    {
        "key": "VIB-17416588",
        "localizer": "00001-VOUCHER-DA22C-0984202559",
        "amount": 163.09,
        "datetime_start": "2022-06-11 02:30:00",
        "seat": 24,
        "status": "confirmed",
        "utilizer": {
            "first_name": "João",
            "last_name": "Silva",
            "document_type": "RG",
            "document_number": "3344/RO",
            "cpf": "586.960.940-26",
            "phone": "69999943333",
        },
        "boarding": {"id": 3, "name": "JI-PARANÁ", "uf": "RO"},
        "landing": {"id": 5, "name": "PORTO VELHO", "uf": "RO"},
    }
    """

    passagem_key: str = Field(alias="key")
    localizador: str = Field(alias="localizer")
    poltrona: int = Field(alias="seat")
    preco: Decimal = Field(alias="amount")


class CompraForm(BaseModel):
    """
    {
        "sale_id": 90815779,
        "amount": 163.09,
        "items": [
            ...
        ],
    }
    """

    pedido_id: int = Field(alias="sale_id")
    passagens: list[BasePassagemForm] = Field(alias="items")


class Parada(BaseModel):
    """
    {
        "seccional_id": 5,
        "seccional_code": "0234",
        "seccional_name": "PORTO VELHO",
        "total_time": "00:00",
        "arrival_zone": "-01:00",
        "local_arrival_date_time": "2022-06-24T23:15:00Z",
        "stop_time": "00:15",
        "departure_time_zone": "-01:00",
        "local_exit": "2022-06-24T23:30:00Z"
    }
    """

    local: Localidade
    datetime_ida: datetime
    distancia: Optional[Decimal]
    duracao: Optional[int]
    tempo_embarque: Optional[int]
    distancia_total: Optional[Decimal] = Field(alias="total_km")

    def to_int_secconds(cls, v):
        [horas, minutos] = v.split(":")
        return (int(horas) * 60 + int(minutos)) * 60

    def generate_datetime_ida(cls, v):
        return datetime.strptime(v, "%Y-%m-%dT%H:%M:%SZ")

    @root_validator(pre=True)
    def validate_all(cls, parada):
        parada["tempo_embarque"] = cls.to_int_secconds(cls, parada["stop_time"])
        parada["datetime_ida"] = cls.generate_datetime_ida(cls, parada["local_exit"])
        parada["local"] = {
            "nome_cidade": parada["seccional_name"],
            "external_local_id": parada["seccional_id"],
            "external_cidade_id": parada["seccional_id"],
            "uf": parada["uf_acronym"],
        }
        return parada


class Itinerario(BaseModel):
    __root__: List[Parada]

    @property
    def _sigla_duracao(self):
        return "-".join(f"{p.local.external_local_id}.{p.duracao}" for p in self.__root__)

    @property
    def hash(self):
        inicio = self[0].local.external_local_id
        fim = self[-1].local.external_local_id
        digest = hashlib.md5(self._sigla_duracao.encode()).hexdigest()  # noqa: S324
        return f"{inicio}{fim}{digest}"

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    @validator("__root__")
    def _attach_tempos(cls, v):
        if not v:
            return v

        iti_first = v[0]
        iti_first.duracao = 0
        iti_first.tempo_embarque = 0
        iti_first.distancia = 0

        for iti_anterior, iti_atual in zip(v, v[1:]):
            duracao = iti_atual.datetime_ida - iti_anterior.datetime_ida
            duracao = timedelta(seconds=duracao.seconds)

            iti_atual.distancia = iti_atual.distancia_total - iti_anterior.distancia_total
            iti_atual.duracao = (duracao).total_seconds() - iti_atual.tempo_embarque

        iti_last = v[-1]
        iti_last.tempo_embarque = 0

        return v


class Viagem(BaseModel):
    """
    {
        "id": 2004701,
        "line_code": "D46",
        "description": "CEREJEIRAS - GARAGEM x PORTO VELHO GARAGEM",
        "departure_time": "17:30:00",
        "departure_date": "2022-07-15",
        "schedule_id": 6237
    }
    """

    item_id: int = Field(alias="id")
    codigo_linha: str = Field(alias="line_code")
    descricao_linha: str = Field(alias="description")
    datetime_ida: datetime
    rota_external_id: int = Field(alias="schedule_id")
    company_external_id: int = Field(alias="company_id")

    @root_validator(pre=True)
    def generate_datetime_ida(cls, v):
        hora = v["departure_time"]
        data = v["departure_date"]
        v["datetime_ida"] = datetime.strptime(f"{data} {hora}", "%Y-%m-%d %H:%M:%S")
        return v


class Viagens(BaseModel):
    __root__: List[Viagem]

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    def filter(self, rotas_external_ids=None, company_external_id=None):
        if rotas_external_ids:
            rotas_external_ids = [int(rei) for rei in rotas_external_ids]
            self.__root__ = list(filter(lambda v: v.rota_external_id in rotas_external_ids, self.__root__))
        if company_external_id:
            companies_ids = _map_empresas(company_external_id)
            self.__root__ = list(filter(lambda v: v.company_external_id in companies_ids, self.__root__))

    def remove_rotas_repetidas(self):
        rotas_encontradas = set()
        viagens_filtradas = []
        for v in self.__root__:
            if v.rota_external_id not in rotas_encontradas:
                rotas_encontradas.add(v.rota_external_id)
                viagens_filtradas.append(v)
        self.__root__ = viagens_filtradas


class TrechoVendidoModel(TrechoVendidoAPI):
    origem_id: int = Field(alias="origin_id")
    origem_name: str = Field(alias="origin_name")
    destino_id: int = Field(alias="destination_id")
    destino_name: str = Field(alias="destination_name")
    classe_id: str = Field(alias="class")
    vagas: int = Field(alias="seats")
    capacidade: int = Field(alias="capacity")
    classe: str = Field(alias="class_description")

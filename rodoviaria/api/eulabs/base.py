from http import HTTPStatus

from requests.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>

from rodoviaria.admin import EulabsLogin
from rodoviaria.api.eulabs.auth import E<PERSON><PERSON>Auth
from rodoviaria.api.executors import Request, RequestConfig, Response
from rodoviaria.api.executors.http_status import CloudflareHTTPStatus
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaOTAException,
    RodoviariaTooManyRequestsError,
)


class BaseRequestConfig(RequestConfig):
    # Prefix for logging and error messages
    MESSAGE_PREFIX = "eulabs"

    # See rodoviaria/api/executors/middlewares.py:LoggingMiddleware
    integration_name = "EulabsAPI"

    timeout = 60

    http_adapter = HTTPAdapter(
        max_retries=Retry(
            total=3,
            backoff_factor=1,
            raise_on_status=False,
            status_forcelist=[401, 406, 408],
        ),
    )

    def __repr__(self):
        return f"EULABS_{self.__class__.__name__}_{self.login.company.id}_{self.login.company.modelo_venda}"

    def __init__(self, login: EulabsLogin):
        self.login = login
        self.auth = EulabsAuth.from_client(self.login)  # TODO: memoize this call?

    def endpoint(self, endpoint: str) -> str:
        return f"{self.login.company.url_base}/{endpoint}"

    def preprocess_request(self, request: Request) -> Request:
        self.auth(request)
        return request

    def process_response(self, response: Response):
        if response.ok:
            return response

        generic_message = (
            f"{self.MESSAGE_PREFIX} '{response.request.method} {response.request.url}' "
            f"json={response.request.json} params={response.request.params} "
            f"status_code={response.status_code}"
        )
        status_code = response.status_code

        try:
            payload = response.json()
            message = payload.get("erro", generic_message)
            message = payload.get("message", message)
        except (ValueError, AttributeError):
            message = generic_message

        self._raise_for_response(status_code, message)
        self._raise_for_status(status_code, message)

    def _raise_for_response(self, status_code, message):
        return

    def _raise_for_status(self, status_code, message):
        if status_code == HTTPStatus.TOO_MANY_REQUESTS:
            message = f"{status_code} too many requests"
            raise RodoviariaTooManyRequestsError(message)
        elif status_code == HTTPStatus.NOT_ACCEPTABLE:
            message = f"{status_code} too many requests"
            raise RodoviariaTooManyRequestsError(message)
        # status_code da cloudflare
        elif status_code == (CloudflareHTTPStatus.CLOUDFLARE_EMPTY_RESPONSE):
            message = f"520 {CloudflareHTTPStatus.CLOUDFLARE_EMPTY_RESPONSE.description}"
            raise RodoviariaConnectionError(message)
        elif status_code == (CloudflareHTTPStatus.CLOUDFLARE_OFFLINE):
            message = f"521 {CloudflareHTTPStatus.CLOUDFLARE_OFFLINE.description}"
            raise RodoviariaConnectionError(message)
        elif status_code == (CloudflareHTTPStatus.CLOUDFLARE_HANDSHAKE_TIMEOUT):
            message = f"522 {CloudflareHTTPStatus.CLOUDFLARE_HANDSHAKE_TIMEOUT.description}"
            raise RodoviariaConnectionError(message)
        elif status_code == (CloudflareHTTPStatus.CLOUDFLARE_UNREACHABLE):
            message = f"523 {CloudflareHTTPStatus.CLOUDFLARE_UNREACHABLE.description}"
            raise RodoviariaConnectionError(message)
        elif status_code == CloudflareHTTPStatus.CLOUDFLARE_CONNECTION_TIMEOUT:
            message = f"524 {CloudflareHTTPStatus.CLOUDFLARE_CONNECTION_TIMEOUT.description}"
            raise RodoviariaConnectionError(message)
        elif status_code == CloudflareHTTPStatus.CLOUDFLARE_SSL_FAILED:
            message = f"525 {CloudflareHTTPStatus.CLOUDFLARE_SSL_FAILED.description}"
            raise RodoviariaConnectionError(message)
        elif status_code == CloudflareHTTPStatus.CLOUDFLARE_SSL_INVALID:
            message = f"526 {CloudflareHTTPStatus.CLOUDFLARE_SSL_INVALID.description}"
            raise RodoviariaConnectionError(message)
        elif status_code == HTTPStatus.BAD_GATEWAY:
            raise RodoviariaConnectionError("502 Bad Gateway")
        elif status_code == HTTPStatus.BAD_REQUEST:
            msg = parse_response(message, "message")
            if (
                msg == "context deadline exceeded"
                or msg == "error reading from server: EOF"
                or "connection error" in msg
            ):
                raise RodoviariaConnectionError(f"{status_code} connection error: {msg}")
        raise RodoviariaOTAException(message)


def parse_response(response_json, key: str):
    if isinstance(response_json, dict):
        return response_json.get(key, "")
    return response_json

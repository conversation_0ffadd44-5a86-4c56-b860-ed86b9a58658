from datetime import date
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPStatus

from pydantic import parse_obj_as
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from rodoviaria.api.eulabs import models
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.executors.middlewares import token_bucket_middleware
from rodoviaria.models.eulabs import EulabsLogin
from rodoviaria.service.exceptions import (
    PassengerInvalidDocumentException,
    PassengerTicketAlreadyReturnedException,
    PoltronaExpirada,
    RodoviariaConnectionError,
)

from .base import BaseRequestConfig
from .exceptions import (
    EulabsNaoPodeCancelar,
    EulabsPoltronaIndisponivel,
    EulabsSeatingMapError,
    EulabsVendaNaoEncontrada,
)


class BuscarOrigensConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("sectionals")


class BuscarCorridasConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("road/travels/search")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code in (HTTPStatus.GATEWAY_TIMEOUT, HTTPStatus.NOT_FOUND):
            raise RodoviariaConnectionError(message)


class RetornaPoltronasConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    def __init__(self, login, travel_key: str):
        self.travel_key = travel_key
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"road/travels/{self.travel_key}/seating-map")

    def _raise_for_response(self, status_code: int, message: dict):
        if status_code == HTTPStatus.BAD_REQUEST:
            if message == "não foi possível realizar a marcação solicitada, tente novamente":
                raise EulabsSeatingMapError(status_code, message)
        super()._raise_for_response(status_code, message)


class BloquearPoltronaConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    def __init__(self, login, travel_key: str):
        self.travel_key = travel_key
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"road/travels/{self.travel_key}/seats")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code == HTTPStatus.BAD_REQUEST:
            if any(
                msg in message
                for msg in ["está sendo marcada.", "essa poltrona está indisponível no momento", "não está disponível"]
            ):
                raise EulabsPoltronaIndisponivel("poltrona bloqueada")
        super()._raise_for_response(status_code, message)


class DesbloquearPoltronaConfig(BaseRequestConfig):
    method = HTTPMethod.DELETE

    def __init__(self, login, travel_key: str, selected_seat_key: str):
        self.travel_key = travel_key
        self.selected_seat_key = selected_seat_key
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"road/travels/{self.travel_key}/selected_seats/{self.selected_seat_key}")


class EfetuarReservaConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    timeout = 120

    @property
    def url(self):
        return self.endpoint("sales")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code == HTTPStatus.UNPROCESSABLE_ENTITY:
            if message == "invalid parameters":
                raise PassengerInvalidDocumentException("Documento inválido. Verifique os dados cadastrados.")
            raise RodoviariaConnectionError(message)
        if status_code == HTTPStatus.BAD_REQUEST:
            if message == "a marcação de poltrona não foi encontrada":
                raise PoltronaExpirada


class ConsultaReservaConfig(BaseRequestConfig):
    method = HTTPMethod.GET
    timeout = 150
    disable_middlewares = {
        token_bucket_middleware,
    }

    def __init__(self, login, sale_id):
        self.sale_id = sale_id
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"sales/find/{self.sale_id}")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code == HTTPStatus.BAD_REQUEST:
            raise EulabsVendaNaoEncontrada(status_code, message)
        super()._raise_for_response(status_code, message)


class ConsultaBpeConfig(BaseRequestConfig):
    method = HTTPMethod.GET
    timeout = 150
    disable_middlewares = {
        token_bucket_middleware,
    }

    def __init__(self, login, sale_id, item_key):
        self.sale_id = sale_id
        self.item_key = item_key
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"sales/{self.sale_id}/items/{self.item_key}/request_bpe")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code == HTTPStatus.BAD_REQUEST:
            raise EulabsVendaNaoEncontrada(status_code, message)
        super()._raise_for_response(status_code, message)


class CondicoesCancelamentoConfig(BaseRequestConfig):
    method = HTTPMethod.GET
    timeout = 300

    def __init__(self, login, item_key):
        self.item_key = item_key
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"sales/cancel_conditions/{self.item_key}")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code in (HTTPStatus.BAD_GATEWAY, HTTPStatus.UNPROCESSABLE_ENTITY):
            raise EulabsNaoPodeCancelar(status_code, message)


class CancelaVendaConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    timeout = 300

    def __init__(self, login, key, type):
        self.key = key
        self.type = type
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"sales/cancel_item/{self.key}/{self.type}")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code in (HTTPStatus.BAD_GATEWAY, HTTPStatus.UNPROCESSABLE_ENTITY):
            if message == "não é possível efetuar o cancelamento pois o item ja consta com status de cancelamento":
                raise PassengerTicketAlreadyReturnedException(message)
            raise EulabsNaoPodeCancelar(status_code, message)
        super()._raise_for_response(status_code, message)


class BuscarItinerarioViagemConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    def __init__(self, login, item_id, rota_external_id=None):
        self.item_id = item_id
        self.rota_external_id = rota_external_id
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"road/travels/summary/{self.item_id}")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code == HTTPStatus.NOT_FOUND:
            raise RodoviariaConnectionError(message)


class BuscarTrechosVendidosViagemConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    def __init__(self, login, item_id):
        self.item_id = item_id
        super().__init__(login)

    @property
    def url(self):
        return self.endpoint(f"road/travels/detail/{self.item_id}")

    def _raise_for_response(self, status_code: int, message: str):
        if status_code == HTTPStatus.NOT_FOUND:
            raise RodoviariaConnectionError(message)


class BuscarViagensPorPeriodoConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("road/travels/summary")


@retry(
    retry=retry_if_exception_type(RodoviariaConnectionError),
    reraise=True,
    stop=stop_after_attempt(3),
)
def buscar_viagens_por_periodo_request(client: EulabsLogin, data_inicio: date, data_fim: date):
    executor = get_http_executor()
    request_config = BuscarViagensPorPeriodoConfig(client)
    response = request_config.invoke(
        executor,
        params={
            "initial_departure_date": data_inicio.strftime("%Y-%m-%d"),
            "final_departure_date": data_fim.strftime("%Y-%m-%d"),
        },
    )
    response_json = response.json()
    # quando não tem viagens esse endpoint pode retornar None
    if response_json:
        return parse_obj_as(models.Viagens, response_json)

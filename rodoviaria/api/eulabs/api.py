import copy
import logging
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal as D
from typing import cast

from celery import shared_task
from django.utils import timezone
from pydantic import ValidationError, parse_obj_as
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from commons.dateutils import to_default_tz, to_tz
from commons.django_utils import error_str
from commons.redis import lock
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.eulabs import descobrir_operacao
from rodoviaria.api.eulabs import endpoints as endpoints
from rodoviaria.api.eulabs import models as models
from rodoviaria.api.eulabs.exceptions import (
    EulabsAPIError,
    EulabsNaoPodeCancelar,
    EulabsPoltronaIndisponivel,
    EulabsSeatingMapError,
    EulabsTravelKeyNotFound,
    EulabsVendaNaoEncontrada,
)
from rodoviaria.api.eulabs.memcache import EulabsMC
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.forms import BuscarServicoForm, RetornoConsultarBilheteForm, RetornoItinerario, ServicoForm
from rodoviaria.api.rodoviaria_api import RodoviariaAPI
from rodoviaria.forms.compra_rodoviaria_forms import (
    BloquearPoltronasResponse,
    ComprarForm,
    PassageiroForm,
    VerificarPoltronaForm,
)
from rodoviaria.forms.mapa_poltronas_forms import Assento, Deck, Onibus
from rodoviaria.models.core import Company, Passagem, TrechoClasse
from rodoviaria.models.eulabs import EulabsLogin, RotaEulabs
from rodoviaria.service import class_match_svc, descobrir_rotas_eulabs_async_svc
from rodoviaria.service.exceptions import (
    CampoObrigatorioException,
    PassengerInvalidDocumentException,
    PassengerNotRegistered,
    PassengerTicketAlreadyPrintedException,
    PoltronaTrocadaException,
    RodoviariaBaseException,
    RodoviariaConnectionError,
    RodoviariaException,
)

rodovlogger = logging.getLogger("rodoviaria")


class EulabsAPI(RodoviariaAPI):
    Rota = RotaEulabs
    divergencia_maxima_pct = 50

    PASSAGEM_TYPE_MAP = {
        Passagem.CategoriaEspecial.CRIANCA: "",
        Passagem.CategoriaEspecial.PCD: "deficient",
        Passagem.CategoriaEspecial.JOVEM_100: "young",
        Passagem.CategoriaEspecial.JOVEM_50: "partial_young",
        Passagem.CategoriaEspecial.IDOSO_100: "elderly",
        Passagem.CategoriaEspecial.IDOSO_50: "partial_elderly",
    }

    CATEGORIA_ESPECIAL_MAP = {valor: chave for chave, valor in PASSAGEM_TYPE_MAP.items()}

    def __init__(self, company):
        super().__init__(company)
        self.login = EulabsLogin.objects.select_related("company__integracao").get(company=company)
        self.cache = EulabsMC(company.company_internal_id)

    def __repr__(self):
        return f"{__class__.__name__}_{self.login.company.name}"

    def atualiza_origens(self):
        executor = get_http_executor()
        request_config = endpoints.BuscarOrigensConfig(self.login)
        response = request_config.invoke(
            executor,
            params={"is_road_station": True},
        )
        response_json = response.json()
        origens = parse_obj_as(list[models.OrigemResponseForm], response_json)
        return list(origens)

    @retry(
        retry=retry_if_exception_type(RodoviariaConnectionError),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def _buscar_corridas_request(self, request_params):
        executor = get_http_executor()
        params = models.BuscarCorridasRequestForm.parse_obj(request_params).dict()
        request_config = endpoints.BuscarCorridasConfig(self.login)
        response = request_config.invoke(executor, params=params)
        return response.json()

    def buscar_corridas(self, request_params, match_params=None):
        corridas = self._buscar_corridas_request(request_params)
        corridas = parse_obj_as(models.CorridasForm, corridas)
        corridas.filter(company_external_id=self.company.company_external_id)
        corridas_form = self._make_corridas_form(corridas)
        if not match_params:
            return corridas_form

        d_args = self._find_match_corridas(corridas, request_params, **match_params)
        return self._parse_retorno_buscar_servico(**d_args)

    def _make_corridas_form(self, corridas):
        found = bool(corridas)
        servicos = []
        for corrida in corridas:
            _, preco = corrida.vagas, self._get_price(corrida)
            corrida.preco_rodoviaria = preco
            corrida = corrida.normalized_dict()
            servicos.append(
                ServicoForm(
                    linha=corrida["linha"],
                    external_datetime_ida=corrida["datetime_ida"],
                    external_id=corrida["external_id"],
                    preco=corrida["preco_rodoviaria"],
                    vagas=corrida["vagas"],
                    provider_data=corrida["provider_data"],
                    external_datetime_chegada=corrida["datetime_chegada"],
                    classe=corrida["classe"],
                    capacidade_classe=corrida["capacidade_classe"],
                    classe_reduzida=corrida["classe_reduzida"],
                    distancia=corrida["distancia"],
                    rota_external_id=corrida["rota_external_id"],
                    external_company_id=corrida["company_external_id"],
                    key=corrida["key"],
                )
            )

        return BuscarServicoForm(found=found, servicos=servicos)

    def _find_match_corridas(self, corridas, request_params, datetime_ida, timezone, tipo_assento):
        matches = []
        mismatches = []
        d_args = {}
        buser_class = tipo_assento
        for servico in corridas:
            api_class = servico.classe.lower()
            if self._match_datetime_ida_servico(datetime_ida, timezone, servico.datetime_ida) and (
                self._does_class_match(buser_class, api_class)
            ):
                matches.append(servico)
            else:
                mismatches.append(servico)

        if len(matches) == 1:
            matched_service = matches[0]
            vagas, preco = matched_service.vagas, self._get_price(matched_service)
            d_args.update({"servico_encontrado": matched_service, "timezone": timezone, "vagas": vagas, "preco": preco})
            return d_args

        if len(matches) > 1:
            sorted_matches_by_diff_datetime_ida = sorted(
                matches,
                key=lambda k: (
                    self._get_diff_datetime_ida_in_minutes(datetime_ida, timezone, k.datetime_ida),
                    -k.vagas,
                ),
            )

            # quando mais de um match, tenta achar o que tenha match de classe
            # senão achar, retorna o mais proximo de horario
            matched_service = sorted_matches_by_diff_datetime_ida[0]
            for servico in sorted_matches_by_diff_datetime_ida:
                api_class = servico.classe.lower()
                if self._does_class_match(buser_class, api_class):
                    matched_service = servico
                    break

            vagas, preco = servico.vagas, self._get_price(servico)
            d_args.update({"servico_encontrado": matched_service, "timezone": timezone, "vagas": vagas, "preco": preco})
            return d_args

        mismatches_classe_horario = [(m.classe, m.datetime_ida) for m in mismatches]
        msg = (
            f"Unmatch de servico {self.company.name} com {request_params}: "
            f"({buser_class}, {datetime_ida}, {timezone}) -> {mismatches_classe_horario}"
        )
        rodovlogger.info(msg)
        d_args.update({"timezone": timezone, "mismatches": mismatches})

        return d_args

    @retry(
        retry=retry_if_exception_type(RodoviariaConnectionError),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def buscar_itinerario(self, params):
        executor = get_http_executor()
        request_config = endpoints.BuscarItinerarioViagemConfig(self.login, item_id=params["item_id"])
        response = request_config.invoke(executor)
        response_raw = response.json()
        response_cleaned = order_itinerario_by_datetime(response_raw)
        response_parsed = parse_obj_as(models.Itinerario, response_cleaned)
        return RetornoItinerario(
            raw=response_raw,
            cleaned=response_cleaned,
            parsed=response_parsed,
        )

    def _parse_retorno_buscar_servico(
        self, servico_encontrado=None, vagas=None, preco=None, timezone=None, mismatches=None
    ) -> BuscarServicoForm:
        if mismatches is None:
            mismatches = []
        found = bool(servico_encontrado)
        if found:
            servico = ServicoForm(
                provider_data=servico_encontrado.dict(),
                external_id=servico_encontrado.external_id,
                classe=servico_encontrado.classe,
                preco=preco,
                vagas=vagas,
                external_datetime_ida=to_tz(servico_encontrado.datetime_ida, timezone),
            )
            servicos = [servico]
        else:
            servicos = [self._normalizar_servico(s, timezone) for s in mismatches]

        return BuscarServicoForm(found=found, servicos=servicos)

    def _normalizar_servico(self, servico: models.CorridaForm, timezone):
        servico_form = ServicoForm.parse_obj(
            {
                "external_id": servico.external_id,
                "preco": self._get_price(servico),
                "external_datetime_ida": to_tz(servico.datetime_ida, timezone),
                "classe": servico.classe,
                "external_company_id": self.company.company_external_id,
                "vagas": servico.vagas,
                "provider_data": servico.provider_data,
            }
        )
        return servico_form

    def _get_price(self, servico: models.CorridaForm):
        if self.company.has_feature(Company.Feature.USE_PRICE_PROMOTIONAL) and servico.menor_preco_rodoviaria > 0:
            return servico.menor_preco_rodoviaria
        return servico.preco_rodoviaria

    @retry(
        retry=retry_if_exception_type(EulabsTravelKeyNotFound),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def _get_viagem_key_and_class(self, trecho_classe):
        (item_id, classe_api_id) = trecho_classe.external_id.split("/")
        request_params = {
            "origem": trecho_classe.origem.id_external,
            "destino": trecho_classe.destino.id_external,
            "data": to_tz(trecho_classe.datetime_ida, trecho_classe.origem.cidade.timezone).strftime("%Y-%m-%d"),
        }
        corridas = self._buscar_corridas_request(request_params)
        for corrida in corridas:
            for item in corrida["items"]:
                if item["id"] != int(item_id):
                    continue
                for classe in item["tariffs"]:
                    category = classe["category"]
                    tipo_veiculo = category.get("TipoVeiculoId") or category.get("vehicle_type_id")
                    classe_reduzida = category.get("DescricaoReduzida") or category.get("short_description")
                    classe = category.get("Descricao") or category.get("description")
                    if tipo_veiculo == int(classe_api_id):
                        return corrida["key"], classe_reduzida, classe
        raise EulabsTravelKeyNotFound("travel_key não encontrado.")

    def verifica_poltrona(self, params: VerificarPoltronaForm):
        trecho_classe_id = params.trechoclasse_id
        poltronas, travel_key = self.get_poltronas_disponiveis(trecho_classe_id, params.categoria_especial)
        self.cache.set_travel_key_cache(trecho_classe_id, travel_key)
        poltronas_selecionadas = poltronas.select(quantidade=params.passageiros)
        poltronas_selecionadas = self._bloquear_poltronas(
            travel_key,
            trecho_classe_id,
            poltronas,
            poltronas_selecionadas,
            categoria_especial=params.categoria_especial,
        )
        return poltronas_selecionadas

    def get_poltronas_disponiveis(self, trecho_classe_id, categoria_especial):
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        poltronas, classe_api, travel_key = self._get_poltronas_classe_and_trechoclasse(trecho_classe_id)
        poltronas.filter(
            classe=classe_api, ocupada=False, preco_gt=0, preco_base_ordenacao=trecho_classe.preco_rodoviaria
        )
        categoria_especial_external = self.PASSAGEM_TYPE_MAP.get(categoria_especial)
        if categoria_especial_external:
            poltronas.filter_por_categoria_especial_external(categoria_especial_external)
        return poltronas, travel_key

    def bloquear_poltronas(self, trechoclasse_id, poltronas):
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        travel_key = self.get_or_update_travel_key(trecho_classe, trechoclasse_id)
        return self._bloquear_poltronas(travel_key, trechoclasse_id, poltronas=None, poltronas_selecionadas=poltronas)

    def bloquear_poltronas_v2(self, trechoclasse_id, poltrona, categoria_especial) -> BloquearPoltronasResponse:
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        travel_key = self.get_or_update_travel_key(trecho_classe, trechoclasse_id)

        self.bloqueia_poltrona(trechoclasse_id, travel_key, poltrona, categoria_especial)

        cache_payload = self.cache.get_poltrona_key_cache(trechoclasse_id, poltrona)
        return BloquearPoltronasResponse(
            seat=poltrona, best_before=timezone.now() + timedelta(minutes=15), external_payload=cache_payload
        )

    def _bloquear_poltronas(
        self,
        travel_key,
        trecho_classe_id,
        poltronas,
        poltronas_selecionadas,
        poltronas_bloqueadas=None,
        poltronas_para_desbloquear=None,
        categoria_especial=Passagem.CategoriaEspecial.NORMAL,
    ):
        poltronas_bloqueadas = [] if poltronas_bloqueadas is None else poltronas_bloqueadas
        poltronas_para_desbloquear = [] if poltronas_para_desbloquear is None else poltronas_para_desbloquear
        for poltrona in poltronas_selecionadas:
            if poltrona not in poltronas_bloqueadas:
                try:
                    self.bloqueia_poltrona(trecho_classe_id, travel_key, poltrona, categoria_especial)
                    poltronas_bloqueadas.append(poltrona)
                except (EulabsAPIError, EulabsPoltronaIndisponivel):
                    if not poltronas:
                        poltronas, travel_key = self.get_poltronas_disponiveis(trecho_classe_id, categoria_especial)
                    poltronas.set_ocupada(poltrona)
                    poltronas.filter(ocupada=False, preco_gt=0)
                    novas_poltronas_selecionadas = poltronas.select(quantidade=len(poltronas_selecionadas))
                    poltronas_para_desbloquear += [
                        p for p in poltronas_bloqueadas if p not in novas_poltronas_selecionadas
                    ]
                    return self._bloquear_poltronas(
                        travel_key,
                        trecho_classe_id,
                        poltronas,
                        novas_poltronas_selecionadas,
                        poltronas_bloqueadas,
                        poltronas_para_desbloquear,
                        categoria_especial,
                    )
        poltronas_para_desbloquear = list(
            set(
                filter(
                    lambda p: p not in poltronas_selecionadas,
                    poltronas_para_desbloquear,
                )
            )
        )
        if poltronas_para_desbloquear:
            async_desbloquear_poltronas.delay(self.company.id, trecho_classe_id, list(poltronas_para_desbloquear))
        return poltronas_selecionadas

    @lock("eulabs_bloqueia_poltrona_{trecho_classe_id}", expire=70)
    def bloqueia_poltrona(
        self, trecho_classe_id, travel_key, poltrona, categoria_especial=Passagem.CategoriaEspecial.NORMAL
    ):
        if self.cache.get_poltrona_key_cache(trecho_classe_id, poltrona):
            raise EulabsPoltronaIndisponivel("poltrona bloqueada")
        seat_key = self.bloqueia_poltrona_request(travel_key, poltrona, categoria_especial)
        self.cache.set_poltrona_key_cache(trecho_classe_id, poltrona, seat_key)
        return seat_key

    @retry(
        retry=retry_if_exception_type(RodoviariaConnectionError),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def bloqueia_poltrona_request(self, travel_key, poltrona, categoria_especial=Passagem.CategoriaEspecial.NORMAL):
        executor = get_http_executor()
        categoria_especial_external = self.PASSAGEM_TYPE_MAP.get(categoria_especial)
        json = models.BloquearPoltronaRequestForm(seat_id=poltrona, tipo=categoria_especial_external).request_body
        request_config = endpoints.BloquearPoltronaConfig(self.login, travel_key=travel_key)
        response = request_config.invoke(executor, json=json)
        response_json = response.json()
        seat_key = models.BloquearPoltrona(**response_json).selected_seat_key
        return seat_key

    def desbloquear_poltronas(self, trecho_classe_id, poltronas):
        travel_key = self.cache.get_travel_key_cache(trecho_classe_id)
        if not travel_key:
            return
        for poltrona in poltronas:
            seat_key = self.cache.get_poltrona_key_cache(trecho_classe_id, poltrona)
            if seat_key:
                _desbloquear_poltronas_request(self.login, travel_key, seat_key)
                self.cache.delete_poltrona_key_cache(trecho_classe_id, poltrona)
        return {}

    def get_desenho_mapa_poltronas(self, trecho_classe_id: int) -> Onibus:
        (poltronas_por_andar, classe_reduzida_api, classe_completa_api, travel_key) = (
            self._get_poltronas_classe_and_trechoclasse_v2(trecho_classe_id)
        )
        tipo_assento_buser = class_match_svc.get_buser_class_by_company(self.company, classe_completa_api)

        decks = []
        for andar, poltronas in enumerate(poltronas_por_andar):
            seats = []
            poltronas.filter(classe=classe_reduzida_api)
            for poltrona in poltronas:
                # categoria = (
                #     self.CATEGORIA_ESPECIAL_MAP.get(poltrona.beneficios_permitidos[0].tipo)
                #     if any(poltrona.beneficios_permitidos)
                #     else Passagem.CategoriaEspecial.NORMAL
                # )
                categoria = Passagem.CategoriaEspecial.NORMAL

                seats.append(
                    {
                        "livre": not poltrona.ocupada,
                        "y": poltrona.y,
                        # mantem o padrao de layout onde o X == 3 indica o corredor
                        "x": poltrona.x + 1 if poltrona.x > 2 else poltrona.x,
                        "numero": poltrona.numero,
                        "tipo_assento": tipo_assento_buser,
                        "categoria_especial": categoria,
                        "preco": poltrona.preco,
                    }
                )
            decks.append(Deck(andar=andar + 1, assentos=parse_obj_as(list[Assento], seats)))
        layout_onibus = Onibus(layout=decks)

        self._muda_map_para_ocupado_poltronas_from_db_v2(trecho_classe_id, layout_onibus)
        self.cache.set_travel_key_cache(trecho_classe_id, travel_key)
        return layout_onibus

    def get_map_poltronas(self, trecho_classe_id):
        poltronas, classe_api, travel_key = self._get_poltronas_classe_and_trechoclasse(trecho_classe_id)
        poltronas.filter(classe=classe_api)
        poltronas_map = poltronas.map()
        poltronas_map = self._muda_para_ocupado_poltronas_from_db(trecho_classe_id, poltronas_map)
        self.cache.set_travel_key_cache(trecho_classe_id, travel_key)
        return poltronas_map

    @retry(
        retry=retry_if_exception_type(EulabsSeatingMapError),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def _get_poltronas_classe_and_trechoclasse(self, trecho_classe_id):
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        travel_key, classe_reduzida_api, _ = self._get_viagem_key_and_class(trecho_classe)

        executor = get_http_executor()
        request_config = endpoints.RetornaPoltronasConfig(self.login, travel_key=travel_key)
        response = request_config.invoke(executor)
        response_json = response.json()
        poltronas = _get_flat_list_poltronas(response_json)
        poltronas = parse_obj_as(models.PoltronasForm, poltronas)
        return poltronas, classe_reduzida_api, travel_key

    @retry(
        retry=retry_if_exception_type(EulabsSeatingMapError),
        reraise=True,
        stop=stop_after_attempt(2),
    )
    def _get_poltronas_classe_and_trechoclasse_v2(self, trecho_classe_id):
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        travel_key, classe_reduzida_api, classe_completa_api = self._get_viagem_key_and_class(trecho_classe)

        executor = get_http_executor()
        request_config = endpoints.RetornaPoltronasConfig(self.login, travel_key=travel_key)
        response = request_config.invoke(executor)
        response_json = response.json()

        def _get_list_poltronas(response, andar):
            list_poltronas = []
            for onibus in response:
                list_poltronas += onibus.get(andar, [])
            return list_poltronas

        poltronas = [
            parse_obj_as(models.PoltronasForm, _get_list_poltronas(response_json, "floor_1")),
            parse_obj_as(models.PoltronasForm, _get_list_poltronas(response_json, "floor_2")),
        ]
        return poltronas, classe_reduzida_api, classe_completa_api, travel_key

    def _reserva_dict_to_comprar_params(self, params: ComprarForm, trecho_classe: TrechoClasse):
        travel_id = params.travel_id
        item_id = trecho_classe.external_id.split("/")[0]
        preco_rodoviaria = D(str(trecho_classe.preco_rodoviaria))
        valor_cheio = D(str(params.valor_cheio))
        categoria_especial = params.categoria_especial or Passagem.CategoriaEspecial.NORMAL

        passagens = self.create_passagens(
            [
                Passagem(
                    trechoclasse_integracao=trecho_classe,
                    company_integracao=self.company,
                    poltrona_external_id=poltrona,
                    buseiro_internal_id=passageiro.id,
                    travel_internal_id=travel_id,
                    valor_cheio=valor_cheio,
                    status=Passagem.Status.INCOMPLETA,
                    preco_rodoviaria=preco_rodoviaria,
                    categoria_especial=categoria_especial,
                )
                for passageiro, poltrona in zip(params.passageiros, params.poltronas)
            ]
        )

        requests = []
        for passagem, passageiro in zip(passagens, params.passageiros):
            utilizer = self._make_utilizer_dict(categoria_especial, passageiro)
            item_params = {
                "road": {
                    "item_id": item_id,
                    "utilizer": utilizer,
                    "with_return": True,
                }
            }

            requests.append(
                {
                    "poltrona": passagem.poltrona_external_id,
                    "confirmar_venda_params": item_params,
                    "passagem": passagem,
                }
            )

        return requests

    def _make_utilizer_dict(self, categoria_especial: Passagem.CategoriaEspecial, passageiro: PassageiroForm):
        names = passageiro.name.split(" ")
        utilizer = {
            "cpf": passageiro.cpf,
            "document_number": passageiro.rg_number,
            "document_type": "RG",
            "first_name": names[0],
            "last_name": names[-1],
            "full_name": passageiro.name,
            "locate_country_id": 1,
            "phone": passageiro.phone,
        }
        if passageiro.birthday:
            utilizer["birth"] = passageiro.birthday.isoformat()
        if categoria_especial != Passagem.CategoriaEspecial.NORMAL and not passageiro.dados_beneficio:
            raise CampoObrigatorioException("Necessário informar 'dados_beneficio' para venda ESPECIAL")
        if categoria_especial in (Passagem.CategoriaEspecial.IDOSO_50, Passagem.CategoriaEspecial.IDOSO_100):
            renda = passageiro.dados_beneficio.renda
            data_expiracao = passageiro.dados_beneficio.data_expiracao
            if not renda or not data_expiracao:
                raise CampoObrigatorioException("Necessário informar 'renda' e 'data_expiracao' para venda IDOSO")
            utilizer["gender"] = "m"
            utilizer["benefit"] = {
                "type": self.PASSAGEM_TYPE_MAP[categoria_especial],
                "income": renda,
                "benefit_expiration": data_expiracao.isoformat(),
            }
        if categoria_especial in (Passagem.CategoriaEspecial.JOVEM_100, Passagem.CategoriaEspecial.JOVEM_50):
            numero_beneficio = passageiro.dados_beneficio.numero_beneficio
            data_expedicao = passageiro.dados_beneficio.data_expedicao
            data_expiracao = passageiro.dados_beneficio.data_expiracao
            if not numero_beneficio or not data_expedicao or not data_expiracao:
                raise CampoObrigatorioException(
                    "Necessário informar 'numero_beneficio', 'data_expedicao' e 'data_expiracao' para venda JOVEM"
                )
            utilizer["benefit"] = {
                "type": self.PASSAGEM_TYPE_MAP[categoria_especial],
                "benefit_number": numero_beneficio,
                "benefit_issue_date": data_expedicao.isoformat(),
                "benefit_expiration": data_expiracao.isoformat(),
                "enabled": True,
            }
        if categoria_especial in (Passagem.CategoriaEspecial.PCD):
            numero_beneficio = passageiro.dados_beneficio.numero_beneficio
            data_expedicao = passageiro.dados_beneficio.data_expedicao
            data_expiracao = passageiro.dados_beneficio.data_expiracao
            tipo_passe_livre = passageiro.dados_beneficio.tipo_passe_livre
            if not numero_beneficio or not data_expedicao or not data_expiracao or not tipo_passe_livre:
                raise CampoObrigatorioException(
                    "Necessário informar 'numero_beneficio', 'data_expedicao',"
                    " 'data_expiracao' e 'tipo_passe_livre' para venda PCD"
                )
            utilizer["benefit"] = {
                "type": self.PASSAGEM_TYPE_MAP[categoria_especial],
                "benefit_number": numero_beneficio,
                "benefit_expiration": data_expiracao.isoformat(),
                "boarding_aid": bool(passageiro.dados_beneficio.auxilio_embarque),
                "required_companion": False,
                "validity_begin": data_expedicao.isoformat(),
                "jurisdiction": tipo_passe_livre,
            }
            if passageiro.dados_beneficio.auxilio_embarque:
                utilizer["benefit"]["boarding_aid_type"] = "cadeirante"
        return utilizer

    @lock("update_travel_key_{trecho_classe_internal_id}", expire=15)
    def get_or_update_travel_key(self, trecho_classe, trecho_classe_internal_id):  # TODO: remover parâmetro redundante
        travel_key = self.cache.get_travel_key_cache(trecho_classe_internal_id)
        if not travel_key:
            travel_key, _, _ = self._get_viagem_key_and_class(trecho_classe)
        self.cache.set_travel_key_cache(trecho_classe_internal_id, travel_key)
        return travel_key

    @lock("compra_rodoviaria_{params.travel_id}", max_wait_time=0, except_timeout=True)
    def comprar(self, params: ComprarForm, from_add_pax=None):
        trecho_classe = self.get_active_trecho_classe(params.trechoclasse_id)
        try:
            requests = self._reserva_dict_to_comprar_params(params, trecho_classe)
        except CampoObrigatorioException:
            if params.extra_poltronas:
                raise

            seat_keys_map = {
                str(poltrona): cache_key
                for poltrona in params.poltronas
                if (cache_key := self.cache.get_poltrona_key_cache(params.trechoclasse_id, poltrona))
            }
            if seat_keys_map:
                travel_key = self.get_or_update_travel_key(trecho_classe, params.trechoclasse_id)
                async_desbloquear_poltronas_by_travel_key.delay(
                    self.company.id, travel_key, seat_keys_map, params.trechoclasse_id
                )
            raise
        travel_key = self.get_or_update_travel_key(trecho_classe, params.trechoclasse_id)
        items = []
        passagens_map_by_poltronas = {}
        seat_keys_list = []
        for reserve in requests:
            if params.extra_poltronas:
                seat_key = str(params.extra_poltronas)
            else:
                seat_key = self.cache.get_poltrona_key_cache(params.trechoclasse_id, reserve["poltrona"])
                if not seat_key:
                    [poltrona] = self._bloquear_poltronas(
                        travel_key=travel_key,
                        trecho_classe_id=params.trechoclasse_id,
                        poltronas=None,
                        poltronas_selecionadas=[reserve["poltrona"]],
                    )
                    reserve["passagem"].poltrona_external_id = poltrona
                    reserve["poltrona"] = poltrona
                    seat_key = self.cache.get_poltrona_key_cache(params.trechoclasse_id, reserve["poltrona"])

            seat_keys_list.append(seat_key)
            reserve["confirmar_venda_params"]["road"]["selected_seat_key"] = seat_key
            items.append(reserve["confirmar_venda_params"])
            passagens_map_by_poltronas[str(reserve["poltrona"])] = reserve["passagem"]

        try:
            executor = get_http_executor()
            request_config = endpoints.EfetuarReservaConfig(self.login)
            response = request_config.invoke(
                executor,
                params={"device_type": "desktop"},
                json={"items": items},
            )
            response_json = response.json()
            comprar_response = parse_obj_as(models.CompraForm, response_json)
            dict_provider_data = self._retorna_provider_data(response_json)
        except (RodoviariaBaseException, PassengerInvalidDocumentException) as ex:
            self._save_passagem_error(passagens_map_by_poltronas.values(), error_str(ex))
            if params.extra_poltronas:
                raise
            seat_keys_map = {
                str(poltrona): cache_key
                for poltrona in params.poltronas
                if (cache_key := self.cache.get_poltrona_key_cache(params.trechoclasse_id, poltrona))
            }
            if not isinstance(ex, RodoviariaConnectionError):
                async_desbloquear_poltronas_by_travel_key.delay(
                    self.company.id, travel_key, seat_keys_map, params.trechoclasse_id
                )
            raise

        for passagem_api in comprar_response.passagens:
            passagem = passagens_map_by_poltronas.get(str(passagem_api.poltrona))
            if not passagem:
                rodovlogger.error(
                    "not passagem in passagens_map_by_poltronas",
                    extra={
                        "passagem_api_poltrona": passagem_api.poltrona,
                        "pedido_id": comprar_response.pedido_id,
                        "passagem_key": passagem_api.passagem_key,
                        "passagens_map_by_poltronas_keys": list(passagens_map_by_poltronas.keys()),
                        "passagens_map_by_poltronas": passagens_map_by_poltronas,
                    },
                )
            passagem.pedido_external_id = comprar_response.pedido_id
            passagem.localizador = passagem_api.localizador
            passagem.numero_passagem = passagem_api.passagem_key
            passagem.preco_poltrona = passagem_api.preco
            passagem.provider_data = dict_provider_data.get(passagem_api.passagem_key)
            passagem.status = Passagem.Status.CONFIRMADA
            passagem.updated_at = to_default_tz(datetime.now())
            passagem.tags.add("dados_bpe_pendente")
        Passagem.objects.bulk_update(
            passagens_map_by_poltronas.values(),
            [
                "pedido_external_id",
                "localizador",
                "numero_passagem",
                "provider_data",
                "status",
                "preco_poltrona",
                "poltrona_external_id",
                "updated_at",
            ],
        )
        buscar_dados_bpe.delay([p.id for p in passagens_map_by_poltronas.values()], self.company.id)  # type: ignore
        return {"passagens": [p.to_dict_json() for p in passagens_map_by_poltronas.values()]}

    def _save_passagem_error(self, passagens, error):
        for passagem in passagens:
            passagem.erro = error
            passagem.status = Passagem.Status.ERRO
        Passagem.objects.bulk_update(passagens, ["erro", "status", "pedido_external_id", "updated_at"])

    def cancela_venda(self, params):
        passagens = self.get_passagens_confirmadas(params.travel_id, params.buseiro_id)
        try:
            return self._cancelar_passagens(passagens)
        except RodoviariaException as ex:
            self._save_error_cancelamento_passagens(passagens, error_str(ex))
            raise

    def _cancela_venda(self, cancel_key, cancel_type):
        executor = get_http_executor()
        request_config = endpoints.CancelaVendaConfig(self.login, cancel_key, cancel_type)
        response = request_config.invoke(
            executor,
        )
        response = response.json()
        return response

    @retry(
        retry=retry_if_exception_type(EulabsVendaNaoEncontrada),
        reraise=True,
        stop=stop_after_attempt(3),
    )
    def _consultar_reserva_request(self, pedido_external_id: int) -> list[models.ReservaForm]:
        executor = get_http_executor()
        request_config = endpoints.ConsultaReservaConfig(self.login, pedido_external_id)
        response = request_config.invoke(
            executor,
        )
        response = response.json()
        items = response.get("items") or []
        return parse_obj_as(list[models.ReservaForm], items)

    @retry(
        retry=retry_if_exception_type(EulabsVendaNaoEncontrada),
        reraise=True,
        stop=stop_after_attempt(3),
    )
    def _consultar_bpe_request(self, pedido_external_id: int, numero_passagem: str) -> list[models.ReservaForm]:
        executor = get_http_executor()
        request_config = endpoints.ConsultaBpeConfig(self.login, pedido_external_id, numero_passagem)
        response = request_config.invoke(
            executor,
        )
        response = response.json()
        items = response.get("items") or []
        return parse_obj_as(list[models.ReservaForm], items)

    def _cancelar_passagens(self, passagens):
        cancel_key_map = {}
        responses = []
        passagens_api_map = {}
        for passagem in passagens:
            try:
                condicoes_cancelamento = self._condicoes_cancelamento(passagem.numero_passagem)
            except EulabsNaoPodeCancelar as ex:
                if not passagens_api_map.get(passagem.pedido_external_id):
                    try:
                        response = self._consultar_reserva_request(passagem.pedido_external_id)
                        passagens_api_map[passagem.pedido_external_id] = response
                    except RodoviariaConnectionError as exc:
                        raise ex from exc
                passagens_api = passagens_api_map[passagem.pedido_external_id]
                passagem_api = next(
                    (p for p in passagens_api if p.passagem_key == passagem.numero_passagem),
                    None,
                )
                if passagem_api and passagem_api.viagem.status == "canceled":
                    rodovlogger.info(
                        "Eulabs - Tentativa de cancelamento de passagem já cancelada na API: id=%s", passagem.id
                    )
                    responses.append(self._response_passagem_ja_cancelada(passagem))
                    passagem.save_canceled()
                    continue
                if passagem_api and passagem_api.viagem.status == "returned":
                    rodovlogger.info(
                        "Eulabs - Tentativa de cancelamento de passagem já devolvida na API: id=%s", passagem.id
                    )
                    passagem.save_returned()
                    raise PoltronaTrocadaException("Passagem trocada no guichê.") from ex

                raise PassengerTicketAlreadyPrintedException from ex
            if condicoes_cancelamento.status != "authorized":
                raise RodoviariaException("Cancelamento não autorizado")
            if condicoes_cancelamento.tipos_cancelamento and "Cancel" in condicoes_cancelamento.tipos_cancelamento:
                cancel_type = "Cancel"
            elif (
                condicoes_cancelamento.tipos_cancelamento and "Devolution" in condicoes_cancelamento.tipos_cancelamento
            ):
                cancel_type = "Devolution"
            else:
                raise RodoviariaException("Cancelamento não autorizado")
            cancel_key_map[passagem] = cancel_type, condicoes_cancelamento.key

        for passagem, (cancel_type, cancel_key) in cancel_key_map.items():
            response = self._cancela_venda(cancel_key, cancel_type)
            responses.append(response)
            passagem.save_canceled()
        return responses

    def _buscar_viagens_por_periodo(self, data_inicio, data_fim):
        data_inicial_str = data_inicio.strftime("%Y-%m-%d")
        data_final_str = data_fim.strftime("%Y-%m-%d")
        params = {
            "initial_departure_date": data_inicial_str,
            "final_departure_date": data_final_str,
        }
        executor = get_http_executor()
        request_config = endpoints.BuscarViagensPorPeriodoConfig(self.login)
        response = request_config.invoke(
            executor,
            params=params,
        )
        response_json = response.json()
        return parse_obj_as(models.Viagens, response_json) if response_json else None

    def _response_passagem_ja_cancelada(self, passagem):
        return {
            "travel_id": passagem.travel_internal_id,
            "buseiro_id": passagem.buseiro_internal_id,
            "error": "passagem já cancelada",
        }

    def itinerario(self, external_id, datetime_ida=None):
        itinerario = self.buscar_itinerario({"item_id": external_id})
        return itinerario

    def descobrir_rotas_async(self, next_days, shift_days, queue_name, return_task_object, modelo_venda):
        return descobrir_rotas_eulabs_async_svc.descobrir_rotas(
            self.login,
            self.company.company_internal_id,
            next_days,
            shift_days,
            queue_name,
            return_task_object,
        )

    def descobrir_operacao_async(self, next_days, shift_days, queue_name, return_task_object):
        return descobrir_operacao.descobrir_operacao(self.login, next_days, shift_days, queue_name, return_task_object)

    def buscar_servicos_por_data(self, data_inicio, data_fim):
        if (data_fim - data_inicio).days > 30:
            raise ValueError("Período de busca não pode ser superior a 30 dias")
        servicos = self._buscar_viagens_por_periodo(data_inicio, data_fim)
        if not servicos:
            return {}

        map_servicos_data = {}
        for servico in servicos:
            id_viagem = servico.rota_external_id
            data_existente = map_servicos_data.get(id_viagem, {"data": datetime.min})["data"]
            if servico.datetime_ida > data_existente:
                map_servicos_data[id_viagem] = {
                    "item_id": servico.item_id,
                    "data": servico.datetime_ida,
                }

        return {map_servicos_data[x]["item_id"]: map_servicos_data[x]["data"] for x in map_servicos_data}

    def _condicoes_cancelamento(self, passagem_key):
        executor = get_http_executor()
        request_config = endpoints.CondicoesCancelamentoConfig(self.login, passagem_key)
        response = request_config.invoke(
            executor,
        )
        response_json = response.json()
        condicoes = parse_obj_as(models.CondicaoCancelamentoForm, response_json)
        return condicoes

    def cancelar_reservas_por_pedido_id(self, pedido_id):
        passagens_parsed = self._consultar_reserva_request(pedido_id)
        responses = []
        for p in passagens_parsed:
            passagem_api = {"poltrona": p.viagem.poltrona, "numero_passagem": p.passagem_key}
            responses.append(passagem_api)
            try:
                cancel_condition = self._condicoes_cancelamento(p.passagem_key)
                if cancel_condition.status != "authorized":
                    passagem_api["error"] = "Cancelamento não autorizado"
                    passagem_api["error_type"] = "nao_autorizado"
                    continue
                if cancel_condition.tipos_cancelamento and "Cancel" in cancel_condition.tipos_cancelamento:
                    cancel_type = "Cancel"
                elif cancel_condition.tipos_cancelamento and "Devolution" in cancel_condition.tipos_cancelamento:
                    cancel_type = "Devolution"
                else:
                    passagem_api["error"] = "Tipo de cancelamento não encontrado"
                    passagem_api["error_type"] = "nao_autorizado"
                    continue
                response = self._cancela_venda(cancel_condition.key, cancel_type)
                passagem_api["cancel_response"] = response
            except EulabsNaoPodeCancelar as ex:
                passagem_api["error"] = error_str(ex)
                passagem_api["error_type"] = "nao_autorizado"
                continue
            except (EulabsAPIError, RodoviariaConnectionError) as ex:
                passagem_api["error"] = error_str(ex)
                passagem_api["error_type"] = None
                continue
        return responses

    def _retorna_provider_data(self, response_raw):
        provider_data = copy.deepcopy(response_raw)
        itens = provider_data.pop("items") or []
        dict_provider_data = {}

        for item in itens:
            dict_provider_data[item["key"]] = {"item": item, **provider_data}

        return dict_provider_data

    def get_atualizacao_passagem_api_parceiro(self, passagem):
        sale_id = passagem.pedido_external_id
        numero_passagem = passagem.numero_passagem
        try:
            passagens = self._consultar_reserva_request(sale_id)
        except RodoviariaBaseException:
            rodovlogger.exception(
                "Eulabs - Erro ao consultar reserva na atualização de passagens no staff com sale_id=%s", sale_id
            )
            raise
        for passagem in passagens:
            if passagem.passagem_key != numero_passagem:
                continue
            passagem_utilizers = passagem.passageiros[0]
            bilhete_padrao = RetornoConsultarBilheteForm.parse_obj(
                {
                    "integracao": "Eulabs",
                    "numero_passagem": passagem.passagem_key,
                    "sale_id": passagem.pedido_id,
                    "localizador": passagem.localizador,
                    "status": passagem.viagem.status,
                    "numero_assento": passagem.viagem.poltrona,
                    "primeiro_nome_pax": passagem_utilizers.primeiro_nome,
                    "ultimo_nome_pax": passagem_utilizers.ultimo_nome,
                    "tipo_documento": passagem_utilizers.tipo_documento,
                    "numero_documento": passagem_utilizers.numero_documento,
                    "birth": passagem_utilizers.nascimento,
                    "bpe_id": passagem.bpe.qrcode,
                    "data_partida": passagem.viagem.datetime_ida.isoformat(),
                    "data_chegada": passagem.viagem.datetime_chegada.isoformat(),
                    "origem": passagem.viagem.embarque.nome,
                    "estado_origem": passagem.viagem.embarque.local.estado.uf,
                    "destino": passagem.viagem.desembarque.nome,
                    "estado_destino": passagem.viagem.desembarque.local.estado.uf,
                    "duracao": passagem.viagem.duracao,
                    "empresa_name": passagem.viagem.empresa.nome,
                    "valor_passagem": passagem.preco.total,
                    "taxa_embarque": passagem.preco.embarque,
                }
            )
            return bilhete_padrao
        raise PassengerNotRegistered(f"Eulabs - Não foi possível encontrar a passagem com {numero_passagem=}")

    def vagas_por_categoria_especial(self, trechoclasse_id):
        trecho_classe = self.get_active_trecho_classe(trechoclasse_id)
        request_params = {
            "origem": trecho_classe.origem.id_external,
            "destino": trecho_classe.destino.id_external,
            "data": trecho_classe.datetime_ida.strftime("%Y-%m-%d"),
        }
        corridas = self._buscar_corridas_request(request_params)
        corridas = parse_obj_as(models.CorridasForm, corridas)
        corridas.filter(company_external_id=self.company.company_external_id)
        corrida = next((c for c in corridas if c.external_id == trecho_classe.external_id), None)
        if not corrida:
            return {}
        assentos_por_categoria = {}
        if corrida.beneficios:
            assentos_por_categoria = {
                self.CATEGORIA_ESPECIAL_MAP[b.tipo]: b.vagas
                for b in corrida.beneficios
                if self.CATEGORIA_ESPECIAL_MAP.get(b.tipo)
            }
        assentos_por_categoria[Passagem.CategoriaEspecial.NORMAL] = corrida.vagas
        return assentos_por_categoria

    def buscar_trechos_vendidos(self, item_id):
        executor = get_http_executor()
        request_config = endpoints.BuscarTrechosVendidosViagemConfig(self.login, item_id=item_id)
        response = request_config.invoke(executor)
        response_raw = response.json()
        return parse_obj_as(list[models.TrechoVendidoModel], response_raw)


@shared_task(queue=DefaultQueueNames.DESBLOQUEAR_POLTRONAS)
def async_desbloquear_poltronas(company_rodoviaria_id, trecho_classe_id, poltronas):
    company = Company.objects.get(id=company_rodoviaria_id)
    api = EulabsAPI(company)
    api.desbloquear_poltronas(trecho_classe_id, poltronas)


@shared_task(queue=DefaultQueueNames.DESBLOQUEAR_POLTRONAS)
def async_desbloquear_poltronas_by_travel_key(company_rodoviaria_id, travel_key, seat_keys_map, trecho_classe_id):
    company = Company.objects.get(id=company_rodoviaria_id)
    api = EulabsAPI(company)
    for poltrona, seat_key in seat_keys_map.items():
        _desbloquear_poltronas_request(api.login, travel_key, seat_key)
        rodovlogger.info(
            "Eulabs - Poltrona desbloqueada para o trecho_classe %s. travel_key = %s e seat_key = %s",
            trecho_classe_id,
            travel_key,
            seat_key,
        )
        api.cache.delete_poltrona_key_cache(trecho_classe_id, int(poltrona))


def _get_flat_list_poltronas(response):
    list_poltronas = []
    for onibus in response:
        list_poltronas += onibus["floor_1"]
        if onibus.get("floor_2"):
            list_poltronas += onibus["floor_2"]
    return list_poltronas


def _desbloquear_poltronas_request(login, travel_key, selected_seat_key):
    executor = get_http_executor()
    request_config = endpoints.DesbloquearPoltronaConfig(login, travel_key, selected_seat_key)
    request_config.invoke(executor)


def order_itinerario_by_datetime(response):
    return sorted(
        response,
        key=lambda d: datetime.strptime(d["local_exit"], "%Y-%m-%dT%H:%M:%SZ"),
    )


@shared_task(queue=DefaultQueueNames.BUSCAR_DADOS_BPE, rate_limit=DefaultRateLimits.BUSCAR_DADOS_BPE)
def buscar_dados_bpe(passagens_ids: list[int], company_rodoviaria_id: int):
    passagens = list(Passagem.objects.filter(id__in=passagens_ids))
    company = Company.objects.get(id=company_rodoviaria_id)
    for passagem in passagens:
        try:
            _buscar_dados_bpe_passagem(passagem, company)
        except Exception as ex:
            rodovlogger.exception(
                "EULABS - ERRO ao buscar e salvar dados do BPe da passagem",
                extra={"error": error_str(ex), "passagem_id": passagem.id, "pedido_id": passagem.pedido_external_id},
            )


def _consultar_dados_bpe_endpoints(passagem: Passagem, company: Company):
    """
    Função responsável por fazer as chamadas aos endpoints para buscar dados do BPE.

    Returns:
        tuple: (passagem_bpe_api, reserva_api) ou (None, None) em caso de erro
    """
    try:
        passagem_bpe_api = EulabsAPI(company)._consultar_bpe_request(
            cast(int, passagem.pedido_external_id), cast(str, passagem.numero_passagem)
        )[0]

        reserva_api = EulabsAPI(company)._consultar_reserva_request(cast(int, passagem.pedido_external_id))
        reserva_api = next((r for r in reserva_api if r.passagem_key == passagem.numero_passagem), None)

        return passagem_bpe_api, reserva_api
    except (RodoviariaConnectionError, RodoviariaException, ValidationError, NotEnoughTokens) as ex:
        rodovlogger.info(
            "EULABS - ERRO ao buscar dados do BPe da passagem",
            extra={"error": error_str(ex), "passagem_id": passagem.id, "pedido_id": passagem.pedido_external_id},
        )
        return None, None


def _atualizar_campos_bpe_passagem(passagem: Passagem, passagem_bpe_api, reserva_api):
    """
    Função responsável por atualizar os campos da passagem com os dados obtidos dos endpoints.
    """
    passagem.linha = passagem_bpe_api.viagem.linha.descricao
    passagem.prefixo = passagem_bpe_api.viagem.linha.prefixo
    passagem.outros_tributos = passagem_bpe_api.viagem.tributos.format()
    _set_preco_fields(passagem, passagem_bpe_api, reserva_api)
    if passagem_bpe_api.bpe.chave:
        passagem.chave_bpe = passagem_bpe_api.bpe.chave
        passagem.data_autorizacao = passagem_bpe_api.bpe.data_autorizacao
        passagem.protocolo_autorizacao = passagem_bpe_api.bpe.autorizacao
        passagem.numero_bpe, passagem.serie_bpe = _get_numero_serie_bpe(passagem_bpe_api)
        passagem.bpe_qrcode = _make_bpe_qrcode(passagem_bpe_api)
    if passagem_bpe_api.viagem.embarque_eletronico:
        if passagem_bpe_api.viagem.embarque_eletronico.qrcode:
            # esse campo é um base64, alterar quando eles mudarem a API
            passagem.embarque_eletronico = passagem_bpe_api.make_embarque_eletronico()
    if reserva_api and reserva_api.viagem.embarque_eletronico.ticket_embarque.numero:
        passagem.numero_bilhete_embarque = reserva_api.viagem.embarque_eletronico.ticket_embarque.numero
        passagem.tipo_taxa_embarque = Passagem.TipoTaxaEmbarque.QRCODE
    passagem.updated_at = timezone.now()


def _buscar_dados_bpe_passagem(passagem: Passagem, company: Company):
    passagem_bpe_api, reserva_api = _consultar_dados_bpe_endpoints(passagem, company)
    if passagem_bpe_api is None:
        return

    _atualizar_campos_bpe_passagem(passagem, passagem_bpe_api, reserva_api)
    passagem.save()
    passagem.tags.remove("dados_bpe_pendente")


def _set_preco_fields(passagem, passagem_bpe_api, reserva_api):
    # total estar 0 significa que o endpoint não retornou os valores
    # então usa como fallback o endpoint antigo.
    if passagem_bpe_api.preco.total == D("0.00"):
        passagem.preco_base = reserva_api.preco.base
        passagem.taxa_embarque = reserva_api.preco.embarque
        passagem.seguro = reserva_api.preco.seguro
        passagem.pedagio = reserva_api.preco.pedagio
        passagem.outras_taxas = reserva_api.preco.outras_taxas
        passagem.desconto = reserva_api.preco.desconto
        return
    passagem.preco_base = passagem_bpe_api.preco.base
    passagem.taxa_embarque = passagem_bpe_api.preco.embarque
    passagem.seguro = passagem_bpe_api.preco.seguro
    passagem.pedagio = passagem_bpe_api.preco.pedagio
    passagem.outras_taxas = passagem_bpe_api.preco.outras_taxas
    passagem.desconto = passagem_bpe_api.preco.desconto


def _make_bpe_qrcode(passagem_api):
    bpe_url = passagem_api.bpe.bpe_url.replace("/Consulta", "")
    return f"{bpe_url}/qrcode?chBPe={passagem_api.bpe.chave}&tpAmb=1"


def _get_numero_serie_bpe(passagem_api):
    # localizer = 00001-BPE1005BA-002-0000000615
    localizer = passagem_api.localizador
    serie = localizer.split("-")[2]
    numero = localizer.split("-")[3]
    return numero, serie

from . import models
from .base import BaseRequest, BaseRequestCollection


class <PERSON>car<PERSON><PERSON>ens(BaseRequestCollection):
    method = "get"
    path = "externalsale/getLocations"
    output_model = models.Local
    clean_param = "locations"

    def send(self, params):
        return super().send(params=params)


class BuscarCorridas(BaseRequest):
    method = "post"
    path = "externalsale/getTrips"
    output_model = models.CorridasResponse
    data_saida = None

    def send(self, origem_id, destino_id, data_str):
        self.data_saida = data_str
        params = {"departureLocation": origem_id, "arrivalLocation": destino_id, "departureDate": data_str}
        return super().send(json=params)

    def clean_response(self, response):
        cleaned_response = response["data"] or []
        # Api não retorna a data de saida, uma vez que a busca é para um dia apenas.
        # Por isso, adicionamos a data de saida ao json de resposta de cada viagem
        for c in cleaned_response:
            c["data_saida"] = self.data_saida
        return cleaned_response


class DetalhesCorrida(BaseRequest):
    method = "post"
    path = "externalsale/getTripDetails"
    output_model = models.DetalhesCorridaResponse

    def send(self, origem_id, destino_id, data_str, external_id):
        params = {
            "departureLocation": origem_id,
            "arrivalLocation": destino_id,
            "departureDate": data_str,
            "controlNumber": external_id,
            "passengerType": 1,
        }
        return super().send(json=params)


class DetalhesRota(BaseRequest):
    method = "post"
    path = "externalsale/getRouteDetails"
    output_model = models.DetalhesRotaResponse
    data_saida = None

    def send(self, origem_id, destino_id, data_str, external_id):
        self.data_saida = data_str
        params = {
            "departureLocation": origem_id,
            "arrivalLocation": destino_id,
            "departureDate": data_str,
            "controlNumber": external_id,
            "passengerType": 1,
        }
        return super().send(json=params)

    def clean_response(self, response):
        cleaned_response = response["data"]
        # Api não retorna a data de saida, uma vez que a busca é para um dia apenas.
        # Por isso, adicionamos a data de saida ao json de resposta de cada viagem
        # TODO: aqui não é lugar de hidratar os dados que a API retorna
        cleaned_response["data_saida"] = self.data_saida
        return cleaned_response


class BloqueiaPoltronas(BaseRequestCollection):
    method = "post"
    path = "externalsale/bookSeat"
    output_model = models.PoltronaBloqueada

    def send(self, origem_id, destino_id, data_str, external_id, poltronas):
        params = {
            "departureLocation": origem_id,
            "arrivalLocation": destino_id,
            "departureDate": data_str,
            "controlNumber": external_id,
            "passengerSeatInfo": [{"passengerType": 1, "seatIdentifier": p} for p in poltronas],
        }
        return super().send(json=params)


class DesbloqueiaPoltronas(BaseRequestCollection):
    method = "post"
    path = "externalsale/cancelBooking"
    output_model = models.Cancelamento

    def send(self, origem_id, destino_id, data_str, external_id, bloqueios):
        params = [
            {
                "departureLocation": origem_id,
                "arrivalLocation": destino_id,
                "departureDate": data_str,
                "controlNumber": external_id,
                "seatIdentifier": b["poltrona"],
                "transactionIdentifier": b["transacao"],
            }
            for b in bloqueios
        ]
        return super().send(json={"params": params})


class EfetuarCompra(BaseRequest):
    method = "post"
    path = "externalsale/confirmBooking"
    output_model = models.CompraResponse

    def send(self, json_params):
        return super().send(json=json_params)


class CancelaPassagem(BaseRequestCollection):
    method = "post"
    path = "externalsale/cancelTicket"
    output_model = models.Cancelamento

    def send(self, origem_id, destino_id, data_str, external_id, passagens):
        params = [
            {
                "departureLocation": origem_id,
                "arrivalLocation": destino_id,
                "departureDate": data_str,
                "controlNumber": external_id,
                "seatIdentifier": p["poltrona_external_id"],
                "transactionIdentifier": p["localizador"],
            }
            for p in passagens
        ]
        return super().send(json={"params": params})


class DevolvePassagem(BaseRequestCollection):
    method = "post"
    path = "externalsale/refundTicket"
    output_model = models.Cancelamento

    def send(self, origem_id, destino_id, data_str, external_id, passagens):
        params = [
            {
                "departureLocation": origem_id,
                "arrivalLocation": destino_id,
                "departureDate": data_str,
                "controlNumber": external_id,
                "seatIdentifier": p["poltrona_external_id"],
                "transactionIdentifier": p["localizador"],
            }
            for p in passagens
        ]
        return super().send(json={"params": params})


class BuscarFormasPagamento(BaseRequestCollection):
    method = "get"
    path = "externalsale/getFormOfPayments"
    output_model = models.FormaPagamento

    def __init__(self, client):
        super().__init__(client)

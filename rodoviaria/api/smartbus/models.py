import copy
import hashlib
import re
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, root_validator, validator

from bp import settings
from rodoviaria.api.forms import ListBaseModel, Localidade


class SmartbusNoCompanyLogin(BaseModel):
    username: str
    password: str
    cliente: str

    @property
    def integracao(self):
        return "smartbus"


class SmartbusLoginForm(SmartbusNoCompanyLogin):
    forma_pagamento_id: Optional[int]


class SmartbusUndefinedCompany(BaseModel):
    id: None = None
    modelo_venda: str = "undefined"
    url_base: str = "http://{ambiente}-{cliente}-gateway-smartbus.oreons.com"


class SmartbusUndefinedCompanyClient(SmartbusNoCompanyLogin):
    company: SmartbusUndefinedCompany = SmartbusUndefinedCompany()

    @property
    def url_base(self):
        return self.company.url_base.format(ambiente=settings.SMARTBUS_API_ENVIRONMENT, cliente=self.cliente)


class LoginResponse(BaseModel):
    """
    {
        'access_token': 'access_token',
        'token_type': 'bearer',
        'expires_in': 31535999,
        'name': 'BUSER',
        'idGateway': 3008,
        'userName': 'buser',
        '.issued': 'Thu, 16 Mar 2023 16:46:38 GMT',
        '.expires': 'Fri, 15 Mar 2024 16:46:38 GMT'
    }
    """

    auth_token: str = Field(alias="access_token")
    expires_in: int


class Local(Localidade):
    """
    {
        "id": 1151,
        "code": "1151",
        "name": "PINGO-D'AGUA - MG",
        "description": null,
        "cityName": "PINGO-D'AGUA - MG",
        "stateCode": "MG",
        "countryCode": "BR",
        "lat": null,
        "lon": null,
        "pointsOfInterest": []
    },
    """

    @root_validator(pre=True)
    def translate(cls, v: dict):
        complemento = v.pop("name")
        # Remove o estado do nome da cidade
        nome_cidade = re.sub(r"\s-\s[a-zA-Z]{2}$", "", v["cityName"])
        return {
            "nome_cidade": nome_cidade,
            "external_local_id": v["id"],
            "uf": v["stateCode"],
            "complemento": complemento,
            "external_cidade_id": v["id"],
        }


class Corrida(BaseModel):
    data_saida: str
    external_id: str = Field(alias="controlNumber")
    horario_saida: str = Field(alias="departureTime")
    duracao_segundos: int
    preco_rodoviaria: Decimal = Field(alias="priceValue")
    provider_data: dict
    company_external_id: int = Field(alias="idCompany")
    tem_conexao: bool = Field(alias="hasConnection")
    tem_bpe: bool = Field(alias="bpe")
    vagas: int = Field(alias="availableSeats")
    rota_id: int = Field(alias="idSchedule")
    rota_diaria_id: int = Field(alias="idDailySchedule")
    numero_controle_rota: str = Field(alias="scheduleControlNumber")
    numero_controle_rota_diaria: str = Field(alias="controlNumberDailySchedule")
    classe: str = Field(alias="classOfServiceName")

    @property
    def datetime_ida(self):
        return datetime.strptime(f"{self.data_saida} {self.horario_saida}", "%Y-%m-%d %H:%M:%S")

    @property
    def datetime_chegada(self):
        return self.datetime_ida + timedelta(seconds=self.duracao_segundos)

    def normalized_dict(self):
        d = self.dict()
        d["datetime_ida"] = self.datetime_ida
        d["datetime_chegada"] = self.datetime_chegada
        return d


class CorridasResponse(ListBaseModel):
    __root__: List[Corrida]

    def dict(self):
        return [c.normalized_dict() for c in self.__root__]

    @validator("__root__", pre=True)
    def parse(cls, v):
        for corrida in v:
            horas, minutos, segundos = corrida["duration"].split(":")
            duracao = timedelta(hours=int(horas), minutes=int(minutos), seconds=int(segundos))
            corrida["provider_data"] = corrida.copy()
            corrida["duracao_segundos"] = duracao.total_seconds()
        return v


class Poltrona(BaseModel):
    numero: int = Field(alias="seatIdentifier")
    x: int = Field(alias="row")
    y: int = Field(alias="cell")
    andar: int = Field(alias="level")
    ocupada: bool = Field(alias="isUnavailable")


class Poltronas(ListBaseModel):
    __root__: List[Poltrona]

    def map(self):
        return {str(p.numero).zfill(2): "ocupada" if p.ocupada else "livre" for p in self.__root__}

    def vagas_disponiveis(self):
        return len([p for p in self.__root__ if p.ocupada is False])


class DetalhesCorridaResponse(BaseModel):
    poltronas: Poltronas

    @root_validator(pre=True)
    def nested_fields(cls, v):
        return {"poltronas": v["routes"][0]["seats"]}


class LocalidadeParada(Localidade):
    @root_validator(pre=True)
    def translate(cls, v: dict):
        nome, uf = v["name"].split(" - ")
        return {
            "nome_cidade": nome,
            "external_local_id": v["id"],
            "uf": uf,
            "complemento": None,
            "external_cidade_id": v["id"],
        }


class Parada(BaseModel):
    local: LocalidadeParada = Field(alias="departureLocation")
    hora_saida: str = Field(alias="departureTime")
    dias_passados: int = Field(alias="arrivalDay")
    datetime_ida: Optional[datetime]
    tempo_embarque: Optional[int]
    duracao: Optional[int]
    distancia: Optional[Decimal]


class DetalhesRotaResponse(BaseModel):
    """
    [{
        "departureTime": "18:00:00",
        "arrivalTime": "20:30:00",
        "duration": "02:30:00",
        "arrivalDay": 0,
        "departureLocation": {
            "id": 4,
            "code": "RAO",
            "name": "RIBEIRÃO PRETO - SP"
        },
        "arrivalLocation": {
            "id": 5,
            "code": "005",
            "name": "CAMPINAS - SP"
        }
    }]
    """

    data_saida: str
    paradas: List[Parada] = Field(alias="sections")

    def __getitem__(self, index):
        return self.paradas[index]

    def __iter__(self):
        return iter(self.paradas)

    def __len__(self):
        return len(self.paradas)

    @property
    def _sigla_duracao(self):
        return "-".join(f"{p.local.external_local_id}.{p.duracao}" for p in self)

    @property
    def hash(self):
        inicio = self[0].local.external_local_id
        fim = self[-1].local.external_local_id
        digest = hashlib.md5(self._sigla_duracao.encode()).hexdigest()  # noqa: S324
        return f"{inicio}{fim}{digest}"

    @root_validator(pre=True)
    def add_final_section(cls, v: dict):
        root_copy = copy.deepcopy(v)
        root_copy["sections"].append(
            {
                "departureLocation": root_copy["sections"][-1]["arrivalLocation"],
                "departureTime": root_copy["sections"][-1]["arrivalTime"],
                "arrivalDay": root_copy["sections"][-1]["arrivalDay"],
            }
        )
        return root_copy

    @validator("paradas")
    def _attach_tempos(cls, v, values):
        if not v:
            return v

        data_saida = values.get("data_saida")
        data_saida = datetime.strptime(data_saida, "%Y-%m-%d")

        iti_first = v[0]
        iti_first.duracao = 0
        iti_first.tempo_embarque = 0
        horas, minutos, segundos = iti_first.hora_saida.split(":")
        iti_first.datetime_ida = data_saida.replace(hour=int(horas), minute=int(minutos), second=int(segundos))

        for iti_anterior, iti_atual in zip(v, v[1:]):
            data_saida = data_saida + timedelta(days=iti_anterior.dias_passados)
            horas, minutos, segundos = iti_atual.hora_saida.split(":")
            iti_atual.datetime_ida = data_saida.replace(hour=int(horas), minute=int(minutos), second=int(segundos))

            duracao = iti_atual.datetime_ida - iti_anterior.datetime_ida

            if iti_atual == v[-1]:
                # Não tem tempo de embarque no último checkpoint.
                tempo_embarque = timedelta(minutes=0)
            elif duracao <= timedelta(minutes=10):
                # Tempo de embarque em paradas rápidas é o minimo de 1 min.
                tempo_embarque = timedelta(minutes=1)
            else:
                tempo_embarque = timedelta(minutes=10)

            iti_atual.duracao = (duracao - tempo_embarque).total_seconds()
            iti_atual.tempo_embarque = tempo_embarque.total_seconds()
            iti_atual.distancia = duracao.total_seconds() // 60  # estima por 60km/h

        return v


class PoltronaBloqueada(BaseModel):
    transacao: str = Field(alias="transactionIdentifier")
    poltrona: int = Field(alias="seatIdentifier")
    tipo_passageiro: int = Field(alias="passengerType")
    tarifa: Decimal = Field(alias="fare")
    taxa_embarque: Decimal = Field(alias="boardingFee")
    pedagio: Decimal = Field(alias="toll")
    seguro: Decimal = Field(alias="mandatoryInsurance")
    outras_taxas: Decimal = Field(alias="other")
    taxa_conveniencia: Decimal = Field(alias="convenienceFee")
    preco_original: Decimal = Field(alias="originalPrice")


class Cancelamento(BaseModel):
    origem_id: int = Field(alias="departureLocation")
    destino_id: int = Field(alias="arrivalLocation")
    data_saida: str = Field(alias="departureDate")
    external_id: str = Field(alias="controlNumber")
    poltrona: int = Field(alias="seatIdentifier")


class PassagemForm(BaseModel):
    poltrona: int = Field(alias="seatIdentifier")
    numero_passagem: str = Field(alias="ticketNumber")
    transacao: str = Field(alias="transactionIdentifier")
    linha: str = Field(alias="scheduleName")
    tarifa: Decimal = Field(alias="priceFare")
    taxa_embarque: Decimal = Field(alias="priceBoardingFee")
    preco_seguro: Decimal = Field(alias="priceMandatoryInsurance")
    preco_pedagio: Decimal = Field(alias="priceToll")
    outras_taxas: Decimal = Field(alias="priceOther")
    preco_desconto: Decimal = Field(alias="priceDiscount")
    data_autorizacao_bpe: Optional[str] = Field(alias="bpeAuthorizationDateTime")
    protocolo_autorizacao_bpe: Optional[str] = Field(alias="bpeAuthorizationProtocol")
    qrcode_bpe: Optional[str] = Field(alias="bpeQrCode")
    chave_bpe: Optional[str] = Field(alias="bpeKey")
    numero_bpe: Optional[str] = Field(alias="bpeSystemNumber")
    serie_bpe: Optional[str] = Field(alias="bpeSerie")
    codigo_monitriip: Optional[str] = Field(alias="monitriipCode")

    @property
    def preco_total(self):
        return self.tarifa + self.preco_seguro + self.preco_pedagio + self.outras_taxas - self.preco_desconto

    @validator(
        "data_autorizacao_bpe",
        "protocolo_autorizacao_bpe",
        "qrcode_bpe",
        "chave_bpe",
        "numero_bpe",
        "serie_bpe",
        "codigo_monitriip",
    )
    def value_or_none(cls, v):
        return v or None

    @root_validator(pre=True)
    def atribui_provider_data(cls, reserva):
        reserva["provider_data"] = reserva.copy()
        return reserva


class CompraResponse(BaseModel):
    pedido: str = Field(alias="recordLocator")
    valor_total: Decimal = Field(alias="totalValue")
    passagens: List[PassagemForm] = Field(alias="data")


class FormaPagamento(BaseModel):
    id: int = Field(alias="idFormOfPayment")
    descricao: str = Field(alias="name")

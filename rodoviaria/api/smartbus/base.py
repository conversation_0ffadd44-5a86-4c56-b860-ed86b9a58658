import logging
from json import JSONDecodeError

import requests
from pydantic import parse_obj_as
from requests.adapters import HTTPA<PERSON>pter, Retry
from requests.exceptions import ConnectionError
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from bp.logging import log_integration_request
from commons.django_utils import error_str
from commons.token_bucket import TokenBucket
from rodoviaria.api.auth import BearerCallableAuth
from rodoviaria.api.base import ResponseWrapper
from rodoviaria.api.smartbus.services import login_token_from_client, parse_error_msg
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaTooManyRequestsError,
    RodoviariaUnauthorizedError,
)

from .exceptions import SmartbusAPIError

rodovlogger = logging.getLogger("rodoviaria")


class BaseRequest:
    extra_logs = {"api": "SmartbusAPI", "log_type": "request_log"}
    retry_on_status: list[int] = None
    timeout = 60
    clean_param = None
    token_bucket = False

    def __repr__(self):
        return f"SMARTBUS_{self.__class__.__name__}_{self.login.company.id}_{self.login.company.modelo_venda}"

    def __init__(self, login):
        self.login = login
        self.session = self._build_session()

    def _build_session(self):
        session = requests.Session()
        retry_params = {"total": 3, "backoff_factor": 1, "raise_on_status": False}
        if self.retry_on_status:
            retry_params["status_forcelist"] = self.retry_on_status
        retry_strategy = Retry(**retry_params)
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        session.auth = BearerCallableAuth(login_token_from_client(self.login), "Authorization")
        return session

    @retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
    def _request(self, method, endpoint, params, json, data):
        if self.token_bucket:
            token_bucket = TokenBucket.from_client(self.login)
            token_bucket.try_get_token()

        url = f"{self.login.url_base}/{endpoint}"
        try:
            response = self.session.request(
                method=method, url=url, params=params, json=json, timeout=self.timeout, data=data
            )
        except ConnectionError as ex:
            log_msg = f"smartbus {method} {url} data={json} connection error"
            rodovlogger.error(log_msg, extra=self.extra_logs)
            error_message = f"smartbus {url} connection error"
            raise RodoviariaConnectionError(error_message) from ex

        try:
            response_json = response.json()
        except (ValueError, JSONDecodeError):
            response_json = None

        error_message = None
        try:
            self.raise_for_status(method, url, json, params, response, response_json)
        except (RodoviariaException, RodoviariaConnectionError, RodoviariaUnauthorizedError) as ex:
            error_message = error_str(ex)
            raise
        finally:
            log_integration_request(
                integration="SmartbusAPI",
                company_id=self.login.company.id,
                url=url,
                method=method,
                payload=json,
                params=params,
                response=response,
                error_message=error_message,
            )

        return response

    def raise_for_status(self, method, url, json, params, response, response_json):
        request_msg = f"smartbus '{method} {url}' json={json} params={params} status_code={response.status_code}"

        str_response = repr(response_json)
        if response.status_code == 401:
            rodovlogger.info("Sessão expirada. Refazendo o login e dando retry no request.", extra=self.extra_logs)
            self.session.auth = BearerCallableAuth(
                login_token_from_client(self.login, force_renew=True), "Authorization"
            )
            raise RodoviariaUnauthorizedError
        if response.status_code == 429:
            raise RodoviariaTooManyRequestsError
        if response_json and response_json.get("success") is False:
            error_msg = parse_error_msg(response_json)
            raise SmartbusAPIError(error_msg or str_response)
        self.raise_for_response(response, response_json, request_msg)
        if not response.ok:
            error_msg = parse_error_msg(response_json)
            raise SmartbusAPIError(error_msg or str_response)
        return response

    def raise_for_response(self, response, response_json, request_msg):
        return

    def send(self, params=None, json=None, data=None):
        response = self._request(self.method, self.path, params=params, json=json, data=data)
        return ResponseWrapper(self, response)

    def clean_response(self, response):
        cleaned_response = response
        if response.get("data") is not None:
            cleaned_response = response["data"]
        if self.clean_param:
            cleaned_response = cleaned_response[self.clean_param]
        return cleaned_response

    def parse_response(self, response):
        return parse_obj_as(self.output_model, response)


class BaseRequestCollection(BaseRequest):
    def parse_response(self, response):
        return parse_obj_as(list[self.output_model], response)

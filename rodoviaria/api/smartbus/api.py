import copy
import logging
from decimal import Decimal as D

from celery import shared_task
from django.db.models import F

from commons.celery_utils import DefaultQueueNames
from commons.dateutils import to_tz
from commons.django_utils import error_str
from commons.redis import lock
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.rodoviaria_api import RodoviariaAPI
from rodoviaria.api.smartbus import endpoints, models
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import ComprarForm, VerificarPoltronaForm
from rodoviaria.models.core import Company, Passagem, TrechoClasse
from rodoviaria.models.smartbus import RotaSmartbus, SmartbusLogin
from rodoviaria.service.exceptions import (
    RodoviariaBaseException,
    RodoviariaOverbookingException,
)
from rodoviaria.service.poltronas_svc import seleciona_poltrona

from .exceptions import SmartbusAPIError
from .memcache import SmartbusMC

buserlogger = logging.getLogger("rodoviaria")


class SmartbusAPI(RodoviariaAPI):
    Rota = RotaSmartbus
    cache = SmartbusMC()
    divergencia_maxima_pct = 50

    def __init__(self, company):
        super().__init__(company)
        self.login = SmartbusLogin.objects.select_related("company").get(company=company)
        # self.cache = SmartbusMC(company.company_internal_id)

    def __repr__(self):
        return f"{__class__.__name__}_{self.login.company.name}"

    @property
    def url_base(self):
        # Sobrescreve a url_base da RodoviariaAPI
        # TODO: usar o url_base da empresa
        return self.login.url_base

    def atualiza_origens(self):
        origens = endpoints.BuscarOrigens(self.login).send(params={})
        return list(origens.parsed)

    def buscar_corridas(self, request_params, match_params=None):
        corridas = (
            endpoints.BuscarCorridas(self.login)
            .send(request_params["origem"], request_params["destino"], request_params["data"])
            .parsed
        )

        corridas_form = self._make_corridas_form(corridas)
        if not match_params:
            found = bool(corridas)
            return BuscarServicoForm(found=found, servicos=corridas_form)

        d_args = self._find_match_corridas(corridas, request_params, **match_params)
        return self._parse_retorno_buscar_servico(**d_args)

    def _make_corridas_form(self, corridas):
        corridas_form = [
            ServicoForm(
                external_id=corrida.external_id,
                external_datetime_ida=corrida.datetime_ida,
                external_datetime_chegada=corrida.datetime_chegada,
                duracao=corrida.duracao_segundos,
                preco=corrida.preco_rodoviaria,
                provider_data=corrida.provider_data,
                external_company_id=corrida.company_external_id,
                has_connection=corrida.tem_conexao,
                vagas=corrida.vagas,
                rota_external_id=corrida.rota_id,
                classe=corrida.classe,
            )
            for corrida in corridas
        ]

        return corridas_form

    def _parse_retorno_buscar_servico(
        self, servico_encontrado: models.Corrida = None, timezone=None, mismatches=None
    ) -> BuscarServicoForm:
        if mismatches is None:
            mismatches = []
        found = bool(servico_encontrado)
        if found:
            servico = ServicoForm(
                provider_data=servico_encontrado.normalized_dict(),
                external_id=servico_encontrado.external_id,
                preco=servico_encontrado.preco_rodoviaria,
                vagas=servico_encontrado.vagas,
                external_datetime_ida=to_tz(servico_encontrado.datetime_ida, timezone),
            )
            servicos = [servico]
        else:
            servicos = [self._normalizar_servico(s, timezone) for s in mismatches]

        return BuscarServicoForm(found=found, servicos=servicos)

    def _normalizar_servico(self, servico: models.Corrida, timezone):
        servico_form = ServicoForm.parse_obj(
            {
                "external_id": servico.external_id,
                "preco": servico.preco_rodoviaria,
                "external_datetime_ida": to_tz(servico.datetime_ida, timezone),
                "classe": servico.classe,
                "external_company_id": servico.company_external_id,
                "vagas": servico.vagas,
                "provider_data": servico.provider_data,
            }
        )
        return servico_form

    def _find_match_corridas(self, corridas, request_params, datetime_ida, timezone, tipo_assento):
        matches = []
        mismatches = []
        d_args = {}
        buser_class = tipo_assento

        for servico in corridas:
            api_class = servico.classe.lower()
            if self._match_datetime_ida_servico(datetime_ida, timezone, servico.datetime_ida) and (
                self._does_class_match(buser_class, api_class)
            ):
                matches.append(servico)
            else:
                mismatches.append(servico)

        if len(matches) == 1:
            matched_service = matches[0]
            d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
            return d_args

        if len(matches) > 1:
            sorted_matches_by_diff_datetime_ida = sorted(
                matches,
                key=lambda k: (
                    self._get_diff_datetime_ida_in_minutes(datetime_ida, timezone, k.datetime_ida),
                    -k.vagas,
                ),
            )

            # quando mais de um match, tenta achar o que tenha match de classe
            # senão achar, retorna o mais proximo de horario
            matched_service = sorted_matches_by_diff_datetime_ida[0]
            for servico in sorted_matches_by_diff_datetime_ida:
                api_class = servico.classe.lower()
                if self._does_class_match(buser_class, api_class):
                    matched_service = servico
                    break

            d_args.update({"servico_encontrado": matched_service, "timezone": timezone})
            return d_args

        mismatches_classe_horario = [(m.classe, m.datetime_ida) for m in mismatches]
        msg = (
            f"Unmatch de servico {self.company.name} com {request_params}: "
            f"({buser_class}, {datetime_ida}, {timezone}) -> {mismatches_classe_horario}"
        )
        buserlogger.info(msg)
        d_args.update({"timezone": timezone, "mismatches": mismatches})

        return d_args

    def _get_poltronas(self, trecho_classe):
        external_id = trecho_classe.external_id
        origem = trecho_classe.origem.id_external
        destino = trecho_classe.destino.id_external
        data_str = trecho_classe.datetime_ida.strftime("%Y-%m-%d")
        detalhes_corrida = endpoints.DetalhesCorrida(self.login).send(origem, destino, data_str, external_id)
        poltronas = detalhes_corrida.parsed.poltronas
        return poltronas

    def get_map_poltronas(self, trecho_classe_id):
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        poltronas = self._get_poltronas(trecho_classe)
        poltronas_map = self._muda_para_ocupado_poltronas_from_db(trecho_classe_id, poltronas.map())
        return poltronas_map

    def verifica_poltrona(self, params: VerificarPoltronaForm):
        trecho_classe_id = params.trechoclasse_id
        poltronas_map = self.get_map_poltronas(trecho_classe_id)
        vagas_disponiveis = len([poltrona for poltrona, situacao in poltronas_map.items() if situacao == "livre"])
        if vagas_disponiveis < params.passageiros:
            raise RodoviariaOverbookingException(vagas_disponiveis)
        poltronas_selecionadas = seleciona_poltrona(poltronas_map, params.passageiros)
        poltronas_selecionadas = sorted([int(p) for p in poltronas_selecionadas])
        self.bloqueia_poltronas(trecho_classe_id, poltronas_selecionadas)
        return poltronas_selecionadas

    def bloqueia_poltronas(self, trecho_classe_id, poltronas):
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        external_id = trecho_classe.external_id
        origem = trecho_classe.origem.id_external
        destino = trecho_classe.destino.id_external
        data_str = trecho_classe.datetime_ida.strftime("%Y-%m-%d")
        bloqueio = endpoints.BloqueiaPoltronas(self.login).send(origem, destino, data_str, external_id, poltronas)
        for b in bloqueio.parsed:
            self.cache.set_poltrona_key_cache(trecho_classe.trechoclasse_internal_id, b.poltrona, b.dict())
        return [b.dict() for b in bloqueio.parsed]

    def desbloquear_poltronas(self, trecho_classe_id, poltronas):
        trecho_classe = self.get_active_trecho_classe(trecho_classe_id)
        external_id = trecho_classe.external_id
        origem = trecho_classe.origem.id_external
        destino = trecho_classe.destino.id_external
        data_str = trecho_classe.datetime_ida.strftime("%Y-%m-%d")
        bloqueios = []
        for p in poltronas:
            bloqueio = self.cache.get_poltrona_key_cache(trecho_classe.trechoclasse_internal_id, p)
            if bloqueio:
                bloqueios.append(bloqueio)
        desbloqueio = endpoints.DesbloqueiaPoltronas(self.login).send(origem, destino, data_str, external_id, bloqueios)
        for p in poltronas:
            self.cache.delete_poltrona_key_cache(trecho_classe.trechoclasse_internal_id, p)
        return [d.dict() for d in desbloqueio.parsed]

    @lock("compra_rodoviaria_{params.travel_id}", max_wait_time=0, except_timeout=True)
    def comprar(self, params: ComprarForm, from_add_pax=None):
        trecho_classe = self.get_active_trecho_classe(params.trechoclasse_id)
        passagens = self._create_passagens(params, trecho_classe)
        request_params, passagens = self._parse_compra_params(params, trecho_classe, passagens)
        passagens_map_by_poltronas = {str(p.poltrona_external_id): p for p in passagens}
        try:
            compra = endpoints.EfetuarCompra(self.login).send(request_params)
        except RodoviariaBaseException as ex:
            async_desbloquear_poltronas.delay(self.company.id, params.trechoclasse_id, params.poltronas)
            self._save_passagem_error(passagens_map_by_poltronas.values(), error_str(ex))
            raise
        dict_provider_data = self._retorna_provider_data(compra.raw)
        comprar_response = compra.parsed
        for passagem_api in comprar_response.passagens:
            passagem_buser = passagens_map_by_poltronas[str(passagem_api.poltrona)]
            self._save_infos_e_confirma_passagem(passagem_buser, passagem_api, comprar_response, dict_provider_data)

        return {"passagens": [p.to_dict_json() for p in passagens_map_by_poltronas.values()]}

    def _create_passagens(self, params: ComprarForm, trecho_classe: TrechoClasse):
        travel_id = params.travel_id
        preco_rodoviaria = D(str(trecho_classe.preco_rodoviaria))
        valor_cheio = D(str(params.valor_cheio))
        passagens = []
        for index, passageiro in enumerate(params.passageiros):
            poltrona = params.poltronas[index]
            bloqueio_poltrona_cache = self._get_cache_poltrona_ou_bloqueia(params.trechoclasse_id, poltrona)
            passagem = Passagem(
                trechoclasse_integracao=trecho_classe,
                company_integracao=self.company,
                poltrona_external_id=poltrona,
                buseiro_internal_id=passageiro.id,
                travel_internal_id=travel_id,
                valor_cheio=valor_cheio,
                status=Passagem.Status.INCOMPLETA,
                preco_rodoviaria=preco_rodoviaria,
                localizador=bloqueio_poltrona_cache["transacao"],
            )
            passagens.append(passagem)
        return self.create_passagens(passagens)

    def _parse_compra_params(self, params: ComprarForm, trecho_classe: TrechoClasse, passagens: list[Passagem]):
        external_id = trecho_classe.external_id
        origem_id = trecho_classe.origem.id_external
        destino_id = trecho_classe.destino.id_external
        origem_tz = trecho_classe.origem.cidade.timezone or "America/Sao_Paulo"
        data_viagem = to_tz(trecho_classe.datetime_ida, origem_tz).strftime("%Y-%m-%d")
        preco_rodoviaria = D(str(trecho_classe.preco_rodoviaria))
        passageiros = []
        passageiros_map = {p.id: p for p in params.passageiros}
        for passagem in passagens:
            passageiro = passageiros_map[passagem.buseiro_internal_id]
            passageiro_params = {
                "departureLocation": origem_id,
                "arrivalLocation": destino_id,
                "departureDate": data_viagem,
                "controlNumber": external_id,
                "passengerType": 1,
                "seatIdentifier": passagem.poltrona_external_id,
                "passengerName": passageiro.name,
                "passengerDocument": passageiro.rg_number,
                "transactionIdentifier": passagem.localizador,
            }
            passageiros.append(passageiro_params)
        request_params = {
            "params": passageiros,
            "fiscalDocument": params.passageiros[0].rg_number,
            "customerName": params.passageiros[0].name,
            "payments": [{"idFormOfPayment": 2, "value": preco_rodoviaria * len(passageiros)}],
        }
        return request_params, passagens

    def _get_cache_poltrona_ou_bloqueia(self, trecho_classe_id, poltrona):
        cache = self.cache.get_poltrona_key_cache(trecho_classe_id, poltrona)
        if not cache:
            cache = self.bloqueia_poltronas(trecho_classe_id, [poltrona])[0]
        return cache

    def _save_infos_e_confirma_passagem(self, passagem_buser, passagem_api, comprar_response, dict_provider_data):
        passagem_buser.pedido_external_id = comprar_response.pedido
        passagem_buser.numero_passagem = passagem_api.numero_passagem
        passagem_buser.localizador = passagem_api.transacao
        passagem_buser.preco_base = passagem_api.tarifa
        passagem_buser.taxa_embarque = passagem_api.taxa_embarque
        passagem_buser.seguro = passagem_api.preco_seguro
        passagem_buser.pedagio = passagem_api.preco_pedagio
        passagem_buser.outras_taxas = passagem_api.outras_taxas
        passagem_buser.preco_poltrona = passagem_api.preco_total
        passagem_buser.desconto = passagem_api.preco_desconto
        passagem_buser.chave_bpe = passagem_api.chave_bpe
        passagem_buser.numero_bpe = passagem_api.numero_bpe
        passagem_buser.bpe_qrcode = passagem_api.qrcode_bpe
        passagem_buser.data_autorizacao = passagem_api.data_autorizacao_bpe
        passagem_buser.serie_bpe = passagem_api.serie_bpe
        passagem_buser.protocolo_autorizacao = passagem_api.protocolo_autorizacao_bpe
        passagem_buser.bpe_monitriip_code = passagem_api.codigo_monitriip
        passagem_buser.provider_data = dict_provider_data[passagem_api.numero_passagem]
        # passagem_buser.numero_bilhete = passagem_api.numero_bilhete
        # passagem_buser.outros_tributos = passagem_api.tributos.format()
        # passagem_buser.prefixo = passagem_api.linha.prefixo
        linha = passagem_api.linha
        passagem_buser.linha = linha
        grupo = passagem_buser.trechoclasse_integracao.grupo
        grupo.linha = linha
        grupo.save()
        passagem_buser.save_confirmed()

    def _save_passagem_error(self, passagens, error):
        for passagem in passagens:
            passagem.erro = error
            passagem.status = Passagem.Status.ERRO
        Passagem.objects.bulk_update(passagens, ["erro", "status", "updated_at"])

    def cancela_venda(self, params: CancelaVendaForm):
        passagens = self.get_passagens_confirmadas(params.travel_id, params.buseiro_id)
        passagens_params = passagens.values("poltrona_external_id", "localizador")
        trecho_classe = self.get_active_trecho_classe(params.trechoclasse_id)
        external_id = trecho_classe.external_id
        origem = trecho_classe.origem.id_external
        destino = trecho_classe.destino.id_external
        data_str = trecho_classe.datetime_ida.strftime("%Y-%m-%d")
        try:
            try:
                cancela_response = endpoints.CancelaPassagem(self.login).send(
                    origem, destino, data_str, external_id, passagens_params
                )
            except SmartbusAPIError:
                cancela_response = endpoints.DevolvePassagem(self.login).send(
                    origem, destino, data_str, external_id, passagens_params
                )
        except SmartbusAPIError as ex:
            for p in passagens:
                p.save_error_cancelamento(error_str(ex))
            raise
        for p in passagens:
            p.save_canceled()
        return [c.dict() for c in cancela_response.parsed]

    def itinerario(self, external_id, datetime_ida):
        trecho_classe = (
            TrechoClasse.objects.annotate(
                origem_external_id=F("origem__id_external"), destino_external_id=F("destino__id_external")
            )
            .filter(external_id=external_id, grupo__company_integracao=self.company)
            .first()
        )
        external_id = trecho_classe.external_id
        origem = trecho_classe.origem_external_id
        destino = trecho_classe.destino_external_id
        data_str = trecho_classe.datetime_ida.strftime("%Y-%m-%d")
        detalhes_corrida = endpoints.DetalhesRota(self.login).send(origem, destino, data_str, external_id)
        return detalhes_corrida

    def _retorna_provider_data(self, compra_raw):
        provider_data = copy.deepcopy(compra_raw)
        reservas = provider_data["data"].pop("data")
        dict_provider_data = {}

        for reserva in reservas:
            dict_provider_data[reserva["ticketNumber"]] = {"reserva": reserva, **provider_data}

        return dict_provider_data


@shared_task(queue=DefaultQueueNames.DESBLOQUEAR_POLTRONAS)
def async_desbloquear_poltronas(company_rodoviaria_id, trecho_classe_id, poltronas):
    company = Company.objects.get(id=company_rodoviaria_id)
    api = SmartbusAPI(company)
    api.desbloquear_poltronas(trecho_classe_id, poltronas)
    buserlogger.info(
        "Smartbus - Desbloqueando poltronas %s do trecho %s da empresa %s",
        poltronas,
        trecho_classe_id,
        company.company_internal_id,
    )


def fetch_formas_pagamento(login_params):
    login_obj = models.SmartbusUndefinedCompanyClient.parse_obj(login_params)
    formas_pagamento = endpoints.BuscarFormasPagamento(login_obj).send()
    return list(formas_pagamento.parsed)

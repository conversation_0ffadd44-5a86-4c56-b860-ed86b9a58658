import logging

from commons.memcache import getmc_pymemcache

logger = logging.getLogger("rodoviaria")


class SmartbusMC:
    base_prefix = "SMARTBUS"
    cachekey_prefix = "SMARTBUS_CLIENTE_TOKEN_"
    default_timeout = 2400
    default_timeout_bloqueio_poltrona = 1800

    @property
    def mc(self):
        return getmc_pymemcache()

    def _bloqueio_poltrona_cache_key(self, trecho_classe_id, poltrona):
        return f"{self.base_prefix}_{trecho_classe_id}_{poltrona}"

    def set_poltrona_key_cache(self, trecho_classe_id, poltrona, bloqueio):
        return self.mc.add(
            key=self._bloqueio_poltrona_cache_key(trecho_classe_id, poltrona),
            value=bloqueio,
            timeout=self.default_timeout_bloqueio_poltrona,
        )

    def get_poltrona_key_cache(self, trecho_classe_id, poltrona):
        return self.mc.get(key=self._bloqueio_poltrona_cache_key(trecho_classe_id, poltrona))

    def delete_poltrona_key_cache(self, trecho_classe_id, poltrona):
        return self.mc.delete(key=self._bloqueio_poltrona_cache_key(trecho_classe_id, poltrona))

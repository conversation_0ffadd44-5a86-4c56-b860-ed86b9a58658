import logging

import requests

from bp.logging import log_integration_request
from commons.memcache import getmc_pymemcache
from rodoviaria.api.smartbus import exceptions
from rodoviaria.api.smartbus.models import SmartbusUndefinedCompanyClient
from rodoviaria.models.smartbus import SmartbusLogin
from rodoviaria.service.exceptions import RodoviariaUnauthorizedError

CACHEKEY_PREFIX = "SMARTBUS_TOKEN_COMPANY_"
LOGIN_PATH = "OAuth"

rodovlogger = logging.getLogger("rodoviaria")


def login_token_from_client(client: SmartbusUndefinedCompanyClient | SmartbusLogin, force_renew=False):
    def func() -> str:
        mc = getmc_pymemcache()
        cache_key = f"{CACHEKEY_PREFIX}{client.company.id}"
        resp = mc.get(cache_key)

        if not resp or force_renew:
            token, expires_in = make_login(client)
            expires_in = min(expires_in, 24 * 60 * 60)  # cache máximo de um dia
            if client.company.id:  # não salva cache se não for login de uma empresa criada
                mc.set(key=cache_key, value=token, timeout=expires_in)
            return token
        return resp

    return func


def make_login(client: SmartbusUndefinedCompanyClient | SmartbusLogin):
    url = f"{client.url_base}/{LOGIN_PATH}"
    data = {"username": client.username, "password": client.password, "grant_type": "password"}
    try:
        response = requests.post(url=url, json=data, timeout=60)
    except requests.exceptions.ConnectionError as exc:
        extra_logs = {"api": "SmartbusAPI", "log_type": "request_log", "url": url}
        rodovlogger.error("Erro de conexão no login", extra_logs)
        raise RodoviariaUnauthorizedError from exc
    log_integration_request(
        integration="SmartbusAPI",
        company_id=client.company.id,
        url=url,
        method="post",
        payload=data,
        params={},
        response=response,
    )
    json_response = response.json()
    if not response.ok:
        error = parse_error_msg(json_response)
        if error == "invalid_grant":
            raise RodoviariaUnauthorizedError
        raise exceptions.SmartbusAPIError(error)
    return json_response["access_token"], json_response["expires_in"]


def parse_error_msg(response_json):
    if not response_json:
        return
    error_msg = None
    error_details = response_json.get("errorDetails")
    error = response_json.get("error")
    if error:
        return error
    if error_details:
        return error_details[0].get("message")
    return error_msg

from commons.memcache import getmc_pymemcache
from rodoviaria.api.guichepass import models


class GuichepassMC:
    prefix = "GUICHEPASS"
    trecho_cache_prefix = "TRECHO"
    poltrona_cache_prefix = "POLTRONA"
    poltrona_indisponivel_cache_prefix = "POLTRONA_INDISPONIVEL"
    default_timeout = 900

    @property
    def mc(self):
        return getmc_pymemcache()

    def __init__(self, company_id):
        self.prefix += f"_COMPANY_{company_id}"

    def _poltrona_cache_key(self, trecho_classe_id, poltrona):
        return f"{self.prefix}_{self.trecho_cache_prefix}_{trecho_classe_id}_{self.poltrona_cache_prefix}_{poltrona}"

    def set_poltrona_bloqueada_cache(self, trecho_classe_id, poltrona, cache_value):
        return self.mc.add(
            key=self._poltrona_cache_key(trecho_classe_id, poltrona),
            value=cache_value,
            timeout=self.default_timeout,
        )

    def get_poltrona_bloqueada_cache(
        self, trecho_classe_id: int, poltrona: int
    ) -> models.InfosCacheaveisBloqueioPoltrona:
        return self.mc.get(key=self._poltrona_cache_key(trecho_classe_id, poltrona))

    def delete_poltrona_bloqueada_cache(self, trecho_classe_id, poltrona):
        return self.mc.delete(key=self._poltrona_cache_key(trecho_classe_id, poltrona))

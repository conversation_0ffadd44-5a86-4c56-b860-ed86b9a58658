from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, StrictStr, root_validator

from rodoviaria.api.forms import RetornoConsultarBilheteForm


class LoginForm(BaseModel):
    username: StrictStr
    password: StrictStr  # ambos vão no header


class LoginOut(BaseModel):
    """
    >>> from datetime import datetime, timezone, timedelta
    >>> data = {
    ... "accessToken": "ACCESS_TOKEN",
    ... "refreshToken": "REFRESH_TOKEN",
    ... "accessTokenSso": "ACCESS_TOKEN_SSO",
    ... "refreshTokenSso": "REFRESH_TOKEN_SSO",
    ... "expiresAt": (datetime.now(timezone.utc) + timedelta(seconds=719)).isoformat(),
    ... "expiresSec": 719,
    ... "currentDate": "2021-04-08",
    ... }
    >>> form = LoginOut(**data)
    >>> assert form.access_token == "ACCESS_TOKEN"
    >>> assert form.access_token_sso == "ACCESS_TOKEN_SSO"
    """

    access_token: str = Field(alias="accessToken")
    refresh_token: str = Field(alias="refreshToken")
    access_token_sso: str = Field(alias="accessTokenSso")
    refresh_token_sso: str = Field(alias="refreshTokenSso")
    expires_at: datetime = Field(alias="expiresAt")
    expires_sec: int = Field(alias="expiresSec")
    current_date: date = Field(alias="currentDate")


class BloquearPoltronaForm(BaseModel):
    origin_id: int = Field(alias="origin")
    destination_id: int = Field(alias="destination")
    date: StrictStr
    bus_company: int = Field(alias="busCompany")
    service: StrictStr
    seat: int


class ConfirmarVendaForm(BaseModel):
    reserve_id: Optional[int] = Field(alias="id")
    name: StrictStr
    document: StrictStr
    birthdate: Optional[StrictStr]
    discount: Optional[float]
    price: Decimal
    ticketTypeId: Optional[int]


class CancelarVendaForm(BaseModel):
    reserve_id: int = Field(alias="id")


class ConsultarBilheteForm(BaseModel):
    reserve_id: int = Field(alias="id")


class RetornaPoltronasForm(BaseModel):
    origin_id: int = Field(alias="origin")
    destination_id: int = Field(alias="destination")
    date: StrictStr
    bus_company: int = Field(alias="busCompany")
    service: StrictStr


class BuscaOrigemForm(BaseModel):
    pass


class BuscarServicoForm(BaseModel):
    origin: int = Field(alias="origem")
    destination: int = Field(alias="destino")
    date: StrictStr = Field(alias="data")


class ExternalCompany(BaseModel):
    external_id: int = Field(alias="id")
    nome: str = Field(alias="name")
    cnpj: str


class ExternalCompanies(BaseModel):
    __root__: List[ExternalCompany]

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)


class Localidade(BaseModel):
    """
    >>> from pydantic import parse_obj_as
    >>> data = [
    ...     {'id': 129,
    ...      'name': 'ABADIA DOS DOURADOS',
    ...      'city': 'ABADIA DOS DOURADOS',
    ...      'state': 'MG',
    ...      'ibgeCityCode': '3100104'},
    ...     {'id': 135,
    ...      'name': 'SÃO PAULO',
    ...      'city': 'SÃO PAULO',
    ...      'state': 'SP',
    ...      'ibgeCityCode': '2611606'}
    ... ]
    >>> form = parse_obj_as(list[Localidade], data)
    >>> assert sorted([localidade.external_local_id for localidade in form]) == sorted(["129", "135"])
    """

    nome_cidade: str = Field(alias="name")
    external_local_id: str = Field(alias="id")
    external_cidade_id: str = Field(alias="id")
    uf: str = Field(alias="state")
    complemento: str | None = None
    id_cidade_ibge: int | None = None

    @property
    def descricao(self):
        desc = f"{self.nome_cidade} - {self.uf}"
        if self.complemento:
            desc += f" - {self.complemento}"
        return desc

    class Config:
        allow_population_by_field_name = True


class BilheteModel(RetornoConsultarBilheteForm):
    """
    Padroniza as informações da consulta de bilhete nas API dos parceiros
    >>> data = {
    ... 'origin': 'UBERLANDIA',
    ... 'destination': 'RECIFE',
    ... 'departure': '2021-04-10T06:00:00',
    ... 'arrival': '2021-04-10T20:50:00',
    ... 'service': '870',
    ... 'busCompany': '1',
    ... 'busType': '1',
    ... 'operationType': 'DEFAULT',
    ... 'amenities': None,
    ... 'distance': None,
    ... 'stopover': False,
    ... 'id': 62251,
    ... 'seat': 24,
    ... 'priceInfo': {
    ...     'basePrice': 300.0,
    ...     'insurancePrice': 0.0,
    ...     'taxPrice': 0,
    ...     'otherPrice': 0.0,
    ...     'tollPrice': 0.0,
    ...     'boardingPrice': 5.42,
    ...     'commission': 0,
    ...     'companyDiscount': 0.0,
    ...     'discounts': [],
    ...     'cancelationFee': 0.0,
    ...     'totalDiscount': 0,
    ...     'price': 305.42,
    ...     'priceWithoutInsurance': 305.42,
    ...     'totalCompanyDiscount': 0,
    ...     'originalPrice': 305.42,
    ...     'originalPriceWithoutInsurance': 305.42,
    ...     'priceWithBusCompanyDiscount': 305.42,
    ...     'priceWithInsurance': 305.42
    ... },
    ... 'alternativePrices': [],
    ... 'status': 'CANCELED',
    ... 'name': 'FULANO DE TAL',
    ... 'document': '1234567',
    ... 'ticket': None,
    ... 'ticketNumber': '1SPBVQ',
    ... 'message': None,
    ... 'metaData': None,
    ... 'orderId': None,
    ... 'buyerInfoId': None,
    ... 'optInInsurance': False,
    ... 'noSeatNumberRequired': False,
    ... 'barCodes': [{'id': None,
    ...   'value': '1SPBVQ',
    ...   'application': 'BOARDING_PASS',
    ...   'standard': 'CODE_128'}],
    ... 'timestamp': '2021-04-07T18:56:45.000',
    ... 'bpeInfo': {
    ...     'bpeQrCode': 'https://dfe-portal.svrs.rs.gov.br/bpe/qrCode?chBPe=26210&tpAmb=2',
    ...     'platform': None,
    ...     'prefix': '6868',
    ...     'line': 'RECIFE / SAO PAULO',
    ...     'totalAmount': 6.65,
    ...     'discountAmount': 0,
    ...     'paymentAmount': 6.65,
    ...     'bpeAccessKey': 'BPe26210224441891000180630000000201961061129410',
    ...     'contactTel': None,
    ...     'specialContactTel': None,
    ...     'bpeQueryUrl': 'https://bpe.svrs.rs.gov.br/ws/bpeConsulta/bpeConsulta.asmx',
    ...     'paymentMethod': 'Cartão de Crédito',
    ...     'paymentMethodAmount': 6.65,
    ...     'changeAmount': 0,
    ...     'discountType': None,
    ...     'bpeNumber': '20196',
    ...     'bpeSeries': '0',
    ...     'bpeAuthProtocol': '326210000001975',
    ...     'bpeAuthorizationDate': '2021-02-24T13:58:38-03:00',
    ...     'systemNumber': '62714',
    ...     'otherTributes': 'Tributos Totais Incidentes(Lei Federal 12.741/2012) - R$1.13',
    ...     'contingency': False,
    ...     'anttOriginCode': None,
    ...     'anttDestinationCode': None,
    ...     'bpeMonitriipCode': None,
    ...     'taxAmount': 0,
    ...     'tollAmount': 0,
    ...     'boardingTaxAmount': 3.32,
    ...     'insuranceAmount': 0,
    ...     'othersAmounts': 0,
    ...     'agencyHeaderDistrict': None,
    ...     'agencyHeaderCity': None,
    ...     'agencyHeaderCnpj': None,
    ...     'agencyHeaderAddress': None,
    ...     'agencyHeaderNumber': None,
    ...     'agencyHeaderCompanyName': None,
    ...     'agencyHeaderState': None,
    ...     'emitterHeaderDistrict': 'Curado',
    ...     'emitterHeaderPostalCode': '',
    ...     'emitterHeaderCity': 'RECIFE',
    ...     'emitterHeaderCnpj': '24441891000180',
    ...     'emitterHeaderAddress': 'Rua Dr. George Wiliam Butler',
    ...     'emitterHeaderStateRegistration': '',
    ...     'emitterHeaderNumber': '863',
    ...     'emitterHeaderCompanyName': 'Rodoviária Borborema Ltda',
    ...     'emitterHeaderState': 'PE',
    ...     'optionalMessage': None
    ...     },
    ... 'channel': None,
    ... 'insurancePolicy': None,
    ... 'insuranceSelected': False
    ... }
    >>> form = BilheteModel.parse_obj(data)
    >>> assert form.numero_passagem is None
    >>> assert form.bpe_id == "20196"

    """

    integracao: str = "GuichePass"
    localizador: str = Field(alias="ticketNumber")
    numero_bilhete: str = Field(alias="ticketNumber")
    status: str = Field(alias="status")
    numero_assento: int = Field(alias="seat")
    primeiro_nome_pax: str = Field(alias="name")
    numero_documento: str = Field(alias="document")
    data_partida: str = Field(alias="departure")
    data_chegada: str = Field(alias="arrival")
    origem: str = Field(alias="origin")
    destino: str = Field(alias="destination")
    duracao: str | None = Field(alias="distance")
    paradas: str = Field(alias="stopover")

    @root_validator(pre=True)
    def parse_nested_objects(cls, values):
        bpe_info = values.get("bpeInfo") or {}
        price_info = values.get("priceInfo") or {}

        values["bpe_id"] = bpe_info.get("bpeNumber")
        values["bpe_public_url"] = bpe_info.get("bpeQrCode")
        values["valor_passagem"] = price_info.get("price")
        values["taxa_de_cancelamento"] = price_info.get("cancelationFee")
        values["taxa_embarque"] = price_info.get("boardingPrice")

        return values

    class Config:
        allow_population_by_field_name = True


class TicketTypeItem(BaseModel):
    descricao: str = Field(alias="description")
    categoria_external_id: str = Field(alias="id")
    categoria_external: str = Field(alias="salesStrategy")
    preco: Decimal = Field(alias="priceValue")
    code: str


class Seat(BaseModel):
    status: str
    x: int
    y: int
    z: int
    number: int
    description: str
    categorias: List[TicketTypeItem] = Field(alias="ticketType")


class Travel(BaseModel):
    origin: str
    destination: str
    departure: str
    arrival: str
    service: str
    bus_company: str = Field(alias="busCompany")
    bus_type: str = Field(alias="busType")
    operation_type: str = Field(alias="operationType")
    amenities: str | None
    distance: str | None
    stopover: str
    free_seats: int = Field(alias="freeSeats")
    price: float
    kind: str | None
    message: str
    has_intinerary: str = Field(alias="hasIntinerary")
    query_only: str = Field(alias="queryOnly")
    connection: str
    no_seat_number_required: str = Field(alias="noSeatNumberRequired")
    bus_id: str | None = Field(alias="busId")
    discounts: List
    company_discount: float = Field(alias="companyDiscount")
    min_advance_time: str | None = Field(alias="minAdvanceTime")
    min_advance_time_in_minutes: str | None = Field(alias="minAdvanceTimeInMinutes")
    expiration_date: str | None = Field(alias="expirationDate")


class PriceInfo(BaseModel):
    base_price: int = Field(alias="basePrice")
    insurance_price: int = Field(alias="insurancePrice")
    tax_price: int = Field(alias="taxPrice")
    other_price: int = Field(alias="otherPrice")
    toll_price: int = Field(alias="tollPrice")
    boarding_price: float = Field(alias="boardingPrice")
    commission: int
    company_discount: float = Field(alias="companyDiscount")
    discounts: List
    cancelation_fee: str | None = Field(alias="cancelationFee")
    price_without_insurance: float = Field(alias="priceWithoutInsurance")
    total_company_discount: int = Field(alias="totalCompanyDiscount")
    original_price: float = Field(alias="originalPrice")
    original_price_without_insurance: float = Field(alias="originalPriceWithoutInsurance")
    price_with_bus_company_discount: float = Field(alias="priceWithBusCompanyDiscount")
    price_with_insurance: float = Field(alias="priceWithInsurance")
    price: float
    total_discount: int = Field(alias="totalDiscount")


class ListQuota(BaseModel):
    categoria_external_id: str = Field(alias="id")
    descricao: str = Field(alias="description")
    categoria_external: str = Field(alias="salesStrategy")
    preco: Decimal = Field(alias="priceValue")
    codigo: str = Field(alias="code")
    poltrona: int = Field(alias="numberSeat")
    tipo_cota: str = Field(alias="quotType")
    quantidade: int = Field(alias="amount")


class RetornaPoltronaModel(BaseModel):
    """
    {
        "seats": [
            {
                "status": "RESERVED",
                "x": 0,
                "y": 4,
                "z": 0,
                "number": "1",
                "description": "CONV",
                "ticketType": [
                    {
                        "id": 1,
                        "description": "PASSAGEM",
                        "salesStrategy": "DEFAULT",
                        "priceValue": 305.42,
                        "isNominalSale": False,
                        "allowsPromotion": True,
                        "code": "1",
                        "validateRule": None,
                        "numberSeat": None,
                        "amount": None,
                        "quotType": None,
                    }
                ],
            },
        ],
        "travel": {
            "origin": "42",
            "destination": "121",
            "departure": "2021-02-24T06:00:00",
            "arrival": "2021-02-24T21:30:00",
            "service": "790-1",
            "busCompany": "VIRTUAL TRANSPORTES LTDA L C",
            "busType": "1",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "freeSeats": 11,
            "price": 82.52,
            "kind": None,
            "message": "",
            "hasIntinerary": False,
            "queryOnly": False,
            "connection": False,
            "noSeatNumberRequired": False,
            "busId": None,
            "discounts": [],
            "companyDiscount": 8.8,
            "minAdvanceTime": None,
            "minAdvanceTimeInMinutes": None,
            "expirationDate": None,
        },
        "priceInfo": {
            "basePrice": 88,
            "insurancePrice": 0,
            "taxPrice": 0,
            "otherPrice": 0,
            "tollPrice": 0,
            "boardingPrice": 3.32,
            "commission": 0,
            "companyDiscount": 8.8,
            "discounts": [],
            "cancelationFee": None,
            "priceWithoutInsurance": 82.52,
            "totalCompanyDiscount": 0,
            "originalPrice": 82.52,
            "originalPriceWithoutInsurance": 82.52,
            "priceWithBusCompanyDiscount": 82.52,
            "priceWithInsurance": 82.52,
            "price": 82.52,
            "totalDiscount": 0,
        },
        "listQuotas": [
            {
                "id": 13,
                "description": "Idoso ANTT 100%",
                "salesStrategy": "ELDER",
                "priceValue": 0,
                "isNominalSale": False,
                "allowsPromotion": False,
                "code": "Idoso 100 ANTT",
                "validateRule": False,
                "numberSeat": 0,
                "amount": 2,
                "quotType": "DYNAMIC_QUOTAS",
            },
        ],
    }
    """

    poltronas: List[Seat] = Field(alias="seats")
    travel: Travel
    price_info: PriceInfo = Field(alias="priceInfo")
    cotas: List[ListQuota] = Field(alias="listQuotas")


class InfosCacheaveisBloqueioPoltrona(BaseModel):
    """
    >>> from decimal import Decimal
    >>> data = dict(
    ... reserva_id=123,
    ... categoria_external_id=2
    ... )
    >>> model = InfosCacheaveisBloqueioPoltrona.parse_obj(data)
    >>> assert model.reserva_id == 123
    >>> assert model.categoria_external_id == 2
    """

    reserva_id: int
    categoria_external_id: Optional[int] = None
    preco: Decimal

import logging

from requests.auth import AuthBase

from rodoviaria.api.guichepass.services import (
    get_api_token_from_client,
)
from rodoviaria.forms.staff_forms import GuichepassUndefinedCompanyLogin
from rodoviaria.models import Company, GuichepassLogin
from rodoviaria.service.exceptions import RodoviariaLoginNotFoundException

rodovlogger = logging.getLogger("rodoviaria")


class GuichepassAuth(AuthBase):
    def __init__(self, client, company, client_id):
        self.client = client
        self.company = company
        self.client_id = client_id
        self._token: None | dict = None

    @classmethod
    def from_company(cls, company: Company):
        try:
            client: GuichepassLogin = GuichepassLogin.objects.select_related("company").get(company=company)

        except GuichepassLogin.DoesNotExist as ex:
            raise RodoviariaLoginNotFoundException(
                integracao="guichepass",
                company_pk=company,
                modelo_venda=company.modelo_venda,
            ) from ex
        return cls(
            client=client,
            company=company,
            client_id=client.client_id,
        )

    @classmethod
    def from_client(cls, client: GuichepassLogin | GuichepassUndefinedCompanyLogin):
        return cls(
            client=client,
            company=client.company,
            client_id=client.client_id,
        )

    def refresh_token(self, force_update=False):
        if self._token is None or force_update:
            data = get_api_token_from_client(self.client, force_update)
            self._token = data

        return self._token.get("access_token")

    def __call__(self, request):
        request.headers["X-Authorization"] = self.refresh_token()
        request.headers["ClientId"] = self.client.client_id
        return request

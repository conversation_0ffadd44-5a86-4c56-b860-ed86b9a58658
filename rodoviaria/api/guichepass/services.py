import logging
from http import HTTPStatus
from json import JSONDecodeError
from urllib.parse import urljoin

import requests

from bp.logging import log_integration_request
from commons.memcache import getmc_pymemcache
from rodoviaria.admin import GuichepassLogin
from rodoviaria.forms.staff_forms import GuichepassUndefinedCompanyLogin
from rodoviaria.service.exceptions import RodoviariaConnectionError, RodoviariaUnauthorizedError

rodovlogger = logging.getLogger("rodoviaria")
CACHEKEY_PREFIX = "GUICHEPASS_TOKEN_COMPANY_"


def get_api_token_from_client(login: GuichepassLogin | GuichepassUndefinedCompanyLogin, force_renew: bool = False):
    base_url = login.company.url_base
    path = "auth/login"
    url = urljoin(base_url, path)
    method = "POST"
    headers = {
        "username": login.username,
        "password": login.password,
        "ClientId": login.client_id,
    }
    json = None
    params = None
    company_id = login.company.id

    mc = getmc_pymemcache()
    cache_key = f"{CACHEKEY_PREFIX}{company_id}"
    data = mc.get(cache_key)

    if not data or force_renew:
        response = _make_request(method, url, json, params, headers, company_id=company_id)
        response_json = response.json()
        timeout = response_json["expiresSec"]
        token = response_json["accessToken"]
        data = {"access_token": token, "new_login": bool(token)}
        mc.set(key=cache_key, value=data, timeout=timeout)
    return data


def _make_request(method, url, json, params, headers, company_id=None):
    extra_logs = {"api": "GuichepassAPI", "log_type": "request_log"}

    log_msg = f"guichepass '{method} {url}' {json=} {params=} connection error"
    try:
        response = requests.request(method=method, url=url, headers=headers, timeout=60)
    except ConnectionError as ex:
        rodovlogger.error(log_msg, extra=extra_logs)
        raise RodoviariaConnectionError(log_msg) from ex

    log_integration_request(
        integration="GuichepassAPI",
        company_id=company_id,
        url=url,
        method=method,
        payload=json,
        params=params,
        response=response,
    )

    if response.ok:
        return response

    try:
        response_json = response.json()
    except (ValueError, JSONDecodeError):
        response_json = None

    err_msg = _parse_response(response_json, "message")
    if response.status_code == HTTPStatus.UNAUTHORIZED:
        raise RodoviariaUnauthorizedError(err_msg)
    raise RodoviariaConnectionError(log_msg)


def _parse_response(response_json, key: str):
    if isinstance(response_json, dict):
        return response_json.get(key, "")
    return response_json

import logging
from http import HTT<PERSON>tatus
from json import JSONDecodeError

from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pt<PERSON>, Retry

from rodoviaria.api.executors import Request, RequestConfig, Response
from rodoviaria.api.guichepass.auth import GuichepassAuth
from rodoviaria.models.guichepass import GuichepassLogin
from rodoviaria.service.exceptions import (
    RodoviariaOTAException,
    RodoviariaTimeoutException,
    RodoviariaTooManyRequestsError,
    RodoviariaUnauthorizedError,
)

rodovlogger = logging.getLogger("rodoviaria")


class BaseRequestConfig(RequestConfig):
    # Prefix for logging and error messages
    MESSAGE_PREFIX = "guichepass"

    # See rodoviaria/api/executors/middlewares.py:LoggingMiddleware
    integration_name = "GuichepassAPI"

    timeout = 60

    http_adapter = HTTPAdapter(
        max_retries=Retry(
            total=3,
            backoff_factor=1,
            raise_on_status=False,
            status_forcelist=[401, 406, 408],
        ),
    )

    def __repr__(self):
        return f"GUICHEPASS_{self.__class__.__name__}_{self.login.company.id}_{self.login.company.modelo_venda}"

    def __init__(self, login: GuichepassLogin):
        self.login = login
        self.auth = GuichepassAuth.from_client(self.login)

    def endpoint(self, endpoint: str) -> str:
        return f"{self.login.company.url_base}/{endpoint}"

    def preprocess_request(self, request: Request) -> Request:
        self.auth(request)
        return request

    def process_response(self, response: Response) -> Response:
        if response.ok:
            return response

        generic_message = (
            f"{self.MESSAGE_PREFIX} '{response.request.method} {response.request.url}' "
            f"json={response.request.json} params={response.request.params} "
            f"status_code={response.status_code}"
        )
        status_code = response.status_code

        try:
            payload = response.json()
            message = payload.get("message", generic_message)
            message = payload.get("code", message)
        except (ValueError, AttributeError, JSONDecodeError):
            message = generic_message

        self._raise_for_response(status_code, message)
        self._raise_for_status(status_code, message)
        return response

    def _raise_for_response(self, status_code, message):
        return

    def _raise_for_status(self, status_code, message):
        if status_code == HTTPStatus.UNAUTHORIZED:
            self.auth.refresh_token(force_update=True)
            raise RodoviariaUnauthorizedError(message)
        elif status_code in (HTTPStatus.TOO_MANY_REQUESTS, HTTPStatus.NOT_ACCEPTABLE):
            raise RodoviariaTooManyRequestsError(message)
        elif status_code == HTTPStatus.REQUEST_TIMEOUT:
            raise RodoviariaTimeoutException(message)

        raise RodoviariaOTAException(message)

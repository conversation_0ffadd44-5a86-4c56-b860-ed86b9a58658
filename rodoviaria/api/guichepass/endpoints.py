from http import HTT<PERSON><PERSON><PERSON>, HTTPStatus

from pydantic import parse_obj_as
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.service.exceptions import (
    RodoviariaUnauthorizedError,
)

from ...service.exceptions import PassengerTicketAlreadyPrintedException
from . import models
from .base import BaseRequestConfig


class EfetuaLoginConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("auth/login")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def efetuar_login(login):
    request = EfetuaLoginConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor)
    return parse_obj_as(models.LoginOut, response.json())


class ConsultarEmpresasConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("web-sale/companies/no-page")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def consultar_empresas_request(login):
    request = ConsultarEmpresasConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor)
    return response.json()


class BloquearPoltronaConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("web-sale/create-reserve")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def bloquear_poltronas_request(login, params: models.BloquearPoltronaForm):
    request = BloquearPoltronaConfig(login)
    json = params.dict(by_alias=True)
    executor = get_http_executor()
    response = request.invoke(executor, json=json)
    return response.json()


class CancelarVendaConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("web-sale/cancel-reserve")

    def _raise_for_response(self, status_code, message):
        if status_code == HTTPStatus.UNPROCESSABLE_ENTITY:
            if message == "cancellation.ticket_printed":
                raise PassengerTicketAlreadyPrintedException


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def cancelar_venda_request(login, params):
    json = models.CancelarVendaForm.parse_obj(params).dict(by_alias=True)
    request = CancelarVendaConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor, json=json)
    return response.json()


class ConfirmarVendaConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("web-sale/confirm-reserve")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def confirmar_venda_request(login, params):
    json = params.dict(by_alias=True, exclude_unset=True)
    request = ConfirmarVendaConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor, json=json)
    return response.json()


class RetornaPoltronasConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("web-sale/bus-layout")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def retorna_poltronas_request(login, params: models.RetornaPoltronasForm) -> models.RetornaPoltronaModel:
    json = params.dict(by_alias=True, exclude_unset=True)
    request = RetornaPoltronasConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor, json=json)
    return parse_obj_as(models.RetornaPoltronaModel, response.json())


class TiposOnibusConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("web-sale/seatTypes")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def tipos_onibus_request(login):
    request = TiposOnibusConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor)
    return response.json()


class AtualizaOrigensConfig(BaseRequestConfig):
    method = HTTPMethod.GET

    @property
    def url(self):
        return self.endpoint("web-sale/leg-stops/no-page")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def atualiza_origens_request(login):
    request = AtualizaOrigensConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor)
    return parse_obj_as(list[models.Localidade], response.json())


class BuscarCorridasConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("web-sale/search")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def buscar_corridas_request(login, params):
    json = models.BuscarServicoForm.parse_obj(params).dict(exclude_unset=True)
    request = BuscarCorridasConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor, json=json)
    return response.json()


class ConsultarBilheteConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint("web-sale/query-reserve")


@retry(retry=retry_if_exception_type(RodoviariaUnauthorizedError), reraise=True, stop=stop_after_attempt(2))
def consultar_bilhete_request(login, params) -> models.BilheteModel:
    json = models.ConsultarBilheteForm.parse_obj(params).dict(by_alias=True)
    request = ConsultarBilheteConfig(login)
    executor = get_http_executor()
    response = request.invoke(executor, json=json)
    return parse_obj_as(models.BilheteModel, response.json())

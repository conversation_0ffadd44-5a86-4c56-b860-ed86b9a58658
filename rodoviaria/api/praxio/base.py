import logging

from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter, Retry
from requests.exceptions import (
    JSONDecodeError,
)

from rodoviaria.api.executors import Request, RequestConfig, Response
from rodoviaria.api.praxio.auth import PraxioAuth
from rodoviaria.api.praxio.exceptions import PraxioAPIError
from rodoviaria.models.praxio import PraxioLogin
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaTooManyRequestsError,
    RodoviariaUnauthorizedError,
)

rodovlogger = logging.getLogger("rodoviaria")


class BaseRequestConfig(RequestConfig):
    # Prefix for logging and error messages
    MESSAGE_PREFIX = "praxio"

    # See rodoviaria/api/executors/middlewares.py:LoggingMiddleware
    integration_name = "PraxioAPI"

    timeout = 60

    http_adapter = HTTPAdapter(
        max_retries=Retry(
            total=3,
            backoff_factor=1,
            raise_on_status=False,
            status_forcelist=[401, 406, 408],
        ),
    )

    def __repr__(self):
        return (
            f"{self.integration_name}_{self.__class__.__name__}"
            f"_{self.login.company.id}_{self.login.company.modelo_venda}"
        )

    def __init__(self, login: PraxioLogin, with_auth=True):
        self.login = login
        self.auth = None
        if with_auth is True:
            # TODO: é possível simplificar o gerenciamento do token de sessão, mas vai ficar
            # para outro MR para reduzir os riscos aqui.
            self.auth = PraxioAuth.from_client(self.login)

    def endpoint(self, endpoint: str) -> str:
        return f"{self.login.company.url_base}/{endpoint}"

    def preprocess_request(self, request: Request) -> Request:
        if self.auth is None:
            return request

        request.json = request.json or {}
        request.json.update(self.auth.request_data)
        return request

    def process_response(self, response: Response) -> Response:
        self._raise_for_status(response)
        self._raise_for_login_expired(response)
        self._raise_for_response(response)

        return response

    def _raise_for_status(self, response: Response):
        payload = get_response_json_supressing_decode_errors(response)
        is_too_many_requests = response.status_code in (429, 406)
        is_connection_error = response.status_code in (522, 524) or (
            isinstance(payload, dict) and "connection error" in payload.get("erro", "")
        )
        if is_too_many_requests:
            error_message = f"{response.status_code} too many requests"
            raise RodoviariaTooManyRequestsError(error_message)
        elif is_connection_error:
            error_message = f"{response.status_code} connection error"
            raise RodoviariaConnectionError(error_message)
        elif not response.ok:
            error_message = f"status_code={response.status_code} payload={payload}"
            if payload and payload.get("message"):
                error_message = payload["message"]
            elif payload and payload.get("Mensagem"):
                error_message = payload["Mensagem"]
            raise PraxioAPIError(response.status_code, error_message)

    def _raise_for_login_expired(self, response: Response):
        payload = get_response_json_supressing_decode_errors(response)
        # Erros na praxio retornam status 200, então o response.ok não resolve todas as formas de erro
        id_erro = payload.get("IdErro")
        if not id_erro:
            return

        if not id_erro.startswith("SE"):
            return

        error_msg = "Erro desconhecido de sessão."
        soft_renew = True
        force_renew = False

        if id_erro == "SE002":
            error_msg = "Sessão Expirada"
            force_renew = True
        if id_erro == "SES002":
            error_msg = "Sessão não existe"

        rodovlogger.info(
            "%s Solicitando novo login e retry no request.",
            error_msg,
            extra={"api": "PraxioAPI", "log_type": "request_log"},
        )
        self.auth = PraxioAuth.from_client(self.login, soft_renew=soft_renew, force_renew=force_renew)
        raise RodoviariaUnauthorizedError(error_msg)

    def _raise_for_response(self, response: Response):
        payload = get_response_json_supressing_decode_errors(response)
        has_error = payload.get("IdErro")
        if not has_error:
            return

        default_error_message = f"status_code={response.status_code} payload={payload}"
        error_message = payload.get("message", payload.get("Mensagem") or default_error_message)
        raise PraxioAPIError(response.status_code, error_message)


def get_response_json_supressing_decode_errors(response):
    try:
        json_response = response.json()
    except (ValueError, JSONDecodeError):
        json_response = None
    return json_response

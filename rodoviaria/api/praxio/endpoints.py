import logging
from datetime import date
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPStatus

from requests.adapters import HTT<PERSON><PERSON>pter, Retry
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from rodoviaria.api.executors import Response
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.executors.middlewares.token_bucket_middleware import (
    token_bucket_middleware,
)
from rodoviaria.api.praxio import models
from rodoviaria.models.praxio import PraxioLogin
from rodoviaria.service.exceptions import (
    PassengerTicketAlreadyPrintedException,
    PassengerTicketAlreadyReturnedException,
    PoltronaIndisponivel,
    RodoviariaBlockingException,
    RodoviariaConnectionError,
    RodoviariaInvalidParamsException,
    RodoviariaUnauthorizedError,
)

from .base import BaseRequestConfig, get_response_json_supressing_decode_errors
from .exceptions import PraxioAPIError, RodoviariaCompraParcialPraxioException, ViagemSemTrechoVendido

CIDADES_LISTAR = "Cidades/Listar"
PASSAGEIROS_RETORNA_PASSAGEIROS_VIAGEM = "Passageiros/retornaPassageirosViagem"

rodovlogger = logging.getLogger("rodoviaria")


class EfetuaLoginConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    def __init__(self, login: PraxioLogin, with_auth=False):
        super().__init__(login, with_auth)

    @property
    def url(self):
        return f"{self.login.company.url_base}/Login/efetualogin"

    def build_json_params(
        nome,
        senha,
        cliente,
        sistema="WINVR.EXE",
        tipo_db=0,
        empresa="BUSER",
        tipo_aplicacao=0,
    ):
        body = {
            "Nome": nome,
            "Senha": senha,
            "Sistema": sistema,
            "TipoBD": tipo_db,
            "Empresa": empresa,
            "Cliente": cliente,
            "TipoAplicacao": tipo_aplicacao,
        }
        return models.LoginForm.parse_obj(body).dict(by_alias=True)

    def _raise_for_response(self, response):
        id_erro, err_msg = _get_error_code_and_message_from_response(response)
        if bool(id_erro):
            if id_erro in ("LOG001", "LOG002"):
                rodovlogger.info("PraxioApi login error with response: %s", err_msg)
                raise RodoviariaUnauthorizedError
            super()._raise_for_response(response)


class ListaPartidasTFOConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/Partidas/listaPartidasTFO"

    def _raise_for_status(self, response: Response):
        id_erro, err_msg = _get_error_code_and_message_from_response(response)
        if not id_erro and response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR:
            # É comum que esse endpoint dê erro 500 quando o servidor da Praxio está em sobrecarga. (429 não tratado)
            # Retorna um RodoviariaConnectionError para que os crons de atualização façam retry do request
            raise RodoviariaConnectionError(str(response))
        super()._raise_for_status(response)


class BuscarServicosConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/Viagens/Listar"

    @staticmethod
    def build_json_params(data_inicio: date, data_fim: date, company_external_id: int | None = None):
        data_inicio = data_inicio.strftime("%Y-%m-%d")
        data_fim = data_fim.strftime("%Y-%m-%d")
        condicao_where = f"DataViagem >= '{data_inicio}' and DataViagem <= '{data_fim}'"

        if company_external_id:
            condicao_where += f" and IdEstabelecimento = '{company_external_id}'"
        return {
            "CondicaoWhere": condicao_where,
            "Ordenacao": "DataViagem",
        }


@retry(
    retry=retry_if_exception_type(RodoviariaConnectionError),
    reraise=True,
    stop=stop_after_attempt(2),
)
def buscar_servicos_request(client: PraxioLogin, company_external_id: int, data_inicio: date, data_fim: date):
    response = BuscarServicosConfig(client).invoke(
        get_http_executor(),
        json=BuscarServicosConfig.build_json_params(data_inicio, data_fim, company_external_id),
    )
    response_json = response.json()
    return models.ViagensServicos.parse_obj(response_json["oObj"])


class BuscarTrechosVendidosConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/Partidas/listaTrechosWl"

    http_adapter = HTTPAdapter(
        max_retries=Retry(
            total=3,
            backoff_factor=1,
            raise_on_status=False,
            status_forcelist=[401, 406, 408, 500, 502],
        ),
    )

    def _raise_for_response(self, response: Response):
        id_erro, err_msg = _get_error_code_and_message_from_response(response)
        if id_erro:
            if id_erro == "LIS001":
                raise ViagemSemTrechoVendido(response.status_code, err_msg)
        super()._raise_for_response(response)

    def _raise_for_status(self, response: Response):
        id_erro, err_msg = _get_error_code_and_message_from_response(response)
        if not id_erro and response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR:
            # É comum que esse endpoint dê erro 500 quando o servidor da Praxio está em sobrecarga. (429 não tratado)
            # Retorna um RodoviariaConnectionError para que os crons de atualização façam retry do request
            raise RodoviariaConnectionError(str(response))
        super()._raise_for_status(response)


class BuscarServicoConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return f"{self.login.company.url_base}/Partidas/Partidas"

    http_adapter = HTTPAdapter(
        max_retries=Retry(
            total=3,
            backoff_factor=1,
            raise_on_status=False,
            status_forcelist=[401, 406, 408, 500, 502],
        ),
    )

    def preprocess_request(self, request):
        if self.auth is None:
            return request

        # Para esse endpoint, os nomes dos parametros de autenticacao são diferentes. Então usa regra customizada
        request.json = request.json or {}
        request.json.update(
            {
                "IdSessaoOp": self.auth.id_sessao_op,
                "IdEstabelecimentoVenda": self.auth.id_estabelecimento,
            }
        )
        return request

    def _raise_for_status(self, response: Response):
        id_erro, err_msg = _get_error_code_and_message_from_response(response)
        if response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR:
            # É comum que esse endpoint dê erro 500 quando o servidor da Praxio está em sobrecarga. (429 não tratado)
            # Retorna um RodoviariaConnectionError para que os crons de atualização façam retry do request
            raise RodoviariaConnectionError(str(response))
        if err_msg == "Data da viagem menor que a data atual.":
            raise RodoviariaInvalidParamsException(err_msg)
        super()._raise_for_status(response)


class BuscarDestinosConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/Partidas/Destinos"


class BloquearPoltronaConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return f"{self.login.company.url_base}/Poltrona/verificaPoltrona"


class ReimprimePassagemConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/VendaPassagem/reimprimePassagem"


class DesmarcaPoltronaConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return f"{self.login.company.url_base}/Poltrona/desmarcaPoltrona"

    def _raise_for_response(self, response: Response):
        id_erro, _ = _get_error_code_and_message_from_response(response)
        if id_erro:
            if id_erro == "DEV@":
                raise PassengerTicketAlreadyPrintedException
        super()._raise_for_response(response)


class CancelaPassagemConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/vendaPassagem/cancelaPassagem"

    def _raise_for_response(self, response: Response):
        id_erro, err_msg = _get_error_code_and_message_from_response(response)
        if id_erro:
            if id_erro == "DEV@":
                raise PassengerTicketAlreadyPrintedException
            elif id_erro == "DEV12" and err_msg == "Passagem devolvida.":
                # Acredito que não exista esse erro para esse endpoint, log para confirmar
                rodovlogger.info("Erro DEV12 no cancelamento: %s", err_msg)
                raise PassengerTicketAlreadyReturnedException(err_msg)
            elif id_erro == "CAN004" and err_msg == (
                "Essa passagem não pode ser cancelada porque a venda já foi devolvida."
            ):
                raise PassengerTicketAlreadyReturnedException(err_msg)
            elif id_erro == "DEV18":
                msg = "Excedido tempo limite para devolução"
                raise RodoviariaBlockingException(msg)
            raise PraxioAPIError(response.status_code, err_msg)
        super()._raise_for_response(response)


class VendaPassagemConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    def _raise_for_response(self, response: Response):
        id_erro, msg = _get_error_code_and_message_from_response(response)
        if bool(id_erro):
            payload = response.json()
            if id_erro == "VEN071":
                msg = "Desconto Manual não permitido"
            if id_erro == "VEN102":
                msg = "Soma dos pagamentos menor que o valor da passagem"
            if id_erro == "VEN079":
                msg = "Que pena, não temos vagas suficientes nesse preço para a sua demanda de passageiros."
            if id_erro == "VEN065":
                msg = "Estabelecimento de venda sem Estabelecimento Fiscal cadastrado. Entre em contato com o parceiro."
            if id_erro == "VEN016":
                msg = "Poltrona Indisponível"
                raise PoltronaIndisponivel
            if id_erro == "VEN107":
                msg = "O nome do passageiro deve possuir no mínimo 3 caracteres"

            # Praxio pode dar erro em cada individualmente, então uma pode ter dado certo e outra erro
            # Essa exception garante que se alguma passagem que foi comprada será cancelada
            raise RodoviariaCompraParcialPraxioException(response.status_code, msg, payload)
        super()._raise_for_response(response)


class VendaPassagemVoucherConfig(VendaPassagemConfig):
    @property
    def url(self):
        return f"{self.login.company.url_base}/VendaPassagem/VendaPassagemTFO"


class VendaPassagemBPEConfig(VendaPassagemConfig):
    @property
    def url(self):
        return f"{self.login.company.url_base}/VendaPassagem/VendaPassagemBPE"


class GravaDevolucaoPassagemConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/vendaPassagem/gravaDevolucao"

    def _raise_for_response(self, response: Response):
        id_erro, err_msg = _get_error_code_and_message_from_response(response)
        if id_erro:
            if id_erro == "DEV23":
                msg = "Não foi possível gravar a devolução da passagem"
                raise RodoviariaBlockingException(msg)
            if id_erro == "DEV@":
                raise PassengerTicketAlreadyPrintedException
            elif id_erro == "DEV12" and err_msg == "Passagem devolvida.":
                raise PassengerTicketAlreadyReturnedException(err_msg)
            elif id_erro == "DEV13" and err_msg == "Passagem cancelada.":
                raise PassengerTicketAlreadyReturnedException(err_msg)
            elif id_erro == "DEV18":
                msg = "Excedido tempo limite para devolução"
                raise RodoviariaBlockingException(msg)
            raise PraxioAPIError(response.status_code, err_msg)
        super()._raise_for_response(response)


class RetornaPoltronasConfig(BaseRequestConfig):
    method = HTTPMethod.POST
    disable_middlewares = {
        token_bucket_middleware,
    }

    @property
    def url(self):
        return f"{self.login.company.url_base}/Poltrona/retornaPoltronas"


class PartidasOrigensConfig(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/Partidas/origens"


class ValorTipoPassageiro(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return f"{self.login.company.url_base}/VendaPassagem/retornaValorTipoPassageiro"


def _get_error_code_and_message_from_response(response: Response):
    json_response = get_response_json_supressing_decode_errors(response)
    if not json_response:
        return None, None

    err = json_response.get("IdErro")
    if err:
        return err, json_response.get("Mensagem")

    if json_response.get("oObj") and isinstance(json_response["oObj"], dict):
        passagem = json_response.get("oObj") and json_response.get("oObj").get("Passagem")
        err_passagens = passagem and passagem.get("IdErro")
        if err_passagens:
            return err_passagens, passagem.get("Mensagem")

        list_passagem = passagem and passagem.get("ListaPassagem")
    else:
        list_passagem = json_response.get("ListaPassagem")

    err_passagem_individual = None
    err_msg_passagem_individual = None
    if list_passagem:
        for x in list_passagem:
            err_passagem_individual = x.get("IdErro")
            if err_passagem_individual:
                err_msg_passagem_individual = x.get("Mensagem")
                break
    return err_passagem_individual, err_msg_passagem_individual

import hashlib
import logging
from datetime import date, datetime, timedelta
from decimal import Decimal
from functools import cached_property
from typing import List, Optional

from pydantic import BaseModel, Field, root_validator, validator

from commons.dateutils import to_default_tz
from rodoviaria.api.forms import Localidade, TrechoVendidoAPI
from rodoviaria.models.core import LocalEmbarque, Passagem, Rota

logger = logging.getLogger(__name__)


class RotaPraxio(Rota):
    id: int

    class Meta:
        proxy = True

    @cached_property
    def parsed_data(self):
        return ListaPartidasTFO.parse_raw(self.provider_data)


class LocalidadeParada(Localidade):
    external_local_sigla: str

    @root_validator(pre=True)
    def translate(cls, local):
        nome_cidade = local["Descricao"].split("(")[0].rstrip().title()
        complemento = local["Descricao"].partition(")")[-1].lstrip()
        return {
            "nome_cidade": nome_cidade,
            "external_cidade_id": local["IDLocalidade"],
            "uf": local["Uf"],
            "complemento": complemento,
            "external_local_id": local["IDLocalidade"],
            "external_local_sigla": local["Sigla"],
        }


class LocalPartida(Localidade):
    @root_validator(pre=True)
    def parse_nested_objects(cls, values):
        nome_cidade = values["Descricao"].partition("(")[0].title().rstrip()
        complemento = values["Descricao"].partition(")")[-1].lstrip()
        uf = values["Descricao"].partition("(")[2].partition(")")[0]
        return {
            "nome_cidade": nome_cidade,
            "external_cidade_id": values["IdLocalidade"],
            "uf": uf,
            "complemento": complemento,
            "external_local_id": values["IdLocalidade"],
        }


class PartidaTFO(BaseModel):
    local: LocalidadeParada = Field(alias="Localidade")
    datetime_ida: datetime = Field(alias="DataPartida")
    plataforma: str = Field(alias="Plataforma")
    duracao: Optional[int]
    tempo_embarque: Optional[int]
    distancia: Optional[Decimal]


class ListaPartidasTFO(BaseModel):
    __root__: List[PartidaTFO]

    @property
    def sigla(self):
        return "-".join(p.local.external_local_sigla for p in self.partidas)

    @property
    def _sigla_duracao(self):
        return "-".join(f"{p.local.external_local_sigla}.{p.duracao}" for p in self.partidas)

    @property
    def hash(self):
        partidas = self.partidas
        rota = f"{partidas[0].local.external_local_sigla}{partidas[-1].local.external_local_sigla}"
        digest = hashlib.md5(self._sigla_duracao.encode()).hexdigest()  # noqa: S324
        return f"{rota}{digest}"

    @property
    def partidas(self):
        return sorted(self.__root__, key=lambda p: p.datetime_ida)

    def __getitem__(self, index):
        return self.partidas[index]

    def __iter__(self):
        return iter(self.partidas)

    def __len__(self):
        return len(self.__root__)

    @validator("__root__")
    def _attach_tempos(cls, v):
        if not v:
            return v

        iti_first = v[0]
        iti_first.duracao = 0
        iti_first.tempo_embarque = 0

        iti_last = v[-1]
        iti_last.tempo_embarque = 0

        for iti_i, iti_j in zip(v, v[1:]):
            duracao = iti_j.datetime_ida - iti_i.datetime_ida

            if iti_j == v[-1]:
                # Não tem tempo de embarque no último checkpoint.
                tempo_embarque = timedelta(minutes=0)
            elif duracao <= timedelta(minutes=10):
                # Tempo de embarque em paradas rápidas é o minimo de 1 min.
                tempo_embarque = timedelta(minutes=1)
            else:
                tempo_embarque = timedelta(minutes=10)

            iti_j.tempo_embarque = tempo_embarque.total_seconds()
            iti_j.duracao = (duracao - tempo_embarque).total_seconds()
            iti_j.distancia = abs(timedelta(seconds=iti_j.duracao).seconds // 60)  # estima por 60km/h
            logger.warning(
                "distancia_nula: praxio._attach_tempos",
                extra={"duracao": iti_j.duracao, "distancia": iti_j.distancia},
            )
        return v


class ViagemModel(BaseModel):
    external_id: int = Field(alias="IdViagem")
    linha_id: int = Field(alias="IdCodigoLinha")
    sentido: int = Field(alias="Sentido")
    capacidade: int = Field(alias="QtdLugares")

    @property
    def rota_id(self):
        return int(f"{self.linha_id}{self.sentido}")


class Tarifas(BaseModel):
    """
    >>> data = {
    ...     "ValorTarifa": 62.60,
    ...     "Seguro": 0.00,
    ...     "Pedagio": 0.00,
    ...     "TxEmbarque": 1.35,
    ...     "Desconto": 0.00,
    ...     "Pricing": 0.00
    ... }
    >>> from pydantic import parse_obj_as
    >>> from decimal import Decimal
    >>> form = parse_obj_as(Tarifas, data)
    >>> assert form.valor_tarifa == Decimal("62.60")
    """

    valor_tarifa: Decimal = Field(alias="ValorTarifa")
    seguro: Decimal = Field(alias="Seguro")
    pedagio: Decimal = Field(alias="Pedagio")
    taxa_embarque: Decimal = Field(alias="TxEmbarque")
    desconto: Decimal = Field(alias="Desconto")
    pricing: Decimal = Field(alias="Pricing")


class TrechoVendidoModel(TrechoVendidoAPI):
    """
    >>> data = {
    ...     "IdOrigem": 100,
    ...     "IdDestino": 200,
    ...     "Tarifa": {
    ...         "ValorTarifa": 62.60,
    ...         "Seguro": 0.00,
    ...         "Pedagio": 0.00,
    ...         "TxEmbarque": 1.35,
    ...         "Desconto": 0.00
    ...     },
    ...     "TipoHorario": "Executivo",
    ...     "TipoServico": 6,
    ...     "Capacidade": 42,
    ...     "HoraPartida": "0500",
    ...     "HoraChegada": "0800",
    ...     "KMTrecho": 178.0
    ... }
    >>> from pydantic import parse_obj_as
    >>> form = parse_obj_as(TrechoVendidoModel, data)
    >>> assert form.origem_id == 100
    >>> assert form.preco == Decimal("63.95")
    """

    origem_id: int = Field(alias="IdOrigem")
    destino_id: int = Field(alias="IdDestino")
    tarifas: Tarifas = Field(alias="Tarifa")
    capacidade: int = Field(alias="Capacidade")
    classe: str = Field(alias="TipoHorario")
    vagas: Optional[int] = None

    @property
    def preco(self):
        return (
            self.tarifas.valor_tarifa
            + self.tarifas.pedagio
            + self.tarifas.taxa_embarque
            + self.tarifas.seguro
            - self.tarifas.desconto
            - self.tarifas.pricing
        )


class ViagensServicos(BaseModel):
    """
    >>> data = [
    ...     {
    ...         "IdViagem": 274196,
    ...         "IdCodigoLinha": 43,
    ...         "DataViagem": "2021-11-19T00:00:00",
    ...         "HoraViagem": "1900",
    ...         "QtdLugares": 52,
    ...         "IdVeiculo": None,
    ...         "TipoVeiculo": None,
    ...         "Sentido": 1,
    ...         "HoraChegada": None,
    ...         "IdTipoVeiculo": 1,
    ...         "Tipo": 0,
    ...         "Naoreservar": None,
    ...         "Servico": 666,
    ...         "Situacao": 0,
    ...         "Vigencia": None,
    ...         "Semconexao": 0,
    ...         "CodigoLinha": "1010",
    ...         "IdLinha": "1010",
    ...         "NomeLinha": "CIDADE 21 - CIDADE 22",
    ...         "NomeTPVeic": "CONVENCIONAL",
    ...         "PlacaVeic": None,
    ...         "QtdAndares": 1,
    ...         "PermiteAlterarDadosPartida": None,
    ...         "Partidas": None,
    ...         "DatasAlt": [],
    ...         "PartidasBloqTempo": None
    ...     }
    ... ]
    >>> from pydantic import parse_obj_as
    >>> form = parse_obj_as(ViagensServicos, data)
    >>> assert form[0].external_id == 274196
    """

    __root__: list[ViagemModel]

    def __getitem__(self, index):
        return self.__root__[index]

    def __iter__(self):
        return iter(self.__root__)

    def __len__(self):
        return len(self.__root__)

    def filter_rota(self, rota):
        if not rota:
            return
        self.__root__ = list(filter(lambda r: r.id_external == rota.id_external, self.__root__))


class SessaoModelMixin(BaseModel):
    id_sessao_op: str = Field(alias="IdSessaoOp")


class LoginForm(BaseModel):
    nome: str = Field(alias="Nome")
    senha: str = Field(alias="Senha")
    sistema: str = Field(alias="Sistema")
    tipo_bd: int = Field(alias="TipoBD")
    empresa: str = Field(alias="Empresa")
    cliente: str = Field(alias="Cliente")
    tipo_aplicacao: int = Field(alias="TipoAplicacao")


class BloquearPoltronaForm(SessaoModelMixin, BaseModel):
    id_viagem: int = Field(alias="IdViagem")
    id_poltrona: int = Field(alias="IdPoltrona")
    fuso_horario: Optional[str] = Field(alias="FusoHorario")
    id_tipo_veiculo: int = Field(alias="IdTipoVeiculo")
    id_loc_origem: int = Field(alias="IdLocOrigem")
    id_loc_destino: int = Field(alias="IdLocDestino")
    andar: Optional[int] = Field(alias="Andar")
    bloqueia: int = Field(alias="Bloqueia")
    verificar_sugestao: int = Field(alias="VerificarSugestao", default=1)

    class Config:
        allow_population_by_field_name = True


class BloquearPoltronaListForm(BaseModel):
    list_bloquear_poltrona: list[BloquearPoltronaForm] = Field(alias="listBloquearPoltrona")


class DesbloquearPoltronaForm(SessaoModelMixin, BaseModel):
    id_viagem: int = Field(alias="IdViagem")
    id_poltrona: int = Field(alias="IdPoltrona")
    fuso_horario: Optional[str] = Field(alias="FusoHorario")
    id_tipo_veiculo: Optional[int] = Field(alias="IdTipoVeiculo")
    id_loc_origem: Optional[int] = Field(alias="IdLocOrigem")
    id_loc_destino: Optional[int] = Field(alias="IdLocDestino")
    andar: Optional[int] = Field(alias="Andar")
    bloqueia: Optional[int] = Field(alias="Bloqueia")
    verificar_sugestao: Optional[int] = Field(alias="VerificarSugestao")


class DesbloquearPoltronaListForm(BaseModel):
    list_desbloquear_poltrona: list = Field(alias="listDesbloquearPoltrona")


class BuscaOrigemForm(SessaoModelMixin, BaseModel):
    id_estabelecimento: int = Field(alias="IdEstabelecimento")


class ListaCidadesForm(SessaoModelMixin, BaseModel):
    pass


class PagamentoXmlItem(BaseModel):
    data_pagamento: datetime = Field(alias="DataPagamento")
    tipo_pagamento: int = Field(alias="TipoPagamento")
    valor_pagamento: Decimal = Field(alias="ValorPagamento")


class PassagemXmlItem(BaseModel):
    id_estabelecimento: int = Field(alias="IdEstabelecimento")
    serie_bloco: str = Field(alias="SerieBloco")
    num_passagem: int = Field(alias="NumPassagem")
    poltrona: int = Field(alias="Poltrona")
    desconto: Decimal = Field(alias="Desconto")
    desconto_manual: int = Field(alias="DescontoManual")
    pricing: Decimal = Field(alias="Pricing")
    id_desconto: int = Field(alias="IdDesconto")
    id_rota: int = Field(alias="IdRota")
    nome_cli: str = Field(alias="NomeCli")
    identidade_cli: str = Field(alias="IdentidadeCli")
    cpf_cnpj_cli: Optional[str] = Field(alias="CpfCnpjCli")
    telefone_1: str = Field(alias="Telefone1")
    tipo_passageiro: Optional[str] = Field(alias="TipoPassageiro")


class VendaXmlEnvioItem(SessaoModelMixin, BaseModel):
    id_estabelecimento_venda: int = Field(alias="IdEstabelecimentoVenda")
    id_viagem: int = Field(alias="IdViagem")
    hora_partida: str = Field(alias="HoraPartida")
    id_origem: int = Field(alias="IdOrigem")
    id_destino: int = Field(alias="IdDestino")
    embarque: str = Field(alias="Embarque")
    seguro: str = Field(alias="Seguro")
    excesso: str = Field(alias="Excesso")
    id_caixa: int = Field(alias="IdCaixa")
    bpe: int = Field(alias="BPe")
    passagem_xml: List[PassagemXmlItem] = Field(alias="PassagemXml")
    pagamento_xml: List[PagamentoXmlItem] = Field(alias="pagamentoXml")


class ConfirmarVendaFormBPE(BaseModel):
    list_vendas_xml_envio: List[VendaXmlEnvioItem] = Field(alias="listVendasXmlEnvio")


class ConfirmarVendaFormVoucher(BaseModel):
    list_vendas_xml_envio: List[VendaXmlEnvioItem] = Field(alias="ListaVendaXmlEnvio")


class CancelarVendaForm(SessaoModelMixin, BaseModel):
    id_estabelecimento: int = Field(alias="IdEstabelecimento")
    serie_bloco: str = Field(alias="SerieBloco")
    num_passagem: int = Field(alias="NumPassagem")
    serie_bloco: Optional[str] = Field(alias="SerieBloco")
    num_passagem: int = Field(alias="NumPassagem")
    id_caixa: Optional[int] = Field(alias="IdCaixa")


class PassagemDevolucao(BaseModel):
    serie_bloco: str = Field(alias="SerieBloco")
    numero_passagem: int = Field(alias="NumeroPassagem")
    id_estabelecimento: int = Field(alias="IdEstabelecimento")
    valor_devolucao: Decimal = Field(alias="ValorDevolucao")
    percentual_desconto: Optional[int] = Field(alias="PercDesconto")
    tipo_passageiro: Optional[int] = Field(alias="TipoPassageiro")


class GravaDevolucaoForm(SessaoModelMixin, BaseModel):
    id_estabelecimento: int = Field(alias="IdEstabelecimento")
    id_estabelecimento_devolucao: int = Field(alias="IdEstabelecimentoDevolucao")
    valor_venda: Decimal = Field(alias="ValorVenda")
    passagem: PassagemDevolucao = Field(alias="Passagem")


class CancelarVendaListForm(BaseModel):
    list_cancelar_venda_form: list = Field(alias="listCancelarVenda")


class BloquearConfirmarModel(BaseModel):
    bloquear_poltrona_params: list[BloquearPoltronaForm]
    confirmar_venda_params: dict


class InfosCacheaveisBloqueioPoltrona(BaseModel):
    """
    >>> from decimal import Decimal
    >>> data = dict(
    ... transacao="bla",
    ... preco=Decimal("10.00"),
    ... preco_conexao=Decimal("3.00"),
    ... origem="2",
    ... destino="3",
    ... is_conexao=False
    ... )
    >>> model = InfosCacheaveisBloqueioPoltrona.parse_obj(data)
    >>> assert model.preco == Decimal("10.00")
    >>> assert model.preco - model.preco_conexao == Decimal("7.00")
    """

    transacao: str
    preco: Decimal
    preco_conexao: Decimal | None = None
    origem: str
    destino: str
    data_hora_partida: str
    is_conexao: bool = False


class BilheteObj(BaseModel):
    id_parametro: int = Field(alias="ID_PARAMETRO")
    id_estabelecimento: Optional[int] = Field(alias="ID_ESTABELECIMENTO")
    id_localidade: Optional[int] = Field(alias="ID_LOCALIDADE")
    nome_tarifa: Optional[str] = Field(alias="NOME_TARIFA")
    codigo_tarifa: Optional[str] = Field(alias="CODIGO_TARIFA")
    km: Optional[int] = Field(alias="KM")
    orgao_regulador: Optional[str] = Field(alias="ORGAO_REGULADOR")
    data_hora_carga: Optional[str] = Field(alias="DATA_HORA_CARGA")
    nome_estab: Optional[str] = Field(alias="NomeEstab")
    nome_loc: Optional[str] = Field(alias="NomeLoc")


class ConsultarBilheteForm(SessaoModelMixin, BaseModel):
    pass


class BuscarItinerarioCorridaForm(SessaoModelMixin, BaseModel):
    id_viagem: int = Field(alias="IdViagem")
    busca: Optional[str] = Field(alias="Busca")
    sincronizacao: Optional[bool] = Field(alias="Sincronizacao")
    fuso_horario: Optional[str] = Field(alias="FusoHorario")
    id_origem: Optional[int] = Field(alias="IdOrigem")


class RetornaPoltronasForm(SessaoModelMixin, BaseModel):
    id_viagem: int = Field(alias="IdViagem")
    id_tipo_veiculo: int = Field(alias="IdTipoVeiculo")
    id_loc_origem: int = Field(alias="IdLocOrigem")
    id_loc_destino: int = Field(alias="IdLocDestino")
    fuso_horario: Optional[str] = Field(alias="FusoHorario")
    andar: Optional[int] = Field(alias="Andar")
    bloqueia: Optional[int] = Field(alias="Bloqueia")
    verificar_sugestao: Optional[int] = Field(alias="VerificarSugestao")
    andar: Optional[int] = Field(alias="Andar")


class BuscarServicoForm(SessaoModelMixin, BaseModel):
    id_estabelecimento_venda: int = Field(alias="IdEstabelecimentoVenda")
    localidade_origem: int = Field(alias="LocalidadeOrigem")
    localidade_destino: int = Field(alias="LocalidadeDestino")
    data_partida: date = Field(alias="DataPartida")
    tempo_partida: int = Field(alias="TempoPartida")
    desconto_automatico: int = Field(alias="DescontoAutomatico")
    sugestao_passagem: int = Field(alias="sugestaoPassagem")
    id_estabelecimento_linha: Optional[int] = Field(alias="IdEstabelecimento")


class ListaPassageirosForm(SessaoModelMixin, BaseModel):
    id_viagem: int = Field(alias="IdViagem")


class ListaPassageirosRetornoForm(BaseModel):
    nome: str = Field(alias="Nome")
    origem: str = Field(alias="Origem")
    destino: str = Field(alias="Destino")
    id_origem: int = Field(alias="IdOrigem")
    id_destino: int = Field(alias="IdDestino")
    cpf: str = Field(alias="Cpf")
    documento: str = Field(alias="Docuemnto")
    poltrona: int = Field(alias="Poltrona")
    passagem: int = Field(alias="NumPassagem")


class ListaTrechosViagemForm(SessaoModelMixin):
    id_viagem: int = Field(alias="IdViagem")


class ListaViagensForm(SessaoModelMixin):
    condicao: str = Field(alias="CondicaoWhere")


class LoginSessaoForm(BaseModel):
    """
    >>> data = {
    ...     "ParametrosEntrada": {},
    ...     "Comissao": 0.00,
    ...     "RelatorioAgencia": 0,
    ...     "Telefone": "",
    ...     "Email": None,
    ...     "MaxDescManual": 0.00,
    ...     "MaxAcresManual": 0.00,
    ...     "PermDescManualOperador": False,
    ...     "PermAcresManualOperador": False,
    ...     "DescontoPercentual": 0,
    ...     "AcrescimoPercentual": 0,
    ...     "IdOperador": 54,
    ...     "IdSessaoOp": "0B179...89==",
    ...     "Nome": "BUSER",
    ...     "Super": 0,
    ...     "TempoInativarOperadorAposTempo": 0,
    ...     "TempoSessao": 720,
    ...     "VisualizouLGPD": 0,
    ...     "IdJuncao": 0,
    ...     "RelSuperOp": False,
    ...     "LiberarCancTEF": False,
    ...     "LiberarReimpPass": False,
    ...     "LiberarMarcarVoucher": False,
    ...     "InativarOperadorAposTempo": False,
    ...     "VisualizouTermoLGPD": 0,
    ...     "VendDevOutroCaixaSemCaixa": False,
    ...     "UsuarioConsulta": False,
    ...     "ApenasBiometria": None,
    ...     "AlterarSenhaOperadorXDias": False,
    ...     "TempoAlterarSenhaOperadorXDias": 0,
    ...     "PassouQtdDiasAlterarSenha": False,
    ...     "ObrigaCadastroPos": None,
    ...     "EstabelecimentoPadrao": 0,
    ...     "EstabelecimentoXml": {
    ...         "AtualizaSegurancaCertificado": None,
    ...         "IDEstabelecimento": 8,
    ...         "RazaoSocial": "BUSER",
    ...         "NomeFantasia": "BUSER",
    ...         "Cnpj": "00033613000125",
    ...         "Endereco": "Avenida Governador Valadares",
    ...         "Uf": "MG",
    ...         "IMunicipal": "ISENTO",
    ...         "IEstadual": "7045287440058",
    ...         "Telefone": "",
    ...         "IdEmpresa": 1,
    ...         "Numero": "1817",
    ...         "Complemento": "empresa",
    ...         "Bairro": "Centro",
    ...         "IdCidade": 3170404,
    ...         "NomeCidade": "Unaí",
    ...         "Cep": "38610014",
    ...         "Cnae": None,
    ...         "Certificado": None,
    ...         "IdMoeda": 0,
    ...         "AmbienteProducao": False,
    ...         "TARBPe": None,
    ...         "SSLType": None,
    ...         "SSLLib": None,
    ...         "SSLHttpLib": None,
    ...         "SSLCryptLib": None,
    ...         "SSLXmlSignLib": None,
    ...         "SerieBpe": 300,
    ...         "Fax": None,
    ...         "Contato": None,
    ...         "Obs": None,
    ...         "CupomEmbarqueVoucher": False,
    ...         "NaoEmbarqueAutomatico": False,
    ...         "NaoEmbarqueAutomaticoAbertura": False,
    ...         "UtilizarBpe": None,
    ...         "FormatoLogo": None,
    ...         "Logo": None,
    ...         "CodigoExterno2": None,
    ...         "CodigoExterno": None,
    ...         "InscricaoJuntac": None,
    ...         "Agenciarod": None,
    ...         "NumeracaoMatriz": None,
    ...         "DadosMatriz": None,
    ...         "EstabelecimentoEmissor": None,
    ...         "NumeroN": None,
    ...         "FormaPgto": [],
    ...         "AmbienteBpe": None,
    ...         "AgenciaBloqueada": 0,
    ...         "MensagemAlerta": "",
    ...         "EstornarComissaoDevolucao": 0,
    ...         "TaxaEmbarqueRepasse": False,
    ...         "ComputarTaxaPorDataViagem": False,
    ...         "ComputarTxRepasseVoucher": False,
    ...         "LocalidadeRepasseTaxaEmbarque": 0,
    ...         "DescLocalidadeRepasse": None,
    ...         "ListarEstabSemGrupoAgencia": False,
    ...         "IdGrupoAgencia": None,
    ...         "NaoCobrarTaxaEmbarque": False,
    ...         "CnpjTef": None,
    ...         "IpServidorTef": None,
    ...         "LojaTef": None,
    ...         "TerminalTef": None,
    ...         "PortaTef": None,
    ...         "MsgPinpadTef": None,
    ...         "EmpresaTef": 0,
    ...         "ImprimirTxEmbW2iSemDll": False,
    ...         "BloqueioPartidas": False,
    ...         "PartidasBloqueio": False,
    ...         "PartidasPermitidas": [],
    ...         "GeraQrcodePix": 0,
    ...         "TipoChavePix": 0,
    ...         "ChavePix": None,
    ...         "ECommerce": 0,
    ...         "OcultarAgenciaRelatorio": False,
    ...         "PagamentoPixTef": False,
    ...         "VendaManualCartao": False,
    ...         "VendaManualPix": False,
    ...         "UtilizaSolAutReimp": None,
    ...         "UtilizaSolAutReimpExcesso": None,
    ...         "PassagemWhatsapp": False,
    ...         "CelularWhatsapp": None,
    ...         "TokenPlugSocial": None,
    ...         "CadastraTerminalTef": 0,
    ...         "ListaTerminalTef": None,
    ...         "ImpressaoPadrao": 0,
    ...         "AbrirCaixaAuto": 0,
    ...         "RetornoSimples": False,
    ...         "OrigemPadrao": 0,
    ...         "SerieNaoFiscal": "",
    ...         "DadosEmissaoBPe": None,
    ...         "BlocoEletronico": 0,
    ...         "QtdCupomEmbarqueNFiscal": None,
    ...         "ControlarSaldoFechamento": 0,
    ...         "DadosControlarSaldoFechamento": None,
    ...         "ExibirStatusImpressora": None,
    ...         "TaxaConveniencia": None,
    ...         "PercTaxaConveniencia": None,
    ...         "MensagemTaxaConveniencia": None,
    ...         "AliquotaIssTaxaConveniencia": None,
    ...         "BlocoBarco": 0,
    ...         "TokenFidelidade": None,
    ...         "NaoAplicarTxEmbarque": False,
    ...         "Latitude": "",
    ...         "Longitude": "",
    ...         "ChavePixBB": None,
    ...         "DevAppKeyPixBB": None,
    ...         "ClientIdPixBB": None,
    ...         "ClientSecretPixBB": None,
    ...         "BasicTokenPixBB": None,
    ...         "UrlLoginPixBB": None,
    ...         "UrlApiPixBB": None,
    ...         "ConsiderarApenasBPe": False,
    ...         "ReplicarCertificadoGrupoAgencia": False,
    ...         "ComissaoDataAcerto": False,
    ...         "DataComissao": None,
    ...         "NaoEmbApenasSolAut": False,
    ...         "ImprimeBlocoEletronicoPdf": False
    ...     },
    ...     "GaragemXml": {
    ...         "IDGaragem": 4,
    ...         "Descricao": "BUSER",
    ...         "Situacao": 1,
    ...         "CodigoExterno": 4
    ...     },
    ...     "EmpresaXml": {
    ...         "NomeFantasia": "SANTA IZABEL TRANSPORTES  E TURISMO LTDA - MG",
    ...         "RazaoSocial": "SANTA IZABEL TRANSPORTES  E TURISMO LTDA - MG",
    ...         "Email": "",
    ...         "Uf": "MG",
    ...         "Cnpj": "00033613000125",
    ...         "IMunicipal": "ISENTO",
    ...         "IEstadual": "7045287440058",
    ...         "Endereco": "Avenida Governador Valadares",
    ...         "Numero": "1817",
    ...         "Complemento": "",
    ...         "Bairro": "Centro",
    ...         "IdCidade": 3170404,
    ...         "NomeCidade": "Unaí",
    ...         "Cep": "38610014",
    ...         "Telefone": "",
    ...         "TokenFidelidade": "",
    ...         "CodigoIndicacaoFidelidade": "",
    ...         "Numero1": "1817",
    ...         "PoliticaPrivacidade": ""
    ...     },
    ...     "IdPerfilOperador": 8,
    ...     "Xml": None,
    ...     "IdErro": "",
    ...     "Mensagem": "Login efetuado com sucesso!",
    ...     "MensagemDetalhada": "Login efetuado com sucesso!",
    ...     "Strings": [],
    ...     "Integers": [],
    ...     "Floats": [],
    ...     "VarStr": None,
    ...     "VarInt": 0,
    ...     "VarFloat": 0.0,
    ...     "Sucesso": True,
    ...     "Advertencia": False
    ... }
    >>> from pydantic import parse_obj_as
    >>> form = parse_obj_as(LoginSessaoForm, data)
    >>> assert form.id_sessao_op == "0B179...89=="
    >>> assert form.serie_bpe == 300
    """

    id_sessao_op: str = Field(alias="IdSessaoOp")
    id_estabelecimento: int
    serie_bpe: int
    tempo_sessao: int = Field(alias="TempoSessao")
    new_login: bool

    @root_validator(pre=True)
    def parse_nested_objects(cls, values):
        values["id_estabelecimento"] = values["EstabelecimentoXml"]["IDEstabelecimento"]
        values["serie_bpe"] = values["EstabelecimentoXml"]["SerieBpe"]
        values["new_login"] = True if values["IdSessaoOp"] else False
        return values


class SimpleLocalidade(BaseModel):
    external_local_id: str = Field(alias="IdLocalidade")


class ViagemTFO(BaseModel):
    tipo_horario: str = Field(alias="TipoHorario")
    tipo_servico: int = Field(alias="TipoServico")


class Partida(BaseModel):
    viagem_tfo: ViagemTFO = Field(alias="ViagemTFO")


class PassagemBaseModel(BaseModel):
    prefixo: str = Field(alias="IdLinha")
    linha: str = Field(alias="NomeLinha")
    plataforma: str = Field(alias="Plataforma")

    preco_base: Decimal = Field(alias="ValorTarifa")
    embarque: Decimal = Field(alias="TaxaEmbarque")
    seguro: Decimal = Field(alias="Seguro")
    pedagio: Decimal = Field(alias="Pedagio")
    outras_taxas: Decimal = Field(alias="ValorOutrosTrib")

    @validator("preco_base", "embarque", "seguro", "pedagio", "outras_taxas", pre=True)
    def parse_valores(cls, v):
        if isinstance(v, str):
            v = v.replace(",", ".")
        return Decimal(str(v)) if v else 0


class PassagemBPE(PassagemBaseModel):
    # Será?
    identificador_provider_data: int = Field(alias="NumPassagem")

    numero_passagem: str = Field(alias="NumPassagem")
    numero_bpe: str = Field(alias="NumPassagem")
    serie_bpe: str = Field(alias="SerieBloco")
    id_estabelecimento: str = Field(alias="IDEstabelecimento")
    numero_bilhete_embarque: Optional[str]
    tipo_taxa_embarque: Optional[str]
    codigo_barras_curitiba: Optional[str] = Field(alias="CodigoBarrasCuritiba")

    @root_validator(pre=True)
    def parse_nested_objects(cls, values):
        values["numero_bilhete_embarque"] = (values.get("DadosBilheteEmbarque", {}) or {}).get(
            "NumeroBilheteEmbarque", None
        )
        if values["numero_bilhete_embarque"]:
            values["tipo_taxa_embarque"] = Passagem.TipoTaxaEmbarque.QRCODE
        return values


class PassagemVoucher(PassagemBaseModel):
    # Será?
    identificador_provider_data: str = Field(alias="Localizador")

    localizador: str = Field(alias="Localizador")
    numero_passagem: str = Field(alias="Localizador")
    serie_bpe: str = "@"
    id_estabelecimento: str = Field(alias="Localizador")

    @validator("numero_passagem", pre=True)
    def parse_numero_passagem(cls, v):
        return int(v.split("-")[0])

    @validator("id_estabelecimento", pre=True)
    def parse_id_estabelecimento(cls, v):
        return int(v.split("@")[1])


class BPE(BaseModel):
    protocolo_autorizacao: str = Field(alias="Protocolo")
    bpe_qrcode: str = Field(alias="QrCode")
    chave_bpe: str = Field(alias="Chave")
    data_autorizacao: Optional[datetime] = Field(alias="DataAutorizacao")
    monitriip: Optional[str] = Field(alias="Monitriip")

    @validator("data_autorizacao", pre=True)
    def parse_datetime_ida(cls, v):
        if not v:
            return None
        try:
            # Caso algum erro aconteça na criação do BPE, a data de autorização vem como 30/12/1899
            # Ele vai dar erro de conversão por causa do formato, e salvamos como None
            return to_default_tz(datetime.strptime(v, "%d/%m/%Y %H:%M:%S"))
        except ValueError:
            return None


class MapaPoltronaItem(BaseModel):
    disponivel: bool
    numero: str
    categoria_especial: Passagem.CategoriaEspecial


class TrechoCompra(BaseModel):
    desconto: Decimal
    preco: Decimal
    id_rota: int
    pricing: Decimal
    origem: LocalEmbarque
    destino: LocalEmbarque

    class Config:
        arbitrary_types_allowed = True

import logging
from datetime import datetime

from requests.auth import AuthBase

import rodoviaria.api.praxio.endpoints as endpoints
from commons.memcache import getmc_pymemcache
from rodoviaria.api.executors.impl import get_http_executor

# from rodoviaria.api.praxio.praxio_api import PraxioAPI
from rodoviaria.api.praxio import models

# from rodoviaria.models import Company, PraxioLogin
# from rodoviaria.service.exceptions import RodoviariaLoginNotFoundException

logger = logging.getLogger("rodoviaria")
CACHEKEY_PREFIX = "PRAXIO_LUNA_TOKEN_COMPANY_"
SOFT_RENEW_TIMESTAMP_THRESHOLD = 5 * 60


class PraxioAuth(AuthBase):
    def __init__(
        self,
        id_sessao_op: str,
        id_estabelecimento: int,
        serie_bpe: int,
        tempo_sessao: int | None = None,
        timestamp=None,
        new_login=None,
    ):
        self.id_sessao_op = id_sessao_op
        self.id_estabelecimento = id_estabelecimento
        self.serie_bpe = serie_bpe
        self.tempo_sessao = tempo_sessao
        self.timestamp = timestamp
        self.new_login = new_login

    @classmethod
    def from_client(cls, client, soft_renew=False, force_renew=False):
        mc = getmc_pymemcache()
        cache_key = f"{CACHEKEY_PREFIX}{client.company.id}"
        resp = mc.get(cache_key)

        current_timestamp = datetime.now().timestamp()
        has_timestamp = resp and "timestamp" in resp
        is_above_soft_renew_threshold = (
            has_timestamp and (current_timestamp - resp["timestamp"]) > SOFT_RENEW_TIMESTAMP_THRESHOLD
        )
        should_soft_renew = soft_renew and (is_above_soft_renew_threshold or not has_timestamp)
        if not resp or should_soft_renew or force_renew:
            login_info = cls.login(client)
            instance = cls(
                login_info.id_sessao_op,
                login_info.id_estabelecimento,
                login_info.serie_bpe,
                (login_info.tempo_sessao * 60)
                - 120,  # tempo sessao 2min a menos que o real para garantir que atualize antes de expirar
                datetime.now().timestamp(),
            )

            mc.set(key=cache_key, value=vars(instance), timeout=instance.tempo_sessao)
            return instance

        return cls(**resp)

    @classmethod
    def login(cls, login):
        resp = endpoints.EfetuaLoginConfig(login, with_auth=False).invoke(
            get_http_executor(),
            json=endpoints.EfetuaLoginConfig.build_json_params(
                login.name,
                login.password,
                login.cliente,
                login.sistema,
                login.tipo_bd,
                login.empresa,
                login.tipo_aplicacao,
            ),
        )
        form = models.LoginSessaoForm.parse_obj(resp.json())
        return form

    def __call__(self, request):
        return request

    @property
    def request_data(self):
        """Campos que devem ser enviados em cada requisição autenticada."""
        return {
            "IdSessaoOp": self.id_sessao_op,
            "IdEstabelecimento": self.id_estabelecimento,
            "SerieBpe": self.serie_bpe,
        }

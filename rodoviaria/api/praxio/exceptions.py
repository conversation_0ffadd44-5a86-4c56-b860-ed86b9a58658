from rodoviaria.service.exceptions import APIError, RodoviariaBlockingException


class PraxioAPIError(APIError):
    status_code = None

    def __init__(self, status_code, message=None):
        super().__init__(message)
        self.status_code = status_code


class ViagemSemTrechoVendido(PraxioAPIError):
    pass


class RodoviariaCompraParcialPraxioException(PraxioAPIError, RodoviariaBlockingException):
    """
    Quando uma compra na Praxio dá erro, mas pelo menos 1 passagem foi confirmada
    """

    def __init__(self, status_code, message, json_response=None):
        self.json_response = json_response
        self.message = message
        self.status_code = status_code
        super().__init__(self.status_code, self.message)

    def __str__(self):
        return self.message

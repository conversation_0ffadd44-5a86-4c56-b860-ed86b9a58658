import logging
from datetime import datetime, timedelta
from functools import partial, wraps

import beeline
from beeline import traced
from django.utils import timezone

from bp.buserdjango_celery import atualiza_trecho_raw
from commons.circuit_breaker import circuit_method
from commons.dateutils import to_tz
from commons.redis import get_key, set_key
from rodoviaria.api import set_current_integration
from rodoviaria.api.eulabs.api import EulabsAPI
from rodoviaria.api.executors.middlewares.token_bucket_middleware import token_bucket_middleware
from rodoviaria.api.forms import BuscarServicoForm, CheckpointsForm
from rodoviaria.api.guichepass.api import GuichepassAPI
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.api.smartbus.api import SmartbusAPI
from rodoviaria.api.ti_sistemas.api import TiSistemasAPI
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.models.core import Checkpoint, Company, LocalEmbarque, Passagem
from rodoviaria.serializers.serializer_passagem import PassagemBasicSerializer
from rodoviaria.service import novos_modelos_svc
from rodoviaria.service.class_match_svc import get_buser_class_by_company_internal_id
from rodoviaria.service.exceptions import (
    RodoviariaCompanyNotFoundException,
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaIntegracaoNotFoundException,
    RodoviariaTooManyRequestsError,
    RodoviariaTrechoNotFoundException,
    RodoviariaUnsupportedFeatureException,
)

buserlogger = logging.getLogger("rodoviaria")


def feature_required(feature):
    def wrapper(func):
        @wraps(func)
        def inner(orchestrator, *args, **kwargs):
            if orchestrator.provider.company.has_feature(feature):
                return func(orchestrator, *args, **kwargs)
            raise RodoviariaUnsupportedFeatureException(feature)

        return inner

    return wrapper


def _tracer(fn=None, name=None):
    """Create a trace span with provider name and company_internal_id."""

    if fn is None:
        return partial(_tracer, name=name)

    if name is None:
        name = fn.__name__

    @wraps(fn)
    def with_tracer(orchestrator, *args, **kwargs):
        trace_name = f"action.{name}"

        with beeline.tracer(trace_name):
            beeline.add_context(
                {
                    "company": orchestrator.company_internal_id,
                    "provider": orchestrator.provider_name,
                }
            )
            return fn(orchestrator, *args, **kwargs)

    return with_tracer


company_active_required = feature_required("active")


class OrchestrateRodoviaria:
    PROVIDERS = {
        "praxio": PraxioAPI,
        "totalbus": TotalbusAPI,
        "guichepass": GuichepassAPI,
        "vexado": VexadoAPI,
        "eulabs": EulabsAPI,
        "smartbus": SmartbusAPI,
        "ti_sistemas": TiSistemasAPI,
    }
    _pool = {}

    def __init__(self, company_internal_id, modelo_venda=Company.ModeloVenda.MARKETPLACE):
        self.company_internal_id = company_internal_id
        self.modelo_venda = modelo_venda

    def __new__(cls, company_internal_id, modelo_venda=Company.ModeloVenda.MARKETPLACE):
        key = f"{company_internal_id}_{modelo_venda}"
        obj = cls._pool.get(key)
        if obj is None:
            obj = super().__new__(cls)
            obj.__init__(company_internal_id, modelo_venda)
            cls._pool[key] = obj

        return obj

    def __repr__(self):
        return f"ORCHESTRATOR_{self.provider.__class__.__name__}_{self.company_internal_id}_{self.modelo_venda}"

    @classmethod
    def from_company(cls, company):
        return cls(company_internal_id=company.company_internal_id, modelo_venda=company.modelo_venda)

    def _resolve_provider(self):
        try:
            company_qs = Company.objects.select_related("integracao").filter(
                company_internal_id=self.company_internal_id
            )
            if self.modelo_venda:
                company_qs = company_qs.filter(modelo_venda=self.modelo_venda)
            company = company_qs.get()

        except Company.DoesNotExist as e:
            raise RodoviariaCompanyNotFoundException("rodoviaria.Company not registered") from e

        integracao_name = company.integracao.name
        try:
            provider = self.PROVIDERS[integracao_name.lower()]
        except KeyError as exc:
            raise RodoviariaIntegracaoNotFoundException(integracao_name.lower()) from exc

        set_current_integration(integracao_name.lower())
        return provider(company)

    @property
    def provider(self):
        if not hasattr(self, "_provider"):
            self._provider = self._resolve_provider()
        return self._provider

    @property
    def provider_name(self):
        return self.provider.__class__.__name__

    def atualiza_origens(self):
        return self.provider.atualiza_origens()

    def cidades_destino(self, origem_external_id):
        return self.provider.cidades_destino(origem_external_id)

    def map_cidades_destinos(self):
        return self.provider.map_cidades_destinos()

    @_tracer()
    def cancela_venda(self, params):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.cancela_venda(params)

    @_tracer()
    @company_active_required
    def comprar(self, params):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.comprar(params)

    @feature_required("itinerario")
    def cadastrar_trechos(self, params):
        return self.provider.cadastrar_trechos(params)

    @feature_required("itinerario")
    def cadastrar_grupo(self, params):
        return self.provider.cadastrar_grupo(params)

    @feature_required("itinerario")
    def buscar_todos_servicos(self, periodo_dias=None):
        return self.provider.buscar_todos_servicos(periodo_dias)

    @feature_required("itinerario")
    def buscar_servicos_por_data(self, data_inicio, data_fim):
        return self.provider.buscar_servicos_por_data(data_inicio, data_fim)

    @feature_required("itinerario")
    def fetch_data_limite_servicos(self, data_inicio):
        return self.provider.fetch_data_limite_servicos(data_inicio)

    @circuit_method()
    @feature_required("itinerario")
    def buscar_itinerario(self, params):
        return self.provider.buscar_itinerario(params)

    @_tracer()
    @company_active_required
    def get_poltronas_livres(self, params):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.get_poltronas_livres(params)

    @feature_required("add_pax_staff")
    def get_map_poltronas(self, trechoclasse_id):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.get_map_poltronas(trechoclasse_id)

    @company_active_required
    def lista_cidades(self):
        return self.provider.lista_cidades()

    @company_active_required
    def verifica_poltrona(self, params):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.verifica_poltrona(params)

    @company_active_required
    def vagas_por_categoria_especial(self, trechoclasse_id):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.vagas_por_categoria_especial(trechoclasse_id)

    @company_active_required
    def desbloquear_poltronas(self, trecho_classe_id, poltronas):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.desbloquear_poltronas(trecho_classe_id, poltronas)

    @_tracer()
    @company_active_required
    def bloquear_poltronas(self, trecho_classe_id, poltronas):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.bloquear_poltronas(trecho_classe_id, poltronas)

    @_tracer()
    @company_active_required
    def bloquear_poltronas_v2(self, trecho_classe_id, poltrona, categoria_especial):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.bloquear_poltronas_v2(trecho_classe_id, poltrona, categoria_especial)

    @feature_required("add_pax_staff")
    def lista_passageiros_viagem(self, travels_ids):
        lista_passagens_rodoviaria = Passagem.objects.filter(
            status__in=Passagem.STATUS_CONFIRMADA_LIST, travel_internal_id__in=travels_ids
        )
        lista_passagens_rodoviaria = lista_passagens_rodoviaria.to_serialize(PassagemBasicSerializer)
        return [passagem.serialize() for passagem in lista_passagens_rodoviaria]

    @feature_required("add_pax_staff")
    def add_pax_na_lista_passageiros_viagem(self, params):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            return self.provider.add_pax_na_lista_passageiros_viagem(params)

    @feature_required("add_pax_staff")
    def add_multiple_pax_na_lista_passageiros_viagem(self, multiple_pax_form):
        buseiros_ids = multiple_pax_form.buseiros_ids
        travels_ids = multiple_pax_form.travels_ids
        passagens_existentes = list(
            Passagem.objects.filter(
                buseiro_internal_id__in=buseiros_ids,
                travel_internal_id__in=travels_ids,
                status__in=Passagem.STATUS_CONFIRMADA_LIST,
            ).values_list("buseiro_internal_id", "travel_internal_id")
        )
        for travel in multiple_pax_form.travels:
            travel.buseiros = [b for b in travel.buseiros if (b.id, travel.travel_id) not in passagens_existentes]
            if len(travel.buseiros) == 0:
                multiple_pax_form.travels.remove(travel)
        if len(multiple_pax_form.travels) == 0:
            return
        return self.provider.add_multiple_pax_na_lista_passageiros_viagem(multiple_pax_form)

    @circuit_method(reset_timeout=8, expected_exceptions=(RodoviariaTooManyRequestsError, RodoviariaConnectionError))
    @feature_required("itinerario")
    def fetch_rota(self, external_id, datetime_ida):
        itinerario = self.provider.itinerario(external_id, datetime_ida)

        if not itinerario or not itinerario.parsed:
            return None
        return itinerario

    def rota_id_from_trecho_classe(self, trecho_classe):
        return self.provider.rota_id_from_trecho_classe(trecho_classe)

    def create_or_update_rota(self, id_hash, company_id, defaults):
        buserlogger.info(
            "create_or_update_rota company_id=%s id_hash=%s",
            company_id,
            id_hash,
            extra={"id_hash": id_hash, "company_internal_id": company_id, "_defaults": defaults},
        )
        return self.provider.Rota.objects.update_or_create(id_hash=id_hash, company_id=company_id, defaults=defaults)

    @feature_required("itinerario")
    def get_rotas_empresa(self, company_rodoviaria_id):
        return self.provider.Rota.objects.filter(company_id=company_rodoviaria_id)

    def get_rotas(self, rotas_ids):
        return self.provider.Rota.objects.filter(pk__in=rotas_ids)

    def converter_parsed_data_to_checkpoints(self, rotas_ids):
        rotas = self.get_rotas(rotas_ids)
        rotas_checkpoints = []
        for rota in rotas:
            cksForm = CheckpointsForm.from_itinerario(
                self.provider.company.id,
                rota.parsed_data,
            )
            for idx, ck in enumerate(cksForm):
                c = Checkpoint(
                    rota=rota,
                    idx=idx,
                    local_id=ck.local.rodoviaria_local_id,
                    internal_id=None,
                    arrival=ck.arrival,
                    departure=ck.departure,
                    distancia_km=ck.distancia_km,
                    duracao=timedelta(seconds=ck.duracao),
                    tempo_embarque=timedelta(seconds=ck.tempo_embarque),
                    id_external=ck.local.id_external,
                    uf=ck.local.uf,
                    name=ck.local.name,
                    nickname=ck.local.nickname,
                    updated_at=timezone.now(),
                )
                rotas_checkpoints.append(c)
        return rotas_checkpoints

    @feature_required("itinerario")
    def match_local_de_embarque(
        self,
        local_embarque_internal_id,
        cidade_internal_id,
        timezone,
        nome_cidade,
        codigo_ibge,
        company,
        company_internal_id,
    ):
        return self.provider.match_local_de_embarque(
            local_embarque_internal_id,
            cidade_internal_id,
            timezone,
            nome_cidade,
            codigo_ibge,
            company,
            company_internal_id,
        )

    @circuit_method(reset_timeout=8)
    @traced("orchestrator.find_trecho_vendido")
    def find_trecho_vendido(
        self,
        origem_local_id,
        destino_local_id,
        data_str,
        timezone_origem,
        timezone_destino,
        rota_id,
        expected_datetime_partidas,
        _logger,
    ):
        request_params = {"origem": origem_local_id, "destino": destino_local_id, "data": data_str}
        trechos_params = {
            "timezone_origem": timezone_origem,
            "timezone_destino": timezone_destino,
            "expected_datetime_partidas": expected_datetime_partidas,
        }
        buserlogger.info(
            "find_trecho_vendido",
            extra={
                "rota_id": rota_id,
                "company_id": self.provider.company.id,
                "request_params": request_params,
                "trechos_params": trechos_params,
            },
        )
        corridas = BuscarServicoForm(found=False, servicos=[])
        from_cache = True
        try:
            corridas, from_cache = self.cached_buscar_corridas(request_params)
        except RodoviariaTrechoNotFoundException:
            buserlogger.info(
                "Calling cached_buscar_corridas for rota_id=%s raised RodoviariaTrechoNotFoundException",
                rota_id,
                extra={
                    "rota_id": rota_id,
                },
            )
            return []
        except RodoviariaException as e:
            buserlogger.info(
                """
                Erro em find_trecho_vendido com provider %s: RodoviariaException as %s.
                 Rota Rodoviária {rota_id}. Horários de partida esperados: %s"
                """,
                self.provider.__module__,
                e,
                expected_datetime_partidas,
            )

        all_trechos_vendidos, trechos_vendidos_same_datetime = self.build_trechos_vendidos(
            corridas, origem_local_id, destino_local_id, **trechos_params
        )

        if all_trechos_vendidos and not from_cache:
            self.try_atualiza_trechos_raw(all_trechos_vendidos, self.company_internal_id, **request_params)

        if trechos_vendidos_same_datetime:
            # TODO: esse log é para pegar uma métrica, depois da analise deve ser removido
            buserlogger.info(
                "Encontrou trechos_vendidos: %s",
                trechos_vendidos_same_datetime,
                extra={
                    "rota_id": rota_id,
                    "company_id": self.provider.company.id,
                    "request_params": request_params,
                    "trechos_params": trechos_params,
                },
            )

        return trechos_vendidos_same_datetime

    @circuit_method()
    @feature_required("buscar_servico")
    def buscar_corridas(self, request_params, match_params=None) -> BuscarServicoForm:
        return self.provider.buscar_corridas(request_params, match_params)

    def cached_buscar_corridas(self, request_params):
        cache_key = f"{self}:corridas:{request_params['origem']}:{request_params['destino']}:{request_params['data']}"
        cached_result = get_key(cache_key)
        from_cache = bool(cached_result)

        if not cached_result:
            corridas = self.buscar_corridas(request_params, None)
            set_key(cache_key, corridas, 12 * 60 * 60)
            return corridas, from_cache

        return cached_result, from_cache

    @traced("orchestrator.build_trechos_vendidos")
    def build_trechos_vendidos(
        self,
        corridas,
        origem_id,
        destino_id,
        timezone_origem,
        timezone_destino,
        expected_datetime_partidas,
    ):
        all_trechos_vendidos = []
        trechos_vendidos_same_datetime = []
        for corrida in corridas.servicos:
            saida = to_tz(corrida.external_datetime_ida, timezone_origem)
            chegada = to_tz(corrida.external_datetime_chegada, timezone_destino)
            duracao = self._get_duracao(chegada, saida)
            tv = {
                "external_origem_id": origem_id,
                "external_destino_id": destino_id,
                "classe": corrida.classe,
                "capacidade_classe": corrida.capacidade_classe,
                "duracao": duracao.total_seconds(),
                "preco": corrida.preco,
                "distancia": corrida.distancia,
                "datetime_ida": saida,
                "vagas": corrida.vagas,
            }
            all_trechos_vendidos.append(tv.copy())
            if self._is_same_datetime_ida(corrida, expected_datetime_partidas):
                trechos_vendidos_same_datetime.append(tv)
        return all_trechos_vendidos, trechos_vendidos_same_datetime

    @traced("orchestrator.try_atualiza_trechos_raw")
    def try_atualiza_trechos_raw(self, all_trechos_vendidos, company_internal_id, origem, destino, data):
        origem_local_internal_id, destino_local_internal_id = self._get_origem_destino(origem, destino)
        if company_internal_id == novos_modelos_svc.VIACAO_ADAMANTINA_INTERNAL_ID:
            return

        for trecho_vendido in all_trechos_vendidos:
            trecho_vendido["classe"] = get_buser_class_by_company_internal_id(
                self.company_internal_id, self.modelo_venda, trecho_vendido["classe"]
            )

        if origem_local_internal_id and destino_local_internal_id and all_trechos_vendidos:
            atualiza_trecho_raw.delay(
                origem_id=origem_local_internal_id,
                destino_id=destino_local_internal_id,
                data_str=data,
                trechos_raw=all_trechos_vendidos,
                company_internal_id=company_internal_id,
            )

    def _get_duracao(self, chegada, saida):
        return chegada - saida

    def _is_same_datetime_ida(self, corrida, expected_datetime_partidas):
        return datetime.strftime(corrida.external_datetime_ida, "%Y-%m-%dT%H:%M:%S") in expected_datetime_partidas

    def _get_origem_destino(self, origem_id, destino_id):
        locais = dict(
            LocalEmbarque.objects.filter(
                cidade__company__company_internal_id=self.company_internal_id, id_external__in=[origem_id, destino_id]
            ).values_list("id_external", "local_embarque_internal_id")
        )
        try:
            origem_local_internal_id = locais[origem_id]
            destino_local_internal_id = locais[destino_id]
        except KeyError:
            return None, None

        return origem_local_internal_id, destino_local_internal_id

    @feature_required("motorista")
    def cria_motorista(self, motorista):
        return self.provider.cria_motorista(motorista)

    @feature_required("motorista")
    def edita_dados_motorista(self, id_usuario, motorista):
        return self.provider.edita_dados_motorista(id_usuario, motorista)

    @feature_required("motorista")
    def edita_documentos_motorista(self, id_motorista, id_usuario_empresa, motorista):
        return self.provider.edita_documentos_motorista(id_motorista, id_usuario_empresa, motorista)

    @feature_required("motorista")
    @feature_required("itinerario")
    def escala_motorista(self, id_usuario_empresa, id_itinerario):
        return self.provider.escala_motorista(id_usuario_empresa, id_itinerario)

    def datetime_ida_from_trecho_classe(self, trecho_classe):
        return self.provider.datetime_ida_from_trecho_classe(trecho_classe)

    @feature_required("escalar_veiculos")
    def get_mapas_veiculos_api(self):
        return self.provider.get_mapas_veiculos_api()

    @feature_required("escalar_veiculos")
    def get_lista_veiculos_api(self):
        return self.provider.get_lista_veiculos_api()

    @feature_required("escalar_veiculos")
    def create_veiculos_api(self, veiculos):
        return self.provider.create_veiculos_api(veiculos)

    @feature_required("itinerario")
    def buscar_rotas(self):
        return self.provider.buscar_rotas()

    @feature_required("escalar_veiculos")
    def altera_veiculo_viagem(self, veiculo, andar, trechos_classe):
        return self.provider.altera_veiculo_viagem(veiculo, andar, trechos_classe)

    @feature_required("itinerario")
    def viagens_por_rota(self, rota_external_id):
        return self.provider.viagens_por_rota(rota_external_id)

    @feature_required("itinerario")
    def listar_trechos(self, rota_id):
        return self.provider.listar_trechos(rota_id)

    @feature_required("itinerario")
    def cadastrar_rota(self, origem_id, destino_id, prefixo, rota_internal_id):
        return self.provider.cadastrar_rota(origem_id, destino_id, prefixo, rota_internal_id)

    @feature_required("itinerario")
    def editar_rota(self, rota_external_id, delimitacao, origem_id, destino_id, prefixo):
        return self.provider.editar_rota(rota_external_id, delimitacao, origem_id, destino_id, prefixo)

    @feature_required("itinerario")
    def inativar_rota(self, rota_external_id, origem_id, destino_id, prefixo):
        return self.provider.inativar_rota(rota_external_id, origem_id, destino_id, prefixo)

    @feature_required("itinerario")
    def criar_checkpoint(self, params):
        return self.provider.cadastrar_checkpoint(params)

    @feature_required("itinerario")
    def mover_posicao_checkpoint_para_baixo(self, trecho_id):
        return self.provider.mover_posicao_checkpoint_para_baixo(trecho_id)

    @feature_required("itinerario")
    def atualizar_checkpoint(self, payload, nova_descricao=None, nova_duracao=None, nova_quilometragem=None):
        return self.provider.atualizar_checkpoint(payload, nova_descricao, nova_duracao, nova_quilometragem)

    @feature_required("escalar_veiculos")
    def lista_reservas_viagem(self, grupo_classe_external_id):
        return self.provider.lista_reservas_viagem(grupo_classe_external_id)

    @feature_required("escalar_veiculos")
    def cancelar_reserva_por_localizador(self, localizador):
        return self.provider.cancelar_reserva_por_localizador(localizador)

    @feature_required("itinerario")
    def descobrir_rotas_async(self, next_days, shift_days, queue_name, return_task_object):
        return self.provider.descobrir_rotas_async(
            next_days, shift_days, queue_name, return_task_object, self.modelo_venda
        )

    @feature_required("itinerario")
    def descobrir_operacao_async(self, next_days, shift_days, queue_name, return_task_object):
        return self.provider.descobrir_operacao_async(next_days, shift_days, queue_name, return_task_object)

    @feature_required("itinerario")
    def fetch_rotina(self, rota, next_days, first_day):
        return self.provider.fetch_rotina(rota, next_days, first_day)

    @feature_required("itinerario")
    def fetch_rotina_async(self, rota, next_days, first_day, queue_name):
        return self.provider.fetch_rotina_async(rota, next_days, first_day, queue_name)

    @feature_required("itinerario")
    def fetch_rotinas_empresa(self, next_days, first_day, queue_name):
        return self.provider.fetch_rotinas_empresa(next_days, first_day, queue_name)

    def listar_empresas(self):
        return self.provider.listar_empresas()

    @feature_required("itinerario")
    def inativar_grupo_classe(self, grupo_classe_external_id):
        return self.provider.inativar_grupo_classe(grupo_classe_external_id)

    def update_bpe_passagem(self, passagem):
        return self.provider.update_bpe_passagem(passagem)

    def cancelar_reservas_por_pedido_id(self, pedido_id):
        return self.provider.cancelar_reservas_por_pedido_id(pedido_id)

    def get_atualizacao_passagem_api_parceiro(self, passagem_list):
        bilhete_formatado_list = []
        for passagem in passagem_list:
            bilhete_formatado = self.provider.get_atualizacao_passagem_api_parceiro(passagem)
            bilhete_formatado_list.append(bilhete_formatado.dict())
        return bilhete_formatado_list

    def buscar_viagens_por_periodo(self, datetime_inicial, datetime_final, company_external_id):
        return self.provider.buscar_viagens_por_periodo(datetime_inicial, datetime_final, company_external_id)

    # @feature_required("selecao_assento")
    def get_desenho_mapa_poltronas(self, trecho_classe_id):
        with token_bucket_middleware.suppress_not_enough_tokens_error():
            mapa_poltronas: Onibus = self.provider.get_desenho_mapa_poltronas(trecho_classe_id)
        return mapa_poltronas.dict()

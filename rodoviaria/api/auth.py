from typing import Callable

import requests


class BearerCallableAuth(requests.auth.AuthBase):
    def __init__(self, api_key: str | Callable[[], str], header_name="X-Api-Key") -> str:
        self.header_name = header_name
        if callable(api_key):
            api_key = api_key()
        self.api_key = api_key

    def __call__(self, request):
        """
        Adiciona credenciais de autenticação aos cabeçalhos do 'request'.

        Args:
            request (requests.PreparedRequest): O objeto de solicitação HTTP.

        Returns:
            requests.PreparedRequest: O objeto de solicitação modificado com as credenciais de autenticação
        nos cabeçalhos.
        """
        request.headers[self.header_name] = f"Bearer {self.api_key}"
        return request

import logging
from datetime import datetime

from django.db.models import Count, F

from commons.dateutils import to_default_tz
from core.models_grupo import Grupo as CoreGrupo
from rodoviaria.models.core import Company, Grupo, Rota

buserlogger = logging.getLogger("rodoviaria")

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE


def get_total_rotas_integradas(company_ids):
    qs_rota = Rota.objects.select_related("company").filter(
        company__company_internal_id__in=company_ids,
        company__modelo_venda=DEFAULT_MODELO_VENDA,
    )
    qs_rota = (
        qs_rota.annotate(company_internal_id=F("company__company_internal_id"))
        .values("company_internal_id")
        .annotate(integrado=Count("id_internal"), total=Count("id"))
    )
    return list(qs_rota)


def atualizar_id_internal_rota_por_rodoviaria_id(rota_id: int, rodoviaria_rota_id: int):
    Rota.objects.filter(pk=rodoviaria_rota_id).update(id_internal=rota_id)


def atualizar_id_internal_rota_por_internal_id_antigo(rota_internal_id_antigo: int, rota_internal_id_novo: int):
    if rota_internal_id_antigo is not None:
        rotas = Rota.objects.filter(id_internal=rota_internal_id_antigo)
        updated_len = rotas.update(id_internal=rota_internal_id_novo)
        rotas_id = list(rotas.values_list("id", flat=True))
        buserlogger.info(
            "%s rota(s) tiveram id_internal atualizado. rotas_id=%s, rota_internal_id_antigo=%s e",
            updated_len,
            rotas_id,
            rota_internal_id_antigo,
            " rota_internal_id_novo=%s",
            rota_internal_id_novo,
        )
        return updated_len


def atualizar_id_internal_rotas_empresa(company_id):
    rodo_grupos = _get_grupos_rotas_rodoviaria(company_id)
    core_grupos_rotas = _get_grupos_rotas_buser_django(rodo_grupos)

    rodo_rotas = _get_lista_rotas_com_id_internal(rodo_grupos, core_grupos_rotas)

    if rodo_rotas:
        Rota.objects.bulk_update(rodo_rotas, fields=["id_internal", "updated_at"])
    return len(rodo_rotas)


def _get_grupos_rotas_rodoviaria(company_id):
    grupos = (
        Grupo.objects.select_related("company_integracao")
        .filter(
            company_integracao__company_internal_id=company_id,
            company_integracao__modelo_venda=DEFAULT_MODELO_VENDA,
            rota__isnull=False,
        )
        .values("rota", "grupo_internal_id", "updated_at")
    )
    max_rota = {}
    for g in grupos:
        if g["rota"] not in max_rota.keys():
            max_rota[g["rota"]] = {"max_updated_at": to_default_tz(datetime(2, 2, 2))}
        if g["updated_at"] > max_rota[g["rota"]].get("max_updated_at"):
            max_rota[g["rota"]]["rota"] = g["rota"]
            max_rota[g["rota"]]["max_updated_at"] = g["updated_at"]
            max_rota[g["rota"]]["grupo_internal_id"] = g["grupo_internal_id"]
    return list(max_rota.values())


def _get_grupos_rotas_buser_django(rodo_grupos):
    grupos_internal_ids = [x["grupo_internal_id"] for x in rodo_grupos]

    core_grupos_rotas = CoreGrupo.objects.filter(pk__in=grupos_internal_ids)

    core_grupos_rotas = {x[0]: x[1] for x in core_grupos_rotas.values_list("id", "rota_id")}
    return core_grupos_rotas


def _get_lista_rotas_com_id_internal(rodo_grupos, core_grupos_rotas):
    rotas_ids = [x["rota"] for x in rodo_grupos]
    rodo_rotas = {rota.id: rota for rota in Rota.objects.filter(pk__in=rotas_ids)}

    for grupo_rota in rodo_grupos:
        grupo_id = grupo_rota["grupo_internal_id"]
        if grupo_id in core_grupos_rotas:
            rodo_rotas[grupo_rota["rota"]].id_internal = core_grupos_rotas[grupo_id]
            rodo_rotas[grupo_rota["rota"]].updated_at = to_default_tz(datetime.now())

    return list(rodo_rotas.values())

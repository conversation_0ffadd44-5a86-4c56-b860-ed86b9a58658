import logging
from datetime import timed<PERSON>ta

from commons.dateutils import to_tz, today_midnight
from rodoviaria.models.core import Rotina
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaItinerarioNotFoundException,
)

logger = logging.getLogger("rodoviaria")


class RotinaTotalbus:
    def __init__(self, provider):
        self.provider = provider

    def _fetch_parsed_itinerario(self, servico, data_viagem):
        itinerario = self.provider.itinerario(servico, data_viagem)
        return itinerario.parsed

    def _find_ocurrence(self, servico, id_hash, data_viagem):
        try:
            itinerario = self._fetch_parsed_itinerario(servico, data_viagem)
        except (RodoviariaException, RodoviariaConnectionError, RodoviariaItinerarioNotFoundException):
            return None
        datetime_ida = itinerario[0].datetime_ida
        rota_hash = itinerario.hash
        if id_hash == rota_hash:
            return datetime_ida
        return None

    def _filter_servicos_condition(self, s, origem_external_id, destino_external_id):
        return s["origem"]["id"] == int(origem_external_id) and s["destino"]["id"] == int(destino_external_id)

    def _fetch_possiveis_servicos_rota(self, rota):
        servicos = self.provider.buscar_todos_servicos()
        origem_external_id = rota.first_checkpoint().id_external
        destino_external_id = rota.last_checkpoint().id_external
        servicos_filtered = [
            s["numservico"]
            for s in servicos
            if self._filter_servicos_condition(s, origem_external_id, destino_external_id)
        ]
        logger.info("Possíveis servicos para rodoviaria_rota_id %s foram %s", rota.id, servicos_filtered)
        return servicos_filtered

    def fetch_rotina(self, rota, next_days, first_day=None):
        first_checkpoint_timezone = rota.first_checkpoint_timezone_or_default()
        servicos = self._fetch_possiveis_servicos_rota(rota)
        id_hash = rota.id_hash

        if not first_day or first_day < today_midnight():
            first_day = today_midnight()

        rotina = set()
        new_utc_dts = []
        for servico in servicos:
            logger.info("Procurando rotina para rodoviaria_rota_id %s com servico %s", rota.id, servico)
            for days_to_add in range(0, next_days + 1):
                current_datetime_viagem = first_day + timedelta(days=days_to_add)
                data_viagem = current_datetime_viagem.strftime("%Y-%m-%d")
                datetime_ida = self._find_ocurrence(servico, id_hash, data_viagem)
                if datetime_ida:
                    datetime_ida_tz = to_tz(datetime_ida, first_checkpoint_timezone)
                    new_utc_dts.append(to_tz(datetime_ida_tz, "UTC"))
                    logger.info("Rotina para rodoviaria_rota_id %s encontrada em %s", rota.id, datetime_ida_tz)
                    Rotina.objects.update_or_create(rota=rota, datetime_ida=datetime_ida_tz, defaults={"ativo": True})
                    rotina.add(datetime_ida_tz)

        rota.atualiza_rotinas_com_base_nas_novas(new_utc_dts, next_days)
        return rotina

from collections import defaultdict

from celery import shared_task
from celery.utils.log import get_task_logger
from django.db.models import F
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from commons.dateutils import to_default_tz
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Grupo, GrupoClasse
from rodoviaria.models.vexado import Veiculo, VexadoGrupoClasse

task_logger = get_task_logger(__name__)

DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO


def link_vexado_grupo_classe(
    grupo_classe,
    company,
    veiculo_id,
    veiculo_andar,
    grupo_classe_external_id,
    rota_external_id,
):
    grupo_classe_vexado = (
        VexadoGrupoClasse.objects.select_related("grupo_classe")
        .select_related("grupo_classe__grupo")
        .filter(
            grupo_classe_external_id=grupo_classe_external_id,
            grupo_classe__grupo__company_integracao=company,
        )
        .first()
    )
    if grupo_classe_vexado:
        grupo_classe_vexado.grupo_classe = grupo_classe
    else:
        grupo_classe_vexado = grupo_classe.vexadogrupoclasse_set.first()
    veiculo = Veiculo.objects.filter(id_external=veiculo_id, company=company).first()
    if grupo_classe_vexado:
        grupo_classe_vexado.grupo_classe_external_id = grupo_classe_external_id
        grupo_classe_vexado.veiculo = veiculo
        grupo_classe_vexado.andar = veiculo_andar
        grupo_classe_vexado.rota_external_id = rota_external_id
        grupo_classe_vexado.status = VexadoGrupoClasse.Status.CRIADO
        grupo_classe_vexado.save()
        # TODO: apagar os grupos antigos criados so pra poder salvar o VexadoGrupoClasse
        return grupo_classe_vexado
    grupo_classe_vexado = VexadoGrupoClasse.objects.create(
        grupo_classe=grupo_classe,
        veiculo=veiculo,
        andar=veiculo_andar,
        grupo_classe_external_id=grupo_classe_external_id,
        rota_external_id=rota_external_id,
        status=VexadoGrupoClasse.Status.CRIADO,
    )
    return grupo_classe_vexado


def update_grupos_hibridos_criados(company_id):
    vexado_grupos_classe_pendentes = (
        VexadoGrupoClasse.objects.filter(
            grupo_classe__grupo__company_integracao__company_internal_id=company_id,
            grupo_classe__grupo__datetime_ida__gt=timezone.now(),
            status=VexadoGrupoClasse.Status.PENDENTE,
        )
        .annotate(datetime_ida=F("grupo_classe__grupo__datetime_ida"))
        .annotate(tipo_assento=F("grupo_classe__tipo_assento_internal"))
    )
    vexado_grupos_classe_pendentes_por_rota_id = defaultdict(list)
    for vexado_grupo_classe in vexado_grupos_classe_pendentes:
        vexado_grupos_classe_pendentes_por_rota_id[vexado_grupo_classe.rota_external_id].append(vexado_grupo_classe)
    return _update_grupos_pendentes(company_id, vexado_grupos_classe_pendentes_por_rota_id)


def _update_grupos_pendentes(company_id, vexado_grupos_classe_pendentes_por_rota_id):
    orchestrator = OrchestrateRodoviaria(company_id, DEFAULT_MODELO_VENDA)
    vexado_grupos_classe_to_update = []
    response = {}
    for (
        rota_external_id,
        vexado_grupos_classe,
    ) in vexado_grupos_classe_pendentes_por_rota_id.items():
        viagens_api = orchestrator.viagens_por_rota(rota_external_id)
        viagens_map = {}
        for vapi in viagens_api:
            viagens_map[
                (
                    vapi.datetime_ida.strftime("%Y-%m-%d %H:%M"),
                    vapi.rota_external_id,
                    vapi.tipo_assento,
                )
            ] = vapi.id_external

        for vgc in vexado_grupos_classe:
            tuple_key = (
                to_default_tz(vgc.datetime_ida).strftime("%Y-%m-%d %H:%M"),
                vgc.rota_external_id,
                vgc.tipo_assento,
            )
            grupo_classe_external_id = viagens_map.get(tuple_key)
            if grupo_classe_external_id:
                vgc.grupo_classe_external_id = grupo_classe_external_id
                vgc.status = VexadoGrupoClasse.Status.CRIADO
                vexado_grupos_classe_to_update.append(vgc)
                response[str(tuple_key)] = "ok"
            else:
                response[str(tuple_key)] = "nao encontrado"
    VexadoGrupoClasse.objects.bulk_update(vexado_grupos_classe_to_update, ["grupo_classe_external_id", "status"])
    return response


def fetch_grupos_criados_anteriormente(company_id):
    company = Company.objects.get(company_internal_id=company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    orchestrator = OrchestrateRodoviaria(company_id, DEFAULT_MODELO_VENDA)
    grupos_classe_to_create = []
    grupos_to_create_map = {}
    vexado_grupos_classe_to_create = []
    vexado_grupos_classe_ja_criados = VexadoGrupoClasse.objects.filter(
        grupo_classe__grupo__company_integracao__company_internal_id=company_id,
        grupo_classe__grupo__datetime_ida__gt=timezone.now(),
        grupo_classe_external_id__isnull=False,
    )
    vexado_grupos_classe_ja_criados = {vgc.grupo_classe_external_id: vgc for vgc in vexado_grupos_classe_ja_criados}
    veiculos_map = {veiculo.id_external: veiculo for veiculo in Veiculo.objects.filter(company=company)}
    rotas_ids = [r["id"] for r in orchestrator.buscar_rotas()]
    for rota_external_id in rotas_ids:
        viagens_api = orchestrator.viagens_por_rota(rota_external_id)
        for v in viagens_api:
            if vexado_grupos_classe_ja_criados.get(v.id_external) or v.datetime_ida < timezone.now():
                continue
            grupo = grupos_to_create_map.get((company, v.datetime_ida))
            if not grupo:
                grupo = Grupo(company_integracao=company, datetime_ida=v.datetime_ida)
                grupos_to_create_map[(company, v.datetime_ida)] = grupo
            grupo_classe = GrupoClasse(grupo=grupo, tipo_assento_internal=v.tipo_assento)
            grupos_classe_to_create.append(grupo_classe)
            vexado_gc_to_create = VexadoGrupoClasse(
                grupo_classe=grupo_classe,
                veiculo=veiculos_map.get(v.veiculo_id),
                andar=v.veiculo_andar,
                rota_external_id=rota_external_id,
                grupo_classe_external_id=v.id_external,
            )
            vexado_grupos_classe_to_create.append(vexado_gc_to_create)
    Grupo.objects.bulk_create(grupos_to_create_map.values())
    GrupoClasse.objects.bulk_create(grupos_classe_to_create)
    VexadoGrupoClasse.objects.bulk_create(vexado_grupos_classe_to_create)
    return {"grupos_classes_criados": len(vexado_grupos_classe_to_create)}


def fill_grupo_classe_external_id():
    vexado_grupos_classe_sem_id_external = (
        VexadoGrupoClasse.objects.select_related("grupo_classe")
        .prefetch_related("grupo_classe__trechoclasse_set")
        .filter(grupo_classe_external_id__isnull=True)
    )
    count = 0
    for vgc in vexado_grupos_classe_sem_id_external:
        trecho_classe = vgc.grupo_classe.trechoclasse_set.first()
        if trecho_classe:
            vgc.grupo_classe_external_id = trecho_classe.external_id
            vgc.status = VexadoGrupoClasse.Status.CRIADO
            count += 1
    VexadoGrupoClasse.objects.bulk_update(vexado_grupos_classe_sem_id_external, ["grupo_classe_external_id", "status"])
    return {"grupos_classes_atualizados": count}


@shared_task(
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.CADASTROS_VEXADO,
)
def update_grupos_hibridos_criados_task(company_id):
    task_logger.info("atualizando hibridos criados da empresa %s", company_id)
    update_grupos_hibridos_criados(company_id)


def update_grupos_hibridos_criados_todas_empresas():
    companies_ids = (
        VexadoGrupoClasse.objects.filter(status=VexadoGrupoClasse.Status.PENDENTE)
        .values_list("grupo_classe__grupo__company_integracao__company_internal_id", flat=True)
        .distinct()
    )
    for company_id in companies_ids:
        update_grupos_hibridos_criados_task.delay(company_id)

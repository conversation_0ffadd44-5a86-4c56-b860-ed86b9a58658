from collections import defaultdict

from django.db.models import Q

from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Passagem
from rodoviaria.serializers import serializer_bpe


def travel_has_bpe(travel_id):
    tem_embarque_rapido = Q(bpe_qrcode__isnull=False) | Q(embarque_eletronico__isnull=False)
    return (
        Passagem.objects.filter(
            travel_internal_id=travel_id,
            trechoclasse_integracao__grupo__company_integracao__features__contains=[
                Company.Feature.ACTIVE,
                Company.Feature.BPE,
            ],
            status=Passagem.Status.CONFIRMADA,
        )
        .filter(tem_embarque_rapido)
        .exists()
    )


def dados_bpe_passagem(travel_id):
    return serializer_bpe.DadosBpeSerializer().serialize_object(travel_id)


def dados_bpe_passagem_batch(travel_ids):
    passagens = (
        Passagem.objects.filter(travel_internal_id__in=travel_ids, status=Passagem.Status.CONFIRMADA)
    ).select_related(
        "company_integracao",
        "trechoclasse_integracao__grupo__company_integracao__integracao",
    )

    passagens_travels_map = defaultdict(list)
    for passagem in passagens:
        passagens_travels_map[passagem.travel_internal_id].append(passagem)

    result = []
    serializer = serializer_bpe.DadosBpeSerializer()
    for travel_id, passagens in passagens_travels_map.items():
        bpe = serializer.serialize_object(travel_id, passagens)
        bpe["travel_id"] = travel_id
        result.append(bpe)

    return result


def get_status_bpe(passagem_id):
    passagem = Passagem.objects.select_related(
        "company_integracao", "trechoclasse_integracao__grupo__company_integracao"
    ).get(id=passagem_id)
    company = passagem.company_integracao
    if not company:
        company = passagem.trechoclasse_integracao.grupo.company_integracao
    orchestrator = OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda)
    if passagem.bpe_em_contingencia:
        try:
            passagem = orchestrator.update_bpe_passagem(passagem)
        except NotImplementedError:
            pass
    return {
        "em_contingencia": passagem.bpe_em_contingencia,
        "url": passagem.bpe_qrcode,
        "chave": passagem.chave_bpe,
        "numero": passagem.numero_bpe,
        "serie": passagem.serie_bpe,
        "tributos": passagem.outros_tributos,
    }

import json
import logging

from celery import shared_task
from constance import config as constance_config
from django.http.response import JsonResponse

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import (
    DefaultForm,
)
from rodoviaria.forms.staff_forms import CheckPaxForm, CheckPaxMultipleForm
from rodoviaria.models.core import Passagem
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import (
    PoltronaTrocadaException,
    RodoviariaCompanyNotIntegratedError,
    RodoviariaException,
)

buserlogger = logging.getLogger("rodoviaria")


def efetua_cancelamento(travel_id, buseiro_id=None, pax_valido=None):
    passagem = (
        Passagem.objects.select_related("trechoclasse_integracao__grupo__company_integracao", "company_integracao")
        .filter(travel_internal_id=travel_id)
        .first()
    )
    if not passagem:
        msg = f"Travel {travel_id} não encontrada no banco do rodoviaria"
        resp = {"message": f"Travel {travel_id} não encontrada no banco do rodoviaria"}
        buserlogger.info(msg)
        return JsonResponse(resp, status=200)

    if passagem.status == Passagem.Status.DEVOLVIDA:
        raise PoltronaTrocadaException

    trecho_classe_id = passagem.trechoclasse_integracao.trechoclasse_internal_id
    if passagem.company_integracao:
        company_internal_id = passagem.company_integracao.company_internal_id
        modelo_venda = passagem.company_integracao.modelo_venda

        if "active" not in passagem.company_integracao.features:
            raise RodoviariaCompanyNotIntegratedError(passagem.company_integracao.name)
    else:
        company_internal_id = passagem.trechoclasse_integracao.grupo.company_integracao.company_internal_id
        modelo_venda = passagem.trechoclasse_integracao.grupo.company_integracao.modelo_venda

    orchestrator = OrchestrateRodoviaria(company_internal_id, modelo_venda)
    params = CancelaVendaForm(
        trechoclasse_id=trecho_classe_id,
        travel_id=travel_id,
        buseiro_id=buseiro_id,
        pax_valido=pax_valido,
    )
    return orchestrator.cancela_venda(params)


def add_pax_na_lista(form: CheckPaxForm):
    form.force_renew_link = constance_config.FORCE_RENEW_LINK_ON_ADD_PAX
    return CompraRodoviariaSVC(form).add_pax_na_lista_passageiros_viagem(form)


def add_pax_na_lista_batch(passageiros):
    passageiros_grupos_map = {}
    for passageiro in passageiros:
        if passageiro.grupo_id not in passageiros_grupos_map:
            passageiros_grupos_map[passageiro.grupo_id] = [passageiro]
        else:
            passageiros_grupos_map[passageiro.grupo_id].append(passageiro)

    for passageiros_grupo in passageiros_grupos_map.values():
        add_pax_na_lista_grupo_task.delay([passageiro.json() for passageiro in passageiros_grupo])


@shared_task(
    bind=True,
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.CADASTROS_VEXADO,
)
def add_pax_na_lista_grupo_task(self, passageiros):
    for passageiro in passageiros:
        passageiro = CheckPaxForm.parse_raw(passageiro)
        add_pax_na_lista(passageiro)


@shared_task(
    bind=True,
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.CADASTROS_VEXADO,
)
@retry(
    exceptions_type=(RodoviariaException,),
    min_delay=10,
    max_retries=5,
)
def add_pax_na_lista_task(self, passageiro):
    add_pax_na_lista(passageiro)


@shared_task(
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.ADD_MULTIPLE_PAX_HIBRIDO,
)
def add_multiple_pax_na_lista_task(params):
    params_parsed = CheckPaxMultipleForm.parse_obj(json.loads(params))
    form = DefaultForm(trechoclasse_id=params_parsed.trechoclasse_id, force_renew_link=True)
    CompraRodoviariaSVC(form).add_multiple_pax_na_lista_passageiros_viagem(form)

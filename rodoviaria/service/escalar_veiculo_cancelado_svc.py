from celery import group

from rodoviaria.models.vexado import <PERSON><PERSON><PERSON><PERSON>
from rodoviaria.service import veiculos_svc


def escalar_veiculo_cancelado_tasks(company_id, vexado_grupos_classe):
    veiculo_cancelado = Veiculo.objects.filter(
        id_internal=999999, company__company_internal_id=company_id
    ).first()  # veiculo que indica que a viagem foi cancelada
    trocar_onibus_tasks = []
    if veiculo_cancelado:
        for vgc in vexado_grupos_classe:
            trocar_onibus_tasks.append(
                veiculos_svc.trocar_onibus_viagem_task.si(
                    vgc.grupo_classe_external_id,
                    veiculo_cancelado.id_external,
                    veiculo_cancelado.id,
                    1,
                    company_id,
                    vgc.grupo_classe_id,
                )
            )
    return group(trocar_onibus_tasks)

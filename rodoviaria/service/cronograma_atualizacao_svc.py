from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Q

from rodoviaria.models.core import Company, CronogramaAtualizacaoOperacao
from rodoviaria.service.atualiza_operacao_empresa_svc import integracao_descobre_operacao_de_uma_vez
from rodoviaria.views_schemas import (
    CronogramaAtualizacaoPaginatorParams,
    HorariosAtualizacaoParams,
    UpdateCronogramaAtualizacaoParams,
)

RODEROTAS_INTERNAL_ID = 313

# o crontab não usa o dia da semana no formato iso, por isso precisa remapear os dias da semana
MAP_DIA_SEMANA_CRON = {1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 7: "0"}


def get_cronograma_atualizacao(params: CronogramaAtualizacaoPaginatorParams):
    companies = get_companies_with_atualizacao_operacao(params.integracao_id_filter, params.search)
    paginator = Paginator(companies, per_page=params.rows_per_page)
    page = paginator.get_page(params.page)
    companies_page = page.object_list
    map_horarios = _get_cronograma_from_companies(companies_page)
    return {
        "horarios": list(map_horarios.values()),
        "count": paginator.count,
        "num_pages": paginator.num_pages,
    }


def _get_cronograma_from_companies(companies_page):
    map_horarios = init_map_horarios(companies_page)
    horarios = CronogramaAtualizacaoOperacao.objects.select_related("company__integracao").filter(
        company__in=companies_page
    )

    def add_horario_to_map(horario):
        if not map_horarios[horario.company_id][horario.tipo_atualizacao]["horario"]:
            map_horarios[horario.company_id][horario.tipo_atualizacao]["horario"] = horario.horario
        map_horarios[horario.company_id][horario.tipo_atualizacao]["dias_semana"].append(horario.dia_semana)

    for horario in horarios:
        has_descobrir_operacao = map_horarios[horario.company_id]["integracao_has_descobrir_operacao"]
        if horario.tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA:
            add_horario_to_map(horario)
        elif (
            has_descobrir_operacao
            and horario.tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO
        ):
            add_horario_to_map(horario)
        elif (
            not has_descobrir_operacao
            and horario.tipo_atualizacao != CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO
        ):
            add_horario_to_map(horario)
    return map_horarios


def get_companies_with_atualizacao_operacao(integracao_id, search):
    needs_atualizacao_operacao = Q(modelo_venda=Company.ModeloVenda.MARKETPLACE) | (
        Q(modelo_venda=Company.ModeloVenda.HIBRIDO) & Q(company_internal_id=RODEROTAS_INTERNAL_ID)
    )
    companies = Company.objects.filter(
        needs_atualizacao_operacao, features__contains=[Company.Feature.ITINERARIO, Company.Feature.BUSCAR_SERVICO]
    )
    if integracao_id:
        companies = companies.filter(integracao_id=integracao_id)
    if search:
        companies = companies.filter(name__icontains=search)
    return list(companies.order_by("name"))


def init_map_horarios(companies):
    map_horarios = {}
    for company in companies:
        map_horarios[company.id] = {
            "empresa": {
                "id": company.id,
                "company_internal_id": company.company_internal_id,
                "modelo_venda": company.modelo_venda,
                "integracao_id": company.integracao_id,
                "name": company.name,
            },
            "integracao_has_descobrir_operacao": integracao_descobre_operacao_de_uma_vez(company.integracao.name),
            CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO: {
                "dias_semana": [],
                "horario": None,
            },
            CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS: {
                "dias_semana": [],
                "horario": None,
            },
            CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS: {
                "dias_semana": [],
                "horario": None,
            },
            CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE: {
                "dias_semana": [],
                "horario": None,
            },
            CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA: {
                "dias_semana": [],
                "horario": None,
            },
        }
    return map_horarios


def update_cronograma_atualizacao(params: UpdateCronogramaAtualizacaoParams):
    horarios_programados = []
    companies_ids = []
    for horario in params.horarios:
        company_id = horario.empresa.id
        companies_ids.append(company_id)
        horarios_programados += parse_horarios(
            horario.fetch_trechos_vendidos, company_id, CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS
        )
        horarios_programados += parse_horarios(
            horario.descobrir_rotas, company_id, CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS
        )
        horarios_programados += parse_horarios(
            horario.fetch_data_limite, company_id, CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE
        )
        horarios_programados += parse_horarios(
            horario.descobrir_operacao, company_id, CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO
        )
        horarios_programados += parse_horarios(
            horario.integracao_automatica, company_id, CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA
        )
    _update_database(companies_ids, horarios_programados)


def parse_horarios(horario_atualizacao: HorariosAtualizacaoParams, company_id, tipo_atualizacao):
    if not horario_atualizacao:
        return []
    horarios = []
    for dia in horario_atualizacao.dias_semana:
        horarios.append(
            CronogramaAtualizacaoOperacao(
                company_id=company_id,
                tipo_atualizacao=tipo_atualizacao,
                horario=horario_atualizacao.horario,
                dia_semana=dia,
            )
        )
    return horarios


@transaction.atomic(using="rodoviaria")
def _update_database(companies_ids, horarios_programados: list[CronogramaAtualizacaoOperacao]):
    horarios_existentes = CronogramaAtualizacaoOperacao.objects.filter(company_id__in=companies_ids)
    map_horarios_existentes = {(h.company_id, h.tipo_atualizacao, h.dia_semana): h for h in horarios_existentes}
    to_create = []
    to_update = []
    for horario_novo in horarios_programados:
        horario_existente = map_horarios_existentes.get(
            (horario_novo.company_id, horario_novo.tipo_atualizacao, horario_novo.dia_semana)
        )
        if horario_existente:
            horario_novo.id = horario_existente.id
            to_update.append(horario_novo)
            map_horarios_existentes.pop(
                (horario_novo.company_id, horario_novo.tipo_atualizacao, horario_novo.dia_semana)
            )
        else:
            to_create.append(horario_novo)
    to_delete_ids = [h.id for h in map_horarios_existentes.values()]
    CronogramaAtualizacaoOperacao.objects.bulk_create(to_create)
    CronogramaAtualizacaoOperacao.objects.bulk_update(to_update, ["horario"])  # não possui updated_at
    CronogramaAtualizacaoOperacao.objects.filter(id__in=to_delete_ids).delete()


def get_lista_crons():
    """
    retorna lista com os dados dos crons para atualização da operação no formato
    [
        (company_internal_id, modelo_venda, tipo_atualizacao, dias_semana, horas, minutos)
    ]
    """
    needs_atualizacao_operacao = Q(modelo_venda=Company.ModeloVenda.MARKETPLACE) | (
        Q(modelo_venda=Company.ModeloVenda.HIBRIDO) & Q(company_internal_id=RODEROTAS_INTERNAL_ID)
    )
    companies = Company.objects.filter(
        needs_atualizacao_operacao, features__contains=[Company.Feature.ITINERARIO, Company.Feature.BUSCAR_SERVICO]
    )
    horarios = _get_cronograma_from_companies(companies)
    cronograma = []

    def adicionar_ao_cronograma(horario, tipo_operacao):
        if horario[tipo_operacao]["dias_semana"]:
            splited_horario = str(horario[tipo_operacao]["horario"]).split(":")
            hora = splited_horario[0]
            minuto = splited_horario[1]
            cronograma.append(
                (
                    horario["empresa"]["company_internal_id"],
                    horario["empresa"]["modelo_venda"],
                    tipo_operacao,
                    ",".join([MAP_DIA_SEMANA_CRON[d] for d in horario[tipo_operacao]["dias_semana"]]),
                    hora,
                    minuto,
                )
            )

    for horario in horarios.values():
        adicionar_ao_cronograma(horario, CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO)
        adicionar_ao_cronograma(horario, CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS)
        adicionar_ao_cronograma(horario, CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS)
        adicionar_ao_cronograma(horario, CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE)
        adicionar_ao_cronograma(horario, CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA)

    return cronograma

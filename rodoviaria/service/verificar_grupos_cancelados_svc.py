from django.db.models import F
from sentry_sdk import capture_exception

from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Passagem
from rodoviaria.models.vexado import VexadoGrupoClasse

DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO


def check_passagens_canceladas_hibrido(limit=50):
    vexado_grupos_classe_cancelados = VexadoGrupoClasse.objects.annotate(
        company_internal_id=F("grupo_classe__grupo__company_integracao__company_internal_id")
    ).filter(
        status=VexadoGrupoClasse.Status.CANCELADO,
        grupo_classe_external_id__isnull=False,
    )[:limit]

    viagens_external_ids = []
    orchestrators_map = {}
    for vgc in vexado_grupos_classe_cancelados:
        viagens_external_ids.append(vgc.grupo_classe_external_id)
        if not orchestrators_map.get(vgc.company_internal_id):
            orchestrators_map[vgc.company_internal_id] = OrchestrateRodoviaria(
                vgc.company_internal_id, DEFAULT_MODELO_VENDA
            )

    passagens_nao_canceladas = (
        Passagem.objects.annotate(
            company_internal_id=F("trechoclasse_integracao__grupo__company_integracao__company_internal_id")
        )
        .filter(
            trechoclasse_integracao__external_id__in=viagens_external_ids,
        )
        .exclude(status__in={Passagem.Status.INCOMPLETA, Passagem.Status.ERRO})
    )
    passagens_nao_canceladas_map = {
        (int(p.localizador), int(p.company_internal_id)): p for p in passagens_nao_canceladas
    }

    grupos_com_passagens_pendentes = []
    for vgc in vexado_grupos_classe_cancelados:
        reservas_nao_canceladas = orchestrators_map[vgc.company_internal_id].lista_reservas_viagem(
            vgc.grupo_classe_external_id
        )
        if not reservas_nao_canceladas:
            vgc.status = VexadoGrupoClasse.Status.CANCELADO_DUPLO_CHECK
            continue
        cancelamento_errors = False
        grupos_com_passagens_pendentes.append(vgc.grupo_classe_external_id)
        for p in reservas_nao_canceladas:
            try:
                orchestrators_map[vgc.company_internal_id].cancelar_reserva_por_localizador(p)
                passagem_nao_cancelada = passagens_nao_canceladas_map.get((int(p), int(vgc.company_internal_id)))
                if passagem_nao_cancelada:
                    passagem_nao_cancelada.save_canceled()
                    passagem_nao_cancelada.tags.add("cancelada_pelo_check_grupos_cancelados")
            except Exception as ex:
                cancelamento_errors = True
                capture_exception(ex)

        if cancelamento_errors:
            vgc.status = VexadoGrupoClasse.Status.CANCELADO_CHECK_ERRO
            continue
        vgc.status = VexadoGrupoClasse.Status.CANCELADO_DUPLO_CHECK
    VexadoGrupoClasse.objects.bulk_update(list(vexado_grupos_classe_cancelados), ["status"])  # não possui updated_at
    return {"grupos_com_passagens_pendentes": grupos_com_passagens_pendentes}

import logging
from datetime import datetime, timedelta

from celery import chain, chord, group
from celery.app import shared_task
from celery.utils.log import get_task_logger

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import to_tz, today_midnight
from commons.token_bucket import NotEnoughTokens
from rodoviaria.models.core import Company, Rota, Rotina
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaItinerarioNotFoundException,
)

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")

DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTINAS
DEFAULT_RATE_LIMIT = DefaultRateLimits.Totalbus.ROTINAS


# TODO: duplicado com _buscar_rota_do_servico do descobrir_rotas_totalbus_async_svc
# TODO: esses imports circulares indicam que a definição das tasks deveria tá num módulo separado
def _fetch_parsed_itinerario(company_internal_id, modelo_venda, servico, data_viagem):
    from rodoviaria.api.orchestrator import OrchestrateRodoviaria

    orchestrator = OrchestrateRodoviaria(company_internal_id, modelo_venda)
    params = {"servico": servico, "data": data_viagem}
    itinerario = orchestrator.buscar_itinerario(params)
    return itinerario.parsed


def _fetch_todos_servicos(company_internal_id, modelo_venda):
    from rodoviaria.api.orchestrator import OrchestrateRodoviaria

    orchestrator = OrchestrateRodoviaria(company_internal_id, modelo_venda)
    todos_servicos = orchestrator.buscar_todos_servicos()
    return todos_servicos


def _find_route_ocurrence_circuit_breaker(company_internal_id, modelo_venda, servico, id_hash, data_viagem):
    try:
        itinerario = _fetch_parsed_itinerario(company_internal_id, modelo_venda, servico, data_viagem)
    except (RodoviariaException, RodoviariaItinerarioNotFoundException):
        return None
    datetime_ida = itinerario[0].datetime_ida
    rota_hash = itinerario.hash
    if id_hash == rota_hash:
        return datetime_ida
    return None


@shared_task(rate_limit=DEFAULT_RATE_LIMIT)
@retry(
    exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens),
    min_delay=10,
)
def _find_route_ocurrence_task(company_internal_id, modelo_venda, rota_id, servico, id_hash, data_viagem):
    task_logger.info(
        "Procurando rotina para (rota_id, servico, data_viagem) = (%s, %s, %s)", rota_id, servico, data_viagem
    )
    return _find_route_ocurrence_circuit_breaker(company_internal_id, modelo_venda, servico, id_hash, data_viagem)


def _filter_servicos_condition(s, origem_external_id, destino_external_id):
    return s["origem"]["id"] == int(origem_external_id) and s["destino"]["id"] == int(destino_external_id)


@shared_task(rate_limit=DEFAULT_RATE_LIMIT)
@retry(exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens))
def _filter_possiveis_servicos_rota(
    company_internal_id, modelo_venda, rota_id, origem_external_id, destino_external_id
):
    todos_servicos = _fetch_todos_servicos(company_internal_id, modelo_venda)
    servicos_filtered = [
        s["numservico"]
        for s in todos_servicos
        if _filter_servicos_condition(s, origem_external_id, destino_external_id)
    ]
    msg = f"{len(servicos_filtered)} possíveis servicos para rodoviaria_rota_id {rota_id}: {servicos_filtered}"
    task_logger.info(msg)
    return servicos_filtered


@shared_task
def _fetch_rotina_callback(datetimes_ida, rota_id, first_checkpoint_timezone, dt_begin, next_days):
    orotinas = []
    datetimes_ida_hydrated = [d for d in datetimes_ida if d]
    new_utc_dts = []
    for d in datetimes_ida_hydrated:
        dt = datetime.fromisoformat(d) if isinstance(d, str) else d
        datetime_ida_tz = to_tz(dt, first_checkpoint_timezone)
        new_utc_dts.append(to_tz(datetime_ida_tz, "UTC"))
        orotinas.append(Rotina(rota_id=rota_id, datetime_ida=datetime_ida_tz))
        task_logger.info("Rotina para rodoviaria_rota_id %s encontrada em %s", rota_id, datetime_ida_tz)
    rota = Rota.objects.get(pk=rota_id)
    rota.atualiza_rotinas_com_base_nas_novas(new_utc_dts, next_days)
    Rotina.objects.bulk_create(orotinas, ignore_conflicts=True)
    delta = str(datetime.now() - datetime.fromisoformat(dt_begin))
    task_logger.info(
        "Foram encontradas %s rotinas para rodoviaria_rota_id %s: %s. dT=%s",
        len(orotinas),
        rota_id,
        datetimes_ida_hydrated,
        delta,
    )


@shared_task
def _find_rotina_por_rota(
    servicos,
    company_internal_id,
    modelo_venda,
    rota_id,
    next_days,
    first_day,
    id_hash,
    first_checkpoint_timezone,
    queue_name=DEFAULT_QUEUE_NAME,
):
    tasks = []
    first_day = datetime.fromisoformat(first_day) if isinstance(first_day, str) else first_day
    for servico in servicos:
        for days_to_add in range(0, next_days + 1):
            current_datetime_viagem = first_day + timedelta(days=days_to_add)
            data_viagem = current_datetime_viagem.strftime("%Y-%m-%d")
            tasks.append(
                _find_route_ocurrence_task.s(
                    company_internal_id,
                    modelo_venda,
                    rota_id,
                    servico,
                    id_hash,
                    data_viagem,
                ).set(queue=queue_name)
            )
    dt_begin = datetime.now().isoformat()
    return chord(tasks)(
        _fetch_rotina_callback.s(rota_id, first_checkpoint_timezone, dt_begin, next_days).set(queue=queue_name)
    )


@shared_task
def _fetch_rotina_por_rota(
    company_internal_id,
    modelo_venda,
    rota_id,
    first_checkpoint_timezone,
    origem_external_id,
    destino_external_id,
    id_hash,
    next_days,
    first_day,
    queue_name=DEFAULT_QUEUE_NAME,
):
    task_logger.info("Iniciada a atualização de rotina para rota %s", rota_id)
    return chain(
        _filter_possiveis_servicos_rota.s(
            company_internal_id, modelo_venda, rota_id, origem_external_id, destino_external_id
        ).set(queue=queue_name),
        _find_rotina_por_rota.s(
            company_internal_id,
            modelo_venda,
            rota_id,
            next_days,
            first_day,
            id_hash,
            first_checkpoint_timezone,
            queue_name,
        ).set(queue=queue_name),
    )()


def _get_origem_destino_rota(rota):
    first_checkpoint_timezone = rota.first_checkpoint_timezone_or_default()
    origem_external_id = rota.first_checkpoint().id_external
    destino_external_id = rota.last_checkpoint().id_external
    return {
        "first_checkpoint_timezone": first_checkpoint_timezone,
        "origem_external_id": origem_external_id,
        "destino_external_id": destino_external_id,
    }


@shared_task
def _fetch_rotinas_empresa(
    company_internal_id, modelo_venda, rotas, next_days, first_day, queue_name=DEFAULT_QUEUE_NAME
):
    tasks = []
    for rota in rotas:
        if not rota.ativo:
            continue
        orig_dest_data = _get_origem_destino_rota(rota)
        tasks.append(
            _fetch_rotina_por_rota.s(
                company_internal_id,
                modelo_venda,
                rota.id,
                orig_dest_data["first_checkpoint_timezone"],
                orig_dest_data["origem_external_id"],
                orig_dest_data["destino_external_id"],
                rota.id_hash,
                next_days,
                first_day,
                queue_name,
            ).set(queue=queue_name)
        )

    task_group = group(tasks)
    task_group_result = task_group()
    return task_group_result


def fetch(company_internal_id, modelo_venda, rota, next_days, first_day=None, queue_name=DEFAULT_QUEUE_NAME):
    if not first_day or first_day < today_midnight():
        first_day = today_midnight()
    if not queue_name:
        queue_name = DEFAULT_QUEUE_NAME
    orig_dest_data = _get_origem_destino_rota(rota)
    return _fetch_rotina_por_rota.s(
        company_internal_id,
        modelo_venda,
        rota.id,
        orig_dest_data["first_checkpoint_timezone"],
        orig_dest_data["origem_external_id"],
        orig_dest_data["destino_external_id"],
        rota.id_hash,
        next_days,
        first_day,
        queue_name,
    ).set(queue=queue_name)()


def fetch_rotinas_empresa(
    client,
    company_id,
    next_days,
    first_day=None,
    queue_name=DEFAULT_QUEUE_NAME,
    modelo_venda=Company.ModeloVenda.MARKETPLACE,
):
    company = Company.objects.get(id=company_id)
    company_internal_id = company.company_internal_id
    if not first_day:
        first_day = today_midnight()
    else:
        first_day = datetime.fromisoformat(first_day) if isinstance(first_day, str) else first_day
        if first_day < today_midnight():
            first_day = today_midnight()
    if not queue_name:
        queue_name = DEFAULT_QUEUE_NAME
    log_msg = f"Iniciada a atualização rotinas de rotas da empresa {company.name}"
    logger.info(log_msg)
    rotas = Rota.objects.filter(company_id=company_id, ativo=True)
    return _fetch_rotinas_empresa.s(company_internal_id, modelo_venda, rotas, next_days, first_day, queue_name).set(
        queue=queue_name
    )()

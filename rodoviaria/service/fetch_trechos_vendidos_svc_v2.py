# As funções desse arquivo devem sobrescrever o arquivo fetch_trechos_vendidos_svc.py
# __       __       __       __       __       __       __
# \ \      \ \      \ \      \ \      \ \      \ \      \ \
#  \ \      \ \      \ \      \ \      \ \      \ \      \ \
# __\ \    __\ \    __\ \    __\ \    __\ \    __\ \    __\ \
# \  __\   \  __\   \  __\   \  __\   \  __\   \  __\   \  __\
#  \ \      \ \      \ \      \ \      \ \      \ \      \ \
# __\ \    __\ \    __\ \    __\ \    __\ \    __\ \    __\ \
# \  __\   \  __\   \  __\   \  __\   \  __\   \  __\   \  __\
#  \ \      \ \      \ \      \ \      \ \      \ \      \ \
#   \ \      \ \      \ \      \ \      \ \      \ \      \ \
#    \/       \/       \/       \/       \/       \/       \/

import logging
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from itertools import combinations

from beeline import traced, tracer
from celery import chain, chord, group
from celery.app import shared_task
from celery.result import AsyncResult
from celery.utils.log import get_task_logger
from django.utils import timezone
from pydantic import BaseModel

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import to_default_tz, to_tz, today_midnight
from commons.memoize import memoize_with_log
from commons.token_bucket import NotEnoughTokens, get_token_bucket_robust
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import (
    Company,
    Integracao,
    Rota,
    Rotina,
    RotinaTrechoVendido,
    TaskStatus,
    TipoAssento,
    TrechoVendido,
)
from rodoviaria.service import busca_precos_trechos_vendidos_svc  # noqa
from rodoviaria.service.atualiza_operacao_utils import (
    create_or_update_trechos_vendidos_por_origem_e_destino,
    tv_unique_key,
)
from rodoviaria.service.create_local_embarque_cidade_svc import create_cidades_and_locais_from_itinerario, local_to_json
from rodoviaria.service.exceptions import RodoviariaConnectionError

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")

QUEUE_TRECHOS_VENDIDOS = DefaultQueueNames.TRECHOS_VENDIDOS
MODELO_VENDA_MARKETPLACE = Company.ModeloVenda.MARKETPLACE
RATE_LIMIT_TRECHOS_VENDIDOS = DefaultRateLimits.TrechosVendidos.RATE_LIMIT
LOW_RATE_LIMIT_TRECHOS_VENDIDOS = DefaultRateLimits.TrechosVendidos.LOW_RATE_LIMIT

MAX_PRECO_TRECHO = Decimal("2500")


class FetchTrechoVendidoTaskForm(BaseModel):
    company_internal_id: int
    modelo_venda: str
    origem_local_id: str
    destino_local_id: str
    data_str: str
    timezone_origem: str
    timezone_destino: str
    rota_id: int
    expected_datetime_partidas_map: dict
    origem_id: int
    destino_id: int


def async_fetch_trechos_vendidos(
    locais_timezone,
    itinerario,
    rota,
    company,
    timezone_origem,
    queue_name=QUEUE_TRECHOS_VENDIDOS,
    return_task_object=False,
):
    _group = _group_task_fetch_trechos_vendidos(locais_timezone, itinerario, rota, company, timezone_origem, queue_name)
    _chain = chain(
        _group,
        fetch_trechos_vendidos_finisher_on_success.si(rota.id),
    ).on_error(fetch_trechos_vendidos_finisher_on_error.s(rota.id))
    if return_task_object:
        return _chain
    return dispara_tasks.delay(
        {
            "rota_id": rota.id,
            "company_id": rota.company_id,
            "task": _chain.task,
            "args": _chain.args,
            "kwargs": _chain.kwargs,
            "options": _chain.options,
            "subtask_type": _chain.subtask_type,
            "immutable": _chain.immutable,
        }
    )


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def fetch_trechos_vendidos_finisher_on_success(rota_id):
    _fetch_trechos_vendidos_finisher(rota_id)


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def fetch_trechos_vendidos_finisher_on_error(context, exception, traceback, rota_id):
    _fetch_trechos_vendidos_finisher(rota_id, exception=exception)


def _fetch_trechos_vendidos_finisher(rota_id, exception=None):
    if exception is None:
        task_logger.info("Finalizando fetch trechos vendidos da rota %s", rota_id)
    else:
        task_logger.error(
            "Erro ao finalizar fetch trechos vendidos da rota %s",
            rota_id,
            exc_info=exception,
        )

    rota = Rota.objects.get(pk=rota_id)
    TaskStatus.objects.update_or_create(
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
        rota_id=rota.id,
        company_id=rota.company_id,
        defaults={
            "status": TaskStatus.Status.SUCCESS if exception is None else TaskStatus.Status.FAILURE,
            "last_success_at": timezone.now(),
        },
    )
    _inativa_trechos_vendidos_antigos(rota_id)
    _associa_tipos_assentos_aos_trechos_vendidos(rota.company_id, rota_id)
    rota_tem_trechos_vendidos = rota.trechovendido_set.filter(ativo=True)
    if not rota_tem_trechos_vendidos:
        rota.ativo = False
        rota.updated_at = timezone.now()
        rota.save()


def _inativa_trechos_vendidos_antigos(rota_id):
    data_limite_atualizacao = timezone.now() - timedelta(days=2)
    trechos_vendidos_para_inativar = TrechoVendido.objects.filter(
        rota_id=rota_id, updated_at__lt=data_limite_atualizacao, ativo=True
    )
    task_logger.info("Inativando %s trechos_vendidos da rota %s", trechos_vendidos_para_inativar.count(), rota_id)
    trechos_vendidos_para_inativar.update(ativo=False, updated_at=timezone.now())


def _associa_tipos_assentos_aos_trechos_vendidos(company_id, rota_id):
    data_limite_atualizacao = timezone.now() - timedelta(days=2)
    trechos_vendidos_sem_tipo_assento = TrechoVendido.objects.filter(
        rota_id=rota_id, updated_at__gt=data_limite_atualizacao, tipo_assento__isnull=True, classe__isnull=False
    )

    classes_trechos_vendidos = {t.classe for t in trechos_vendidos_sem_tipo_assento}
    tipos_assentos_existentes = TipoAssento.objects.filter(
        company_id=company_id, tipo_assento_parceiro__in=classes_trechos_vendidos
    )

    tipos_assentos_existentes = {ta.tipo_assento_parceiro: ta for ta in tipos_assentos_existentes}
    tipos_assentos_para_criar = {}
    for c in classes_trechos_vendidos:
        if c not in tipos_assentos_existentes and c not in tipos_assentos_para_criar:
            tipos_assentos_para_criar[c] = TipoAssento(company_id=company_id, tipo_assento_parceiro=c)
    TipoAssento.objects.bulk_create(tipos_assentos_para_criar.values(), 300)
    tipos_assentos_existentes.update(tipos_assentos_para_criar)

    for tv in trechos_vendidos_sem_tipo_assento:
        if tv.classe in tipos_assentos_existentes:
            tv.tipo_assento_id = tipos_assentos_existentes[tv.classe].id

    TrechoVendido.objects.bulk_update(trechos_vendidos_sem_tipo_assento, ["tipo_assento_id", "updated_at"])


@traced("rodoviaria.service.fetch_trechos_vendidos_svc_v2._group_task_fetch_trechos_vendidos")
def _group_task_fetch_trechos_vendidos(locais_timezone, itinerario, rota, company, timezone_origem, queue_name):
    tasks = []
    checkpoints_map = {}
    first_cp_partida = itinerario[0].datetime_ida

    fetch_rotinas_dates_further = company.features and Company.Feature.FETCH_TRECHOS_FURTHER in company.features
    rotinas_list = _get_datetime_rotinas_to_find_trechos(rota.id, timezone_origem, fetch_rotinas_dates_further)
    origem_destinos_map = _get_map_origem_destinos(company.company_internal_id, company.modelo_venda)

    locais_map = create_cidades_and_locais_from_itinerario(itinerario, company.id)
    skipped_destinos_map = defaultdict(list)
    count_skipped = 0

    task_function = fetch_trecho_vendido_task
    if company.integracao.name == Integracao.API.EULABS:
        task_function = fetch_trecho_vendido_task_low_rate_limit

    for origem, destino in combinations(itinerario, 2):
        timezone_origem = locais_timezone.get(str(origem.local.external_local_id)) or timezone_origem
        timezone_destino = locais_timezone.get(str(destino.local.external_local_id)) or timezone_origem
        # Contempla rotas que comecam em um dia e terminam em outro
        # diff eh o shift em dias em relacao ao primeiro cp, de acordo com o provider_data
        diff_partida = origem.datetime_ida - first_cp_partida

        data_partidas_map = defaultdict(dict)
        for rotina_id, datetime_ida_rotina in rotinas_list:
            data_partida = datetime_ida_rotina + diff_partida
            data_partidas_map[data_partida.strftime("%Y-%m-%d")][data_partida.strftime("%Y-%m-%dT%H:%M:%S")] = rotina_id

        checkpoints_map[str(origem.local.external_local_id)] = local_to_json(origem.local)
        checkpoints_map[str(origem.local.external_local_id)]["first_cp_shift"] = diff_partida.total_seconds()
        destino_list = origem_destinos_map.get(origem.local.external_local_id)
        if destino_list is not None and destino.local.external_local_id not in destino_list:
            skipped_destinos_map[origem.local.external_local_id].append(destino.local.external_local_id)
            count_skipped += len(data_partidas_map.keys())
            continue
        timezone_destino = locais_timezone.get(str(destino.local.external_local_id)) or timezone_destino
        for partida_str, expected_datetime_partidas_map in data_partidas_map.items():
            with tracer("rodoviaria.service.fetch_trechos_vendidos_svc_v2.task_mounting"):
                tasks.append(
                    task_function.s(
                        company_internal_id=company.company_internal_id,
                        modelo_venda=company.modelo_venda,
                        origem_local_id=origem.local.external_local_id,
                        destino_local_id=destino.local.external_local_id,
                        data_str=partida_str,
                        timezone_origem=timezone_origem,
                        timezone_destino=timezone_destino,
                        rota_id=rota.id,
                        expected_datetime_partidas_map=expected_datetime_partidas_map,
                        origem_id=locais_map[str(origem.local.external_local_id)],
                        destino_id=locais_map[str(destino.local.external_local_id)],
                    ).set(queue=queue_name)
                )

    if skipped_destinos_map:
        logger.info(
            "Skipped trechos vendidos: %s",
            skipped_destinos_map,
            extra={"company_internal_id": company.company_internal_id, "requisicoes_economizadas": count_skipped},
        )

    _group = group(*tasks)
    return _group


def _get_datetime_rotinas_to_find_trechos(rota_id, timezone_origem, fetch_further=False) -> dict:
    # Busca rotinas ao longo de 7 dias, a partir da primeira rotina encontrada >= amanha 00h00
    rotinas_qs = Rotina.objects.filter(
        rota_id=rota_id, datetime_ida__gt=today_midnight() + timedelta(days=1), ativo=True
    ).values_list("id", "datetime_ida")
    rotinas_list = list(rotinas_qs)

    primeira_data_de_cada_horario = {}
    for _, rotina_datetime_ida in rotinas_list:
        primeira_data = primeira_data_de_cada_horario.get(rotina_datetime_ida.time())
        if not primeira_data or primeira_data > rotina_datetime_ida:
            primeira_data_de_cada_horario[rotina_datetime_ida.time()] = rotina_datetime_ida

    days_range = timedelta(days=(17 if fetch_further else 7))

    response = []
    for rotina_id, rotina_datetime_ida in rotinas_list:
        primeira_data = primeira_data_de_cada_horario[rotina_datetime_ida.time()]
        if primeira_data <= rotina_datetime_ida <= primeira_data + days_range:
            response.append((rotina_id, to_tz(rotina_datetime_ida, timezone_origem)))
    return response


@memoize_with_log(timeout=24 * 60 * 60)
def _get_map_origem_destinos(company_internal_id, modelo_venda):
    map_origem_destinos = {}
    orchestrator = OrchestrateRodoviaria(company_internal_id, modelo_venda)
    try:
        map_origem_destinos = orchestrator.map_cidades_destinos()
    except NotImplementedError:
        pass
    return map_origem_destinos


def _parse_datetime(dt):
    if isinstance(dt, str):
        return datetime.strptime(dt, "%Y-%m-%dT%H:%M:%S%z")
    elif isinstance(dt, datetime):
        return dt
    else:
        raise TypeError("datetime_ida do trecho deve ser uma string no formato ISO8601 ou um datetime.")


def find_trecho_vendido_circuit_breaker(task_form: FetchTrechoVendidoTaskForm):
    orchestrator = OrchestrateRodoviaria(task_form.company_internal_id, task_form.modelo_venda)
    return orchestrator.find_trecho_vendido(
        task_form.origem_local_id,
        task_form.destino_local_id,
        task_form.data_str,
        task_form.timezone_origem,
        task_form.timezone_destino,
        task_form.rota_id,
        list(task_form.expected_datetime_partidas_map.keys()),
        task_logger,
    )


@shared_task(rate_limit=RATE_LIMIT_TRECHOS_VENDIDOS)
@retry(
    exceptions_type=(
        RodoviariaConnectionError,
        MyCircuitBreakerError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
def fetch_trecho_vendido_task(*args, **kwargs):
    return _fetch_trecho_vendido_task(*args, **kwargs)


@shared_task(rate_limit=LOW_RATE_LIMIT_TRECHOS_VENDIDOS)
@retry(
    exceptions_type=(
        RodoviariaConnectionError,
        MyCircuitBreakerError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
def fetch_trecho_vendido_task_low_rate_limit(*args, **kwargs):
    return _fetch_trecho_vendido_task(*args, **kwargs)


def _fetch_trecho_vendido_task(
    company_internal_id,
    modelo_venda,
    origem_local_id,
    destino_local_id,
    data_str,
    timezone_origem,
    timezone_destino,
    rota_id,
    expected_datetime_partidas_map,
    origem_id,
    destino_id,
):
    task_form = FetchTrechoVendidoTaskForm(
        company_internal_id=company_internal_id,
        modelo_venda=modelo_venda,
        origem_local_id=origem_local_id,
        destino_local_id=destino_local_id,
        data_str=data_str,
        timezone_origem=timezone_origem,
        timezone_destino=timezone_destino,
        rota_id=rota_id,
        expected_datetime_partidas_map=expected_datetime_partidas_map,
        origem_id=origem_id,
        destino_id=destino_id,
    )
    logger.info(
        "fetch_trecho_vendido_task company=%s origem=%s destino=%s",
        company_internal_id,
        origem_local_id,
        destino_local_id,
        extra=task_form.dict(),
    )
    token_bucket = get_token_bucket_robust(task_form.company_internal_id)
    token_bucket.try_get_token()
    trechos_vendidos = find_trecho_vendido_circuit_breaker(task_form)
    if not trechos_vendidos:
        return trechos_vendidos
    _create_trechos_vendidos(task_form, trechos_vendidos)


@traced("rodoviaria.service.fetch_trechos_vendidos_svc_v2._create_trechos_vendidos")
def _create_trechos_vendidos(
    task_form: FetchTrechoVendidoTaskForm,
    trechos_vendidos: list[dict],  # tech-debt Transformar resposta do find_trecho_vendido em um form,
):
    if task_form.origem_id == task_form.destino_id:
        # não permite criar TV inválido.
        # TODO isso é algo que acontece? Pq?
        return

    trechos_vendidos_to_create = {}
    rotinas_trecho_vendido_to_create = {}
    for tv_dict in trechos_vendidos:
        ida_trecho = _parse_datetime(tv_dict["datetime_ida"])
        rotina_id = _get_rotina_id(task_form, ida_trecho)
        if not tv_dict.get("distancia"):
            tv_dict["distancia"] = abs(timedelta(seconds=tv_dict["duracao"]).seconds // 60)
            task_logger.warning(
                "distancia_nula: _create_trechos_vendidos",
                extra={"duracao": tv_dict["duracao"], "distancia": tv_dict["distancia"]},
            )
        if tv_dict["preco"] <= Decimal("0"):
            task_logger.warning("Preço do trecho igual ou menor a zero. Trecho: %s", tv_dict)
            continue
        if tv_dict["preco"] > Decimal("9999.99"):
            # TODO: adicionar lógica para tratamento de preços muito altos parametrizado por t["distancia"].
            # Algumas empresas colocam valores enormes pra inibir a venda.
            # Uma trava apenas pra não estourar erro no DB
            task_logger.warning("Preço do trecho maior que o limite permitido. Trecho: %s", tv_dict)
            continue

        tv = _trecho_vendido_obj(tv_dict, task_form)
        trecho_vendido_key = tv_unique_key(tv)
        tv_existente = trechos_vendidos_to_create.get(trecho_vendido_key)
        if tv_existente:
            capacidade = tv.capacidade_classe or 0
            tv = tv_existente
            tv.capacidade_classe = max(capacidade, tv.capacidade_classe)
        else:
            trechos_vendidos_to_create[trecho_vendido_key] = tv

        rotina_trecho_vendido_key = (ida_trecho, *trecho_vendido_key)
        if rotina_trecho_vendido_key not in rotinas_trecho_vendido_to_create:
            rtv = _rotina_trecho_vendido_obj(ida_trecho, rotina_id, tv)
            rotinas_trecho_vendido_to_create[rotina_trecho_vendido_key] = rtv

    create_or_update_trechos_vendidos_por_origem_e_destino(
        task_form.rota_id,
        trechos_vendidos_to_create.values(),
        rotinas_trecho_vendido_to_create.values(),
        task_form.origem_id,
        task_form.destino_id,
    )


def _get_rotina_id(task_form: FetchTrechoVendidoTaskForm, ida_trecho):
    return task_form.expected_datetime_partidas_map[ida_trecho.strftime("%Y-%m-%dT%H:%M:%S")]


def _trecho_vendido_obj(trecho_vendido_api, task_form):
    tv = TrechoVendido(
        rota_id=task_form.rota_id,
        origem_id=task_form.origem_id,
        destino_id=task_form.destino_id,
        classe=trecho_vendido_api["classe"],
        capacidade_classe=trecho_vendido_api["capacidade_classe"],
        distancia=trecho_vendido_api["distancia"],
        duracao=timedelta(seconds=trecho_vendido_api["duracao"]),
        preco=trecho_vendido_api["preco"],
        updated_at=to_default_tz(datetime.now()),
        ativo=Decimal(str(trecho_vendido_api["preco"])) < MAX_PRECO_TRECHO,
    )
    return tv


def _rotina_trecho_vendido_obj(ida_trecho, rotina_id, tv):
    return RotinaTrechoVendido(
        trecho_vendido=tv,
        rotina_id=rotina_id,
        datetime_ida_trecho_vendido=ida_trecho,
    )


@shared_task(queue=DefaultQueueNames.MESSENGER, rate_limit=DefaultRateLimits.MESSENGER)
def dispara_tasks(task_dict):
    task = chord.from_dict(task_dict)
    result = task()
    result_parent = None
    if isinstance(result, AsyncResult):
        result_parent = result.parent
    result_to_save = result_parent or result
    task_id = result_to_save.id
    TaskStatus.objects.update_or_create(
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
        rota_id=task_dict["rota_id"],
        company_id=task_dict["company_id"],
        defaults={"task_id": task_id, "status": TaskStatus.Status.PENDING},
    )

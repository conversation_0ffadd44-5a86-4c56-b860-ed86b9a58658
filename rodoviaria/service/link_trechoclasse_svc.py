import logging
from datetime import timedelta

from django.utils import timezone
from sentry_sdk import capture_exception

from commons.django_utils import error_str
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, TaggedTrechoClasse, TrechoClasse
from rodoviaria.serializers.serializer_trechoclasse import TrechoClasseSerializer
from rodoviaria.service.update_trechoclasse_with_api_data import update_trechoclasse_with_api_data

buserlogger = logging.getLogger("rodoviaria")


def _get(
    tag=None,
    integracao=None,
    company_id=None,
    modelo_venda=None,
    grupo_id=None,
    trechoclasse_ids=None,
    days=None,
):
    now = timezone.now()
    qs = (
        TrechoClasse.objects.select_related("grupo", "grupo__company_integracao")
        .to_serialize(TrechoClasseSerializer())
        .filter(active=True, datetime_ida__gt=now, grupo__datetime_ida__gt=now - timedelta(days=30))
        # filtra também por datetime_ida do Grupo para otimizar a query no banco de dados
    )

    if tag:
        qs = qs.filter(tags__name=tag)

    if integracao:
        qs = qs.filter(grupo__company_integracao__integracao__name=integracao)

    if company_id:
        qs = qs.filter(grupo__company_integracao__company_internal_id=company_id)

    if modelo_venda:
        qs = qs.filter(grupo__company_integracao__modelo_venda=modelo_venda)

    if grupo_id:
        qs = qs.filter(grupo__grupo_internal_id=grupo_id)

    if trechoclasse_ids:
        qs = qs.filter(trechoclasse_internal_id__in=trechoclasse_ids)

    if days:
        qs = qs.filter(datetime_ida__lte=timezone.now() + timedelta(days=days))

    return qs


def _is_valid(tc):
    timediff = timezone.now() - tc.updated_at
    return timediff > timedelta(days=1)


def _refaz_link(trechoclasse, rodoviaria_company):
    tc = update_trechoclasse_with_api_data(
        rodoviaria_company,
        OrchestrateRodoviaria(rodoviaria_company.company_internal_id, rodoviaria_company.modelo_venda),
        trechoclasse,
    )
    return tc


def get(
    tag=None,
    integracao=None,
    company_id=None,
    modelo_venda=None,
    grupo_id=None,
    trechoclasse_ids=None,
):
    qs = _get(tag, integracao, company_id, modelo_venda, grupo_id, trechoclasse_ids)
    parsed_trechos = [tc.serialize() for tc in qs]
    return {"trechos": parsed_trechos}


def atualiza(
    tag=None,
    integracao=None,
    company_id=None,
    modelo_venda=None,
    grupo_id=None,
    trechoclasse_ids=None,
):
    qs = _get(tag, integracao, company_id, modelo_venda, grupo_id, trechoclasse_ids)
    total = len(qs)
    qs = qs.order_by("tags__id")[:250]

    msg = f"{len(qs)} de {total} trechos serão atualizados."
    if company_id and modelo_venda:
        company_name = Company.objects.get(company_internal_id=company_id, modelo_venda=modelo_venda).name
        msg = f"{len(qs)} de {total} trechos da empresa {company_name} ({modelo_venda}) serão atualizados."
    buserlogger.info(msg)

    successes = []
    exceptions = []
    for tc in qs:
        rodoviaria_company = tc.grupo.company_integracao
        trechoclasse_internal_id = tc.trechoclasse_internal_id

        try:
            if _is_valid(tc):
                _refaz_link(tc, rodoviaria_company)
                tc.refresh_from_db()
            successes.append(tc.serialize())
        except Exception as ex:
            capture_exception(ex)
            buserlogger.info(error_str(ex))
            exceptions.append(
                {
                    "trechoclasse_internal_id": trechoclasse_internal_id,
                    "error": error_str(ex),
                }
            )

    msg = f"{len(qs)} de {total} trechos foram atualizados."
    if company_id and modelo_venda:
        company_name = Company.objects.get(company_internal_id=company_id, modelo_venda=modelo_venda).name
        msg = f"{len(qs)} de {total} trechos da empresa {company_name} ({modelo_venda}) foram atualizados."
    buserlogger.info(msg)

    return {"successes": successes, "exceptions": exceptions}


def remove_tag(
    tag,
    integracao=None,
    company_id=None,
    modelo_venda=None,
    grupo_id=None,
    trechoclasse_ids=None,
    days=None,
):
    qs = _get(tag, integracao, company_id, modelo_venda, grupo_id, trechoclasse_ids, days)
    tc_internal_ids = []
    tc_rodov_ids = []
    for tc in qs:
        tc_internal_ids.append(tc.trechoclasse_internal_id)
        tc_rodov_ids.append(tc.id)
    TaggedTrechoClasse.objects.filter(content_object_id__in=tc_rodov_ids).delete()
    msg = f"A tag {tag} foi removida dos trechos_classe {tc_internal_ids}"
    buserlogger.info(msg)
    return {"mensagem": msg}


def add_tag(
    tag,
    integracao=None,
    company_id=None,
    modelo_venda=None,
    grupo_id=None,
    trechoclasse_ids=None,
    days=None,
):
    qs = _get(
        integracao=integracao,
        company_id=company_id,
        modelo_venda=modelo_venda,
        grupo_id=grupo_id,
        trechoclasse_ids=trechoclasse_ids,
        days=days or 30,
    )
    tc_ids = []
    for tc in qs:
        tc.tags.add(tag)
        tc_ids.append(tc.trechoclasse_internal_id)
    msg = f"A tag {tag} foi adicionada aos trechos_classe {tc_ids}"
    buserlogger.info(msg)
    return {"mensagem": msg}

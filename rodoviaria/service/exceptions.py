import logging

buserlogger = logging.getLogger("rodoviaria")


class RodoviariaBaseException(Exception):
    pass


class RodoviariaBlockingException(RodoviariaBaseException):
    pass


class PassengerNotRegistered(RodoviariaBaseException):
    pass


class PoltronaTrocadaException(RodoviariaBaseException):
    pass


class PassengerTicketAlreadyReturnedException(RodoviariaBaseException):
    message = "Passagem já foi trocada ou cancelada"

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class PassengerTicketAlreadyPrintedException(RodoviariaBaseException):
    message = "Passagem já foi impressa"

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class PassengerInvalidDocumentException(RodoviariaBaseException):
    message = "Documento inválido."

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class RodoviariaBpeException(RodoviariaBaseException):
    pass


class RodoviariaException(RodoviariaBaseException):
    """
        Expected Exceptions for integration with bus stations

    Attributes:
        message -- error message returned by bus tions APIs
    """

    message = ""

    def __init__(self, message=None):
        if message:
            self.message = message
            super().__init__(self.message)

    def __str__(self):
        return self.message


class RodoviariaOTAException(RodoviariaException):
    pass


class APIError(RodoviariaException):
    pass


class RodoviariaConnectionError(RodoviariaBaseException):
    """
    Exceptions for error on connection with extenal API
    """

    message = "Erro de conexão com a API da integração"

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class RodoviariaUnauthorizedError(RodoviariaBaseException):
    """
    Exceptions for unauthorized requests on API
    """

    message = "Login inválido"

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class RodoviariaTooManyRequestsError(RodoviariaConnectionError):
    """
    Exceptions for making too many requests on an extenal API
    """

    message = "Too many requests"

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class RodoviariaOverbookingException(RodoviariaBlockingException):
    """
    Exceptions for the user when booking without a free seat
    """

    message = "Não há mais poltronas disponíveis para essa viagem."

    def __init__(self, vagas_disponiveis=None, trechoclasse_id=None, ida_ou_volta=None):
        self.vagas_disponiveis = vagas_disponiveis
        self.trechoclasse_id = trechoclasse_id
        self.set_message(ida_ou_volta)
        super().__init__(self.message)

    def __str__(self):
        return self.message

    def plural_singular(self):
        if self.vagas_disponiveis > 1:
            return f"Apenas {self.vagas_disponiveis} poltronas disponíveis"
        elif self.vagas_disponiveis == 1:
            return f"Apenas {self.vagas_disponiveis} poltrona disponível"
        elif self.vagas_disponiveis == 0:
            return "Não há mais poltronas disponíveis"

    def set_message(self, ida_ou_volta=None):
        message = self.plural_singular()
        if ida_ou_volta:
            self.message = f"{message} para viagem de {ida_ou_volta}"
        else:
            self.message = f"{message} para esta viagem"


class RodoviariaUnlinkedGroupException(RodoviariaBaseException):
    """
    Exception for unlinked group in buser_rodoviaria database
    """

    def __init__(self, grupo_id):
        self.grupo_id = grupo_id

    def __str__(self):
        return f"O grupo {self.grupo_id} ainda não está linkado"


class RodoviariaUnlinkedTrechoclasseException(RodoviariaBaseException):
    """
    Exception for unlinked trechoclasse in buser_rodoviaria database
    """

    def __init__(self, trechoclasse_id):
        self.trechoclasse_id = trechoclasse_id

    def __str__(self):
        return f"O trechoclasse {self.trechoclasse_id} ainda não está linkado"


class RodoviariaTrechoclasseFactoryException(RodoviariaBlockingException):
    """
    Exception for unmatch travel on API
    """

    message = "Serviço não encontrado na API"
    motivo = None

    def __init__(self, trechoclasse_id, message="Serviço não encontrado na API"):
        self.trechoclasse_id = trechoclasse_id
        self.message = message

    def __str__(self):
        message = self.message
        if self.motivo:
            message += f": {self.motivo}"
        return message


class RodoviariaTrechoclasseNotFoundException(RodoviariaTrechoclasseFactoryException):
    def __init__(self, trechoclasse_id, message=None, motivo=None):
        if motivo:
            self.motivo = motivo
        if message:
            self.message = message
        super().__init__(trechoclasse_id=trechoclasse_id, message=self.message)


class RodoviariaItinerarioNotFoundException(RodoviariaBaseException):
    message = "Itinerário não encontrado"

    def __init__(self, message=None):
        if message:
            self.message = message

    def __str__(self):
        return self.message


class RodoviariaUnsupportedFeatureException(RodoviariaException):
    pass


class RodoviariaNotImplementedException(RodoviariaBaseException):
    pass


class RodoviariaLocalEmbarqueException(RodoviariaBaseException):
    pass


class RodoviariaCompanyNotFoundException(RodoviariaBaseException):
    message = "Empresa não encontrada"

    def __init__(self, message=None):
        if message:
            self.message = message

    def __str__(self):
        return self.message


class RodoviariaRotaNotFoundException(RodoviariaBaseException):
    message = "Rota não encontrada no banco do rodoviaria"

    def __str__(self):
        return self.message


class RodoviariaNoLocalCheckpointException(RodoviariaBaseException):
    message = "Checkpoint sem local não linkado"

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class RodoviariaNoTimezoneException(RodoviariaBaseException):
    message = "Timezone não encontrado"

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class RodoviariaCompanyExistenteException(RodoviariaBaseException):
    def __init__(self, company_internal_id, modelo_venda):
        self.message = (
            f"Empresa já existente com (modelo_venda={modelo_venda}, company_internal_id={company_internal_id})"
        )

    def __str__(self):
        return self.message


class RodoviariaLoginNotFoundException(RodoviariaBaseException):
    def __init__(self, integracao, company_pk=None, modelo_venda=None):
        self.message = f"Login {integracao} não encontrado para (company_pk={company_pk}, modelo_venda={modelo_venda})"

    def __str__(self):
        return self.message


class RodoviariaIntegracaoNotFoundException(RodoviariaBaseException):
    def __init__(self, integracao):
        self.message = f"Integracao {integracao} não encontrada"

    def __str__(self):
        return self.message


class RodoviariaTrechoNotFoundException(RodoviariaException):
    message = "Trecho não disponível"


class RodoviariaTrechoBloqueadoException(RodoviariaException):
    message = "Trecho bloqueado pelo parceiro"


class RodoviariaTimeoutException(RodoviariaConnectionError):
    message = "Tempo excedido na comunicação com o serviço"

    def __init__(self, message=None):
        if message:
            self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


class HibridoNotAllowedException(RodoviariaException):
    message = "Função não implementada para modelo híbrido"


class TaskInExecutionException(RodoviariaException):
    message = "Task em execução"


class HibridoEmissaoForaDaData(RodoviariaException):
    message = "Emissão do modelo Hibrido só pode ser feita faltando menos de 3h para o embarque"


class RodoviariaUnableHardStop(RodoviariaBaseException):
    def __init__(self, nome_empresa):
        self.message = f"A operação da empresa {nome_empresa} já está em hardStop"

    def __str__(self):
        return self.message


class RodoviariaUnableRevertHardStop(RodoviariaBaseException):
    def __init__(self, nome_empresa):
        self.message = f"A operação da empresa {nome_empresa} não está em hardStop"

    def __str__(self):
        return self.message


class RodoviariaCompanyNotIntegratedError(RodoviariaBaseException):
    def __init__(self, nome_empresa):
        self.message = f"A empresa {nome_empresa} não está integrada"

    def __str__(self):
        return self.message


class AtualizacaoCheckoutEmAndamento(RodoviariaException):
    message = "Atualização do checkout ainda em execução"


class PoltronaJaSelecionadaException(RodoviariaException):
    message = "A poltrona já está selecionada"


class PoltronaExpirada(RodoviariaException):
    message = "A poltrona selecionada para compra não está bloqueada"


class PoltronaIndisponivel(RodoviariaException):
    message = "A poltrona selecionada para compra não está mais disponível"


class CampoObrigatorioException(RodoviariaException):
    pass


class DivergenciaPrecoException(RodoviariaException):
    pass


class RodoviariaInvalidParamsException(RodoviariaException):
    pass

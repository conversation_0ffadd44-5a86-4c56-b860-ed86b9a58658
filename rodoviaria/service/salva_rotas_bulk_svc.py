import json
from collections import defaultdict
from datetime import datetime, timedelta

from beeline import traced
from celery import shared_task
from celery.utils.log import get_task_logger
from django.db.models import Count, Q
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames
from commons.dateutils import to_default_tz, to_tz
from commons.redis import lock
from rodoviaria.models.core import Checkpoint, Company, Rota, Rotina, TaskStatus
from rodoviaria.service.atualiza_operacao_utils import finaliza_task_status

task_logger = get_task_logger(__name__)

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE
DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTAS
LOG_PREFIX = "[salva_rotas_bulk]"


@shared_task(queue=DEFAULT_QUEUE_NAME)
@traced("salvar_rotas_achadas_em_bulk_task")
def salvar_rotas_achadas_em_bulk_task(
    rotas_result, company_internal_id, next_days, modelo_venda=Company.ModeloVenda.MARKETPLACE
):
    task_logger.info(
        "%s [company_internal_id=%s] Inicio salvar_rotas_achadas_em_bulk_task rotas_result=%s",
        LOG_PREFIX,
        company_internal_id,
        rotas_result,
    )
    company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
    rotas_descobertas = _agrega_itinerarios_por_hash(rotas_result)
    bulk_create_rota(company=company, itinerarios=rotas_descobertas, next_days=next_days)
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_ROTAS, company_id=company.id)


def _agrega_itinerarios_por_hash(rotas_result):
    rotas_descobertas = defaultdict(list)
    for rota in rotas_result:
        if rota:
            rotas_descobertas[rota["hash"]].append(
                {
                    "itinerario": rota["cleaned"],
                    "datetime_ida": datetime.strptime(rota["datetime_ida"], "%Y-%m-%dT%H:%M:%S"),
                    "id_external": rota["id_external"],
                }
            )

    return rotas_descobertas


def bulk_create_rota(company: Company, itinerarios: dict[int, list], next_days: int):
    hashes = itinerarios.keys()
    rotas_existentes = Rota.objects.filter(company_id=company.id, id_hash__in=hashes)
    _bulk_update_rotas(company.company_internal_id, rotas_existentes, itinerarios)

    hashes_existentes = list(rotas_existentes.values_list("id_hash", flat=True))
    rotas_novas = {}
    for id_hash in itinerarios:
        itinerario_cleaned = itinerarios[id_hash][0]["itinerario"]
        id_external = itinerarios[id_hash][0]["id_external"]
        if id_hash not in hashes_existentes:
            rota = Rota(
                id_hash=id_hash,
                company_id=company.id,
                provider_data=json.dumps(itinerario_cleaned),
                id_external=id_external,
            )
            rotas_novas[id_hash] = rota

    Rota.objects.bulk_create(rotas_novas.values(), batch_size=500)
    bulk_create_checkpoints(company.company_internal_id, [r.id for r in rotas_novas.values()])

    # ids de todas as rotas: novas e existentes
    rotas_ids = Rota.objects.filter(company_id=company.id, id_hash__in=hashes).values_list("pk", flat=True)
    bulk_create_update_rotinas(rotas_ids, itinerarios, next_days)


def bulk_create_checkpoints(company_internal_id, rotas_ids):
    rotas_checkpoints = _converter_parsed_data_to_checkpoints(company_internal_id, rotas_ids)
    Checkpoint.objects.bulk_create(rotas_checkpoints, batch_size=500)


def bulk_update_checkpoints(company_internal_id, rotas_ids):
    Checkpoint.objects.filter(rota_id__in=rotas_ids).delete()
    bulk_create_checkpoints(company_internal_id, rotas_ids)


def bulk_create_update_rotinas(rotas_ids, itinerarios, next_days):
    first_chekpoint_timezone_rotas = (
        Checkpoint.objects.select_related(
            "rotalocal__cidade",
        )
        .filter(rota_id__in=rotas_ids, idx=0)
        .values("rota_id", "rota__id_hash", "local__cidade__timezone")
    )
    first_checkpoint_por_rota_id = {
        c["rota_id"]: {
            "id_hash": c["rota__id_hash"],
            "first_checkpoint_timezone": c["local__cidade__timezone"] or "America/Sao_Paulo",
        }
        for c in first_chekpoint_timezone_rotas
    }
    rotinas_create_set = set()
    rotinas_update_set = set()
    orotinas_create = []
    orotinas_update = []
    rotas_map = Rota.objects.select_related("company").filter(pk__in=rotas_ids).in_bulk(field_name="id")
    rotinas = Rotina.objects.filter(rota_id__in=rotas_ids)
    rotinas_map = defaultdict(dict)
    for rotina in rotinas:
        rotinas_map[rotina.rota_id][rotina.datetime_ida] = rotina
    for rota_id in rotas_ids:
        id_hash = first_checkpoint_por_rota_id[rota_id]["id_hash"]
        first_checkpoint_timezone = first_checkpoint_por_rota_id[rota_id]["first_checkpoint_timezone"]
        for iti in itinerarios[id_hash]:
            rotina_dt_tz = to_tz(iti["datetime_ida"], first_checkpoint_timezone)
            rotina_dt_utc = to_tz(rotina_dt_tz, "UTC")
            id_external = iti["id_external"]
            if rotina_dt_utc in rotinas_map[rota_id].keys():
                rotina_to_update = rotinas_map[rota_id][rotina_dt_utc]
                rotina_to_update.ativo = True
                rotina_to_update.updated_at = timezone.now()
                rotina_to_update.id_external = id_external
                if (rota_id, rotina_dt_utc) not in rotinas_update_set:
                    orotinas_update.append(rotinas_map[rota_id][rotina_dt_utc])
                    rotinas_update_set.add((rota_id, rotina_dt_tz))
            else:
                if (rota_id, rotina_dt_utc) not in rotinas_create_set:
                    orotinas_create.append(Rotina(rota_id=rota_id, datetime_ida=rotina_dt_tz, id_external=id_external))
                    rotinas_create_set.add((rota_id, rotina_dt_tz))
        company_internal_id = rotas_map[rota_id].company.company_internal_id
        task_logger.info(
            "%s [company_internal_id=%s] Rotinas da rota rota_id=%s id_hash=%s (first_checkpoint_timezone=%s): %s",
            LOG_PREFIX,
            company_internal_id,
            rota_id,
            id_hash,
            first_checkpoint_timezone,
            itinerarios[id_hash],
        )
    Rotina.objects.bulk_update(orotinas_update, fields=["ativo", "updated_at", "id_external"], batch_size=5000)
    Rotina.objects.bulk_create(orotinas_create, batch_size=500)


def _is_rota_valid_to_update(rota):
    return timezone.now() - rota.updated_at > timedelta(minutes=5)


@lock("_bulk_update_rotas_{company_internal_id}")
def _bulk_update_rotas(company_internal_id, rotas_existentes: list[Rota], itinerarios: dict[int, list]):
    rotas_ids = []
    for rota in rotas_existentes:
        if not _is_rota_valid_to_update(rota):
            continue
        rotas_ids.append(rota.id)
        id_hash = rota.id_hash
        itinerario_cleaned = itinerarios[id_hash][0]["itinerario"]
        id_external = itinerarios[id_hash][0]["id_external"]
        rota.provider_data = json.dumps(itinerario_cleaned)
        rota.id_external = id_external
        rota.ativo = True
        rota.updated_at = to_default_tz(datetime.now())

    Rota.objects.bulk_update(
        rotas_existentes,
        fields=["provider_data", "id_external", "ativo", "updated_at"],
        batch_size=500,
    )
    bulk_update_checkpoints(company_internal_id, rotas_ids)


def _converter_parsed_data_to_checkpoints(company_internal_id, rotas_ids):
    from rodoviaria.api.orchestrator import OrchestrateRodoviaria

    orchestrator = OrchestrateRodoviaria(company_internal_id)
    rotas_checkpoints = orchestrator.converter_parsed_data_to_checkpoints(rotas_ids)
    return rotas_checkpoints


def inativa_rotinas_nao_atualizadas(company_rodoviaria_id, init, end):
    Rotina.objects.filter(
        rota__company_id=company_rodoviaria_id,
        ativo=True,
        datetime_ida__range=[init, end],
        updated_at__lt=timezone.now() - timedelta(days=1),
    ).update(ativo=False, updated_at=timezone.now())


def inativa_rotas_sem_rotinas(company_rodoviaria_id):
    filter_rotinas = Q(rotina__ativo=True, rotina__datetime_ida__gt=timezone.now())
    rotas_sem_rotinas = (
        Rota.objects.filter(company_id=company_rodoviaria_id)
        .annotate(qtd_rotinas=Count("rotina", filter=filter_rotinas))
        .filter(qtd_rotinas=0)
    )
    rotas_sem_rotinas.update(ativo=False, updated_at=timezone.now())

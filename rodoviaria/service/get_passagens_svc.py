from typing import Dict

from django.db.models import Q

from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Passagem


def get_passagens(travel_ids, buseiro_ids=None, status=None, with_preco_rodoviaria=False):
    filters = Q(travel_internal_id__in=travel_ids) & ~Q(status__in=(Passagem.Status.ERRO, Passagem.Status.INCOMPLETA))
    if buseiro_ids:
        filters &= Q(buseiro_internal_id__in=buseiro_ids)
    if status:
        filters &= Q(status=status)
    values = [
        "travel_internal_id",
        "buseiro_internal_id",
        "status",
        "localizador",
        "numero_passagem",
    ]
    if with_preco_rodoviaria:
        values.append("preco_rodoviaria")

    passagens = Passagem.objects.filter(filters).values(*values)

    return list(passagens)


def grupos_com_emissao(grupos_passenger_map) -> Dict[str, bool]:
    travel_internal_ids = []
    buseiro_internal_ids = []
    for passageiros in grupos_passenger_map.values():
        for p in passageiros:
            travel_internal_ids.append(p["travel_id"])
            buseiro_internal_ids.append(p["buseiro_id"])

    passageiros_emitidos = Passagem.objects.filter(
        status__in=Passagem.STATUS_CONFIRMADA_LIST,
        travel_internal_id__in=travel_internal_ids,
        buseiro_internal_id__in=buseiro_internal_ids,
    ).values_list("travel_internal_id", "buseiro_internal_id")

    passageiros_emitidos = set(passageiros_emitidos)

    map_grupo_tem_passagens_emitidas = {}
    for grupo_id, passageiros in grupos_passenger_map.items():
        map_grupo_tem_passagens_emitidas[grupo_id] = any(
            (p["travel_id"], p["buseiro_id"]) in passageiros_emitidos for p in passageiros
        )

    return map_grupo_tem_passagens_emitidas


def get_passagem_info(travel_id, buseiro_id):
    passagens = list(
        Passagem.objects.filter(travel_internal_id=travel_id, buseiro_internal_id=buseiro_id).order_by("id").values()
    )
    return passagens


def get_infos_atualizacao_passagem(buseiro_id, modelo_venda, travel_id):
    passagem_list = list(
        Passagem.objects.select_related("company_integracao", "trechoclasse_integracao__origem__cidade")
        .filter(travel_internal_id=travel_id, buseiro_internal_id=buseiro_id)
        .order_by("id")
    )

    if not passagem_list:
        return []

    orchestrator = OrchestrateRodoviaria(passagem_list[0].company_integracao.company_internal_id, modelo_venda)
    return orchestrator.get_atualizacao_passagem_api_parceiro(passagem_list)

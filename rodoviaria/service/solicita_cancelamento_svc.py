import logging

from rodoviaria.models.core import Passagem

logger = logging.getLogger("rodoviaria")


def solicita_cancelamento(travel_internal_id, buseiro_internal_id=None):
    passagens = Passagem.objects.filter(
        travel_internal_id=travel_internal_id,
        status=Passagem.Status.CONFIRMADA,
    )
    if buseiro_internal_id:
        passagens = passagens.filter(buseiro_internal_id=buseiro_internal_id)
    solicita_cancelamento_passagens(passagens)


def solicita_cancelamento_travels(travel_ids):
    passagens = Passagem.objects.filter(travel_internal_id__in=travel_ids)
    solicita_cancelamento_passagens(passagens)


def solicita_cancelamento_por_passagens_ids(passagens_ids):
    passagens = Passagem.objects.filter(id__in=passagens_ids, status=Passagem.Status.CONFIRMADA)
    solicita_cancelamento_passagens(passagens)


def solicita_cancelamento_passagens(passagens):
    for p in passagens:
        p.tags.add("cancelamento_pendente")
        logger.info(
            "passagem de id %s (p.travel_internal_id=%s, p.buseiro_internal_id=%s) marcada com cancelamento pendente",
            p.id,
            p.travel_internal_id,
            p.buseiro_internal_id,
        )

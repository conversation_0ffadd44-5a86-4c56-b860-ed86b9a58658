import json
from datetime import timed<PERSON><PERSON>

from celery import group
from celery.app import shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import to_tz
from commons.django_utils import error_str
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Grupo, TrechoClasse
from rodoviaria.service.exceptions import (
    RodoviariaCompanyNotFoundException,
    RodoviariaException,
    RodoviariaItinerarioNotFoundException,
    RodoviariaTooManyRequestsError,
)
from rodoviaria.service.rota_svc import (
    create_or_update_rota,
    get_timezone_inicio_itinerario,
    itinerario_parsed_data_from_rota,
)

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE
DEFAULT_QUEUE_NAME = DefaultQueueNames.FETCH_ROTAS
DEFAULT_RATE_LIMIT = DefaultRateLimits.FETCH_ROTAS

buserlogger = get_task_logger(__name__)


def marketplace_fetch_rotas(company_id=None, grupo_id=None):
    if grupo_id:
        return _fetch_rotas_por_grupo_id(grupo_id)
    else:
        return _fetch_rotas_por_company_id(company_id)


def fetch_rota_external(company_internal_id, id_external, data, modelo_venda):
    try:
        company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
    except Company.DoesNotExist as e:
        raise RodoviariaCompanyNotFoundException("rodoviaria.Company not registered") from e
    orchestrator = OrchestrateRodoviaria.from_company(company)

    itinerario = orchestrator.fetch_rota(id_external, data)
    rota_external_id = _rota_external_id(itinerario.parsed, id_external)
    rota, _ = create_or_update_rota(
        orchestrator,
        company_id=company.id,
        itinerario=itinerario,
        id_external=rota_external_id,
    )
    provider_data = json.loads(rota.provider_data)
    return provider_data


def _fetch_rotas_por_grupo_id(grupo_id):
    trecho_classe = (
        TrechoClasse.objects.select_related("grupo__company_integracao")
        .filter(
            grupo__grupo_internal_id=grupo_id,
        )
        .first()
    )

    if not trecho_classe:
        return {"error": "TrechoClasse não encontrado"}

    orchestrator = OrchestrateRodoviaria(
        trecho_classe.grupo.company_integracao.company_internal_id,
        trecho_classe.grupo.company_integracao.modelo_venda,
    )
    try:
        rota = orchestrator.fetch_rota(
            trecho_classe.external_id,
            orchestrator.datetime_ida_from_trecho_classe(trecho_classe),
        )
    except RodoviariaException as ex:
        return {"error": error_str(ex)}
    rota_external_id = _rota_external_id(rota.parsed, trecho_classe.external_id)
    atualizar_rota_grupo(orchestrator, trecho_classe.grupo, rota, rota_external_id)

    return {"rota": json.loads(trecho_classe.grupo.rota.provider_data)}


def _fetch_rotas_por_company_id(company_id):
    trechos_classe = _get_distinct_tc_external_id_and_datetime(company_id)

    orchestrator = OrchestrateRodoviaria(company_id)
    tasks = []
    for tc in trechos_classe:
        try:
            tasks.append(
                _atualizar_rota_do_grupo_por_trecho_classe.s(
                    company_id,
                    tc.grupo.id,
                    tc.external_id,
                    orchestrator.datetime_ida_from_trecho_classe(tc),
                )
            )
        except RodoviariaException:
            pass
    tasks = group(tasks)
    tasks.apply_async()

    return {"quantidade_rotas_criadas": len(trechos_classe)}


def _get_distinct_tc_external_id_and_datetime(company_id):
    return (
        TrechoClasse.objects.select_related("grupo", "origem__cidade")
        .filter(
            grupo__datetime_ida__range=[
                timezone.now(),
                timezone.now() + timedelta(days=60),
            ],
            grupo__company_integracao__company_internal_id=company_id,
            grupo__company_integracao__modelo_venda=DEFAULT_MODELO_VENDA,
        )
        .distinct("external_id", "grupo__datetime_ida")
        .order_by("grupo__datetime_ida")
    )


def _rota_external_id(rota_parsed, default):
    if "rota_external_id" in rota_parsed.__dict__:
        return rota_parsed.rota_external_id
    return default


@shared_task(bind=True, queue=DEFAULT_QUEUE_NAME, rate_limit=DEFAULT_RATE_LIMIT)
@retry(
    exceptions_type=(
        RodoviariaTooManyRequestsError,
        MyCircuitBreakerError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
def _atualizar_rota_do_grupo_por_trecho_classe(self, company_id, grupo_id, external_id, datetime_ida):
    buserlogger.info("Atualizando ou criando a rota %s", external_id)
    orchestrator = OrchestrateRodoviaria(company_id)
    rota = _fetch_rota(orchestrator, external_id, datetime_ida)
    if rota:
        buserlogger.info("Rota encontrada para o servico %s do grupo %s", str(external_id), grupo_id)
        grupo = Grupo.objects.get(pk=grupo_id)
        rota_external_id = _rota_external_id(rota.parsed, external_id)
        atualizar_rota_grupo(orchestrator, grupo, rota, rota_external_id)


def _fetch_rota(orchestrator, external_id, grupo_datetime_ida):
    try:
        return orchestrator.fetch_rota(external_id, grupo_datetime_ida)
    except (RodoviariaException, RodoviariaItinerarioNotFoundException):
        # status 400 é esperado e ignorado
        return None


def atualizar_rota_grupo(orchestrator, grupo, rota, external_id):
    rota, _ = create_or_update_rota(
        orchestrator,
        company_id=grupo.company_integracao_id,
        itinerario=rota,
        id_external=external_id,
    )

    grupo_external_datetime_ida = to_tz(
        itinerario_parsed_data_from_rota(rota)[0].datetime_ida,
        get_timezone_inicio_itinerario(rota_id=rota.id),
    )
    grupo.rota = rota
    grupo.external_datetime_ida = grupo_external_datetime_ida
    grupo.save()

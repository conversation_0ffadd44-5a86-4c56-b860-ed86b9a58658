import logging
from datetime import datetime, timedelta

from celery import chain, chord, group
from celery.app import shared_task
from celery.utils.log import get_task_logger

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.token_bucket import NotEnoughTokens, get_token_bucket_robust
from rodoviaria.api.ti_sistemas import endpoints as endpoints
from rodoviaria.api.ti_sistemas.exceptions import TiSistemasAPIError
from rodoviaria.models.core import Company
from rodoviaria.models.ti_sistemas import TiSistemasLogin
from rodoviaria.service.exceptions import RodoviariaConnectionError
from rodoviaria.service.salva_rotas_bulk_svc import salvar_rotas_achadas_em_bulk_task

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")

DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTAS
DEFAULT_RATE_LIMIT = DefaultRateLimits.Eulabs.ROTAS


DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE


def descobrir_rotas(
    client: TiSistemasLogin,
    company_internal_id,
    next_days=7,
    shift_days=2,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA)
    data_inicial = datetime.today() + timedelta(days=shift_days)
    data_final = datetime.today() + timedelta(days=shift_days + next_days)
    parsed_servicos = endpoints.buscar_lista_viagens_request(
        login=client, company_external_id=company.company_external_id, data_inicio=data_inicial, data_fim=data_final
    )
    msg = f"{len(parsed_servicos)} possíveis rotas encontradas para empresa {company_internal_id}"
    logger.info(msg)

    task = _buscar_e_salvar_rotas_dos_servicos(
        parsed_servicos,
        company_internal_id,
        next_days,
        queue_name,
        return_task_object,
    )
    return task


def _buscar_e_salvar_rotas_dos_servicos(
    servicos,
    company_internal_id,
    next_days,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    tasks = []
    tasks.extend(
        [_buscar_rota_servico.s(company_internal_id, servico.id_viagem).set(queue=queue_name) for servico in servicos]
    )
    _group = group(*tasks)
    if return_task_object:
        _chain = chain(
            _group,
            salvar_rotas_achadas_em_bulk_task.s(company_internal_id, next_days).set(queue=queue_name),
        )
        return _chain

    _chord = chord(_group)(salvar_rotas_achadas_em_bulk_task.s(company_internal_id, next_days).set(queue=queue_name))
    group_result = _chord.parent
    return group_result


@shared_task(rate_limit=DEFAULT_RATE_LIMIT)
@retry(
    exceptions_type=(
        RodoviariaConnectionError,
        MyCircuitBreakerError,
        TiSistemasAPIError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
def _buscar_rota_servico(company_internal_id, id_viagem):
    rota = buscar_rota_do_servico(company_internal_id, id_viagem)
    if rota.raw and len(rota.cleaned) > 1:
        logger.info("Rota encontrada para o servico %s", str(id_viagem))
        return {
            "id_external": id_viagem,
            "hash": rota.parsed.hash,
            "cleaned": rota.cleaned,
            "datetime_ida": rota.parsed[0].datetime_ida.strftime("%Y-%m-%dT%H:%M:%S"),
        }


def buscar_rota_do_servico(company_internal_id: int, id_viagem):
    from rodoviaria.api.orchestrator import OrchestrateRodoviaria

    token_bucket = get_token_bucket_robust(company_internal_id, DEFAULT_MODELO_VENDA)
    token_bucket.try_get_token()

    orchestrator = OrchestrateRodoviaria(company_internal_id, DEFAULT_MODELO_VENDA)
    params = {"id_viagem": id_viagem}
    return orchestrator.buscar_itinerario(params)

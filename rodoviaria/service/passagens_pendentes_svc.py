import logging

from celery import shared_task
from django.db.models import F

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from rodoviaria.forms.staff_forms import CheckPaxForm
from rodoviaria.models.core import Company, Passagem
from rodoviaria.service import reserva_svc
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaTrechoclasseFactoryException,
    RodoviariaTrechoclasseNotFoundException,
)
from rodoviaria.views_schemas import PassagensConfirmadasPorEmpresaParams

DEFAULT_RATE_LIMIT = DefaultRateLimits.ADD_PAX
DEFAULT_RATE_LIMIT_HIBRIDO = DefaultRateLimits.ADD_PAX_HIBRIDO
DEFAULT_QUEUE_NAME_MARKETPLACE = DefaultQueueNames.ADD_PAX
DEFAULT_QUEUE_NAME_HIBRIDO = DefaultQueueNames.ADD_PAX_HIBRIDO
EMISSAO_ASYNC_LOG_TAG = "[EMISSAO ASSÍNCRONA DE PASSAGENS]"

buserlogger = logging.getLogger("rodoviaria")


def lista_passagens_confirmadas_por_empresa(data: PassagensConfirmadasPorEmpresaParams):
    passagens = (
        Passagem.objects.filter(
            status__in=Passagem.STATUS_CONFIRMADA_LIST,
            trechoclasse_integracao__datetime_ida__range=[
                data.data_inicial,
                data.data_final,
            ],
        )
        .filter(company_integracao__company_internal_id__in=data.companies_ids)
        .values(
            travel_id=F("travel_internal_id"),
            buseiro_id=F("buseiro_internal_id"),
            passagem_id=F("id"),
        )
    )
    return list(passagens)


def emitir_passagens_pendentes_async(passagens: list[CheckPaxForm], modelo_venda):
    queue_name = DEFAULT_QUEUE_NAME_MARKETPLACE
    task_funcion = emitir_async
    if modelo_venda == Company.ModeloVenda.HIBRIDO:
        queue_name = DEFAULT_QUEUE_NAME_HIBRIDO
        task_funcion = emitir_async_hibrido
    for passagem in passagens:
        buserlogger.info(
            "%s disparando task para emissão da passagem (travel_id=%s e buseiro_id=%s)",
            EMISSAO_ASYNC_LOG_TAG,
            passagem.travel_id,
            passagem.passenger.id,
        )
        task_funcion.s(passagem.dict()).set(queue=queue_name).apply_async()


@shared_task(rate_limit=DEFAULT_RATE_LIMIT)
def emitir_async(passagem_dict):
    _emitir_async(passagem_dict)


@shared_task(rate_limit=DEFAULT_RATE_LIMIT_HIBRIDO)
def emitir_async_hibrido(passagem_dict):
    _emitir_async(passagem_dict)


def _emitir_async(passagem_dict):
    passagem = CheckPaxForm.parse_obj(passagem_dict)
    passagem_existe = Passagem.objects.filter(
        travel_internal_id=passagem.travel_id,
        buseiro_internal_id=passagem.passenger.buseiro_id,
        status__in=Passagem.STATUS_CONFIRMADA_LIST,
    ).exists()
    if passagem_existe:
        buserlogger.info(
            "%s tentativa de emissão de passagem já emitida (travel_id=%s e buseiro_id=%s)",
            EMISSAO_ASYNC_LOG_TAG,
            passagem.travel_id,
            passagem.passenger.id,
        )
        return

    try:
        reserva_svc.add_pax_na_lista(passagem)
    except (RodoviariaTrechoclasseNotFoundException, RodoviariaTrechoclasseFactoryException, RodoviariaConnectionError):
        pass

import logging
from collections import defaultdict
from itertools import product

from celery import shared_task

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from commons.dateutils import to_tz
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import CidadeInternal, Company, LocalEmbarque, ViagemAPILogger
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaOTAException,
    RodoviariaTrechoNotFoundException,
)

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE
logger = logging.getLogger("rodoviaria")


@shared_task(queue=DefaultQueueNames.BUSCAR_VIAGENS_SEARCH, rate_limit=DefaultRateLimits.BUSCAR_VIAGENS_SEARCH)
def buscar_viagens_todas_empresas_api_na_search(
    cidade_origem_internal_id: int,
    cidade_destino_internal_id: int,
    data_busca: str,
    rotina_atualizacao_id: int | None = None,
):
    companies = Company.objects.filter(
        rota__ativo=True,
        rota__trechovendido__ativo=True,
        rota__trechovendido__origem__cidade__cidade_internal_id=cidade_origem_internal_id,
        rota__trechovendido__destino__cidade__cidade_internal_id=cidade_destino_internal_id,
        rota__company__features__contains=[Company.Feature.BUSCAR_SERVICO],
    ).distinct()
    return _buscar_viagens_api_por_empresas(
        companies, cidade_origem_internal_id, cidade_destino_internal_id, data_busca, rotina_atualizacao_id
    )


@shared_task(queue=DefaultQueueNames.BUSCAR_VIAGENS_SEARCH, rate_limit=DefaultRateLimits.BUSCAR_VIAGENS_SEARCH)
def buscar_viagens_api_na_search(
    companies_ids: list[int], cidade_origem_internal_id: int, cidade_destino_internal_id: int, data_busca: str
):
    companies = Company.objects.filter(
        company_internal_id__in=companies_ids, modelo_venda=Company.ModeloVenda.MARKETPLACE
    )
    return _buscar_viagens_api_por_empresas(
        companies, cidade_origem_internal_id, cidade_destino_internal_id, data_busca
    )


def _buscar_viagens_api_por_empresas(
    companies: list[Company],
    cidade_origem_internal_id: int,
    cidade_destino_internal_id: int,
    data_busca: str,
    rotina_atualizacao_id: int | None = None,
):
    timezone = CidadeInternal.objects.get(id=cidade_origem_internal_id).timezone or "America/Sao_Paulo"
    locais_embarque_origem_map = get_locais_embarque_map(companies, cidade_origem_internal_id)
    locais_embarque_destino_map = get_locais_embarque_map(companies, cidade_destino_internal_id)
    logs: list[ViagemAPILogger] = []
    # TODO: tornar isso paralelo usando o Django_Async
    # No momento não tem como pois vamos usar o celery para executar a task
    for company in companies:
        api = OrchestrateRodoviaria.from_company(company)
        for origem, destino in product(locais_embarque_origem_map[company.id], locais_embarque_destino_map[company.id]):
            if origem["id_external"] == destino["id_external"]:
                continue
            request_params = {"origem": origem["id_external"], "destino": destino["id_external"], "data": data_busca}
            try:
                resp: BuscarServicoForm = api.buscar_corridas(request_params)
            except (RodoviariaTrechoNotFoundException, RodoviariaConnectionError, RodoviariaOTAException):
                continue
            logs += make_log_viagem_api(company, resp.servicos, origem, destino, timezone, rotina_atualizacao_id)
    create_log_viagens_encontradas(logs)
    return [log.to_dict_json() for log in logs]


def get_locais_embarque_map(companies, cidade_internal_id):
    locais_embarque = (
        LocalEmbarque.objects.select_related("cidade__company")
        .filter(cidade__company__in=companies, cidade__cidade_internal_id=cidade_internal_id)
        .values("id", "cidade__company_id", "local_embarque_internal_id", "id_external")
    )
    locais_embarque_map = defaultdict(list)
    for le in locais_embarque:
        locais_embarque_map[le["cidade__company_id"]].append(le)
    return locais_embarque_map


def make_log_viagem_api(
    company: Company,
    corridas_form: list[ServicoForm],
    origem: dict,
    destino: dict,
    timezone: str,
    rotina_atualizacao_id: int,
):
    logs = []
    for c in corridas_form:
        if c.vagas < 0:
            logger.info(
                "[BUSCA VIAGEM SEARCH] Trecho com vagas negativas",
                extra={
                    "origem_id": origem["id"],
                    "destino_id": destino["id"],
                    "datetime_ida": to_tz(c.external_datetime_ida, timezone),
                    "tipo_assento": c.classe,
                    "vagas": c.vagas,
                },
            )
            continue
        logs.append(
            ViagemAPILogger(
                company_id=company.id,
                origem_id=origem["id"],
                destino_id=destino["id"],
                datetime_ida=to_tz(c.external_datetime_ida, timezone),
                tipo_assento_parceiro=c.classe,
                preco=c.preco,
                id_external=c.external_id,
                vagas=c.vagas,
                rotina_atualizacao_id=rotina_atualizacao_id,
            )
        )
    return logs


def create_log_viagens_encontradas(logs: list[ViagemAPILogger]):
    ViagemAPILogger.objects.bulk_create(logs)

from datetime import timedelta
from decimal import Decimal

from django.db.models import F, Sum
from django.utils import timezone

from rodoviaria.models.core import Company, Passagem


def trechos_empresa_com_maiores_divergencias_de_preco(days_behind=1, quantity=20):
    now = timezone.now()
    sorted_divergencia = (
        Passagem.objects.filter(
            created_at__range=(now - timedelta(days=days_behind), now),
            company_integracao__modelo_venda=Company.ModeloVenda.MARKETPLACE,
            company_integracao__features__contains=[Company.Feature.ACTIVE],
            status__in=Passagem.STATUS_CONFIRMADA_LIST,
        )
        .values(
            "trechoclasse_integracao__origem__cidade__cidade_internal_id",
            "trechoclasse_integracao__destino__cidade__cidade_internal_id",
            "company_integracao__company_internal_id",
        )
        .annotate(
            divergencia=Sum(F("preco_rodoviaria") - F("valor_cheio")),
        )
        .order_by("-divergencia")
    )
    divergencias = [
        {
            "order": index,
            "origem_id": d["trechoclasse_integracao__origem__cidade__cidade_internal_id"],
            "destino_id": d["trechoclasse_integracao__destino__cidade__cidade_internal_id"],
            "company_id": d["company_integracao__company_internal_id"],
            "divergencia": d["divergencia"],
        }
        for index, d in enumerate(sorted_divergencia[:quantity], 1)
        if d["divergencia"] > Decimal("10.00")
    ]
    return divergencias

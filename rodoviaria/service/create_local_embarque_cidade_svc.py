from beeline import traced

import rodoviaria.models.core as rodoviaria
from commons.redis import lock
from commons.utils import strip_punctuation
from rodoviaria.models.core import Cidade, LocalEmbarque


def local_to_json(local):  # TODO: sobrescrever a serialização do cerely para não precisar mais disso
    return {
        "nome_cidade": local.nome_cidade,
        "external_local_id": local.external_local_id,
        "uf": local.uf,
        "external_cidade_id": local.external_cidade_id,
        "descricao": local.descricao,
        "id_cidade_ibge": local.id_cidade_ibge,
    }


def get_or_create_local_embarque(parada, company):
    nome_cidade = parada["nome_cidade"].partition("(")[0].rstrip()
    id_cidade = parada["external_cidade_id"]
    id_localidade = parada["external_local_id"]
    cidade, _ = Cidade.objects.get_or_create(
        company=company,
        id_external=id_cidade,
        defaults={
            "name": nome_cidade,
        },
    )
    local, _ = LocalEmbarque.objects.get_or_create(
        id_external=id_localidade,
        cidade=cidade,
        defaults={"nickname": parada["descricao"]},
    )
    return local


@traced("create_cidades_and_locais_from_itinerario")
@lock("create_cidades_e_locais_{company_rodoviaria_id}")
def create_cidades_and_locais_from_itinerario(itinerario, company_rodoviaria_id):
    locais_existentes = _lista_locais_embarque_por_empresa(company_rodoviaria_id)
    cidades_existentes = _lista_cidades_por_empresa(company_rodoviaria_id)
    cidades_to_create = []
    locais_to_create = []
    for cp in itinerario:
        cidade_id = cidades_existentes.get(str(cp.local.external_cidade_id))
        if not cidade_id:
            cidades_to_create.append(
                {
                    "id_external": cp.local.external_cidade_id,
                    "name": cp.local.nome_cidade.partition("(")[0].rstrip(),
                    "id_ibge": cp.local.id_cidade_ibge,
                }
            )

        local_embarque_id = locais_existentes.get(str(cp.local.external_local_id))
        if not local_embarque_id:
            locais_to_create.append(
                {
                    "cidade_external_id": cp.local.external_cidade_id,
                    "cidade_id": cidade_id,
                    "id_external": cp.local.external_local_id,
                    "nickname": cp.local.descricao,
                }
            )
    cidades_criadas = _create_cidades(cidades_to_create, company_rodoviaria_id) if cidades_to_create else []
    for c in cidades_criadas:
        cidades_existentes[str(c.id_external)] = c.id
    locais_criados = _create_locais(locais_to_create, cidades_existentes) if locais_to_create else []
    for local in locais_criados:
        locais_existentes[str(local.id_external)] = local.id
    return locais_existentes


def _create_cidades(cidades_to_create, company_rodoviaria_id):
    cidades_internas = rodoviaria.CidadeInternal.objects.values("id", "timezone", "name", "city_code_ibge")
    cidades_internas_por_id_ibge = {}
    cidades_internas_por_nome = {}
    for cidade_buser in cidades_internas:
        if cidade_buser["city_code_ibge"]:
            cidades_internas_por_id_ibge[cidade_buser["city_code_ibge"]] = cidade_buser
        cidades_internas_por_nome[strip_punctuation(cidade_buser["name"])] = cidade_buser

    cidades_models = []
    for cidade in cidades_to_create:
        cidade_interna = cidades_internas_por_id_ibge.get(cidade["id_ibge"])
        if not cidade_interna:
            cidade_interna = cidades_internas_por_nome.get(strip_punctuation(cidade["name"]))

        cidades_models.append(
            rodoviaria.Cidade(
                id_external=cidade["id_external"],
                name=cidade["name"],
                company_id=company_rodoviaria_id,
                cidade_internal_id=cidade_interna["id"] if cidade_interna else None,
                timezone=cidade_interna["timezone"] if cidade_interna else None,
            )
        )
    cidades = rodoviaria.Cidade.objects.bulk_create(cidades_models)
    return cidades


def _create_locais(locais_to_create, cidades_map):
    locais_models = []
    for local in locais_to_create:
        if not local.get("cidade_id"):
            local["cidade_id"] = cidades_map.get(str(local["cidade_external_id"]))
        locais_models.append(
            rodoviaria.LocalEmbarque(
                id_external=local["id_external"],
                nickname=local["nickname"],
                cidade_id=local["cidade_id"],
            )
        )
    locais = rodoviaria.LocalEmbarque.objects.bulk_create(locais_models)
    return locais


def _lista_cidades_por_empresa(company_rodoviaria_id):
    cidades = rodoviaria.Cidade.objects.filter(company_id=company_rodoviaria_id)
    cidades = {str(cidade.id_external): cidade.id for cidade in cidades}
    return cidades


def _lista_locais_embarque_por_empresa(company_rodoviaria_id):
    locais = rodoviaria.LocalEmbarque.objects.filter(cidade__company_id=company_rodoviaria_id)
    locais = {str(local.id_external): local.id for local in locais}
    return locais

from beeline import traced
from celery import shared_task
from django.db.models import F
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from core.models_grupo import GrupoClasse as CoreGrupoClasse
from rodoviaria.models.core import GrupoClasse as RodovGrupoClasse
from rodoviaria.models.vexado import VexadoGrupoClasse as RodovVexadoGrupoClasse
from rodoviaria.service import cancelar_grupos_hibridos_svc
from rodoviaria.service.status_integracao_svc import StatusIntegracaoSVC

DEFAULT_RATE_LIMIT = DefaultRateLimits.LINK_TRECHOS_CLASSES


@traced("link_grupos_classe_svc.link_grupos_classe_nao_linkados")
def link_grupos_classe_nao_linkados():
    rodoviaria_grupos_classe = RodovGrupoClasse.objects.filter(
        grupo__company_integracao__modelo_venda="hibrido",
        grupo__datetime_ida__gt=timezone.now(),
        grupoclasse_internal_id__isnull=False,
    )
    grupos_classe_linkados_ids = list(rodoviaria_grupos_classe.values_list("grupoclasse_internal_id", flat=True))
    core_grupos_classe = (
        CoreGrupoClasse.objects.prefetch_related("trechoclasse_set")
        .select_related("grupo__rota")
        .annotate(rota_origem_id=F("grupo__rota__origem_id"))
        .filter(
            grupo__modelo_venda="hibrido",
            grupo__datetime_ida__gt=timezone.now(),
            grupo__company_id__isnull=False,
        )
        .exclude(id__in=grupos_classe_linkados_ids, grupo__status="canceled")
    )
    trechos_classe_to_update = []
    for cgc in core_grupos_classe:
        tc = cgc.trechoclasse_set.filter(trecho_vendido__origem_id=cgc.rota_origem_id).first()
        if not tc:
            continue
        trechos_classe_to_update.append(tc.id)
    trechos_classe_atualizados = []
    for trecho_classe_id in trechos_classe_to_update:
        trechos_classe_atualizados.append(trecho_classe_id)
        _update_status_integracao_task.delay(trecho_classe_id)
    return {"trechos_classe_atualizados": len(trechos_classe_atualizados)}


@shared_task(queue=DefaultQueueNames.Hibrido.LINK_TRECHO_CLASSE, rate_limit=DEFAULT_RATE_LIMIT)
def _update_status_integracao_task(trecho_classe_internal_id):
    StatusIntegracaoSVC(internal_trecho_classe_id=trecho_classe_internal_id).atualiza()


@shared_task(queue=DefaultQueueNames.Hibrido.CANCELA_GRUPO_CLASSE, rate_limit=DEFAULT_RATE_LIMIT)
def _cancela_grupo_classe_task(company_id, grupo_classe_id):
    cancelar_grupos_hibridos_svc.cancelar_grupos_classe(company_id, [grupo_classe_id])


@traced("link_grupos_classe_svc.cancela_grupos_classe_com_link_invalido")
def cancela_grupos_classe_com_link_invalido():
    rodoviaria_grupos_classe = RodovVexadoGrupoClasse.objects.select_related("grupo_classe").filter(
        grupo_classe__grupo__company_integracao__modelo_venda="hibrido",
        grupo_classe__grupo__datetime_ida__gt=timezone.now(),
        grupo_classe__grupoclasse_internal_id__isnull=False,
        status=RodovVexadoGrupoClasse.Status.CRIADO,
    )
    rodov_grupos_classe_ids = set(
        rodoviaria_grupos_classe.values_list(
            "grupo_classe__grupoclasse_internal_id",
            "grupo_classe__grupo__company_integracao__company_internal_id",
        )
    )
    core_grupos_classe_map = CoreGrupoClasse.objects.in_bulk([r[0] for r in rodov_grupos_classe_ids])
    grupos_classe_to_cancel_count = 0
    for grupo_classe_id, company_id in rodov_grupos_classe_ids:
        if not core_grupos_classe_map.get(grupo_classe_id):
            grupos_classe_to_cancel_count += 1
            _cancela_grupo_classe_task.delay(company_id, grupo_classe_id)
    return {"grupos_classe_to_cancel": grupos_classe_to_cancel_count}

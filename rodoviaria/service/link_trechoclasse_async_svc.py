from datetime import timedelta

from beeline import traced
from celery.app import shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone
from redis.exceptions import LockError
from sentry_sdk import capture_exception

from bp.buserdjango_celery import atualiza_trecho_unico
from commons.celery_utils import Default<PERSON>ueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.redis import lock
from commons.taggit_utils import Tags
from commons.token_bucket import NotEnoughTokens, get_token_bucket_robust
from rodoviaria.api.forms import BuscarServicoForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import (
    Company,
    LocalEmbarque,
    TaggedTrechoClasse,
    TaggedTrechoClasseError,
    TrechoClasse,
    TrechoClasseError,
)
from rodoviaria.service.exceptions import RodoviariaConnectionError, RodoviariaOTAException
from rodoviaria.service.grupo_trechoclasse_factory import upsert_trecho_classe
from rodoviaria.service.servicos_proximos_svc import motivo_servico_nao_encontrado
from rodoviaria.views_schemas import LinkTrechoParams

task_logger = get_task_logger(__name__)

TAG_TO_BE_CLOSED = Tags.TAG_TO_BE_CLOSED_IN_DJANGO
TAG_TO_BE_UPDATED = Tags.TAG_TO_BE_UPDATED_IN_DJANGO
DEFAULT_QUEUE_NAME = DefaultQueueNames.LINK_TRECHOS_CLASSES
UPDATE_PRICE_QUEUE_NAME = DefaultQueueNames.UPDATE_PRICE_QUEUE_NAME
HOT_UPDATE_PRICE_QUEUE_NAME = DefaultQueueNames.HOT_UPDATE_PRICE_QUEUE_NAME
UPDATE_TOP_DIVERGENCIAS_QUEUE_NAME = DefaultQueueNames.UPDATE_TOP_DIVERGENCIAS_QUEUE_NAME
DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE


@shared_task(rate_limit=DefaultRateLimits.LinkTrechosClasses.HIGH_RATE_LIMIT)
@retry(
    exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens),
    min_delay=20,
)
def buscar_dados_api_high_rate_limit(trecho):
    return _buscar_dados_api(trecho)


@shared_task(rate_limit=DefaultRateLimits.LinkTrechosClasses.RATE_LIMIT)
@retry(
    exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens),
    min_delay=10,
)
def buscar_dados_api_default_rate_limit(trecho):
    return _buscar_dados_api(trecho)


@shared_task(rate_limit=DefaultRateLimits.LinkTrechosClasses.LOW_RATE_LIMIT)
@retry(
    exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens),
    min_delay=10,
)
def buscar_dados_api_low_rate_limit(trecho):
    return _buscar_dados_api(trecho)


def link_trechoclasse_async(
    trechos_a_verificar: list[LinkTrechoParams],
    use_update_price_queue=False,
    use_hot_update_price_queue=False,
    use_update_top_divergencias=False,
):
    queue = DEFAULT_QUEUE_NAME
    buscar_dados_func = buscar_dados_api_default_rate_limit
    if use_hot_update_price_queue:
        queue = HOT_UPDATE_PRICE_QUEUE_NAME
        buscar_dados_func = buscar_dados_api_high_rate_limit
    if use_update_price_queue:
        queue = UPDATE_PRICE_QUEUE_NAME
    if use_update_top_divergencias:
        queue = UPDATE_TOP_DIVERGENCIAS_QUEUE_NAME
    _link_trechoclasse_async(trechos_a_verificar, queue, buscar_dados_func)


def _link_trechoclasse_async(
    trechos_a_verificar: list[LinkTrechoParams],
    queue_name,
    buscar_dados_func=buscar_dados_api_default_rate_limit,
):
    trechos_a_verificar = remove_trechos_com_tag(trechos_a_verificar)

    ids_locais = []
    ids_companies = []
    for trecho_classe in trechos_a_verificar:
        ids_companies.append(trecho_classe.company_internal_id)
        ids_locais.append(trecho_classe.origem_internal_id)
        ids_locais.append(trecho_classe.destino_internal_id)

    companies_use_low_rate_limit = (
        Company.objects.select_related("integracao")
        .filter(
            integracao__use_low_rate_limit=True,
            company_internal_id__in=ids_companies,
            modelo_venda=DEFAULT_MODELO_VENDA,
        )
        .values_list("company_internal_id", flat=True)
    )

    locais = _get_locais_map(ids_locais, ids_companies)
    trechos_a_verificar_cleaned = []
    for trecho in trechos_a_verificar:
        try:
            cid = trecho.company_internal_id
            oid = trecho.origem_internal_id
            did = trecho.destino_internal_id
            trecho.rodoviaria_origem_id = locais[(cid, oid)][0]
            trecho.origem_id_external = locais[(cid, oid)][1]
            trecho.rodoviaria_destino_id = locais[(cid, did)][0]
            trecho.destino_id_external = locais[(cid, did)][1]
            trecho.use_low_rate_limit = cid in companies_use_low_rate_limit
            trechos_a_verificar_cleaned.append(trecho)
        except KeyError:
            pass

    _generate_tasks(trechos_a_verificar_cleaned, queue_name, buscar_dados_func)


def _generate_tasks(trechos_a_verificar: list[LinkTrechoParams], queue_name, buscar_dados_func):
    for trecho in trechos_a_verificar:
        task_function = buscar_dados_func
        # Adapta para empresa de baixo rate_limit
        if trecho.use_low_rate_limit:
            task_function = buscar_dados_api_low_rate_limit
        try:
            _deduplicate_tasks(
                trecho,
                task_function,
                queue_name,
                trecho.company_internal_id,
                trecho.modelo_venda,
                trecho.trechoclasse_internal_id,
            )
        except LockError:
            task_logger.info(
                "link_trechoclasse_async_lockerror",
                extra={
                    "task_function": task_function,
                    "queue_name": queue_name,
                    "company_id": trecho.company_internal_id,
                    "modelo_venda": trecho.modelo_venda,
                    "trechoclasse_id": trecho.trechoclasse_internal_id,
                },
            )
            pass


@lock("link_trechoclasse_async_{company_id}_{modelo_venda}_{trechoclasse_id}", expire=300)
def _deduplicate_tasks(trecho, task_function, queue_name, company_id, modelo_venda, trechoclasse_id):
    task_function.s(trecho.json()).set(queue=queue_name).apply_async()


def _get_locais_map(ids_locais, ids_companies):
    locais = (
        LocalEmbarque.objects.select_related("cidade__company")
        .filter(
            cidade__company__company_internal_id__in=ids_companies,
            cidade__company__modelo_venda=DEFAULT_MODELO_VENDA,
            local_embarque_internal_id__in=ids_locais,
        )
        .distinct("cidade__company__company_internal_id", "local_embarque_internal_id")
    )
    locais = {
        (local.cidade.company.company_internal_id, local.local_embarque_internal_id): (
            local.id,
            local.id_external,
        )
        for local in locais
    }
    return locais


def remove_trechos_com_tag(trechos_a_verificar: list[LinkTrechoParams]):
    ids_trechos_a_verificar = [x.trechoclasse_internal_id for x in trechos_a_verificar]
    ids_trechos_com_tag_to_be_closed = TrechoClasseError.objects.filter(
        trechoclasse_internal_id__in=ids_trechos_a_verificar,
        tags__name=TAG_TO_BE_CLOSED,
    ).values_list("trechoclasse_internal_id", flat=True)
    ids_trechos_com_tag_to_be_updated = TrechoClasse.objects.filter(
        trechoclasse_internal_id__in=ids_trechos_a_verificar,
        tags__name=TAG_TO_BE_UPDATED,
    ).values_list("trechoclasse_internal_id", flat=True)

    trechos_a_verificar = [
        x
        for x in trechos_a_verificar
        if x.trechoclasse_internal_id not in ids_trechos_com_tag_to_be_closed
        and x.trechoclasse_internal_id not in ids_trechos_com_tag_to_be_updated
    ]

    return trechos_a_verificar


def _is_valid(trecho: LinkTrechoParams):
    try:
        tc = TrechoClasse.objects.get(trechoclasse_internal_id=trecho.trechoclasse_internal_id)
    except TrechoClasse.DoesNotExist:
        return True  # o trecho ainda não existe no banco do rodoviaria, logo deve ser criado
    else:
        timediff = timezone.now() - tc.updated_at
        return timediff > timedelta(minutes=5)


@traced("link_trechoclasse_async_svc._buscar_dados_api")
def _buscar_dados_api(trecho):
    trecho = LinkTrechoParams.parse_raw(trecho)
    if not _is_valid(trecho):
        task_logger.info("Skip trecho de atualizacao async. trecho=%s", trecho)
        return

    task_logger.info("Buscando serviço para trecho %s", trecho.trechoclasse_internal_id)
    servicos_form = _buscar_dados_api_circuit_breaker(trecho)
    if not servicos_form:
        return

    encontrado = servicos_form.found
    task_logger.info("Serviço para o trecho %s encontrado=%s", trecho.trechoclasse_internal_id, encontrado)

    # TODO: Entender o porque precisa fazer essa gambs
    company = Company.objects.get(
        company_internal_id=trecho.company_internal_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    trecho.company_internal_id = company.id

    if encontrado:
        servico = servicos_form.servicos[0]
        task_logger.info("Salvando trecho encontrado: trecho=%s dados_api=%s", trecho.json(), servico.json())
        tc, _ = upsert_trecho_classe(api_form=servico, input_form=trecho)
        atualiza_trecho_unico.delay(
            trecho_classe_id=tc.trechoclasse_internal_id,
            to_close=False,
            preco=str(tc.preco_rodoviaria),
            vagas=tc.vagas,
        )
    else:
        servicos_proximos = [servico.dict() for servico in servicos_form.servicos]
        tc_error = salvar_trecho_classe_error(company, trecho, servicos_proximos)
        atualiza_trecho_unico.delay(
            trecho_classe_id=tc_error.trechoclasse_internal_id,
            to_close=True,
            motivo_unmatch=tc_error.motivo_fechamento,
        )
    return trecho.json()


def _buscar_dados_api_circuit_breaker(trecho: LinkTrechoParams) -> BuscarServicoForm:
    company_internal_id = trecho.company_internal_id

    token_bucket = get_token_bucket_robust(company_internal_id)
    token_bucket.try_get_token()

    orchestrator = OrchestrateRodoviaria(company_internal_id)

    try:
        return orchestrator.buscar_corridas(
            {
                "origem": trecho.origem_id_external,
                "destino": trecho.destino_id_external,
                "data": trecho.datetime_ida.strftime("%Y-%m-%d"),
            },
            {
                "datetime_ida": trecho.datetime_ida,
                "timezone": trecho.origem_timezone,
                "tipo_assento": trecho.tipo_assento,
            },
        )
    except (RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens):
        raise
    except RodoviariaOTAException:
        pass
    except Exception as ex:
        capture_exception(ex)


def salvar_trecho_classe_error(company: Company, dados_trecho: LinkTrechoParams, servicos_proximos):
    motivo, servicos_proximos_parseados = motivo_servico_nao_encontrado(
        company,
        dados_trecho.datetime_ida,
        dados_trecho.origem_timezone,
        dados_trecho.tipo_assento.lower(),
        servicos_proximos,
    )
    tc_obj = TrechoClasseError(
        trechoclasse_internal_id=dados_trecho.trechoclasse_internal_id,
        company_id=company.id,
        tipo_assento=dados_trecho.tipo_assento.lower(),
        datetime_ida=dados_trecho.datetime_ida,
        origem_id=dados_trecho.rodoviaria_origem_id,
        destino_id=dados_trecho.rodoviaria_destino_id,
        servicos_proximos=servicos_proximos,
        motivo=motivo,
        servicos_proximos_parseados=servicos_proximos_parseados,
    )
    fields_to_update = (
        "company_id",
        "tipo_assento",
        "datetime_ida",
        "origem_id",
        "destino_id",
        "servicos_proximos",
        "motivo",
        "servicos_proximos_parseados",
        "updated_at",
    )
    tcs_error = TrechoClasseError.objects.bulk_create(
        [tc_obj], update_conflicts=True, update_fields=fields_to_update, unique_fields=["trechoclasse_internal_id"]
    )
    return tcs_error[0]


def add_tag_on_trechos_in_bulk(trechos_created, tag_name):
    model_sample = trechos_created[0]
    model_sample.tags.add(tag_name)
    tag_id = TaggedTrechoClasse.objects.filter(tag__name=tag_name).values("tag_id")[0]["tag_id"]

    ids_trechos_created = [x.id for x in trechos_created]
    ids_trechos_tagged = TaggedTrechoClasse.objects.filter(content_object_id__in=ids_trechos_created).values_list(
        "content_object_id", flat=True
    )

    tag_throughs: list[TaggedTrechoClasse] = [
        TaggedTrechoClasse(
            tag_id=tag_id,
            content_object=x,
        )
        for x in trechos_created[1:]
        if x.id not in ids_trechos_tagged
    ]

    TaggedTrechoClasse.objects.bulk_create(tag_throughs, batch_size=250)


def add_tag_on_trechos_error_in_bulk(trechos_error, tag_name):
    model_sample = trechos_error[0]
    model_sample.tags.add(tag_name)
    tag_id = TaggedTrechoClasseError.objects.filter(tag__name=tag_name).values("tag_id")[0]["tag_id"]

    ids_trechos_created = [x.id for x in trechos_error]
    ids_trechos_tagged = TaggedTrechoClasseError.objects.filter(content_object_id__in=ids_trechos_created).values_list(
        "content_object_id", flat=True
    )

    tag_throughs: list[TaggedTrechoClasseError] = [
        TaggedTrechoClasseError(
            tag_id=tag_id,
            content_object=x,
        )
        for x in trechos_error[1:]
        if x.id not in ids_trechos_tagged
    ]

    TaggedTrechoClasseError.objects.bulk_create(tag_throughs, batch_size=250)


def get_tagged_trechos_classes():
    trechos_to_be_closed = TrechoClasseError.objects.filter(tags__name=TAG_TO_BE_CLOSED)
    trechos_to_be_closed = {tc.trechoclasse_internal_id: tc.motivo_fechamento for tc in trechos_to_be_closed}
    dados_trechos_to_be_updated = TrechoClasse.objects.filter(tags__name=TAG_TO_BE_UPDATED).values(
        "trechoclasse_internal_id", "preco_rodoviaria", "vagas"
    )
    dados_trechos_to_be_updated = {
        x["trechoclasse_internal_id"]: {"preco": x["preco_rodoviaria"], "vagas": x["vagas"]}
        for x in dados_trechos_to_be_updated
    }
    return {
        TAG_TO_BE_CLOSED: trechos_to_be_closed,
        TAG_TO_BE_UPDATED: dados_trechos_to_be_updated,
    }


def remover_tags_trechos_atualizados(trechos_por_tag):
    ids_trechos_closed = trechos_por_tag[TAG_TO_BE_CLOSED]
    ids_trechos_updated = trechos_por_tag[TAG_TO_BE_UPDATED]
    tags_trechos_closed = TaggedTrechoClasseError.objects.filter(
        content_object__trechoclasse_internal_id__in=ids_trechos_closed
    )
    tags_trechos_closed.delete()
    tags_trechos_updated = TaggedTrechoClasse.objects.filter(
        content_object__trechoclasse_internal_id__in=ids_trechos_updated
    )
    tags_trechos_updated.delete()

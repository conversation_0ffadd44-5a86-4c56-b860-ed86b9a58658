from collections import defaultdict

from rodoviaria.models.core import Company, TrechoVendido

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE


def get_classes_e_precos(internal_rota_id, ids_internal_trechos_vendidos=None, rodoviaria_rotina_id=None):
    qs = (
        TrechoVendido.objects.select_related("rota")
        .filter(
            rota__id_internal=internal_rota_id,
            rota__company__modelo_venda=DEFAULT_MODELO_VENDA,
            id_internal__isnull=False,
            ativo=True,
        )
        .order_by("classe")
    )
    if ids_internal_trechos_vendidos:
        qs = qs.filter(id_internal__in=ids_internal_trechos_vendidos)
    if rodoviaria_rotina_id:
        qs = qs.filter(rotinas=rodoviaria_rotina_id)

    trechos_vendidos_db = merge_trechos_similares(list(qs))

    return to_dict_classes_e_trechos(trechos_vendidos_db)


def to_dict_classes_e_trechos(trechos_vendidos):
    trechos = {}
    class_map = defaultdict(int)
    for tv in trechos_vendidos:
        key = tv["id_internal"]
        classe = tv["classe"]

        if key not in trechos.keys():
            trechos[key] = {}

        trechos[key][classe] = tv["preco"]

        class_map[classe] = max(class_map[classe], tv["capacidade_classe"])  # Map classe x max capacidade_classe

    classes = []
    for c in class_map:
        classes.append({"tipo_assento": c, "max_capacity": class_map[c]})

    retorno = {"classes": classes, "trechos_classes": trechos}
    return retorno


def merge_trechos_similares(trechos_vendidos):
    t_map = defaultdict(int)
    for t in trechos_vendidos:
        t_keys = (
            t.id_internal,
            t.origem_id,
            t.destino_id,
            t.classe_buser,
            t.preco,
            t.duracao,
            t.distancia,
            t.rota_id,
        )
        t_map[t_keys] += t.capacidade_classe or 0

    merged_trechos = [
        {
            "id_internal": t[0],
            "origem_id": t[1],
            "destino_id": t[2],
            "classe": t[3],
            "preco": t[4],
            "duracao": t[5],
            "distancia": t[6],
            "rodoviaria_rota_id": t[7],
            "capacidade_classe": t_map[t],
        }
        for t in t_map
    ]

    return merged_trechos

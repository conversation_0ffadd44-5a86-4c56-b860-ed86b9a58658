from datetime import datetime, timedelta

from django.core.paginator import Paginator
from django.db.models import Q

from commons.dateutils import replace_timezone, to_default_tz, to_tz
from commons.utils import strip_punctuation
from rodoviaria.forms.staff_forms import ListLinksLocaisForm
from rodoviaria.models.core import Checkpoint, CidadeInternal, LocalEmbarque, Rota, Rotina
from rodoviaria.serializers import serializer_locais


def list_filtered_locais(params: ListLinksLocaisForm):
    links_qs = LocalEmbarque.objects.select_related("cidade")

    if params.search:
        links_qs = links_qs.filter(
            Q(nickname__istartswith=params.search)
            | Q(nickname__istartswith=strip_punctuation(params.search))
            | Q(cidade__name__istartswith=params.search)
            | Q(cidade__name__istartswith=strip_punctuation(params.search))
        )

    if params.empresa_id_filter:
        links_qs = links_qs.filter(cidade__company_id=params.empresa_id_filter)

    if params.associado_rota is not None:
        links_qs = links_qs.filter(checkpoint__rota__ativo=params.associado_rota)

    if params.linkado_buser is not None:
        links_qs = links_qs.filter(local_embarque_internal_id__isnull=not params.linkado_buser)

    if params.order_by:
        links_qs = links_qs.order_by(*params.order_by)

    links_qs = links_qs.distinct().to_serialize(serializer_locais.LinksLocaisSerializer)

    if params.has_pagination:
        paginator = Paginator(links_qs, per_page=params.paginator.rows_per_page)
        page = paginator.get_page(params.paginator.page)
        response = {
            "items": [link.serialize() for link in page.object_list],
            "count": paginator.count,
            "num_pages": paginator.num_pages,
        }
    else:
        links_list = [link.serialize() for link in links_qs]
        response = {
            "items": links_list,
            "count": len(links_list),
            "num_pages": 1,
        }
    return response


def update_link_local_embarque(local_internal_id, local_embarque_id, cidade_internal_id=None):
    try:
        local = LocalEmbarque.objects.select_related("cidade__company").get(id=local_embarque_id)
    except LocalEmbarque.DoesNotExist:
        return {"error": "Erro ao encontrar local de embarque do rodoviaria"}

    if local_internal_id:
        existing_links = LocalEmbarque.objects.filter(
            cidade__company_id=local.cidade.company_id,
            local_embarque_internal_id=local_internal_id,
        )
        if len(existing_links) > 0:
            return {"error": f"Link já existente para esta empresa. ID = {local.id}"}

    cidade_timezone = None
    if cidade_internal_id:
        try:
            cidade_internal = CidadeInternal.objects.get(pk=cidade_internal_id)
            cidade_timezone = cidade_internal.timezone
        except CidadeInternal.DoesNotExist:
            return {"error": f"Cidade Interna pk = {cidade_internal_id} não encontrada"}

    local.local_embarque_internal_id = local_internal_id
    local.cidade.cidade_internal_id = cidade_internal_id
    old_tz = local.cidade.timezone or "America/Sao_Paulo"
    local.cidade.timezone = cidade_timezone
    local.save()
    local.cidade.save()

    update_checkpoints_por_local(local, old_tz)

    return True


def update_checkpoints_por_local(local, old_tz):
    if not local.local_embarque_internal_id:
        return

    company_pk = local.cidade.company_id
    rotas_pks = Rota.objects.filter(company_id=company_pk).values_list("pk", flat=True)
    checkpoints_comuns = Checkpoint.objects.select_related("local__cidade").filter(
        rota_id__in=rotas_pks, id_external=local.id_external
    )

    # Linka checkpoints com local de embarque
    cp_to_update = []
    for cp in checkpoints_comuns:
        cp.local = local
        cp.updated_at = to_default_tz(datetime.now())
        cp_to_update.append(cp)
    Checkpoint.objects.bulk_update(cp_to_update, ["local", "updated_at"])

    _corrige_timezones_rotinas_por_local(local, checkpoints_comuns, old_tz)

    return True


def _corrige_timezones_rotinas_por_local(local, checkpoints_comuns, old_tz):
    # Os checkpoints devem estar linkados com locais
    start = to_tz(datetime.now() - timedelta(days=1), "UTC")
    rotinas = Rotina.objects.select_related("rota").filter(
        rota__company_id=local.cidade.company_id, datetime_ida__gte=start
    )
    rotas_com_checkpoints_ids = checkpoints_comuns.values_list("rota_id", flat=True)
    rotinas_com_checkpoints = rotinas.filter(rota_id__in=rotas_com_checkpoints_ids)

    new_tz = local.cidade.timezone
    rt_to_update = []
    for rotina in rotinas_com_checkpoints:
        try:
            # Verifica se o tz do primeiro checkpoint foi alterado
            checkpoints_comuns.get(rota_id=rotina.rota_id, idx=0)
            # Troca tz da partida
            old_tz_dt = to_tz(rotina.datetime_ida, old_tz)
            rotina.datetime_ida = replace_timezone(old_tz_dt, new_tz)
            rotina.updated_at = to_default_tz(datetime.now())
            rt_to_update.append(rotina)
        except Checkpoint.DoesNotExist:
            pass

    Rotina.objects.bulk_update(rt_to_update, ["datetime_ida", "updated_at"])

    return True

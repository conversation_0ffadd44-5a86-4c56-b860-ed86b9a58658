import json
import logging

from django.utils.timezone import now, <PERSON><PERSON><PERSON>
from zoneinfo import ZoneInfo

import rodoviaria.models as rodoviaria
from commons.dateutils import to_tz
from commons.redis import lock
from commons.taggit_utils import Tags
from core.models_grupo import TrechoClasse
from rodoviaria.api.forms import ServicoForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.forms.grupo_trechoclasse_form import (
    CidadeInfos,
    GrupoInfo,
    GrupoTrechoClasseInfo,
    TrechoClasseInternoInfos,
)
from rodoviaria.models.core import (
    Company,
)
from rodoviaria.service import update_grupos_hibridos_svc
from rodoviaria.service.exceptions import (
    RodoviariaTrechoclasseFactoryException,
    RodoviariaTrechoclasseNotFoundException,
)
from rodoviaria.service.update_trechoclasse_with_api_data import get_api_data
from rodoviaria.views_schemas import LinkTrechoParams

buserlogger = logging.getLogger("rodoviaria")


def get_trechoclasse_from_buser_django(trechoclasse_id):
    core_tc = TrechoClasse.objects.select_related(
        "grupo", "grupo_classe", "trecho_vendido__origem", "trecho_vendido__destino"
    ).get(pk=trechoclasse_id)

    return trecho_classe_infos(core_tc)


def trecho_classe_infos(core_tc):
    core_tv_origem = core_tc.trecho_vendido.origem
    core_tv_destino = core_tc.trecho_vendido.destino
    core_grupo = core_tc.grupo
    core_grupoclasse = core_tc.grupo_classe

    core_cidade_origem = core_tv_origem.cidade
    cidade_origem = CidadeInfos(
        id=core_cidade_origem.id,
        timezone=core_cidade_origem.timezone,
        name=core_cidade_origem.name,
        city_code_ibge=core_cidade_origem.city_code_ibge,
    )

    core_cidade_destino = core_tv_destino.cidade
    cidade_destino = CidadeInfos(
        id=core_cidade_destino.id,
        timezone=core_cidade_destino.timezone,
        name=core_cidade_destino.name,
        city_code_ibge=core_cidade_destino.city_code_ibge,
    )

    return TrechoClasseInternoInfos(
        trechoclasse_id=core_tc.id,
        localembarque_origem_id=core_tv_origem.id,
        cidade_origem=cidade_origem,
        localembarque_destino_id=core_tv_destino.id,
        cidade_destino=cidade_destino,
        trecho_datetime_ida=core_tc.datetime_ida,
        grupo_id=core_grupo.id,
        grupo_datetime_ida=core_grupo.datetime_ida,
        grupoclasse_id=core_grupoclasse.id,
        tipo_assento=core_grupoclasse.tipo_assento,
    )


class GrupoTrechoClasseFactory:
    def __init__(self, rodoviaria_company, trechoclasse_from_buser_django, api):
        self.trechoclasse_id = trechoclasse_from_buser_django.trechoclasse_id
        self.api = api
        self.company_integracao = rodoviaria_company
        self._set_properties(trechoclasse_from_buser_django)
        self._set_api_data(trechoclasse_from_buser_django)

    def create(self, tag_to_add=Tags.TAG_TO_BE_UPDATED_IN_RODOVIARIA):
        trecho_classe, created = upsert_trecho_classe(api_form=self.api_data, input_form=self.params)

        if (
            self.company_integracao.modelo_venda == Company.ModeloVenda.HIBRIDO
            and self.company_integracao.integracao.name == "vexado"
        ):
            update_grupos_hibridos_svc.link_vexado_grupo_classe(
                trecho_classe.grupo_classe,
                self.company_integracao,
                self.api_data.veiculo_id,
                self.api_data.veiculo_andar,
                self.api_data.external_id,
                self.api_data.rota_external_id,
            )

        if tag_to_add:
            trecho_classe.tags.add(tag_to_add)

        # TODO: Revisar se faz sentido essa lógica
        last_update = None if created else trecho_classe.updated_at
        return GrupoTrechoClasseInfo(grupo=trecho_classe.grupo, trecho_classe=trecho_classe, last_update=last_update)

    def _set_properties(self, trechoclasse_from_buser_django):
        self.grupoclasse_internal_id = trechoclasse_from_buser_django.grupoclasse_id
        self.tipo_assento = trechoclasse_from_buser_django.tipo_assento
        self.grupo = GrupoInfo(
            grupo_id=trechoclasse_from_buser_django.grupo_id,
            datetime_ida=trechoclasse_from_buser_django.grupo_datetime_ida,
        )

        self.id_origem_localembarque = int(trechoclasse_from_buser_django.localembarque_origem_id)
        self.origem = self._get_local_embarque(
            self.id_origem_localembarque,
            trechoclasse_from_buser_django.cidade_origem,
            "origem",
        )

        self.id_destino_localembarque = int(trechoclasse_from_buser_django.localembarque_destino_id)
        self.destino = self._get_local_embarque(
            self.id_destino_localembarque,
            trechoclasse_from_buser_django.cidade_destino,
            "destino",
        )

        self.datetime_ida_utc = trechoclasse_from_buser_django.trecho_datetime_ida
        self.datetime_ida = self.datetime_ida_utc.astimezone(
            ZoneInfo(trechoclasse_from_buser_django.cidade_origem.timezone)
        )

    def _set_api_data(self, trechoclasse_from_buser_django):
        # TODO: guardar só os parâmetros e parar de guardar as outras infos
        self.params = LinkTrechoParams(
            company_internal_id=self.company_integracao.id,
            modelo_venda=self.company_integracao.modelo_venda,
            trechoclasse_internal_id=self.trechoclasse_id,
            origem_internal_id=self.origem.id,
            destino_internal_id=self.destino.id,
            datetime_ida=trechoclasse_from_buser_django.trecho_datetime_ida,
            origem_timezone=trechoclasse_from_buser_django.cidade_origem.timezone,
            grupo_internal_id=self.grupo.grupo_id,
            grupo_datetime_ida=self.grupo.datetime_ida,
            grupoclasse_internal_id=self.grupoclasse_internal_id,
            tipo_assento=trechoclasse_from_buser_django.tipo_assento,
            rodoviaria_origem_id=self.origem.id,
            rodoviaria_destino_id=self.destino.id,
        )
        self.api_data = get_api_data(
            self.company_integracao,
            self.api,
            self.trechoclasse_id,
            trechoclasse_from_buser_django.trecho_datetime_ida,
            trechoclasse_from_buser_django.cidade_origem.timezone,
            self.origem,
            self.destino,
            trechoclasse_from_buser_django.tipo_assento,
        )

    def _get_local_embarque(self, id_internal_localembarque, cidade, lado):
        try:
            return rodoviaria.LocalEmbarque.objects.get(
                cidade__company=self.company_integracao,
                local_embarque_internal_id=id_internal_localembarque,
            )
        except rodoviaria.LocalEmbarque.DoesNotExist as ex:
            raise RodoviariaTrechoclasseFactoryException(
                trechoclasse_id=self.trechoclasse_id,
                message=(
                    f"Local de embarque de {lado} de id {id_internal_localembarque} não existe no banco rodoviaria"
                ),
            ) from ex


def create_missing_grupo_and_trechoclasses(grupo_internal_id):
    # already created TrechoClasse's
    tc_created = rodoviaria.TrechoClasse.objects.filter(grupo__grupo_internal_id=grupo_internal_id, active=True)
    tc_created_ids = set(tc_created.values_list("trechoclasse_internal_id", flat=True))

    # missing TrechoClasse's
    tc_missing = TrechoClasse.objects.filter(grupo_id=grupo_internal_id).exclude(id__in=tc_created_ids)
    tc_missing = tc_missing.select_related("grupo")

    # create missing
    for trechoclasse in tc_missing:
        company_internal_id = trechoclasse.grupo.company_id
        modelo_venda = trechoclasse.grupo.modelo_venda
        try:
            company = rodoviaria.Company.objects.get(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
        except Company.DoesNotExist:
            continue
        api = OrchestrateRodoviaria(company_internal_id, modelo_venda)
        trechoclasse_from_buser_django = get_trechoclasse_from_buser_django(trechoclasse.id)
        try:
            factory = GrupoTrechoClasseFactory(company, trechoclasse_from_buser_django, api)
            factory.create()
            buserlogger.info(
                "[create_missing] Trecho classe %s criado com sucesso.",
                trechoclasse.id,
                extra={"company_internal_id": company_internal_id},
            )
        except RodoviariaTrechoclasseNotFoundException:
            # TrechoClasseError already saved on `get_api_data()`
            pass


def get_or_create_grupo(rodoviaria_company_id, grupo_internal_id, grupo_datetime_ida, grupo_linha):
    try:
        grupo, created = rodoviaria.Grupo.objects.get_or_create(
            grupo_internal_id=grupo_internal_id,
            defaults={
                "datetime_ida": grupo_datetime_ida,
                "linha": grupo_linha,
                "company_integracao_id": rodoviaria_company_id,
            },
        )
        if not created:
            grupo.company_integracao_id = rodoviaria_company_id
            grupo.save()
        grupos = [grupo]
    except rodoviaria.Grupo.MultipleObjectsReturned:
        grupos = rodoviaria.Grupo.objects.filter(grupo_internal_id=grupo_internal_id)
        grupos.update(company_integracao_id=rodoviaria_company_id)

    return grupos[0]


def get_or_create_grupo_classe(grupo, grupoclasse_internal_id, tipo_assento_internal, tipo_assento_external):
    try:
        grupo_classe, _ = rodoviaria.GrupoClasse.objects.get_or_create(
            grupoclasse_internal_id=grupoclasse_internal_id,
            tipo_assento_internal=tipo_assento_internal,
            defaults={"grupo_id": grupo.id},
        )
    except rodoviaria.GrupoClasse.MultipleObjectsReturned:
        grupo_classe = rodoviaria.GrupoClasse.objects.filter(
            grupoclasse_internal_id=grupoclasse_internal_id, tipo_assento_internal=tipo_assento_internal
        ).first()

    grupo_classe.tipo_assento_external = tipo_assento_external
    grupo_classe.save()

    return grupo_classe


@lock("upsert_trecho_classe_{input_form.trechoclasse_internal_id}", expire=60 * 2)
def upsert_trecho_classe(api_form: ServicoForm, input_form: LinkTrechoParams) -> tuple[rodoviaria.TrechoClasse, bool]:
    """
    Creates or updates a 'TrechoClasse' instance based on the data provided by the API (api_form)
    and the parameters used for API requests (input_form).
    """

    grupo = get_or_create_grupo(
        input_form.company_internal_id,
        input_form.grupo_internal_id,
        input_form.grupo_datetime_ida,
        api_form.linha,
    )
    grupo_classe = get_or_create_grupo_classe(
        grupo, input_form.grupoclasse_internal_id, input_form.tipo_assento, api_form.classe
    )

    try:
        trecho_classe, created = rodoviaria.TrechoClasse.objects.get_or_create(
            trechoclasse_internal_id=input_form.trechoclasse_internal_id, defaults={"grupo_id": grupo.id}
        )
    except rodoviaria.TrechoClasse.MultipleObjectsReturned:
        trecho_classe = rodoviaria.TrechoClasse.objects.filter(
            trechoclasse_internal_id=input_form.trechoclasse_internal_id
        ).first()
        created = False

    trecho_classe.destino_id = input_form.rodoviaria_destino_id
    trecho_classe.datetime_ida = input_form.datetime_ida
    trecho_classe.origem_id = input_form.rodoviaria_origem_id
    trecho_classe.grupo_classe = grupo_classe
    trecho_classe.grupo = grupo
    trecho_classe.external_id = api_form.external_id
    trecho_classe.external_datetime_ida = to_tz(api_form.external_datetime_ida, input_form.origem_timezone)
    trecho_classe.preco_rodoviaria = api_form.preco
    trecho_classe.vagas = api_form.vagas
    trecho_classe.external_id_tipo_veiculo = api_form.tipo_veiculo
    trecho_classe.desconto = api_form.desconto
    trecho_classe.external_id_desconto = api_form.id_desconto

    trecho_classe.provider_data = json.dumps(api_form.provider_data)
    trecho_classe.active = True
    trecho_classe.save()
    atualiza_trechos_vendidos(trecho_classe)

    return trecho_classe, created


def atualiza_trechos_vendidos(trecho_classe: rodoviaria.TrechoClasse):
    inicio = now() - timedelta(days=1)
    fim = now() + timedelta(days=7)

    if not inicio < trecho_classe.datetime_ida < fim:
        return

    trechos_vendidos = rodoviaria.TrechoVendido.objects.filter(
        rota__company=trecho_classe.grupo.company_integracao,
        origem=trecho_classe.origem,
        destino=trecho_classe.destino,
        classe=trecho_classe.grupo_classe.tipo_assento_external,
        status_preco=rodoviaria.TrechoVendido.StatusPreco.OK,
    )

    trechos_vendidos.update(preco=trecho_classe.preco_rodoviaria, datetimeida_preco=trecho_classe.datetime_ida)

from rodoviaria.service.exceptions import RodoviariaOverbookingException


def seleciona_poltrona(map_poltronas: dict[str, str], numero_poltronas: int, poltronas_to_exclude: list[str] = None):
    poltronas_to_exclude = poltronas_to_exclude or []
    set_poltronas = set()
    poltronas_separadas = []
    first_seat = False
    for poltrona, status in map_poltronas.items():
        n_poltrona = poltrona
        disponivel = status == "livre" and n_poltrona not in poltronas_to_exclude
        poltronas_restantes = numero_poltronas - len(set_poltronas)
        if not first_seat and poltronas_restantes > 1 and disponivel:
            first_seat = n_poltrona
            continue
        if poltronas_restantes == 1 and disponivel:
            set_poltronas.add(n_poltrona)
            break
        if first_seat and disponivel:
            set_poltronas.add(first_seat)
            set_poltronas.add(n_poltrona)
            first_seat = False
            if len(set_poltronas) >= numero_poltronas:
                break
        if first_seat and not disponivel:
            poltronas_separadas.append(first_seat)
            first_seat = False
            continue
    if first_seat and len(set_poltronas) < numero_poltronas:
        set_poltronas.add(first_seat)
    while len(set_poltronas) < numero_poltronas and poltronas_separadas:
        set_poltronas.add(poltronas_separadas.pop(0))
    if len(set_poltronas) < numero_poltronas:
        raise RodoviariaOverbookingException(vagas_disponiveis=len(set_poltronas))
    return sorted(set_poltronas)

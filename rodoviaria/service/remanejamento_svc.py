import logging
from datetime import <PERSON><PERSON><PERSON>

from celery import group, shared_task
from django.db.models import Q
from django.utils.timezone import now
from sentry_sdk.api import capture_exception

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from rodoviaria.forms.compra_rodoviaria_forms import ComprarForm, VerificarPoltronaForm
from rodoviaria.forms.staff_forms import RemanejaPassageiroForm
from rodoviaria.models.core import Passagem, Remanejamento, TrechoClasse
from rodoviaria.service import reserva_svc, staff_rodoviaria_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import (
    HibridoEmissaoForaDaData,
    PassengerNotRegistered,
    RodoviariaBaseException,
    RodoviariaConnectionError,
    RodoviariaOverbookingException,
    RodoviariaTrechoBloqueadoException,
)
from rodoviaria.views_schemas import RemanejaPassageirosAsyncParams

buserlogger = logging.getLogger("rodoviaria")
REMANEJAMENTO_LOG_TAG = "[REMANEJAMENTO]"


def _handle_error_response_on_relocation(
    remanejamento,
    etapa_erro,
    trechoclasse_origem_id,
    trechoclasse_destino_id,
    external_purchase_success,
    external_cancelation_success,
    reservation_code,
    info_msg,
    internal_relocation_permission=False,
    overbooking_on_purchase=False,
    connection_error_on_purchase=False,
    blocked_travel_on_purchase=False,
    poltronas_destino=None,
):
    remanejamento.status = Remanejamento.Status.ERRO
    remanejamento.etapa_erro = etapa_erro
    remanejamento.erro = info_msg
    remanejamento.save()
    buserlogger.info(info_msg)
    if poltronas_destino:
        staff_rodoviaria_svc.async_desbloquear_poltrona.delay(trechoclasse_destino_id, poltronas_destino)
    return {
        "reservation_code": reservation_code,
        "external_purchase_success": external_purchase_success,
        "external_cancelation_success": external_cancelation_success,
        "external_error_reason": info_msg,
        "is_trechoclasse_origem_integrado": staff_rodoviaria_svc.is_trechoclasse_integrado(trechoclasse_origem_id),
        "is_trechoclasse_destino_integrado": staff_rodoviaria_svc.is_trechoclasse_integrado(trechoclasse_destino_id),
        "internal_relocation_permission": internal_relocation_permission,
        "overbooking_on_purchase": overbooking_on_purchase,
        "connection_error_on_purchase": connection_error_on_purchase,
        "blocked_travel_on_purchase": blocked_travel_on_purchase,
    }


def remaneja_passageiros_async(params: RemanejaPassageirosAsyncParams):
    remanejamentos_map = {}
    for remanejamento_form in params:
        remanejamentos_map[(remanejamento_form.travel_id, remanejamento_form.travel_destino_id)] = Remanejamento(
            travel_destino_internal_id=remanejamento_form.travel_destino_id,
            travel_origem_internal_id=remanejamento_form.travel_id,
            grupo_destino_internal_id=remanejamento_form.grupo_destino_id,
            count_seats=len(remanejamento_form.passengers),
        )
    Remanejamento.objects.bulk_create(remanejamentos_map.values())

    remanejamentos_ids = []
    tasks = []
    for remanejamento_form in params:
        remanejamento_form.remanejamento_id = remanejamentos_map[
            (remanejamento_form.travel_id, remanejamento_form.travel_destino_id)
        ].id
        remanejamento_json_form = remanejamento_form.json()
        remanejamentos_ids.append(remanejamento_form.remanejamento_id)
        tasks.append(remaneja_passageiro_async.s(remanejamento_json_form))
    tasks_group = group(tasks)
    tasks_group.apply_async()
    return {
        "message": "tasks de remanejamento disparadas.",
        "remanejamentos_ids": remanejamentos_ids,
    }


@shared_task(queue=DefaultQueueNames.REMANEJAMENTO, rate_limit=DefaultRateLimits.REMANEJAMENTO)
def remaneja_passageiro_async(params_json):
    params = RemanejaPassageiroForm.parse_raw(params_json)
    remanejamento = Remanejamento.objects.get(id=params.remanejamento_id)
    buserlogger.info(
        "%s Executando remanejamento assincronamente (id=%s): %s -> %s",
        REMANEJAMENTO_LOG_TAG,
        remanejamento.id,
        remanejamento.travel_origem_internal_id,
        remanejamento.travel_destino_internal_id,
    )
    response = _remaneja_passageiro(params, remanejamento)
    buserlogger.info(
        "%s Remanejamento executado assincronamente (id=%s): %s -> %s",
        REMANEJAMENTO_LOG_TAG,
        remanejamento.id,
        remanejamento.travel_origem_internal_id,
        remanejamento.travel_destino_internal_id,
    )
    return response


def remaneja_passageiro_sync(params: RemanejaPassageiroForm):
    remanejamento = Remanejamento.objects.create(
        travel_destino_internal_id=params.travel_destino_id,
        travel_origem_internal_id=params.travel_id,
        grupo_destino_internal_id=params.grupo_destino_id,
        count_seats=len(params.passengers),
    )
    buserlogger.info(
        "%s Executando remanejamento sincronamente (id=%s): %s -> %s",
        REMANEJAMENTO_LOG_TAG,
        remanejamento.id,
        remanejamento.travel_origem_internal_id,
        remanejamento.travel_destino_internal_id,
    )
    return _remaneja_passageiro(params, remanejamento)


def _remaneja_passageiro(params: RemanejaPassageiroForm, remanejamento):
    external_purchase_success = False
    external_cancelation_success = False
    poltronas_destino_bkp = None

    if staff_rodoviaria_svc.is_empresa_integrada(params.company_destino_id, params.modelo_venda_destino):
        try:
            if not params.poltronas_destino:
                params_verifica_poltrona = VerificarPoltronaForm(
                    trechoclasse_id=params.trechoclasse_destino_id, passageiros=len(params.passengers)
                )
                params.poltronas_destino = CompraRodoviariaSVC(params_verifica_poltrona).verifica_poltrona(
                    params_verifica_poltrona
                )
            poltronas_destino_bkp = params.poltronas_destino.copy()
            reservation_params = ComprarForm(
                travel_id=params.travel_destino_id,
                trechoclasse_id=params.trechoclasse_destino_id,
                passageiros=params.passengers,
                valor_cheio=params.travel_max_split_value,
                poltronas=params.poltronas_destino,
            )
            CompraRodoviariaSVC(reservation_params).efetua_compra(reservation_params)
        except HibridoEmissaoForaDaData:
            pass
        except RodoviariaOverbookingException:
            info_msg = (
                f"{REMANEJAMENTO_LOG_TAG} Remanejamento de travel {params.travel_id}->{params.travel_destino_id} não"
                " movida externamente, por overbooking no trecho destino"
            )
            return _handle_error_response_on_relocation(
                remanejamento,
                Remanejamento.Etapas.EMISSAO,
                params.trechoclasse_origem_id,
                params.trechoclasse_destino_id,
                external_purchase_success,
                external_cancelation_success,
                params.reservation_code,
                info_msg,
                overbooking_on_purchase=True,
            )
        except RodoviariaConnectionError:
            info_msg = (
                f"{REMANEJAMENTO_LOG_TAG} Remanejamento de travel {params.travel_id}->{params.travel_destino_id} não"
                " movida externamente, por perda de conexão com a rodoviária"
            )
            return _handle_error_response_on_relocation(
                remanejamento,
                Remanejamento.Etapas.EMISSAO,
                params.trechoclasse_origem_id,
                params.trechoclasse_destino_id,
                external_purchase_success,
                external_cancelation_success,
                params.reservation_code,
                info_msg,
                connection_error_on_purchase=True,
                poltronas_destino=poltronas_destino_bkp,
            )

        except RodoviariaTrechoBloqueadoException as ex:
            info_msg = (
                f"{REMANEJAMENTO_LOG_TAG} Remanejamento de travel {params.travel_id}->{params.travel_destino_id} não"
                f" movida externamente, por trecho bloqueado pelo parceiro: {str(ex)}"
            )
            return _handle_error_response_on_relocation(
                remanejamento,
                Remanejamento.Etapas.EMISSAO,
                params.trechoclasse_origem_id,
                params.trechoclasse_destino_id,
                external_purchase_success,
                external_cancelation_success,
                params.reservation_code,
                info_msg,
                blocked_travel_on_purchase=True,
                poltronas_destino=poltronas_destino_bkp,
            )

        except RodoviariaBaseException as ex:
            info_msg = (
                f"{REMANEJAMENTO_LOG_TAG} Remanejamento de travel {params.travel_id}->{params.travel_destino_id} não"
                f" movida externamente, pela ocorrência de uma exception no serviço da rodoviária: {str(ex)}"
            )
            return _handle_error_response_on_relocation(
                remanejamento,
                Remanejamento.Etapas.EMISSAO,
                params.trechoclasse_origem_id,
                params.trechoclasse_destino_id,
                external_purchase_success,
                external_cancelation_success,
                params.reservation_code,
                info_msg,
                poltronas_destino=poltronas_destino_bkp,
            )

        except Exception as ex:
            capture_exception(ex)
            info_msg = (
                f"{REMANEJAMENTO_LOG_TAG} Remanejamento de travel {params.travel_id}->{params.travel_destino_id} não"
                f" movida externamente, pela ocorrência de uma exception: {str(ex)}"
            )
            return _handle_error_response_on_relocation(
                remanejamento,
                Remanejamento.Etapas.EMISSAO,
                params.trechoclasse_origem_id,
                params.trechoclasse_destino_id,
                external_purchase_success,
                external_cancelation_success,
                params.reservation_code,
                info_msg,
                poltronas_destino=poltronas_destino_bkp,
            )

        external_purchase_success = True

    passagens_confirmadas = Passagem.objects.filter(
        travel_internal_id=params.travel_destino_id, status=Passagem.Status.CONFIRMADA
    )

    if staff_rodoviaria_svc.is_empresa_integrada(params.company_origem_id, params.modelo_venda_origem):
        trechoclasse_origem = TrechoClasse.objects.filter(
            trechoclasse_internal_id=params.trechoclasse_origem_id, active=True
        ).first()
        if trechoclasse_origem and round((trechoclasse_origem.datetime_ida - now()).total_seconds() / 3600) <= 3:
            info_msg = (
                f"{REMANEJAMENTO_LOG_TAG} O remanejamento só é possível pelo menos 3h antes do embarque do trecho do"
                " grupo de origem"
            )
            for p in passagens_confirmadas:
                p.tags.add("cancelamento_pendente")
            return _handle_error_response_on_relocation(
                remanejamento,
                Remanejamento.Etapas.CANCELAMENTO,
                params.trechoclasse_origem_id,
                params.trechoclasse_destino_id,
                external_purchase_success,
                external_cancelation_success,
                params.reservation_code,
                info_msg,
            )

        try:
            reserva_svc.efetua_cancelamento(travel_id=params.travel_id)

        except PassengerNotRegistered:
            pass

        except RodoviariaBaseException as ex:
            info_msg = (
                f"{REMANEJAMENTO_LOG_TAG} Remanejamento de travel {params.travel_id}->{params.travel_destino_id} não"
                f" movida externamente, pela ocorrência de uma exception no serviço da rodoviária: {ex}"
            )
            for p in passagens_confirmadas:
                p.tags.add("cancelamento_pendente")
            return _handle_error_response_on_relocation(
                remanejamento,
                Remanejamento.Etapas.CANCELAMENTO,
                params.trechoclasse_origem_id,
                params.trechoclasse_destino_id,
                external_purchase_success,
                external_cancelation_success,
                params.reservation_code,
                info_msg,
            )

        except Exception as ex:
            capture_exception(ex)
            info_msg = (
                f"{REMANEJAMENTO_LOG_TAG} Remanejamento de travel {params.travel_id}->{params.travel_destino_id} não"
                f" movida externamente, pela ocorrência de uma exception: {str(ex)}"
            )
            for p in passagens_confirmadas:
                p.tags.add("cancelamento_pendente")
            return _handle_error_response_on_relocation(
                remanejamento,
                Remanejamento.Etapas.CANCELAMENTO,
                params.trechoclasse_origem_id,
                params.trechoclasse_destino_id,
                external_purchase_success,
                external_cancelation_success,
                params.reservation_code,
                info_msg,
            )

        external_cancelation_success = True

    remanejamento.status = Remanejamento.Status.COMPLETO
    remanejamento.save()

    buserlogger.info(
        "%s Remanejamento (id=%s, %s -> %s) efetuado com sucesso",
        REMANEJAMENTO_LOG_TAG,
        remanejamento.id,
        remanejamento.travel_origem_internal_id,
        remanejamento.travel_destino_internal_id,
    )

    return {
        "reservation_code": params.reservation_code,
        "external_purchase_success": external_purchase_success,
        "external_cancelation_success": external_cancelation_success,
        "external_error_reason": "",
        "is_trechoclasse_origem_integrado": staff_rodoviaria_svc.is_trechoclasse_integrado(
            params.trechoclasse_origem_id
        ),
        "is_trechoclasse_destino_integrado": staff_rodoviaria_svc.is_trechoclasse_integrado(
            params.trechoclasse_destino_id
        ),
        "internal_relocation_permission": True,
        "overbooking_on_purchase": False,
        "connection_error_on_purchase": False,
        "blocked_travel_on_purchase": False,
    }


def remanejamentos_pendentes_ou_completos_recentes(grupo_id):
    is_pendente = Q(status=Remanejamento.Status.PENDENTE)
    is_recente = Q(status=Remanejamento.Status.COMPLETO) & Q(updated_at__gt=now() - timedelta(minutes=5))
    remanejamentos = (
        Remanejamento.objects.filter(grupo_destino_internal_id=grupo_id)
        .filter(is_pendente | is_recente)
        .values(
            "travel_origem_internal_id",
            "travel_destino_internal_id",
            "grupo_destino_internal_id",
            "count_seats",
            "status",
        )
    )
    return {"remanejamentos": list(remanejamentos)}

from commons.celery_utils import DefaultQueueNames
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, TaskStatus
from rodoviaria.service.atualiza_operacao_utils import inicia_task_status, pre_inicia_task_status
from rodoviaria.service.exceptions import HibridoNotAllowedException
from rodoviaria.service.map_marketplace_cidades_svc import MapMarketplaceCidadesSVC

DEFAULT_QUEUE_NAME = DefaultQueueNames.DESCOBRIR_OPERACAO


def descobrir_operacao_proximos_dias(
    company: Company,
    next_days=30,
    shift_days=1,
    return_task_object=False,
):
    if company.modelo_venda == Company.ModeloVenda.HIBRIDO:
        raise HibridoNotAllowedException
    orchestrator = OrchestrateRodoviaria.from_company(company)
    queue_name = orchestrator.provider.queue_name or DEFAULT_QUEUE_NAME
    try:
        MapMarketplaceCidadesSVC(company.company_internal_id).execute()
    except NotImplementedError:
        pass

    pre_inicia_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company.id)
    task = orchestrator.descobrir_operacao_async(
        next_days=next_days,
        shift_days=shift_days,
        queue_name=queue_name,
        return_task_object=return_task_object,
    )
    inicia_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company.id)

    if return_task_object:
        return task

    task_info = {
        "task_id": task.id,
        "queue": queue_name,
        "mensagem": f"Descobre operacao da empresa {company.name} nos próximos {next_days} dias",
    }
    return task_info

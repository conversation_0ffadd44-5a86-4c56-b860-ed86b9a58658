import logging
from decimal import Decimal

from celery import shared_task
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import to_tz
from commons.django_utils import error_str
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.forms import ServicoForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import TrechoVendido
from rodoviaria.service.atualiza_operacao_utils import MAX_PRECO_TRECHO
from rodoviaria.service.exceptions import RodoviariaConnectionError, RodoviariaException

logger = logging.getLogger(__name__)


@shared_task(queue=DefaultQueueNames.BUSCAR_PRECO_TRECHO_VENDIDO, rate_limit=DefaultRateLimits.BUSCAR_VIAGENS_SEARCH)
@retry(
    exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens),
    min_delay=10,
)
def busca_preco_trecho_vendido(trecho_vendido_id):
    tv = TrechoVendido.objects.select_related("origem__cidade__cidade_internal", "destino", "rota__company").get(
        id=trecho_vendido_id
    )
    if tv.status_preco != TrechoVendido.StatusPreco.PENDENTE:
        return
    timezone_origem = _get_timezone_origem(tv)
    datetime_ida = _get_datetime_ida_mais_proximo(tv, timezone_origem)
    if not datetime_ida:
        tv.status_preco = TrechoVendido.StatusPreco.IMPOSSIVEL_BUSCAR
        tv.updated_at = timezone.now()
        tv.save()
        return

    api = OrchestrateRodoviaria.from_company(tv.rota.company)
    try:
        servicos_api: list[ServicoForm] = api.buscar_corridas(
            {
                "origem": tv.origem.id_external,
                "destino": tv.destino.id_external,
                "data": datetime_ida.strftime("%Y-%m-%d"),
            }
        )
    except RodoviariaException as ex:
        msg = f"[BUSCA PRECO TRECHO VENDIDO] Erro na busca do {trecho_vendido_id=}: {error_str(ex)}"
        logger.info(msg)
        return
    preco = None
    for servico in servicos_api.servicos:
        if (
            servico.classe.upper() == tv.classe.upper()
            and to_tz(servico.external_datetime_ida, timezone_origem) == datetime_ida
        ):
            preco = servico.preco
            break
    if preco:
        tv.status_preco = TrechoVendido.StatusPreco.OK
        tv.preco = preco
        tv.ativo = Decimal("0") < tv.preco < MAX_PRECO_TRECHO
    else:
        tv.status_preco = TrechoVendido.StatusPreco.NAO_ENCONTRADO
    tv.updated_at = timezone.now()
    tv.save()


def _get_timezone_origem(tv):
    timezone_origem = "America/Sao_Paulo"
    cidade_internal = tv.origem.cidade.cidade_internal
    if cidade_internal:
        timezone_origem = cidade_internal.timezone
    return timezone_origem


def _get_datetime_ida_mais_proximo(tv, timezone_origem):
    datetime_rotinas = tv.rotinatrechovendido_set.filter(datetime_ida_trecho_vendido__gt=timezone.now()).order_by(
        "datetime_ida_trecho_vendido"
    )
    rotina_trecho_vendido = datetime_rotinas.first()
    if not rotina_trecho_vendido:
        return None

    datetime_ida = to_tz(rotina_trecho_vendido.datetime_ida_trecho_vendido, timezone_origem)
    return datetime_ida

import logging
from collections import defaultdict
from datetime import date, datetime, timedelta
from functools import reduce
from random import randint

from beeline import traced

from commons.dateutils import midnight, to_tz, today_midnight
from rodoviaria.models.core import <PERSON><PERSON>, Rotina
from rodoviaria.service.auto_integra_operacao.classes_e_precos_por_rotas_e_rotinas_svc import (
    get_classes_trechos_vendidos_e_datetime_ida_por_rotas_e_rotinas,
)
from rodoviaria.service.auto_integra_operacao.duracao_checkpoint_linkado_svc import (
    get_duracao_e_timezone_primeiro_checkpoint_linkado_por_rota,
)

MAX_DIAS_FUTUROS_DATA_LIMITE = 150
logger = logging.getLogger("rodoviaria")
LOG_PREFIX = "[integracao_automatica_grupos_mktplace]"


def _log_info(msg, extra=None):
    logger.info("%s %s", LOG_PREFIX, msg, extra=extra)


def get_detalhes_rotinas_uma_rota(
    rodoviaria_rota_id: int,
    trechos_vendidos_ids_filter: list,
    datetimes_a_ignorar: list = None,
    start_date: date = None,
    end_date: date = None,
):
    detalhes_por_rota = get_detalhes_rotinas(
        [rodoviaria_rota_id],
        {rodoviaria_rota_id: trechos_vendidos_ids_filter},
        {rodoviaria_rota_id: datetimes_a_ignorar} if datetimes_a_ignorar else None,
        start_date,
        end_date,
    )
    if rodoviaria_rota_id in detalhes_por_rota:
        return detalhes_por_rota[rodoviaria_rota_id]
    else:
        return []


@traced("auto_integra_operacao.integracao_grupos_svc.get_detalhes_rotinas")
def get_detalhes_rotinas(
    rotas_ids: list[int],
    trechos_vendidos_ids_por_rota_id: dict[int, list],
    datetimes_a_ignorar_por_rota_id: dict[int, list] = None,
    start_date: date = None,
    end_date: date = None,
    extend_dates: bool = True,
):
    """
    Retorna um dict com todos os trechos vendidos, classes e rotinas de uma rota. Em um map por rodoviaria_rota_id

    `rotas_ids`: lista de ids do rodoviaria das rotas para buscar os detalhes

    `trechos_vendidos_ids_por_rota_id`: filtro de trechos. Apenas retorna trechos vendidos com esses IDs.
    Evita criar grupos com trechos não integrados da rota

    `datetimes_a_ignorar_por_rota_id`: filtro de datas. Não retorna na lista de rotinas as datas do filtro.
    Evita criar dois grupos com o mesmo horário

    Para buscar todas as informações das rotinas, são necessários alguns passos:
     1. Busca dados de timezone e horario de saida da rotina considerando o checkpoint linkado no buser_django,
     pelo metodo `_get_checkpoint_info_por_rota`
     2. Depois, pega o filtro de datas a ignorar e corrige os timezones
     3. Busca as datas limites da rota
     4. Busca as datas das rotinas que encontramos na API do parceiro.
     4.1. Busca também as classes e trechos vendidos por horario e dia da semana,
     ignora trechos vendidos sem tipo_assento mapeado
     5. Com base nas datas da API, calcula as proximas datas até a data limite
     6. Com a lista de datas, remove as datas conforme o filtro de inicio e fim e também removendo as datas a ignorar

    """

    if not start_date:
        start_date = today_midnight().date()
    elif isinstance(start_date, datetime):
        start_date = start_date.date()

    if end_date and isinstance(end_date, datetime):
        end_date = end_date.date()

    _log_info(
        (
            f"Buscando detalhes de rotinas para as rotas. {rotas_ids=}"
            f"apenas para os trechos vendidos: {trechos_vendidos_ids_por_rota_id}"
            f"exceto para as seguintes datas {datetimes_a_ignorar_por_rota_id=}"
        ),
        extra={"datetimes_a_ignorar_por_rota_id": datetimes_a_ignorar_por_rota_id},
    )

    info_checkpoints = _get_checkpoint_info_por_rota(rotas_ids)

    mapped_dates_to_remove = _map_datetimes_a_ignorar_por_hora(
        datetimes_a_ignorar_por_rota_id, info_checkpoints.first_timezone_linkado
    )

    map_data_limite_por_rota = _get_data_limite_rotas(rotas_ids)

    # Busca pra todas as rotas os trechos, classes e datas da API (filtro de data não se aplica ainda)
    rotinas_por_hora_dia_classes_por_rota = _get_datas_rotinas_com_trechos_da_api(
        rotas_ids,
        trechos_vendidos_ids_por_rota_id,
        info_checkpoints.duracao_ate_linkado,
        info_checkpoints.first_timezone_rota,
    )

    rotinas_por_rota_map = _get_map_datetime_ida_rotinas_por_rota(rotas_ids, start_date)

    filtered_rotinas_por_hora_dia_classes_por_rota = {}
    for rota_id in rotinas_por_hora_dia_classes_por_rota:
        end_date_rota = (today_midnight() + timedelta(days=MAX_DIAS_FUTUROS_DATA_LIMITE)).date()
        rotinas_por_hora_dia_classes = rotinas_por_hora_dia_classes_por_rota[rota_id]
        rotinas_por_hora_dia_classes = _add_datas_rotinas_sem_trechos_vendidos(
            rota_id,
            rotinas_por_rota_map[rota_id],
            rotinas_por_hora_dia_classes,
            info_checkpoints.first_timezone_linkado[rota_id],
            info_checkpoints.duracao_ate_linkado[rota_id],
        )
        rotinas_da_rota = rotinas_por_hora_dia_classes.values()
        if extend_dates:
            end_date_rota = end_date or map_data_limite_por_rota[rota_id]
            rotinas_da_rota = _calcular_proximas_datas_rotina(rota_id, rotinas_da_rota, end_date_rota)

        filtered_rotinas_da_rota = _filtra_datas_pelo_inicio_fim_e_datas_para_remover(
            rotinas_da_rota, start_date, end_date_rota, mapped_dates_to_remove, rota_id
        )

        filtered_rotinas_por_hora_dia_classes_por_rota[rota_id] = filtered_rotinas_da_rota

        _log_info(
            f"Fim do filtro das datas para a rota={rota_id}."
            f" len_datas_filtradas: {len(filtered_rotinas_da_rota)}."
            f" datas_filtradas: {filtered_rotinas_da_rota}"
        )

    return filtered_rotinas_por_hora_dia_classes_por_rota


def _get_map_datetime_ida_rotinas_por_rota(rotas_ids, start_date):
    """
    Keyword arguments:
    argument

    --`rotas_ids` ids das rotas

    --`start_data` data de inicio das rotinas


    Return: map com o id da rota e a lista de horarios de rotinas ativas


    {
        `rota_id`: [datetime(...), datetime(...)]
    }
    """

    rotinas = Rotina.objects.filter(rota_id__in=rotas_ids, ativo=True, datetime_ida__gt=start_date).values(
        "rota_id", "datetime_ida"
    )
    rotinas_por_rota_map = defaultdict(list)
    for rotina in rotinas:
        rotinas_por_rota_map[rotina["rota_id"]].append(rotina["datetime_ida"])
    return rotinas_por_rota_map


@traced("auto_integra_operacao.integracao_grupos_svc._filtra_datas_pelo_inicio_fim_e_datas_para_remover")
def _filtra_datas_pelo_inicio_fim_e_datas_para_remover(
    rotinas_por_hora_dia_classes, start_date_filter, end_date_filter, mapped_dates_to_remove_filter, rota_id
):
    """
    Filtra a lista de datas das rotinas pelo filtro de inicio e fim, e também pela lista de datas a serem removidas

    `rotinas_por_hora_dia_classes`: lista de rotinas. De onde será removido as datas

    `start_date_filter`: Filtro de início. Datas menores que essa serão removidas

    `end_date_filter`: Filtro de fim. Datas maiores que essa serão removidas

    `mapped_dates_to_remove_filter`: Lista de datas que devem removidas. Serve para evitar duplicação de grupos
    """
    _log_info(
        f"Iniciando filtro das datas para a rota={rota_id}."
        f"Filtrando para o período de {start_date_filter} até {end_date_filter}"
    )
    filtered_rotinas_por_hora_dia_classes = []
    for rotina in rotinas_por_hora_dia_classes:
        primeira_data = rotina["datas"][0]["data"]
        ultima_data = rotina["datas"][-1]["data"]
        if ultima_data < start_date_filter or primeira_data > end_date_filter:
            # se a ultima data for menor que o inicio do filtro, nenhuma data será maior
            # se a primeira data for maior que o fim do filtro, nenhuma data será menor
            rotina["datas"] = []
            continue

        _filtra_datas_pelo_inicio_e_fim(start_date_filter, end_date_filter, rotina)

        _remove_datas_vetadas(mapped_dates_to_remove_filter, rota_id, rotina)

        if len(rotina["datas"]) > 0:
            filtered_rotinas_por_hora_dia_classes.append(rotina)
    return filtered_rotinas_por_hora_dia_classes


@traced("auto_integra_operacao.integracao_grupos_svc._remove_datas_vetadas")
def _remove_datas_vetadas(mapped_dates_to_remove_filter, rota_id, rotina):
    """
    remove da lista de datas as que estão na lista "negra" de datas

    é usado para evitar repetição de grupos no mesmo horário
    """
    map_dates_to_remove_by_hour = mapped_dates_to_remove_filter.get(rota_id, {}).get(rotina["horario"])
    if map_dates_to_remove_by_hour:
        _log_info(
            f"Removendo datas da rotina das {rotina['horario']=} da rota {rota_id=}. {map_dates_to_remove_by_hour=}"
        )
        rotina["datas"] = [
            data_rotina for data_rotina in rotina["datas"] if data_rotina["data"] not in map_dates_to_remove_by_hour
        ]


@traced("auto_integra_operacao.integracao_grupos_svc._filtra_datas_pelo_inicio_e_fim")
def _filtra_datas_pelo_inicio_e_fim(start_date_filter, end_date_filter, rotina):
    """
    Remove da lista de datas as que forem menores que `start_date_filter` e maiores que `end_date_filter`
    """
    while rotina["datas"] and rotina["datas"][0]["data"] < start_date_filter:
        rotina["datas"].pop(0)["data"]

    while rotina["datas"] and rotina["datas"][-1]["data"] > end_date_filter:
        rotina["datas"].pop(-1)["data"]


def _get_checkpoint_info_por_rota(rotas_ids):
    return get_duracao_e_timezone_primeiro_checkpoint_linkado_por_rota(rotas_ids)


@traced("auto_integra_operacao.integracao_grupos_svc._get_data_limite_rotas")
def _get_data_limite_rotas(rotas_ids):
    """
    Busca data limite da rota, mas se a data ultrapassar 5 meses, usa 5 meses
    """
    limite_data_limite = (today_midnight() + timedelta(days=MAX_DIAS_FUTUROS_DATA_LIMITE)).date()

    data_limite_por_rota = {}
    for rota in Rota.objects.filter(id__in=rotas_ids).values("id", "data_limite"):
        if rota["data_limite"]:
            data_limite_por_rota[rota["id"]] = min(rota["data_limite"], limite_data_limite)
        else:
            data_limite_por_rota[rota["id"]] = limite_data_limite

    return data_limite_por_rota


@traced("auto_integra_operacao.integracao_grupos_svc._calcular_proximas_datas_rotina")
def _calcular_proximas_datas_rotina(rota_id, rotinas_por_hora_dia_classes, data_limite):
    """
    Utiliza as datas buscadas da API para calcular os próximos dias de uma rotina
    """
    _log_info(
        f"Iniciando o cálculo de próximas datas paras rotinas da rota={rota_id}."
        f" Calculando até {data_limite}."
        f" len_datas_antes_calculo: {len(rotinas_por_hora_dia_classes)}."
        f" datas_antes_calculo: {rotinas_por_hora_dia_classes}"
    )

    for rotina in rotinas_por_hora_dia_classes:
        ultima_data_api = rotina["datas"][-1]["data"]
        if len(rotina["datas"]) > 1:
            penultima_data_api = rotina["datas"][-2]["data"]
            diff_dias = timedelta(days=(ultima_data_api - penultima_data_api).days)
        else:
            diff_dias = timedelta(days=7)

        data = ultima_data_api + diff_dias
        while data <= data_limite:
            rotina["datas"].append({"data": data})
            data += diff_dias

        _log_info(
            f"Fim do cálculo de próximas datas paras rotinas da rota={rota_id}."
            f" len_datas_depois_calculo: {len(rotinas_por_hora_dia_classes)}."
            f" datas_depois_calculo: {rotinas_por_hora_dia_classes}"
        )
    return rotinas_por_hora_dia_classes


def _add_datas_rotinas_sem_trechos_vendidos(
    rota_id, rotinas_datetime_ida, rotinas_por_hora_dia_classes, timezone, duracao_ate_linkado
):
    """
    adiciona as datas das rotinas cadastradas no banco à lista de datas para cada dia_semana e horario

    retorna a lista de rotinas `rotinas_por_hora_dia_classes` com as datas adicionadas
    """

    _log_info(
        f"Adicionando datas de rotinas existentes no banco para rota={rota_id}."
        f" {rotinas_datetime_ida=}"
        f" {len(rotinas_por_hora_dia_classes)=}."
        f" {rotinas_por_hora_dia_classes=}"
        f" {timezone=}."
        f" {duracao_ate_linkado=}."
    )

    keys_rotinas_por_hora_dia = defaultdict(list)

    # Agrupar as chaves de rotinas (hora, dia_semana, classes) por apenas (hora, dia_semana)
    for rotina_key in rotinas_por_hora_dia_classes:
        keys_rotinas_por_hora_dia[(rotina_key[0], rotina_key[1])].append(rotina_key)

    for rotina_datetime_ida in rotinas_datetime_ida:
        datetime_ida = to_tz(rotina_datetime_ida, timezone) + duracao_ate_linkado
        dia_semana = datetime_ida.strftime("%a")
        horario = datetime_ida.strftime("%H:%M")
        data_para_add = datetime_ida.date()

        # Verificar se a data já foi adicionada para evitar duplicações
        data_ja_adicionada = False
        for rotina_key in keys_rotinas_por_hora_dia[(horario, dia_semana)]:
            rotina = rotinas_por_hora_dia_classes[rotina_key]
            datas_existentes = {x["data"] for x in rotina["datas"]}
            if data_para_add in datas_existentes:
                data_ja_adicionada = True
                break

        if not data_ja_adicionada:
            # Encontrar a chave com maior número de classes
            if keys_rotinas_por_hora_dia[(horario, dia_semana)]:
                max_len_classes_key = max(
                    keys_rotinas_por_hora_dia[(horario, dia_semana)],
                    key=lambda k: len(k[2].split(",")),
                )
                rotinas_por_hora_dia_classes[max_len_classes_key]["datas"].append(
                    {"data": data_para_add, "from_api": True}
                )

    # ordena novamente as datas
    for rotina_key in rotinas_por_hora_dia_classes:
        rotina = rotinas_por_hora_dia_classes[rotina_key]
        rotina["datas"] = sorted(rotina["datas"], key=lambda d: d["data"])

    return rotinas_por_hora_dia_classes


@traced("auto_integra_operacao.integracao_grupos_svc._map_datetimes_a_ignorar_por_hora")
def _map_datetimes_a_ignorar_por_hora(
    datetimes_a_ignorar_por_rota_id, timezone_primeiro_checkpoint_linkado_por_rota_id
):
    """
    Mapeia por horário da rotina os datetimes de grupos que não precisarão ser criados. Evita criar grupos
    no mesmo horário

    Para comparação correta, é preciso converter as datas desse filtro para o primeiro timezone linkado

    - `datetimes_a_ignorar_por_rota_id`: map de datetimes em UTC vindo do buser_django
    - `timezone_primeiro_checkpoint_linkado_por_rota_id`: Os horários do filtro datetimes_a_ignorar virão do
    buser_django em UTC, mas considerando que o
    horário de saída é o primeiro checkpoint linkado
    """
    if not datetimes_a_ignorar_por_rota_id:
        return {}
    map_dates_por_rota = {}
    for rota_id in datetimes_a_ignorar_por_rota_id:
        map_dates = defaultdict(list)
        for data_a_ignorar in datetimes_a_ignorar_por_rota_id[rota_id]:
            tz = timezone_primeiro_checkpoint_linkado_por_rota_id[rota_id]
            data_a_ignorar_tz = to_tz(data_a_ignorar, tz)

            hora = data_a_ignorar_tz.strftime("%H:%M")
            map_dates[hora].append(data_a_ignorar_tz.date())

        map_dates_por_rota[rota_id] = map_dates
    return map_dates_por_rota


@traced("auto_integra_operacao.integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api")
def _get_datas_rotinas_com_trechos_da_api(
    rodoviaria_rota_ids,
    trechos_vendidos_ids_por_rota_id,
    duracao_ate_linkado_por_rota_id,
    first_timezone_rota_por_rota_id,
):
    """
    Para cada rota, busca todos os horários, datas, trechos vendidos, classes e preços

    `rodoviaria_rota_ids`: Lista de rotas para buscar rotinas

    `trechos_vendidos_ids_por_rota_id`: Filtro de trechos vendidos, para apenas trazer trechos integrados

    `duracao_ate_linkado_por_rota_id`: usado para calcular a duracao do primeiro checkpoint da rota até o primeiro
    checkpoint criado no buser_django (linkado)

    `first_timezone_rota_por_rota_id`: timezone do primeiro checkpoint da rota, usado para calcular o horário de saida
    correto da rotina.
    O horário da rotina é baseado no timezone do checkpoint de IDX=0
    """
    (
        classes_e_precos_por_rota_e_rotina,
        dt_ida_por_rota_e_rotina,
    ) = get_classes_trechos_vendidos_e_datetime_ida_por_rotas_e_rotinas(
        rodoviaria_rota_ids, trechos_vendidos_ids_por_rota_id
    )

    rotinas_agg_por_rota = {}
    for rota_id in classes_e_precos_por_rota_e_rotina:
        rotinas_agg = defaultdict(list)
        for rotina_id in classes_e_precos_por_rota_e_rotina[rota_id]:
            dt_ida_shift_tz = _calcular_datetime_ida_timezone_para_primeiro_chekpoint_integrado(
                dt_ida_por_rota_e_rotina[rota_id][rotina_id],
                duracao_ate_linkado_por_rota_id[rota_id],
                first_timezone_rota_por_rota_id[rota_id],
            )

            dia_semana = dt_ida_shift_tz.strftime("%a")
            data = midnight(dt_ida_shift_tz)
            hora = dt_ida_shift_tz.strftime("%H:%M")

            classes_e_precos = classes_e_precos_por_rota_e_rotina[rota_id].get(rotina_id)

            if classes_e_precos:
                classes = classes_e_precos["classes"]
                trechos = classes_e_precos["trechos_classes"]

                _add_rotina(rotinas_agg, rota_id, hora, dia_semana, rotina_id, data, classes, trechos)

        _log_info(f"Rotinas obtidas para a rota {rota_id=}. {rotinas_agg=}")
        rotinas_agg_por_rota[rota_id] = rotinas_agg

    return rotinas_agg_por_rota


@traced("auto_integra_operacao.integracao_grupos_svc._calcular_datetime_ida_timezone_para_primeiro_chekpoint_integrado")
def _calcular_datetime_ida_timezone_para_primeiro_chekpoint_integrado(
    datetime_ida, duracao_ate_primeiro_checkpoint_linkado, timezone_primeiro_checkpoint
):
    """
    Para pegar a data da rotina, pega o datetime_ida da rotina e adiciona a duracao dos checkpoints até o primeiro
    integrado.

    Assim, caso uma empresa tenha o itinerario A->B->C e nós só vendemos a partir do B, nosso grupo vai ter o horario
    de saida de B
    """
    dt_ida_shift = datetime_ida + duracao_ate_primeiro_checkpoint_linkado
    dt_ida_shift_tz = to_tz(dt_ida_shift, timezone_primeiro_checkpoint)
    return dt_ida_shift_tz


@traced("auto_integra_operacao.integracao_grupos_svc._add_rotina")
def _add_rotina(rotinas_agg, rota_id, hora, dia_semana, rotina_id, data, classes, trechos):
    _log_info(f"Adicionando rotina{rota_id=}{hora=}{dia_semana=}{rotina_id=}{data=}{classes=}{trechos=}")
    key_classes = _gerar_hash_key_classes(classes)
    map_key = (hora, dia_semana, key_classes)
    if not rotinas_agg.get(map_key):
        _add_id_classes(classes)
        rotinas_agg[map_key] = {
            "horario": hora,
            "dia_semana": dia_semana,
            "datas": [],
            "id": rotina_id,
            "classes": classes,
            "trechos_vendidos": trechos,
        }
    else:
        rotinas_agg[map_key]["trechos_vendidos"].update(trechos)
    rotinas_agg[map_key]["datas"].append({"data": data.date(), "from_api": True})


def _gerar_hash_key_classes(classes):
    """
    Gera uma chave de classes para separar as rotinas por classes diferentes.
    """
    tipos_assentos = (x["tipo_assento"] for x in classes)
    tipos_assentos_concatenated = reduce(lambda x, y: x + "," + y, tipos_assentos, "")
    return tipos_assentos_concatenated


def _add_id_classes(classes):
    """
    Gera um ID aleatorio nas classes, para facilitar a criação de grupos no buser_django
    """
    for classe in classes:
        classe["id"] = f"new-{randint(0,9999999)}"  # noqa: S311

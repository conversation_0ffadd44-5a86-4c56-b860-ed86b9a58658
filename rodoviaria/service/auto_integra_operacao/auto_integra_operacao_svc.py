from collections import defaultdict

from beeline import traced
from django.core.exceptions import ValidationError
from django.db.models import F

from core.models_grupo import GrupoClasse as GrupoClasseBdjango
from core.models_rota import TrechoVendido as TrechoVendidoBdjango
from rodoviaria.models import Company, Rota
from rodoviaria.service import (
    atualiza_operacao_empresa_svc,
    link_rotas_svc,
    link_trechos_vendidos_svc,
    rota_svc,
    rotina_svc,
)
from rodoviaria.service.auto_integra_operacao import integracao_grupos_svc
from rodoviaria.service.exceptions import RodoviariaException


@traced("auto_integra_operacao.verificar_elegibilidade_auto_integra_operacao")
def verificar_elegibilidade_auto_integra_operacao(company_internal_id) -> bool:
    """
    Retorna se empresa pode rodar ou não integracao automatica.
    """
    company = Company.objects.filter(
        company_internal_id=company_internal_id, modelo_venda=Company.ModeloVenda.MARKETPLACE
    ).first()
    empresa_valida = bool(company) and company.has_feature(Company.Feature.AUTO_INTEGRA_OPERACAO)
    if not empresa_valida:
        raise RodoviariaException("Empresa não possui a feature flag necessária.")

    has_crons_running = atualiza_operacao_empresa_svc.existe_atualizacao_em_execucao(company_internal_id)
    if has_crons_running:
        raise RodoviariaException("Existe uma tarefa em execução para essa empresa. Aguarde o término.")
    qtd_rotas = atualiza_operacao_empresa_svc.qtd_rotas_por_empresa(company)
    if qtd_rotas < (company.auto_integra_rotas_min or 0):
        raise RodoviariaException(
            f"A empresa de {company_internal_id=} possui {qtd_rotas}"
            f" rotas nos próximos {company.margem_dias_busca_operacao} dias, "
            f"mas eram esperadas {company.auto_integra_rotas_min} rotas."
        )

    return True


def get_min_rotas_integracao(company_internal_id: int, modelo_venda: Company.ModeloVenda) -> int:
    """
    Retorna quantidade mínima de rotas necessárias para executar integração.
    Esse valor é definido por empresa.
    """
    try:
        qtd_rotas = (
            Company.objects.get(
                company_internal_id=company_internal_id, modelo_venda=modelo_venda
            ).auto_integra_rotas_min
            or 0
        )
        return qtd_rotas
    except Company.DoesNotExist:
        raise RodoviariaException(f"Company_id {company_internal_id} não existe.") from None


def set_min_rotas_integracao(company_internal_id, qtd_rotas) -> int:
    """
    Atualiza quantidade mínima de rotas necessárias para executar integração.
    """
    try:
        company = Company.objects.get(company_internal_id=company_internal_id)
        company.auto_integra_rotas_min = qtd_rotas
        company.save()
        return qtd_rotas
    except Company.DoesNotExist:
        raise RodoviariaException(f"Company_id {company_internal_id} não existe.") from None


@traced("auto_integra_operacao.get_empresas_integracao_automatica_rodar_hoje")
def get_empresas_integracao_automatica_rodar_hoje():
    """
    Retorna uma lista de company_internal_id usando com base o `trigger_time()` para a integracao automatica.

    Usado no buser_django para: todos os crons da integração automatica
    """
    return atualiza_operacao_empresa_svc.get_empresas_integracao_automatica_rodar_hoje()


@traced("auto_integra_operacao.get_empresas_integracao_automatica_agora")
def get_empresas_integracao_automatica_agora():
    """
    Retorna uma lista de company_internal_id usando com base o `trigger_time()` para a integracao automatica.

    Usado no buser_django para: todos os crons da integração automatica
    """
    return atualiza_operacao_empresa_svc.get_empresas_integracao_automatica_agora()


@traced("auto_integra_operacao.auto_integra_operacao_svc.get_ids_rotas_integradas_inativas")
def get_ids_rotas_integradas_inativas(company_internal_ids):
    """
    Busca uma lista de rota_internal_id que estão integradas (possuem id_internal) e inativas (ativo=False)

    Usado no buser_django para: fechamento grupos divergentes
    """
    return rota_svc.get_ids_rotas_integradas_inativas(company_internal_ids)


@traced("auto_integra_operacao.get_rotinas_inativas_por_rota_integrada_ativa")
def get_rotinas_inativas_por_rota_integrada_ativa(company_internal_ids):
    """
    Busca um map por rota_internal_id com uma lista de datetimes de rotinas.

    Os datetimes das rotinas são com base na saída do grupo do buser_django, referente ao primeiro checkpoint integrado

    Ex:
        Rota ID 12, itinerario: A > B > C
            A: saida: 01/01/2000 10:00 (não integrado no buser_django)
            B: saida: 01/01/2000 12:00 (integrado no buser_django)
            C: saida: 01/01/2000 14:00 (integrado no buser_django)

    O retorno será o datetime de saida a partir do B, pois é de onde a viagem sai para o buser_django

    Usado no buser_django para: fechamento grupos divergentes

    Returns:
        dict: No formato:
            `{ rota_id: [01/01/2000 12:00] }`
    """
    return rotina_svc.get_rotinas_inativas_por_rota_integrada_ativa(company_internal_ids)


@traced("auto_integra_operacao.auto_integra_operacao_svc.get_ids_rotas_ativas_empresa")
def get_ids_rotas_ativas_empresa(company_internal_id, modelo_venda):
    """
    Busca por rota_internal_id, os rodoviaria_rota_ids associados

    Apenas rotas integradas (possuem id_internal), ativas (ativo=False) e com data limite

    Usado no buser_django para: criacao grupos

    Returns:
        dict: No formato:
            `{rota_internal_id: [rodoviaria_rotas_id, rodoviaria_rotas_id]}`
    """
    return rota_svc.get_ids_rotas_ativas_empresa(company_internal_id, modelo_venda)


@traced("auto_integra_operacao.auto_integra_operacao_svc.get_detalhes_rotinas_por_rota")
def get_detalhes_rotinas_por_rota(
    rodoviaria_rota_ids, trechos_vendidos_ids_por_rota_id, datetimes_a_ignorar_por_rota_id, extend_dates
):
    """
    Busca informações de trechos vendidos, classes e datas para cada hora e dia da semana das rotinas de rotas buscadas.

    Usado no buser_django para: criacao grupos

    Args:
        - rodoviaria_rota_ids (list): Lista de rotas para buscar os detalhes.
        - trechos_vendidos_ids_por_rota_id (dict): Filtro de trechos. Apenas retorna trechos vendidos com esses IDs.
            Evita criar grupos com trechos não integrados da rota.
        - datetimes_a_ignorar_por_rota_id (dict): Filtro de datas. Não retorna na lista de rotinas as datas do filtro.
            Evita criar dois grupos com o mesmo horário.

    Returns:
        dict: Um dicionário no formato:
            ```
            {
                'rota_id' (str):
                    [{
                        'hora' (str): "08:00",
                        'dia_semana' (str): "ter",
                        'datas' (list): [{'data' (str): "2000-01-01", 'from_api' (bool): True}],
                        'classes' (list): [
                            {
                                'tipo_assento' (str): "executivo",
                                'max_capacity' (int): 44
                            }
                        ],
                        'trechos_classe' (dict): {
                            'trecho_vendido_internal_id' (str): {
                                'tipo_assento' (decimal): 119,90
                            }
                        }
                    }]
            }
            ```
    """

    if not trechos_vendidos_ids_por_rota_id and not datetimes_a_ignorar_por_rota_id:
        # Workaround to fetch "trechos vendidos" and "datetimes_a_ignorar"
        # Passing them via parameters caused issues due to a large number of routes.
        # Since "rodoviaria" is being deprecated, this workaround will remain until the end of the project.

        rotas = Rota.objects.filter(id__in=rodoviaria_rota_ids).only("id", "company_id", "company__company_internal_id")
        company_ids = set(rotas.values_list("company_id", flat=True))
        if len(company_ids) != 1:
            raise ValidationError("Detalhes das rotinas devem ser buscadas para rotas de apenas uma empresa.")

        company_id_internal = rotas.first().company.company_internal_id
        map_rotas = {rota.id_internal: rota.id for rota in rotas.iterator()}

        trechos = TrechoVendidoBdjango.objects.filter(rota_id__in=map_rotas.keys()).values("rota_id", "id")
        trechos_vendidos_ids_por_rota_id = defaultdict(list)
        for trecho in trechos:
            rota_internal_id = trecho["rota_id"]
            rodoviaria_rota_id = map_rotas[rota_internal_id]
            trechos_vendidos_ids_por_rota_id[rodoviaria_rota_id].append(trecho["id"])

        datas = (
            GrupoClasseBdjango.objects.filter(
                grupo__rota_id__in=map_rotas.keys(),
                grupo__modelo_venda="marketplace",
                grupo__status__in=("travel_confirmed", "pending"),
                grupo__company_id=company_id_internal,
            )
            .exclude(closed=True, closed_reason__contains="[auto_close_divergent_groups_mktplace]")
            .values(rota_id=F("grupo__rota_id"), data=F("grupo__datetime_ida"))
        )
        datetimes_a_ignorar_por_rota_id = defaultdict(list)
        for d in datas.iterator():
            rota_internal_id = d["rota_id"]
            rodoviaria_rota_id = map_rotas[rota_internal_id]
            datetimes_a_ignorar_por_rota_id[rodoviaria_rota_id].append(d["data"])

        # TODO: Remover, apenas mantendo compatibilidade por enquanto
        trechos_vendidos_ids_por_rota_id = dict(trechos_vendidos_ids_por_rota_id)
        datetimes_a_ignorar_por_rota_id = dict(datetimes_a_ignorar_por_rota_id)

    return integracao_grupos_svc.get_detalhes_rotinas(
        rodoviaria_rota_ids,
        trechos_vendidos_ids_por_rota_id,
        datetimes_a_ignorar_por_rota_id,
        extend_dates=extend_dates,
    )


@traced("auto_integra_operacao.auto_integra_operacao_svc.pos_salvar_rota")
def pos_salvar_rota(rota_internal_id, rodoviaria_rota_id, trechos_vendidos):
    """
    Salva no rodoviaria os ids da rota e trechos vendidos criados no buser_django.
    Efetivamente "integra" as rotas e trechos.

    Atualiza os campos `id_internal` dentro de `rodoviaria_rota`, e `rodoviaria_trecho_vendido`

    Recria/Atualiza os checkpoints da rota

    Usado no buser_django para: integracao rotas & no painel de rotas
    """
    link_trechos_vendidos_svc.atualizar_id_internal_dict_trechos_vendidos(trechos_vendidos)
    link_rotas_svc.atualizar_id_internal_rota_por_rodoviaria_id(rota_internal_id, rodoviaria_rota_id)
    rota_svc.atualizar_checkpoints_rota(rodoviaria_rota_id)


@traced("auto_integra_operacao.auto_integra_operacao_svc.get_rotas_ativas_para_reintegrar")
def get_rotas_ativas_para_reintegrar(company_internal_ids):
    """
    Retorna informações necessárias para recriar todas as rotas já integradas mas com atualizações de trecho ou
    classe das empresas do filtro no buser_django:
    - Itinerário (os checkpoints da rota)
    - Trechos vendidos
    - UFs intermediários

    Usado no buser_django para: integracao rotas
    """
    return rota_svc.get_rotas_ativas_para_reintegrar(company_internal_ids)


@traced("auto_integra_operacao.auto_integra_operacao_svc.get_rotas_novas_empresas_para_integrar")
def get_rotas_novas_empresas_para_integrar(company_internal_ids):
    """
    Retorna informações necessárias para criar todas as rotas novas das empresas do filtro no buser_django:
    - Itinerário (os checkpoints da rota)
    - Trechos vendidos
    - UFs intermediários

    Usado no buser_django para: integracao rotas
    """
    return rota_svc.get_rotas_novas_empresas_para_integrar(company_internal_ids)


@traced("auto_integra_operacao.auto_integra_operacao_svc.get_detalhes_rotinas_uma_rota")
def get_detalhes_rotinas_uma_rota(
    rodoviaria_rota_id, trechos_vendidos_ids_filter, datetimes_a_ignorar, start_date=None, end_date=None
):
    """
    Busca informações de trechos vendidos, classes e datas para cada hora e dia da semana das rotinas de uma rota.

    Usado no buser_django para: painel de rotas

    Args:
        - `rodoviaria_rota_id` (int): rota para buscar os detalhes
        - `trechos_vendidos_ids_filter` (list): Filtro de trechos. Apenas retorna trechos vendidos com esses IDs.
            Evita criar grupos com trechos não integrados da rota
        - `datetimes_a_ignorar` (list): Filtro de datas. Não retorna na lista de rotinas as datas do filtro.
            Evita criar dois grupos com o mesmo horário
        - `start_date` (date): Filtro de datas. Apenas traz datas a partir desta
        - `end_date` (date): Filtro de datas. Apenas traz datas até esta


    Returns:
        dict: Um dicionário no formato:
            ```
            [{
                'hora' (str): "08:00",
                'dia_semana' (str): "ter",
                'datas' (list): [{'data' (str): "2000-01-01", 'from_api' (bool): True}],
                'classes' (list): [
                    {
                        'tipo_assento' (str): "executivo",
                        'max_capacity' (int): 44
                    }
                ],
                'trechos_classe' (dict): {
                    'trecho_vendido_internal_id' (str): {
                        'tipo_assento' (decimal): 119,90
                    }
                }
            }]
            ```
    """
    return integracao_grupos_svc.get_detalhes_rotinas_uma_rota(
        rodoviaria_rota_id,
        trechos_vendidos_ids_filter,
        datetimes_a_ignorar,
        start_date,
        end_date,
    )


@traced("auto_integra_operacao.auto_integra_operacao_svc.get_detalhes_para_integrar_uma_rota")
def get_detalhes_para_integrar_uma_rota(rodoviaria_rota_id):
    """
    Usado no buser_django para: painel de rotas
    """
    return rota_svc.get_detalhes_para_integrar_uma_rota(rodoviaria_rota_id)


@traced("auto_integra_operacao.auto_integra_operacao_svc.get_rotas_novas_para_integrar")
def get_rotas_novas_para_integrar(company_internal_ids):
    """
    Retorna informações necessárias para criar uma rota no buser_django:
    - Itinerário (os checkpoints da rota)
    - Trechos vendidos
    - UFs intermediários

    Usado no buser_django para: integracao rotas
    """
    return rota_svc.get_rotas_novas_empresas_para_integrar(company_internal_ids)

import logging
from collections import defaultdict
from datetime import timedelta
from typing import Named<PERSON>uple

from beeline import traced
from django.db.models import F

from rodoviaria.models.core import Checkpoint

buserlogger = logging.getLogger("rodoviaria")


class CheckpointInfo(NamedTuple):
    first_timezone_linkado: dict[int, str]
    first_timezone_rota: dict[int, str]
    duracao_ate_linkado: dict[int, timedelta]


def get_duracao_checkpoint_ate_primeiro_linkado_por_rota(rotas_ids):
    return get_duracao_e_timezone_primeiro_checkpoint_linkado_por_rota(rotas_ids).duracao_ate_linkado


@traced("duracao_checkpoint_linkado_svc.get_duracao_e_timezone_primeiro_checkpoint_linkado_por_rota")
def get_duracao_e_timezone_primeiro_checkpoint_linkado_por_rota(rotas_ids):
    """
    Retorna 3 informações dos checkpoints por rota:
    - `first_timezone_rota`: Timezone do primeiro checkpoint.
        No caso, o timezone cadastrado para o checkpoint de IDX 0.
    - `first_timezone_linkado`: Timezone do primeiro checkpoint linkado.
        Ou seja, timezone do inicio da rota no buser_django.
    - `duracao_ate_linkado`: Duração da viagem do primeiro checkpoint da rota até o primeiro checkpoint da rota no
    buser_django.
        Será usado pra calcular o início da rotina no buser_django a partir do horario da rotina real.
    """
    chks_por_rota = _get_checkpoints_ordenados_e_mapeados_por_rota_id(rotas_ids)

    first_timezone_linkado = {}
    first_timezone_rota = {}
    duracao_ate_linkado = {}

    for rota_id in chks_por_rota:
        checkpoints_rota = chks_por_rota[rota_id]
        for c in checkpoints_rota:
            if rota_id not in duracao_ate_linkado:
                duracao_ate_linkado[rota_id] = timedelta(seconds=0)

            # Pega o timezone do primeiro local da rota
            # O datetime_ida da rotina é salvado no banco o timezone do primeiro checkpoint em consideração (e se não
            # estiver linkado, ele usa o de SP)
            if c["idx"] == 0:
                first_timezone_rota[rota_id] = c["timezone"] or "America/Sao_Paulo"

            # Calcula duracao até o primeiro local linkado
            # Usa isso pra calcular o início da rotina, pq o grupo no staff começa a partir do primeiro local linkado
            duracao_ate_linkado[rota_id] += c["duracao"] + c["tempo_embarque"]

            if c["local_linkado"]:
                first_timezone_linkado[rota_id] = c["timezone"]
                break

    buserlogger.info(
        "Timezones e Shift do inicio das rotas."
        " first_timezone_linkado=%s first_timezone_rota=%s duracao_ate_linkado=%s",
        first_timezone_linkado,
        first_timezone_rota,
        duracao_ate_linkado,
    )
    return CheckpointInfo(
        first_timezone_linkado=first_timezone_linkado,
        first_timezone_rota=first_timezone_rota,
        duracao_ate_linkado=duracao_ate_linkado,
    )


def _get_checkpoints_ordenados_e_mapeados_por_rota_id(rotas_ids):
    checkpoints_db = _get_query_set_checkpoints_ordenados(rotas_ids)
    chks_por_rota = defaultdict(list)

    for checkpoint in checkpoints_db:
        chks_por_rota[checkpoint["rota_id"]].append(checkpoint)
    return chks_por_rota


def _get_query_set_checkpoints_ordenados(rotas_ids):
    checkpoints_db = (
        Checkpoint.objects.filter(
            rota_id__in=rotas_ids,
        )
        .order_by("idx")
        .values(
            "rota_id",
            "idx",
            "duracao",
            "tempo_embarque",
        )
        .annotate(
            local_linkado=F("local__local_embarque_internal_id"),
            timezone=F("local__cidade__cidade_internal__timezone"),
        )
    )
    return checkpoints_db

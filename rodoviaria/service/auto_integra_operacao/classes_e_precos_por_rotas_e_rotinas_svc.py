import logging
from collections import defaultdict
from datetime import timedelta

from beeline import traced

from commons.dateutils import today_midnight
from rodoviaria.models.core import RotinaTrechoVendido
from rodoviaria.service import classes_e_precos_svc

logger = logging.getLogger("rodoviaria")
LOG_PREFIX = "[integracao_automatica_grupos_mktplace]"


def _log_info(msg):
    logger.info("%s %s", LOG_PREFIX, msg)


@traced("auto_integra_operacao.get_classes_trechos_vendidos_e_datetime_ida_por_rotas_e_rotinas")
def get_classes_trechos_vendidos_e_datetime_ida_por_rotas_e_rotinas(rotas_ids, ids_internal_trechos_vendidos_por_rota):
    """
    Retorna por rota_id e rotina_id, as classes (tipo_assento), trechos vendidos e a data de saida das rotinas
    É separado entre 2 maps, um com as classes e trechos vendidos e outro map com as datas

    Exemplos de retorno:

    ### trechos_por_rota_e_rotina
    ```
    {
        `{{rota_id}}`:
        {
            `{{rotina_id}}`: {
                classes:[
                    {
                        tipo_assento: "executivo",
                        max_capacity: 44
                    }
                ],
                trechos_classe: {
                    `{{trecho_vendido_internal_id}}`: {
                        `{{tipo_assento}}`: 119,90
                        }
                    }
                }
            }
        }
    }
    ```

    ### dt_ida_por_rota_e_rotina

    ```
    {
        `{{rota_id}}`:
        {
            `{{rotina_id}}`: "2000-01-01 08:00"
        }
    }
    ```
    """
    rotinas_trechos_vendidos = _get_ordered_query_set_rotina_trecho_vendido_ativo_integrado(
        rotas_ids, ids_internal_trechos_vendidos_por_rota
    )

    trechos_por_rota_e_rotina, dt_ida_por_rota_e_rotina = _get_trechos_vendidos_e_datetime_ida_por_rotina_e_rota(
        ids_internal_trechos_vendidos_por_rota, rotinas_trechos_vendidos
    )

    trechos_por_rota_e_rotina = _merge_trechos_similares_e_converte_para_dict(trechos_por_rota_e_rotina)

    return trechos_por_rota_e_rotina, dt_ida_por_rota_e_rotina


@traced("auto_integra_operacao._get_ordered_query_set_rotina_trecho_vendido_ativo_integrado")
def _get_ordered_query_set_rotina_trecho_vendido_ativo_integrado(rotas_ids, ids_internal_trechos_vendidos_por_rota):
    _log_info(
        f"Iniciando busca de trechos vendidos integrados. {rotas_ids=}. {ids_internal_trechos_vendidos_por_rota=}"
    )
    filtro_ids_trechovendido = _get_all_ids_internal_trecho_vendido(ids_internal_trechos_vendidos_por_rota)

    rotinas_trechos_vendidos = (
        RotinaTrechoVendido.objects.select_related(
            "trecho_vendido", "trecho_vendido__tipo_assento", "rotina", "rotina__rota"
        )
        .filter(
            rotina__rota_id__in=rotas_ids,
            rotina__ativo=True,
            rotina__datetime_ida__gte=today_midnight() - timedelta(days=7),
            trecho_vendido__id_internal__in=filtro_ids_trechovendido,
            trecho_vendido__ativo=True,
            trecho_vendido__origem__local_embarque_internal_id__isnull=False,
            trecho_vendido__destino__local_embarque_internal_id__isnull=False,
            trecho_vendido__tipo_assento__tipo_assento_buser_preferencial__isnull=False,
        )
        # ordena por datetime_ida para facilitar o filtro de periodo (dentro de get_detalhes_rotinas)
        .order_by("datetime_ida_trecho_vendido", "trecho_vendido__classe")
    )

    return rotinas_trechos_vendidos


@traced("auto_integra_operacao._get_trechos_vendidos_e_datetime_ida_por_rotina_e_rota")
def _get_trechos_vendidos_e_datetime_ida_por_rotina_e_rota(
    ids_internal_trechos_vendidos_por_rota, rotinas_trechos_vendidos
):
    trechos_por_rota_e_rotina = defaultdict(dict)
    dt_ida_por_rota_e_rotina = defaultdict(dict)
    log_len = 0
    for rtv in rotinas_trechos_vendidos:
        if rtv.trecho_vendido.id_internal in ids_internal_trechos_vendidos_por_rota.get(rtv.rotina.rota_id, []):
            log_len += 1
            if rtv.rotina_id not in trechos_por_rota_e_rotina[rtv.rotina.rota_id]:
                trechos_por_rota_e_rotina[rtv.rotina.rota_id][rtv.rotina_id] = []

            trechos_por_rota_e_rotina[rtv.rotina.rota_id][rtv.rotina_id].append(rtv.trecho_vendido)

            if rtv.rotina_id not in dt_ida_por_rota_e_rotina[rtv.rotina.rota_id]:
                dt_ida_por_rota_e_rotina[rtv.rotina.rota_id][rtv.rotina_id] = rtv.rotina.datetime_ida

    _log_info(f"Trechos da API obtidos com sucesso. Quantidade de trechos buscados={log_len}")
    _log_info(f"Busca de data saida das rotinas bem sucedida. {dt_ida_por_rota_e_rotina=}")
    return trechos_por_rota_e_rotina, dt_ida_por_rota_e_rotina


@traced("auto_integra_operacao._get_all_ids_internal_trecho_vendido")
def _get_all_ids_internal_trecho_vendido(ids_internal_trechos_vendidos_por_rota):
    filtro_tcs = []
    for id_rota in ids_internal_trechos_vendidos_por_rota:
        for y in ids_internal_trechos_vendidos_por_rota[id_rota]:
            filtro_tcs.append(y)
    return filtro_tcs


@traced("auto_integra_operacao._merge_trechos_similares_e_converte_para_dict")
def _merge_trechos_similares_e_converte_para_dict(trechos_por_rota_e_rotina):
    _log_info("Iniciando merge de trechos similares e map de classes.")
    for rota_id in trechos_por_rota_e_rotina:
        trechos_rotina = trechos_por_rota_e_rotina[rota_id]
        for rotina_id in trechos_rotina:
            trechos_vendidos_merged = classes_e_precos_svc.merge_trechos_similares(trechos_rotina[rotina_id])
            trechos_rotina[rotina_id] = classes_e_precos_svc.to_dict_classes_e_trechos(trechos_vendidos_merged)

    _log_info(f"Merge de trechos similares e map de classes bem sucedido. {trechos_por_rota_e_rotina=}")
    return trechos_por_rota_e_rotina

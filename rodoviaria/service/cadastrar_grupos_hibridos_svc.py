import json
import logging
from collections import defaultdict
from datetime import date, datetime, timedelta
from typing import Optional

from celery import group, shared_task
from celery.utils.log import get_task_logger
from django.db.models import F, Q
from django.utils import timezone
from ninja import Schema

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from commons.dateutils import to_default_tz
from commons.taggit_utils import Tags
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.vexado.utils import duracao_to_timedelta, timedelta_to_duracao
from rodoviaria.data.transbrasil import MAP_CLASSES_VEXADO
from rodoviaria.models.core import Company, Grupo, GrupoClasse, Integracao, TrechoClasse, TrechoClasseError
from rodoviaria.models.vexado import Veiculo, VexadoGrupoClasse, VexadoRota
from rodoviaria.service import veiculos_svc
from rodoviaria.views_schemas import GrupoParams

task_logger = get_task_logger(__name__)

DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO
logger = logging.getLogger("rodoviaria")


class CadastrarGrupoTaskParams(Schema):
    data_partida: date
    hora_saida: str
    id_rota: int
    id_veiculo: int
    classe_primeiro_andar: str
    classe_segundo_andar: Optional[str]

    @property
    def data_partida_str(self):
        return self.data_partida.strftime("%Y-%m-%dTT03:00:00.000Z")


class CadastrarGrupoSVC:
    def __init__(self, company_id, assincrono=True):
        self.company = Company.objects.get(company_internal_id=company_id, modelo_venda=DEFAULT_MODELO_VENDA)
        self.tasks = []
        self.grupos_to_create = []
        self.grupos_to_update = []
        self.grupos_classe_to_create = []
        self.vexado_grupos_classe_to_create = []
        self.assincrono = assincrono
        self.vexado_grupos_classe_ja_criados_map = {}
        self.grupos_classe_existentes_map = {}
        self.grupos_existentes = []

    def cadastrar_grupos(self, grupos: list[GrupoParams], rota_internal_id: int = None, rota_external_id: int = None):
        veiculos_linkados_map = self._get_veiculos_linkados_map(grupos)

        self._get_grupos_classe_ja_criados(grupos)
        self._get_grupos_existentes(grupos)
        self._get_rotas_delta_horarios(grupos)

        for grupo in grupos:
            if not rota_external_id:
                rota_external_id = get_rota_vexado_external_id(grupo.rota_internal_id, self.company.company_internal_id)
            datetime_ida = to_default_tz(
                datetime.strptime(f"{grupo.data_partida} {grupo.hora_saida}", "%Y-%m-%d %H:%M")
            )
            if self._grupo_ja_criado(rota_external_id, grupo, datetime_ida):
                continue
            grupo_model = self._get_or_make_grupo(grupo, datetime_ida)

            veiculos: list[Veiculo] = _get_and_check_veiculos(veiculos_linkados_map, grupo, self.company)

            classes_por_capacidade = defaultdict(list)
            for c in grupo.classes:
                classes_por_capacidade[c.capacidade].append(c)
            for v in veiculos:
                classe_primeiro_andar = classes_por_capacidade[v.mapa_veiculo.quantidade_poltronas_primeiro_andar].pop(
                    0
                )
                classe_segundo_andar = None
                if classes_por_capacidade[v.mapa_veiculo.quantidade_poltronas_segundo_andar]:
                    classe_segundo_andar = classes_por_capacidade[
                        v.mapa_veiculo.quantidade_poltronas_segundo_andar
                    ].pop(0)

                gc_vexado = self._get_or_make_grupo_classe(rota_external_id, v, grupo_model, 1, classe_primeiro_andar)
                vexado_grupos_classe = [gc_vexado]
                self.vexado_grupos_classe_to_create.append(gc_vexado)
                task_params = _task_params(v, grupo, classe_primeiro_andar, rota_external_id)

                if classe_segundo_andar:
                    gc_vexado = self._get_or_make_grupo_classe(
                        rota_external_id, v, grupo_model, 2, classe_segundo_andar
                    )
                    self.vexado_grupos_classe_to_create.append(gc_vexado)
                    task_params["classe_segundo_andar"] = MAP_CLASSES_VEXADO[classe_segundo_andar.tipo.lower()]
                    vexado_grupos_classe.append(gc_vexado)
                if v.id_internal is None:
                    logger.info("cadastrar_grupos_debug_id_veiculo_nulo", extra=grupo.dict())
                logger.info("cadastrar_grupos_task_params", extra=task_params)
                self.tasks.append((task_params, vexado_grupos_classe))

                for delta in self.rotas_delta_horarios.get(grupo.rota_internal_id, []):
                    horario_partida, days_ahead = timedelta_to_duracao(duracao_to_timedelta(grupo.hora_saida) + delta)
                    extra_task_params = task_params.copy()
                    if days_ahead:
                        extra_task_params["data_partida"] += timedelta(days=days_ahead)
                    extra_task_params["hora_saida"] = horario_partida
                    self.tasks.append((extra_task_params, vexado_grupos_classe))

        self._create_models()
        self._dispara_tasks()

    def _get_veiculos_linkados_map(self, grupos):
        veiculos_internal_ids = {g.veiculo_internal_id for g in grupos}
        veiculos_linkados = Veiculo.objects.select_related("mapa_veiculo").filter(
            id_internal__in=veiculos_internal_ids, company=self.company
        )
        veiculos_linkados_map = defaultdict(list)
        for v in veiculos_linkados:
            veiculos_linkados_map[v.id_internal].append(v)
        return veiculos_linkados_map

    def _create_models(self):
        Grupo.objects.bulk_create(self.grupos_to_create)
        if self.grupos_to_update:
            Grupo.objects.bulk_update(self.grupos_to_update, ["datetime_ida", "updated_at"])
        GrupoClasse.objects.bulk_create(self.grupos_classe_to_create)  # não possui updated_at
        VexadoGrupoClasse.objects.bulk_create(self.vexado_grupos_classe_to_create)  # não possui updated_at

    def _dispara_tasks(self):
        tasks_to_apply = []
        for task_params, vexado_grupos_classe in self.tasks:
            vexado_grupos_classe_ids = [vgc.id for vgc in vexado_grupos_classe]
            tasks_to_apply.append(
                cadastrar_grupo_task.s(
                    self.company.company_internal_id,
                    json.dumps(task_params),
                    vexado_grupos_classe_ids,
                )
            )
        group_task = group(tasks_to_apply)
        if self.assincrono:
            group_task.apply_async()
        else:
            group_task.apply()

    def _get_or_make_grupo_classe(self, rota_external_id, v, grupo_model, andar, classe):
        gc = self.grupos_classe_existentes_map.get(classe.grupo_classe_id)
        if not gc:
            gc = self._make_grupo_classe(grupo_model, classe)
            self.grupos_classe_to_create.append(gc)
        gc_vexado = self._make_grupo_classe_vexado(v, rota_external_id, andar, gc)
        return gc_vexado

    def _get_grupos_existentes(self, grupos):
        grupos_internal_ids = [g.grupo_id for g in grupos]
        grupos_existentes = Grupo.objects.filter(grupo_internal_id__in=grupos_internal_ids)
        self.grupos_existentes = {g.grupo_internal_id: g for g in grupos_existentes}

    def _grupo_ja_criado(self, rota_external_id, grupo, datetime_ida):
        return bool(
            self.vexado_grupos_classe_ja_criados_map.get(
                (
                    datetime_ida.strftime("%Y-%m-%d %H:%M"),
                    rota_external_id,
                    grupo.classes[0].tipo,
                )
            )
        )

    def _get_or_make_grupo(self, grupo, datetime_ida):
        grupo_model = self.grupos_existentes.get(grupo.grupo_id)
        if not grupo_model:
            grupo_model = Grupo(
                grupo_internal_id=grupo.grupo_id,
                datetime_ida=datetime_ida,
                company_integracao=self.company,
            )
            self.grupos_to_create.append(grupo_model)
        else:
            grupo_model.datetime_ida = datetime_ida
            grupo_model.company_integracao = self.company
            self.grupos_to_update.append(grupo_model)
        return grupo_model

    def _get_grupos_classe_ja_criados(self, grupos):
        grupos_classe_ids = []
        for grupo in grupos:
            for classe in grupo.classes:
                grupos_classe_ids.append(classe.grupo_classe_id)
        vexado_grupos_classe_ja_criados = (
            VexadoGrupoClasse.objects.filter(grupo_classe__grupoclasse_internal_id__in=grupos_classe_ids)
            .exclude(
                Q(status__contains=VexadoGrupoClasse.Status.CANCELADO)
                | Q(status=VexadoGrupoClasse.Status.FECHADO)
                | Q(status=VexadoGrupoClasse.Status.INCOMPLETO)
            )
            .select_related("grupo_classe")
            .annotate(datetime_ida=F("grupo_classe__grupo__datetime_ida"))
            .annotate(tipo_assento=F("grupo_classe__tipo_assento_internal"))
        )
        for vgc in vexado_grupos_classe_ja_criados:
            self.vexado_grupos_classe_ja_criados_map[
                (
                    to_default_tz(vgc.datetime_ida).strftime("%Y-%m-%d %H:%M"),
                    vgc.rota_external_id,
                    vgc.tipo_assento,
                )
            ] = vgc
        grupos_classe_existentes = GrupoClasse.objects.filter(grupoclasse_internal_id__in=grupos_classe_ids)
        self.grupos_classe_existentes_map = {gc.grupoclasse_internal_id: gc for gc in grupos_classe_existentes}
        self.grupos_classe_existentes_map = {}  # já volto a como era antes
        self.vexado_grupos_classe_ja_criados_map = {}  # já volto a como era antes

    def _make_grupo_classe(self, grupo_model, classe):
        grupo_classe = GrupoClasse(
            grupo=grupo_model,
            grupoclasse_internal_id=classe.grupo_classe_id,
            tipo_assento_internal=classe.tipo,
        )
        return grupo_classe

    def _make_grupo_classe_vexado(self, veiculo, rota_external_id, andar, grupo_classe):
        grupo_classe_vexado = VexadoGrupoClasse(
            grupo_classe=grupo_classe,
            veiculo=veiculo,
            andar=andar,
            rota_external_id=rota_external_id,
        )
        return grupo_classe_vexado

    def _get_rotas_delta_horarios(self, grupos):
        orchestrator = OrchestrateRodoviaria(self.company.company_internal_id, modelo_venda=Company.ModeloVenda.HIBRIDO)
        delta_horarios_rotas_map = {}
        rotas_ids = set()
        for g in grupos:
            rotas_ids.add(str(g.rota_internal_id))
        rotas_api = orchestrator.buscar_rotas()
        for rota_api in rotas_api:
            if rota_api["delimitacao"] not in rotas_ids:
                continue
            delta_horarios = []
            cidade_anterior_id = None
            for checkpoint in rota_api["itinerario"][1:-1]:
                if checkpoint["cidadeDestino"]["id"] == cidade_anterior_id:
                    duracao = duracao_to_timedelta(checkpoint["duracao"])
                    delta_horarios.append(delta_horarios[-1] + duracao if len(delta_horarios) > 0 else duracao)
                cidade_anterior_id = checkpoint["cidadeDestino"]["id"]
            delta_horarios_rotas_map[int(rota_api["delimitacao"])] = delta_horarios
        self.rotas_delta_horarios = delta_horarios_rotas_map


def get_rota_vexado_external_id(rota_internal_id, company_internal_id):
    rota = VexadoRota.objects.filter(
        rota_internal_id=rota_internal_id,
        company__company_internal_id=company_internal_id,
        company__modelo_venda=DEFAULT_MODELO_VENDA,
    ).order_by("-id")
    if not rota:
        raise RotaNaoCadastradaException(rota_internal_id, company_internal_id)
    rota = rota[0]
    if not rota.rota_external_id:
        raise RotaNaoCadastradaException(rota_internal_id, company_internal_id)
    return rota.rota_external_id


def _get_and_check_veiculos(veiculos_linkados_map, grupo, company):
    veiculos = veiculos_linkados_map.get(grupo.veiculo_internal_id)
    if not veiculos:
        veiculos = veiculos_svc.link_or_create_veiculo(grupo.veiculo_params, company)
        veiculos_linkados_map[grupo.veiculo_internal_id] = list(veiculos)
    else:
        veiculos = veiculos_svc._check_veiculo_link_and_update_if_necessary(grupo.veiculo_params, veiculos, company)
    if isinstance(veiculos, dict) and "error" in veiculos:
        raise veiculos_svc.GetOrCreateVeiculosError(veiculos["error"])
    return veiculos


def _task_params(veiculo, grupo, classe, rota_external_id):
    params = {
        "data_partida": grupo.data_partida,
        "hora_saida": grupo.hora_saida,
        "id_rota": rota_external_id,
        "id_veiculo": veiculo.id_external,
        "classe_primeiro_andar": MAP_CLASSES_VEXADO[classe.tipo.lower()],
        "classe_segundo_andar": None,
    }
    return params


@shared_task(
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.CADASTROS_VEXADO,
)
def cadastrar_grupo_task(company_id, grupo, vexado_grupos_classe_ids):
    grupos_classe_external_ids = OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA).cadastrar_grupo(
        CadastrarGrupoTaskParams.parse_raw(grupo)
    )
    vexado_grupos_classe = VexadoGrupoClasse.objects.select_related("grupo_classe").filter(
        id__in=vexado_grupos_classe_ids
    )
    for vgc in vexado_grupos_classe:
        vgc.grupo_classe_external_id = grupos_classe_external_ids[vgc.grupo_classe.tipo_assento_internal]
        vgc.status = VexadoGrupoClasse.Status.CRIADO
    VexadoGrupoClasse.objects.bulk_update(vexado_grupos_classe, ["grupo_classe_external_id", "status"])
    return grupos_classe_external_ids


def cadastrar_grupos_params(company_id, rota_internal_id):
    rotas = OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA).buscar_rotas()
    last_option = VexadoRota.objects.filter(
        company__company_internal_id=company_id,
        company__modelo_venda=DEFAULT_MODELO_VENDA,
        rota_internal_id=rota_internal_id,
    )

    return {
        "rotas": rotas,
        "last_option": last_option[0].rota_external_id if last_option else None,
    }


def get_trechos_overbooking():
    ids_trechos = TrechoClasse.objects.filter(datetime_ida__gt=timezone.now(), tags__name=Tags.OVERBOOKING).values_list(
        "trechoclasse_internal_id", flat=True
    )
    return list(ids_trechos)


def get_trechos_classe_error_hibrido():
    ids_trechos = TrechoClasseError.objects.filter(
        datetime_ida__gt=timezone.now(),
        company__modelo_venda=Company.ModeloVenda.HIBRIDO,
        company__integracao__name=Integracao.API.VEXADO,
    ).values_list("trechoclasse_internal_id", flat=True)
    return list(ids_trechos)


def delete_trechos_classe_error_hibrido(trechos_classe_ids):
    TrechoClasseError.objects.filter(
        trechoclasse_internal_id__in=trechos_classe_ids,
        company__modelo_venda=Company.ModeloVenda.HIBRIDO,
    ).delete()


class RotaNaoCadastradaException(Exception):
    def __init__(self, rota_internal_id, company_id):
        self.rota_internal_id = rota_internal_id
        self.company_id = company_id

    def __str__(self):
        return f"Rota com id {self.rota_internal_id} não cadastrada no banco rodoviaria para empresa {self.company_id}"

from collections import defaultdict
from datetime import datetime

from beeline import traced
from celery import chord, group, shared_task
from celery.utils.log import get_task_logger
from django.db.models import Max
from sentry_sdk import capture_exception

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import to_default_tz, today_midnight
from commons.django_utils import error_str
from commons.token_bucket import NotEnoughTokens, get_token_bucket_robust
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Rota, TaskStatus
from rodoviaria.service.atualiza_operacao_utils import finaliza_task_status, inicia_task_status
from rodoviaria.service.exceptions import RodoviariaConnectionError

task_logger = get_task_logger(__name__)

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE
DEFAULT_QUEUE_NAME = DefaultQueueNames.FETCH_DATA_LIMITE
LOG_PREFIX = "[fetch_data_limite_rotas]"


def _log_info(msg, _extra=None):
    task_logger.info("%s %s", LOG_PREFIX, msg, extra=_extra)


@shared_task(queue=DEFAULT_QUEUE_NAME)
@traced("fetch_data_limite_rotas.fetch_data_limite_rotas")
def fetch_data_limite_rotas(company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA):
    orchestrator = OrchestrateRodoviaria(company_internal_id, modelo_venda)
    queue_name = orchestrator.provider.queue_name or DEFAULT_QUEUE_NAME
    _log_info(f"Executando fetch_data_limite_rotas para empresa {company_internal_id} na fila {queue_name}")
    task = (
        _fetch_data_limite_rotas_task.s(queue_name, company_internal_id, modelo_venda)
        .set(queue=queue_name)
        .apply_async()
    )
    task_info = {
        "task_id": task.id,
        "queue": queue_name,
        "mensagem": f"Fetch data limite de rotas da empresa {company_internal_id}",
    }
    return task_info


@shared_task
@retry(
    exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens),
    min_delay=10,
)
@traced("fetch_data_limite_rotas._fetch_data_limite_rotas_task")
def _fetch_data_limite_rotas_task(queue_name, company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA):
    token_bucket = get_token_bucket_robust(company_internal_id, modelo_venda)
    token_bucket.try_get_token()

    orchestrator = OrchestrateRodoviaria(company_internal_id, modelo_venda)
    try:
        map_servicos_data = orchestrator.fetch_data_limite_servicos(today_midnight())
    except Exception as ex:
        task_logger.error(
            "%s [company_internal_id=%s] Erro ao buscar data limite dos servicos. error_str(ex)=%s",
            LOG_PREFIX,
            company_internal_id,
            error_str(ex),
        )
        raise ex

    _log_info(f"empresa {company_internal_id} data limite por id_external encontradas para: {map_servicos_data}")
    use_low_rate_limit = (
        Company.objects.select_related("integracao")
        .filter(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
        .values_list("integracao__use_low_rate_limit", flat=True)
        .first()
    )

    servicos = []
    for id_external in map_servicos_data:
        try:
            servicos.append(
                {
                    "company_internal_id": company_internal_id,
                    "datetime_ida": map_servicos_data[id_external].strftime("%Y-%m-%dT%H:%M:%S"),
                    "id_external": id_external,
                    "use_low_rate_limit": use_low_rate_limit,
                }
            )
        except KeyError:
            pass

    inicia_task_status(task_name=TaskStatus.Name.FETCH_DATA_LIMITE, company_id=orchestrator.provider.company.id)
    _generate_tasks(orchestrator.provider.company.id, servicos, queue_name)


@traced("fetch_data_limite_rotas._generate_tasks")
def _generate_tasks(company_id, rotas_a_verificar, queue_name):
    tasks = []
    for trecho in rotas_a_verificar:
        tasks.append(buscar_hash_rota_default_rate_limit.s(trecho).set(queue=queue_name))

    _group = group(*tasks)
    _chord = chord(_group)(batch_save_data_limite_on_rotas.s(company_id).set(queue=queue_name))
    _chord.parent.save()


@shared_task
@traced("fetch_data_limite_rotas.batch_save_data_limite_on_rotas")
def batch_save_data_limite_on_rotas(hashes, company_id):
    _log_info(
        f"Iniciando batch_save_data_limite_on_rotas para {len(hashes)} rotas. {hashes=}", _extra={"hashes": hashes}
    )

    map_hash_data = {}
    for hash_data in hashes:
        if hash_data:
            hash_key = hash_data["hash_key"]
            datetime_ida = datetime.strptime(hash_data["datetime_ida"], "%Y-%m-%dT%H:%M:%S")
            map_hash_data[hash_key] = max(datetime_ida, map_hash_data.get(hash_key, datetime.min))

    _log_info(f"{len(map_hash_data)} hashes para salvar. {map_hash_data=}", _extra={"map_hash_data": map_hash_data})
    rotas = Rota.objects.filter(id_hash__in=map_hash_data.keys())
    for r in rotas:
        r.data_limite = map_hash_data[r.id_hash]
        r.updated_at = to_default_tz(datetime.now())
    _log_info(f"{len(map_hash_data)} hashes para salvar.", _extra={"rotas_ids": [r.id for r in rotas]})
    Rota.objects.bulk_update(rotas, ["data_limite", "updated_at"])
    finaliza_task_status(task_name=TaskStatus.Name.FETCH_DATA_LIMITE, company_id=company_id)


@shared_task(rate_limit=DefaultRateLimits.FetchDataLimite.RATE_LIMIT)
@retry(
    exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens),
    min_delay=10,
)
def buscar_hash_rota_default_rate_limit(servico):
    company_internal_id = servico["company_internal_id"]

    token_bucket = get_token_bucket_robust(company_internal_id)
    token_bucket.try_get_token()

    orchestrator = OrchestrateRodoviaria(company_internal_id)
    id_external = servico["id_external"]
    datetime_ida = datetime.strptime(servico["datetime_ida"], "%Y-%m-%dT%H:%M:%S")
    _log_info(
        f'empresa {servico["company_internal_id"]}. buscando hash para o servico {id_external} no dia {datetime_ida}'
    )
    try:
        rota_fetchada = orchestrator.fetch_rota(id_external, datetime_ida)
        if not rota_fetchada:
            return None
        hash_key = rota_fetchada.parsed.hash
        return {"hash_key": hash_key, "datetime_ida": servico["datetime_ida"]}
    except (MyCircuitBreakerError, RodoviariaConnectionError, NotEnoughTokens):
        raise
    except Exception as ex:
        capture_exception(ex)
        return None


def get_data_limite_rotas_integradas(company_internal_ids):
    max_data_limite_agg = (
        Rota.objects.filter(
            company__company_internal_id__in=company_internal_ids,
            id_internal__isnull=False,
            data_limite__isnull=False,
            ativo=True,
        )
        .values_list("company__company_internal_id", "id_internal")
        .annotate(max_data=Max("data_limite"))
    )

    map_data_limite = defaultdict(dict)
    for company_id, rota_id, max_data_limite in max_data_limite_agg:
        map_data_limite[company_id][rota_id] = max_data_limite

    return dict(map_data_limite)

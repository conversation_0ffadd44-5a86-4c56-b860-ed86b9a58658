from celery import shared_task

from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.forms.motorista_forms import Motorista as MotoristaForm
from rodoviaria.models.core import Company, Grupo, Motorista, TrechoClasse
from rodoviaria.service.grupo_trechoclasse_factory import create_missing_grupo_and_trechoclasses

DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO


def escala_motorista(motorista, grupo_internal_id):
    escala_motorista_task.delay(motorista.json(), grupo_internal_id)


@shared_task(queue="bp_motorista")
def escala_motorista_task(dados_motorista, grupo_internal_id):
    motorista = MotoristaForm.parse_raw(dados_motorista)

    create_missing_grupo_and_trechoclasses(grupo_internal_id)

    grupo = Grupo.objects.filter(grupo_internal_id=grupo_internal_id).select_related("company_integracao").last()
    company = grupo.company_integracao

    orchestrator = OrchestrateRodoviaria(company.company_internal_id, DEFAULT_MODELO_VENDA)

    motora_obj, _ = Motorista.objects.get_or_create(
        company=company,
        internal_id=motorista.user_id,
    )

    # motorista novo na company
    if not motora_obj.external_id_usuario:
        result = orchestrator.cria_motorista(motorista)

        motora_obj.external_id_usuario = result["id_usuario"]
        motora_obj.external_id_usuario_empresa = result["id_usuario_empresa"]
        motora_obj.external_id_motorista = result["id_motorista"]

        motora_obj.nome = motorista.nome
        motora_obj.email = motorista.email
        motora_obj.telefone = motorista.telefone
        motora_obj.cpf = motorista.cpf

        if motorista.cnh:
            motora_obj.cnh_numero = motorista.cnh.numero
            motora_obj.cnh_validade = motorista.cnh.validade
            motora_obj.cnh_categoria = motorista.cnh.categoria
            motora_obj.cnh_orgao_emissor = motorista.cnh.orgao_emissor
            motora_obj.cnh_uf = motorista.cnh.uf

        if motorista.registro_antt:
            motora_obj.antt_numero = motorista.registro_antt.numero
            motora_obj.antt_validade = motorista.registro_antt.validade

        motora_obj.save()

    # dados do motorista alterados
    if (
        motora_obj.nome != motorista.nome
        or motora_obj.email != motorista.email
        or motora_obj.telefone != motorista.telefone
        or motora_obj.cpf != motorista.cpf
    ):
        orchestrator.edita_dados_motorista(motora_obj.external_id_usuario, motorista)

        motora_obj.nome = motorista.nome
        motora_obj.email = motorista.email
        motora_obj.telefone = motorista.telefone
        motora_obj.cpf = motorista.cpf

        motora_obj.save()

    # documentos do motorista alterados
    if motorista.cnh and motorista.registro_antt:
        if (
            motora_obj.cnh_numero != motorista.cnh.numero
            or motora_obj.cnh_validade != motorista.cnh.validade
            or motora_obj.cnh_categoria != motorista.cnh.categoria
            or motora_obj.cnh_orgao_emissor != motorista.cnh.orgao_emissor
            or motora_obj.cnh_uf != motorista.cnh.uf
            or motora_obj.antt_numero != motorista.registro_antt.numero
            or motora_obj.antt_validade != motorista.registro_antt.validade
        ):
            orchestrator.edita_documentos_motorista(
                motora_obj.external_id_motorista,
                motora_obj.external_id_usuario_empresa,
                motorista,
            )

            motora_obj.cnh_numero = motorista.cnh.numero
            motora_obj.cnh_validade = motorista.cnh.validade
            motora_obj.cnh_categoria = motorista.cnh.categoria
            motora_obj.cnh_orgao_emissor = motorista.cnh.orgao_emissor
            motora_obj.cnh_uf = motorista.cnh.uf
            motora_obj.antt_numero = motorista.registro_antt.numero
            motora_obj.antt_validade = motorista.registro_antt.validade

            motora_obj.save()

    # escala motorista para viagem
    external_ids = TrechoClasse.objects.filter(grupo=grupo).values_list("external_id", flat=True)
    itinerarios = set(external_ids)

    for id_itinerario in itinerarios:
        orchestrator.escala_motorista(motora_obj.external_id_usuario_empresa, id_itinerario)

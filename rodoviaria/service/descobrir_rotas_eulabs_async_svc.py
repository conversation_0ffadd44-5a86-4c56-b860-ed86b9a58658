import logging
from datetime import timed<PERSON><PERSON>

from celery import chain, group
from celery.app import shared_task
from celery.utils.log import get_task_logger
from pydantic import parse_obj_as

from commons import dateutils
from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.eulabs import endpoints as endpoints
from rodoviaria.api.eulabs import models as eulabs_models
from rodoviaria.api.eulabs.exceptions import EulabsAPIError
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.models import EulabsLogin
from rodoviaria.models.core import Company, TaskStatus
from rodoviaria.service.atualiza_operacao_utils import (
    atualizar_ou_criar_rota,
    atualizar_ou_criar_rotina,
    finaliza_task_status,
    inativa_rotas_e_rotinas_empresa,
)
from rodoviaria.service.exceptions import RodoviariaConnectionError

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")

DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTAS
DEFAULT_RATE_LIMIT = DefaultRateLimits.Eulabs.ROTAS


DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE


def descobrir_rotas(
    client: EulabsLogin,
    company_internal_id,
    next_days=7,
    shift_days=2,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA)
    date_inicial = dateutils.today() + timedelta(days=shift_days)
    date_final = dateutils.today() + timedelta(days=shift_days + next_days)

    parsed_servicos = _buscar_viagens_api(client, company, date_inicial, date_final)

    inativa_rotas_e_rotinas_empresa(company.id, date_inicial, date_final, inativa_trechos_vendidos=False)

    msg = f"{len(parsed_servicos)} possíveis rotas encontradas para empresa {company_internal_id}"
    logger.info(msg)

    task = _buscar_e_salvar_rotas_dos_servicos(
        parsed_servicos,
        company_internal_id,
        queue_name,
        return_task_object,
    )
    return task


def _buscar_viagens_api(client, company, data_inicial, data_final):
    buscas = _get_range_buscas(data_inicial, data_final)
    viagens = []
    for data_inicio, data_fim in buscas:
        data_inicial_str = data_inicio.strftime("%Y-%m-%d")
        data_final_str = data_fim.strftime("%Y-%m-%d")
        params = {
            "initial_departure_date": data_inicial_str,
            "final_departure_date": data_final_str,
        }
        executor = get_http_executor()
        request_config = endpoints.BuscarViagensPorPeriodoConfig(client)
        response = request_config.invoke(
            executor,
            params=params,
        )
        response_json = response.json()
        parsed_servicos = parse_obj_as(eulabs_models.Viagens, response_json)

        parsed_servicos.filter(company_external_id=company.company_external_id)

        viagens += parsed_servicos
    return viagens


def _get_range_buscas(data_inicial, data_final) -> list[tuple]:
    diff = (data_final - data_inicial).days
    qtd_buscas = diff // 30
    buscas = [data_inicial + timedelta(days=i * 30) for i in range(qtd_buscas + 1)]
    if diff % 30 > 0:
        buscas += [data_final]
    tuplas_buscas = []
    for inicio, fim in zip(buscas, buscas[1:]):
        if inicio == data_inicial:
            tuplas_buscas.append((inicio, fim))
        else:
            tuplas_buscas.append((inicio + timedelta(days=1), fim))
    return tuplas_buscas


def _buscar_e_salvar_rotas_dos_servicos(
    servicos,
    company_internal_id,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA)
    tasks = []
    tasks.extend(
        [
            _buscar_rota_servico.s(company.id, servico.item_id, servico.rota_external_id).set(queue=queue_name)
            for servico in servicos
        ]
    )
    _group = group(*tasks)
    chain_task = chain(_group, finisher.si(company.id)).on_error(error_handler.s(company_internal_id))
    if not return_task_object:
        chain_task()
    return chain_task


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def error_handler(request, exc, traceback, company_id):
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_ROTAS, company_id=company_id, failure=True)


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def finisher(company_id):
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_ROTAS, company_id=company_id)


@shared_task(rate_limit=DEFAULT_RATE_LIMIT)
@retry(
    exceptions_type=(
        RodoviariaConnectionError,
        MyCircuitBreakerError,
        EulabsAPIError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
def _buscar_rota_servico(company_id, item_id, rota_external_id):
    itinerario = buscar_rota_do_servico(company_id, item_id, rota_external_id)
    if itinerario.raw and len(itinerario.raw) > 1:
        logger.info("Rota encontrada para o servico %s", str(item_id))
        rota = atualizar_ou_criar_rota(company_id, itinerario, item_id, activate=True)
        datetime_ida_naive = itinerario.parsed[0].datetime_ida
        atualizar_ou_criar_rotina(rota=rota, datetime_ida=datetime_ida_naive, id_viagem=item_id, ativo=True)


def buscar_rota_do_servico(company_id: int, item_id, rota_external_id):
    from rodoviaria.api.orchestrator import OrchestrateRodoviaria

    company = Company.objects.get(id=company_id)
    orchestrator = OrchestrateRodoviaria.from_company(company)
    params = {"item_id": item_id, "rota_external_id": rota_external_id}
    return orchestrator.buscar_itinerario(params)

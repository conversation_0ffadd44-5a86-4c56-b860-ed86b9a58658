from datetime import <PERSON><PERSON><PERSON>

from django.db.models import Q

from core.models_grupo import Grupo as CoreGrupo
from core.models_rota import Checkpoint as CoreCheckpoint
from core.models_rota import <PERSON>ota as CoreRota
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Rota
from rodoviaria.service.rota_svc import (
    atualizar_horarios_e_duracoes_por_timezone,
    filtrar_apenas_checkpoints_integrados,
)

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE


def _gerar_itinerarios_checkpoints_rotas_rodoviaria(orchestrator, rotas_ids: list[int], recalcular_duracao=True):
    checkpoints = {}
    for rota_id in rotas_ids:
        checkpoints_rodoviaria = orchestrator.converter_parsed_data_to_checkpoints([rota_id])
        if recalcular_duracao:
            checkpoints_rodoviaria = atualizar_horarios_e_duracoes_por_timezone(checkpoints_rodoviaria)

        checkpoints_rodoviaria_apenas_integradas = filtrar_apenas_checkpoints_integrados(checkpoints_rodoviaria)
        for c in checkpoints_rodoviaria_apenas_integradas:
            if c.rota_id not in checkpoints.keys():
                checkpoints[c.rota_id] = {}
            checkpoints[c.rota_id][int(c.local.cidade.cidade_internal_id)] = c.duracao + c.tempo_embarque

    return checkpoints


def _gerar_itinerarios_checkpoints_rotas_django(django_rota_ids: list[int]):
    qs = CoreCheckpoint.objects.select_related("local", "local__cidade").filter(rota_id__in=django_rota_ids)
    checkpoints_rotas_django = {}
    for c in qs:
        if c.rota_id not in checkpoints_rotas_django.keys():
            checkpoints_rotas_django[c.rota_id] = {}
        checkpoints_rotas_django[c.rota_id][c.local.cidade_id] = (
            c.duracao if c.duracao else timedelta()
        ) + c.tempo_embarque
    return checkpoints_rotas_django


def _verificar_rota_rodoviaria_contem_rota_django(chks_rodo, chks_django):
    rodo_contains_django = True
    only_siglas_rodo = chks_rodo.keys()
    for sigla_django in chks_django:
        if sigla_django not in only_siglas_rodo:
            rodo_contains_django = False
            break
    return rodo_contains_django


def _verificar_duracao_rotas_eh_compativel(chks_rodo, chks_django):
    only_siglas_django = chks_django.keys()
    duracao_somada = timedelta()

    # verifica se rota do rodoviaria contem todos os checkpoints da rota do django
    # rodo_has_any_chk_in_django testa se tem pelo menos um checkpoint dentro
    rodo_contains_django = True
    rodo_has_any_chk_in_django = False
    for s in chks_rodo:
        if s in only_siglas_django:
            rodo_has_any_chk_in_django = True
            if s == list(only_siglas_django)[0]:
                duracao_chk = timedelta()
            else:
                duracao_chk = chks_rodo[s] + duracao_somada
            if abs((duracao_chk - chks_django[s]).total_seconds()) > 900:
                rodo_contains_django = False
                break

            duracao_somada = timedelta()
        else:
            duracao_somada = duracao_somada + chks_rodo[s]

    return rodo_contains_django and rodo_has_any_chk_in_django


def get_rotas_linkadas_erradas(company_internal_id, rotas_rodoviaria, recalcular_duracao=True):
    orchestrator = OrchestrateRodoviaria(company_internal_id=company_internal_id)
    checkpoints_rotas_empresa_django = _gerar_itinerarios_checkpoints_rotas_django(
        [x.id_internal for x in rotas_rodoviaria]
    )
    checkpoints_rotas_empresa_rodoviaria = _gerar_itinerarios_checkpoints_rotas_rodoviaria(
        orchestrator, [x.id for x in rotas_rodoviaria], recalcular_duracao
    )

    rotas_linkadas_erradas = []
    for r in rotas_rodoviaria:
        checkpoints_rodo = checkpoints_rotas_empresa_rodoviaria[r.id]

        if r.id_internal not in checkpoints_rotas_empresa_django.keys():
            rotas_linkadas_erradas.append((r, checkpoints_rodo))
            continue

        checkpoints_django = checkpoints_rotas_empresa_django[r.id_internal]

        rodo_contains_django = _verificar_rota_rodoviaria_contem_rota_django(checkpoints_rodo, checkpoints_django)

        if rodo_contains_django:
            rodo_contains_django = _verificar_duracao_rotas_eh_compativel(checkpoints_rodo, checkpoints_django)

        if not rodo_contains_django:
            rotas_linkadas_erradas.append((r, checkpoints_rodo))
    return rotas_linkadas_erradas


def _get_ids_rotas_ativas_empresa_django(company_id):
    # busca todas as rotas que tem grupos da empresa ou que nao tenham nenhum grupo associado
    ids_rotas_ignorados = (
        CoreGrupo.objects.filter(~Q(company_id=company_id, modelo_venda=DEFAULT_MODELO_VENDA))
        .values_list("rota_id", flat=True)
        .distinct()
    )
    ids_rotas = CoreRota.objects.filter(~Q(id__in=ids_rotas_ignorados)).values_list("id", flat=True)
    return ids_rotas


def _add_rota_lista_sugestoes(
    sugestao_novos_ids,
    rota_rodo,
    id_rota_django,
    chks_rodo,
    chks_django,
    chks_django_antiga,
    rodo_contains_django,
):
    join_separator = " - "
    if rota_rodo.id not in sugestao_novos_ids.keys():
        sugestao_novos_ids[rota_rodo.id] = {
            "rodoviaria_rota_id": rota_rodo.id,
            "id_internal_atual": rota_rodo.id_internal,
            "siglas_rodoviaria": join_separator.join([f"{x}({str(chks_rodo[x].total_seconds())})" for x in chks_rodo]),
            "siglas_atual": (
                join_separator.join([f"{x}({str(chks_django_antiga[x].total_seconds())})" for x in chks_django_antiga])
                if rota_rodo.id_internal
                else ""
            ),
            "sugestoes": [],
        }
    if rodo_contains_django:
        if rota_rodo.id in sugestao_novos_ids.keys():
            sugestao_novos_ids[rota_rodo.id]["sugestoes"].append(
                {
                    "id_internal": id_rota_django,
                    "siglas": join_separator.join([f"{x}({str(chks_django[x].total_seconds())})" for x in chks_django]),
                }
            )


def tenta_achar_match_no_django(company_id, rotas_linkadas_erradas, recalcular_duracao=True):
    sugestao_novos_ids = {}
    ids_rotas_empresa_django = _get_ids_rotas_ativas_empresa_django(company_id)
    checkpoints_rotas_empresa_django = _gerar_itinerarios_checkpoints_rotas_django(ids_rotas_empresa_django)

    for rota_rodo, chks_rodo in rotas_linkadas_erradas:
        chks_django_antiga = (
            checkpoints_rotas_empresa_django[rota_rodo.id_internal]
            if rota_rodo.id_internal in checkpoints_rotas_empresa_django.keys()
            else {}
        )
        for id_rota_django in ids_rotas_empresa_django:
            chks_django = checkpoints_rotas_empresa_django[id_rota_django]

            rodo_contains_django = _verificar_rota_rodoviaria_contem_rota_django(chks_rodo, chks_django)

            if rodo_contains_django:
                rodo_contains_django = _verificar_duracao_rotas_eh_compativel(chks_rodo, chks_django)

            _add_rota_lista_sugestoes(
                sugestao_novos_ids,
                rota_rodo,
                id_rota_django,
                chks_rodo,
                chks_django,
                chks_django_antiga,
                rodo_contains_django,
            )

    return sugestao_novos_ids


def comparar_rotas(company_internal_id, apenas_linkadas_erradas=True, recalcular_duracao=True):
    rotas_rodoviaria = Rota.objects.filter(
        company__company_internal_id=company_internal_id,
        company__modelo_venda=DEFAULT_MODELO_VENDA,
        id_internal__isnull=False,
    )
    if apenas_linkadas_erradas:
        rotas_rodoviaria = get_rotas_linkadas_erradas(company_internal_id, rotas_rodoviaria, recalcular_duracao)
    return tenta_achar_match_no_django(company_internal_id, rotas_rodoviaria, recalcular_duracao)

import logging
from datetime import datetime

from commons.dateutils import to_default_tz
from core.models_grupo import TrechoVendido as CoreTrechoVendido
from rodoviaria.models.core import Company, TrechoVendido

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE

buserlogger = logging.getLogger("rodoviaria")


def atualizar_id_internal_trechos_vendidos_company(company_id):
    rodo_trechos = _get_trechos_vendidos_company_rodoviaria(company_id)
    core_grupos_rotas = _get_trechos_vendidos_buser_django(rodo_trechos)

    trechos_atualizados = _add_id_internal_rodo_trechos(rodo_trechos, core_grupos_rotas)
    if trechos_atualizados:
        TrechoVendido.objects.bulk_update(trechos_atualizados, fields=["id_internal", "updated_at"])
    return len(trechos_atualizados)


def atualizar_id_internal_dict_trechos_vendidos(map_trechos_vendidos_internal_id):
    trechos_db = TrechoVendido.objects.filter(pk__in=map_trechos_vendidos_internal_id.keys())

    map_trechos_irmaos = {}
    rodoviaria_rota_ids = set()
    for tv in trechos_db:
        rodoviaria_rota_ids.add(tv.rota_id)
        trecho_vendido_internal_id = map_trechos_vendidos_internal_id[str(tv.id)]
        map_trechos_irmaos[(tv.rota_id, tv.origem_id, tv.destino_id)] = trecho_vendido_internal_id

    trechos_rota = []
    for rodoviaria_rota_id in rodoviaria_rota_ids:
        trechos_rota = TrechoVendido.objects.filter(rota_id=rodoviaria_rota_id)

        for tv in trechos_rota:
            try:
                tv.id_internal = map_trechos_irmaos[(tv.rota_id, tv.origem_id, tv.destino_id)]
                tv.updated_at = to_default_tz(datetime.now())
            except KeyError:  # PERF203
                pass

    TrechoVendido.objects.bulk_update(trechos_rota, fields=["id_internal", "updated_at"])
    return len(trechos_rota)


def _get_trechos_vendidos_company_rodoviaria(company_id):
    # Busca todos os trechos vendidos da empresa que nao tenham id_internal (nao linkada)
    # mas que tenha origem, destino e rota já linkados
    return (
        TrechoVendido.objects.select_related("origem", "destino", "rota", "rota__company")
        .filter(
            rota__company__company_internal_id=company_id,
            rota__company__modelo_venda=DEFAULT_MODELO_VENDA,
            id_internal__isnull=True,
            rota__id_internal__isnull=False,
            origem__local_embarque_internal_id__isnull=False,
            destino__local_embarque_internal_id__isnull=False,
        )
        .order_by("id")
    )


def _get_trechos_vendidos_buser_django(rodo_trechos):
    rotas_internal_ids = list(rodo_trechos.values_list("rota__id_internal", flat=True).distinct())

    core_trechos_rotas = CoreTrechoVendido.objects.filter(rota_id__in=rotas_internal_ids)

    core_trechos_rotas = {
        f"{x[0]}{x[1]}": x[2] for x in core_trechos_rotas.values_list("origem_id", "destino_id", "id")
    }
    return core_trechos_rotas


def _add_id_internal_rodo_trechos(rodo_trechos, core_trechos):
    atualizados = []
    for trecho in rodo_trechos:
        key = f"{trecho.origem.local_embarque_internal_id}{trecho.destino.local_embarque_internal_id}"
        if key in core_trechos:
            trecho.id_internal = core_trechos[key]
            atualizados.append(trecho)

    return atualizados

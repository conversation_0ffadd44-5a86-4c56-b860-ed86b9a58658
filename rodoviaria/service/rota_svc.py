import json
import logging
from collections import defaultdict
from datetime import datetime, timedelta
from functools import reduce

from beeline import traced
from constance import config as constance
from django.core.paginator import Paginator
from django.db.models import F, Q

from commons.dateutils import replace_timezone, to_default_tz, to_tz
from rodoviaria.api.forms import CheckpointsForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria, feature_required
from rodoviaria.forms.staff_forms import ItinerariosNaoIntegradosForm
from rodoviaria.models.core import Checkpoint, Company, Grupo, Rota, Rotina, TaskStatus, TrechoVendido
from rodoviaria.service import rotina_svc
from rodoviaria.service.exceptions import RodoviariaNoLocalCheckpointException, RodoviariaNoTimezoneException

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE

buserlogger = logging.getLogger("rodoviaria")
AUTO_INTEGRAR_ROTAS_LOG_PREFIX = "[auto_integrate_rotas_mktplace] "


@traced("rota_svc.atualizar_horarios_e_duracoes_por_timezone")
def atualizar_horarios_e_duracoes_por_timezone(checkpoints: list[Checkpoint], grupo_external_datetime_ida=None):
    """
    Calcula os horarios e durações do itinerário.
    Quando grupo_external_datetime_ida é passado, usa ele como horario de inicio do itinerario.
    ---
    Checkpoints do rodoviaria e do buser_django diferem em relação a duração entre eles:
    O rodoviaria calcula as diferenças absolutas entre os horarios, e o buser_django considera os timezones.


    Por exemplo:

        A(`08:00 -3:00`) -> B(`12:00 -5:00`)

        No rodoviaria a duração será: `04:00`

        No buser_django, ela será: `02:00`

    Por causa disso, ao calcular os horários fazemos um replace_timezone. Isso, porque no banco do rodoviária nós
    salvamos a duração sem considerar timezone,
    i.e., uma duração tão somente como `dt_prox - dt-inicio - tempo_embarque`.

    Fazemos isso pois muitas APIs de parceiros não retornam o timezone de cada localidade no itinerário de uma rota,
    daí a necessidade de linkagem de locais para corrigirmos os datetime_ida das rotinas extraídas via API do parceiro.
    Em suma, recalculamos a duração para considerar os timezones e repassamos a duração real para o buser_django.
    """
    if len(checkpoints) == 0:
        return checkpoints

    timezone = checkpoints[0].get_timezone_or_fallback()

    if grupo_external_datetime_ida:
        # caso o grupo tenha external_datetime_ida, é preciso corrigir todos horarios de arrival/departure
        # o horario de saida vindo do provider data pode estar desatualizado em relacao a este grupo especifico,
        # entao corrige todos os horarios pra esse grupo
        new_departure = to_tz(grupo_external_datetime_ida, timezone)
    else:
        new_departure = to_tz(checkpoints[0].departure, timezone)
    checkpoints[0].departure = new_departure

    for chk_anterior, chk_atual in zip(checkpoints, checkpoints[1:]):
        timezone = chk_atual.get_timezone_or_fallback(timezone)

        new_arrival = new_departure + chk_atual.duracao
        chk_atual.arrival = replace_timezone(new_arrival, timezone)

        if chk_atual != checkpoints[-1]:
            new_departure += chk_atual.duracao + chk_atual.tempo_embarque
            chk_atual.departure = replace_timezone(new_departure, timezone)

        horario_chk_atual = chk_atual.departure or chk_atual.arrival
        horario_chk_anterior = chk_anterior.departure or chk_anterior.arrival
        duracao = horario_chk_atual - horario_chk_anterior

        if chk_atual == checkpoints[-1]:
            # Não tem tempo de embarque no último checkpoint.
            tempo_embarque = timedelta(minutes=0)
        elif duracao <= timedelta(minutes=10):
            # Tempo de embarque em paradas rápidas é o minimo de 1 min.
            tempo_embarque = timedelta(minutes=1)
        else:
            tempo_embarque = timedelta(minutes=10)

        chk_atual.duracao = duracao - tempo_embarque
        chk_atual.tempo_embarque = tempo_embarque

    return checkpoints


def _get_checkpoints_por_rota(
    rota_id,
    grupo_external_datetime_ida=None,
    recalcular_horarios_e_duracoes_por_timezone=True,
):
    checkpoints = list(
        Checkpoint.objects.select_related("local", "local__cidade", "local__cidade__cidade_internal")
        .filter(rota_id=rota_id)
        .order_by("idx")
    )
    if recalcular_horarios_e_duracoes_por_timezone:
        checkpoints = atualizar_horarios_e_duracoes_por_timezone(checkpoints, grupo_external_datetime_ida)
    return checkpoints


def _get_checkpoints_por_grupo(grupo_internal_id):
    if not grupo_internal_id:
        return None
    grupo = Grupo.objects.filter(grupo_internal_id=grupo_internal_id).exclude(rota=None).first()
    if not grupo:
        return None

    checkpoints = _get_checkpoints_por_rota(grupo.rota_id, grupo.external_datetime_ida)
    return checkpoints


@feature_required("itinerario")
def itinerario(orchestrator, grupo_buser_id):
    checkpoints = _get_checkpoints_por_grupo(grupo_buser_id)
    if checkpoints is None or len(checkpoints) == 0:
        return None
    checkpoints = [checkpoint.to_dict_json() for checkpoint in checkpoints]
    return checkpoints


def itinerario_parsed_data_from_rota(rota):
    itinerario = list(rota.parsed_data)
    return itinerario


def _parsed_itinerario(orchestrator, grupo_buser_id=None, rota_id=None):
    if not grupo_buser_id and not rota_id:
        return None, None

    grupo = None
    if grupo_buser_id:
        grupo = (
            Grupo.objects.select_related("company_integracao")
            .filter(grupo_internal_id=grupo_buser_id)
            .exclude(rota=None)
            .first()
        )
        if not grupo:
            return None, None
        rota_id = grupo.rota_id

    rota = orchestrator.get_rotas([rota_id])[0]
    return grupo, itinerario_parsed_data_from_rota(rota)


@feature_required("itinerario")
def itinerario_parsed_data(orchestrator, grupo_buser_id=None, rota_id=None):
    _, itinerario = _parsed_itinerario(orchestrator, grupo_buser_id, rota_id)
    return itinerario


def get_timezone_inicio_itinerario(rota_id=None, grupo_internal_id=None):
    if not rota_id and not grupo_internal_id:
        raise ValueError("É obrigatório passar rota_id ou grupo_internal_id")

    if not rota_id and grupo_internal_id:
        grupo = Grupo.objects.filter(grupo_internal_id=grupo_internal_id).exclude(rota=None).first()
        if not grupo:
            return "America/Sao_Paulo"
        rota_id = grupo.rota.id

    c = Checkpoint.objects.select_related("local__cidade").get(rota_id=rota_id, idx=0)
    return c.local.cidade.timezone if c.local and c.local.cidade and c.local.cidade.timezone else "America/Sao_Paulo"


def _create_or_update_checkpoints_por_rota(orchestrator: OrchestrateRodoviaria, rodoviaria_rota_id):
    parsed_data = itinerario_parsed_data(orchestrator, rota_id=rodoviaria_rota_id)
    cksForm = CheckpointsForm.from_itinerario(
        orchestrator.provider.company.id,
        parsed_data,
    )
    checkpoints_existentes_por_idx = {c.idx: c for c in Checkpoint.objects.filter(rota_id=rodoviaria_rota_id)}
    checkpoints = []
    for idx, new_chk in enumerate(cksForm):
        chk = checkpoints_existentes_por_idx.get(idx, Checkpoint())
        chk.idx = idx
        chk.rota_id = rodoviaria_rota_id
        chk.local_id = new_chk.local.rodoviaria_local_id
        chk.internal_id = None
        chk.arrival = new_chk.arrival
        chk.departure = new_chk.departure
        chk.distancia_km = new_chk.distancia_km
        chk.duracao = timedelta(seconds=new_chk.duracao)
        chk.tempo_embarque = timedelta(seconds=new_chk.tempo_embarque)
        chk.id_external = new_chk.local.id_external
        chk.uf = new_chk.local.uf
        chk.name = new_chk.local.name
        chk.nickname = new_chk.local.nickname
        chk.updated_at = to_default_tz(datetime.now())
        checkpoints.append(chk)
    Checkpoint.objects.bulk_update(
        [c for c in checkpoints if c.id],
        [
            "idx",
            "local_id",
            "internal_id",
            "arrival",
            "departure",
            "distancia_km",
            "duracao",
            "tempo_embarque",
            "id_external",
            "uf",
            "name",
            "nickname",
            "updated_at",
        ],
    )
    Checkpoint.objects.bulk_create([c for c in checkpoints if not c.id])

    return checkpoints


@feature_required("itinerario")
def create_or_update_rota(
    orchestrator: OrchestrateRodoviaria,
    company_id,
    itinerario,
    id_external=None,
    trecho_classe=None,
):
    rota, _ = orchestrator.create_or_update_rota(
        id_hash=itinerario.parsed.hash,
        company_id=company_id,
        defaults={
            "provider_data": json.dumps(itinerario.cleaned),
            "id_external": id_external or orchestrator.rota_id_from_trecho_classe(trecho_classe),
        },
    )
    checkpoints = _create_or_update_checkpoints_por_rota(orchestrator, rota.id)

    # se um itinerario foi encontrado, é de um horario ativo, então ativa a rotina
    Rotina.objects.update_or_create(
        rota_id=rota.id,
        datetime_ida=checkpoints[0].departure,
        defaults={"ativo": True},
    )

    return rota, checkpoints


def atualizar_checkpoints_rotas_por_empresa(company_internal_id, modelo_venda):
    company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
    orchestrator = OrchestrateRodoviaria(company_internal_id, modelo_venda)
    rotas = orchestrator.get_rotas_empresa(company.id).prefetch_related("itinerario")
    rotas_ids = rotas.values_list("id", flat=True)

    list_chks = list(Checkpoint.objects.filter(rota_id__in=rotas_ids))
    checkpoints_db = defaultdict(list)
    for c in list_chks:
        checkpoints_db[c.rota_id].append(c)
    checkpoints = []
    for rota in rotas:
        if rota.id in checkpoints_db.keys():
            cksForm = CheckpointsForm.from_itinerario(company.id, itinerario_parsed_data_from_rota(rota))
            checkpoints_by_index = {c.idx: c for c in checkpoints_db[rota.id]}
            for idx, ck in enumerate(cksForm):
                c = checkpoints_by_index[idx]
                c.local_id = ck.local.rodoviaria_local_id
                c.arrival = ck.arrival
                c.departure = ck.departure
                c.duracao = timedelta(seconds=ck.duracao)
                c.tempo_embarque = timedelta(seconds=ck.tempo_embarque)
                c.id_external = ck.local.id_external
                c.uf = ck.local.uf
                c.name = ck.local.name
                c.nickname = ck.local.nickname
                c.updated_at = to_default_tz(datetime.now())

                checkpoints.append(c)

    if checkpoints:
        Checkpoint.objects.bulk_update(
            checkpoints,
            [
                "local_id",
                "arrival",
                "departure",
                "duracao",
                "tempo_embarque",
                "id_external",
                "uf",
                "name",
                "nickname",
                "updated_at",
            ],
            batch_size=500,
        )


def atualizar_checkpoints_rota(rota_id):
    company = Rota.objects.select_related("company").get(pk=rota_id).company
    cid = company.company_internal_id
    modelo_venda = company.modelo_venda
    _create_or_update_checkpoints_por_rota(OrchestrateRodoviaria(cid, modelo_venda), rota_id)


def _paginator_sort_by(itinerarios, sort_key, descending):
    if sort_key == "checkpoints__local__nickname":
        itinerarios = sorted(
            itinerarios,
            key=lambda x: x["checkpoints"][0]["local"]["name"],
            reverse=descending,
        )
    else:
        if sort_key == "rotina":
            itinerarios = sorted(
                itinerarios,
                key=lambda x: len(json.dumps(x[sort_key])),
                reverse=descending,
            )
        else:
            if sort_key == "pk":
                sort_key = "rodoviaria_rota_id"
            itinerarios = sorted(
                itinerarios,
                key=lambda x: x[sort_key] if x[sort_key] else 0,
                reverse=descending,
            )
    return itinerarios


def _get_trechos_vendidos_rota(rodoviaria_rota_id):
    trechos_vendidos = (
        TrechoVendido.objects.select_related("origem", "destino")
        .filter(rota_id=rodoviaria_rota_id, ativo=True)
        .values(
            "id",
            "origem__local_embarque_internal_id",
            "destino__local_embarque_internal_id",
            "preco",
        )
        .distinct("origem_id", "destino_id")
    )

    return [
        {
            "rodoviaria_trecho_vendido_id": t["id"],
            "origem_id": t["origem__local_embarque_internal_id"],
            "destino_id": t["destino__local_embarque_internal_id"],
            "preco_rodoviaria": t["preco"],
        }
        for t in trechos_vendidos
    ]


def _get_rotas_empresa(company, integradas: str = "todas", ativas: str = "ativas"):
    orchestrator = OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda)
    rotas = orchestrator.get_rotas_empresa(company.id)

    if integradas == "nao_integradas":
        rotas = rotas.filter(id_internal__isnull=True)
    elif integradas == "integradas":
        rotas = rotas.filter(id_internal__isnull=False)

    if ativas == "ativas":
        rotas = rotas.filter(ativo=True)
    elif ativas == "inativas":
        rotas = rotas.filter(ativo=False)

    rotas = rotas.values("id", "id_internal", "id_external", "id_hash", "ativo")

    if not rotas or len(rotas) == 0:
        return []

    parsed_itinerarios = []
    for rota in rotas:
        rodoviaria_rota_id = rota["id"]

        checkpoints = _get_checkpoints_por_rota(rodoviaria_rota_id, recalcular_horarios_e_duracoes_por_timezone=False)
        if len(checkpoints) == 0:
            continue

        json_checkpoints = []
        todos_locais_integrados = True
        for c in checkpoints:
            if todos_locais_integrados:
                if not c.local or c.local.local_embarque_internal_id is None:
                    todos_locais_integrados = False
            json_checkpoints.append(c.to_dict_json())

        trechos = TrechoVendido.objects.filter(rota_id=rodoviaria_rota_id, ativo=True)
        has_trechos = trechos.exists()

        try:
            trechos_last_update = TaskStatus.objects.get(
                rota_id=rodoviaria_rota_id,
                task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
                company_id=company.id,
            ).last_success_at
        except TaskStatus.DoesNotExist:
            trechos_last_update = ""

        if trechos_last_update:
            trechos_last_update = to_default_tz(trechos_last_update).strftime("%d/%m/%Y às %H:%M")

        try:
            rotina = rotina_svc.get_rotina(rodoviaria_rota_id)["rotina"]
        except (RodoviariaNoLocalCheckpointException, RodoviariaNoTimezoneException):
            rotina = None

        parsed_itinerarios.append(
            {
                "rodoviaria_rota_id": rodoviaria_rota_id,
                "id_internal": rota["id_internal"],
                "id_external": rota["id_external"],
                "id_hash": rota["id_hash"],
                "todos_locais_integrados": todos_locais_integrados,
                "possui_trechos_vendidos": has_trechos,
                "trechos_last_update": trechos_last_update,
                "checkpoints": json_checkpoints,
                "rotina": rotina,
                "ativo": rota["ativo"],
            }
        )

    return parsed_itinerarios


def _get_filtered_get_rotas_empresa(company, params: ItinerariosNaoIntegradosForm):
    base_data = _get_rotas_empresa(company, integradas=params.integradas, ativas=params.ativas)
    if not params or not params.search_term_list or len(base_data) == 0:
        return base_data

    search_list = params.search_term_list

    if params.is_search_by_id:
        return [x for x in base_data if str(x["rodoviaria_rota_id"]) in search_list]

    rotas = []
    for rota in base_data:
        checkpoints = reduce(
            lambda a, b: f"{a} | {b['local']['nickname'].upper()}, {b['local']['name'].upper()}",
            rota["checkpoints"],
            f"{rota['checkpoints'][0]['local']['nickname'].upper()}, {rota['checkpoints'][0]['local']['name'].upper()}",
        )
        for term in search_list:
            if checkpoints.find(term.upper()) >= 0:
                rotas.append(rota)
                break

    return rotas


def get_rotas_empresa_paginator(company_rodoviaria_id: int, params: ItinerariosNaoIntegradosForm):
    company = Company.objects.get(id=company_rodoviaria_id)
    itinerarios = _get_filtered_get_rotas_empresa(company, params)

    if params.paginator:
        if params.paginator.sort_by:
            itinerarios = _paginator_sort_by(itinerarios, params.paginator.sort_by, params.paginator.descending)
        if params.paginator.rows_per_page == -1:
            params.paginator.rows_per_page = len(itinerarios) if len(itinerarios) > 0 else 1
        paginator = Paginator(itinerarios, per_page=params.paginator.rows_per_page)
        page = paginator.get_page(params.paginator.page)
        response = {
            "company_internal_id": company.company_internal_id,
            "company_name": company.name,
            "items": page.object_list,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
        }
    else:
        response = {
            "company_internal_id": company.company_internal_id,
            "company_name": company.name,
            "items": itinerarios,
            "count": len(itinerarios),
            "num_pages": 1,
        }

    return response


def filtrar_apenas_checkpoints_integrados(checkpoints):
    checkpoints_apenas_integrados = []
    duracao_somada = timedelta()
    for c in checkpoints:
        if c.local and c.local.local_embarque_internal_id and c.local.cidade and c.local.cidade.cidade_internal:
            c.duracao = c.duracao + duracao_somada
            duracao_somada = timedelta()
            checkpoints_apenas_integrados.append(c)
        else:
            duracao_somada += c.duracao + c.tempo_embarque

    # conserta a duração do último chk integrado, caso seja diferente do último do parceiro pela API
    if checkpoints_apenas_integrados and checkpoints_apenas_integrados[-1] != checkpoints[-1]:
        ultimo_chk = checkpoints_apenas_integrados[-1]
        ultimo_chk.duracao += ultimo_chk.tempo_embarque
        ultimo_chk.tempo_embarque = timedelta(seconds=0)
        checkpoints_apenas_integrados[-1] = ultimo_chk

    # conserta a duração do primeiro chk integrado, caso seja diferente do primeiro do parceiro pela API
    if checkpoints_apenas_integrados and checkpoints_apenas_integrados[0] != checkpoints[0]:
        checkpoints_apenas_integrados[0].duracao = timedelta(seconds=0)
        checkpoints_apenas_integrados[0].tempo_embarque = timedelta(seconds=0)

    return checkpoints_apenas_integrados


def update_trechos_vendidos_internal_ids(rota_internal_id, trechos_internal_ids_map):
    # Atua sobre map de novos trechos de uma rota do buser_django {old_id_internal: new_id_internal}
    tvs_to_update = TrechoVendido.objects.select_related("rota").filter(
        rota__id_internal=rota_internal_id, id_internal__isnull=False
    )
    # Novo trecho id_internal = valor_do_map or None
    for tv in tvs_to_update:
        tv.id_internal = trechos_internal_ids_map.get(str(tv.id_internal))
        tv.updated_at = to_default_tz(datetime.now())

    TrechoVendido.objects.bulk_update(tvs_to_update, ["id_internal", "updated_at"])


def get_ids_rotas_ativas_empresa(company_internal_id, modelo_venda):
    if modelo_venda == Company.ModeloVenda.HIBRIDO:
        raise ValueError("Não é possível buscar rotas ativas para empresas hibridas")
    lista_rotas_ids = Rota.objects.filter(
        company__company_internal_id=company_internal_id,
        company__modelo_venda=modelo_venda,
        ativo=True,
        id_internal__isnull=False,
    ).values(
        rota_internal_id=F("id_internal"),
        rodoviaria_rota_id=F("id"),
    )
    map_rotas = defaultdict(list)
    for rota in lista_rotas_ids:
        map_rotas[rota["rota_internal_id"]].append(rota["rodoviaria_rota_id"])
    return map_rotas


@traced("rota_svc.get_rotas_ativas_para_reintegrar")
def get_rotas_ativas_para_reintegrar(company_internal_ids):
    buserlogger.info(
        "%s Buscando rotas ativas para reintegração para as empresas company_internal_ids=%s",
        AUTO_INTEGRAR_ROTAS_LOG_PREFIX,
        company_internal_ids,
    )
    # Busca todas as rotas integradas que tenham trecho vendido ativo e não integrado.
    rotas_para_forcar_reintegracao = get_lista_rotas_para_forcar_reintegracao()
    deve_forcar_reintegracao = Q(id__in=rotas_para_forcar_reintegracao)
    trecho_vendido_nao_integrado = Q(trechovendido__id_internal__isnull=True)
    filtro = Q(
        ativo=True,
        id_internal__isnull=False,
        company__company_internal_id__in=company_internal_ids,
        company__modelo_venda=Company.ModeloVenda.MARKETPLACE,
        trechovendido__ativo=True,
        trechovendido__origem__local_embarque_internal_id__isnull=False,
        trechovendido__destino__local_embarque_internal_id__isnull=False,
    ) & (trecho_vendido_nao_integrado | deve_forcar_reintegracao)
    rotas_ids = list(Rota.objects.filter(filtro).distinct("id").values_list("id", flat=True))
    buserlogger.info(
        "%s Rotas ativas encontradas para reintegração rotas_ids=%s", AUTO_INTEGRAR_ROTAS_LOG_PREFIX, rotas_ids
    )

    return get_detalhes_para_integrar_rotas(rotas_ids, retornar_id_internal=True)


def get_lista_rotas_para_forcar_reintegracao():
    rotas_para_forcar_reintegracao = constance.ROTAS_PARA_REINTEGRAR
    return [servico for servico in rotas_para_forcar_reintegracao.split(",") if servico]


def get_detalhes_para_integrar_uma_rota(rodoviaria_rota_id):
    list_detalhes = get_detalhes_para_integrar_rotas([rodoviaria_rota_id])
    if not list_detalhes:
        return {}

    detalhes = list_detalhes[0]

    # TODO: Temporario, para não quebrar contrato com o buser_django. Só até corrigir lá
    detalhes["checkpoints"] = detalhes["itinerario"]
    return detalhes


@traced("rota_svc.get_rotas_novas_empresas_para_integrar")
def get_rotas_novas_empresas_para_integrar(company_internal_ids):
    buserlogger.info(
        "%s Buscando rotas para integrar para as empresas company_internal_ids=%s",
        AUTO_INTEGRAR_ROTAS_LOG_PREFIX,
        company_internal_ids,
    )
    rotas_ids = list(
        Rota.objects.filter(
            ativo=True,
            id_internal__isnull=True,
            company__company_internal_id__in=company_internal_ids,
            company__modelo_venda=Company.ModeloVenda.MARKETPLACE,
            trechovendido__id_internal__isnull=True,
            trechovendido__ativo=True,
            trechovendido__origem__local_embarque_internal_id__isnull=False,
            trechovendido__destino__local_embarque_internal_id__isnull=False,
        )
        .distinct("id")
        .values_list("id", flat=True)
    )
    buserlogger.info("%s Rotas encontradas rotas_ids=%s", AUTO_INTEGRAR_ROTAS_LOG_PREFIX, rotas_ids)

    return get_detalhes_para_integrar_rotas(rotas_ids)


@traced("rota_svc.get_detalhes_para_integrar_rotas")
def get_detalhes_para_integrar_rotas(rotas_ids, retornar_id_internal=False):
    trechos_vendidos_por_rota = _get_map_trechos_vendidos_por_rota(rotas_ids)
    (
        map_itinerario_por_rota,
        ufs_intermediarios_por_rota,
    ) = _get_map_itinerario_e_ufs_intermediarios(rotas_ids)
    if retornar_id_internal:
        map_rodoviaria_rota_id_para_id_internal = {
            x[0]: x[1] for x in Rota.objects.filter(id__in=rotas_ids).values_list("id", "id_internal")
        }
    rotas = []
    for rota_id in rotas_ids:
        trechos_vendidos = trechos_vendidos_por_rota[rota_id]
        itinerario = map_itinerario_por_rota[rota_id]
        ufs_intermediarios = ufs_intermediarios_por_rota[rota_id]

        if len(itinerario) > 1 and trechos_vendidos:
            rota_integrar = {
                "rodoviaria_rota_id": rota_id,
                "itinerario": itinerario,
                "trechos_vendidos": trechos_vendidos,
                "ufs_intermediarios": ",".join(ufs_intermediarios),
            }
            if retornar_id_internal:
                rota_integrar["id_internal"] = map_rodoviaria_rota_id_para_id_internal[rota_id]

            rotas.append(rota_integrar)
            buserlogger.info("%s Rota pra integrar rota_integrar=%s", AUTO_INTEGRAR_ROTAS_LOG_PREFIX, rota_integrar)

    return rotas


def _get_map_trechos_vendidos_por_rota(rota_ids):
    buserlogger.info("%s Buscando trechos vendidos. rota_ids=%s", AUTO_INTEGRAR_ROTAS_LOG_PREFIX, rota_ids)
    trechos = list(
        TrechoVendido.objects.select_related("origem", "destino")
        .filter(
            rota_id__in=rota_ids,
            ativo=True,
            origem__local_embarque_internal_id__isnull=False,
            destino__local_embarque_internal_id__isnull=False,
        )
        .values(
            "id",
            "rota_id",
            "origem__local_embarque_internal_id",
            "destino__local_embarque_internal_id",
            "preco",
        )
        .distinct("rota_id", "origem_id", "destino_id")
    )
    trechos_vendidos_por_rota = defaultdict(list)
    for t in trechos:
        trechos_vendidos_por_rota[t["rota_id"]].append(
            {
                "rodoviaria_trecho_vendido_id": t["id"],
                "origem_id": t["origem__local_embarque_internal_id"],
                "destino_id": t["destino__local_embarque_internal_id"],
                "preco_rodoviaria": t["preco"],
            }
        )
    buserlogger.info(
        "%s Buscando trechos vendidos. trechos_vendidos_por_rota=%s",
        AUTO_INTEGRAR_ROTAS_LOG_PREFIX,
        trechos_vendidos_por_rota,
    )

    return trechos_vendidos_por_rota


@traced("rota_svc._get_map_itinerario_e_ufs_intermediarios")
def _get_map_itinerario_e_ufs_intermediarios(rotas_ids):
    buserlogger.info("%s Buscando itinerario. rotas_ids=%s", AUTO_INTEGRAR_ROTAS_LOG_PREFIX, rotas_ids)
    itinerario_por_rota = defaultdict(list)
    ufs_intermediarios_por_rota = defaultdict(list)

    checkpoints_por_rota = _get_checkpoints_por_rotas(rotas_ids)
    for rid in rotas_ids:
        checkpoints = checkpoints_por_rota.get(rid)
        if not checkpoints:
            continue
        checkpoints = atualizar_horarios_e_duracoes_por_timezone(checkpoints)
        checkpoints = filtrar_apenas_checkpoints_integrados(checkpoints)
        if not checkpoints:
            # caso rota não tenha nenhum checkpoint integrado
            continue

        uf_seen = set()
        ufs_intermediarios = []
        first_last_checkpoints_idx = [checkpoints[0].idx, checkpoints[-1].idx]
        for chkpoint in checkpoints:
            itinerario_por_rota[rid].append(chkpoint.to_dict_json(local=False))

            uf = chkpoint.local.cidade.cidade_internal.uf
            if chkpoint.idx not in first_last_checkpoints_idx and uf not in uf_seen:
                uf_seen.add(uf)
                ufs_intermediarios.append(uf)

        ufs_intermediarios_por_rota[rid] = ufs_intermediarios
    buserlogger.info(
        "%s Buscando Itinerario. itinerario_por_rota=%s ufs_intermediarios_por_rota=%s",
        AUTO_INTEGRAR_ROTAS_LOG_PREFIX,
        itinerario_por_rota,
        ufs_intermediarios_por_rota,
    )
    return itinerario_por_rota, ufs_intermediarios_por_rota


def _get_checkpoints_por_rotas(rotas_ids):
    checkpoints_por_rota = defaultdict(list)
    checkpoints = list(
        Checkpoint.objects.select_related("local", "local__cidade", "local__cidade__cidade_internal")
        .filter(rota_id__in=rotas_ids)
        .order_by("rota_id", "idx")
    )
    for c in checkpoints:
        checkpoints_por_rota[c.rota_id].append(c)

    return checkpoints_por_rota


def get_ids_rotas_integradas_inativas(company_internal_ids):
    active_rota_ids = set(
        Rota.objects.filter(
            company__company_internal_id__in=company_internal_ids,
            company__modelo_venda=Company.ModeloVenda.MARKETPLACE,
            ativo=True,
            id_internal__isnull=False,
        ).values_list("id_internal", flat=True)
    )

    return list(
        Rota.objects.filter(
            company__company_internal_id__in=company_internal_ids,
            company__modelo_venda=Company.ModeloVenda.MARKETPLACE,
            ativo=False,
            id_internal__isnull=False,
        )
        .exclude(id_internal__in=active_rota_ids)
        .values_list("id_internal", flat=True)
    )

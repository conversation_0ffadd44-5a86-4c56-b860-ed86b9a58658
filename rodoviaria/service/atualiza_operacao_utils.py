import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal as D

from beeline import traced
from celery.app import shared_task
from django.db import transaction
from django.utils import timezone

from bp.buserdjango_celery import atualiza_trecho_batch
from commons.celery_utils import DefaultQueueNames
from commons.dateutils import to_tz
from commons.memoize import memoize_with_log
from commons.redis import lock
from rodoviaria.api.forms import CheckpointsForm, TrechoVendidoAPI
from rodoviaria.api.praxio import models as models_praxio
from rodoviaria.api.ti_sistemas import models as models_ti_sistemas
from rodoviaria.models.core import (
    Checkpoint,
    Company,
    LocalEmbarque,
    Rota,
    Rotina,
    RotinaTrechoVendido,
    TaskStatus,
    TipoAssento,
    TrechoVendido,
)
from rodoviaria.service.class_match_svc import buser_class
from rodoviaria.service.create_local_embarque_cidade_svc import create_cidades_and_locais_from_itinerario

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE
MAX_PRECO_TRECHO = D("2500")
logger = logging.getLogger("rodoviaria")


def tv_unique_key(tv: TrechoVendido) -> tuple:
    # tv.classe == tv.tipo_assento_id
    # tv.classe é legado.
    return (tv.origem_id, tv.destino_id, tv.classe)


@traced("utils.descobrir_operacao._create_or_update_trechos_vendidos")
@lock("atualizando_ou_criando_trecho_vendido_{rota_id}")
def create_or_update_trechos_vendidos(
    rota_id: int, trechos_vendidos: list[TrechoVendido], rotinas_trecho_vendido: list[RotinaTrechoVendido]
):
    with transaction.atomic(using="rodoviaria"):
        trechos_vendidos_existentes_map = {
            tv_unique_key(tv): tv for tv in TrechoVendido.objects.select_for_update(nowait=True).filter(rota_id=rota_id)
        }
        # objetos são listados em dicionario para permitir remover duplicidades usando uma tupla como chave.
        to_create = {}
        to_update = {}
        for tv in trechos_vendidos:
            key = tv_unique_key(tv)
            trecho_vendido = trechos_vendidos_existentes_map.get(key)
            if trecho_vendido:
                tv.id = trecho_vendido.id
                tv.capacidade_classe = max(trecho_vendido.capacidade_classe or 0, tv.capacidade_classe or 0)
                if tv.status_preco == TrechoVendido.StatusPreco.PENDENTE:
                    # se API não retornou preço mas já tem cadastrado, muda status para OK
                    if trecho_vendido.preco:
                        tv.status_preco = TrechoVendido.StatusPreco.OK
                        tv.preco = trecho_vendido.preco
                        tv.ativo = D("0") < tv.preco < MAX_PRECO_TRECHO
                to_update[key] = tv
            else:
                to_create[key] = tv

        TrechoVendido.objects.bulk_create(to_create.values())
        TrechoVendido.objects.bulk_update(
            to_update.values(),
            [
                "distancia",
                "duracao",
                "preco",
                "updated_at",
                "ativo",
                "tipo_assento",
                "status_preco",
                "capacidade_classe",
            ],
        )
        RotinaTrechoVendido.objects.bulk_create(
            rotinas_trecho_vendido,
            update_conflicts=True,
            unique_fields=["trecho_vendido", "rotina"],
            update_fields=["datetime_ida_trecho_vendido"],
        )


@traced("utils.descobrir_operacao._create_or_update_trechos_vendidos_por_origem_e_destino")
@lock("atualizando_ou_criando_trecho_vendido_{rota_id}_{origem_id}_{destino_id}")
def create_or_update_trechos_vendidos_por_origem_e_destino(
    rota_id: int,
    trechos_vendidos: list[TrechoVendido],
    rotinas_trecho_vendido: list[RotinaTrechoVendido],
    origem_id: int,
    destino_id: int,
):
    with transaction.atomic(using="rodoviaria"):
        trechos_vendidos_existentes_map = {
            tv_unique_key(tv): tv
            for tv in TrechoVendido.objects.select_for_update(nowait=True).filter(
                rota_id=rota_id, origem=origem_id, destino=destino_id
            )
        }
        # objetos são listados em dicionario para permitir remover duplicidades usando uma tupla como chave.
        to_create = {}
        to_update = {}
        for tv in trechos_vendidos:
            key = tv_unique_key(tv)
            trecho_vendido = trechos_vendidos_existentes_map.get(key)
            if trecho_vendido:
                tv.id = trecho_vendido.id
                tv.capacidade_classe = max(trecho_vendido.capacidade_classe or 0, tv.capacidade_classe or 0)
                to_update[key] = tv
            else:
                to_create[key] = tv

        TrechoVendido.objects.bulk_create(to_create.values())
        TrechoVendido.objects.bulk_update(
            to_update.values(), ["distancia", "duracao", "preco", "updated_at", "ativo", "capacidade_classe"]
        )
        RotinaTrechoVendido.objects.bulk_create(
            rotinas_trecho_vendido,
            update_conflicts=True,
            unique_fields=["trecho_vendido", "rotina"],
            update_fields=["datetime_ida_trecho_vendido"],
        )


@traced("utils.descobrir_operacao._atualizar_ou_criar_rotina")
def atualizar_ou_criar_rotina(rota: Rota, datetime_ida: datetime, id_viagem, ativo: bool | None = None):
    datetime_ida = to_tz(datetime_ida, rota.first_checkpoint_timezone_or_default())

    update_fields = {"id_external": id_viagem}
    if ativo is not None:
        update_fields["ativo"] = ativo
    rotina, _ = Rotina.objects.update_or_create(rota=rota, datetime_ida=datetime_ida, defaults=update_fields)
    return rotina


@traced("utils.descobrir_operacao.inativa_rotas_e_rotinas_empresa")
def inativa_rotas_e_rotinas_empresa(company_id, data_inicial, data_final, inativa_trechos_vendidos=True):
    rotas = Rota.objects.filter(company_id=company_id, rotina__datetime_ida__range=[data_inicial, data_final])
    rotas.update(ativo=False, updated_at=timezone.now())
    Rotina.objects.filter(rota__in=rotas, datetime_ida__range=[data_inicial, data_final]).update(
        ativo=False, updated_at=timezone.now()
    )
    if inativa_trechos_vendidos:
        TrechoVendido.objects.filter(rota__in=rotas).update(ativo=False, updated_at=timezone.now())


@traced("utils.descobrir_operacao.atualizar_ou_criar_rota")
@lock("atualizando_ou_criando_rota_{itinerario.parsed.hash}")
def atualizar_ou_criar_rota(company_id, itinerario, id_viagem, create_locais=False, activate=False):
    update_fields = {
        "provider_data": json.dumps(itinerario.cleaned),
        "id_external": id_viagem,
        "updated_at": timezone.now(),
    }
    if activate:
        update_fields["ativo"] = True
    rota, created = Rota.objects.update_or_create(
        company_id=company_id,
        id_hash=itinerario.parsed.hash,
        defaults=update_fields,
    )
    if created:
        rota.ativo = False
        rota.save()
        create_checkpoints(rota, itinerario.parsed)
        if create_locais:
            create_cidades_and_locais_from_itinerario(itinerario.parsed, company_id)
    return rota


@traced("utils.descobrir_operacao.create_checkpoints")
def create_checkpoints(rota, itinerario):
    rota_checkpoints = []
    cksForm = CheckpointsForm.from_itinerario(
        rota.company_id,
        itinerario,
    )
    for idx, ck in enumerate(cksForm):
        c = Checkpoint(
            rota=rota,
            idx=idx,
            local_id=ck.local.rodoviaria_local_id,
            internal_id=None,
            arrival=ck.arrival,
            departure=ck.departure,
            distancia_km=ck.distancia_km,
            duracao=timedelta(seconds=ck.duracao),
            tempo_embarque=timedelta(seconds=ck.tempo_embarque),
            id_external=ck.local.id_external,
            uf=ck.local.uf,
            name=ck.local.name,
            nickname=ck.local.nickname,
        )
        rota_checkpoints.append(c)
    Checkpoint.objects.bulk_create(rota_checkpoints)


def inicia_task_status(task_name, company_id):
    TaskStatus.objects.update_or_create(
        task_name=task_name, company_id=company_id, defaults={"status": TaskStatus.Status.PENDING}
    )


def pre_inicia_task_status(task_name: TaskStatus.Status, company_id: int):
    TaskStatus.objects.update_or_create(
        task_name=task_name, company_id=company_id, defaults={"status": TaskStatus.Status.NOT_STARTED}
    )


def finaliza_task_status(task_name, company_id, failure=False):
    _now = timezone.now()
    update_fields = {
        "status": TaskStatus.Status.FAILURE if failure else TaskStatus.Status.SUCCESS,
        "last_finished_at": _now,
    }
    if not failure:
        update_fields["last_success_at"] = _now
    TaskStatus.objects.update_or_create(task_name=task_name, company_id=company_id, defaults=update_fields)


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def finisher_descobrir_operacao(company_id):
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company_id)


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def finisher_descobrir_operacao_on_error(context, exception, traceback, company_id):
    logger.error("Finishing descobrir operacao empresa %s with error", company_id)
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company_id, failure=True)


@memoize_with_log(timeout=12 * 60 * 60)
def get_map_locais_embarque_company(company_id: int):
    locais_embarque = LocalEmbarque.objects.filter(cidade__company_id=company_id).values(
        "id", "id_external", "local_embarque_internal_id", "cidade__cidade_internal__timezone"
    )

    return {local["id_external"]: local for local in locais_embarque}


@traced("utils.descobrir_operacao.create_trecho_vendido_obj")
def create_trecho_vendido_obj(
    rota_id: int,
    map_locais_embarque: dict[str, dict[str, str | int]],
    map_datetime_ida: dict[int, datetime],
    map_distancia: dict[int, int],
    tipo_assento_id: int,
    tv_api: TrechoVendidoAPI,
):
    duracao = map_datetime_ida[tv_api.destino_id] - map_datetime_ida[tv_api.origem_id]
    distancia = map_distancia[tv_api.destino_id] - map_distancia[tv_api.origem_id]
    status_preco = TrechoVendido.StatusPreco.OK if tv_api.preco else TrechoVendido.StatusPreco.PENDENTE
    ativo = bool(tv_api.preco) and D("0") < tv_api.preco < MAX_PRECO_TRECHO
    tv_buser = TrechoVendido(
        rota_id=rota_id,
        origem_id=map_locais_embarque[str(tv_api.origem_id)]["id"],
        destino_id=map_locais_embarque[str(tv_api.destino_id)]["id"],
        classe=tv_api.classe,
        capacidade_classe=tv_api.capacidade,
        distancia=distancia,
        duracao=duracao,
        preco=tv_api.preco,
        updated_at=timezone.now(),
        ativo=ativo,
        tipo_assento_id=tipo_assento_id,
        status_preco=status_preco,
    )

    return tv_buser


@traced("utils.descobrir_operacao.create_rotina_trecho_vendido_obj")
def create_rotina_trecho_vendido_obj(rotina: Rotina, tv_buser: TrechoVendido, datetime_ida_trecho: datetime):
    return RotinaTrechoVendido(
        trecho_vendido=tv_buser,
        rotina=rotina,
        datetime_ida_trecho_vendido=datetime_ida_trecho,
    )


@traced("utils.descobrir_operacao._get_or_create_tipos_assento")
def get_or_create_tipos_assentos(company: Company, trechos_vendidos_api: list[TrechoVendidoAPI]):
    classes_api = {tv.classe for tv in trechos_vendidos_api}
    tipos_assentos = [TipoAssento(company=company, tipo_assento_parceiro=classe) for classe in classes_api]
    TipoAssento.objects.bulk_create(tipos_assentos, ignore_conflicts=True)
    return (
        TipoAssento.objects.filter(company=company)
        .distinct("tipo_assento_parceiro")
        .in_bulk(field_name="tipo_assento_parceiro")
    )


@traced("utils.descobrir_operacao.get_map_datetime_ida_and_distancias")
def get_map_datetime_ida_and_distancias(
    itinerario: models_praxio.ListaPartidasTFO | models_ti_sistemas.Itinerario,
    map_locais_embarque: dict[str, dict[str, str]],
):
    distancia_total = 0
    map_datetime_ida, map_distancia = {}, {}
    for cp in itinerario:
        timezone = (
            map_locais_embarque[cp.local.external_local_id]["cidade__cidade_internal__timezone"] or "America/Sao_Paulo"
        )
        map_datetime_ida[int(cp.local.external_local_id)] = to_tz(cp.datetime_ida, timezone)
        distancia_total += cp.distancia if cp.distancia else 0
        map_distancia[int(cp.local.external_local_id)] = distancia_total
    return map_datetime_ida, map_distancia


@traced("utils.descobrir_operacao._atualizar_ou_criar_trechos_vendidos")
def atualizar_ou_criar_trechos_vendidos(
    company: Company,
    rotina: Rotina,
    trechos_vendidos_api: list[TrechoVendidoAPI],
    itinerario: models_praxio.ListaPartidasTFO | models_ti_sistemas.Itinerario,
):
    map_locais_embarque = get_map_locais_embarque_company(company.id)
    map_datetime_ida, map_distancia = get_map_datetime_ida_and_distancias(itinerario, map_locais_embarque)
    tipos_assento_map = get_or_create_tipos_assentos(company, trechos_vendidos_api)
    tvs_to_save = {}
    rtv_to_create = []
    raw_trechos = []
    rota_id = rotina.rota_id
    for tv_api in trechos_vendidos_api:
        datetime_ida_trecho = map_datetime_ida[tv_api.origem_id]
        datetime_chegada_trecho = map_datetime_ida[tv_api.destino_id]

        if datetime_ida_trecho > datetime_chegada_trecho:
            # pode acontecer de um endpoint retornar um trecho inverso ao itinerario (rota A->B tem trecho B->A)
            logger.info(
                "atualizar_ou_criar_trechos_vendidos.tv_inverso_ao_itinerario",
                extra={"company": company.id, "origem_api": tv_api.origem_id, "destino": tv_api.destino_id},
            )
            continue

        tipo_assento = tipos_assento_map[tv_api.classe]
        tv_buser = create_trecho_vendido_obj(
            rota_id, map_locais_embarque, map_datetime_ida, map_distancia, tipo_assento.id, tv_api
        )
        if tvs_to_save.get(tv_unique_key(tv_buser)) or tv_buser.origem_id == tv_buser.destino_id:
            # nao duplica RotinaTrechoVendido caso o TV já exista e não permite criar TV inválido.
            logger.info(
                "atualizar_ou_criar_trechos_vendidos.tv_duplicado",
                extra={"company": company.id, "origem": tv_buser.origem_id, "destino": tv_buser.destino_id},
            )
            continue
        tvs_to_save[tv_unique_key(tv_buser)] = tv_buser
        rtv_to_create.append(create_rotina_trecho_vendido_obj(rotina, tv_buser, datetime_ida_trecho))
        origem_internal_id = map_locais_embarque[str(tv_api.origem_id)]["local_embarque_internal_id"]
        destino_internal_id = map_locais_embarque[str(tv_api.destino_id)]["local_embarque_internal_id"]
        if (
            tv_buser.ativo
            and origem_internal_id is not None
            and destino_internal_id is not None
            and tv_api.preco is not None
        ):
            raw_trechos.append(
                {
                    "origem_internal_id": origem_internal_id,
                    "destino_internal_id": destino_internal_id,
                    "classe": tipo_assento.tipo_assento_buser_preferencial or buser_class(tv_api.classe),
                    "preco": tv_api.preco,
                    "datetime_ida": map_datetime_ida[tv_api.origem_id],
                    "vagas": tv_api.vagas,
                }
            )
    if tvs_to_save.values():
        if rotina.rota.ativo is False:
            rotina.rota.ativo = True
            rotina.rota.save()
        if rotina.ativo is False:
            rotina.ativo = True
            rotina.save()
    create_or_update_trechos_vendidos(rota_id, tvs_to_save.values(), rtv_to_create)
    if raw_trechos:
        atualiza_trecho_batch.delay(raw_trechos, company.company_internal_id)
    return tvs_to_save.values()


def log_descobrir_operacao_buscar_rota_servico(
    ota_name: str,
    id_viagem,
    len_trechos_vendidos,
    rotina_id,
    rotina_ativo,
    rota_id,
    company_id,
    datetime_ida_naive,
):
    logger.info(
        "[DESCOBRIR_OPERACAO][%s] ID %s com %s trechos vendidos",
        ota_name.upper(),
        id_viagem,
        len_trechos_vendidos,
        extra={
            "company_id": company_id,
            "rota_id": rota_id,
            "datetime_ida_naive": datetime_ida_naive,
            "rotina_id": rotina_id,
            "rotina_ativo": rotina_ativo,
        },
    )


def get_chunked_range_buscas(data_inicial, data_final, max_search_range=30) -> list[tuple]:
    diff = (data_final - data_inicial).days
    qtd_buscas = diff // max_search_range
    buscas = [data_inicial + timedelta(days=i * max_search_range) for i in range(qtd_buscas + 1)]
    if diff % max_search_range > 0:
        buscas += [data_final]
    tuplas_buscas = []
    for inicio, fim in zip(buscas, buscas[1:]):
        if inicio == data_inicial:
            tuplas_buscas.append((inicio, fim))
        else:
            tuplas_buscas.append((inicio + timedelta(days=1), fim))
    return tuplas_buscas

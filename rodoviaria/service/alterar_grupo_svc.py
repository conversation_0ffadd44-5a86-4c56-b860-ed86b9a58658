from celery import shared_task
from celery.utils.log import get_task_logger

from rodoviaria.service import cadastrar_grupos_hibridos_svc, cancelar_grupos_hibridos_svc
from rodoviaria.views_schemas import AlterarGrupoParams

task_logger = get_task_logger("escalar_onibus")


@shared_task(queue="bp_alterar_grupo")
def alterar_grupo_task(dict_params_escalar_veiculo):
    params_escalar_veiculo = AlterarGrupoParams.parse_raw(dict_params_escalar_veiculo)
    alterar_grupo(params_escalar_veiculo)


def alterar_grupo(params_alterar_grupo):
    (
        grupos_classe_vexado_cancelados,
        trechos_classe_rodoviaria_ids,
    ) = cancelar_grupos_hibridos_svc.cancelar_grupos_classe(
        params_alterar_grupo.company_id,
        params_alterar_grupo.grupos_classe_ids_antigos,
        call_task_cancelar=False,
    )
    if not grupos_classe_vexado_cancelados:
        return
    task_logger.info("Grupo %s cancelado", params_alterar_grupo.grupo.grupo_id)
    rota_external_id = grupos_classe_vexado_cancelados[0].rota_external_id
    cadastrar_grupos_hibridos_svc.CadastrarGrupoSVC(params_alterar_grupo.company_id, assincrono=False).cadastrar_grupos(
        [params_alterar_grupo.grupo], rota_external_id=rota_external_id
    )
    task_logger.info("Grupo %s recriado", params_alterar_grupo.grupo.grupo_id)
    cancelar_grupos_hibridos_svc.inativar_trechos_classe(trechos_classe_rodoviaria_ids)
    cancelar_grupos_hibridos_svc.inativar_grupos_classe_group_tasks(
        params_alterar_grupo.company_id,
        [gcv.grupo_classe_external_id for gcv in grupos_classe_vexado_cancelados],
    ).apply_async()

from datetime import datetime

from commons.dateutils import to_tz
from rodoviaria.models.core import TrechoClasseError
from rodoviaria.service import class_match_svc

MARGEM_DEPARTURE_MISMATCH_MINUTOS = 180


def motivo_servico_nao_encontrado(company, datetime_ida, timezone, tipo_assento, servicos_proximos):
    datetime_ida_trecho_classe = datetime_ida
    datetime_ida_trecho_classe = to_tz(datetime_ida_trecho_classe, timezone)
    servicos_proximos_parseados = []
    servicos_horario_proximo = []
    motivo = TrechoClasseError.Motivo.SEM_SERVICO
    for s in servicos_proximos:
        classe_servico = class_match_svc.get_buser_class_by_company(company, s["classe"])
        datetime_ida_servico = s["external_datetime_ida"]
        if isinstance(datetime_ida_servico, str):
            datetime_ida_servico = datetime.fromisoformat(datetime_ida_servico)
        difftime = (datetime_ida_servico - datetime_ida_trecho_classe).total_seconds() / 60
        servicos_proximos_parseados.append((datetime_ida_servico.strftime("%H:%M"), s["classe"]))
        if abs(difftime) <= MARGEM_DEPARTURE_MISMATCH_MINUTOS:
            servicos_horario_proximo.append((datetime_ida_servico.strftime("%H:%M"), s["classe"]))
        if motivo == TrechoClasseError.Motivo.MISMATCH_DE_CLASSE:
            continue
        match_de_horario = -1 < difftime < 30
        match_de_classe = classe_servico == tipo_assento
        if not match_de_horario and abs(difftime) <= MARGEM_DEPARTURE_MISMATCH_MINUTOS:
            motivo = TrechoClasseError.Motivo.MISMATCH_DE_HORARIO
        elif match_de_horario and not match_de_classe:
            motivo = TrechoClasseError.Motivo.MISMATCH_DE_CLASSE

    if motivo == TrechoClasseError.Motivo.SEM_SERVICO:
        return motivo, sorted(servicos_proximos_parseados, key=lambda k: k[0])
    return motivo, sorted(servicos_horario_proximo, key=lambda k: k[0])

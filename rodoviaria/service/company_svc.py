import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from celery import chain
from django.core.paginator import Paginator
from django.db.models import Q
from django.db.utils import IntegrityError
from django.utils import timezone

from commons.dateutils import DIAS_SEMANA, to_default_tz
from rodoviaria.api.guichepass import api as guichepass_api
from rodoviaria.api.smartbus import api as smartbus_api
from rodoviaria.api.ti_sistemas import api as ti_sistemas_api
from rodoviaria.api.totalbus import api as totalbus_api
from rodoviaria.api.vexado import api as vexado_api
from rodoviaria.models import (
    Company,
    EulabsLogin,
    GuichepassLogin,
    Integracao,
    PraxioLogin,
    SmartbusLogin,
    TiSistemasLogin,
    TotalbusLogin,
    VexadoLogin,
)
from rodoviaria.serializers.serializer_company import CompanySerializer
from rodoviaria.serializers.serializer_logins import (
    EulabsSerializer,
    GuichepassSerializer,
    PraxioSerializer,
    SmartbusSerializer,
    TiSistemasSerializer,
    TotalbusSerializer,
    VexadoSerializer,
)
from rodoviaria.service import (
    atualiza_operacao_empresa_svc,
    descobrir_rotas_svc,
    fetch_data_limite_rotas_svc,
    fetch_trechos_vendidos_svc,
    rotina_svc,
)
from rodoviaria.service.exceptions import (
    RodoviariaCompanyExistenteException,
    RodoviariaCompanyNotFoundException,
    RodoviariaCompanyNotIntegratedError,
    RodoviariaException,
    RodoviariaIntegracaoNotFoundException,
    RodoviariaLoginNotFoundException,
    RodoviariaUnableHardStop,
    RodoviariaUnableRevertHardStop,
    RodoviariaUnauthorizedError,
)
from rodoviaria.service.map_marketplace_cidades_svc import MapMarketplaceCidadesSVC

buserlogger = logging.getLogger("rodoviaria")

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE

LOGIN_FROM_IMPLEMENTED_APIS = {
    "totalbus": TotalbusLogin,
    "praxio": PraxioLogin,
    "vexado": VexadoLogin,
    "guichepass": GuichepassLogin,
    "eulabs": EulabsLogin,
    "smartbus": SmartbusLogin,
    "ti_sistemas": TiSistemasLogin,
}

LOGIN_SERIALIZERS = {
    "totalbus": TotalbusSerializer,
    "praxio": PraxioSerializer,
    "vexado": VexadoSerializer,
    "guichepass": GuichepassSerializer,
    "eulabs": EulabsSerializer,
    "smartbus": SmartbusSerializer,
    "ti_sistemas": TiSistemasSerializer,
}

FIRST_DAY_ROTINAS_TIMEDELTA = {
    "totalbus": lambda shift_days, next_days: timedelta(days=shift_days + next_days),
    "praxio": lambda shift_days, next_days: timedelta(days=shift_days + next_days),
    "vexado": lambda shift_days, next_days: timedelta(days=shift_days + next_days),
    "guichepass": lambda shift_days, next_days: timedelta(days=shift_days + next_days),
    "eulabs": lambda shift_days, next_days: timedelta(days=shift_days),
    "ti_sistemas": lambda shift_days, next_days: timedelta(days=shift_days + next_days),  # ?
}

PROVIDERS = {
    "totalbus": totalbus_api,
    "vexado": vexado_api,
    "guichepass": guichepass_api,
    "smartbus": smartbus_api,
    "ti_sistemas": ti_sistemas_api,
}


def _format_trigger_time(minute, hour, day):
    days = sorted(day.split(","))
    dias_da_semana = [DIAS_SEMANA[int(day)] for day in days]
    dias_da_semana_str = (
        f"{','.join(dias_da_semana[:-1])} e {dias_da_semana[-1]}" if len(dias_da_semana) > 1 else f"{dias_da_semana[0]}"
    )
    return f"{dias_da_semana_str} às {int(hour):02d}:{int(minute):02d}h"


def get_company_by_id(company_id, modelo_venda):
    return (
        Company.objects.filter(company_internal_id=company_id, modelo_venda=modelo_venda)
        .to_serialize(CompanySerializer)
        .first()
    )


def get_empresas(features=None, qs=None, integracoes: Optional[list[str]] = None):
    if qs is None:
        qs = Company.objects.select_related("integracao")

    if features:
        qs = qs.filter(features__contains=features)

    if integracoes:
        qs = qs.filter(integracao__name__in=integracoes)

    seen, multi_modelo = set(), set()
    for company in qs:
        if company.company_internal_id in seen:
            multi_modelo.add(company.company_internal_id)

        seen.add(company.company_internal_id)

    result = []
    for company in qs:
        minute, hour, day = atualiza_operacao_empresa_svc.descobrir_rotas_trigger_time(
            company.company_internal_id, company.modelo_venda, company.features, company.integracao.name
        )
        update_routes_trigger_time = None
        update_trechos_vendidos_trigger_time = None
        if minute:
            update_routes_trigger_time = _format_trigger_time(minute, hour, day)
        (
            minute,
            hour,
            day,
        ) = atualiza_operacao_empresa_svc.fetch_trechos_vendidos_trigger_time(
            company.company_internal_id, company.modelo_venda, company.features, company.integracao.name
        )
        if minute:
            update_trechos_vendidos_trigger_time = _format_trigger_time(minute, hour, day)

        company_name = company.name
        if company.company_internal_id in multi_modelo:
            company_name += f" ({company.modelo_venda.title()})"

        result.append(
            {
                "id": company.pk,
                "nome": company_name,
                "company_internal_id": company.company_internal_id,
                "modelo_venda": company.modelo_venda,
                "integracao": company.integracao.name,
                "features": company.features,
                "update_routes_trigger_time": update_routes_trigger_time,
                "update_trechos_vendidos_trigger_time": update_trechos_vendidos_trigger_time,
                "max_percentual_divergencia": company.max_percentual_divergencia,
            }
        )

    return result


def get_filtered_companies(name=None, status=None, modelo_venda=None, order_by=None):
    qs = Company.objects.select_related("integracao")
    if name:
        qs = qs.filter(Q(name__icontains=name))
    if modelo_venda:
        qs = qs.filter(modelo_venda=modelo_venda)
    if status == "ativas":
        qs = qs.filter(Q(features__icontains="active"))
    if status == "inativas":
        qs = qs.filter(~Q(features__icontains="active"))
    if order_by:
        qs = qs.order_by(order_by)
    return list(qs)


def companies_paginator(name, status, modelo_venda, rows_per_page, page, order_by):
    companies_qs = get_filtered_companies(name=name, status=status, modelo_venda=modelo_venda, order_by=order_by)

    if not rows_per_page or rows_per_page == -1:
        rows_per_page = len(companies_qs) if len(companies_qs) > 0 else 1
    paginator = Paginator(companies_qs, per_page=rows_per_page)
    page = paginator.get_page(page)

    companies = get_empresas(qs=page.object_list)
    response = {
        "empresas": list(companies),
        "count": paginator.count,
        "num_pages": paginator.num_pages,
    }
    return response


def get_all_possible_features(is_staff=False):
    if is_staff:
        return [
            Company.Feature.ACTIVE,
            Company.Feature.BPE,
            Company.Feature.ATUALIZAR_PRECO_CHECKOUT,
            Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ,
            Company.Feature.AUTO_INTEGRA_OPERACAO,
            Company.Feature.RISK_SHARE,
            Company.Feature.ASSENTOS_OCIOSOS,
        ]
    return Company.Feature.values


def add_default_features(features):
    return {
        Company.Feature.ADD_PAX_STAFF,
        Company.Feature.BUSCAR_SERVICO,
        Company.Feature.ITINERARIO,
    }.union(features if features else set())


def update_or_create_login(empresa, api, login_data):
    if not login_data:
        buserlogger.info(
            "[MAP_INTEGRATION] A empresa %s não teve o login criado para %s, porque se tratar de uma"
            " integração em fase de mapeamento",
            empresa.name,
            api,
        )
        return False
    empresa.company_external_id = None
    empresa.url_base = LOGIN_FROM_IMPLEMENTED_APIS[api].DEFAULT_URL_BASE

    if "company_external_id" in login_data:
        empresa.company_external_id = login_data.pop("company_external_id")
    if "url_base" in login_data:
        empresa.url_base = login_data.pop("url_base")

    if empresa.integracao.name == Integracao.API.VEXADO:
        login_base = VexadoLogin.objects.filter(company__modelo_venda=empresa.modelo_venda).first()
        login_data["user"] = login_base.user
        login_data["password"] = login_base.password

    empresa.save()
    _, login_created = LOGIN_FROM_IMPLEMENTED_APIS[api].objects.update_or_create(company=empresa, defaults=login_data)
    buserlogger.info("[MAP_INTEGRATION] Login criado/atualizado para a %s com novos dados %s", empresa.name, login_data)
    return login_created


def create_empresa(
    name,
    company_internal_id,
    modelo_venda,
    features,
    login_data,
    api,
    max_percentual_divergencia,
):
    if login_data:
        features = list(add_default_features(features))
    else:
        features = []

    integracao, _ = Integracao.objects.update_or_create(name=api, defaults={"versao": "1.0"})
    try:
        company = Company.objects.create(
            company_internal_id=company_internal_id,
            modelo_venda=modelo_venda,
            integracao=integracao,
            name=name,
            features=features,
            max_percentual_divergencia=max_percentual_divergencia,
        )
    except IntegrityError as exc:
        raise RodoviariaCompanyExistenteException(
            company_internal_id=company_internal_id, modelo_venda=modelo_venda
        ) from exc
    update_or_create_login(empresa=company, api=api, login_data=dict(login_data))

    if login_data:
        try:
            fetch_operacao_empresa_marketplace(company_internal_id=company_internal_id)
        except NotImplementedError:
            pass
        except (RodoviariaUnauthorizedError, RodoviariaException):
            company.delete()
            raise
    return {"success": True}


def update_empresa(
    name,
    company_internal_id,
    modelo_venda,
    features,
    login_data,
    api,
    max_percentual_divergencia,
):
    if login_data:
        features = list(add_default_features(features))
    else:
        features = list(features)

    integracao, _ = Integracao.objects.get_or_create(name=api)
    try:
        company = Company.objects.get(
            company_internal_id=company_internal_id,
            modelo_venda=modelo_venda,
        )
    except Company.DoesNotExist as exc:
        raise RodoviariaCompanyNotFoundException from exc
    company.integracao = integracao
    company.features = features
    company.name = name
    company.max_percentual_divergencia = max_percentual_divergencia
    company.save()

    update_or_create_login(empresa=company, api=api, login_data=dict(login_data))


def hard_stop_empresa(
    company_internal_id,
    modelo_venda,
):
    try:
        company = Company.objects.get(
            company_internal_id=company_internal_id,
            modelo_venda=modelo_venda,
        )
    except Company.DoesNotExist as exc:
        raise RodoviariaCompanyNotFoundException from exc
    if company.features == []:
        raise RodoviariaUnableHardStop(nome_empresa=company.name)
    company.previous_features = company.features
    company.previous_features_updated_at = timezone.now()
    company.features = []
    company.save(update_fields=["features", "previous_features", "previous_features_updated_at"])


def revert_hard_stop_empresa(
    company_internal_id,
    modelo_venda,
):
    try:
        company = Company.objects.get(
            company_internal_id=company_internal_id,
            modelo_venda=modelo_venda,
        )
    except Company.DoesNotExist as exc:
        raise RodoviariaCompanyNotFoundException from exc
    if not company.previous_features:
        raise RodoviariaUnableRevertHardStop(nome_empresa=company.name)

    company.features = company.previous_features
    company.previous_features_updated_at = timezone.now()
    company.previous_features = []
    company.save(update_fields=["features", "previous_features", "previous_features_updated_at"])


def get_login(integracao, company_id, modelo_venda):
    try:
        login = (
            LOGIN_FROM_IMPLEMENTED_APIS[integracao]
            .objects.to_serialize(LOGIN_SERIALIZERS[integracao])
            .get(company__pk=company_id, company__modelo_venda=modelo_venda)
        )
    except KeyError as exc:
        raise RodoviariaIntegracaoNotFoundException(integracao=integracao) from exc
    except LOGIN_FROM_IMPLEMENTED_APIS[integracao].DoesNotExist as exc:
        raise RodoviariaLoginNotFoundException(
            integracao=integracao, company_pk=company_id, modelo_venda=modelo_venda
        ) from exc
    return login.serialize()


def fetch_operacao_empresa_marketplace(company_internal_id):
    company = Company.objects.select_related("integracao").get(
        company_internal_id=company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA
    )
    try:
        MapMarketplaceCidadesSVC(company_internal_id).execute()
    except RodoviariaUnauthorizedError:
        raise
    except RodoviariaException:
        pass

    next_days = 14
    shift_days = 1

    try:
        descobrir_rotas_chain = descobrir_rotas_svc.descobrir_rotas_proximos_dias(
            company=company,
            next_days=next_days,
            shift_days=shift_days,
            return_task_object=True,
        )
    except RodoviariaException:
        raise
    except NotImplementedError:
        return

    if company.integracao.name == Integracao.API.VEXADO:
        descobrir_rotas_chain()
        return {"success": True}

    fetch_trechos_vendidos_group = fetch_trechos_vendidos_svc.fetch_trechos_by_company_id.si(
        company_internal_id=company_internal_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    first_day_rotinas = to_default_tz(
        datetime.now() + FIRST_DAY_ROTINAS_TIMEDELTA[company.integracao.name](shift_days, next_days)
    )
    fetch_rotinas_group = rotina_svc.fetch_rotinas_empresa.si(
        company_internal_id=company_internal_id,
        first_day=first_day_rotinas,
        next_days=next_days + 3,
    )
    fetch_trechos_vendidos_finisher = fetch_trechos_vendidos_svc.finisher.si()

    fetch_data_limite_rotas = fetch_data_limite_rotas_svc.fetch_data_limite_rotas.si(
        company_internal_id=company_internal_id,
    )
    """
    O principal aqui é que os comandos de trechos e rotinas rodem após as rotas forem criadas
    entre group tasks, precisa de finisher para esperar as tasks serem consumidas de um grupo pra outro
    https://stackoverflow.com/questions/15123772/celery-chaining-groups-and-subtasks-out-of-order-execution.
    Isso não espera as tasks acabarem de serem executadas, somente consumidas
    """
    chain(
        descobrir_rotas_chain,
        fetch_trechos_vendidos_group,
        fetch_trechos_vendidos_finisher,
        fetch_data_limite_rotas,
        fetch_rotinas_group,
    )()


def lista_empresas_api(login_params):
    return PROVIDERS[login_params.integracao].lista_empresas_api(login_params)


def raise_if_unable_hard_stop(empresa):
    if empresa.previous_features and "active" in empresa.previous_features and empresa.features == []:
        return True
    if not empresa.previous_features and "active" not in empresa.features:
        raise RodoviariaCompanyNotIntegratedError(empresa.name)
    if not empresa.previous_features and empresa.features:
        raise RodoviariaUnableRevertHardStop(empresa.name)

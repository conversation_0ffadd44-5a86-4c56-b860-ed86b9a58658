import logging

from celery import shared_task
from constance import config as constance_config

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.vexado.utils import fill_duracao
from rodoviaria.data.transbrasil import MAP_CLASSES_VEXADO
from rodoviaria.models import Cidade, CidadeInternal, Company, Grupo, Passagem, Rota, VexadoRota
from rodoviaria.views_schemas import (
    AtualizarLocaisBatchParams,
    AtualizarLocaisParams,
    CadastrarCheckpointParams,
    CadastrarItinerarioParams,
    CadastrarOuEditarRotaHibridoParams,
    CadastrarRotaHibridoParams,
    TrechoParams,
)

buserlogger = logging.getLogger("rodoviaria")

DURACAO_NONE = (None, "00:00", "0:00", "0:0")
DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO


def cadastrar_trechos(company_id, trechos):
    cidades_locais_ids = []
    for trecho in trechos:
        cidades_locais_ids.append((trecho.cidade_origem_id, trecho.local_origem_id))
        cidades_locais_ids.append((trecho.cidade_destino_id, trecho.local_destino_id))

    MAP_CIDADES = cadastrar_cidades(company_id, cidades_locais_ids)

    if not MAP_CIDADES:
        return

    for trecho in trechos:
        trecho.classe = MAP_CLASSES_VEXADO[trecho.classe.lower()]
        trecho.cidade_origem_id = MAP_CIDADES[trecho.cidade_origem_id]
        trecho.cidade_destino_id = MAP_CIDADES[trecho.cidade_destino_id]
    return cadastrar_trechos_task.delay(company_id, [trecho.json() for trecho in trechos])


@shared_task(
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.CADASTROS_VEXADO,
)
def cadastrar_trechos_task(company_id, trechos):
    return OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA).cadastrar_trechos(
        [TrechoParams.parse_raw(trecho) for trecho in trechos]
    )


def cadastrar_cidades(company_id, cidades_locais_ids):
    cidades_ids = [c[0] for c in cidades_locais_ids]
    cidades_cadastradas = Cidade.objects.filter(
        cidade_internal_id__in=cidades_ids,
        company__company_internal_id=company_id,
        company__modelo_venda=DEFAULT_MODELO_VENDA,
    ).values_list("cidade_internal_id", flat=True)
    company = _get_company_transbrasil(company_id)
    if company is None:
        return {}

    cadastrados = []
    for cidade_id, local_id in cidades_locais_ids:
        if cidade_id not in cidades_cadastradas and cidade_id not in cadastrados:
            cadastrar_cidade(company, cidade_id, local_id)
            cadastrados.append(cidade_id)
    return {
        c.cidade_internal_id: c.id_external
        for c in Cidade.objects.filter(
            company__company_internal_id=company_id,
            company__modelo_venda=DEFAULT_MODELO_VENDA,
        )
    }


def cadastrar_cidade(company, id_cidade, id_local_embarque):
    cidade_internal = CidadeInternal.objects.get(id=id_cidade)
    return OrchestrateRodoviaria(
        company.company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA
    ).match_local_de_embarque(
        id_local_embarque,
        id_cidade,
        cidade_internal.timezone,
        cidade_internal.name,
        cidade_internal.city_code_ibge,
        company,
        company.company_internal_id,
    )


def criar_itinerario(params: CadastrarItinerarioParams, MAP_CIDADES=None):
    if params.id_rota_internal in _get_rotas_fixas_hibrido():
        return
    if params.company_id in (1365, 1364, 1361):
        return
    if MAP_CIDADES is None:
        MAP_CIDADES = {}
    buserlogger.info("criar_itinerario: %s", params)
    company_id = params.company_id
    cidades_locais_ids = []
    for checkpoint in params.checkpoints:
        cidades_locais_ids.append((checkpoint.cidade_destino_id, checkpoint.local_embarque_id))

    if hasattr(params, "cidade_destino_id"):
        cidades_locais_ids.append((params.cidade_destino_id, None))

    MAP_CIDADES = {**MAP_CIDADES, **cadastrar_cidades(company_id, cidades_locais_ids)}

    responses = []
    ponto_embarque_anterior = "*"
    orchestrator = OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    for checkpoint in params.checkpoints:
        checkpoint.id_rota_external = params.id_rota_external
        checkpoint.cidade_destino_id = MAP_CIDADES[checkpoint.cidade_destino_id]
        checkpoint.ponto_embarque, ponto_embarque_anterior = (
            ponto_embarque_anterior,
            checkpoint.ponto_embarque,
        )
        if checkpoint.duracao in DURACAO_NONE:
            checkpoint.tempo_total = 0
        responses.append(orchestrator.criar_checkpoint(checkpoint))

    if hasattr(params, "cidade_destino_id"):
        responses.append(
            orchestrator.criar_checkpoint(
                CadastrarCheckpointParams(
                    cidade_destino_id=MAP_CIDADES[params.cidade_destino_id],
                    ponto_embarque=ponto_embarque_anterior,
                    id_rota_external=params.id_rota_external,
                    local_embarque_id=0,
                )
            )
        )

    salva_vexado_rota(
        params.company_id,
        params.id_rota_internal,
        params.id_rota_external,
    )
    return responses


def _tempo_atualizado(local_embarque):
    if local_embarque.duracao in DURACAO_NONE:
        return "00:00"
    return fill_duracao(local_embarque.tempo_total)


def atualizar_locais_embarque(params: AtualizarLocaisParams):
    if params.id_rota_internal in _get_rotas_fixas_hibrido():
        return
    if params.company_id in (1365, 1364, 1361):
        return
    msg = f"Atualizar_locais_embarque: {params.checkpoints}"
    buserlogger.info(msg)
    orchestrator = OrchestrateRodoviaria(params.company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    itinerario = orchestrator.listar_trechos(params.id_rota_external)
    locais_map = {}
    locais_existentes = {}
    for local in itinerario:
        cidade_key = local["cidadeDestino"]["nome"].lower().strip()
        locais_map[cidade_key] = local
        locais_existentes[cidade_key] = False

    cidades = CidadeInternal.objects.filter(id__in=[local.cidade_destino_id for local in params.checkpoints])
    cidades_map = {cidade.id: cidade for cidade in cidades}

    full_response = []
    nao_existentes = []
    cidade_key = None
    for local_embarque in params.checkpoints:
        cidade_key_anterior = cidade_key
        cidade = cidades_map[local_embarque.cidade_destino_id]
        cidade_key = cidade.name.lower().strip()
        locais_existentes[cidade_key] = True
        if cidade_key in locais_map and (
            locais_map[cidade_key]["pontoEmbarque"] != local_embarque.ponto_embarque
            or locais_map[cidade_key]["duracao"] != _tempo_atualizado(local_embarque)
            or int(locais_map[cidade_key]["quilometragem"].split(",")[0]) != int(local_embarque.km or 0)
        ):
            response = orchestrator.atualizar_checkpoint(
                locais_map[cidade_key],
                local_embarque.ponto_embarque,
                _tempo_atualizado(local_embarque),
                int(local_embarque.km or 0),
            )
            full_response.append(response)
        elif cidade_key not in locais_map:
            nao_existentes.append((local_embarque, cidade_key_anterior))

    for cidade, existe in locais_existentes.items():
        if not existe:
            response = orchestrator.atualizar_checkpoint(locais_map[cidade], None, "00:00")
            full_response.append(response)
    if nao_existentes:
        _adicionar_checkpoints(params, nao_existentes)
    salva_vexado_rota(
        params.company_id,
        params.id_rota_internal,
        params.id_rota_external,
    )
    atualizar_delimitacao_rota(
        params.company_id,
        params.id_rota_internal,
        params.id_rota_external,
    )
    return full_response


def _atualizar_rota_ids(old_rota_id, new_rota_id):
    rotas = Rota.objects.filter(id_internal=old_rota_id, ativo=True)
    rotas.update(id_internal=new_rota_id)


def atualizar_delimitacao_rota(company_id, rota_internal_id, rota_external_id):
    orchestrator = OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    rotas = orchestrator.buscar_rotas()
    rota = next(r for r in rotas if str(r["id"]) == str(rota_external_id))
    editar_delimitacao_rota(orchestrator, rota, rota_internal_id)


def editar_delimitacao_rota(orchestrator, rota, delimitacao):
    return orchestrator.editar_rota(
        rota["id"],
        delimitacao,
        rota["cidade_origem_id"],
        rota["cidade_destino_id"],
        rota["prefixo"],
    )


def inativar_rota_delimitacao(orchestrator, rota):
    return orchestrator.inativar_rota(
        rota["id"],
        rota["cidade_origem_id"],
        rota["cidade_destino_id"],
        rota["prefixo"],
    )


def atualizar_locais_embarque_batch(params: AtualizarLocaisBatchParams):
    if params.id_rota_internal in _get_rotas_fixas_hibrido():
        return
    if params.company_id in (1365, 1364, 1361):
        return
    _atualizar_rota_ids(params.old_rota_id, params.id_rota_internal)
    full_response = []
    rotas = VexadoRota.objects.filter(rota_internal_id=params.old_rota_id)
    for rota in rotas:
        params.company_id = rota.company.company_internal_id
        params.id_rota_external = rota.rota_external_id
        response = atualizar_locais_embarque(params)
        full_response.append(response)
    return full_response


def _adicionar_checkpoints(AtualizarLocaisParams, checkpoints_nao_existentes):
    for local_embarque, cidade_anterior_key in checkpoints_nao_existentes:
        adicionar_checkpoint_itinerario(AtualizarLocaisParams, local_embarque, cidade_anterior_key)


def adicionar_checkpoint_itinerario(AtualizarLocaisParams, local_embarque, cidade_anterior_key):
    company_id = AtualizarLocaisParams.company_id
    id_rota_external = AtualizarLocaisParams.id_rota_external
    local_embarque.id_rota_external = id_rota_external
    local_embarque.cidade_destino_id = get_cidade_id(
        company_id, local_embarque.cidade_destino_id, local_embarque.local_embarque_id
    )
    if local_embarque.duracao in DURACAO_NONE:
        local_embarque.tempo_total = 0
    orchestrator = OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    checkpoints_external = criar_checkpoint_e_listar_trechos(orchestrator, local_embarque, id_rota_external)
    n, trecho_external_id = get_trecho_id_and_n_shifts(checkpoints_external, cidade_anterior_key)
    for _ in range(n):
        orchestrator.mover_posicao_checkpoint_para_baixo(trecho_external_id)
    return True


def get_trecho_id_and_n_shifts(checkpoints_external, cidade_anterior_key):
    trecho_external_id = checkpoints_external[0]["id"]
    if not cidade_anterior_key:
        return len(checkpoints_external) - 2, trecho_external_id
    n = -1
    for ckp in checkpoints_external:
        cidade_key = ckp["cidadeDestino"]["nome"].lower().strip()
        if cidade_key == cidade_anterior_key:
            break
        n += 1
    return n, trecho_external_id


def criar_checkpoint_e_listar_trechos(orchestrator, local_embarque, id_rota_external):
    orchestrator.criar_checkpoint(local_embarque)
    itinerario = orchestrator.listar_trechos(id_rota_external)
    return itinerario


def get_cidade_id(company_id, id_cidade, id_local_embarque):
    cidade = Cidade.objects.filter(
        company__company_internal_id=company_id,
        company__modelo_venda=DEFAULT_MODELO_VENDA,
        cidade_internal_id=id_cidade,
    ).first()
    if not cidade:
        company = Company.objects.get(company_internal_id=company_id, modelo_venda=DEFAULT_MODELO_VENDA)
        cidade = cadastrar_cidade(company, id_cidade, id_local_embarque)
    return cidade.id_external


def _cidades_origem_e_destino(params):
    company_id = params.company_id
    cidades_locais_ids = []
    cidades_locais_ids.append((params.cidade_origem_id, None))
    cidades_locais_ids.append((params.cidade_destino_id, None))

    return cadastrar_cidades(company_id, cidades_locais_ids)


def cadastrar_rota(params: CadastrarRotaHibridoParams):
    if params.id_rota_internal in _get_rotas_fixas_hibrido():
        return
    if params.company_id in (1365, 1364, 1361):
        return
    company_id = params.company_id
    MAP_CIDADES = _cidades_origem_e_destino(params)

    OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA).cadastrar_rota(
        MAP_CIDADES[params.cidade_origem_id],
        MAP_CIDADES[params.cidade_destino_id],
        params.prefixo,
        params.id_rota_internal,
    )
    rotas_cadastradas = OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA).buscar_rotas()
    id_rota = None
    for rota in rotas_cadastradas:
        if str(rota["delimitacao"]) == str(params.id_rota_internal):
            id_rota = rota["id"]
            break

    params.id_rota_external = id_rota
    salva_vexado_rota(company_id, params.id_rota_internal, params.id_rota_external)
    return criar_itinerario(params, MAP_CIDADES)


def cadastrar_ou_editar_rota(params: CadastrarOuEditarRotaHibridoParams):
    if params.id_rota_internal in _get_rotas_fixas_hibrido():
        return
    if params.company_id in (1365, 1364, 1361):
        return
    if params.id_rota_external:
        rota_cadastrada = salva_vexado_rota(params.company_id, params.id_rota_internal, params.id_rota_external)
    else:
        rota_cadastrada = VexadoRota.objects.filter(
            company__company_internal_id=params.company_id,
            rota_internal_id=params.id_rota_internal,
        ).first()
    if rota_cadastrada and rota_cadastrada.rota_external_id:
        params.id_rota_external = rota_cadastrada.rota_external_id
        return editar_rota(params)
    return match_or_create_rota(params)


def sincronizar_rota(params: CadastrarRotaHibridoParams):
    if params.id_rota_internal in _get_rotas_fixas_hibrido():
        return False
    if params.company_id in (1365, 1364, 1361):
        return
    company = _get_company_transbrasil(params.company_id)
    if company is None:
        return False
    if company.integracao.name.lower() != "vexado":
        return False
    orchestrator = OrchestrateRodoviaria.from_company(company)
    rotas_api = orchestrator.buscar_rotas()
    rota_api = _find_rota_by_delimitacao(rotas_api, params.id_rota_internal)
    if not rota_api:
        return cadastrar_rota(params)
    rota_verificada = verificar_e_atualizar_rota(params, rota_api)
    if not rota_verificada:
        inativar_rota_delimitacao(orchestrator, rota_api)
        return cadastrar_rota(params)
    return rota_verificada


def _find_rota_by_delimitacao(rotas_api, rota_internal_id):
    for rota in rotas_api:
        if str(rota["delimitacao"]) == str(rota_internal_id):
            return rota


def _map_cidades_api(rota_params: CadastrarOuEditarRotaHibridoParams):
    cidades_locais_ids = [
        (rota_params.cidade_origem_id, None),
        (rota_params.cidade_destino_id, None),
    ]
    for c in rota_params.checkpoints:
        cidades_locais_ids.append((c.cidade_destino_id, c.local_embarque_id))
    return cadastrar_cidades(rota_params.company_id, cidades_locais_ids)


def verificar_e_atualizar_rota(rota_params: CadastrarOuEditarRotaHibridoParams, rota_api):
    orchestrator = OrchestrateRodoviaria(rota_params.company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    MAP_CIDADES = _map_cidades_api(rota_params)
    rota_api_itinerario = sorted(rota_api["itinerario"], key=lambda k: k["ordem"])
    if len(rota_params.checkpoints) == len(rota_api_itinerario) - 1:
        # possivelmente falta o destino da autorização
        _cadastra_destino(
            orchestrator,
            rota_params.checkpoints[-1].ponto_embarque,
            MAP_CIDADES[rota_params.cidade_destino_id],
            rota_api["id"],
        )
        rota_api_itinerario = _get_sorted_rota_api(orchestrator, rota_api["id"])
    # verifica se o tamanho das rota na API é dois checkpoints maior que na buser
    # pois na API devem estar origem e destino da LOP como checkpoints
    if len(rota_params.checkpoints) != len(rota_api_itinerario) - 2:
        return False
    to_update = []
    origem_mismatch = str(rota_api_itinerario[0]["cidadeDestino"]["id"]) != str(
        MAP_CIDADES[rota_params.cidade_origem_id]
    )
    if origem_mismatch:
        return False

    buser_checkpoints = rota_params.checkpoints
    # loop igora primeiro checkpoint da rota_api pq ele sempre vai ser padrão
    for index, api_checkpoint in enumerate(rota_api_itinerario[1:]):
        (
            expected_duracao,
            expected_distancia,
            expected_cidade_destino_id,
        ) = _expected_props(rota_params, rota_api_itinerario, buser_checkpoints, index)

        city_mismatch = str(api_checkpoint["cidadeDestino"]["id"]) != str(MAP_CIDADES[expected_cidade_destino_id])
        if city_mismatch:
            return False

        expected_ponto_embarque = buser_checkpoints[index - 1].ponto_embarque if index > 0 else "*"
        if not _match_checkpoint(
            api_checkpoint,
            expected_ponto_embarque,
            expected_duracao,
            expected_distancia,
        ):
            to_update.append(
                (
                    api_checkpoint,
                    expected_ponto_embarque,
                    expected_duracao,
                    expected_distancia,
                )
            )
    for local_api, ponto_embarque, duracao, km in to_update:
        _atualiza_checkpoint(orchestrator, local_api, ponto_embarque, duracao, km)
    return True


def _expected_props(rota_params, rota_api_itinerario, buser_checkpoints, index):
    if index == len(rota_api_itinerario[1:]) - 1:
        expected_duracao = "00:00"
        expected_distancia = 0
        cidade_destino_id = rota_params.cidade_destino_id
    else:
        expected_duracao = _tempo_atualizado(buser_checkpoints[index])
        expected_distancia = int(buser_checkpoints[index].km or 0)
        cidade_destino_id = buser_checkpoints[index].cidade_destino_id
    return expected_duracao, expected_distancia, cidade_destino_id


def _get_sorted_rota_api(orchestrator: OrchestrateRodoviaria, id_rota_external):
    rota_api = orchestrator.listar_trechos(id_rota_external)
    return sorted(rota_api, key=lambda k: k["ordem"])


def _match_checkpoint(api_checkpoint, expected_ponto_embarque, expected_duracao, expected_distancia):
    return (
        api_checkpoint["pontoEmbarque"] == expected_ponto_embarque
        and api_checkpoint["duracao"] == expected_duracao
        and int(api_checkpoint["quilometragem"].split(",")[0]) == expected_distancia
    )


def _atualiza_checkpoint(orchestrator: OrchestrateRodoviaria, local_api, ponto_embarque, duracao, km):
    return orchestrator.atualizar_checkpoint(local_api, ponto_embarque, duracao, km)


def _cadastra_destino(
    orchestrator: OrchestrateRodoviaria,
    ponto_embarque,
    cidade_external_id,
    id_rota_external,
):
    orchestrator.criar_checkpoint(
        CadastrarCheckpointParams(
            cidade_destino_id=cidade_external_id,
            ponto_embarque=ponto_embarque,
            id_rota_external=id_rota_external,
            local_embarque_id=0,
        )
    )


def match_or_create_rota(params):
    orchestrator = OrchestrateRodoviaria(params.company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    _cidades_origem_e_destino(params)
    rotas_api = orchestrator.buscar_rotas()
    for rota in rotas_api:
        if str(rota["delimitacao"]) == str(params.id_rota_internal):
            params.id_rota_external = rota["id"]
            return editar_rota(params)
    return cadastrar_rota(params)


def editar_rota(params: CadastrarOuEditarRotaHibridoParams):
    if params.id_rota_internal in _get_rotas_fixas_hibrido():
        return
    if params.company_id in (1365, 1364, 1361):
        return
    company_id = params.company_id
    _cidades_origem_e_destino(params)
    cidade_origem = Cidade.objects.get(
        cidade_internal_id=params.cidade_origem_id,
        company__company_internal_id=company_id,
        company__modelo_venda=DEFAULT_MODELO_VENDA,
    )
    cidade_destino = Cidade.objects.get(
        cidade_internal_id=params.cidade_destino_id,
        company__company_internal_id=company_id,
        company__modelo_venda=DEFAULT_MODELO_VENDA,
    )
    OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA).editar_rota(
        params.id_rota_external,
        params.id_rota_internal,
        cidade_origem.id_external,
        cidade_destino.id_external,
        params.prefixo,
    )
    atualizar_linhas(
        params.grupo_ids,
        f"{str(cidade_origem.cidade_internal)} à {str(cidade_destino.cidade_internal)}",
        params.prefixo,
    )
    salva_vexado_rota(company_id, params.id_rota_internal, params.id_rota_external)


def atualizar_linhas(grupo_ids, nova_linha, novo_prefixo):
    Grupo.objects.filter(grupo_internal_id__in=grupo_ids).update(linha=nova_linha)
    Passagem.objects.filter(trechoclasse_integracao__grupo__grupo_internal_id__in=grupo_ids).update(
        prefixo=novo_prefixo
    )


def salva_vexado_rota(company_id, id_rota_internal, id_rota_external):
    company = Company.objects.get(company_internal_id=company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    obj, _created = VexadoRota.objects.update_or_create(
        company=company,
        rota_external_id=id_rota_external,
        defaults={"rota_internal_id": id_rota_internal},
    )
    return obj


def verifica_link_rotas(rotas_internas):
    companies_ids = [r.company_id for r in rotas_internas]
    rotas_links = VexadoRota.objects.filter(company__company_internal_id__in=companies_ids)
    rotas_links = list(rotas_links.values_list("rota_internal_id", "company__company_internal_id"))
    response = {}
    for r in rotas_internas:
        if (r.rota_id, r.company_id) in rotas_links:
            response[str((r.rota_id, r.company_id))] = "ok"
            continue
        response[str((r.rota_id, r.company_id))] = "link nao cadastrado"
    return response


def _get_company_transbrasil(company_internal_id):
    try:
        company = Company.objects.select_related("integracao").get(
            company_internal_id=company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA
        )
        return company
    except Company.DoesNotExist:
        buserlogger.info(
            "_get_company_transbrasil.company_does_not_exist",
            extra={"company_internal_id": company_internal_id, "modelo_venda": DEFAULT_MODELO_VENDA},
        )
        return None


def _get_rotas_fixas_hibrido():
    rotas_fixas = constance_config.ROTAS_FIXAS_HIBRIDO
    return [int(servico) for servico in rotas_fixas.split(",") if servico]

from collections import defaultdict

from django.core.paginator import Paginator
from django.db.models import F, Q, QuerySet
from django.db.models.aggregates import Count

from rodoviaria.models.core import Passagem
from rodoviaria.views_schemas import PassagensPorEmpresaParams


def get_passagens_empresa(data: PassagensPorEmpresaParams):
    total_rows = 0
    qs = (
        Passagem.objects.select_related("company_integracao", "trechoclasse_integracao")
        .filter(
            company_integracao__company_internal_id=data.company_id,
            status__in=(Passagem.Status.CANCELADA, Passagem.Status.CONFIRMADA, Passagem.Status.DEVOLVIDA),
        )
        .order_by("travel_internal_id", "buseiro_internal_id", "trechoclasse_integracao__datetime_ida")
    )
    if data.start_date_saida and data.end_date_saida:
        qs = qs.filter(
            trechoclasse_integracao__datetime_ida__date__gte=data.start_date_saida,
            trechoclasse_integracao__datetime_ida__date__lte=data.end_date_saida,
        )

    if data.start_date_compra_e_cancelamento and data.end_date_compra_e_cancelamento:
        qs = qs.filter(
            Q(
                created_at__date__gte=data.start_date_compra_e_cancelamento,
                created_at__date__lte=data.end_date_compra_e_cancelamento,
            )
            | Q(
                datetime_cancelamento__date__gte=data.start_date_compra_e_cancelamento,
                datetime_cancelamento__date__lte=data.end_date_compra_e_cancelamento,
            )
            | Q(
                trechoclasse_integracao__datetime_ida__date__gte=data.start_date_compra_e_cancelamento,
                trechoclasse_integracao__datetime_ida__date__lte=data.end_date_compra_e_cancelamento,
            )
        )

    if data.travel_ids:
        qs = qs.filter(travel_internal_id__in=data.travel_ids)

    if data.status_passagens:
        qs = qs.filter(status=data.status_passagens)

    fator_conexao_by_localizador = _get_fator_conexao_by_localizador(qs)

    if data.current_page and data.items_per_page:
        qs, total_rows = _passagens_paginator(data.current_page, data.items_per_page, qs)

    list_passagens = _get_list_from_queryset_passagens(qs)
    return list_passagens, fator_conexao_by_localizador, total_rows


def _get_fator_conexao_by_localizador(qs: QuerySet):
    travels_repetidas = list(
        qs.filter(origem__isnull=False, destino__isnull=False)
        .values_list("travel_internal_id", "buseiro_internal_id")
        .order_by("travel_internal_id", "buseiro_internal_id")
        .annotate(count_id=Count("id"))
        .filter(count_id__gt=1)
        .distinct()
    )
    travel_ids_repetidas = [x[0] for x in travels_repetidas]
    buseiro_ids_repetidas = [x[1] for x in travels_repetidas]
    passagens_list = list(
        qs.filter(
            travel_internal_id__in=travel_ids_repetidas,
            buseiro_internal_id__in=buseiro_ids_repetidas,
        ).values(
            "travel_internal_id",
            "buseiro_internal_id",
            "localizador",
            "preco_rodoviaria",
            "origem",
            "destino",
        )
    )
    grouped_passagens = defaultdict(dict)
    fator_passagens_conexao = {}
    for p in passagens_list:
        list_passagens = grouped_passagens[(p["travel_internal_id"], p["buseiro_internal_id"])]

        # Se o destino de P é a origem da outra passagem, então a outra passagem é a segunda perna. P é a primeira perna
        segunda_perna = list_passagens.get(f"origem-{p['destino']}", None)
        # Se o origem de P é a destino da outra passagem, então a outra passagem é a primeira perna. P é a segunda perna
        primeira_perna = list_passagens.get(f"destino-{p['origem']}", None)
        # Se algum dos 2 foi encontrado, ela é a outra perna da conexão de P
        outra_perna = segunda_perna or primeira_perna

        if outra_perna:
            total = p["preco_rodoviaria"] + outra_perna["preco_rodoviaria"]
            fator_passagens_conexao[p["localizador"]] = p["preco_rodoviaria"] / total
            fator_passagens_conexao[outra_perna["localizador"]] = outra_perna["preco_rodoviaria"] / total
            continue
        if p["destino"]:
            list_passagens[f"destino-{p['destino']}"] = p
        if p["origem"]:
            list_passagens[f"origem-{p['origem']}"] = p

    return fator_passagens_conexao


def _get_list_from_queryset_passagens(qs):
    return list(
        qs.annotate(
            company_id=F("company_integracao__company_internal_id"),
            datetime_compra=F("created_at"),
            poltrona=F("poltrona_external_id"),
            status_rodoviaria=F("status"),
        ).values(
            "company_id",
            "travel_internal_id",
            "buseiro_internal_id",
            "status_rodoviaria",
            "localizador",
            "numero_passagem",
            "preco_rodoviaria",
            "poltrona",
            "datetime_compra",
            "datetime_cancelamento",
            "erro_cancelamento",
        )
    )


def _passagens_paginator(current_page, items_per_page, pax_qs):
    paginator = Paginator(object_list=pax_qs, per_page=items_per_page)
    page = paginator.get_page(current_page)
    total_rows = paginator.count

    return page.object_list, total_rows

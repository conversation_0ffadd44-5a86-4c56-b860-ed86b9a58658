import logging
from collections import defaultdict
from datetime import timedelta

from celery import shared_task
from celery.utils.log import get_task_logger
from django.db.models import Min

from commons.celery_utils import DefaultQueueNames
from commons.dateutils import to_tz, today_midnight
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Rota, Rotina
from rodoviaria.service.auto_integra_operacao.duracao_checkpoint_linkado_svc import (
    get_duracao_checkpoint_ate_primeiro_linkado_por_rota,
)
from rodoviaria.service.exceptions import (
    RodoviariaRotaNotFoundException,
)

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")


DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE
DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTINAS
DIAS_SEMANA = ["seg", "ter", "qua", "qui", "sex", "sab", "dom"]


def _parse_hits(datetime_idas):
    hits = {}
    for d in datetime_idas:
        dia = DIAS_SEMANA[d.weekday()]
        data = d.strftime("%Y-%m-%d")
        hora = d.strftime("%H:%M")
        if hora not in hits:
            hits[hora] = [{"dia": dia, "datas": [data]}]
        else:
            match_hora_dia = next((h for h in hits[hora] if dia == h.get("dia")), None)
            if not match_hora_dia:
                hits[hora].append({"dia": dia, "datas": [data]})
            else:
                match_hora_dia["datas"].append(data)

    for hora in hits:
        hits[hora] = sorted(hits[hora], key=lambda x: DIAS_SEMANA.index(x["dia"]))

    return hits


def _get_rotina_response(rota, datetime_idas_tz):
    hits = _parse_hits(datetime_idas_tz)
    resp = {
        "empresa": rota.company.name,
        "empresa_internal_id": rota.company.company_internal_id,
        "id_hash": rota.id_hash,
        "id_internal": rota.id_internal,
        "id_external": rota.id_external,
        "rodoviaria_rota_id": rota.id,
        "rotina": hits,
    }
    return resp


def _fetch_rotina(rota, next_days, first_day):
    orchestrator = OrchestrateRodoviaria(rota.company.company_internal_id)
    datetime_idas_tz = orchestrator.fetch_rotina(rota, next_days, first_day)
    resp = _get_rotina_response(rota, datetime_idas_tz)
    last_day = first_day + timedelta(days=next_days - 1)
    resp["intervalo_busca"] = {
        "primeiro": first_day.strftime("%Y-%m-%d"),
        "ultimo": last_day.strftime("%Y-%m-%d"),
    }
    logger.info("Rotina para rodoviaria_rota_id %s encontrada: %s", rota.id, resp)
    return resp


def _get_rotinas_tz(rota, next_days):
    first_day = Rotina.objects.filter(rota_id=rota.id, ativo=True, datetime_ida__gte=today_midnight()).aggregate(
        Min("datetime_ida")
    )["datetime_ida__min"]

    if not first_day:
        return None, None, None

    last_day = first_day + timedelta(days=next_days - 1)
    rotinas = Rotina.objects.filter(rota_id=rota.id, datetime_ida__range=[first_day, last_day], ativo=True).order_by(
        "datetime_ida"
    )
    first_checkpoint_timezone = rota.first_checkpoint_timezone_or_default()
    for r in rotinas:
        r.datetime_ida = to_tz(r.datetime_ida, first_checkpoint_timezone)
    return rotinas, first_day, last_day


def _get_datetime_idas_tz(rota, next_days):
    rotinas, first_day, last_day = _get_rotinas_tz(rota, next_days)
    if rotinas is None:
        return None, None, None
    datetime_idas_tz = [d.datetime_ida for d in rotinas]
    return datetime_idas_tz, first_day, last_day


def _get_rotina(rota, next_days):
    datetime_idas_tz, first_day, last_day = _get_datetime_idas_tz(rota, next_days)
    if datetime_idas_tz is None:
        return _get_rotina_response(rota, [])
    else:
        resp = _get_rotina_response(rota, datetime_idas_tz)
        resp["intervalo_busca"] = {
            "primeiro": first_day.strftime("%Y-%m-%d"),
            "ultimo": last_day.strftime("%Y-%m-%d"),
        }
        return resp


def _fetch_rotina_async(rota, next_days, first_day):
    orchestrator = OrchestrateRodoviaria(rota.company.company_internal_id)
    queue_name = orchestrator.provider.queue_name or DEFAULT_QUEUE_NAME
    task = orchestrator.fetch_rotina_async(rota, next_days, first_day, queue_name)
    task_info = {
        "task_id": task.id,
        "queue": queue_name,
        "mensagem": f"Fetch rotina assíncrono da rota {rota.id_internal=}",
    }
    return task_info


def fetch_rotina_by_internal_id(rota_id, next_days=14, first_day=None, async_fetch=False):
    if not first_day or first_day < today_midnight():
        first_day = today_midnight()
    try:
        rota = Rota.objects.select_related("company").get(
            id_internal=rota_id, company__modelo_venda=DEFAULT_MODELO_VENDA
        )
    except Rota.DoesNotExist as exc:
        raise RodoviariaRotaNotFoundException from exc
    if async_fetch:
        return _fetch_rotina_async(rota, next_days, first_day)
    return _fetch_rotina(rota, next_days, first_day)


def fetch_rotina(rodoviaria_rota_id, next_days=14, first_day=None, async_fetch=False):
    if not first_day or first_day < today_midnight():
        first_day = today_midnight()
    try:
        rota = Rota.objects.select_related("company").get(pk=rodoviaria_rota_id)
    except Rota.DoesNotExist as exc:
        raise RodoviariaRotaNotFoundException from exc
    if async_fetch:
        return _fetch_rotina_async(rota, next_days, first_day)
    return _fetch_rotina(rota, next_days, first_day)


def get_rotina_by_internal_id(rota_id, next_days=30):
    try:
        rota = Rota.objects.select_related("company").get(
            id_internal=rota_id, company__modelo_venda=DEFAULT_MODELO_VENDA
        )
    except Rota.DoesNotExist as exc:
        raise RodoviariaRotaNotFoundException from exc
    return _get_rotina(rota, next_days)


def get_rotina(rodoviaria_rota_id, next_days=30):
    try:
        rota = Rota.objects.select_related("company").get(pk=rodoviaria_rota_id)
    except Rota.DoesNotExist as exc:
        raise RodoviariaRotaNotFoundException from exc
    return _get_rotina(rota, next_days)


@shared_task(queue=DEFAULT_QUEUE_NAME)
def fetch_rotinas_empresa(company_internal_id, first_day=None, next_days=30):
    orchestrator = OrchestrateRodoviaria(company_internal_id)
    queue_name = orchestrator.provider.queue_name or DEFAULT_QUEUE_NAME
    task = orchestrator.fetch_rotinas_empresa(next_days, first_day, queue_name)
    task_info = {
        "task_id": task.id,
        "queue": queue_name,
        "mensagem": f"Fetch rotinas assíncrono de empresa {company_internal_id}",
    }
    return task_info


def get_rotinas_inativas_por_rota_integrada_ativa(company_internal_ids):
    rotinas_inativas = list(
        Rotina.objects.filter(
            rota__company__company_internal_id__in=company_internal_ids,
            rota__company__modelo_venda=Company.ModeloVenda.MARKETPLACE,
            rota__ativo=True,
            rota__id_internal__isnull=False,
            ativo=False,
        ).values("rota_id", "rota__id_internal", "datetime_ida")
    )
    rotas_ids = [r["rota_id"] for r in rotinas_inativas]
    shift_time_ate_linkado_por_rota = get_duracao_checkpoint_ate_primeiro_linkado_por_rota(rotas_ids)

    map_por_rota = defaultdict(list)
    for r in rotinas_inativas:
        datetime_ida = r["datetime_ida"]
        duracao_ate_linkado = shift_time_ate_linkado_por_rota.get(r["rota_id"], timedelta(seconds=0))
        datetime_ida_linkado = datetime_ida + duracao_ate_linkado

        rota_internal_id = r["rota__id_internal"]
        map_por_rota[rota_internal_id].append(datetime_ida_linkado)

        logger.info(
            "Rotina inativa encontrada para rota_internal_id=%s. "
            "(datetime_ida=%s + duracao_ate_linkado=%s = datetime_ida_linkado=%s)",
            rota_internal_id,
            datetime_ida,
            duracao_ate_linkado,
            datetime_ida_linkado,
        )
    return map_por_rota

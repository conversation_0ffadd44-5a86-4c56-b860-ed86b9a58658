import hashlib
from collections import namedtuple
from datetime import datetime, time, timed<PERSON>ta

from django.utils import timezone

from rodoviaria.models.core import Company, CronogramaAtualizacaoOperacao, Integracao, Rota, TaskStatus

# company_internal_id: CronData(weekday, hour, minute, company_name)
# weekday = (0: <PERSON>, 1: <PERSON>, 2: <PERSON><PERSON><PERSON>, ..., 6: Sabad<PERSON>)

CronData = namedtuple("CronData", ["weekday", "hour", "minute", "name"])
FETCH_TV_SCHEDULE = {
    6247: CronData("3", "8", "0", "Itapemirim"),
    6297: <PERSON>ron<PERSON>ata("6", "10", "0", "Expresso Marly"),
    6272: <PERSON><PERSON><PERSON><PERSON>("1", "12", "0", "Viação Verde"),
    321: <PERSON>ron<PERSON>ata("3", "3", "30", "Viação Estrela"),
    13: <PERSON>ron<PERSON><PERSON>("1", "8", "0", "Levare"),
    6210: <PERSON><PERSON><PERSON><PERSON>("0", "8", "0", "JamJoy"),
    1021: <PERSON><PERSON><PERSON><PERSON>("6", "14", "0", "Val<PERSON>"),
    333: <PERSON><PERSON><PERSON><PERSON>("0", "8", "0", "<PERSON> <PERSON> <PERSON><PERSON>o"),
    445: <PERSON><PERSON>D<PERSON>("5", "12", "0", "Guerino Seiscento"),
    1196: CronData("5", "13", "50", "Viação Pluma"),
    244: <PERSON>ronData("2", "7", "40", "J<PERSON> <PERSON>rism<PERSON>"),
    44: <PERSON>ronData("2", "9", "0", "<PERSON>xor <PERSON>rism<PERSON>"),
    6293: CronData("2", "8", "30", "Viação J Araujo"),
    448: CronData("2", "13", "0", "Transpiauí"),
    6219: CronData("5", "7", "0", "Lopesul"),
}

HOUR_CHOICES = [18, 19, 20, 21, 22, 23, 0, 1, 2, 3, 4, 5, 6]


def has_feature(features, feature):
    if not features:
        return False
    return feature in features


def integracao_descobre_operacao_de_uma_vez(integracao_name):
    return integracao_name in (
        Integracao.API.VEXADO,
        Integracao.API.TI_SISTEMAS,
        Integracao.API.PRAXIO,
        Integracao.API.EULABS,
    )


def ops_trigger_time(company_internal_id, modelo_venda):
    if not company_internal_id:
        return None, None, None
    if not modelo_venda == Company.ModeloVenda.MARKETPLACE:
        return None, None, None

    empresa_com_agendamento_definido = FETCH_TV_SCHEDULE.get(company_internal_id)
    if empresa_com_agendamento_definido:
        day, hour, minute, _ = empresa_com_agendamento_definido
        return minute, hour, day

    hash_key_hexa = hashlib.md5(  # noqa: S324
        str(company_internal_id).encode()
    ).hexdigest()
    hash_key_dec = int(hash_key_hexa, 16)
    day = hash_key_dec % 7
    hour = HOUR_CHOICES[hash_key_dec % len(HOUR_CHOICES)]
    minute = hash_key_dec % 60
    return str(minute), str(hour), str(day)


def descobrir_rotas_trigger_time(company_internal_id, modelo_venda, features, integracao_name):
    if not has_feature(features, Company.Feature.ITINERARIO) or integracao_descobre_operacao_de_uma_vez(
        integracao_name
    ):
        return None, None, None
    minute, hour, day = ops_trigger_time(company_internal_id, modelo_venda)
    if minute is None:
        return minute, hour, day
    day = _days_ops(
        day,
        high_freq=has_feature(features, Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ),
    )
    return minute, hour, day


def fetch_rotas_trigger_time(company_internal_id, modelo_venda, features, integracao_name):
    if not has_feature(features, Company.Feature.ITINERARIO) or integracao_descobre_operacao_de_uma_vez(
        integracao_name
    ):
        return None, None, None
    minute, hour, day = ops_trigger_time(company_internal_id, modelo_venda)
    if minute is None:
        return minute, hour, day
    day = _days_ops(
        day,
        offset_days=1,
        high_freq=has_feature(features, Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ),
    )
    return minute, hour, day


def fetch_trechos_vendidos_trigger_time(company_internal_id, modelo_venda, features, integracao_name):
    if not has_feature(features, Company.Feature.BUSCAR_SERVICO) or integracao_descobre_operacao_de_uma_vez(
        integracao_name
    ):
        return None, None, None
    minute, hour, day = ops_trigger_time(company_internal_id, modelo_venda)
    if minute is None:
        return minute, hour, day
    day = _days_ops(
        day,
        offset_days=2,
        high_freq=has_feature(features, Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ),
    )
    return minute, hour, day


def fetch_data_limite_rotas_trigger_time(company_internal_id, modelo_venda, features, integracao_name):
    return fetch_rotas_trigger_time(company_internal_id, modelo_venda, features, integracao_name)


def fetch_all_operation_trigger_time(company_internal_id, modelo_venda, features, integracao_name):
    if not has_feature(features, Company.Feature.ITINERARIO) or not integracao_descobre_operacao_de_uma_vez(
        integracao_name
    ):
        return None, None, None
    minute, hour, day = ops_trigger_time(company_internal_id, modelo_venda)
    if minute is None:
        return minute, hour, day
    return minute, hour, "*"


def get_empresas_integracao_automatica_rodar_hoje():
    today_of_week = datetime.now().weekday()
    empresas = Company.objects.filter(
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        features__contains=[
            Company.Feature.AUTO_INTEGRA_OPERACAO,
            Company.Feature.ACTIVE,
        ],
    ).values_list("company_internal_id", "features")
    ids_empresas_hoje = []
    for _id, features in empresas:
        pode_rodar_hoje = today_of_week in _integracao_automatica_trigger_days_of_week(_id, features)
        if pode_rodar_hoje and not existe_atualizacao_em_execucao(_id):
            ids_empresas_hoje.append(_id)
    return ids_empresas_hoje


def get_dia_semana_e_margem_horarios():
    """retorna a margem de horários de 10 minutos com base no horário atual

    O limite inferior da margem é arredondado para 0:00 e o superior, para 9:59, exemplo:
    hora atual = 16:45 -> retorno = 16:40:00 e 16:49:59

    Dessa forma, cada horário redondo só existirá em uma única margem de tempo

    Return: (day_of_week, init_margem, end_margem)
    """

    now = datetime.now()
    day_of_week = now.isoweekday()
    start_minute = (now.minute // 10) * 10
    stop_minute = start_minute + 9
    init = time(hour=now.hour, minute=start_minute, second=0)
    end = time(hour=now.hour, minute=stop_minute, second=59)
    return day_of_week, init, end


def get_empresas_integracao_automatica_agora():
    day_of_week, time_init, time_end = get_dia_semana_e_margem_horarios()
    horarios = CronogramaAtualizacaoOperacao.objects.select_related("company").filter(
        dia_semana=day_of_week,
        horario__range=[time_init, time_end],
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA,
        company__modelo_venda=Company.ModeloVenda.MARKETPLACE,
        company__features__contains=[Company.Feature.AUTO_INTEGRA_OPERACAO],
    )
    return [h.company.company_internal_id for h in horarios]


def _integracao_automatica_trigger_days_of_week(company_internal_id, features) -> list[int]:
    _, _, day = ops_trigger_time(company_internal_id, Company.ModeloVenda.MARKETPLACE)
    if day is None:
        return []

    day = _days_ops(
        day,
        offset_days=3,
        high_freq=has_feature(features, Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ),
    )
    return list(map(int, day.split(",")))


def _days_ops(dow: str, offset_days: int = 0, high_freq: bool = False) -> str:
    """
    retorna um conjunto de dias pra rodar a atualização num formato legível pelo cron
    com as opções de colocar um offset em dias e se atualização é com alta frequência
    """
    days = [f"{(int(dow) + offset_days) % 7}"]
    if high_freq:
        days.append(f"{(int(dow) + offset_days + 3) % 7}")
    return ",".join(days)


def existe_atualizacao_em_execucao(company_internal_id):
    return TaskStatus.objects.filter(
        status=TaskStatus.Status.PENDING,
        company__company_internal_id=company_internal_id,
    ).exists()


def qtd_rotas_por_empresa(company: Company):
    qtd_rotas = Rota.objects.filter(
        company=company,
        ativo=True,
        rotina__ativo=True,
        rotina__datetime_ida__lt=timezone.now() + timedelta(days=company.margem_dias_busca_operacao),
    ).count()
    return qtd_rotas

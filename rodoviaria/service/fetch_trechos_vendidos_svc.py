import logging
from decimal import Decimal as D

from celery.app import shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import (
    Company,
    LocalEmbarque,
    Rota,
    TrechoVendido,
)
from rodoviaria.service.rota_svc import itinerario_parsed_data

from . import fetch_trechos_vendidos_svc_v2 as v2

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")

DEFAULT_QUEUE_NAME = DefaultQueueNames.TRECHOS_VENDIDOS
DEFAULT_RATE_LIMIT = DefaultRateLimits.TrechosVendidos.RATE_LIMIT
DEFAULT_LOW_RATE_LIMIT = DefaultRateLimits.TrechosVendidos.LOW_RATE_LIMIT
DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE

MAX_PRECO_TRECHO = D("2500")


def fetch_trechos_vendidos():
    # - para cada rota
    #     - verificar se precisa atualizar os trechos_vendidos da rota
    #     - se sim
    #         - pegar o primeiro grupo que ainda vai acontecer
    #         - verificar se é totalbus
    #         - pegar o primeiro trecho integrado
    #         - pegar o itinerario
    #         - para cada combinação de (origem, destino) chamar uma task para buscar o trecho vendido
    rotas = Rota.objects.all()
    for rota in rotas:
        limit_time = timezone.now() - TrechoVendido.EXPIRATION_TIME
        trechos_vendidos = list(rota.trechovendido_set.all()) or []
        needs_update = rota.trechovendido_set.filter(updated_at__lte=limit_time).exists()
        if trechos_vendidos and not needs_update:
            continue
        grupos = rota.grupo_set.filter(datetime_ida__gt=timezone.now())
        if grupos:
            grupo = grupos.first()
            if grupo.company_integracao.integracao.name not in {"totalbus"}:
                continue
            fetch_trecho_vendido_uma_rota(rota)


def fetch_trecho_vendido_uma_rota(rota, return_task_object=False):
    company = rota.company
    orchestrator = OrchestrateRodoviaria(
        company_internal_id=company.company_internal_id, modelo_venda=company.modelo_venda
    )
    itinerario = itinerario_parsed_data(orchestrator, rota_id=rota.id)

    locais_external_ids = [cp.local.external_local_id for cp in itinerario]
    locais = LocalEmbarque.objects.select_related("cidade").filter(
        cidade__company=company, id_external__in=locais_external_ids
    )
    locais_timezone = {str(local.id_external): local.cidade.timezone for local in locais}
    timezone_origem = locais_timezone.get(str(itinerario[0].local.external_local_id)) or "America/Sao_Paulo"
    return v2.async_fetch_trechos_vendidos(
        locais_timezone,
        itinerario,
        rota,
        company,
        timezone_origem,
        orchestrator.provider.queue_name or DEFAULT_QUEUE_NAME,
        return_task_object,
    )


@shared_task(queue=DEFAULT_QUEUE_NAME)
def fetch_trechos_by_company_id(company_internal_id, modelo_venda):
    rotas = (
        Rota.objects.select_related("company")
        .filter(
            company__company_internal_id=company_internal_id,
            company__modelo_venda=modelo_venda,
            ativo=True,
        )
        .order_by("id")
    )

    for rota in rotas:
        task = fetch_trecho_vendido_uma_rota(rota=rota, return_task_object=True)
        v2.dispara_tasks.delay(
            {
                "rota_id": rota.id,
                "company_id": rota.company_id,
                "task": task.task,
                "args": task.args,
                "kwargs": task.kwargs,
                "options": task.options,
                "subtask_type": task.subtask_type,
                "immutable": task.immutable,
            }
        )


@shared_task(queue=DEFAULT_QUEUE_NAME)
def finisher():
    task_logger.info("Finalizando fetch de trechos vendidos")

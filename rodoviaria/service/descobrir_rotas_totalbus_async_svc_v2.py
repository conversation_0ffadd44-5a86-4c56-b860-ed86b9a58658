import json
import logging
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta

from celery import chain, group
from celery.app import shared_task
from celery.utils.log import get_task_logger
from django.db.models import Prefetch
from django.utils import timezone
from pydantic import parse_obj_as

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import midnight, to_tz, today_midnight
from commons.memoize import memoize_with_log
from commons.redis import lock
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.forms import CheckpointsForm
from rodoviaria.api.totalbus import endpoints as endpoints
from rodoviaria.api.totalbus import models as models_totalbus
from rodoviaria.models.core import Checkpoint, Company, Rota, Rotina, TaskStatus
from rodoviaria.service.atualiza_operacao_utils import finaliza_task_status
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
)
from rodoviaria.service.novos_modelos_svc import filter_servicos_novos_modelos
from rodoviaria.service.salva_rotas_bulk_svc import inativa_rotas_sem_rotinas, inativa_rotinas_nao_atualizadas

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")

DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTAS
DEFAULT_RATE_LIMIT = DefaultRateLimits.Totalbus.ROTAS
LOG_PREFIX = "[descobrir_rotas_totalbus_async]"
BUCKET_SIZE = int(DEFAULT_RATE_LIMIT.split("/")[0])


class LocalidadeNaoEncontrada(Exception):
    pass


def descobrir_rotas(
    client,
    company_internal_id,
    modelo_venda,
    next_days=7,
    shift_days=2,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    if client.company.company_external_id:
        companies_ids = [client.company.company_external_id]
    else:
        executor = get_http_executor()
        request_config = endpoints.ConsultarEmpresasConfig(client)
        response = request_config.invoke(executor)
        response_json = response.json()
        if {"id": -1, "nome": "TODAS"} in response_json:
            response_json.remove({"id": -1, "nome": "TODAS"})

        empresas_api = parse_obj_as(list[models_totalbus.ExternalCompany], response_json)
        companies_ids = [e.external_id for e in empresas_api]

    init = today_midnight() + timedelta(days=shift_days)
    end = init + timedelta(days=next_days)
    selected_dates = [init + timedelta(days=next_day) for next_day in range(next_days + 1)]
    tasks = []
    for company_external_id in companies_ids:
        tasks += [
            descobrir_rotas_do_dia.s(
                company_internal_id, modelo_venda, company_external_id, selected_date.isoformat()
            ).set(queue=queue_name)
            for selected_date in selected_dates
        ]

    _group = group(*tasks)
    chain_task = chain(
        _group, finisher_descobrir_rotas_totalbus.si(client.company.id, init.isoformat(), end.isoformat())
    ).on_error(finisher_descobrir_rotas_totalbus_on_error.s(client.company.id))
    if return_task_object:
        return chain_task
    chain_task()
    return chain_task


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def finisher_descobrir_rotas_totalbus(company_id, init, end):
    init = datetime.fromisoformat(init)
    end = datetime.fromisoformat(end)
    inativa_rotinas_nao_atualizadas(company_id, init, end)
    inativa_rotas_sem_rotinas(company_id)
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_ROTAS, company_id=company_id)


@shared_task(ignore_result=True, queue=DefaultQueueNames.COMMIT_CALLBACK)
def finisher_descobrir_rotas_totalbus_on_error(context, exception, traceback, company_id):
    logger.error("Finishing descobrir rotas totalbus with error")
    inativa_rotas_sem_rotinas(company_id)
    finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_ROTAS, company_id=company_id, failure=True)


@shared_task(rate_limit=DEFAULT_RATE_LIMIT, queue=DEFAULT_QUEUE_NAME)
@retry(
    exceptions_type=(
        RodoviariaConnectionError,
        MyCircuitBreakerError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
def descobrir_rotas_do_dia(company_internal_id, modelo_venda, company_external_id, day):
    from rodoviaria.api.orchestrator import OrchestrateRodoviaria

    logger.info(
        "%s Descobrindo rotas do dia %s para empresa (%s, %s)", LOG_PREFIX, day, company_internal_id, modelo_venda
    )

    day = datetime.fromisoformat(day)
    orchestrator = OrchestrateRodoviaria(company_internal_id, modelo_venda)
    company_rodoviaria_id = orchestrator.provider.company.id
    viagens_api = _buscar_viagens_api(company_external_id, day, orchestrator)
    viagens_api = filter_servicos_novos_modelos(viagens_api, company_internal_id, modelo_venda)
    if not viagens_api:
        return
    map_rotas_api = _map_rotas_api(viagens_api, company_rodoviaria_id)
    map_rotas_buser = _map_rotas_buser(day, orchestrator, map_rotas_api)

    rotinas_encontradas_por_rota = {
        hash_rota: list(rota["rotinas"].keys()) for hash_rota, rota in map_rotas_api.items()
    }

    log_msg = (
        f"{LOG_PREFIX} Rotinas encontradas para o dia {day} na empresa ({company_internal_id}, {modelo_venda}): "
        f"{rotinas_encontradas_por_rota}"
    )
    logger.info(log_msg)

    _create_rotas_nao_existentes(company_rodoviaria_id, map_rotas_api, map_rotas_buser)
    _add_first_checkpoints_to_map(map_rotas_buser)

    rotinas_to_create = []
    rotinas_to_update = []
    rotas_ativas_ids = []
    for hash_api, rota_api in map_rotas_api.items():
        rota_buser_dict = map_rotas_buser.pop(hash_api)
        for rotina_api, id_external in rota_api["rotinas"].items():
            datetime_ida = to_tz(rotina_api, rota_buser_dict["first_timezone"])
            rotina_buser = rota_buser_dict["rotinas"].pop(datetime_ida, None)
            if not rotina_buser:
                rotinas_to_create.append(
                    Rotina(rota_id=rota_buser_dict["rota_id"], datetime_ida=datetime_ida, id_external=id_external)
                )
                continue
            rotina_buser.ativo = True
            rotina_buser.id_external = id_external
            rotina_buser.updated_at = timezone.now()
            rotinas_to_update.append(rotina_buser)
        if rota_buser_dict:
            rotas_ativas_ids.append(rota_buser_dict["rota_id"])
    _ativa_rotas(rotas_ativas_ids)
    _create_or_update_rotinas(company_rodoviaria_id, rotinas_to_create, rotinas_to_update)


def _ativa_rotas(rotas_ids):
    Rota.objects.filter(id__in=rotas_ids).update(ativo=True, updated_at=timezone.now())


def _add_first_checkpoints_to_map(map_rotas_buser):
    rotas_ids = [r["rota_id"] for r in map_rotas_buser.values()]
    first_checkpoints = Checkpoint.objects.filter(rota_id__in=rotas_ids, idx=0).values_list(
        "rota_id", "local__cidade__timezone"
    )
    first_timezones_map = {cp[0]: cp[1] for cp in first_checkpoints}
    for rota_buser in map_rotas_buser.values():
        rota_buser["first_timezone"] = first_timezones_map.get(rota_buser["rota_id"]) or "America/Sao_Paulo"


def _create_rotas_nao_existentes(company_rodoviaria_id, map_rotas_api, map_rotas_buser):
    rotas_to_create = []
    for hash_api, rota_api in map_rotas_api.items():
        if not map_rotas_buser.get(hash_api):
            rotas_to_create.append(rota_api)
    rotas_criadas = _bulk_create_rotas_and_checkpoints(company_rodoviaria_id, rotas_to_create)
    for rota_id, rota_hash in rotas_criadas:
        map_rotas_buser[rota_hash] = {"rotinas": {}, "rota_id": rota_id}


@lock("creating_rotas_and_checkpoints_totalbus_{company_rodoviaria_id}")
def _bulk_create_rotas_and_checkpoints(company_rodoviaria_id, rotas_to_create):
    objs_rota_to_create = []
    hashs_map = {}
    for rota_to_create in rotas_to_create:
        rota_obj = Rota(
            id_hash=rota_to_create["hash"],
            company_id=company_rodoviaria_id,
            provider_data=json.dumps(rota_to_create["cleaned"]),
            id_external=rota_to_create["id_external"],
        )
        objs_rota_to_create.append(rota_obj)
        hashs_map[rota_to_create["hash"]] = rota_to_create["parsed"]
    Rota.objects.bulk_create(objs_rota_to_create, ignore_conflicts=True)
    rotas_criadas = list(
        Rota.objects.filter(company_id=company_rodoviaria_id, id_hash__in=tuple(hashs_map.keys())).values_list(
            "id", "id_hash"
        )
    )
    objs_checkpoints_to_create = []
    for rota_criadas in rotas_criadas:
        itinerario = hashs_map[rota_criadas[1]]
        objs_checkpoints_to_create += _objs_checkpoints_to_create(rota_criadas[0], company_rodoviaria_id, itinerario)
    Checkpoint.objects.bulk_create(objs_checkpoints_to_create, ignore_conflicts=True)
    return rotas_criadas


def _objs_checkpoints_to_create(rota_id, company_rodoviaria_id, itinerario_parsed):
    rota_checkpoints = []
    cksForm = CheckpointsForm.from_itinerario(
        company_rodoviaria_id,
        itinerario_parsed,
    )
    for idx, ck in enumerate(cksForm):
        c = Checkpoint(
            rota_id=rota_id,
            idx=idx,
            local_id=ck.local.rodoviaria_local_id,
            internal_id=None,
            arrival=ck.arrival,
            departure=ck.departure,
            distancia_km=ck.distancia_km,
            duracao=timedelta(seconds=ck.duracao),
            tempo_embarque=timedelta(seconds=ck.tempo_embarque),
            id_external=ck.local.id_external,
            uf=ck.local.uf,
            name=ck.local.name,
            nickname=ck.local.nickname,
        )
        rota_checkpoints.append(c)
    return rota_checkpoints


def _buscar_viagens_api(company_external_id, day, orchestrator):
    midnight_day = midnight(day)
    start_day = midnight_day - timedelta(
        hours=5
    )  # coloca 5 horas a menos para garantir que está pegando todas as viagens do dia em UTC
    end_day = midnight_day + timedelta(hours=24)
    viagens_api = orchestrator.buscar_viagens_por_periodo(start_day, end_day, company_external_id)
    return viagens_api


def _map_rotas_buser(day, orchestrator, map_rotas_api):
    start_date = midnight(day)
    end_date = start_date + timedelta(hours=24)
    prefetch_rotinas = Prefetch(
        "rotina_set", queryset=Rotina.objects.filter(datetime_ida__range=[start_date, end_date])
    )
    rotas_existentes = Rota.objects.prefetch_related(prefetch_rotinas).filter(
        company_id=orchestrator.provider.company.id
    )
    map_rotas_buser = defaultdict(dict)
    for rota in rotas_existentes:
        map_rotas_buser[rota.id_hash]["rotinas"] = {rotina.datetime_ida: rotina for rotina in rota.rotina_set.all()}
        map_rotas_buser[rota.id_hash]["rota_id"] = rota.id
    logger.info("Rotas já existentes da empresa %s: %s", orchestrator.provider.company.id, map_rotas_buser)
    return map_rotas_buser


def _map_rotas_api(viagens_api, company_rodoviaria_id):
    map_rotas_api = {}
    for viagem_api in viagens_api:
        try:
            cleaned_itinerario = _convert_to_itinerario_input(viagem_api.trechos, company_rodoviaria_id)
        except LocalidadeNaoEncontrada:
            continue
        itinerario = models_totalbus.Itinerario.parse_obj(cleaned_itinerario)
        if not map_rotas_api.get(itinerario.hash):
            map_rotas_api[itinerario.hash] = {
                "rotinas": {},
                "parsed": itinerario,
                "cleaned": cleaned_itinerario,
                "hash": itinerario.hash,
                "id_external": viagem_api.external_id,
            }
        map_rotas_api[itinerario.hash]["rotinas"][itinerario[0].datetime_ida] = viagem_api.external_id
    return map_rotas_api


def _inativa_rotinas(rotinas):
    for r in rotinas:
        r.ativo = False
        r.updated_at = timezone.now()
    return rotinas


@lock("creating_rotinas_totalbus_{company_rodoviaria_id}")
def _create_or_update_rotinas(company_rodoviaria_id, rotinas_to_create, rotinas_to_update):
    Rotina.objects.bulk_update(rotinas_to_update, fields=["ativo", "updated_at", "id_external"], batch_size=500)
    Rotina.objects.bulk_create(rotinas_to_create, batch_size=500, ignore_conflicts=True)


def _convert_to_itinerario_input(trechos_viagem_api: list[models_totalbus.Trecho], company_rodoviaria_id: int):
    lsParadas = []
    for t in trechos_viagem_api:
        datetime_ida = datetime.strptime(t.data_inicio, "%Y-%m-%d %H:%M:%S")
        desc_origem = t.desc_origem
        if not desc_origem:
            desc_origem = _get_missing_desc_localidade(t.origem, company_rodoviaria_id)
        lsParadas.append(
            {
                "localidade": {"id": t.origem, "cidade": desc_origem, "uf": desc_origem.split("- ")[-1]},
                "distancia": f"{float(t.km_real):.1f}",
                "permanencia": "00:00",
                "data": datetime_ida.strftime("%Y-%m-%d"),
                "hora": datetime_ida.strftime("%H:%M"),
                "tempo_trecho": t.tempo_trecho,
            }
        )
    t = trechos_viagem_api[-1]
    datetime_ida = datetime.strptime(t.data_final, "%Y-%m-%d %H:%M:%S")
    desc_destino = t.desc_destino
    if not desc_destino:
        desc_destino = _get_missing_desc_localidade(t.destino, company_rodoviaria_id)
    lsParadas.append(
        {
            "localidade": {"id": t.destino, "cidade": desc_destino, "uf": desc_destino.split("- ")[-1]},
            "distancia": "0.00",
            "permanencia": "00:00",
            "data": datetime_ida.strftime("%Y-%m-%d"),
            "hora": datetime_ida.strftime("%H:%M"),
            "tempo_trecho": t.tempo_trecho,
        }
    )
    return lsParadas


@memoize_with_log(timeout=24 * 60 * 60)
def _get_missing_desc_localidade(localidade_id: int, company_rodoviaria_id: int):
    from rodoviaria.api.orchestrator import OrchestrateRodoviaria

    company = Company.objects.get(id=company_rodoviaria_id)
    api = OrchestrateRodoviaria.from_company(company)
    locais: list[models_totalbus.Localidade] = api.atualiza_origens()
    for local in locais:
        if int(local.external_local_id) == localidade_id:
            return f"{local.nome_cidade} - {local.uf}"
    raise LocalidadeNaoEncontrada

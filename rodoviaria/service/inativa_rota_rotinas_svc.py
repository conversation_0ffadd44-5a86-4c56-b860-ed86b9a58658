from collections import defaultdict
from datetime import timedelta

from celery.utils.log import get_task_logger
from django.db.models import F
from django.utils import timezone

from commons.dateutils import today_midnight
from commons.django_utils import error_str
from rodoviaria.models.core import Rota, Rotina

task_logger = get_task_logger(__name__)
TASK_LOG_PREFIX = "[inativar_rotas_rotinas]"


def inativar_rotas_rotinas():
    try:
        _inativar_rotinas()
        _inativar_rotas()
    except Exception as ex:
        task_logger.error("%s Erro ao inativar rotas e rotinas. error_str(ex)=%s", TASK_LOG_PREFIX, error_str(ex))
        raise ex


def _inativar_rotinas():
    """
    Inativa todas as rotinas que estão ativas e que não sofreram atualização nos ultimos 9 dias
    """
    data_ultima_vez_encontrado = today_midnight() - timedelta(days=9)
    rotinas_para_inativar = Rotina.objects.filter(
        ativo=True, datetime_ida__gte=timezone.now(), updated_at__lt=data_ultima_vez_encontrado
    )

    _log_inativar_rotinas(rotinas_para_inativar)

    rotinas_para_inativar.update(ativo=False, updated_at=timezone.now())


def _log_inativar_rotinas(rotinas_para_inativar):
    values_para_logs_rotinas_para_inativar = list(
        rotinas_para_inativar.values_list("datetime_ida", "rota_id", "rota__company__company_internal_id")
    )

    ids_rotinas_para_inativar_por_rota_id = defaultdict(list)
    for datetime_ida, rota_id, company_internal_id in values_para_logs_rotinas_para_inativar:
        key = f"({company_internal_id=}, {rota_id=})"
        ids_rotinas_para_inativar_por_rota_id[key].append(datetime_ida)

    task_logger.info(
        "%s Inativando rotinas. len_rotas=%s. len_rotinas=%s. ids_rotinas_para_inativar_por_rota_id=%s",
        TASK_LOG_PREFIX,
        len(ids_rotinas_para_inativar_por_rota_id.keys()),
        len(values_para_logs_rotinas_para_inativar),
        ids_rotinas_para_inativar_por_rota_id,
    )


def _inativar_rotas():
    """
    Inativa todas as rotas que estão ativas e que não possuem rotinas ativas
    """
    rotas_ids_com_rotinas_ativas = list(
        Rotina.objects.filter(ativo=True, datetime_ida__gte=timezone.now())
        .distinct("rota_id")
        .values_list("rota_id", flat=True)
    )
    rotas_para_inativar = Rota.objects.filter(ativo=True).exclude(id__in=rotas_ids_com_rotinas_ativas)

    ids_rotas_para_inativar = list(rotas_para_inativar.values("company__company_internal_id").annotate(rota_id=F("id")))
    task_logger.info(
        "%s Inativando rotas. len(ids_rotas_para_inativar)=%s. ids_rotas_para_inativar=%s",
        TASK_LOG_PREFIX,
        len(ids_rotas_para_inativar),
        ids_rotas_para_inativar,
    )

    rotas_para_inativar.update(ativo=False, updated_at=timezone.now())

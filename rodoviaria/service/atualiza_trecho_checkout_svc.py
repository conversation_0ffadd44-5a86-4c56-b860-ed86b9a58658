import logging

import beeline
from celery import shared_task
from redis.exceptions import LockNotOwnedError
from requests.exceptions import HTTPError

from commons.celery_utils import DefaultQueueNames
from commons.django_utils import error_str
from commons.memoize import memoize_with_log
from commons.redis import delete_key, get_key, lock, set_key
from rodoviaria.models.core import Company
from rodoviaria.service.exceptions import RodoviariaException
from rodoviaria.service.status_integracao_svc import StatusIntegracaoSVC
from rodoviaria.views_schemas import AtualizacaoCheckoutAsyncParams

DEFAULT_MODELO_VENDA = Company.ModeloVenda.MARKETPLACE
logger = logging.getLogger("rodoviaria")


def _condicao_atualiza(trecho_integracao, preco_atual, company):
    if trecho_integracao["status"] != "integracao_ok":
        return False
    if not company.has_feature(Company.Feature.ATUALIZAR_PRECO_CHECKOUT):
        return False
    max_percentual_divergencia = company.max_percentual_divergencia or 0
    if trecho_integracao["preco_rodoviaria"] == preco_atual:
        return False
    percentual_divergencia = ((trecho_integracao["preco_rodoviaria"] / preco_atual) - 1) * 100
    return abs(percentual_divergencia) > max_percentual_divergencia


def atualiza_trecho(company_id, trecho_classe_id, preco_atual):
    company = Company.objects.get(company_internal_id=company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    beeline.add_context(
        {
            "integracao_id": company.integracao_id,
        }
    )

    if not company.has_feature(Company.Feature.ATUALIZAR_PRECO_CHECKOUT):
        logger.info(
            "Update Trecho Checkout -> empresa sem feature",
            extra={"company_id": str(company_id), "company_name": company.name, "preco_antigo": str(preco_atual)},
        )
        return {
            "trecho_classe_id": trecho_classe_id,
            "atualiza": False,
            "mensagem": "Empresa sem feature de atualização no checkout",
        }

    trecho_integracao = _atualiza_trecho_api(trecho_classe_id)
    trecho_integracao = trecho_integracao[trecho_classe_id]
    return _atualizacao_checkout_retorno(trecho_classe_id, preco_atual, company, trecho_integracao, trecho_integracao)


def atualiza_trecho_async(data: AtualizacaoCheckoutAsyncParams):
    companies_ids = [t.company_id for t in data.trechos]
    companies = Company.objects.filter(company_internal_id__in=companies_ids, modelo_venda=DEFAULT_MODELO_VENDA)
    company_map = {c.company_internal_id: c for c in companies}
    response = []
    for trecho in data.trechos:
        company = company_map[trecho.company_id]
        response.append(_dispara_atualizacao_checkout_async(company, trecho.trecho_classe_id))
    return response


def _dispara_atualizacao_checkout_async(company, trecho_classe_id):
    beeline.add_context(
        {
            "integracao_id": company.integracao_id,
        }
    )

    if not company.has_feature(Company.Feature.ATUALIZAR_PRECO_CHECKOUT):
        logger.info(
            "Update Trecho Checkout -> empresa sem feature",
            extra={"company_id": str(company.company_internal_id), "company_name": company.name},
        )
        return {
            "trecho_classe_id": trecho_classe_id,
            "atualizacao_iniciada": False,
            "mensagem": "Empresa sem feature de atualização no checkout",
        }
    # adiciona no cache para indicar que a mensagem está na fila
    set_key(_status_cache_key(trecho_classe_id), "na_fila", timeout=12)
    _task_atualiza_trecho_api.apply_async((trecho_classe_id,), expires=8)
    return {
        "trecho_classe_id": trecho_classe_id,
        "atualizacao_iniciada": True,
        "mensagem": "Atualizacao async iniciada",
    }


def verifica_atualizacao(data: AtualizacaoCheckoutAsyncParams):
    response = []
    for trecho in data.trechos:
        response.append(_verifica_atualizacao_trecho(trecho.trecho_classe_id, trecho.preco_atual, trecho.company_id))
    return response


def _verifica_atualizacao_trecho(trecho_classe_id, preco_atual, company_id):
    terminou = atualizacao_terminou(trecho_classe_id)
    if not terminou:
        return {"trecho_classe_id": trecho_classe_id, "atualizacao_finalizada": False}
    erro_atualizacao = get_erro_atualizacao(trecho_classe_id)
    if erro_atualizacao:
        erro_atualizacao.update({"trecho_classe_id": trecho_classe_id, "atualizacao_finalizada": True})
        return erro_atualizacao

    dados_atualizacao = get_dados_atualizacao(trecho_classe_id)
    response = _handle_trecho_atualizado_async(trecho_classe_id, preco_atual, company_id, dados_atualizacao)
    return response


@memoize_with_log(timeout=60)
def _handle_trecho_atualizado_async(trecho_classe_id, preco_atual, company_id, dados_atualizacao):
    # TODO: tech-debt explicar melhor o que esse cara faz.
    company = Company.objects.get(company_internal_id=company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    beeline.add_context({"integracao_id": company.integracao_id})
    trecho_integracao = StatusIntegracaoSVC(internal_trecho_classe_id=trecho_classe_id).verifica()
    atualizacao = _atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company, trecho_integracao[trecho_classe_id], dados_atualizacao
    )
    atualizacao["atualizacao_finalizada"] = True
    return atualizacao


def _atualizacao_checkout_retorno(trecho_classe_id, preco_atual, company, trecho_integracao, dados_atualizacao):
    deve_atualizar = _condicao_atualiza(trecho_integracao, preco_atual, company)
    extra_log = {
        "company_id": str(company.company_internal_id),
        "company_name": company.name,
        "status": str(trecho_integracao["status"]),
        "preco_novo": str(trecho_integracao.get("preco_rodoviaria")),
        "preco_antigo": str(preco_atual),
        "trecho_classe_id": trecho_classe_id,
    }
    logger.info(
        "Update Trecho Checkout -> trecho_classe_id %s %s deve ser atualizado",
        trecho_classe_id,
        "não" if not deve_atualizar else "",
        extra=extra_log,
    )
    return {
        "trecho_classe_id": trecho_classe_id,
        "atualiza": deve_atualizar,
        "trecho": trecho_integracao,
        "dados_atualizacao": dados_atualizacao,
    }


def atualizacao_terminou(trecho_classe_id):
    em_execucao = bool(get_key(_status_cache_key(trecho_classe_id)))
    return not em_execucao


def get_dados_atualizacao(trecho_classe_id):
    return get_key(_data_cache_key(trecho_classe_id))


def get_erro_atualizacao(trecho_classe_id):
    return get_key(_error_cache_key(trecho_classe_id))


@memoize_with_log(timeout=5 * 60)
def _atualiza_trecho_api(trecho_classe_id):
    result = StatusIntegracaoSVC(internal_trecho_classe_id=trecho_classe_id).atualiza()
    return result


@shared_task(queue=DefaultQueueNames.ATUALIZA_PRECO_CHECKOUT)
def _task_atualiza_trecho_api(trecho_classe_id):
    try:
        response = _locked_atualiza_trecho_api(trecho_classe_id)
    except LockNotOwnedError:
        logger.info(
            "[TASK ATUALIZA CHECKOUT] Atualizacao já em andamento",
            extra={"trecho_classe_id": trecho_classe_id},
        )
        return  # atualização já está em andamento
    except (RodoviariaException, HTTPError) as ex:
        logger.error(
            "[TASK ATUALIZA CHECKOUT] Erro ao atualizar trecho",
            extra={"trecho_classe_id": trecho_classe_id, "error": error_str(ex)},
        )
        set_key(_error_cache_key(trecho_classe_id), {"error": error_str(ex)}, timeout=10)
        return
    logger.info(
        "[TASK ATUALIZA CHECKOUT] Atualizacao realizada",
        extra={"trecho_classe_id": trecho_classe_id, "response": repr(response)},
    )
    tc_status = response.get(trecho_classe_id)
    if tc_status and tc_status.get("status") in (
        StatusIntegracaoSVC.ERRO_API_NAO_RESPONDEU,
        StatusIntegracaoSVC.ERRO_INESPERADO,
    ):
        set_key(_error_cache_key(trecho_classe_id), tc_status, timeout=10)
    return response


@lock("atualiza_preco_checkout_{trecho_classe_id}", expire=15)
def _locked_atualiza_trecho_api(trecho_classe_id):
    response = _atualiza_trecho_api(trecho_classe_id)
    delete_key(_status_cache_key(trecho_classe_id))
    set_key(_data_cache_key(trecho_classe_id), response[trecho_classe_id], timeout=10)
    return response


def _status_cache_key(key):
    return f"atualizacao_checkout_{key}"


def _data_cache_key(key):
    return f"dados_atualizacao_checkout_{key}"


def _error_cache_key(key):
    return f"erro_atualizacao_checkout_{key}"

import json

from celery import shared_task

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models import MapaVeiculo, Veiculo
from rodoviaria.models.core import Company
from rodoviaria.models.vexado import VexadoGrupoClasse
from rodoviaria.service.exceptions import RodoviariaUnsupportedFeatureException

DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO


def fetch_mapas_veiculos_api():
    company = Company.objects.filter(
        modelo_venda=Company.ModeloVenda.HIBRIDO,
        integracao__name="vexado",
        features__contains=["escalar_veiculos"],
    ).first()
    if not company:
        return {"message": "Nenhuma empresa com a feature de escalar veiculos"}
    orchestrator = OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda)
    mapas_veiculos = orchestrator.get_mapas_veiculos_api()
    mapas_veiculos_existentes = {m.id_external: m for m in MapaVeiculo.objects.all()}
    to_update, to_create = [], []
    for mapa in mapas_veiculos:
        mapa_existente = mapas_veiculos_existentes.get(mapa.id_external)
        if mapa_existente:
            mapa_existente.has_dois_andares = mapa.has_dois_andares
            mapa_existente.quantidade_poltronas_primeiro_andar = mapa.quantidade_poltronas_primeiro_andar
            mapa_existente.quantidade_poltronas_segundo_andar = mapa.quantidade_poltronas_segundo_andar
            mapa_existente.provider_data = json.dumps(mapa.provider_data)
            to_update.append(mapa_existente)
            continue
        to_create.append(
            MapaVeiculo(
                id_external=mapa.id_external,
                has_dois_andares=mapa.has_dois_andares,
                quantidade_poltronas_primeiro_andar=mapa.quantidade_poltronas_primeiro_andar,
                quantidade_poltronas_segundo_andar=mapa.quantidade_poltronas_segundo_andar,
                provider_data=json.dumps(mapa.provider_data),
            )
        )
    MapaVeiculo.objects.bulk_create(to_create)
    MapaVeiculo.objects.bulk_update(  # não possui updated_at
        to_update,
        [
            "has_dois_andares",
            "quantidade_poltronas_primeiro_andar",
            "quantidade_poltronas_segundo_andar",
            "provider_data",
        ],
    )
    return {"mapas_veiculos_count": MapaVeiculo.objects.count()}


def fetch_veiculos_api(company_id, company=None):
    orchestrator = OrchestrateRodoviaria(company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    try:
        lista_veiculos = orchestrator.get_lista_veiculos_api()
    except RodoviariaUnsupportedFeatureException:
        return {"error": "Empresa sem a feature escalar_onibus"}
    if not company:
        company = Company.objects.get(company_internal_id=company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    veiculos_existentes = {v.id_external: v for v in company.veiculo_set.all()}
    mapas_veiculos_existentes = {m.id_external: m for m in MapaVeiculo.objects.all()}
    to_update, to_create = [], []
    for veiculo in lista_veiculos:
        veiculo_existente = veiculos_existentes.get(veiculo.id_external)
        mapa_existente = mapas_veiculos_existentes.get(veiculo.external_mapa_veiculo_id)
        if veiculo_existente:
            veiculo_existente.id_external = veiculo.id_external
            veiculo_existente.descricao = veiculo.descricao
            veiculo_existente.mapa_veiculo = mapa_existente
            to_update.append(veiculo_existente)
            continue
        to_create.append(
            Veiculo(
                id_external=veiculo.id_external,
                descricao=veiculo.descricao,
                company=company,
                mapa_veiculo=mapa_existente,
            )
        )
    Veiculo.objects.bulk_create(to_create)
    Veiculo.objects.bulk_update(to_update, ["id_external", "descricao", "mapa_veiculo"])  # não possui updated_at
    return {"quantidade_veiculos_da_empresa": company.veiculo_set.count()}


def _check_veiculo_link_and_update_if_necessary(veiculo, veiculos_to_check, company):
    try:
        return _check_veiculo_link(veiculo, veiculos_to_check)
    except GetOrCreateVeiculosError:
        return _update_veiculo(veiculo, veiculos_to_check, company)


def _update_veiculo(veiculo, veiculos_to_check, company):
    splited_descricao = veiculos_to_check[0].descricao.split("v.")
    veiculo_version = int(splited_descricao[1]) + 1 if len(splited_descricao) > 1 else 2
    for v in veiculos_to_check:
        v.descricao = "(OLD) " + v.descricao
        v.id_internal = None
    novos_veiculos = _create_veiculo(veiculo, company, veiculo_version=veiculo_version)
    Veiculo.objects.bulk_update(list(veiculos_to_check), ["descricao", "id_internal"])  # não possui updated_at
    return novos_veiculos


def get_link_or_create_veiculo(veiculo, company=None):
    company = _get_company(veiculo, company)
    veiculos_linkados = Veiculo.objects.select_related("mapa_veiculo").filter(
        id_internal=veiculo.veiculo_id, company=company
    )
    if veiculos_linkados:
        return _check_veiculo_link_and_update_if_necessary(veiculo, list(veiculos_linkados), company)
    return link_or_create_veiculo(veiculo, company)


def _get_company(veiculo, company):
    if not company and veiculo.company_id:
        company = Company.objects.get(company_internal_id=veiculo.company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    return company


def link_or_create_veiculo(veiculo, company=None):
    company = _get_company(veiculo, company)
    veiculos_nao_linkados = Veiculo.objects.select_related("mapa_veiculo").filter(
        descricao__startswith=veiculo.placa, id_internal__isnull=True, company=company
    )
    if veiculos_nao_linkados:
        return _link_veiculo(veiculo, list(veiculos_nao_linkados), company)
    veiculos_linkados_errado = Veiculo.objects.select_related("mapa_veiculo").filter(
        descricao__startswith=veiculo.placa, id_internal__isnull=False, company=company
    )
    if veiculos_linkados_errado:
        raise GetOrCreateVeiculosError(f"veiculo com a placa {veiculo.placa} está com link divergente")
    return _create_veiculo(veiculo, company)


def _check_veiculo_link(veiculo, veiculos_to_check):
    capacidades_api = []
    for v in veiculos_to_check:
        if veiculo.placa not in v.descricao:
            raise GetOrCreateVeiculosError(f"veiculo com id {v.id} com placa diferente da informada")
        capacidades_api.append(v.mapa_veiculo.quantidade_poltronas_primeiro_andar)
        if v.mapa_veiculo.quantidade_poltronas_segundo_andar:
            capacidades_api.append(v.mapa_veiculo.quantidade_poltronas_segundo_andar)

    for c in veiculo.classes:
        try:
            capacidades_api.remove(c.capacidade)
        except ValueError as exc:
            raise GetOrCreateVeiculosError(
                f"Quantidade de poltronas da classe {c.tipo} não bate com os veiculos da API"
            ) from exc
            # Possivel tratamento: criar os onibus corretamente e mudar os veiculos dos itinerarios que usam esse onibus

    if len(capacidades_api):
        raise GetOrCreateVeiculosError("Quantidade de classes não bate com os veiculos da API")
        # Possivel tratamento: criar os onibus corretamente e mudar os veiculos dos itinerarios que usam esse onibus

    return veiculos_to_check


def _link_veiculo(veiculo, veiculos_nao_linkados, company=None):
    veiculos_nao_linkados = _check_veiculo_link_and_update_if_necessary(veiculo, veiculos_nao_linkados, company)
    for v in veiculos_nao_linkados:
        v.id_internal = int(veiculo.veiculo_id)
    Veiculo.objects.bulk_update(list(veiculos_nao_linkados), ["id_internal"])  # não possui updated_at
    return veiculos_nao_linkados


def _create_veiculo(veiculo, company=None, veiculo_version=0):
    mapas = MapaVeiculo.objects.all()
    if not company and veiculo.company_id:
        company = Company.objects.get(company_internal_id=veiculo.company_id, modelo_venda=DEFAULT_MODELO_VENDA)
    orchestrator = OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda)
    poltronas_classes = [c.capacidade for c in veiculo.classes]
    try:
        mapas_veiculos = _find_mapas_veiculos(poltronas_classes, mapas)
    except MapasVeiculosNaoEncontrados as exc:
        raise GetOrCreateVeiculosError("Não foi encontrado um conjunto de mapas de veiculos para esse onibus") from exc
    to_create = []
    for index, mapa in enumerate(mapas_veiculos):
        descricao = (
            veiculo.placa + "*" * index + " v." + str(veiculo_version)
            if veiculo_version
            else veiculo.placa + "*" * index
        )
        v = Veiculo(
            mapa_veiculo=mapa,
            company=company,
            id_internal=veiculo.veiculo_id,
            descricao=descricao,
        )
        to_create.append(v)
    Veiculo.objects.bulk_create(to_create)
    orchestrator.create_veiculos_api(to_create)
    return to_create


def _find_mapas_veiculos(poltronas_classes, mapas_veiculos):
    mapas_veiculos_poltronas_map = {}
    for mapa in mapas_veiculos:
        q1 = mapa.quantidade_poltronas_primeiro_andar
        q2 = mapa.quantidade_poltronas_segundo_andar
        key = (q1, q2) if q2 > q1 else (q2, q1)
        mapas_veiculos_poltronas_map[key] = mapa
    poltronas_classes.sort()
    mapas = _find_mapas_veiculos_recursive(poltronas_classes, mapas_veiculos_poltronas_map)
    if not mapas:
        raise MapasVeiculosNaoEncontrados
    return mapas


def _find_mapas_veiculos_recursive(poltronas_classes, mapas_veiculos_poltronas_map):
    if len(poltronas_classes) == 0:
        return []
    if len(poltronas_classes) == 1:
        onibus_unico = mapas_veiculos_poltronas_map.get((0, poltronas_classes[0]))
        return [onibus_unico] if onibus_unico else False
    for index_i, quant_classe_i in enumerate(poltronas_classes):
        for quant_classe_j in poltronas_classes[index_i + 1 :]:
            if mapas_veiculos_poltronas_map.get((quant_classe_i, quant_classe_j)):
                poltronas_aux = poltronas_classes.copy()
                poltronas_aux.remove(quant_classe_i)
                poltronas_aux.remove(quant_classe_j)
                outras_combinacoes = _find_mapas_veiculos_recursive(poltronas_aux, mapas_veiculos_poltronas_map)
                if isinstance(outras_combinacoes, list):
                    outras_combinacoes.append(mapas_veiculos_poltronas_map[(quant_classe_i, quant_classe_j)])
                    return outras_combinacoes
        onibus_unico = mapas_veiculos_poltronas_map.get((0, quant_classe_i))
        if onibus_unico:
            poltronas_aux = poltronas_classes.copy()
            poltronas_aux.remove(quant_classe_i)
            outras_combinacoes = _find_mapas_veiculos_recursive(poltronas_aux, mapas_veiculos_poltronas_map)
            if isinstance(outras_combinacoes, list):
                outras_combinacoes.append(onibus_unico)
                return outras_combinacoes
        raise MapasVeiculosNaoEncontrados


@shared_task(
    queue=DefaultQueueNames.CADASTROS_VEXADO,
    rate_limit=DefaultRateLimits.CADASTROS_VEXADO,
)
def trocar_onibus_viagem_task(
    id_itinerario,
    veiculo_id_external,
    veiculo_id,
    andar,
    company_internal_id,
    grupo_classe_id,
):
    OrchestrateRodoviaria(company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA).altera_veiculo_viagem(
        veiculo_id_external, andar, id_itinerario
    )
    VexadoGrupoClasse.objects.update_or_create(
        grupo_classe_external_id=id_itinerario,
        defaults={"veiculo_id": veiculo_id, "andar": andar, "grupo_classe_id": grupo_classe_id},
    )


class MapasVeiculosNaoEncontrados(Exception):
    pass


class GetOrCreateVeiculosError(Exception):
    pass

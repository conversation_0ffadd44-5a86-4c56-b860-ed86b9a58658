import logging
from datetime import datetime, timedelta

from celery import chain, chord, group
from celery.app import shared_task
from celery.utils.log import get_task_logger
from sentry_sdk import capture_exception

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.django_utils import error_str
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.praxio import endpoints, models
from rodoviaria.models.core import Company
from rodoviaria.service.exceptions import RodoviariaConnectionError
from rodoviaria.service.salva_rotas_bulk_svc import salvar_rotas_achadas_em_bulk_task
from rodoviaria.utils import split_list

task_logger = get_task_logger(__name__)
logger = logging.getLogger("rodoviaria")
LOG_PREFIX = "[descobrir_rotas_praxio_async]"

DEFAULT_QUEUE_NAME = DefaultQueueNames.ROTAS
DEFAULT_RATE_LIMIT = DefaultRateLimits.Praxio.ROTAS


def descobrir_rotas(
    client,
    company_internal_id,
    next_days=7,
    shift_days=2,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    datetime_inicio = datetime.now() + timedelta(days=shift_days)
    datetime_fim = datetime_inicio + timedelta(days=next_days)
    parsed_servicos = _call_buscar_servicos(client, client.company, datetime_inicio, datetime_fim)
    viagem_ids = [s.external_id for s in parsed_servicos]
    task = _buscar_itinerarios_viagens(
        viagem_ids,
        next_days,
        client.company,
        queue_name,
        return_task_object,
    )
    return task


def _call_buscar_servicos(client, company, datetime_inicial, datetime_final):
    response = endpoints.BuscarServicosConfig(client).invoke(
        get_http_executor(),
        json=endpoints.BuscarServicosConfig.build_json_params(
            datetime_inicial, datetime_final, company.company_external_id
        ),
    )
    cleaned_servicos = response.json()["oObj"]
    parsed_servicos = models.ViagensServicos.parse_obj(cleaned_servicos)

    msg = (
        f"{LOG_PREFIX} {len(parsed_servicos)} possíveis servicos para empresa {company.company_internal_id}:"
        f" {cleaned_servicos}"
    )
    logger.info(msg)
    return parsed_servicos


def _buscar_itinerarios_viagens(
    viagem_ids,
    next_days,
    company,
    queue_name=DEFAULT_QUEUE_NAME,
    return_task_object=False,
):
    tasks = []
    for viagem_id in viagem_ids:
        tasks.append(_busca_itinerario_viagem.s(company.id, viagem_id).set(queue=queue_name))
    if return_task_object:
        _group = group(*tasks)
        _chain = chain(
            _group,
            salvar_rotas_achadas_em_bulk_task.s(company.company_internal_id, next_days).set(queue=queue_name),
        )
        return _chain

    chunked_tasks = split_list(tasks, 500)
    group_result = None
    for chunk_of_tasks in chunked_tasks:
        _group = group(*chunk_of_tasks)
        _chord = chord(_group)(
            salvar_rotas_achadas_em_bulk_task.s(company.company_internal_id, next_days).set(queue=queue_name)
        )
        group_result = _chord.parent
    return group_result


@shared_task(rate_limit=DEFAULT_RATE_LIMIT)
@retry(
    exceptions_type=(
        RodoviariaConnectionError,
        MyCircuitBreakerError,
        NotEnoughTokens,
    ),
    min_delay=10,
)
def _busca_itinerario_viagem(company_rodoviaria_id, id_viagem):
    try:
        rota = _busca_itinerario_viagem_circuit(company_rodoviaria_id, id_viagem)
        if rota.cleaned:
            task_logger.info("%s Rota encontrada para o servico id_viagem=%s", LOG_PREFIX, id_viagem)
            return {
                "id_external": id_viagem,
                "hash": rota.parsed.hash,
                "cleaned": rota.cleaned,
                "datetime_ida": rota.parsed[0].datetime_ida.strftime("%Y-%m-%dT%H:%M:%S"),
            }
        else:
            return None
    except (RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens):
        raise
    except Exception as ex:
        # Loga o erro e deixa a task morrer, pra não impedir o chord de rodar
        task_logger.error(
            "%s Erro inesperado ao descobrir rotas praxio. error_str(ex)=%s",
            LOG_PREFIX,
            error_str(ex),
        )
        capture_exception(ex)
        return None


def _busca_itinerario_viagem_circuit(company_rodoviaria_id, id_viagem):
    # retorna lista vazia e status 200, caso nao encontre itinerario para id_viagem
    from rodoviaria.api.orchestrator import OrchestrateRodoviaria

    company = Company.objects.get(id=company_rodoviaria_id)

    orchestrator = OrchestrateRodoviaria.from_company(company)
    params = {"id_viagem": id_viagem}
    return orchestrator.buscar_itinerario(params)

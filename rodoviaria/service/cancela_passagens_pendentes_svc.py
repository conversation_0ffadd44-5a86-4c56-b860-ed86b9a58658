import logging
from datetime import <PERSON><PERSON><PERSON>

from celery import group, shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone

from commons.celery_utils import DefaultQueueNames, DefaultRateLimits
from commons.django_utils import error_str
from core.models_travel import Travel
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Passagem
from rodoviaria.serializers.serializer_passagem import PassagemSerializer
from rodoviaria.service import reserva_svc
from rodoviaria.service.exceptions import (
    PassengerTicketAlreadyPrintedException,
    PassengerTicketAlreadyReturnedException,
    PoltronaTrocadaException,
    RodoviariaBaseException,
    RodoviariaBlockingException,
    RodoviariaConnectionError,
)

task_logger = get_task_logger(__name__)
buserlogger = logging.getLogger("rodoviaria")

DEFAULT_RATE_LIMIT = DefaultRateLimits.CANCELA_PASSAGEM
DEFAULT_QUEUE_NAME = DefaultQueueNames.CANCELA_PASSAGEM


@shared_task(queue=DEFAULT_QUEUE_NAME, ignore_result=True)
def cancela_passagens_pendentes():
    passagens_pendentes = (
        Passagem.objects.select_related("trechoclasse_integracao")
        .filter(
            tags__name="cancelamento_pendente",
            trechoclasse_integracao__datetime_ida__gt=timezone.now(),
            status=Passagem.Status.CONFIRMADA,
        )
        .exclude(tags__name__in=("passagem_impressa", "cancelamento_nao_permitido"))
    )
    tasks = []
    for passagem in passagens_pendentes:
        tasks.append(
            cancelar_task.s(
                travel_id=passagem.travel_internal_id,
                buseiro_id=passagem.buseiro_internal_id,
                passagem_id=passagem.id,
            )
        )
    tasks_group = group(tasks)
    tasks_group.apply_async()


@shared_task(queue=DEFAULT_QUEUE_NAME, rate_limit=DEFAULT_RATE_LIMIT)
def cancelar_task(travel_id, buseiro_id, passagem_id):
    passagem = Passagem.objects.get(id=passagem_id)
    try:
        reserva_svc.efetua_cancelamento(travel_id=travel_id, buseiro_id=buseiro_id, pax_valido=False)
    except RodoviariaConnectionError:
        task_logger.info(
            "Erro de conexão em cancelamento de passagem marcada com cancelamento_pendente."
            " travel_id=%s buseiro_id=%s passagem_id=%s",
            travel_id,
            buseiro_id,
            passagem_id,
        )
    except PassengerTicketAlreadyPrintedException:
        passagem.tags.add("passagem_impressa")
    except (PoltronaTrocadaException, PassengerTicketAlreadyReturnedException, RodoviariaBlockingException):
        passagem.tags.add("cancelamento_nao_permitido")
    else:
        passagem.tags.add("cancelada_pelo_cron")


def cancelar_passagens(passagens):
    exceptions = {}
    sucessos = {}
    for passagem in passagens:
        travel_id = passagem.travel_internal_id
        buseiro_id = passagem.buseiro_internal_id
        try:
            sucessos[f"travel:{travel_id} - buseiro:{buseiro_id}"] = reserva_svc.efetua_cancelamento(
                travel_id=travel_id, buseiro_id=buseiro_id, pax_valido=False
            )
            passagem.tags.add("cancelada_pelo_cron")
        except Exception as ex:
            exceptions[f"travel:{travel_id} - buseiro:{buseiro_id}"] = error_str(ex)
    return sucessos, exceptions


# Isso aqui deve sumir, é provisório enquanto não tem no metabase
def passagens_sem_travel_correspondente(*, _serializer=PassagemSerializer):
    passagens = (
        Passagem.objects.to_serialize(_serializer)
        .filter(
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao__datetime_ida__gt=timezone.now() + timedelta(minutes=179),
            created_at__lt=timezone.now() - timedelta(minutes=15),
        )
        .order_by("trechoclasse_integracao__datetime_ida")
    )

    travel_internal_ids = [passagem.travel_internal_id for passagem in passagens]
    buser_rodov_travels_ids = set(Travel.objects.filter(id__in=travel_internal_ids).values_list("id", flat=True))
    passagens_pendentes = []
    for passagem in passagens:
        travel_existe = passagem.travel_internal_id in buser_rodov_travels_ids
        if travel_existe:
            continue
        passagens_pendentes.append(passagem)
    return passagens_pendentes


def cancelar_passagens_sem_travel_correspondente():
    passagens_pendentes = passagens_sem_travel_correspondente()
    sucessos, exceptions = cancelar_passagens(passagens_pendentes)
    if exceptions:
        buserlogger.error("erro ao cancelar passagens sem travel: %s", exceptions)
    return {"sucessos": sucessos, "exceptions": exceptions}


# TODO: esperar a fila esvaziar e matar
@shared_task(queue=DefaultQueueNames.CANCELA_PASSAGEM)
def log_cron(complete_json_log):
    task_logger.info("Cron - Passagens com erro canceladas: %s", complete_json_log)


@shared_task(
    rate_limit=DefaultRateLimits.CANCELA_PASSAGENS_COM_ERRO,
    queue=DefaultQueueNames.CANCELA_PASSAGEM,
)
def cancela_pedido_com_erro(company_internal_id, pedido_id, passagens_ids):
    passagens = Passagem.objects.filter(id__in=passagens_ids)
    orchestrator = OrchestrateRodoviaria(company_internal_id)
    try:
        response = orchestrator.cancelar_reservas_por_pedido_id(pedido_id)
    except (NotImplementedError, RodoviariaBaseException):
        return "Erro no cancelamento do pedido"
    json_log = {"nao_cadastradas_no_banco": []}
    passagens_map = {p.poltrona_external_id: p for p in passagens}
    for passagem_api in response:
        passagem = passagens_map.get(passagem_api.get("poltrona"))
        if not passagem:
            task_logger.info("Passagem com erro não cadastrada na tabela rodoviaria: %s", passagem_api)
            json_log["nao_cadastradas_no_banco"].append(passagem_api)
            continue
        json_log[passagem.id] = passagem_api
        passagem.datetime_cancelamento = timezone.now()
        passagem.numero_passagem = passagem_api.get("numero_passagem")
        passagem.localizador = passagem_api.get("localizador")
        if passagem_api.get("error"):
            if passagem_api.get("error_type") == "nao_autorizado":
                passagem.tags.add("cancelamento_nao_autorizado")
            passagem.erro_cancelamento = passagem_api["error"]
            passagem.save()
            continue
        passagem.tags.add("passagem_com_erro_cancelada_pelo_cron")
        passagem.tags.remove("passagem_com_erro_confirmada_na_api")
        passagem.save()
    task_logger.info("Cron - Passagens com erro com pedido_id=%s canceladas: json_log=%s", pedido_id, json_log)
    return json_log

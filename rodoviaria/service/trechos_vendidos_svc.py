import logging

from django.utils import timezone

from commons.celery_utils import DefaultQueueNames
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Grupo, Rota, TaskStatus
from rodoviaria.service import fetch_trechos_vendidos_svc
from rodoviaria.service.exceptions import HibridoNotAllowedException, TaskInExecutionException

buserlogger = logging.getLogger("rodoviaria")


def get_trechos_vendidos(grupo_id, rodoviaria_rota_id):
    grupo, rota = _get_grupo_e_rota(grupo_id, rodoviaria_rota_id)
    if grupo_id and not rota:
        return {"error": "Grupo não possui uma rota vinculada no banco rodoviaria"}
    # TODO: tech-debt RodeRotas pode atualizar trecho vendido
    if rota.company.modelo_venda == Company.ModeloVenda.HIBRIDO and rota.company.company_internal_id != 313:
        raise HibridoNotAllowedException
    trechos_vendidos = list(rota.trechovendido_set.all()) or []
    if not trechos_vendidos:
        return {"warning": "Trechos vendidos ainda não foram buscados"}
    return [trecho.to_dict_json() for trecho in trechos_vendidos]


def fetch_trechos_vendidos_uma_rota(grupo_id, rodoviaria_rota_id):
    grupo, rota = _get_grupo_e_rota(grupo_id, rodoviaria_rota_id)
    if grupo and not rota:
        return {"error": "Grupo não possui uma rota vinculada no banco rodoviaria"}
    # TODO: tech-debt RodeRotas pode atualizar trecho vendido
    if rota.company.modelo_venda == Company.ModeloVenda.HIBRIDO and rota.company.company_internal_id != 313:
        raise HibridoNotAllowedException

    is_task_pending = TaskStatus.objects.filter(
        rota_id=rota.id,
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
        company_id=rota.company.id,
        status__in=(TaskStatus.Status.NOT_STARTED, TaskStatus.Status.PENDING),
    ).first()
    if is_task_pending:
        raise TaskInExecutionException(f"Fetch trechos vendidos da rota {rodoviaria_rota_id} já em execução")

    task = fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota(rota)
    company = rota.company
    orchestrator = OrchestrateRodoviaria(company.company_internal_id)
    task_info = {
        "task_id": task.id,
        "queue": orchestrator.provider.queue_name or DefaultQueueNames.TRECHOS_VENDIDOS,
        "mensagem": f"Fetch de trechos vendidos da rota {rota.id}",
    }
    return task_info


def _get_grupo_e_rota(grupo_id, rodoviaria_rota_id):
    if grupo_id:
        grupo = Grupo.objects.select_related("rota__company").get(grupo_internal_id=grupo_id)
        rota = grupo.rota
        return grupo, rota
    rota = Rota.objects.select_related("company").get(pk=rodoviaria_rota_id)
    grupo = rota.grupo_set.filter(datetime_ida__gt=timezone.now()).first()
    return grupo, rota

from rodoviaria.models import Company, VexadoLogin


def cria_company_transbrasil(company_internal_id, name, company_external_id):
    company_existente = _company_existente()
    login_existente = _login_existente(Company.ModeloVenda.HIBRIDO)
    company = Company()
    company.integracao = company_existente.integracao
    company.company_internal_id = company_internal_id
    company.company_external_id = company_external_id
    company.name = name
    company.url_base = company_existente.url_base
    company.modelo_venda = Company.ModeloVenda.HIBRIDO
    company.features = [
        Company.Feature.ITINERARIO,
        Company.Feature.BUSCAR_SERVICO,
        Company.Feature.ADD_PAX_STAFF,
        Company.Feature.BPE,
        Company.Feature.MOTORISTA,
        Company.Feature.ATUALIZAR_PRECO,
        Company.Feature.ACTIVE,
        Company.Feature.ESCALAR_VEICULOS,
    ]
    company.save()

    vexado_login = VexadoLogin()
    vexado_login.company = company
    vexado_login.user = login_existente.user
    vexado_login.password = login_existente.password
    vexado_login.site = login_existente.site
    vexado_login.save()
    return company


def _company_existente():
    return Company.objects.filter(integracao__name="vexado")[0]


def _login_existente(modelo_venda):
    return VexadoLogin.objects.filter(company__integracao__name="vexado", company__modelo_venda=modelo_venda)[0]

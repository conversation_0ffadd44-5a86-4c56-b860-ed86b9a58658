import logging

import rodoviaria.models.core as rodoviaria
from commons.utils import strip_punctuation
from rodoviaria.api.forms import Localidade
from rodoviaria.api.orchestrator import OrchestrateRodoviaria

buserlogger = logging.getLogger("rodoviaria")

DEFAULT_MODELO_VENDA = rodoviaria.Company.ModeloVenda.MARKETPLACE


class MapMarketplaceCidadesSVC:
    def __init__(self, company_internal_id, modelo_venda=DEFAULT_MODELO_VENDA):
        self.company_internal_id = company_internal_id
        self.internal_cidades = list(
            rodoviaria.CidadeInternal.objects.values("id", "name", "city_code_ibge", "timezone")
        )
        self.company = rodoviaria.Company.objects.select_related("integracao").get(
            company_internal_id=self.company_internal_id, modelo_venda=modelo_venda
        )
        self.api = OrchestrateRodoviaria(self.company_internal_id, modelo_venda)

    def execute(self):
        external_locais: list[Localidade] = self.api.atualiza_origens()
        count = 0
        for local in external_locais:
            nome_cidade = local.nome_cidade
            uf = local.uf
            nome_cidade_completo = f"{nome_cidade} - {uf}"
            nome_cidade_com_complemento = nome_cidade_completo
            if local.complemento:
                nome_cidade_com_complemento = f"{nome_cidade} - {uf} - {local.complemento}"
            internal_cidade, timezone = next(
                (
                    (item["id"], item["timezone"])
                    for item in self.internal_cidades
                    if strip_punctuation(item["name"]) == strip_punctuation(nome_cidade)
                ),
                (None, None),
            )
            cidade = self._get_or_create_cidade(
                local.external_cidade_id, nome_cidade_completo, internal_cidade, timezone
            )

            count = count + self._get_or_create_local_embarque(
                local.external_local_id, nome_cidade_com_complemento, cidade
            )
        return count

    def _get_or_create_cidade(self, id_cidade, name, internal_cidade, timezone):
        cidades = self.company.cidade_set.filter(id_external=id_cidade)
        if len(cidades) == 0:
            return self._create_cidade(id_cidade, name, internal_cidade, timezone)
        return cidades.first()

    def _create_cidade(self, id_cidade, cidade_name, internal_cidade, timezone):
        return rodoviaria.Cidade.objects.create(
            id_external=id_cidade,
            name=cidade_name,
            company=self.company,
            cidade_internal_id=internal_cidade,
            timezone=timezone,
        )

    def _get_or_create_local_embarque(self, local, name, cidade):
        try:
            rodoviaria.LocalEmbarque.objects.get(id_external=local, cidade__company=self.company)
            return 0
        except rodoviaria.LocalEmbarque.DoesNotExist:
            self._create_local_embarque(local, name, cidade)
            return 1

    def _create_local_embarque(self, id_cidade, city, cidade):
        return rodoviaria.LocalEmbarque.objects.create(
            id_external=id_cidade,
            nickname=city.title(),
            cidade=cidade,
        )

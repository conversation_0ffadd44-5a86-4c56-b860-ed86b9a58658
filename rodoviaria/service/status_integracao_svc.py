import logging

from celery import group, shared_task
from simple_token_bucket import NotEnoughTokens

from bp.buserdjango_celery import atualiza_trecho_unico
from commons.celery_utils import DefaultQueueNames, DefaultRateLimits, retry
from commons.circuit_breaker import MyCircuitBreakerError
from commons.django_utils import error_str
from core.models_travel import TrechoClasse as CoreTrechoClasse
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.forms.grupo_trechoclasse_form import TrechoClasseInternoInfos
from rodoviaria.models import Company, GrupoClasse, TrechoClasse, TrechoClasseError
from rodoviaria.service import novos_modelos_svc
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaTooManyRequestsError,
    RodoviariaTrechoclasseFactoryException,
    RodoviariaTrechoclasseNotFoundException,
)

from .grupo_trechoclasse_factory import GrupoTrechoClasseFactory, trecho_classe_infos

logger = logging.getLogger("rodoviaria")


class StatusIntegracaoSVC:
    INTEGRACAO_OK = "integracao_ok"
    INTEGRACAO_NAO_ENCONTRADA = "integracao_nao_encontrada"
    INTEGRACAO_PENDENTE = "integracao_pendente"
    ERRO_API_NAO_RESPONDEU = "erro_api_nao_respondeu"
    ERRO_INESPERADO = "erro_inesperado"

    def __init__(
        self,
        internal_grupo_id=None,
        internal_trecho_classe_id=None,
        batch_trecho_classe_ids=None,
    ):
        if not internal_grupo_id and not internal_trecho_classe_id and not batch_trecho_classe_ids:
            raise Exception("Precisa passar internal_grupo_id ou internal_trecho_classe_id ou batch_trecho_classe_ids")
        self.internal_grupo_id = internal_grupo_id
        self.internal_trecho_classe_id = internal_trecho_classe_id
        self.batch_trecho_classe_ids = batch_trecho_classe_ids

    def verifica(self):
        if self.internal_grupo_id:
            return self.search_for_grupo()
        if self.batch_trecho_classe_ids:
            return {
                trecho_classe_id: self.search_for_trechoclasse(trecho_classe_id)
                for trecho_classe_id in self.batch_trecho_classe_ids
            }
        return {self.internal_trecho_classe_id: self.search_for_trechoclasse(self.internal_trecho_classe_id)}

    def atualiza(self):
        trechos_classes, company_base = self._trechos_e_companies_para_atualizar()

        if self.internal_grupo_id:
            return self.atualiza_async(trechos_classes, company_base)

        if not trechos_classes:
            return self.verifica()
        tc_error_status = {}
        map_last_update = {}
        for trecho_classe in trechos_classes:
            company = company_base
            # TODO: tech-debt -> resolver o N+1 do batch_trecho_classe_ids
            if not company:
                company = Company.objects.get(
                    company_internal_id=trecho_classe.grupo.company_id,
                    modelo_venda=trecho_classe.grupo.modelo_venda,
                )
            try:
                trechoclasse_from_buser_django = trecho_classe_infos(trecho_classe)
                info = GrupoTrechoClasseFactory(
                    company,
                    trechoclasse_from_buser_django,
                    OrchestrateRodoviaria.from_company(company),
                ).create(tag_to_add=None)
                map_last_update[trechoclasse_from_buser_django.trechoclasse_id] = info.last_update
            except RodoviariaTrechoclasseFactoryException as ex:
                tc_error_status[trechoclasse_from_buser_django.trechoclasse_id] = (
                    self.INTEGRACAO_NAO_ENCONTRADA,
                    error_str(ex),
                )
            except RodoviariaConnectionError:
                tc_error_status[trechoclasse_from_buser_django.trechoclasse_id] = (
                    self.ERRO_API_NAO_RESPONDEU,
                    "Erro de conexão com a API do parceiro",
                )
            except RodoviariaException as ex:
                tc_error_status[trechoclasse_from_buser_django.trechoclasse_id] = (
                    self.ERRO_INESPERADO,
                    error_str(ex),
                )

        trechos_status = self.verifica()
        for trecho_id in trechos_status.keys():
            trechos_status[trecho_id]["last_update"] = map_last_update.get(trecho_id)
            if trecho_id in tc_error_status:
                status, error = tc_error_status[trecho_id]
                trechos_status[trecho_id]["status"] = status
                trechos_status[trecho_id]["error"] = error
        return trechos_status

    def atualiza_async(self, trechos_classes, company):
        tasks = []
        for trecho_classe in trechos_classes:
            trechoclasse_from_buser_django = trecho_classe_infos(trecho_classe)
            tasks.append(update_trecho_classe_link_task.s(company.id, trechoclasse_from_buser_django.json()))
        group(tasks).apply_async()
        return {"message": "Os links estão sendo atualizados"}

    def search_for_grupo(self):
        trecho_classes = CoreTrechoClasse.objects.filter(grupo_id=self.internal_grupo_id).values_list("id", flat=True)

        rodoviaria_trecho_classes_map = self._gera_dict_trechos(set(trecho_classes))
        trecho_classes_erros_map = self._gera_dict_trechos_erros(set(trecho_classes))

        status_trecho_classes = {}
        for trecho_classe in trecho_classes:
            status_trecho_classes[trecho_classe] = self._get_status_trecho_no_grupo(
                trecho_classe, rodoviaria_trecho_classes_map, trecho_classes_erros_map
            )
        return status_trecho_classes

    def search_for_trechoclasse(self, internal_trecho_classe_id):
        retorno = {}
        retorno["status"] = self.INTEGRACAO_PENDENTE
        retorno["preco_rodoviaria"] = None
        retorno["vagas"] = None

        trecho_integrated = TrechoClasse.objects.filter(
            trechoclasse_internal_id=internal_trecho_classe_id, active=True
        ).first()
        trecho_error = (
            TrechoClasseError.objects.filter(trechoclasse_internal_id=internal_trecho_classe_id)
            .order_by("-updated_at")
            .first()
        )

        has_error_and_not_integrated = trecho_error and not trecho_integrated
        has_error_and_integrated_but_error_newer = (
            trecho_error and trecho_integrated and trecho_integrated.updated_at < trecho_error.updated_at
        )
        if has_error_and_not_integrated or has_error_and_integrated_but_error_newer:
            retorno["status"] = self.INTEGRACAO_NAO_ENCONTRADA
            if trecho_error.motivo:
                retorno["error"] = trecho_error.motivo_fechamento
            return retorno

        if trecho_integrated:
            retorno["status"] = self.INTEGRACAO_OK
            retorno["preco_rodoviaria"] = trecho_integrated.preco_rodoviaria
            retorno["vagas"] = trecho_integrated.vagas
            retorno["external_tipo_assento"] = trecho_integrated.grupo_classe.tipo_assento_external
            retorno["external_datetime_ida"] = trecho_integrated.external_datetime_ida

        # caso nao caia em nenhum if, retorna status INTEGRACAO_PENDENTE
        return retorno

    def _trechos_e_companies_para_atualizar(self):
        # TODO: precisa melhorar o construtor dessa classe. Esse monte de if é sintoma da classe mal instanciada.
        trechos_classe_queryset = (
            CoreTrechoClasse.objects.select_related("grupo")
            .select_related("trecho_vendido__origem__cidade")
            .select_related("trecho_vendido__destino__cidade")
            .select_related("grupo_classe")
        )
        base_grupo = None
        if self.internal_grupo_id:
            trechos_classes = list(trechos_classe_queryset.filter(grupo_id=self.internal_grupo_id))
            base_grupo = trechos_classes[0].grupo
        elif self.batch_trecho_classe_ids:
            trechos_classes = list(trechos_classe_queryset.filter(id__in=self.batch_trecho_classe_ids))
        else:
            trechos_classes = [trechos_classe_queryset.get(id=self.internal_trecho_classe_id)]
            base_grupo = trechos_classes[0].grupo
        if not base_grupo:
            return trechos_classes, None
        company_internal_id = base_grupo.company_id
        modelo_venda = base_grupo.modelo_venda
        if base_grupo.modelo_venda == Company.ModeloVenda.HIBRIDO:
            company_internal_id = novos_modelos_svc.get_company_internal_id_integrado_por_rotina(
                company_internal_id, base_grupo.rotina_onibus_id
            )
        company = Company.objects.get(
            company_internal_id=company_internal_id,
            modelo_venda=modelo_venda,
        )
        return trechos_classes, company

    def _gera_dict_trechos(self, trecho_classes):
        rodoviaria_trecho_classes = TrechoClasse.objects.filter(
            trechoclasse_internal_id__in=trecho_classes, active=True
        )
        result_dict = {}
        for rodoviaria_trecho_classe in rodoviaria_trecho_classes:
            # add se nao tiver no map, ou se o que ja tava tiver inativo
            if rodoviaria_trecho_classe.trechoclasse_internal_id not in result_dict or (
                rodoviaria_trecho_classe.active
                and not result_dict[rodoviaria_trecho_classe.trechoclasse_internal_id].active
            ):
                result_dict[rodoviaria_trecho_classe.trechoclasse_internal_id] = rodoviaria_trecho_classe

        return result_dict

    def _gera_dict_trechos_erros(self, trecho_classes):
        trecho_classes_erros = TrechoClasseError.objects.filter(trechoclasse_internal_id__in=set(trecho_classes))
        result_dict = {erro.trechoclasse_internal_id: erro for erro in trecho_classes_erros}
        return result_dict

    def _get_status_trecho_no_grupo(self, trecho_classe, rodoviaria_trecho_classes_map, trecho_classes_erros_map):
        retorno = {}
        retorno["status"] = self.INTEGRACAO_PENDENTE
        retorno["preco_rodoviaria"] = None
        retorno["vagas"] = None
        error_updated_at = None

        if trecho_classe in trecho_classes_erros_map:
            error_updated_at = trecho_classes_erros_map[trecho_classe].updated_at
            retorno["status"] = self.INTEGRACAO_NAO_ENCONTRADA

        if trecho_classe in rodoviaria_trecho_classes_map:
            integrated_updated_at = rodoviaria_trecho_classes_map[trecho_classe].updated_at
            has_error = error_updated_at
            has_error_but_integrated_newer = has_error and integrated_updated_at > error_updated_at
            if not has_error or has_error_but_integrated_newer:
                tc = rodoviaria_trecho_classes_map[trecho_classe]
                retorno["status"] = self.INTEGRACAO_OK
                retorno["preco_rodoviaria"] = tc.preco_rodoviaria
                retorno["vagas"] = tc.vagas

        # caso nao caia em nenhum if, retorna status INTEGRACAO_PENDENTE
        return retorno


def update_grupos_classe_link(grupos_classe_to_update):
    new_ids_map = grupos_classe_to_update.map
    grupos_classe = GrupoClasse.objects.filter(
        grupoclasse_internal_id__in=grupos_classe_to_update.old_grupos_classe_ids
    )
    for gc in grupos_classe:
        new_id = new_ids_map[gc.grupoclasse_internal_id]
        old_id = gc.grupoclasse_internal_id
        gc.grupoclasse_internal_id = new_id
        logger.info("Atualizando grupoclasse_internal_id: %s -> %s", old_id, new_id)
    GrupoClasse.objects.bulk_update(list(grupos_classe), ["grupoclasse_internal_id"])  # não possui updated_at


@shared_task(
    queue=DefaultQueueNames.STAFF_UPDATE_LINK,
    rate_limit=DefaultRateLimits.LinkTrechosClasses.RATE_LIMIT,
)
@retry(
    exceptions_type=(RodoviariaConnectionError, MyCircuitBreakerError, NotEnoughTokens),
    min_delay=10,
)
def update_trecho_classe_link_task(company_rodoviaria_id, trechoclasse_from_buser_django):
    trechoclasse_from_buser_django = TrechoClasseInternoInfos.parse_raw(trechoclasse_from_buser_django)
    company = Company.objects.get(id=company_rodoviaria_id)
    try:
        result = GrupoTrechoClasseFactory(
            company,
            trechoclasse_from_buser_django,
            OrchestrateRodoviaria.from_company(company),
        ).create(tag_to_add=None)
        if company.modelo_venda == Company.ModeloVenda.MARKETPLACE:
            atualiza_trecho_unico.delay(
                trecho_classe_id=result.trecho_classe.trechoclasse_internal_id,
                to_close=False,
                preco=result.trecho_classe.preco_rodoviaria,
                vagas=result.trecho_classe.vagas,
            )
    except RodoviariaTrechoclasseNotFoundException as ex:
        atualiza_trecho_unico.delay(
            trecho_classe_id=trechoclasse_from_buser_django.trechoclasse_id,
            to_close=True,
            motivo_unmatch=ex.motivo,
        )
    except (
        RodoviariaTrechoclasseFactoryException,
        RodoviariaException,
        RodoviariaTooManyRequestsError,
        RodoviariaConnectionError,
    ):
        pass

import json
import logging
from datetime import datetime

from django.http import JsonResponse
from ninja import NinjaAPI, Query
from pydantic import parse_obj_as
from sentry_sdk import capture_exception

from commons import log_svc
from commons.dateutils import to_default_tz_required
from commons.django_utils import error_str
from instrumentation.honeycomb import sample_view
from rodoviaria.api.praxio import api as praxio_api
from rodoviaria.api.totalbus import api as totalbus_api
from rodoviaria.forms.compra_rodoviaria_forms import (
    BloquearPoltronasForm,
    BloquearPoltronasFormV2,
    ComprarForm,
    DefaultForm,
    DesbloquearPoltronasForm,
    VerificarPoltronaForm,
)
from rodoviaria.forms.staff_forms import (
    CheckPaxBatchForm,
    CheckPaxForm,
    CheckPaxMultipleForm,
    CompanyForm,
    CreateRodoviariaCompanyForm,
    FetchFormasPagamentoForm,
    ItinerariosNaoIntegradosForm,
    ListLinksLocaisForm,
    PraxioLoginForm,
    RemanejaPassageiroForm,
    RodoviariaLinkarTiposAssentosParams,
    RodoviariaListarTiposAssentosParams,
    TotalbusNoCompanyLogin,
)
from rodoviaria.models.core import Company, Passagem
from rodoviaria.models.ti_sistemas import TiSistemasLogin
from rodoviaria.service import (
    alterar_grupo_svc,
    atualiza_precos_search_svc,
    atualiza_trecho_checkout_svc,
    cancela_passagens_pendentes_svc,
    class_match_svc,
    classes_e_precos_svc,
    cronograma_atualizacao_svc,
    descobrir_operacao_svc,
    descobrir_rotas_svc,
    divergencia_precos_svc,
    fetch_data_limite_rotas_svc,
    fetch_trechos_vendidos_svc,
    get_passagens_svc,
    link_grupos_classe_svc,
    link_rotas_svc,
    link_trechoclasse_async_svc,
    link_trechoclasse_svc,
    link_trechos_vendidos_svc,
    marketplace_fetch_rotas_svc,
    novos_modelos_svc,
    passagens_pendentes_svc,
    relatorio_passagens_svc,
    rota_svc,
    rotina_svc,
    sistema_integracao_empresa_svc,
    solicita_cancelamento_svc,
    staff_rodoviaria_svc,
    status_integracao_svc,
    trechos_vendidos_svc,
    veiculos_svc,
    verificar_grupos_cancelados_svc,
)
from rodoviaria.service.auto_integra_operacao import auto_integra_operacao_svc
from rodoviaria.service.compare_rotas_svc import comparar_rotas
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import (
    CampoObrigatorioException,
    DivergenciaPrecoException,
    HibridoEmissaoForaDaData,
    PassengerInvalidDocumentException,
    PassengerNotRegistered,
    PassengerTicketAlreadyPrintedException,
    PassengerTicketAlreadyReturnedException,
    PoltronaExpirada,
    PoltronaIndisponivel,
    PoltronaJaSelecionadaException,
    PoltronaTrocadaException,
    RodoviariaBlockingException,
    RodoviariaCompanyExistenteException,
    RodoviariaCompanyNotFoundException,
    RodoviariaCompanyNotIntegratedError,
    RodoviariaException,
    RodoviariaIntegracaoNotFoundException,
    RodoviariaInvalidParamsException,
    RodoviariaLoginNotFoundException,
    RodoviariaNoLocalCheckpointException,
    RodoviariaNoTimezoneException,
    RodoviariaOverbookingException,
    RodoviariaRotaNotFoundException,
    RodoviariaTimeoutException,
    RodoviariaTrechoBloqueadoException,
    RodoviariaTrechoclasseFactoryException,
    RodoviariaUnableRevertHardStop,
    RodoviariaUnauthorizedError,
    TaskInExecutionException,
)
from rodoviaria.service.map_marketplace_cidades_svc import MapMarketplaceCidadesSVC

from .service import (
    bpe_svc,
    cadastrar_grupos_hibridos_svc,
    cancelar_grupos_hibridos_svc,
    company_svc,
    links_locais_embarque_svc,
    motorista_svc,
    remanejamento_svc,
    reserva_svc,
    rotas_transbrasil_svc,
    update_grupos_hibridos_svc,
)
from .service.status_integracao_svc import StatusIntegracaoSVC
from .service.transbrasil_company_factory import cria_company_transbrasil
from .views_schemas import (
    AddMultiplePaxAsyncParams,
    AlterarGrupoParams,
    AtualizacaoCheckoutAsyncParams,
    AtualizarLocaisBatchParams,
    AtualizarLocaisParams,
    BloquearPoltronasParams,
    BloquearPoltronasParamsV2,
    BulkGetPoltronasParams,
    BulkGrupoTemPassagemEmitidaParams,
    BuscarViagensAPIParams,
    BuscarViagensTodasEmpresasAPIParams,
    CadastrarGruposParams,
    CadastrarItinerarioParams,
    CadastrarOuEditarRotaHibridoParams,
    CadastrarRotaHibridoParams,
    CadastrarTrechosParams,
    CadastrarVeiculoParams,
    CancelarGruposClasseParams,
    CompanyPaginatorParams,
    CompanyTransbrasilParams,
    CronogramaAtualizacaoPaginatorParams,
    DadosBpePassagemBatchParams,
    DeleteTrechoClasseErrorParams,
    DesbloquearPoltronasParams,
    EditarRotaHibridoParams,
    EscalaMotoristaParams,
    GetDetalhesRotinasParams,
    GetDetalhesRotinasPorRotaParams,
    IdsEmpresasParams,
    LinkTrechosClassesAsyncParams,
    ListaEmpresasAPIParams,
    ListaPassageirosViagem,
    MapCidadeParams,
    PassagensConfirmadasPorEmpresaParams,
    PassagensPorEmpresaParams,
    PosSalvarRotaParams,
    RemanejaPassageirosAsyncParams,
    RemoverTagsTrechosAtualizadosParams,
    SolicitaCancelamentoPorPassagemIdParams,
    SolicitaCancelamentoTravelsParams,
    StatusIntegracaoBatchParams,
    StatusIntegracaoCheckoutParams,
    StatusIntegracaoParams,
    TrechoClasseAddRemoveTagParams,
    TrechoClasseGetAtualizaParams,
    UpdateCronogramaAtualizacaoParams,
    UpdateGruposClasseLink,
    VerificaLinkRotasParams,
)

api = NinjaAPI()
buserlogger = logging.getLogger("rodoviaria")


@api.get("/v1/empresas")
# Por enquanto features é uma string só.
def empresas(request, params: CompanyPaginatorParams = Query(...)):
    if params.id and params.modelo_venda:
        empresa = company_svc.get_company_by_id(params.id, params.modelo_venda)
        if empresa is None:
            return JsonResponse({"message": "Not found."}, status=404)
        return JsonResponse(empresa.serialize())
    if params.integracoes:
        empresas = company_svc.get_empresas(features=["active"], integracoes=params.integracoes)
        return JsonResponse(
            {
                "empresas": list(empresas),
            }
        )
    if params.features:
        features = params.features.split(",")
        empresas = company_svc.get_empresas(features=features)
        return JsonResponse(
            {
                "empresas": list(empresas),
            }
        )

    companies = company_svc.companies_paginator(
        name=params.name,
        modelo_venda=params.modelo_venda,
        status=params.status,
        rows_per_page=params.rows_per_page,
        page=params.page,
        order_by=params.order_by,
    )
    return JsonResponse(companies)


@api.get("/v1/integracoes")
def integracoes(request):
    sistemas_integracao = sistema_integracao_empresa_svc.get_integracoes_empresa()
    return JsonResponse({"integracoes": sistemas_integracao})


@api.get("/v1/get_poltronas")
def get_poltronas(
    request,
    trecho_classe_id: int = 0,
    num_passageiros: int = 0,
    categoria_especial: Passagem.CategoriaEspecial | None = None,
):
    if not trecho_classe_id:
        return JsonResponse({"message": "Necessário passar o trecho_classe_id"}, status=422)
    if not num_passageiros:
        return JsonResponse({"message": "Necessário passar o num_passageiros"}, status=422)

    form = VerificarPoltronaForm(
        force_renew_link=True,
        trechoclasse_id=trecho_classe_id,
        passageiros=num_passageiros,
        categoria_especial=categoria_especial,
    )

    try:
        resp = CompraRodoviariaSVC(form).verifica_poltrona(form)
        return JsonResponse(resp, safe=False)
    except RodoviariaOverbookingException as ex:
        dict_error = {
            "error": error_str(ex),
            "error_type": "overbooking",
            "vagas_disponiveis": ex.vagas_disponiveis,
        }
        log_svc.log_reserva_exception("OverbookingException", extra=dict_error)
        return JsonResponse(dict_error, status=444)
    except RodoviariaTrechoclasseFactoryException as exception:
        dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)
    except RodoviariaTrechoBloqueadoException as ex:
        dict_error = {"error": error_str(ex), "error_type": "service_not_for_sale"}
        log_svc.log_reserva_exception("RodoviariaTrechoBloqueadoException", extra=dict_error)
        return JsonResponse(dict_error, status=410)


@api.get("/v1/get_vagas_por_categoria_especial")
def get_vagas_por_categoria_especial(request, trecho_classe_id: int):
    if not trecho_classe_id:
        return JsonResponse({"message": "Necessário passar o trecho_classe_id"}, status=422)

    form = DefaultForm(trechoclasse_id=trecho_classe_id, force_renew_link=False)

    try:
        resp = CompraRodoviariaSVC(form).vagas_por_categoria_especial(form)
        return JsonResponse(resp, safe=False)
    except RodoviariaTrechoclasseFactoryException as exception:
        dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)


@api.get("/v1/get_map_poltronas")
def get_map_poltronas(request, trecho_classe_id: int):
    form = DefaultForm(trechoclasse_id=trecho_classe_id, force_renew_link=True)

    try:
        return CompraRodoviariaSVC(form).get_map_poltronas(form)
    except RodoviariaTrechoclasseFactoryException as exception:
        dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)


@api.post("/v1/desbloquear-poltronas")
def desbloquear_poltronas(request, data: DesbloquearPoltronasParams):
    form = DesbloquearPoltronasForm(trechoclasse_id=data.trecho_classe_id, poltronas=data.poltronas)

    try:
        resp = CompraRodoviariaSVC(form).desbloquear_poltronas(form)
        return JsonResponse(resp, safe=False)
    except RodoviariaTrechoclasseFactoryException as exception:
        dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)


@api.post("/v1/bloquear-poltronas")
def bloquear_poltronas(request, data: BloquearPoltronasParams):
    form = BloquearPoltronasForm(trechoclasse_id=data.trecho_classe_id, poltronas=data.poltronas)

    try:
        resp = CompraRodoviariaSVC(form).bloquear_poltronas(form)
        return JsonResponse(resp, safe=False)
    except RodoviariaTrechoBloqueadoException as ex:
        dict_error = {"error": error_str(ex), "error_type": "service_not_for_sale"}
        log_svc.log_reserva_exception("RodoviariaTrechoBloqueadoException", extra=dict_error)
        return JsonResponse(dict_error, status=410)
    except RodoviariaBlockingException as ex:
        dict_error = {"error": error_str(ex), "error_type": "blocked"}
        log_svc.log_reserva_exception("BlockingException", extra=dict_error)
        return JsonResponse(dict_error, status=406)


@api.post("/v2/bloquear-poltronas")
def bloquear_poltronas_v2(request, data: BloquearPoltronasParamsV2):
    form = BloquearPoltronasFormV2(
        trechoclasse_id=data.trecho_classe_id,
        poltrona=data.poltrona,
        categoria_especial=data.categoria_especial or Passagem.CategoriaEspecial.NORMAL,
    )

    try:
        resp = CompraRodoviariaSVC(form).bloquear_poltronas_v2(form)
        return JsonResponse(resp.dict(), safe=False)
    except RodoviariaTrechoBloqueadoException as ex:
        dict_error = {"error": error_str(ex), "error_type": "service_not_for_sale"}
        log_svc.log_reserva_exception("RodoviariaTrechoBloqueadoException", extra=dict_error)
        return JsonResponse(dict_error, status=410)
    except RodoviariaBlockingException as ex:
        dict_error = {"error": error_str(ex), "error_type": "blocked"}
        log_svc.log_reserva_exception("BlockingException", extra=dict_error)
        return JsonResponse(dict_error, status=406)
    except PoltronaJaSelecionadaException as ex:
        dict_error = {"error": error_str(ex), "error_type": "seat_already_taken", "poltrona": data.poltrona}
        log_svc.log_reserva_exception("PoltronaJaSelecionadaException", extra=dict_error)
        return JsonResponse(dict_error, status=409)
    except PoltronaIndisponivel as ex:
        dict_error = {"error": error_str(ex), "error_type": "poltrona_indisponivel"}
        log_svc.log_reserva_exception("PoltronaIndisponivel", extra=dict_error)
        return JsonResponse(dict_error, status=409)


@api.post("/v1/map_cidade_e_locais")
def map_cidade_e_locais(request):
    params = MapCidadeParams.parse_raw(request.body)
    try:
        company = Company.objects.get(id=params.company_rodoviaria_id)
    except Company.DoesNotExist:
        return JsonResponse({"error": "Empresa não encontrada"}, status=404)

    count = MapMarketplaceCidadesSVC(company.company_internal_id, company.modelo_venda).execute()
    return JsonResponse({"count": count})


@api.get("/v1/passageiros")
def passageiros(request, data: ListaPassageirosViagem = Query(...)):
    result = staff_rodoviaria_svc.StaffRodoviariaSVC().lista_passageiros_viagem(data.travels_ids)
    return JsonResponse(result, safe=False)


@api.post("/v1/trecho_classe_integracao/atualiza")
def atualiza_trecho_classe_integracao(request, data: StatusIntegracaoParams = Query(...)):
    if not data.grupo_id and not data.trecho_classe_id:
        return JsonResponse({"message": "É preciso passar grupo_id ou trecho_classe_id."}, status=422)
    result = StatusIntegracaoSVC(data.grupo_id, data.trecho_classe_id).atualiza()
    return JsonResponse(result)


@api.post("/v1/trecho_classe_integracao/atualiza_checkout")
def atualiza_trecho_classe_integracao_checkout(request, data: StatusIntegracaoCheckoutParams = Query(...)):
    result = atualiza_trecho_checkout_svc.atualiza_trecho(data.company_id, data.trecho_classe_id, data.preco_atual)
    return JsonResponse(result)


@api.post("/v1/trecho_classe_integracao/atualiza_checkout_async")
def atualiza_trecho_classe_integracao_checkout_async(request, data: AtualizacaoCheckoutAsyncParams):
    result = atualiza_trecho_checkout_svc.atualiza_trecho_async(data)
    return JsonResponse(result, safe=False, status=202)


@api.get("/v1/trecho_classe_integracao/verifica_atualizacao_checkout_async")
def verifica_atualizacao_trecho_classe_integracao_checkout_async(request, data: AtualizacaoCheckoutAsyncParams):
    result = atualiza_trecho_checkout_svc.verifica_atualizacao(data)
    return JsonResponse(result, safe=False)


@api.post("/v1/trecho_classe_integracao/batch/atualiza")
def atualiza_trecho_classe_integracao_batch(request, data: StatusIntegracaoBatchParams):
    result = StatusIntegracaoSVC(batch_trecho_classe_ids=data.trecho_classe_ids).atualiza()
    return JsonResponse(result)


@api.post("/v1/update-grupo-classe-internal-id")
def update_grupos_classe_rodoviaria(request, data: UpdateGruposClasseLink):
    status_integracao_svc.update_grupos_classe_link(data)
    return JsonResponse({"message": "Link de Grupos Classe atualizados"})


@api.get("/v1/trecho_classe_integracao/verifica")
def verifica_trecho_classe_integracao(request, data: StatusIntegracaoParams = Query(...)):
    if not data.grupo_id and not data.trecho_classe_id:
        return JsonResponse({"message": "É preciso passar grupo_id ou trecho_classe_id."}, status=422)
    result = StatusIntegracaoSVC(data.grupo_id, data.trecho_classe_id).verifica()
    return JsonResponse(result)


@api.get("v1/trechoclasse/get")
def get_trechos_classe(request, params: TrechoClasseGetAtualizaParams = Query(...)):
    if (
        not params.tag
        and not params.integracao
        and not params.company_id
        and not params.grupo_id
        and not params.trechoclasse_ids
    ):
        return JsonResponse(
            {"error": "É necessário algum parâmetro tag, integracao, company_id, grupo_id ou trechoclasse_ids"},
            status=400,
        )
    resp = link_trechoclasse_svc.get(
        params.tag,
        params.integracao,
        params.company_id,
        params.modelo_venda,
        params.grupo_id,
        params.trechoclasse_ids,
    )
    return JsonResponse(resp, safe=False)


@api.post("v1/trechoclasse/atualiza")
def atualiza_link_trecho_classe(request, params: TrechoClasseGetAtualizaParams = Query(...)):
    if (
        not params.tag
        and not params.integracao
        and not params.company_id
        and not params.grupo_id
        and not params.trechoclasse_ids
    ):
        return JsonResponse(
            {"error": "É necessário algum parâmetro tag, integracao, company_id, grupo_id ou trechoclasse_ids"},
            status=400,
        )
    resp = link_trechoclasse_svc.atualiza(
        params.tag,
        params.integracao,
        params.company_id,
        params.modelo_venda,
        params.grupo_id,
        params.trechoclasse_ids,
    )
    return JsonResponse(resp)


@api.post("v1/trechoclasse/remove_tag")
def remove_trechos_classe_tags(request, params: TrechoClasseAddRemoveTagParams = Query(...)):
    return link_trechoclasse_svc.remove_tag(
        params.tag,
        params.integracao,
        params.company_id,
        params.modelo_venda,
        params.grupo_id,
        params.trechoclasse_ids,
        params.days,
    )


@api.post("v1/trechoclasse/add_tag")
def add_trechos_classe_tags(request, params: TrechoClasseAddRemoveTagParams = Query(...)):
    return link_trechoclasse_svc.add_tag(
        params.tag,
        params.integracao,
        params.company_id,
        params.modelo_venda,
        params.grupo_id,
        params.trechoclasse_ids,
        params.days,
    )


@api.post("/v1/local_embarque/atualiza")
def atualiza_link_local_embarque(
    request,
    local_embarque_id: int = None,
    link_id: int = None,
    cidade_internal_id: int = None,
):
    result = links_locais_embarque_svc.update_link_local_embarque(local_embarque_id, link_id, cidade_internal_id)
    return JsonResponse(result, safe=False)


@api.post("/v1/local_embarque/list")
def list_link_local_embarque(request):
    params = ListLinksLocaisForm.parse_raw(request.body)
    result = links_locais_embarque_svc.list_filtered_locais(params)
    return JsonResponse(result, safe=False)


@api.post("/v1/compra/cancela")
def efetua_cancelamento(request, travel_id: int, buseiro_id: int = None, pax_valido: bool = None):
    try:
        return reserva_svc.efetua_cancelamento(travel_id, buseiro_id, pax_valido)
    except PassengerNotRegistered as ex:
        dict_error = {"error": error_str(ex), "error_type": "passenger_not_registered"}
        log_svc.log_reserva_exception("PassengerNotRegistered", extra=dict_error)
        return JsonResponse(dict_error, status=400)
    except PassengerTicketAlreadyPrintedException as ex:
        dict_error = {
            "error": error_str(ex),
            "error_type": "passenger_ticket_already_printed",
        }
        log_svc.log_reserva_exception("PassengerTicketAlreadyPrintedException", extra=dict_error)
        return JsonResponse(dict_error, status=403)
    except PoltronaTrocadaException as ex:
        dict_error = {
            "error": error_str(ex),
            "error_type": "passenger_ticket_seat_changed",
        }
        log_svc.log_reserva_exception("PoltronaTrocadaException", extra=dict_error)
        return JsonResponse(dict_error, status=403)
    except PassengerTicketAlreadyReturnedException as ex:
        dict_error = {
            "error": error_str(ex),
            "error_type": "passenger_ticket_already_canceled",
        }
        log_svc.log_reserva_exception("PassengerTicketAlreadyReturnedException", extra=dict_error)
        return JsonResponse(dict_error, status=403)
    except RodoviariaBlockingException as ex:
        dict_error = {"error": error_str(ex), "error_type": "blocked"}
        log_svc.log_reserva_exception("BlockingException", extra=dict_error)
        return JsonResponse(dict_error, status=406)
    except RodoviariaCompanyNotIntegratedError as ex:
        dict_error = {"error": error_str(ex), "error_type": "company_not_integrated"}
        log_svc.log_reserva_exception("CompanyNotIntegratedError", extra=dict_error)
        return JsonResponse(dict_error, status=422)


@sample_view(sample_rate=1)
@api.post("/v1/compra/comprar")
def efetua_compra(request):
    params = ComprarForm.parse_raw(request.body)
    buserlogger.info("Compra da travel %s", params.travel_id)

    try:
        resp = CompraRodoviariaSVC(params).efetua_compra(params)
        return JsonResponse(resp, safe=False)
    except CampoObrigatorioException as ex:
        dict_error = {
            "error": error_str(ex),
            "error_type": "campo_obrigatorio",
        }
        log_svc.log_reserva_exception("CampoObrigatorioException", extra=dict_error)
        return JsonResponse(dict_error, status=400)
    except RodoviariaTrechoclasseFactoryException as ex:
        dict_error = {"error": error_str(ex), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)
    except DivergenciaPrecoException as ex:
        dict_error = {"error": error_str(ex), "error_type": "preco_divergente"}
        log_svc.log_reserva_exception("DivergenciaPrecoException", extra=dict_error)
        return JsonResponse(dict_error, status=400)
    except PassengerInvalidDocumentException as ex:
        dict_error = {"error": error_str(ex), "error_type": "invalid_document"}
        log_svc.log_reserva_exception("BlockingException", extra=dict_error)
        return JsonResponse(dict_error, status=422)
    except RodoviariaTrechoBloqueadoException as ex:
        dict_error = {"error": error_str(ex), "error_type": "service_not_for_sale"}
        log_svc.log_reserva_exception("RodoviariaTrechoBloqueadoException", extra=dict_error)
        return JsonResponse(dict_error, status=410)
    except PoltronaExpirada as ex:
        dict_error = {"error": error_str(ex), "error_type": "poltrona_expirada"}
        log_svc.log_reserva_exception("PoltronaExpirada", extra=dict_error)
        return JsonResponse(dict_error, status=400)
    except PoltronaIndisponivel as ex:
        dict_error = {"error": error_str(ex), "error_type": "poltrona_indisponivel"}
        log_svc.log_reserva_exception("PoltronaIndisponivel", extra=dict_error)
        return JsonResponse(dict_error, status=409)
    except RodoviariaBlockingException as ex:
        dict_error = {"error": error_str(ex), "error_type": "blocked"}
        log_svc.log_reserva_exception("BlockingException", extra=dict_error)
        return JsonResponse(dict_error, status=406)


@api.post("/v1/add_pax_na_lista")
def add_pax_na_lista(request):
    params = CheckPaxForm.parse_raw(request.body)
    buserlogger.info("Add pax (travel_id=%s, buseiro_id=%s)", params.travel_id, params.passenger.buseiro_id)

    try:
        resp = reserva_svc.add_pax_na_lista(params)
        return JsonResponse(resp, safe=False)
    except RodoviariaTrechoclasseFactoryException as exception:
        dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)
    except DivergenciaPrecoException as ex:
        dict_error = {"error": error_str(ex), "error_type": "preco_divergente"}
        log_svc.log_reserva_exception("DivergenciaPrecoException", extra=dict_error)
        return JsonResponse(dict_error, status=400)
    except HibridoEmissaoForaDaData as ex:
        dict_error = {"error": error_str(ex), "error_type": "hibrido_date_out_of_range"}
        log_svc.log_reserva_exception("HibridoEmissaoForaDaData", extra=dict_error)
        return JsonResponse(dict_error, status=400)
    except RodoviariaBlockingException as ex:
        dict_error = {"error": error_str(ex), "error_type": "blocked"}
        log_svc.log_reserva_exception("BlockingException", extra=dict_error)
        return JsonResponse(dict_error, status=406)


@api.post("/v1/add_multiple_pax_na_lista")
def add_multiple_pax_na_lista(request):
    params = CheckPaxMultipleForm.parse_raw(request.body)
    async_add = params.async_add

    if not async_add:
        form = DefaultForm(trechoclasse_id=params.trechoclasse_id, force_renew_link=True)
        try:
            resp = CompraRodoviariaSVC(form).add_multiple_pax_na_lista_passageiros_viagem(form)
            return JsonResponse(resp, safe=False)
        except RodoviariaTrechoclasseFactoryException as exception:
            dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
            log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
            return JsonResponse(dict_error, status=404)
        except DivergenciaPrecoException as ex:
            dict_error = {"error": error_str(ex), "error_type": "preco_divergente"}
            log_svc.log_reserva_exception("DivergenciaPrecoException", extra=dict_error)
            return JsonResponse(dict_error, status=400)

    reserva_svc.add_multiple_pax_na_lista_task.s(json.dumps(params.dict())).apply_async()
    return {"mensagem": "A task de adição múltipla de pax na lista foi disparada"}


@api.post("/v1/add_pax_na_lista_batch")
def add_pax_na_lista_batch(request):
    params = CheckPaxBatchForm.parse_raw(request.body)
    return reserva_svc.add_pax_na_lista_batch(params.passageiros)


@api.get("/v1/dados_bpe_passagem")
def dados_bpe_passagem(request, travel_id: int):
    if not bpe_svc.travel_has_bpe(travel_id):
        return JsonResponse({}, status=403)
    resp = bpe_svc.dados_bpe_passagem(travel_id)
    return JsonResponse(resp, safe=False)


@api.get("/v1/dados_bpe_passagem_batch")
def dados_bpe_passagem_batch(request, data: DadosBpePassagemBatchParams = Query(...)):
    resp = bpe_svc.dados_bpe_passagem_batch(data.travel_ids)
    return JsonResponse(resp, safe=False)


@api.get("/v1/has_bpe")
def has_bpe(request, trechoclasse_id: int, travel_id: int):
    return JsonResponse({"has_bpe": bpe_svc.travel_has_bpe(travel_id)})


@api.post("/v1/remaneja_passageiro")
def remaneja_passageiro(request):
    params = RemanejaPassageiroForm.parse_raw(request.body)
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    return JsonResponse(resp, safe=False)


@api.post("/v1/remaneja_passageiros_async")
def remaneja_passageiros_async(request, data: RemanejaPassageirosAsyncParams):
    resp = remanejamento_svc.remaneja_passageiros_async(data)
    return JsonResponse(resp, safe=False)


@api.get("/v1/remanejamentos_pendentes")
def remanejamentos_pendentes_ou_completos_recentes(request, grupo_id):
    resp = remanejamento_svc.remanejamentos_pendentes_ou_completos_recentes(grupo_id)
    return JsonResponse(resp)


@api.post("/v1/bulk-get-poltronas")
def bulk_get_poltronas(request, data: BulkGetPoltronasParams):
    try:
        resp = staff_rodoviaria_svc.bulk_get_poltronas(data)
        return JsonResponse(resp)
    except RodoviariaTrechoclasseFactoryException as exception:
        dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)


@api.get("/v1/itinerario")
def itinerario(request, grupo_id: int):
    itinerario = staff_rodoviaria_svc.StaffRodoviariaSVC().itinerario(grupo_id)
    if not itinerario:
        return JsonResponse({"itinerario": None})
    return JsonResponse({"itinerario": itinerario}, safe=False)


@api.get("/v1/get_rotas_empresa")
def get_rotas_empresa(request, company_rodoviaria_id: int):
    try:
        form_data = ItinerariosNaoIntegradosForm.parse_raw(request.GET.get("filters", "{}"))
        response = rota_svc.get_rotas_empresa_paginator(company_rodoviaria_id, form_data)
        return JsonResponse(response, safe=False)
    except RodoviariaIntegracaoNotFoundException:
        return JsonResponse(
            {"mensagem": "Integração não implementada para empresa"},
            status=422,
        )
    except (RodoviariaCompanyNotFoundException, Company.DoesNotExist):
        return JsonResponse(
            {"mensagem": f"Não foi possível encontrar empresa com o ID {company_rodoviaria_id}"},
            status=422,
        )
    except RodoviariaException as ex:
        capture_exception(ex)
        return JsonResponse({"mensagem": ex.message}, status=400)


@api.post("/v1/fetch_rotas")
def fetch_rotas(request, company_id: int = None, grupo_id: int = None):
    if not company_id and not grupo_id:
        return JsonResponse({"message": "Necessário passar o grupo_id ou company_id"}, status=422)

    result = marketplace_fetch_rotas_svc.marketplace_fetch_rotas(company_id, grupo_id)

    return JsonResponse(result, safe=False)


@api.get("/v1/fetch_rota_external")
def fetch_rota_external(
    request, company_id: int, id_external: int, data: str = None, modelo_venda=Company.ModeloVenda.MARKETPLACE
):
    try:
        data = datetime.strptime(data, "%Y-%m-%d") if data else None
        res = marketplace_fetch_rotas_svc.fetch_rota_external(company_id, id_external, data, modelo_venda)
    except (RodoviariaException, ValueError) as e:
        return JsonResponse({"error": error_str(e)}, status=400)
    return JsonResponse(res, safe=False)


@api.post("/v1/solicita_cancelamento")
def solicita_cancelamento(request, travel_id: int, buseiro_id: int = None):
    solicita_cancelamento_svc.solicita_cancelamento(travel_id, buseiro_id)
    return JsonResponse({"sucesso": "Cancelamento(s) solicitado"})


@api.post("/v1/solicita-cancelamento-por-passagens-ids")
def solicita_cancelamento_por_passagens_ids(request, data: SolicitaCancelamentoPorPassagemIdParams):
    solicita_cancelamento_svc.solicita_cancelamento_por_passagens_ids(data.passagens_ids)
    return JsonResponse({"sucesso": "Cancelamento(s) solicitado"})


@api.post("/v1/cancela_passagens_pendentes")
def cancela_passagens_pendentes(request):
    cancela_passagens_pendentes_svc.cancela_passagens_pendentes()
    return JsonResponse({"sucesso": "Cancelamento de passagens pendentes disparadados assincronamente"})


@api.post("/v1/solicita_cancelamento_travels")
def solicita_cancelamento_travels(request, data: SolicitaCancelamentoTravelsParams):
    solicita_cancelamento_svc.solicita_cancelamento_travels(data.travel_ids)
    return JsonResponse({"sucesso": "Cancelamento(s) solicitado"})


@api.get("/v1/fetch_trechos_vendidos_uma_rota")
def fetch_trechos_vendidos_uma_rota(request, grupo_id: int = None, rodoviaria_rota_id: int = None):
    if not grupo_id and not rodoviaria_rota_id:
        return JsonResponse({"error": "É necessário passar grupo_id ou rodoviaria_rota_id"})
    try:
        response = trechos_vendidos_svc.fetch_trechos_vendidos_uma_rota(grupo_id, rodoviaria_rota_id)
        return JsonResponse(response, safe=False)
    except TaskInExecutionException as exc:
        return JsonResponse({"mensagem": exc.message}, status=400)


@api.get("/v1/fetch_all_company_trechos")
def fetch_trechos_by_company_id(request, company_internal_id: int, modelo_venda=Company.ModeloVenda.MARKETPLACE):
    fetch_trechos_vendidos_svc.fetch_trechos_by_company_id(company_internal_id, modelo_venda)
    return JsonResponse(
        {
            "mensagem": (
                "Fetch de trechos vendidos de todas as rotas ativas foi iniciado para a empresa"
                f" {company_internal_id} {modelo_venda=}"
            )
        }
    )


@api.get("/v1/get_trechos_vendidos")
def get_trechos_vendidos(request, grupo_id: int = None, rodoviaria_rota_id: int = None):
    if not grupo_id and not rodoviaria_rota_id:
        return JsonResponse({"error": "É necessário passar grupo_id ou rodoviaria_rota_id"}, status=422)

    response = trechos_vendidos_svc.get_trechos_vendidos(grupo_id, rodoviaria_rota_id)
    return JsonResponse(response, safe=False)


@api.post("/v1/descobrir_rotas_proximos_dias")
def descobrir_rotas_proximos_dias(
    request,
    company_internal_id: int,
    next_days: int = 7,
    shift_days: int = 2,
    modelo_venda=Company.ModeloVenda.MARKETPLACE,
):
    company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
    resp = descobrir_rotas_svc.descobrir_rotas_proximos_dias(
        company=company, next_days=next_days, shift_days=shift_days
    )
    return JsonResponse(resp, safe=False)


@api.post("/v1/descobrir_operacao_proximos_dias")
def descobrir_operacao_proximos_dias(
    request,
    company_internal_id: int,
    next_days: int = 7,
    shift_days: int = 2,
    modelo_venda=Company.ModeloVenda.MARKETPLACE,
):
    company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
    resp = descobrir_operacao_svc.descobrir_operacao_proximos_dias(
        company=company, next_days=next_days, shift_days=shift_days
    )
    return JsonResponse(resp, safe=False)


# PROVISORIO
@api.get("/v1/passagens_sem_travel_correspondente")
def passagens_sem_travel_correspondente(request):
    passagens = cancela_passagens_pendentes_svc.passagens_sem_travel_correspondente()
    response = {
        "quantidade": len(passagens),
        "passagens": [passagem.serialize() for passagem in passagens],
    }
    return JsonResponse(response, safe=False)


# PROVISORIO
@api.post("/v1/cancela_passagens_sem_travel_correspondente")
def cancela_passagens_sem_travel_correspondente(request):
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    return JsonResponse(response, safe=False)


# PROVISORIO
@api.post("/v1/hibrido/link-grupos-classe")
def link_grupos_classe_hibridos(request):
    link_grupos_classe_svc.link_grupos_classe_nao_linkados()
    return JsonResponse({}, safe=False)


# PROVISORIO
@api.post("/v1/hibrido/cancela-grupos-classe-invalidos")
def cancela_grupos_classe_invalidos(request):
    link_grupos_classe_svc.cancela_grupos_classe_com_link_invalido()
    return JsonResponse({}, safe=False)


@api.post("/v1/hibrido/create-company")
def company_transbrasil_create(request, data: CompanyTransbrasilParams):
    cria_company_transbrasil(data.company_internal_id, data.name, data.company_external_id)
    return {"message": "Company criada"}


@api.post("/v1/hibrido/cadastrar-trechos")
def cadastrar_trechos(request, data: CadastrarTrechosParams):
    rotas_transbrasil_svc.cadastrar_trechos(data.company_id, data.trechos)
    return JsonResponse({"message": "Cadastro de trechos iniciado"}, safe=False)


@api.get("/v1/hibrido/cadastrar-grupos-params")
def cadastrar_grupos_params(request, company_id: int, rota_internal_id: int = None):
    response = cadastrar_grupos_hibridos_svc.cadastrar_grupos_params(company_id, rota_internal_id)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/cadastrar-grupos")
def cadastrar_grupos(request, data: CadastrarGruposParams):
    buserlogger.info("cadastrar_grupos: %s", data)
    try:
        response = cadastrar_grupos_hibridos_svc.CadastrarGrupoSVC(data.company_id).cadastrar_grupos(
            data.grupos, rota_internal_id=data.rota_internal_id
        )
    except veiculos_svc.GetOrCreateVeiculosError as ex:
        return JsonResponse({"error": error_str(ex)}, safe=False, status=422)
    except cadastrar_grupos_hibridos_svc.RotaNaoCadastradaException as ex:
        return JsonResponse(
            {
                "error": error_str(ex),
                "error_type": "rota_nao_cadastrada",
                "company_id": ex.company_id,
                "rota_id": ex.rota_internal_id,
            },
            safe=False,
            status=412,
        )
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/cancelar-grupos-classe")
def cancelar_grupos_classe(request, data: CancelarGruposClasseParams):
    cancelar_grupos_hibridos_svc.cancelar_grupos_classe(data.company_id, data.grupos_classe_ids)
    return JsonResponse({"message": "Grupos Classe Cancelados"})


@api.post("/v1/hibrido/fechar-grupos-classe")
def fechar_grupos_classe(request, data: CancelarGruposClasseParams):
    cancelar_grupos_hibridos_svc.fechar_grupos_classe(data.company_id, data.grupos_classe_ids)
    return JsonResponse({"message": "Grupos Classe Fechados"})


@api.post("/v1/hibrido/update-grupos-criados")
def update_grupos_hibridos_criados(request, company_id):
    response = update_grupos_hibridos_svc.update_grupos_hibridos_criados(company_id)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/fill-grupo-classe-external-id")
def fill_grupo_classe_external_id(request):
    response = update_grupos_hibridos_svc.fill_grupo_classe_external_id()
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/fetch-grupos-criados-anteriormente")
def fetch_grupos_criados_anteriormente(request, company_id):
    response = update_grupos_hibridos_svc.fetch_grupos_criados_anteriormente(company_id)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/cadastrar-rota")
def cadastrar_rota(request, data: CadastrarRotaHibridoParams):
    response = rotas_transbrasil_svc.cadastrar_rota(data)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/cadastrar-ou-editar-rota")
def cadastrar_ou_editar_rota(request, data: CadastrarOuEditarRotaHibridoParams):
    response = rotas_transbrasil_svc.cadastrar_ou_editar_rota(data)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/sincronizar-rota")
def sincronizar_rota(request, data: CadastrarRotaHibridoParams):
    response = rotas_transbrasil_svc.sincronizar_rota(data)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/editar-rota")
def editar_rota(request, data: EditarRotaHibridoParams):
    response = rotas_transbrasil_svc.editar_rota(data)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/criar-itinerario")
def criar_itinerario(request, data: CadastrarItinerarioParams):
    response = rotas_transbrasil_svc.criar_itinerario(data)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/atualizar-locais-embarque")
def atualizar_locais_embarque(request, data: AtualizarLocaisParams):
    response = rotas_transbrasil_svc.atualizar_locais_embarque(data)
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/atualizar-locais-embarque-batch")
def atualizar_locais_embarque_batch(request, data: AtualizarLocaisBatchParams):
    response = rotas_transbrasil_svc.atualizar_locais_embarque_batch(data)
    return JsonResponse(response, safe=False)


@api.post("/v1/link_rotas")
def link_rotas(request, company_id: int):
    len_rotas_atualizadas = link_rotas_svc.atualizar_id_internal_rotas_empresa(company_id)
    return JsonResponse(
        {"message": f"Foram atualizadas {len_rotas_atualizadas} rotas para a empresa de ID {company_id}"},
        safe=False,
    )


@api.post("/v1/atualizar_link_rota")
def atualizar_link_rota(request, rota_internal_id_antigo: int, rota_internal_id_novo: int):
    len_rotas_atualizadas = link_rotas_svc.atualizar_id_internal_rota_por_internal_id_antigo(
        rota_internal_id_antigo, rota_internal_id_novo
    )
    return JsonResponse(
        {"message": f"Foram atualizadas {len_rotas_atualizadas} rotas"},
        safe=False,
    )


@api.get("/v1/total_rotas_integradas")
def total_rotas_integradas(request):
    company_ids = request.GET.getlist("company_ids")
    total_rotas = link_rotas_svc.get_total_rotas_integradas(company_ids)
    return JsonResponse(total_rotas, safe=False)


@api.post("/v1/motoristas/escala")
def escala_motorista(request, data: EscalaMotoristaParams):
    motorista_svc.escala_motorista(data.motorista, data.grupo_id)
    return {"message": "Motorista escalado"}


@api.post("/v1/onibus/fetch-mapas-veiculos-api")
def fetch_mapas_veiculos_api(request):
    response = veiculos_svc.fetch_mapas_veiculos_api()
    return JsonResponse(response)


@api.post("/v1/onibus/fetch-veiculos-api")
def fetch_veiculos_api(request, company_id):
    response = veiculos_svc.fetch_veiculos_api(company_id)
    return JsonResponse(response)


@api.post("/v1/onibus/create-or-link")
def get_link_or_create_veiculo(request, data: CadastrarVeiculoParams):
    if not data.company_id:
        return JsonResponse({"error": "Necessario passar o company_id"}, status=400)
    try:
        veiculos = veiculos_svc.get_link_or_create_veiculo(data)
    except veiculos_svc.GetOrCreateVeiculosError as ex:
        return JsonResponse({"error": error_str(ex)}, status=422)
    response = [v.to_dict_json() for v in veiculos]
    return JsonResponse(response, safe=False)


@api.post("/v1/link_trechos_vendidos")
def link_trechos_vendidos(request, company_id: int = None):
    if company_id:
        count = link_trechos_vendidos_svc.atualizar_id_internal_trechos_vendidos_company(company_id)
        return JsonResponse(
            {"message": f"Foram atualizados {count} trechos vendidos para a empresa de ID {company_id}"},
            safe=False,
        )


@api.get("/v1/classes_e_precos_rota")
def classes_e_precos_rota(request, rota_id: int, ids_trechos_vendidos_filter: list[int] = None):
    response = classes_e_precos_svc.get_classes_e_precos(
        internal_rota_id=rota_id,
        ids_internal_trechos_vendidos=ids_trechos_vendidos_filter,
    )
    return JsonResponse(response, safe=False)


@api.post("/v1/hibrido/onibus/escalar")
def escalar_veiculo(request, data: AlterarGrupoParams):
    alterar_grupo_svc.alterar_grupo_task.delay(data.json())
    return JsonResponse({})


@api.post("/v1/hibrido/alterar-grupo")
def alterar_grupo(request, data: AlterarGrupoParams):
    buserlogger.info("alterar_grupo: %s", data)
    alterar_grupo_svc.alterar_grupo_task.delay(data.json())
    return JsonResponse({})


@api.post("/v1/atualizar_checkpoints")
def atualizar_checkpoints(request, rota_id: int = None, company_id: int = None, modelo_venda: str = None):
    if rota_id:
        rota_svc.atualizar_checkpoints_rota(rota_id)
        return JsonResponse({"mensagem": f"checkpoints da rota {rota_id} atualizados"})
    if company_id:
        if not modelo_venda:
            return JsonResponse({"error": "necessário passar o modelo_venda"}, status=404)
        rota_svc.atualizar_checkpoints_rotas_por_empresa(company_id, modelo_venda)
        return JsonResponse(
            {"mensagem": f"checkpoints das rotas da empresa ({company_id}, {modelo_venda}) atualizados"}
        )
    return JsonResponse(
        {"error": "necessário passar rota_id ou (company_id e modelo_venda)"},
        status=404,
    )


@api.post("/v1/hibrido/verificar-grupos-cancelados")
def verificar_grupos_hibridos_cancelados(request):
    response = verificar_grupos_cancelados_svc.check_passagens_canceladas_hibrido(limit=10)
    return JsonResponse(response, safe=False)


@api.get("/v1/fetch_rotina")
def fetch_rotina(
    request,
    rota_id: int = None,
    rodoviaria_rota_id: int = None,
    next_days: int = 14,
    first_day: str = None,
    async_fetch: bool = False,
):
    try:
        if first_day:
            first_day = to_default_tz_required(datetime.strptime(first_day, "%Y-%m-%d"))
        else:
            first_day = to_default_tz_required(datetime.now())

        if rota_id:
            rotina = rotina_svc.fetch_rotina_by_internal_id(rota_id, next_days, first_day, async_fetch)
            return JsonResponse(rotina)
        if rodoviaria_rota_id:
            rotina = rotina_svc.fetch_rotina(rodoviaria_rota_id, next_days, first_day, async_fetch)
            return JsonResponse(rotina)
        return JsonResponse(
            {"error": "É necessário passar rota_id ou rodoviaria_rota_id como parâmetro"},
            status=400,
        )
    except RodoviariaRotaNotFoundException as e:
        return JsonResponse({"error": error_str(e)}, status=404)
    except (
        RodoviariaNoLocalCheckpointException,
        RodoviariaNoTimezoneException,
        ValueError,
    ) as e:
        return JsonResponse({"error": error_str(e)}, status=400)


@api.get("/v1/get_rotina")
def get_rotina(request, rota_id: int = None, rodoviaria_rota_id: int = None, next_days: int = 30):
    try:
        if rota_id:
            rotina = rotina_svc.get_rotina_by_internal_id(rota_id, next_days)
            return JsonResponse(rotina)
        if rodoviaria_rota_id:
            rotina = rotina_svc.get_rotina(rodoviaria_rota_id, next_days)
            return JsonResponse(rotina)
        return JsonResponse(
            {"error": "É necessário passar rota_id ou rodoviaria_rota_id como parâmetro"},
            status=400,
        )
    except RodoviariaRotaNotFoundException as e:
        return JsonResponse({"error": error_str(e)}, status=404)
    except (RodoviariaNoLocalCheckpointException, RodoviariaNoTimezoneException) as e:
        return JsonResponse({"error": error_str(e)}, status=400)


@api.get("/v1/comparar_rotas_empresa")
def comparar_rotas_empresa(
    request,
    company_id: int,
    apenas_linkadas_erradas: bool = True,
    recalcular_duracao: bool = True,
):
    return JsonResponse(
        comparar_rotas(company_id, apenas_linkadas_erradas, recalcular_duracao),
        safe=False,
    )


@api.get("/v1/features")
def list_all_possible_features(request):
    is_staff = request.GET.get("is_staff")
    features = company_svc.get_all_possible_features(is_staff=is_staff)
    return JsonResponse({"features": features}, safe=False)


@api.post("/v1/create_company")
def create_company(request):
    data = CreateRodoviariaCompanyForm.parse_raw(request.body)
    try:
        result = company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )
        return JsonResponse(result)
    except RodoviariaUnauthorizedError as exc:
        return JsonResponse({"error": error_str(exc)}, status=401)
    except (RodoviariaException, RodoviariaCompanyExistenteException) as exc:
        return JsonResponse({"error": error_str(exc)}, status=400)


@api.get("/v1/company_login")
def get_company_login(request, integracao: str, company_id: int, modelo_venda: str):
    try:
        result = company_svc.get_login(integracao=integracao, company_id=company_id, modelo_venda=modelo_venda)
    except (
        RodoviariaLoginNotFoundException,
        RodoviariaIntegracaoNotFoundException,
    ) as exc:
        return JsonResponse({"error": error_str(exc)}, status=400)
    return JsonResponse({"login": result})


@api.post("/v1/update_company")
def update_company(request):
    data = CreateRodoviariaCompanyForm.parse_raw(request.body)
    try:
        company_svc.update_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )
    except RodoviariaCompanyNotFoundException as exc:
        return JsonResponse({"error": error_str(exc)}, status=400)
    return JsonResponse({"success": True})


@api.post("/v1/hard_stop_empresa")
def hard_stop_empresa(request):
    data = CompanyForm.parse_raw(request.body)
    try:
        company_svc.hard_stop_empresa(
            data.company_internal_id,
            data.modelo_venda,
        )
    except RodoviariaCompanyNotFoundException as exc:
        return JsonResponse({"error": error_str(exc)}, status=400)
    return JsonResponse({"success": True})


@api.post("/v1/revert_hard_stop_empresa")
def revert_hard_stop_empresa(request, company_internal_id, modelo_venda):
    try:
        company_svc.revert_hard_stop_empresa(
            company_internal_id,
            modelo_venda,
        )
    except (RodoviariaCompanyNotFoundException, RodoviariaUnableRevertHardStop) as exc:
        return JsonResponse({"error": error_str(exc)}, status=400)
    return JsonResponse({"success": True})


@api.get("/v1/fetch_rotinas_empresa")
def fetch_rotinas_empresa(request, company_id: int, next_days: int = 30, first_day: str = None):
    try:
        if first_day:
            first_day = to_default_tz_required(datetime.strptime(first_day, "%Y-%m-%d"))
        else:
            first_day = to_default_tz_required(datetime.now())
        resp = rotina_svc.fetch_rotinas_empresa(
            company_internal_id=company_id, next_days=next_days, first_day=first_day
        )
    except ValueError as e:
        return JsonResponse({"error": error_str(e)}, status=400)
    return JsonResponse(resp, safe=False)


@api.get("/v1/verifica-rotas-integradas")
def verifica_rotas_integradas(request, data: VerificaLinkRotasParams):
    response = rotas_transbrasil_svc.verifica_link_rotas(data.rotas)
    return JsonResponse(response, safe=False)


@api.get("/v1/fetch_operacao_empresa_marketplace")
def fetch_operacao_empresa_marketplace(request, company_internal_id: int):
    try:
        company_svc.fetch_operacao_empresa_marketplace(company_internal_id)
        return JsonResponse({"mensagem": "Busca de dados da operação iniciada"})
    except RodoviariaUnauthorizedError as exc:
        return JsonResponse({"error": error_str(exc)}, status=401)


@api.post("/v1/fetch_external_totalbus_companies")
def fetch_external_totalbus_companies(request):
    login = TotalbusNoCompanyLogin.parse_raw(request.body)
    try:
        external_companies = totalbus_api.lista_empresas_api(login.dict())
        return JsonResponse({"external_companies": external_companies})
    except RodoviariaUnauthorizedError:
        return JsonResponse({"error": "Login inválido"}, status=401)
    except RodoviariaTimeoutException:
        return JsonResponse({"error": "Login inválido"}, status=408)


@api.post("/v1/fetch_formas_pagamento")
def fetch_formas_pagamento(request):
    params = FetchFormasPagamentoForm.parse_raw(request.body)
    try:
        formas_pagamento = company_svc.PROVIDERS[params.integracao].fetch_formas_pagamento(params.login)
        return JsonResponse({"formas_pagamento": formas_pagamento})
    except RodoviariaUnauthorizedError:
        return JsonResponse({"error": "Login inválido"}, status=401)
    except RodoviariaTimeoutException:
        return JsonResponse({"error": "Login inválido"}, status=408)


@api.post("/v1/login/praxio")
def verify_praxio_login(request):
    login = PraxioLoginForm.parse_raw(request.body)
    try:
        praxio_api.verify_praxio_login(nome=login.name, senha=login.password, cliente=login.cliente)
        return JsonResponse({"success": True})
    except RodoviariaUnauthorizedError:
        return JsonResponse({"error": "Login inválido"}, status=401)


@api.get("v1/get_auth_key_ti_sistemas")
def get_auth_key_ti_sistemas(request):
    auth_key = TiSistemasLogin.get_active_auth_key()
    return JsonResponse({"auth_key": auth_key})


@api.post("/v1/link_trechos_classes_async")
def link_trechos_classe_async(
    request,
    use_update_price_queue: bool = False,
    use_hot_update_price_queue: bool = False,
    use_update_top_divergencias: bool = False,
):
    trechos = LinkTrechosClassesAsyncParams.parse_raw(request.body)
    link_trechoclasse_async_svc.link_trechoclasse_async(
        trechos.trechos,
        use_update_price_queue,
        use_hot_update_price_queue,
        use_update_top_divergencias,
    )
    return JsonResponse({"mensagem": "Link de trechos classe async foi iniciado"}, safe=False)


@api.get("/v1/link_trechoclasse_async/get_tagged")
def link_trechoclasse_async_get_tagged(request):
    result = link_trechoclasse_async_svc.get_tagged_trechos_classes()
    return JsonResponse(result, safe=False)


@api.get("/v1/get-trechos-overbooking")
def get_trechos_overbooking(request):
    ids_trechos = cadastrar_grupos_hibridos_svc.get_trechos_overbooking()
    return JsonResponse({"trechos": ids_trechos}, safe=False)


@api.get("/v1/hibrido/get-trechos-classe-error")
def get_trechos_classe_error_hibrido(request):
    ids_trechos = cadastrar_grupos_hibridos_svc.get_trechos_classe_error_hibrido()
    return JsonResponse({"trechos": ids_trechos}, safe=False)


@api.post("/v1/hibrido/delete-trechos-classe-error")
def delete_trechos_classe_error_hibrido(request, data: DeleteTrechoClasseErrorParams):
    cadastrar_grupos_hibridos_svc.delete_trechos_classe_error_hibrido(data.trechos_classe_ids)
    return JsonResponse({}, safe=False)


@api.post("/v1/link_trechoclasse_async/remover_tags")
def link_trechoclasse_async_remover_tags(request):
    trechos_por_tag = RemoverTagsTrechosAtualizadosParams.parse_raw(request.body).dict()
    link_trechoclasse_async_svc.remover_tags_trechos_atualizados(trechos_por_tag)
    return JsonResponse({}, safe=False)


@api.get("/v1/list-passagens-confirmadas-por-company")
def lista_passagens_confirmadas_por_empresa(request, data: PassagensConfirmadasPorEmpresaParams = Query(...)):
    passagens = passagens_pendentes_svc.lista_passagens_confirmadas_por_empresa(data)
    return JsonResponse(passagens, safe=False)


@api.post("/v1/add-multiples-pax-async")
def add_multiple_pax_async(request, data: AddMultiplePaxAsyncParams):
    passagens_pendentes_svc.emitir_passagens_pendentes_async(passagens=data.passagens, modelo_venda=data.modelo_venda)
    return JsonResponse({})


@api.post("/v1/lista-empresas-api")
def lista_empresas_api(request, data: ListaEmpresasAPIParams):
    try:
        external_companies = company_svc.lista_empresas_api(data.login_params)
        return JsonResponse({"external_companies": external_companies})
    except RodoviariaUnauthorizedError:
        return JsonResponse({"error": "Login inválido"}, status=401)
    except RodoviariaTimeoutException:
        return JsonResponse({"error": "Login inválido"}, status=408)


@api.get("/v1/get-status-bpe")
def get_status_bpe(request, passagem_id):
    response = bpe_svc.get_status_bpe(passagem_id)
    return JsonResponse(response)


@api.post("/v1/update_trechos_vendidos_rota_integrada")
def update_trechos_vendidos_de_rota_integrada(request):
    data = json.loads(request.body)
    rota_svc.update_trechos_vendidos_internal_ids(data["rota_internal_id"], data["trechos_internal_ids"])
    return JsonResponse({"success": True})


@api.post("/v1/passagens/list")
def list_passagens(request):
    data = json.loads(request.body)
    passagens = get_passagens_svc.get_passagens(
        travel_ids=data["travel_ids"],
        buseiro_ids=data["buseiro_ids"],
        status=data["status"],
        with_preco_rodoviaria=data["with_preco_rodoviaria"],
    )

    return JsonResponse({"passagens": passagens})


@api.post("/v1/passagens/list_passagens_company_id")
def list_passagens_by_company_id(request):
    data = PassagensPorEmpresaParams.parse_raw(request.body)
    passagens, fator_conexoes, total_rows = relatorio_passagens_svc.get_passagens_empresa(data)

    return JsonResponse({"passagens": passagens, "fator_conexoes": fator_conexoes, "total_rows": total_rows})


@api.get("/v1/fetch_data_limite_rotas")
def fetch_data_limite_rotas(request, company_internal_id: int, modelo_venda=Company.ModeloVenda.MARKETPLACE):
    retorno = fetch_data_limite_rotas_svc.fetch_data_limite_rotas(
        company_internal_id=company_internal_id, modelo_venda=modelo_venda
    )

    return JsonResponse(retorno, safe=False)


@api.post("/v1/bulk-grupo-tem-passagem-emitida")
def bulk_grupo_tem_passagem_emitida(request):
    form = BulkGrupoTemPassagemEmitidaParams.parse_raw(request.body)
    grupos_map = get_passagens_svc.grupos_com_emissao(form.grupos_passenger_map)
    return JsonResponse(grupos_map)


@api.get("/v1/list-trechos-maiores-divergencias-preco")
def trechos_empresa_com_maiores_divergencias_de_preco(request, days_behind: int = 1, quantity: int = 20):
    resp = divergencia_precos_svc.trechos_empresa_com_maiores_divergencias_de_preco(days_behind, quantity)
    return JsonResponse(resp, safe=False)


@api.post("/v1/passagens/get")
def get_passagem_info(request):
    data = json.loads(request.body)
    passagem_list = get_passagens_svc.get_passagem_info(
        travel_id=data["travel_id"],
        buseiro_id=data["buseiro_id"],
    )
    return JsonResponse(passagem_list, safe=False)


@api.get("/v1/get_data_limite_rotas_integradas")
def get_data_limite_rotas_integradas(request, data: IdsEmpresasParams = Query(...)):
    return JsonResponse(fetch_data_limite_rotas_svc.get_data_limite_rotas_integradas(data.company_internal_ids))


# Integração automática da operação ### TODO: Remover esse comentario e colocar os prefixos da integracao_automatica


@api.get("/v1/auto_integra_operacao/get_rotas_ativas_para_reintegrar")
def get_rotas_ativas_para_reintegrar(request, data: IdsEmpresasParams = Query(...)):
    rotas = auto_integra_operacao_svc.get_rotas_ativas_para_reintegrar(data.company_internal_ids)
    return JsonResponse(rotas, safe=False)


@api.get("/v1/auto_integra_operacao/get_rotas_novas_para_integrar")
@api.get("/v1/get_rotas_novas_para_integrar")
def get_rotas_novas_para_integrar(request, data: IdsEmpresasParams = Query(...)):
    rotas = auto_integra_operacao_svc.get_rotas_novas_para_integrar(data.company_internal_ids)
    return JsonResponse(rotas, safe=False)


@api.get("/v1/auto_integra_operacao/get_ids_rotas_integradas_inativas")
@api.get("/v1/get_ids_rotas_integradas_inativas")
def get_ids_rotas_integradas_inativas(request, data: IdsEmpresasParams = Query(...)):
    rotas = auto_integra_operacao_svc.get_ids_rotas_integradas_inativas(data.company_internal_ids)
    return JsonResponse(rotas, safe=False)


@api.get("/v1/auto_integra_operacao/get_rotinas_inativas_por_rota_integrada_ativa")
@api.get("/v1/get_rotinas_inativas_por_rota_integrada_ativa")
def get_rotinas_inativas_por_rota_integrada_ativa(request, data: IdsEmpresasParams = Query(...)):
    rotas = auto_integra_operacao_svc.get_rotinas_inativas_por_rota_integrada_ativa(data.company_internal_ids)
    return JsonResponse(rotas, safe=False)


@api.get("/v1/auto_integra_operacao/get_ids_empresas_auto_integra_operacao_hoje")
@api.get("/v1/get_ids_empresas_criar_grupos_hoje")
def get_ids_empresas_auto_integra_operacao_hoje(request):
    try:
        response = auto_integra_operacao_svc.get_empresas_integracao_automatica_rodar_hoje()
        return JsonResponse(response, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"mensagem": ex.message}, status=400)


@api.get("/v1/auto_integra_operacao/get_ids_empresas_auto_integra_operacao_agora")
def get_ids_empresas_auto_integra_operacao_agora(request):
    try:
        response = auto_integra_operacao_svc.get_empresas_integracao_automatica_agora()
        return JsonResponse(response, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"mensagem": ex.message}, status=400)


@api.get("/v1/auto_integra_operacao/get_ids_rotas_ativas_empresa")
@api.get("/v1/get_ids_rotas_ativas_empresa")
def get_ids_rotas_ativas_empresa(request, company_internal_id, modelo_venda):
    try:
        response = auto_integra_operacao_svc.get_ids_rotas_ativas_empresa(company_internal_id, modelo_venda)
        return JsonResponse(response, safe=False)
    except ValueError as ex:
        return JsonResponse({"mensagem": ex.message}, status=400)
    except RodoviariaException as ex:
        return JsonResponse({"mensagem": ex.message}, status=500)


@api.post("/v1/auto_integra_operacao/get_detalhes_rotinas_por_rota")
@api.post("/v1/get_detalhes_rotinas_por_rota")
def get_detalhes_rotinas_por_rota(request):
    params = GetDetalhesRotinasPorRotaParams.parse_raw(request.body)
    try:
        response = auto_integra_operacao_svc.get_detalhes_rotinas_por_rota(
            params.rodoviaria_rota_ids,
            params.trechos_vendidos_ids_por_rota_id,
            params.datetimes_a_ignorar_por_rota_id,
            params.extend_dates,
        )
        return JsonResponse(response, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"message": ex.message}, status=500)


@api.post("/v1/auto_integra_operacao/pos_salvar_rota")
@api.post("/v1/pos_salvar_rota")
def pos_salvar_rota(request):
    params = PosSalvarRotaParams.parse_raw(request.body)
    rota_internal_id = params.rota.id_internal
    rodoviaria_rota_id = params.rota.rodoviaria_rota_id
    trechos_vendidos = params.trechos_vendidos

    auto_integra_operacao_svc.pos_salvar_rota(rota_internal_id, rodoviaria_rota_id, trechos_vendidos)

    return JsonResponse({}, safe=False)


@api.post("/v1/auto_integra_operacao/bulk_pos_salvar_rota")
def bulk_pos_salvar_rota(request):
    params = parse_obj_as(list[PosSalvarRotaParams], json.loads(request.body))
    # TODO: tech-debt -> esse endpoint não é bulk de verdade
    for param in params:
        rota_internal_id = param.rota.id_internal
        rodoviaria_rota_id = param.rota.rodoviaria_rota_id
        trechos_vendidos = param.trechos_vendidos

        auto_integra_operacao_svc.pos_salvar_rota(rota_internal_id, rodoviaria_rota_id, trechos_vendidos)

    return JsonResponse({}, safe=False)


@api.get("/v1/auto_integra_operacao/get_rota")
@api.get("/v1/get_rota")
def get_rota(request, rodoviaria_rota_id: int):
    try:
        response = auto_integra_operacao_svc.get_detalhes_para_integrar_uma_rota(rodoviaria_rota_id)
        return JsonResponse(response, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"message": ex.message}, status=400)


@api.get("/v1/auto_integra_operacao/get_detalhes_rotinas")
@api.get("/v1/get_detalhes_rotinas")
def get_detalhes_rotinas(request, params: GetDetalhesRotinasParams = Query(...)):
    try:
        detalhes_uma_rota = auto_integra_operacao_svc.get_detalhes_rotinas_uma_rota(
            params.rodoviaria_rota_id, params.ids_trechos_vendidos_filter, None, params.start_date, params.end_date
        )
        return JsonResponse(detalhes_uma_rota, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"message": ex.message}, status=500)


@api.get("/v1/auto_integra_operacao/verificar_elegibilidade")
def verificar_elegibilidade_auto_integra_operacao(request):
    try:
        company_internal_id = request.GET.get("company_internal_id")
        pode_rodar = auto_integra_operacao_svc.verificar_elegibilidade_auto_integra_operacao(company_internal_id)
        return JsonResponse(pode_rodar, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"error": ex.message}, status=400)


@api.get("/v1/auto_integra_operacao/get_min_rotas_para_integrar")
def get_min_rotas_integrar_por_empresa(request, company_internal_id, modelo_venda=Company.ModeloVenda.MARKETPLACE):
    try:
        qtd_rotas = auto_integra_operacao_svc.get_min_rotas_integracao(company_internal_id, modelo_venda)
        return JsonResponse(qtd_rotas, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"error": ex.message}, status=400)


@api.post("/v1/auto_integra_operacao/set_min_rotas_para_integrar")
def set_min_rotas_integrar_por_empresa(request, company_internal_id, qtd_rotas):
    try:
        qtd_rotas = auto_integra_operacao_svc.set_min_rotas_integracao(company_internal_id, qtd_rotas)
        return JsonResponse(qtd_rotas, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"error": ex.message}, status=400)


# FIM Integração automática da operação ###


@api.get("/v1/get-atualizacao-passagem-api-parceiro")
def get_atualizacao_passagem_api_parceiro(request, buseiro_id: int, modelo_venda: str, travel_id: int):
    try:
        passagem_list = get_passagens_svc.get_infos_atualizacao_passagem(buseiro_id, modelo_venda, travel_id)
        return JsonResponse(passagem_list, safe=False)
    except PassengerNotRegistered:
        return JsonResponse([], safe=False)


@api.get("/v1/verifica_elegibilidade_revert_hard_stop")
def verifica_elegibilidade_revert_hard_stop(request, company_internal_id):
    empresa = company_svc.get_company_by_id(company_internal_id, "marketplace")
    if empresa is None:
        return JsonResponse({"message": "Not found."}, status=404)
    try:
        company_svc.raise_if_unable_hard_stop(empresa)
    except (RodoviariaUnableRevertHardStop, RodoviariaCompanyNotIntegratedError) as exc:
        return JsonResponse({"error": str(exc)}, status=422)
    return JsonResponse({"sucesso": True}, status=200)


@api.get("/v1/map_rotinas_integradas")
def map_rotinas_integradas(request):
    resp = novos_modelos_svc.get_map_rotinas_integradas()
    return JsonResponse(resp)


@api.get("/v1/tipo_assento/list")
def listar_tipos_assentos(request):
    params = RodoviariaListarTiposAssentosParams.parse_raw(request.GET.get("params"))
    items, count, num_pages = class_match_svc.listar_tipos_assentos(params)
    return JsonResponse({"items": items, "count": count, "num_pages": num_pages})


@api.post("/v1/tipo_assento/link")
def linkar_tipo_assento(request, data: RodoviariaLinkarTiposAssentosParams):
    class_match_svc.linkar_tipo_assento(
        data.id_assento, data.tipo_assento_buser_preferencial, data.tipos_assentos_buser_match
    )
    return JsonResponse({})


@api.get("/v1/desenho-mapa-poltronas")
def get_desenho_mapa_poltronas(request, trecho_classe_id: int):
    form = DefaultForm(trechoclasse_id=trecho_classe_id)

    try:
        resp = CompraRodoviariaSVC(form).get_desenho_mapa_poltronas(form)
        return JsonResponse(resp, safe=False)
    except RodoviariaTrechoclasseFactoryException as exception:
        dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)


@api.get("/v1/has-marcacao-assento")
def has_marcacao_assento(request, trecho_classe_id: int):
    form = DefaultForm(trechoclasse_id=trecho_classe_id)

    try:
        resp = CompraRodoviariaSVC(form).has_marcacao_assento(form)
        return JsonResponse(resp, safe=False)
    except RodoviariaTrechoclasseFactoryException as exception:
        dict_error = {"error": error_str(exception), "error_type": "service_not_found"}
        log_svc.log_reserva_exception("TrechoClasseFactoryException", extra=dict_error)
        return JsonResponse(dict_error, status=404)
    except RodoviariaInvalidParamsException as exception:
        dict_error = {"error": error_str(exception), "error_type": "invalid_params"}
        log_svc.log_reserva_exception("InvalidParamsException", extra=dict_error)
        return JsonResponse(dict_error, status=400)


@api.get("/v1/get-cronograma-atualizazao-operacao")
def get_cronograma_atualizacao_operacao(request, params: CronogramaAtualizacaoPaginatorParams = Query(...)):
    cronograma = cronograma_atualizacao_svc.get_cronograma_atualizacao(params)
    return JsonResponse(cronograma, safe=False)


@api.post("/v1/update-cronograma-atualizazao-operacao")
def update_cronograma_atualizacao_operacao(request, data: UpdateCronogramaAtualizacaoParams):
    cronograma_atualizacao_svc.update_cronograma_atualizacao(data)
    return JsonResponse({})


@api.get("/v1/buscar-viagens-api-search")
def buscar_viagens_api_na_search(request, params: BuscarViagensAPIParams = Query(...)):
    funcao_busca = atualiza_precos_search_svc.buscar_viagens_api_na_search.s(
        params.companies_ids, params.origem_id, params.destino_id, params.data_busca.strftime("%Y-%m-%d")
    )
    if params.execute_async:
        funcao_busca.apply_async()
        return JsonResponse({"message": "busca iniciada"})
    resp = funcao_busca.apply()
    return JsonResponse(resp.result, safe=False)


@api.get("/v1/buscar-viagens-todas-empresas-api-search")
def buscar_viagens_todas_empresas_api_na_search(request, params: BuscarViagensTodasEmpresasAPIParams = Query(...)):
    funcao_busca = atualiza_precos_search_svc.buscar_viagens_todas_empresas_api_na_search.s(
        params.origem_id, params.destino_id, params.data_busca.strftime("%Y-%m-%d")
    )
    if params.execute_async:
        funcao_busca.apply_async()
        return JsonResponse({"message": "busca iniciada"})
    resp = funcao_busca.apply()
    return JsonResponse(resp.result, safe=False)

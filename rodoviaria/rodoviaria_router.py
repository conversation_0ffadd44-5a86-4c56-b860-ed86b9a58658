class RodoviariaRouter:
    """
    A router to control all database operations on models in the
    rodoviaria application..
    """

    route_app_label = ["rodoviaria", "marketplace"]

    def db_for_read(self, model, **hints):
        """
        Attempts to read rodoviaria models go to rodoviaria.
        """
        if model._meta.app_label in self.route_app_label:
            return "rodoviaria"
        return None

    def db_for_write(self, model, **hints):
        """
        Attempts to write rodoviaria models go to rodoviaria.
        """
        if model._meta.app_label in self.route_app_label:
            return "rodoviaria"
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure the rodoviaria app only appear in the 'rodoviaria' database.
        """
        if app_label in self.route_app_label:
            return db == "rodoviaria"
        return None

from datetime import date, datetime, time
from decimal import Decimal
from typing import Dict, Optional

from dateutil.parser import parse
from ninja import Field, Schema
from pydantic import BaseModel
from pydantic.class_validators import root_validator, validator

from commons.dateutils import to_tz
from rodoviaria.forms.motorista_forms import Motorista
from rodoviaria.forms.staff_forms import (
    CheckPaxForm,
    CheckPaxMultipleForm,
    GuichepassAnonymousLogin,
    RemanejaPassageiroForm,
    TotalbusNoCompanyLogin,
    TravelGetPoltronas,
    VexadoAnonymousLogin,
)
from rodoviaria.models.core import Company, Passagem


class StatusIntegracaoParams(Schema):
    grupo_id: int = None
    trecho_classe_id: int = None


class StatusIntegracaoCheckoutParams(Schema):
    company_id: int
    trecho_classe_id: int
    preco_atual: Optional[Decimal]


class AtualizacaoCheckoutAsyncParams(Schema):
    trechos: list[StatusIntegracaoCheckoutParams]


class StatusIntegracaoBatchParams(Schema):
    trecho_classe_ids: list[int]


class IdsEmpresasParams(Schema):
    company_internal_ids: list[int]


class DesbloquearPoltronasParams(Schema):
    trecho_classe_id: int
    poltronas: list[int]


class BloquearPoltronasParams(DesbloquearPoltronasParams): ...


class BloquearPoltronasParamsV2(Schema):
    trecho_classe_id: int
    poltrona: int
    categoria_especial: Optional[Passagem.CategoriaEspecial]


class UpdateGrupoClasseLink(Schema):
    new_id: int
    old_id: int


class UpdateGruposClasseLink(Schema):
    updates: list[UpdateGrupoClasseLink]

    @property
    def old_grupos_classe_ids(self):
        return [u.old_id for u in self.updates]

    @property
    def map(self):
        return {u.old_id: u.new_id for u in self.updates}


class DadosBpePassagemBatchParams(Schema):
    travel_ids: list[int]


class ListaPassageirosViagem(Schema):
    travels_ids: list[int]


class MapCidadeParams(Schema):
    company_rodoviaria_id: int


class TrechoClasseGetAtualizaParams(Schema):
    tag: Optional[str] = None
    integracao: Optional[str] = None
    company_id: Optional[int] = None
    modelo_venda: Optional[str] = None
    grupo_id: Optional[int] = None
    trechoclasse_ids: Optional[str] = None

    @validator("trechoclasse_ids")
    def validate_trechoclasse_ids(cls, v):
        return v.split(",")


class TrechoClasseAddRemoveTagParams(Schema):
    tag: str
    integracao: Optional[str] = None
    company_id: Optional[int] = None
    modelo_venda: Optional[str] = None
    grupo_id: Optional[int] = None
    trechoclasse_ids: Optional[str] = None
    days: Optional[int] = None

    @validator("trechoclasse_ids")
    def validate_trechoclasse_ids(cls, v):
        return v.split(",")


class TrechoParams(Schema):
    cidade_origem_id: int
    local_origem_id: int
    cidade_destino_id: int
    local_destino_id: int
    classe: str
    max_split_value: Decimal


class CadastrarTrechosParams(Schema):
    company_id: int
    trechos: list[TrechoParams]


class CompanyTransbrasilParams(Schema):
    company_internal_id: int
    name: str
    company_external_id: int


class DeleteTrechoClasseErrorParams(Schema):
    trechos_classe_ids: list[int]


class SolicitaCancelamentoTravelsParams(Schema):
    travel_ids: list[int]


class SolicitaCancelamentoPorPassagemIdParams(Schema):
    passagens_ids: list[int]


class EscalaMotoristaParams(Schema):
    grupo_id: int
    motorista: Motorista


class OnibusClasseParams(Schema):
    tipo: str
    capacidade: int


class CadastrarVeiculoParams(Schema):
    company_id: Optional[int] = None
    veiculo_id: int
    placa: str
    classes: list[OnibusClasseParams]


class GrupoClasseParams(OnibusClasseParams):
    grupo_classe_id: int


class PosSalvarRotaLinkRotaParams(Schema):
    rodoviaria_rota_id: int
    id_internal: int


class PosSalvarRotaParams(Schema):
    trechos_vendidos: dict
    rota: PosSalvarRotaLinkRotaParams


class GrupoParams(Schema):
    data_partida: date
    hora_saida: str
    veiculo_internal_id: int
    rota_internal_id: Optional[int]
    veiculo_placa: str
    grupo_id: int
    classes: list[GrupoClasseParams]

    @property
    def data_partida_str(self):
        return self.data_partida.strftime("%Y-%m-%dT03:00:00.000Z")

    @property
    def veiculo_params(self):
        return CadastrarVeiculoParams(
            veiculo_id=self.veiculo_internal_id,
            placa=self.veiculo_placa,
            classes=self.classes,
        )


class CadastrarGruposParams(Schema):
    company_id: int
    rota_external_id: Optional[int]
    rota_internal_id: Optional[int]
    grupos: list[GrupoParams]


class CompanyPaginatorParams(Schema):
    id: Optional[int]
    features: Optional[str]
    name: Optional[str]
    integracoes: Optional[list[str]]
    status: Optional[str]
    modelo_venda: Optional[str]
    rows_per_page: Optional[int]
    page: Optional[int]
    order_by: Optional[str]


class CancelarGruposClasseParams(Schema):
    company_id: int
    grupos_classe_ids: list[int]


class AlterarGrupoParams(Schema):
    company_id: int
    grupos_classe_ids_antigos: list[int]
    grupo: GrupoParams
    passageiros: Optional[list[CheckPaxMultipleForm]] = []


class CadastrarCheckpointParams(Schema):
    cidade_destino_id: int
    duracao: Optional[str] = 0
    tempo_embarque: Optional[str] = 0
    tempo_total: Optional[str] = 0
    ponto_embarque: str
    km: Optional[int] = 0
    id_rota_external: Optional[int] = None
    local_embarque_id: int


class CadastrarItinerarioParams(Schema):
    company_id: int
    id_rota_internal: int
    id_rota_external: int
    checkpoints: list[CadastrarCheckpointParams]


class RotaHibridoParams(Schema):
    company_id: int
    cidade_destino_id: int
    cidade_origem_id: int
    prefixo: str
    id_rota_internal: int
    id_rota_external: Optional[int]


class EditarRotaHibridoParams(RotaHibridoParams):
    grupo_ids: list[int]


class CadastrarRotaHibridoParams(RotaHibridoParams):
    checkpoints: list[CadastrarCheckpointParams]


class CadastrarOuEditarRotaHibridoParams(RotaHibridoParams):
    checkpoints: list[CadastrarCheckpointParams]
    grupo_ids: list[int]


class AtualizarLocaisParams(Schema):
    company_id: int
    id_rota_internal: int
    id_rota_external: int
    checkpoints: list[CadastrarCheckpointParams]


class AtualizarLocaisBatchParams(Schema):
    company_id: Optional[int]
    id_rota_internal: int
    id_rota_external: Optional[int]
    old_rota_id: int
    checkpoints: list[CadastrarCheckpointParams]


class RotaInternalLink(Schema):
    company_id: int
    rota_id: int


class VerificaLinkRotasParams(Schema):
    rotas: list[RotaInternalLink]


class LinkTrechoParams(Schema):
    company_internal_id: int
    modelo_venda: Optional[str] = Company.ModeloVenda.MARKETPLACE
    trechoclasse_internal_id: int
    origem_internal_id: int
    destino_internal_id: int
    origem_timezone: str
    datetime_ida: datetime
    grupo_internal_id: int
    grupo_datetime_ida: datetime
    grupoclasse_internal_id: int
    tipo_assento: str
    rodoviaria_origem_id: Optional[int] = None
    origem_id_external: Optional[str] = None
    rodoviaria_destino_id: Optional[int] = None
    destino_id_external: Optional[str] = None
    use_low_rate_limit: Optional[bool] = None

    @validator("datetime_ida", pre=True, always=True)
    def parse_datetime_ida(cls, value, values):
        origem_timezone = values.get("origem_timezone")
        if isinstance(value, str):
            try:
                datetime_ida = parse(value)
                return to_tz(datetime_ida, origem_timezone)
            except ValueError as err:
                raise ValueError(f"Invalid datetime format for datetime_ida: {value}") from err
        elif isinstance(value, datetime):
            return value
        raise TypeError(f"datetime_ida must be a string or datetime, got {type(value)}")

    @validator("grupo_datetime_ida", pre=True, always=True)
    def parse_grupo_datetime_ida(cls, value, values):
        origem_timezone = values.get("origem_timezone")
        if isinstance(value, str):
            try:
                grupo_datetime_ida = parse(value)
                return to_tz(grupo_datetime_ida, origem_timezone)
            except ValueError as err:
                raise ValueError(f"Invalid datetime format for grupo_datetime_ida: {value}") from err
        elif isinstance(value, datetime):
            return value
        raise TypeError(f"grupo_datetime_ida must be a string or datetime, got {type(value)}")


class LinkTrechosClassesAsyncParams(Schema):
    trechos: list[LinkTrechoParams]


class RemoverTagsTrechosAtualizadosParams(Schema):
    to_be_closed: list[int]
    to_be_updated_in_django: list[int]


class PassagensConfirmadasPorEmpresaParams(Schema):
    companies_ids: list[int]
    data_inicial: datetime
    data_final: datetime

    @validator("data_inicial", "data_final", pre=True)
    def from_iso_format(cls, v):
        return datetime.fromisoformat(v)


class PassagensPorEmpresaParams(Schema):
    company_id: int
    start_date_saida: Optional[datetime] = None
    end_date_saida: Optional[datetime] = None
    start_date_compra_e_cancelamento: Optional[datetime] = None
    end_date_compra_e_cancelamento: Optional[datetime] = None
    current_page: Optional[int] = None
    items_per_page: Optional[int] = None
    travel_ids: Optional[list[int]] = None
    status_passagens: Optional[str] = None

    @validator(
        "start_date_saida",
        "end_date_saida",
        "start_date_compra_e_cancelamento",
        "end_date_compra_e_cancelamento",
        pre=True,
    )
    def from_iso_format(cls, v):
        if v is None:
            return v
        return datetime.fromisoformat(v)


class AddMultiplePaxAsyncParams(Schema):
    passagens: list[CheckPaxForm]
    modelo_venda: str

    def __getitem__(self, index):
        return self.passagens[index]

    def __iter__(self):
        return iter(self.passagens)

    def __len__(self):
        return len(self.passagens)

    @root_validator(pre=True)
    def handle_list_params(cls, values):
        if isinstance(values, dict):
            return values
        if isinstance(values._obj, list):
            return {
                "passagens": values._obj,
                "modelo_venda": Company.ModeloVenda.MARKETPLACE,
            }
        return values


class ListaEmpresasAPIParams(Schema):
    login_params: TotalbusNoCompanyLogin | VexadoAnonymousLogin | GuichepassAnonymousLogin


class GetDetalhesRotinasParams(Schema):
    rodoviaria_rota_id: int
    ids_trechos_vendidos_filter: str
    start_date: datetime
    end_date: datetime

    @validator("ids_trechos_vendidos_filter")
    def validate_ids_trechos_vendidos_filter(cls, v):
        # TODO: Remover tudo isso daqui e fazer ids_trechos_vendidos_filter ser tipado como list[int]
        # Feito assim pra não quebrar contrato. Proximo MR já vai pro pau
        splitted = list(map(int, v.split(",")))
        return splitted


class BulkGetPoltronasParams(Schema):
    trecho_classe_id: int
    travels: list[TravelGetPoltronas]


class RemanejaPassageirosAsyncParams(Schema):
    remanejamentos: list[RemanejaPassageiroForm]

    def __getitem__(self, index):
        return self.remanejamentos[index]

    def __iter__(self):
        return iter(self.remanejamentos)

    def __len__(self):
        return len(self.remanejamentos)


class GetDetalhesRotinasPorRotaParams(Schema):
    rodoviaria_rota_ids: list[int]
    trechos_vendidos_ids_por_rota_id: Dict[int, list[int]]
    datetimes_a_ignorar_por_rota_id: Optional[Dict[int, list[datetime]]]
    extend_dates: bool = True


class BulkGrupoTemPassagemEmitidaParams(Schema):
    grupos_passenger_map: Dict[int, list[Dict[str, int]]]


class CronogramaAtualizacaoPaginatorParams(Schema):
    search: Optional[str]
    integracao_id_filter: Optional[int]
    rows_per_page: Optional[int] = Field(alias="rowsPerPage")
    page: Optional[int]
    order_by: Optional[str] = Field(alias="sortBy")


class BuscarViagensAPIParams(Schema):
    companies_ids: list[int]
    origem_id: int
    destino_id: int
    data_busca: date
    execute_async: Optional[bool] = True

    @validator("data_busca")
    def validate_data_format(cls, v):
        if v < datetime.today().date():
            raise ValueError("data menor que o dia atual")
        return v


class BuscarViagensTodasEmpresasAPIParams(Schema):
    origem_id: int
    destino_id: int
    data_busca: date
    execute_async: Optional[bool] = True

    @validator("data_busca")
    def validate_data_format(cls, v):
        if v < datetime.today().date():
            raise ValueError("data menor que o dia atual")
        return v


class HorariosAtualizacaoParams(BaseModel):
    dias_semana: list[int]
    horario: time | None


class CompanyParams(BaseModel):
    id: int


class CronogramaEmpresaParams(Schema):
    empresa: CompanyParams
    fetch_trechos_vendidos: Optional[HorariosAtualizacaoParams]
    fetch_data_limite: Optional[HorariosAtualizacaoParams]
    descobrir_rotas: Optional[HorariosAtualizacaoParams]
    descobrir_operacao: Optional[HorariosAtualizacaoParams]
    integracao_automatica: Optional[HorariosAtualizacaoParams]


class UpdateCronogramaAtualizacaoParams(Schema):
    horarios: list[CronogramaEmpresaParams]

MAP_CLASSES_VEXADO = {
    "semi leito": "SEMI_LEITO",
    "leito individual": "LEITO_INDIVIDUAL",
    "leito": "LEITO",
    "cama premium": "LEITO_CAMA_ESPECIAL_1",
    "cama premium individual": "LEITO_CAMA_ESPECIAL_2",
    "leito cama": "LEITO_ESPECIAL",
    "leito cama individual": "LEITO_CAMA_INDIVIDUAL",
    "executivo": "EXECUTIVO",
}

MAP_CLASSES_BUSER = {v: k for k, v in MAP_CLASSES_VEXADO.items()}

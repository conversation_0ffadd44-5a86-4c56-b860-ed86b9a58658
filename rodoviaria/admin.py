from django.contrib import admin

from rodoviaria.models import (
    Checkpoint,
    Cidade,
    Company,
    ConexaoRota,
    ConexaoTrechoClasse,
    CronogramaAtualizacaoOperacao,
    EulabsLogin,
    Grupo,
    GuichepassLogin,
    Integracao,
    LocalEmbarque,
    Passagem,
    PraxioLogin,
    Remanejamento,
    Rota,
    Rotina,
    RotinaAtualizacaoTrecho,
    RotinaTrechoVendido,
    SmartbusLogin,
    TaskStatus,
    TipoAssento,
    TiSistemasLogin,
    TotalbusLogin,
    TrechoClasse,
    TrechoClasseError,
    TrechoVendido,
    Veiculo,
    VexadoGrupoClasse,
    VexadoLogin,
    VexadoRota,
)
from rodoviaria.models.core import CompanyCategoriaEspecial


@admin.register(TotalbusLogin)
class TotalbusLogin(admin.ModelAdmin):
    search_fields = ("id", "company__id", "user")
    fields = (
        "user",
        "password",
        "company",
        "tenant_id",
        "id_forma_pagamento",
        "forma_pagamento",
        "validar_multa",
    )
    list_display = (
        "company",
        "user",
        "id_forma_pagamento",
        "forma_pagamento",
        "validar_multa",
    )


@admin.register(GuichepassLogin)
class GuichepassLogin(admin.ModelAdmin):
    fields = ("username", "password", "company", "client_id")
    list_display = ("company", "username", "client_id")


@admin.register(VexadoLogin)
class VexadoLogin(admin.ModelAdmin):
    search_fields = ("id", "company__id", "user")
    fields = ("user", "password", "company", "site")
    list_display = ("company", "user", "site")


@admin.register(EulabsLogin)
class EulabsLogin(admin.ModelAdmin):
    search_fields = ("company__name",)
    fields = ("api_id", "api_key", "company")
    list_display = ("company", "api_id", "api_key")


@admin.register(SmartbusLogin)
class SmartbusLogin(admin.ModelAdmin):
    search_fields = (
        "company__name",
        "cliente",
    )
    fields = ("company", "username", "password", "cliente")
    list_display = (
        "company",
        "cliente",
        "url_base",
        "username",
    )


@admin.register(TiSistemasLogin)
class TiSistemasLogin(admin.ModelAdmin):
    search_fields = ("company__name",)
    fields = ("company", "auth_key")
    list_display = ("company",)


@admin.register(VexadoGrupoClasse)
class VexadoGrupoClasse(admin.ModelAdmin):
    search_fields = (
        "id",
        "grupo_classe__grupoclasse_internal_id",
        "grupo_classe_external_id",
    )
    fields = (
        "veiculo",
        "andar",
        "rota_external_id",
        "status",
        "grupo_classe_external_id",
    )
    list_display = [field.name for field in VexadoGrupoClasse._meta.fields]


@admin.register(VexadoRota)
class VexadoRota(admin.ModelAdmin):
    search_fields = ("id", "rota_internal_id", "rota_external_id")
    fields = ("rota_internal_id", "rota_external_id", "company")
    list_display = [field.name for field in VexadoRota._meta.fields]


@admin.register(Integracao)
class IntegracaoAdmin(admin.ModelAdmin):
    search_fields = ("name", "versao", "use_low_rate_limit")
    list_display = [field.name for field in Integracao._meta.fields]
    fields = ("name", "versao", "use_low_rate_limit")


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    search_fields = (
        "company_internal_id",
        "integracao__id",
        "integracao__name",
        "url_base",
        "company_external_id",
        "name",
    )
    list_display = [field.name for field in Company._meta.fields]
    fields = (
        "company_internal_id",
        "integracao",
        "url_base",
        "company_external_id",
        "name",
        "modelo_venda",
        "features",
        "max_percentual_divergencia",
        "auto_integra_rotas_min",
        "margem_dias_busca_operacao",
    )


@admin.register(Grupo)
class GrupoAdmin(admin.ModelAdmin):
    search_fields = (
        "id",
        "grupo_internal_id",
        "company_integracao__id",
        "company_integracao__company_internal_id",
    )
    list_display = [field.name for field in Grupo._meta.fields]
    fields = (
        "grupo_internal_id",
        "rota",
        "company_integracao",
        "datetime_ida",
        "external_datetime_ida",
        "linha",
    )


@admin.register(Passagem)
class PassagemAdmin(admin.ModelAdmin):
    search_fields = (
        "localizador",
        "buseiro_internal_id",
        "status",
        "travel_internal_id",
        "trechoclasse_integracao__grupo__company_integracao__name",
        "trechoclasse_integracao__grupo__company_integracao__integracao__name",
        "trechoclasse_integracao__grupo__company_integracao__modelo_venda",
    )
    list_display = [field.name for field in Passagem._meta.fields]
    raw_id_fields = ("trechoclasse_integracao",)
    fields = (
        "trechoclasse_integracao",
        "linha",
        "prefixo",
        "origem",
        "destino",
        "data_hora_partida",
        "company_integracao",
        "buseiro_internal_id",
        "poltrona_external_id",
        "travel_internal_id",
        "localizador",
        "numero_passagem",
        "pedido_external_id",
        "id_estabelecimento_external",
        "tags",
        "valor_cheio",
        "bpe_qrcode",
        "bpe_monitriip_code",
        "plataforma",
        "status",
        "preco_base",
        "taxa_embarque",
        "tipo_taxa_embarque",
        "codigo_taxa_embarque",
        "numero_bilhete_embarque",
        "seguro",
        "pedagio",
        "outras_taxas",
        "outros_tributos",
        "chave_bpe",
        "numero_bpe",
        "serie_bpe",
        "protocolo_autorizacao",
        "numero_bilhete",
        "data_autorizacao",
        "preco_rodoviaria",
        "multa",
        "desconto",
        "categoria_especial",
    )


@admin.register(TrechoClasse)
class TrechoClasseAdmin(admin.ModelAdmin):
    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("origem__cidade", "destino__cidade", "grupo_classe", "grupo__company_integracao")
        )

    search_fields = (
        "id",
        "trechoclasse_internal_id",
        "grupo__grupo_internal_id",
        "external_id",
        "external_id_tipo_veiculo",
        "grupo__company_integracao__name",
    )
    list_display = [field.name for field in TrechoClasse._meta.fields]
    raw_id_fields = ("grupo", "origem", "destino")
    fields = (
        "trechoclasse_internal_id",
        "provider_data",
        "grupo",
        "external_id",
        "active",
        "origem",
        "destino",
        "datetime_ida",
        "preco_rodoviaria",
        "external_id_tipo_veiculo",
        "tags",
    )


@admin.register(PraxioLogin)
class PraxioLoginAdmin(admin.ModelAdmin):
    search_fields = ("id", "company__id", "sistema", "name", "empresa", "cliente")
    list_display = (
        "id",
        "company",
        "desconto_manual",
        "sistema",
        "name",
        "tipo_bd",
        "empresa",
        "cliente",
        "tipo_aplicacao",
    )
    fields = (
        "company",
        "sistema",
        "name",
        "tipo_bd",
        "empresa",
        "cliente",
        "tipo_aplicacao",
        "password",
        "desconto_manual",
    )


@admin.register(Cidade)
class CidadeAdmin(admin.ModelAdmin):
    search_fields = (
        "id",
        "name",
        "company__id",
        "company__name",
        "cidade_internal__id",
    )
    list_display = [field.name for field in Cidade._meta.fields]
    raw_id_fields = ("cidade_internal",)
    fields = ("id_external", "name", "company", "cidade_internal", "timezone")


@admin.register(LocalEmbarque)
class LocalEmbarque(admin.ModelAdmin):
    search_fields = (
        "id",
        "nickname",
        "cidade__id",
        "cidade__company__name",
        "local_embarque_internal_id",
    )
    list_display = [field.name for field in LocalEmbarque._meta.fields]
    raw_id_fields = ("cidade",)
    fields = ("id_external", "nickname", "cidade", "local_embarque_internal_id")


@admin.register(TrechoVendido)
class TrechoVendidoAdmin(admin.ModelAdmin):
    search_fields = ("id_internal", "rota__company__name", "rota__id_internal")
    list_display = [field.name for field in TrechoVendido._meta.fields]
    raw_id_fields = ("rota", "origem", "destino")
    fields = (
        "id_internal",
        "rota",
        "origem",
        "destino",
        "classe",
        "capacidade_classe",
        "distancia",
        "duracao",
        "preco",
        "tipo_assento",
    )


@admin.register(RotinaTrechoVendido)
class RotinaTrechoVendidoAdmin(admin.ModelAdmin):
    search_fields = (
        "id",
        "rotina__id",
        "trecho_vendido__id",
        "datetime_ida_trecho_vendido",
    )
    list_display = [field.name for field in RotinaTrechoVendido._meta.fields]
    fields = ("rotina", "trecho_vendido", "datetime_ida_trecho_vendido")


@admin.register(TipoAssento)
class TipoAssentoAdmin(admin.ModelAdmin):
    search_fields = ("company__name", "tipo_assento_parceiro", "tipo_assento_buser_preferencial")
    list_display = [field.name for field in TipoAssento._meta.fields]
    fields = ("company", "tipo_assento_parceiro", "tipo_assento_buser_preferencial")


@admin.register(TaskStatus)
class TaskStatusAdmin(admin.ModelAdmin):
    search_fields = ("task_name", "status", "rota__id", "company__name")
    list_display = [field.name for field in TaskStatus._meta.fields]
    fields = ("company", "rota", "task_name", "task_id", "status", "last_success_at", "last_finished_at")


@admin.register(TrechoClasseError)
class TrechoClasseErrorAdmin(admin.ModelAdmin):
    search_fields = (
        "trechoclasse_internal_id",
        "tipo_assento",
        "origem__nickname",
        "destino__nickname",
        "company__name",
    )
    list_display = [field.name for field in TrechoClasseError._meta.fields]
    raw_id_fields = ("origem", "destino")
    fields = (
        "trechoclasse_internal_id",
        "tipo_assento",
        "datetime_ida",
        "origem",
        "destino",
        "servicos_proximos",
        "company",
    )


@admin.register(Rota)
class RotaAdmin(admin.ModelAdmin):
    search_fields = (
        "company__id",
        "company__name",
        "company__integracao__name",
        "id_hash",
    )
    list_display = [field.name for field in Rota._meta.fields]
    fields = (
        "company",
        "id_hash",
        "id_internal",
        "id_external",
        "provider_data",
        "ativo",
    )


@admin.register(Rotina)
class RotinaAdmin(admin.ModelAdmin):
    search_fields = (
        "rota__id_internal",
        "datetime_ida",
    )
    list_display = [field.name for field in Rotina._meta.fields]
    fields = ("rota", "datetime_ida", "ativo")


@admin.register(Veiculo)
class VeiculoAdmin(admin.ModelAdmin):
    search_fields = (
        "company__id",
        "company__name",
        "id_internal",
        "id_external",
        "descricao",
    )
    list_display = [field.name for field in Veiculo._meta.fields]
    fields = ("company", "id_internal", "mapa_veiculo", "id_external", "descricao")


@admin.register(Checkpoint)
class CheckpointAdmin(admin.ModelAdmin):
    search_fields = (
        "local__id_external",
        "id_external",
        "nickname",
        "rota__company__name",
        "rota__id_internal",
    )
    list_display = [field.name for field in Checkpoint._meta.fields]
    fields = (
        "rota",
        "idx",
        "local",
        "id_external",
        "departure",
        "arrival",
        "distancia_km",
        "duracao",
        "tempo_embarque",
        "nickname",
        "name",
        "uf",
    )


@admin.register(ConexaoRota)
class ConexaoRotaAdmin(admin.ModelAdmin):
    fields = search_fields = (
        "internal_grupo_id",
        "internal_rota_id",
        "external_rota_id_inicial",
        "external_rota_id_final",
        "local_corte",
    )
    list_display = [field.name for field in ConexaoRota._meta.fields]


@admin.register(ConexaoTrechoClasse)
class ConexaoTrechoClasseAdmin(admin.ModelAdmin):
    fields = search_fields = (
        "conexao_rota",
        "trechoclasse_internal_id",
        "trechoclasse_internal_inicial_id",
        "trechoclasse_internal_final_id",
    )
    list_display = [field.name for field in ConexaoTrechoClasse._meta.fields]


@admin.register(Remanejamento)
class RemanejamentoAdmin(admin.ModelAdmin):
    fields = ("travel_origem_internal_id", "travel_destino_internal_id", "status", "etapa_erro")
    search_fields = ("id", "travel_origem_internal_id", "travel_destino_internal_id")
    list_display = [field.name for field in Remanejamento._meta.fields]


@admin.register(CronogramaAtualizacaoOperacao)
class CronogramaAtualizacaoOperacaoAdmin(admin.ModelAdmin):
    fields = ("tipo_atualizacao", "dia_semana", "horario", "company")
    search_fields = ("company__name",)
    list_display = [field.name for field in CronogramaAtualizacaoOperacao._meta.fields]


@admin.register(CompanyCategoriaEspecial)
class CompanyCategoriaEspecialAdmin(admin.ModelAdmin):
    fields = (("company", "categoria_id_external", "descricao_external", "categoria_especial"),)
    search_fields = ("company__name", "categoria_id_external", "descricao_external", "categoria_especial")
    list_display = [field.name for field in CompanyCategoriaEspecial._meta.fields]


@admin.register(RotinaAtualizacaoTrecho)
class RotinaAtualizacaoTrechoAdmin(admin.ModelAdmin):
    fields = (
        (
            "tipo_atualizacao",
            "cidade_origem",
            "cidade_destino",
            "intervalo_execucao_minutos",
            "dias_busca_intervalo_dinamico",
            "margem_inicio_intervalo_dinamico",
            "data_inicio_intervalo_fixo",
            "data_fim_intervalo_fixo",
            "ativo",
        ),
    )
    search_fields = ("origem__name", "destino__name")
    list_display = [field.name for field in RotinaAtualizacaoTrecho._meta.fields]

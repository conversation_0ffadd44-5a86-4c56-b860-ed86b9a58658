import json

from celery import shared_task
from celery.schedules import crontab as _unsafe_celery_crontab
from celery.utils.log import get_task_logger
from django.conf import settings
from django.core.management import call_command

from bp.celery import app
from rodoviaria.models.core import CronogramaAtualizacaoOperacao
from rodoviaria.service import cronograma_atualizacao_svc, update_grupos_hibridos_svc
from rodoviaria.service.cancela_passagens_pendentes_svc import cancelar_passagens_sem_travel_correspondente

cron_logger = get_task_logger(__name__)

HORARIO_COMERCIAL = "07-23"
UM_EM_UM_MINUTO = "*/1"
CINCO_EM_CINCO_MINUTOS = "*/5"
DEZ_EM_DEZ_MINUTOS = "*/10"
QUINZE_EM_QUINZE_MINUTOS = "*/15"
TRINTA_EM_TRINTA_MINUTOS = "*/30"


def crontab(*, minute, hour, **kwargs):
    """
    O crontab do celery trata tudo como opcional, mas se você não definir
    `minute`, ele roda a cada minuto. Se não definir hour, ele roda a
    cada hora.

    Isso força a definição do `minute` e do `hour`. É mais burocrático, mas
    também é mais seguro.

    Para rodar todo minuto (não recomendo), use `minute='*', hour='*'.

    Para rodar a cada hora, `minute='42', hour='*'.
    """
    return _unsafe_celery_crontab(minute=minute, hour=hour, **kwargs)


@shared_task(queue="bp_cron")
def marketplace_fetch_rotas(company_id=None):
    kwargs = {"company_id": company_id}
    call_command("marketplace_fetch_rotas", **kwargs)


@shared_task(queue="bp_cron")
def verificar_grupos_hibridos_cancelados():
    call_command("verificar_grupos_hibridos_cancelados")


@shared_task(queue="bp_cron")
def cancela_passagens_pendentes():
    call_command("cancela_passagens_pendentes")


@shared_task(queue="bp_cron")
def cancela_passagens_com_erro_confirmadas_na_api():
    call_command("cancela_passagens_com_erro_confirmadas_na_api")


@shared_task(queue="bp_cron")
def fetch_trechos_vendidos():
    call_command("fetch_trechos_vendidos")


@shared_task(queue="bp_cron")
def task_cancelar_passagens_sem_travel_correspondente():
    passagens_canceladas = cancelar_passagens_sem_travel_correspondente()
    cron_logger.info(
        "CRON EXECUTADO - Cancelar Passagens sem travel correspondente - %s", json.dumps(passagens_canceladas)
    )


@shared_task(queue="bp_cron")
def task_update_grupos_hibridos_criados():
    update_grupos_hibridos_svc.update_grupos_hibridos_criados_todas_empresas()


@shared_task(queue="bp_cron")
def atualiza_trechos(tag="to_be_updated"):
    kwargs = {"tag": tag}
    call_command("atualiza_trechos", **kwargs)


@shared_task(queue="bp_cron")
def descobrir_rotas(company_id, modelo_venda):
    kwargs = {"company_internal_id": company_id, "modelo_venda": modelo_venda}
    call_command("descobrir_rotas", **kwargs)


@shared_task(queue="bp_cron")
def descobrir_operacao(company_id, modelo_venda):
    kwargs = {"company_internal_id": company_id, "modelo_venda": modelo_venda}
    call_command("descobrir_operacao", **kwargs)


@shared_task(queue="bp_cron")
def atualiza_trechos_vendidos(company_id, modelo_venda):
    kwargs = {"company_id": company_id, "modelo_venda": modelo_venda}
    call_command("fetch_trechos_vendidos", **kwargs)


@shared_task(queue="bp_cron")
def atualiza_data_limite_rotas(company_id, modelo_venda):
    # aqui não está implementado o uso do modelo_venda pois não tem necessidade
    kwargs = {"company_id": company_id}
    call_command("fetch_data_limite_rotas", **kwargs)


@shared_task(queue="bp_cron")
def finaliza_task_status():
    call_command("finaliza_task_status")


@shared_task(queue="bp_cron")
def dispara_rotina_atualizacao_trecho():
    call_command("dispara_rotina_atualizacao_trecho")


@shared_task(queue="bp_cron")
def busca_preco_trechos_vendidos():
    call_command("busca_precos_trechos_vendidos")


@shared_task(queue="bp_cron")
def marketplace_busca_operacao():
    call_command("busca_operacao_publisher")


@shared_task(queue="bp_cron")
def busca_dados_bpe_passagens():
    call_command("busca_dados_bpe_passagens")


def setup():
    if settings.ENVIRONMENT == "prod":
        app.add_periodic_task(crontab(minute=QUINZE_EM_QUINZE_MINUTOS, hour="*"), cancela_passagens_pendentes.s())
        app.add_periodic_task(crontab(minute="30", hour="6,13"), finaliza_task_status.s())
        app.add_periodic_task(crontab(minute=CINCO_EM_CINCO_MINUTOS, hour="*"), dispara_rotina_atualizacao_trecho.s())
        app.add_periodic_task(crontab(minute="45", hour="5,11,17,23"), busca_preco_trechos_vendidos.s())
        app.add_periodic_task(
            crontab(minute=TRINTA_EM_TRINTA_MINUTOS, hour="*"),
            cancela_passagens_com_erro_confirmadas_na_api.s(),
        )
        app.add_periodic_task(
            crontab(minute="40", hour="*"),
            busca_dados_bpe_passagens.s(),
        )
        app.add_periodic_task(
            crontab(minute="18,48", hour="*"),
            task_cancelar_passagens_sem_travel_correspondente.s(),
        )
        app.add_periodic_task(
            crontab(minute=TRINTA_EM_TRINTA_MINUTOS, hour="*"),
            verificar_grupos_hibridos_cancelados.s(),
        )
        app.add_periodic_task(
            crontab(minute=QUINZE_EM_QUINZE_MINUTOS, hour="*"),
            task_update_grupos_hibridos_criados.s(),
        )

        map_tasks = {
            CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO: descobrir_operacao,
            CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS: descobrir_rotas,
            CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS: atualiza_trechos_vendidos,
            CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE: atualiza_data_limite_rotas,
        }

        cronograma = cronograma_atualizacao_svc.get_lista_crons()
        for company_internal_id, modelo_venda, tipo_atualizacao, dias_semana, horas, minutos in cronograma:
            task = map_tasks.get(tipo_atualizacao)
            if not task:
                continue
            app.add_periodic_task(
                crontab(minute=minutos, hour=horas, day_of_week=dias_semana),
                task.s(company_id=company_internal_id, modelo_venda=modelo_venda),
            )

        # marketplace
        app.add_periodic_task(
            crontab(minute=QUINZE_EM_QUINZE_MINUTOS, hour="*"),
            marketplace_busca_operacao.s(),
        )

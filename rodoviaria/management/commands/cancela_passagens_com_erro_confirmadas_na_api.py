from collections import defaultdict

from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand
from django.db.models import F

from rodoviaria.models.core import Company, Passagem
from rodoviaria.service.cancela_passagens_pendentes_svc import cancela_pedido_com_erro

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    def handle(self, *args, **options):
        passagens_com_erro_e_pedido_id = (
            Passagem.objects.select_related("company_integracao")
            .filter(
                status=Passagem.Status.ERRO,
                tags__name="passagem_com_erro_confirmada_na_api",
                company_integracao__modelo_venda=Company.ModeloVenda.MARKETPLACE,
            )
            .exclude(
                tags__name__in=(
                    "passagem_com_erro_cancelada_pelo_cron",
                    "cancelamento_nao_autorizado",
                )
            )
            .annotate(company_internal_id=F("company_integracao__company_internal_id"))
            .order_by("trechoclasse_integracao__datetime_ida")
        )
        map_passagens_company = {}
        for p in passagens_com_erro_e_pedido_id:
            if p.company_internal_id not in map_passagens_company:
                map_passagens_company[p.company_internal_id] = defaultdict(list)
            map_passagens_company[p.company_internal_id][p.pedido_external_id].append(p)
        for (
            company_internal_id,
            map_passagens_pedido_id,
        ) in map_passagens_company.items():
            for pedido_id, passagens in map_passagens_pedido_id.items():
                cancela_pedido_com_erro.s(company_internal_id, pedido_id, [p.id for p in passagens]).apply_async()

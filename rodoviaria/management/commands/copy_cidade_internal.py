from django.core.management import BaseCommand
from django.utils import timezone

from core.models_commons import Cidade as CoreCidade
from rodoviaria.models.core import CidadeInternal


class Command(BaseCommand):
    help = "Copia tabela Cidade do banco buser para a tabela CidadeInternal do banco rodoviaria"

    def handle(self, *args, **options):
        cidades_banco_buser = CoreCidade.objects.in_bulk(field_name="id")
        cidades_banco_rodov = CidadeInternal.objects.in_bulk(field_name="id")
        to_create = []
        to_update = []

        for cidade_id, cidade_buser in cidades_banco_buser.items():
            cidade_rodov = cidades_banco_rodov.get(cidade_id)
            if not cidade_rodov:
                cidade_rodov = CidadeInternal()
                cidade_rodov.id = cidade_buser.id
                to_create.append(cidade_rodov)
            else:
                to_update.append(cidade_rodov)
            cidade_rodov.name = cidade_buser.name
            cidade_rodov.uf = cidade_buser.uf
            cidade_rodov.sigla = cidade_buser.sigla
            cidade_rodov.timezone = cidade_buser.timezone
            cidade_rodov.city_code_ibge = cidade_buser.city_code_ibge
            cidade_rodov.uf_code_ibge = cidade_buser.uf_code_ibge
            cidade_rodov.updated_at = timezone.now()

        CidadeInternal.objects.bulk_create(to_create)
        CidadeInternal.objects.bulk_update(
            to_update,
            [
                "name",
                "uf",
                "sigla",
                "timezone",
                "city_code_ibge",
                "uf_code_ibge",
                "updated_at",
            ],
        )

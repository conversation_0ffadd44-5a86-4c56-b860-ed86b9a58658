from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from rodoviaria.service import inativa_rota_rotinas_svc

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = (
        "Inativa rotas ativas sem nenhuma rotina ativa & inativa rotinas que não foram atualizadas nos ultimos 7 dias"
    )

    def handle(self, *args, **options):
        inativa_rota_rotinas_svc.inativar_rotas_rotinas()

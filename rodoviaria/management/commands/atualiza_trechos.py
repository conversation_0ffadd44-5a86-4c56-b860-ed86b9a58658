import logging

from django.core.management.base import BaseCommand

from rodoviaria.models.core import Company
from rodoviaria.service import link_trechoclasse_svc

buserlogger = logging.getLogger("rodoviaria")


class Command(BaseCommand):
    help = "Atualiza dados de trechos."

    def add_arguments(self, parser):
        parser.add_argument("--tag", type=str, help="trecho_tag")

    def handle(self, *args, **options):
        tag = options.get("tag")
        company_ids = Company.objects.filter(
            features__contains=[Company.Feature.ACTIVE],
            modelo_venda=Company.ModeloVenda.MARKETPLACE,
        ).values_list("company_internal_id", flat=True)
        for company_id in company_ids:
            resp = link_trechoclasse_svc.atualiza(tag, company_id)
            buserlogger.info(resp)

import logging

from django.core.management.base import BaseCommand

from rodoviaria.models.core import Integracao, TrechoVendido
from rodoviaria.service import busca_precos_trechos_vendidos_svc

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Busca o preço dos trechos vendidos com preços pendentes"

    def handle(self, *args, **options):
        tvs_pendentes = TrechoVendido.objects.filter(
            status_preco=TrechoVendido.StatusPreco.PENDENTE,
            rota__company__integracao__name=Integracao.API.EULABS,
            rota__ativo=True,
        )
        for tv in tvs_pendentes:
            busca_precos_trechos_vendidos_svc.busca_preco_trecho_vendido.delay(tv.id)

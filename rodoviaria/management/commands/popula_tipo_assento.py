from datetime import datetime

from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from rodoviaria.models import TipoAssento, TrechoVendido
from rodoviaria.service.class_match_svc import (
    CLASSE_NAO_MAPEADA,
    _does_class_match_por_nome_ou_regra_excecao,
    buser_class,
)

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = """Popula a tabela TipoAssento com todos as classes encontradas na
    tabela TrechoVendido. E tenta fazer match pra todas as classes buser"""

    def add_arguments(self, parser):
        parser.add_argument("--size", type=int, help="Quantidade de trechos que vai buscar para criar tipo_assento")

    def handle(self, *args, **options):
        size = options.get("size", 10_000)
        trechos = (
            TrechoVendido.objects.select_related("rota__company")
            .filter(classe__isnull=False, tipo_assento__isnull=True)
            .order_by("id")
        )
        if size:
            trechos = trechos[:size]
        trechos_to_update = []
        tipos_assentos_to_update = {(t.company_id, t.tipo_assento_parceiro): t for t in TipoAssento.objects.all()}
        tipos_assentos_to_create = {}
        for trecho in trechos:
            trecho.tipo_assento = self._get_or_create_obj_tipo_assento(
                tipos_assentos_to_create, tipos_assentos_to_update, trecho
            )

            self._try_add_classe_preferencial(trecho)
            self._try_add_classes_match(trecho)

            trechos_to_update.append(trecho)

        TipoAssento.objects.bulk_update(
            list(tipos_assentos_to_update.values()),
            ["tipo_assento_buser_preferencial", "tipos_assentos_buser_match", "updated_at"],
            batch_size=5000,
        )
        TipoAssento.objects.bulk_create(list(tipos_assentos_to_create.values()), batch_size=5000)
        for trecho in trechos_to_update:
            trecho.tipo_assento_id = trecho.tipo_assento.id
        TrechoVendido.objects.bulk_update(trechos_to_update, ["tipo_assento_id", "updated_at"])

    def _get_or_create_obj_tipo_assento(self, tipos_assentos_to_create, tipos_assentos_to_update, trecho):
        if (trecho.rota.company_id, trecho.classe) in tipos_assentos_to_update:
            tipo_assento = tipos_assentos_to_update[(trecho.rota.company_id, trecho.classe)]
            tipo_assento.updated_at = datetime.now()
            return tipo_assento

        if (trecho.rota.company_id, trecho.classe) in tipos_assentos_to_create:
            return tipos_assentos_to_create[(trecho.rota.company_id, trecho.classe)]

        tipo_assento = TipoAssento(company=trecho.rota.company, tipo_assento_parceiro=trecho.classe)
        tipos_assentos_to_create[(trecho.rota.company_id, trecho.classe)] = tipo_assento
        return tipo_assento

    def _try_add_classe_preferencial(self, t):
        classe_preferencial = buser_class(t.classe)
        if classe_preferencial != CLASSE_NAO_MAPEADA:
            t.tipo_assento.tipo_assento_buser_preferencial = classe_preferencial

    def _try_add_classes_match(self, t):
        for ab in TipoAssento.TipoAssentoBuser.values:
            if not _does_class_match_por_nome_ou_regra_excecao(t.rota.company, ab, t.classe):
                continue

            if not t.tipo_assento.tipos_assentos_buser_match:
                t.tipo_assento.tipos_assentos_buser_match = []
            if ab not in t.tipo_assento.tipos_assentos_buser_match:
                t.tipo_assento.tipos_assentos_buser_match.append(ab)

from collections import defaultdict

from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand
from django.utils import timezone

from rodoviaria.api.eulabs.api import buscar_dados_bpe
from rodoviaria.models.core import Company, Integracao, Passagem

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    def handle(self, *args, **options):
        passagens_com_dados_pendentes = Passagem.objects.select_related("company_integracao").filter(
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao__datetime_ida__gt=timezone.now(),
            company_integracao__modelo_venda=Company.ModeloVenda.MARKETPLACE,
            company_integracao__integracao__name=Integracao.API.EULABS,
            tags__name="dados_bpe_pendente",
        )
        map_passagens_pedido_id = defaultdict(list)
        for p in passagens_com_dados_pendentes:
            map_passagens_pedido_id[p.company_integracao_id].append(p.id)
        for company_rodoviaria_id, passagens_ids in map_passagens_pedido_id.items():
            buscar_dados_bpe.delay(list(passagens_ids), company_rodoviaria_id)  # type: ignore

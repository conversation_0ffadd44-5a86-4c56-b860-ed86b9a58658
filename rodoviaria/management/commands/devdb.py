import djclick as click
from decouple import config
from django.core.management import CommandError
from django.core.management.color import no_style
from django.db import connections, transaction

from commons.devdb import ALL_DATA


@click.command()
@transaction.atomic(using="rodoviaria")
def command():
    # Só roda devdb em banco de teste!
    if config("DB_HOST", default="localhost") not in {
        "localhost",
        "postgresql",
        "postgresql14",
        "db",
        "**********",
        "postgresql-buserenvs.buserenvs.svc.cluster.local",
        "postgresql-14-buserenvs.buserenvs.svc.cluster.local",
    }:
        raise CommandError("Só pode rodar devdb em banco de teste!")

    connection = connections["rodoviaria"]

    for instancias in ALL_DATA:
        model_name = instancias[0]._meta.object_name
        click.secho(f"Populando dados da tabela {model_name}... ", fg="blue", nl=False)

        # TODO: ultilizar bulk_create(update_conflicts=True) quando migrar para django 4.1
        for instancia in instancias:
            if not instancia.pk:
                raise CommandError("Todas instâncias devem ter pk definida")
            instancia.save()

        _reset_sequence(connection, instancias)

        click.secho("OK", fg="green")


def _reset_sequence(connection, models):
    with connection.cursor() as cursor:
        sequence_sql = connection.ops.sequence_reset_sql(no_style(), models)
        if sequence_sql:
            for line in sequence_sql:
                cursor.execute(line)

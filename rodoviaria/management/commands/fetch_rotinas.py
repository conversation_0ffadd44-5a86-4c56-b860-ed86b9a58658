from django.core.management.base import BaseCommand

from rodoviaria.service import rotina_svc


class Command(BaseCommand):
    help = "Fetch rotinas das rotas de uma empresa"

    def add_arguments(self, parser):
        parser.add_argument("--company-id", type=int, help="company_internal_id")
        parser.add_argument("--next-days", type=int, help="next_days")

    def handle(self, *args, **options):
        company_id = options["company_id"]
        next_days = options["next_days"]
        try:
            rotina_svc.fetch_rotinas_empresa(company_internal_id=company_id, next_days=next_days)
        except NotImplementedError:
            pass

from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from rodoviaria.service.exceptions import RodoviariaUnsupportedFeatureException
from rodoviaria.service.marketplace_fetch_rotas_svc import marketplace_fetch_rotas

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Define rota para os grupos integrados."

    def add_arguments(self, parser):
        parser.add_argument("--company-id", type=int, help="company_internal_id")

    def handle(self, *args, **options):
        company_id = options["company_id"]

        try:
            qtd_rotas = marketplace_fetch_rotas(company_id=company_id)
            task_logger.info("Iniciada a atualização ou criação de %s rotas da empresa de id %s", qtd_rotas, company_id)
        except (NotImplementedError, RodoviariaUnsupportedFeatureException):
            pass

from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.executors.middlewares.token_bucket_middleware import token_bucket_middleware
from rodoviaria.api.totalbus import endpoints
from rodoviaria.models.core import Company, CompanyCategoriaEspecial, Integracao
from rodoviaria.models.totalbus import TotalbusLogin

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Busca todas as categorias disponibilizadas por todas as empresas Totalbus."

    def handle(self, *args, **options):
        logins_empresas_totalbus = TotalbusLogin.objects.select_related("company").filter(
            company__integracao__name=Integracao.API.TOTALBUS,
            company__features__contains=[Company.Feature.ACTIVE],
        )

        print("len logins_empresas_totalbus: ", len(logins_empresas_totalbus))

        for login in logins_empresas_totalbus:
            print("--------------------------------")
            print("Empresa:", login.company.name)
            with token_bucket_middleware.suppress_not_enough_tokens_error():
                response = endpoints.ConsultarCategoriaRequestConfig(login).invoke(get_http_executor())
            response_json = response.json()

            categorias = self._get_categorias_from_response(login.company, response_json)

            print("categorias a criar:", len(categorias))
            CompanyCategoriaEspecial.objects.bulk_create(categorias, ignore_conflicts=True)

    def _get_categorias_from_response(self, company, response_json):
        print("pegando categorias:")
        print("id, desc")
        categorias = []
        for categ in response_json:
            id = categ["categoriaId"]
            desc = categ["desccategoria"]
            print(id, desc)
            categorias.append(
                CompanyCategoriaEspecial(company=company, categoria_id_external=id, descricao_external=desc)
            )
        return categorias

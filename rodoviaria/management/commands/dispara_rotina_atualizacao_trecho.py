from datetime import date, datetime, timedelta

from celery import group
from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand
from django.utils import timezone

from commons.dateutils import today_midnight
from rodoviaria.models.core import RotinaAtualizacaoTrecho
from rodoviaria.service import atualiza_precos_search_svc

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Dispara busca na API de trechos cadastrados na tabela RotinaAtualizacaoTrecho"

    def handle(self, *args, **options):
        rotinas = RotinaAtualizacaoTrecho.objects.filter(ativo=True)
        now = timezone.now()
        for rotina_atualizacao in rotinas:
            proxima_execucao = rotina_atualizacao.ultima_execucao + timedelta(
                minutes=rotina_atualizacao.intervalo_execucao_minutos
            )
            if now < proxima_execucao:
                continue
            if rotina_atualizacao.is_intervalo_dinamico:
                tasks = self._make_tasks_intervalo_dinamico(rotina_atualizacao)
            elif rotina_atualizacao.is_intervalo_fixo:
                tasks = self._make_tasks_intervalo_fixo(rotina_atualizacao)
                if not tasks:
                    rotina_atualizacao.ativo = False
                    continue
            group(tasks).apply_async()
            rotina_atualizacao.ultima_execucao = arredonda_5_minutos_para_baixo(timezone.now())
        RotinaAtualizacaoTrecho.objects.bulk_update(rotinas, fields=["ativo", "ultima_execucao"])

    def _make_tasks_intervalo_dinamico(self, rotina_atualizacao: RotinaAtualizacaoTrecho):
        today = today_midnight().date()
        data_inicial = today + timedelta(days=rotina_atualizacao.margem_inicio_intervalo_dinamico or 0)
        data_final = data_inicial + timedelta(days=rotina_atualizacao.dias_busca_intervalo_dinamico)
        return self._make_tasks_margem_data(rotina_atualizacao, data_inicial, data_final)

    def _make_tasks_intervalo_fixo(self, rotina_atualizacao: RotinaAtualizacaoTrecho):
        data_inicial = rotina_atualizacao.data_inicio_intervalo_fixo
        data_final = rotina_atualizacao.data_fim_intervalo_fixo
        return self._make_tasks_margem_data(rotina_atualizacao, data_inicial, data_final)

    def _make_tasks_margem_data(
        self, rotina_atualizacao: RotinaAtualizacaoTrecho, data_inicial: date, data_final: date
    ):
        data_busca = data_inicial
        tasks = []
        today = today_midnight().date()
        total_dias = (data_final - data_inicial).days + 1
        for d in range(total_dias):
            data_busca = data_inicial + timedelta(days=d)
            if data_busca >= today:
                tasks.append(
                    atualiza_precos_search_svc.buscar_viagens_todas_empresas_api_na_search.s(
                        rotina_atualizacao.cidade_origem_id,
                        rotina_atualizacao.cidade_destino_id,
                        data_busca.strftime("%Y-%m-%d"),
                        rotina_atualizacao_id=rotina_atualizacao.id,
                    )
                )
        return tasks


def arredonda_5_minutos_para_baixo(data_hora: datetime):
    minutos = data_hora.minute
    minutos_arredondados = minutos - (minutos % 5)
    return data_hora.replace(minute=minutos_arredondados, second=0, microsecond=0)

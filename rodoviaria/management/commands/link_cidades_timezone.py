from django.core.management.base import BaseCommand

import rodoviaria.models.core as rodoviaria


class Command(BaseCommand):
    help = "Link timezone from buser_django to rodoviaria"

    def handle(self, *args, **options):
        cidade_tz_map = dict(rodoviaria.CidadeInternal.objects.values_list("pk", "timezone"))
        rodoviaria_cidades = rodoviaria.Cidade.objects.all()
        for cidade in rodoviaria_cidades:
            if not cidade.cidade_internal_id:
                continue
            cidade.timezone = cidade_tz_map[cidade.cidade_internal_id]
        rodoviaria.Cidade.objects.bulk_update(rodoviaria_cidades, ["timezone", "updated_at"])

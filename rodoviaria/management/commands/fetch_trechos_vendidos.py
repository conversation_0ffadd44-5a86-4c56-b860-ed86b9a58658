from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from rodoviaria.models import Company
from rodoviaria.service.fetch_trechos_vendidos_svc import fetch_trechos_by_company_id

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Busca de trechos vendidos de rotas ativas."

    def add_arguments(self, parser):
        parser.add_argument("--company-id", type=int, help="company_internal_id")
        parser.add_argument("--modelo-venda", type=str, help="modelo-venda hibrido ou marketplace")

    def handle(self, *args, **options):
        company_id = options["company_id"]
        modelo_venda = options.get("modelo_venda")

        if modelo_venda is None:
            modelo_venda = Company.ModeloVenda.MARKETPLACE
        fetch_trechos_by_company_id(company_internal_id=company_id, modelo_venda=modelo_venda)
        task_logger.info(
            "A busca de trechos vendidos de rotas ativas para empresa de id_internal %s modelo_venda=%s foi iniciada",
            company_id,
            modelo_venda,
        )

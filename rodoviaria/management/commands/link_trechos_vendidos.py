import logging

from django.core.management.base import BaseCommand

from rodoviaria.models.core import Company
from rodoviaria.service.link_trechos_vendidos_svc import atualizar_id_internal_trechos_vendidos_company

buserlogger = logging.getLogger("rodoviaria")


class Command(BaseCommand):
    help = "Define rota para os grupos."

    def add_arguments(self, parser):
        parser.add_argument("--company-id", type=int, help="company_internal_id")

    def handle(self, *args, **options):
        company_id = options["company_id"]

        if company_id:
            company_ids = [company_id]
        else:
            company_ids = list(
                Company.objects.filter(
                    features__contains=[Company.Feature.ITINERARIO],
                ).values_list("company_internal_id", flat=True)
            )

        for company_id in company_ids:
            count = atualizar_id_internal_trechos_vendidos_company(company_id=company_id)
            buserlogger.info(
                "Foram feitos %s links de trechos vendidos entre rodoviaria/buser_django para a empresa de id %s",
                count,
                company_id,
            )

from datetime import timedelta

from django.core.management.base import BaseCommand
from django.utils import timezone

from rodoviaria.models.core import TaskStatus


class Command(BaseCommand):
    help = "Finaliza TaskStatus com mais de 36 horas de execução"

    def handle(self, *args, **options):
        TaskStatus.objects.filter(
            status=TaskStatus.Status.PENDING, updated_at__lt=(timezone.now() - timedelta(hours=36))
        ).update(status=TaskStatus.Status.FAILURE, updated_at=timezone.now())

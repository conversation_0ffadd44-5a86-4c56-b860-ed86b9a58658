from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from rodoviaria.models.core import Company, CronogramaAtualizacaoOperacao
from rodoviaria.service import atualiza_operacao_empresa_svc

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Preenche a tabela rodoviaria_CronogramaAtualizacaoOperacao com os horários de atualização atuais"

    def handle(self, *args, **options):
        companies = Company.objects.values_list(
            "id", "company_internal_id", "modelo_venda", "features", "integracao__name"
        )
        to_create = []
        for company_id, company_internal_id, modelo_venda, features, integracao_name in companies:
            (
                minute,
                hour,
                day,
            ) = atualiza_operacao_empresa_svc.descobrir_rotas_trigger_time(
                company_internal_id, modelo_venda, features, integracao_name
            )
            if minute:
                to_create += self.cronogramas_to_create(
                    company_id, CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS, day, hour, minute
                )
            (
                minute,
                hour,
                day,
            ) = atualiza_operacao_empresa_svc.fetch_trechos_vendidos_trigger_time(
                company_internal_id, modelo_venda, features, integracao_name
            )
            if minute:
                to_create += self.cronogramas_to_create(
                    company_id, CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS, day, hour, minute
                )
            (
                minute,
                hour,
                day,
            ) = atualiza_operacao_empresa_svc.fetch_data_limite_rotas_trigger_time(
                company_internal_id, modelo_venda, features, integracao_name
            )
            if minute:
                to_create += self.cronogramas_to_create(
                    company_id, CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE, day, hour, minute
                )
            (
                minute,
                hour,
                day,
            ) = atualiza_operacao_empresa_svc.fetch_all_operation_trigger_time(
                company_internal_id, modelo_venda, features, integracao_name
            )
            if minute:
                to_create += self.cronogramas_to_create(
                    company_id, CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO, day, hour, minute
                )
        CronogramaAtualizacaoOperacao.objects.bulk_create(to_create, ignore_conflicts=True)

    def cronogramas_to_create(self, company_id, tipo_atualizacao, dias, hora, minuto):
        dias_da_semana = [1, 2, 3, 4, 5, 6, 7] if dias == "*" else dias.split(",")
        horario = f"{hora.zfill(2)}:{minuto.zfill(2)}"
        horarios_to_create = []
        for dia in dias_da_semana:
            horarios_to_create.append(
                CronogramaAtualizacaoOperacao(
                    company_id=company_id, tipo_atualizacao=tipo_atualizacao, horario=horario, dia_semana=dia
                )
            )
        return horarios_to_create

from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from rodoviaria.api.executors.middlewares.token_bucket_middleware import token_bucket_middleware
from rodoviaria.models.core import Company
from rodoviaria.service.descobrir_operacao_svc import descobrir_operacao_proximos_dias
from rodoviaria.service.exceptions import (
    RodoviariaOTAException,
)

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Busca todas as rotas disponibilizadas pela empresa."

    def add_arguments(self, parser):
        parser.add_argument("--company-internal-id", type=int, help="company_internal_id")
        parser.add_argument("--modelo-venda", type=int, help="modelo_venda")

    def handle(self, *args, **options):
        company_internal_id = options["company_internal_id"]
        modelo_venda = options.get("modelo_venda")

        if modelo_venda is None:
            modelo_venda = Company.ModeloVenda.MARKETPLACE

        company = Company.objects.get(company_internal_id=company_internal_id, modelo_venda=modelo_venda)
        try:
            with token_bucket_middleware.suppress_not_enough_tokens_error():
                descobrir_operacao_proximos_dias(company, next_days=company.margem_dias_busca_operacao)
                task_logger.info(
                    "A descoberta de operacao para empresa de id_internal %s foi iniciada", company_internal_id
                )
        except (NotImplementedError, RodoviariaOTAException):
            pass

from collections import defaultdict

from django.core.management.base import BaseCommand
from django.db.models import Count

from rodoviaria.models.core import RotinaTrechoVendido, TrechoVendido


class Command(BaseCommand):
    def handle(self, *args, **options):
        rota_ids_com_tv_duplicado = (
            TrechoVendido.objects.values("rota_id", "origem_id", "destino_id", "classe")
            .annotate(total=Count("id"))
            .filter(total__gt=1)
        ).values_list("rota_id", flat=True)

        for rota_id in rota_ids_com_tv_duplicado:
            rtvs_to_update = {}
            tvs_to_delete_ids = []
            trechos_vendidos = TrechoVendido.objects.filter(rota_id=rota_id).order_by("id_internal", "created_at")
            rtvs = RotinaTrechoVendido.objects.filter(trecho_vendido__in=trechos_vendidos)
            rtvs_map = defaultdict(list)
            rotinas_ids_map = defaultdict(set)
            for rtv in rtvs:
                rtv_key = (rtv.rotina_id, rtv.trecho_vendido_id)
                rtvs_map[rtv.trecho_vendido_id].append(rtv)
                rotinas_ids_map[rtv.trecho_vendido_id].add(rtv.rotina_id)

            tvs_to_keep = {}
            for tv in trechos_vendidos:
                key = (tv.rota_id, tv.origem_id, tv.destino_id, tv.classe)
                if not tvs_to_keep.get(key):
                    tvs_to_keep[key] = tv
                else:
                    tvs_to_delete_ids.append(tv.id)
                    keeped_tv = tvs_to_keep[key]
                    for rtv in rtvs_map.get(tv.id, []):
                        if rtv.rotina_id not in rotinas_ids_map[keeped_tv.id]:
                            rtv.trecho_vendido_id = keeped_tv.id
                            rtvs_to_update[rtv_key] = rtv
                            rotinas_ids_map[tv.id].add(rtv.rotina_id)
            print(f"Executando operação para {rota_id=}.")
            RotinaTrechoVendido.objects.bulk_update(
                rtvs_to_update.values(),
                fields=["trecho_vendido_id"],
                batch_size=5000,
            )
            TrechoVendido.objects.filter(id__in=tvs_to_delete_ids).delete()
            print(f"{len(tvs_to_delete_ids)} Trechos Vendidos deletados")
            print(f"{len(rtvs_to_update)} Rotina Trechos Vendidos deletados")

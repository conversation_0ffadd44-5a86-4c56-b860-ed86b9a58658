from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand
from django.utils import timezone

from rodoviaria.models.core import Integracao, Passagem

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Busca todas as categorias disponibilizadas por todas as empresas Totalbus."

    def handle(self, *args, **options):
        passagens = Passagem.objects.filter(
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao__datetime_ida__gt=timezone.now(),
            company_integracao__integracao__name=Integracao.API.PRAXIO,
            trechoclasse_integracao__origem__nickname__icontains="Curitiba",
            provider_data__isnull=False,
        )
        for p in passagens:
            provider_data = p.provider_data
            lista_passagem = provider_data.get("ListaPassagem")
            if lista_passagem:
                codigo_barras_curitiba = lista_passagem.get("CodigoBarrasCuritiba")
                if codigo_barras_curitiba:
                    p.numero_bilhete_embarque = codigo_barras_curitiba
                    p.tipo_taxa_embarque = Passagem.TipoTaxaEmbarque.QRCODE
        Passagem.objects.bulk_update(passagens, fields=["numero_bilhete_embarque", "tipo_taxa_embarque"])

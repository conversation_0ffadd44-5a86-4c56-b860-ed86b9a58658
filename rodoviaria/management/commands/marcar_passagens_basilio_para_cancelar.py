from datetime import datetime, timedelta

from django.core.management.base import BaseCommand
from django.utils import timezone

from commons.dateutils import to_tz
from rodoviaria.models.core import Passagem
from rodoviaria.service import solicita_cancelamento_svc


class Command(BaseCommand):
    help = "Cancela passagens da Basilio que não foram canceladas devido a API ter bloqueado"

    def handle(self, *args, **options):
        inicio = to_tz(datetime(2022, 6, 14, 17, 0, 0), "UTC")
        fim = to_tz(datetime(2022, 7, 14, 15, 0, 0), "UTC")
        datetime_ida_mais_três = timezone.now() + timedelta(hours=3)
        passagens = Passagem.objects.filter(
            company_integracao_id=44,
            trechoclasse_integracao__datetime_ida__gt=datetime_ida_mais_três,
            status=Passagem.Status.CANCELADA,
            datetime_cancelamento__range=[inicio, fim],
        )
        passagens.update(status=Passagem.Status.CONFIRMADA)
        solicita_cancelamento_svc.solicita_cancelamento_passagens(passagens)

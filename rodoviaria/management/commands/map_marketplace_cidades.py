from django.core.management.base import BaseCommand

from rodoviaria.service.map_marketplace_cidades_svc import MapMarketplaceCidadesSVC


class Command(BaseCommand):
    help = "Map marketplace Cidades"

    def add_arguments(self, parser):
        parser.add_argument("--company-id", type=int, help="company_internal_id")
        parser.add_argument("--modelo_venda", type=str, help="modelo_venda da company")

    def handle(self, *args, **options):
        company_id = options.get("company_id")
        modelo_venda = options.get("modelo_venda")
        count = MapMarketplaceCidadesSVC(company_id, modelo_venda).execute()
        print(f"{count} Locais de Embarque criados para empresa ({company_id},{modelo_venda})")

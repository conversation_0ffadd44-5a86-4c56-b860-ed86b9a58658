from celery.utils.log import get_task_logger
from django.core.management.base import BaseCommand

from rodoviaria.service.fetch_data_limite_rotas_svc import fetch_data_limite_rotas

task_logger = get_task_logger(__name__)


class Command(BaseCommand):
    help = "Busca de ultimo dia em que as rotas estão ativas na API."

    def add_arguments(self, parser):
        parser.add_argument("--company-id", type=int, help="company_internal_id")

    def handle(self, *args, **options):
        company_id = options["company_id"]

        fetch_data_limite_rotas(company_internal_id=company_id)
        task_logger.info(
            "A busca de data limite de rotas ativas para empresa de id_internal %s foi iniciada", company_id
        )

from django.core.management.base import BaseCommand

from rodoviaria.service.salva_rotas_bulk_svc import bulk_update_checkpoints


class Command(BaseCommand):
    help = "Fetch rotinas das rotas de uma empresa"

    def add_arguments(self, parser):
        parser.add_argument("--rotas-ids", nargs="+", type=int, help="Lista das rotas para atualizar checkpoints")
        parser.add_argument("--company-internal-id", type=int, help="Id rodoviaria da empresa")

    def handle(self, *args, **options):
        rotas_ids = options["rotas_ids"]
        company_internal_id = options["company_internal_id"]
        bulk_update_checkpoints(company_internal_id, rotas_ids)

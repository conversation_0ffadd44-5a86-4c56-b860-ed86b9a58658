from functools import cached_property

from django.conf import settings
from django.db import models
from django_cryptography.fields import encrypt
from django_qserializer import SerializableManager

from rodoviaria.api.smartbus import models as form_models
from rodoviaria.models.core import <PERSON>FieldAbstract, Company, Rota


class SmartbusLogin(AutoFieldAbstract):
    id: int
    DEFAULT_URL_BASE = "http://{ambiente}-{cliente}-gateway-smartbus.oreons.com"

    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    cliente = models.CharField(max_length=40)
    username = models.Char<PERSON><PERSON>(max_length=40)
    password = encrypt(models.Char<PERSON>ield(max_length=100))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    forma_pagamento_id = models.IntegerField(blank=True, null=True, default=2)

    objects = SerializableManager()

    @cached_property
    def url_base(self):
        return self.DEFAULT_URL_BASE.format(ambiente=settings.SMARTBUS_API_ENVIRONMENT, cliente=self.cliente)

    def to_dict_json(self):
        return {
            "company_id": self.company_id,
            "username": self.username,
            "password": self.password,
            "cliente": self.cliente,
            "forma_pagamento_id": self.forma_pagamento_id,
        }


class RotaSmartbus(Rota):
    id: int

    class Meta:
        proxy = True

    @cached_property
    def parsed_data(self):
        return form_models.DetalhesRotaResponse.parse_raw(self.provider_data)

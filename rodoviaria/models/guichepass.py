from django.db import models
from django_cryptography.fields import encrypt
from django_qserializer import SerializableManager

from rodoviaria.models.core import AutoFieldAbstract, Company


class GuichepassLogin(AutoFieldAbstract):
    id: int
    DEFAULT_URL_BASE = "http://api-gravataense.buson.com.br"
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    client_id = models.CharField(max_length=40)
    username = models.Char<PERSON><PERSON>(max_length=40)
    password = encrypt(models.Char<PERSON><PERSON>(max_length=100))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = SerializableManager()

    def to_dict_json(self):
        return {
            "username": self.username,
            "password": self.password,
            "client_id": self.client_id,
        }

    @property
    def cliente(self) -> str:
        return str(self.company.name)

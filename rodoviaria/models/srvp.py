from django.db import models
from simple_history.models import HistoricalRecords

from rodoviaria.models.core import AutoFieldAbstract, Cidade, RodoviariaUser


class SRVPSearch(AutoFieldAbstract):
    id: int
    origem_id: int
    origem = models.ForeignKey(Cidade, related_name="+", on_delete=models.CASCADE)
    destino_id: int
    destino = models.ForeignKey(Cidade, related_name="+", on_delete=models.CASCADE)


class SRVPService(AutoFieldAbstract):
    id: int
    search_id: int
    search = models.ForeignKey(SRVPSearch, on_delete=models.CASCADE)
    key = models.CharField(max_length=60, unique=True)
    datetime_ida = models.DateTimeField()
    datetime_chega = models.DateTimeField()
    servico = models.CharField(max_length=30)
    assentos_livres = models.IntegerField()
    tipo = models.CharField(max_length=30)
    comentario = models.Char<PERSON><PERSON>(max_length=100)

    changed_by_id: int
    changed_by = models.ForeignKey(RodoviariaUser, null=True, blank=True, on_delete=models.CASCADE)
    history = HistoricalRecords(user_model=RodoviariaUser)

    @property
    def _history_user(self):
        return self.changed_by

    @_history_user.setter
    def _history_user(self, value):
        self.changed_by = value

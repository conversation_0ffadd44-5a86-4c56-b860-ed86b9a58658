from django.db import models
from django_cryptography.fields import encrypt
from django_qserializer import SerializableManager

from rodoviaria.models.core import AutoFieldAbstract, Company


class PraxioLogin(AutoFieldAbstract):
    id: int
    DEFAULT_URL_BASE = "https://oci-parceiros2.praxioluna.com.br/Autumn"

    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    sistema = models.CharField(max_length=40, default="WINVR.EXE")
    name = models.CharField(max_length=40)
    tipo_bd = models.IntegerField(default=0)
    empresa = models.CharField(max_length=40, default="BUSER")
    cliente = models.CharField(max_length=40)
    tipo_aplicacao = models.IntegerField(default=0)
    password = encrypt(models.CharField(max_length=100))
    desconto_manual = models.<PERSON><PERSON>anField(default=True)
    created_at = models.DateTime<PERSON>ield(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = SerializableManager()

    def to_dict_json(self):
        return {
            "name": self.name,
            "password": self.password,
            "cliente": self.cliente,
            "desconto_manual": self.desconto_manual,
        }

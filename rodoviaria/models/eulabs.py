import json
from functools import cached_property

from django.db import models
from django_qserializer import SerializableManager

from rodoviaria.api.eulabs import models as forms
from rodoviaria.models.core import AutoFieldAbstract, Company, Rota


class EulabsLogin(AutoFieldAbstract):
    id: int
    DEFAULT_URL_BASE = "https://dev-api-portais-v2.euca.tur.br"
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    api_id = models.CharField(max_length=10, null=True, blank=True)
    api_key = models.CharField(max_length=50, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = SerializableManager()

    def to_dict_json(self):
        return {
            "api_id": self.api_id,
            "api_key": self.api_key,
        }

    @property
    def cliente(self) -> str:
        return f"{self.api_id}-{self.api_key}"


class RotaEulabs(Rota):
    id: int

    class Meta:
        proxy = True

    @cached_property
    def parsed_data(self):
        return forms.Itinerario.parse_obj(json.loads(self.provider_data))

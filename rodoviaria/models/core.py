import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal

from celery.utils.log import get_task_logger
from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.db.models import Q
from django.utils import timezone
from django_qserializer.serialization import SerializableManager
from simple_history.models import HistoricalRecords
from taggit.managers import TaggableManager
from taggit.models import TaggedItemBase

from commons.dateutils import timedelta_to_milliseconds, to_default_tz

task_logger = get_task_logger(__name__)
rodov_logger = logging.getLogger("rodoviaria")

TIMEZONES = [
    "America/Araguaina",
    "America/Bahia",
    "America/Belem",
    "America/Boa_Vista",
    "America/Campo_Grande",
    "America/Cuiaba",
    "America/Eirunepe",
    "America/Fortaleza",
    "America/Maceio",
    "America/Manaus",
    "America/Noronha",
    "America/Porto_Velho",
    "America/Recife",
    "America/Rio_Branco",
    "America/Santarem",
    "America/Sao_Paulo",
]
CHOICES_TIMEZONES = [(t, t) for t in TIMEZONES]

UFS = [
    ("AC", "Acre"),
    ("AL", "Alagoas"),
    ("AP", "Amapá"),
    ("AM", "Amazonas"),
    ("BA", "Bahia"),
    ("CE", "Ceará"),
    ("DF", "Distrito Federal"),
    ("ES", "Espírito Santo"),
    ("GO", "Goiás"),
    ("MA", "Maranhão"),
    ("MT", "Mato Grosso"),
    ("MS", "Mato Grosso do Sul"),
    ("MG", "Minas Gerais"),
    ("PA", "Pará"),
    ("PB", "Paraíba"),
    ("PR", "Paraná"),
    ("PE", "Pernambuco"),
    ("PI", "Piauí"),
    ("RJ", "Rio de Janeiro"),
    ("RN", "Rio Grande do Norte"),
    ("RS", "Rio Grande do Sul"),
    ("RO", "Rondônia"),
    ("RR", "Roraima"),
    ("SC", "Santa Catarina"),
    ("SP", "São Paulo"),
    ("SE", "Sergipe"),
    ("TO", "Tocantins"),
    ("EX", "Exterior"),
]


class WeekDay(models.IntegerChoices):
    # Dia da semana no padrão iso8601 começa na segunda e é representado com o número 1 (não mude para zero!)
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7


class AutoFieldAbstract(models.Model):
    id = models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")

    class Meta:
        abstract = True


class Integracao(AutoFieldAbstract):
    class API(models.TextChoices):
        TOTALBUS = "totalbus"
        PRAXIO = "praxio"
        VEXADO = "vexado"
        GUICHE = "guichepass"
        EULABS = "eulabs"
        SMARTBUS = "smartbus"
        TI_SISTEMAS = "ti_sistemas"

    name = models.CharField(max_length=50, null=True, blank=True, unique=True)
    versao = models.CharField(max_length=30)
    use_low_rate_limit = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Integrações"
        verbose_name = "Integração"

    def __str__(self):
        return self.name


class Company(AutoFieldAbstract):
    class Feature(models.TextChoices):
        ITINERARIO = "itinerario"
        BUSCAR_SERVICO = "buscar_servico"
        ADD_PAX_STAFF = "add_pax_staff"
        BPE = "bpe"
        MOTORISTA = "motorista"
        ATUALIZAR_PRECO = "atualizar_preco"
        ACTIVE = "active"
        ESCALAR_VEICULOS = "escalar_veiculos"
        USAR_PRECO_API = "usar_preco_api"
        PARENT_COMPANY_NO_BPE = "parent_company_no_bpe"
        FETCH_TRECHOS_FURTHER = "fetch_trechos_further"
        ATUALIZAR_PRECO_CHECKOUT = "atualizar_preco_checkout"
        AUTO_INTEGRA_OPERACAO = "auto_integra_operacao"
        ATUALIZA_OPERACAO_HIGH_FREQ = "atualiza_operacao_com_alta_frequencia"
        RISK_SHARE = "risk_share"  # usado em queries para separar de marketplace
        ASSENTOS_OCIOSOS = "assentos_ociosos"  # usado em queries para separar de marketplace
        USE_PRICE_PROMOTIONAL = "use_price_promotional"  # Tomada de decisão se volta menor preço da Eulabs
        NAO_ATUALIZA_PRECO = "nao_atualiza_preco"
        NAO_ATUALIZA_VAGAS = "nao_atualiza_vagas"
        ZERO_TOLERANCIA_MATCH_HORARIO = "zero_tolerancia_match_horario"

    class ModeloVenda(models.TextChoices):
        MARKETPLACE = "marketplace"
        HIBRIDO = "hibrido"

    objects = SerializableManager()
    integracao_id: int
    integracao = models.ForeignKey(Integracao, on_delete=models.CASCADE)
    company_internal_id = models.IntegerField(db_index=True)
    company_external_id = models.IntegerField(db_index=True, null=True, blank=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    url_base = models.URLField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    modelo_venda = models.CharField(max_length=15, choices=ModeloVenda.choices, default=ModeloVenda.MARKETPLACE)
    features = ArrayField(models.CharField(max_length=50, choices=Feature.choices), null=True, blank=True)
    previous_features = ArrayField(models.CharField(max_length=50, choices=Feature.choices), null=True, blank=True)
    previous_features_updated_at = models.DateTimeField(null=True, blank=True)
    max_percentual_divergencia = models.PositiveIntegerField(null=True, blank=True)
    auto_integra_rotas_min = models.PositiveIntegerField(null=True, blank=True)
    margem_dias_busca_operacao = models.PositiveIntegerField(default=60)

    class Meta:
        verbose_name_plural = "Integrações com empresas"
        verbose_name = "Integração com empresa"
        unique_together = ("company_internal_id", "modelo_venda")

    def __str__(self):
        return f"{self.modelo_venda} - {self.integracao} - {self.name} - {self.company_internal_id}"

    def has_feature(self, feature):
        if not self.features:
            return False
        return feature in self.features

    @classmethod
    def get_by_trechoclasse_id(cls, trechoclasse_id):
        return (
            TrechoClasse.objects.select_related("grupo__company_integracao")
            .get(trechoclasse_internal_id=trechoclasse_id)
            .grupo.company_integracao
        )


class CidadeInternal(models.Model):
    # select
    #    id, name, uf, sigla, timezone, city_code_ibge, uf_code_ibge, updated_at
    # from core_cidade
    id: int = models.IntegerField(primary_key=True)
    name = models.TextField()
    uf = models.CharField(max_length=2, null=True, blank=True, choices=UFS)
    sigla = models.CharField(max_length=3, null=True, blank=True)
    timezone = models.CharField(null=True, blank=True, max_length=256, choices=CHOICES_TIMEZONES)
    city_code_ibge = models.IntegerField(null=True, blank=True)
    uf_code_ibge = models.IntegerField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.uf}"


class Cidade(AutoFieldAbstract):
    id_external = models.CharField(max_length=30, null=True, blank=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    cidade_internal_id: int
    cidade_internal = models.ForeignKey(CidadeInternal, null=True, blank=True, on_delete=models.SET_NULL)
    timezone = models.CharField(null=True, blank=True, max_length=256, choices=CHOICES_TIMEZONES)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Integrações com cidades"
        verbose_name = "Integração com cidade"

    def __str__(self):
        return f"{self.name} - {self.cidade_internal_id} - {self.company.name}"


class LocalEmbarque(AutoFieldAbstract):
    id_external = models.CharField(max_length=30)
    nickname = models.CharField(max_length=100, null=True, blank=True)
    cidade_id: int
    cidade = models.ForeignKey(Cidade, on_delete=models.CASCADE)
    local_embarque_internal_id = models.IntegerField(db_index=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = SerializableManager()

    class Meta:
        verbose_name_plural = "Integrações com locais de embarque"
        verbose_name = "Integração com local de embarque"

    def __str__(self):
        return f"{self.nickname} - {self.local_embarque_internal_id}"

    def to_dict_json(self):
        d = {
            "id": self.id,
            "name": self.cidade.cidade_internal and self.cidade.cidade_internal.name,
            "uf": self.cidade.cidade_internal and self.cidade.cidade_internal.uf,
            "nickname": self.nickname,
            "id_external": self.id_external,
            "id_internal": self.local_embarque_internal_id,
        }
        return d


class Rota(AutoFieldAbstract):
    company_id: int
    company = models.ForeignKey["Company"](Company, on_delete=models.CASCADE)
    id_hash = models.CharField(max_length=50, db_index=True)
    id_internal = models.IntegerField(db_index=True, null=True, blank=True)
    id_external = models.CharField(blank=True, db_index=True, max_length=50, null=True)
    ativo = models.BooleanField(default=True)
    data_limite = models.DateField(null=True, blank=True)

    provider_data = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = (
            "company",
            "id_hash",
        )

    def get_itinerario(self):
        return self.itinerario.all()

    def get_itinerario_name(self):
        return " - ".join([c.local.cidade.name for c in self.get_itinerario()])

    def first_checkpoint(self):
        return self.itinerario.first()

    def first_checkpoint_timezone_or_default(self, default="America/Sao_Paulo"):
        try:
            first_checkpoint = Checkpoint.objects.select_related("local__cidade").get(rota_id=self.id, idx=0)
            first_timezone = first_checkpoint.local.cidade.timezone
        except (Checkpoint.DoesNotExist, AttributeError):
            first_timezone = None
        if not first_timezone:
            rodov_logger.debug("first_checkpoint_timezone não encontrado para rota %s", self.id)
            first_timezone = default
        return first_timezone

    def last_checkpoint(self):
        return self.itinerario.last()

    def atualiza_rotinas_com_base_nas_novas(self, novos_datetimes_ida_encontrados_utc, next_days):
        if not novos_datetimes_ida_encontrados_utc:
            if next_days >= 14:
                self.ativo = False
                self.save()
                Rotina.objects.filter(rota_id=self.pk, datetime_ida__gt=to_default_tz(datetime.now())).update(
                    ativo=False
                )
                msg_log = f"Rota rodoviaria_rota_id {self.id} sendo inativada por falta de rotinas"
                rodov_logger.info(msg_log)
                task_logger.info(msg_log)
            return

        self.ativo = True
        self.save()
        sorted_novos_datetimes_ida_encontrados_utc = sorted(novos_datetimes_ida_encontrados_utc)
        first_dt_tz = sorted_novos_datetimes_ida_encontrados_utc[0].date()
        last_dt_tz = sorted_novos_datetimes_ida_encontrados_utc[-1].date()
        rotinas = Rotina.objects.filter(rota_id=self.pk, datetime_ida__date__range=[first_dt_tz, last_dt_tz])
        # inativa rotinas dentro do mesmo range que não foram encontradas no novo batch
        for r in rotinas:
            if r.datetime_ida in novos_datetimes_ida_encontrados_utc:
                r.ativo = True
            else:
                r.ativo = False
            r.updated_at = to_default_tz(datetime.now())
        Rotina.objects.bulk_update(rotinas, ["ativo", "updated_at"])

    def to_dict_json(self):
        d = {
            "id": self.id,
            "ativo": self.ativo,
            "itinerario": [c.to_dict_json() for c in self.get_itinerario()],
        }
        return d

    def __str__(self):
        return f"{self.company.name} - {self.id_internal}"


class Rotina(AutoFieldAbstract):
    rota_id: int
    rota = models.ForeignKey(Rota, on_delete=models.CASCADE)
    datetime_ida = models.DateTimeField()
    ativo = models.BooleanField(default=True)
    id_external = models.CharField(blank=True, db_index=True, max_length=50, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ("rota", "datetime_ida")
        unique_together = (
            "rota",
            "datetime_ida",
        )


class Checkpoint(AutoFieldAbstract):
    objects = SerializableManager(
        select_related=["local__cidade", "local__cidade__cidade_internal"],
    )
    rota_id: int
    rota = models.ForeignKey(Rota, related_name="itinerario", on_delete=models.CASCADE)
    local_id: int
    local = models.ForeignKey(LocalEmbarque, null=True, blank=True, on_delete=models.SET_NULL)
    idx = models.IntegerField()
    internal_id = models.IntegerField(db_index=True, null=True, blank=True)
    departure = models.DateTimeField(null=True, blank=True)
    arrival = models.DateTimeField(null=True, blank=True)
    distancia_km = models.DecimalField(null=True, blank=True, decimal_places=2, max_digits=12, default=Decimal(0))
    duracao = models.DurationField(null=True, blank=True)
    tempo_embarque = models.DurationField(default=timedelta(minutes=1))
    id_external = models.CharField(max_length=30, null=True, blank=True)
    nickname = models.CharField(max_length=100, null=True, blank=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    uf = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Integrações com checkpoints"
        verbose_name = "Integração com checkpoint"
        unique_together = (
            "rota",
            "idx",
        )
        ordering = (
            "rota",
            "idx",
        )

    def get_timezone_or_fallback(self, timezone_fallback="America/Sao_Paulo"):
        timezone_aux = None
        if self.local and self.local.cidade:
            if self.local.cidade.cidade_internal:
                timezone_aux = self.local.cidade.cidade_internal.timezone
            else:
                timezone_aux = self.local.cidade.timezone

        return timezone_aux or timezone_fallback

    def __str__(self):
        return f"{self.nickname} - {self.idx=}"

    def to_dict_json(self, local=True):
        d = {
            "id": self.id,
            "idx": self.idx,
            "local_id": self.local and self.local.local_embarque_internal_id,
            "cidade_id": self.local and self.local.cidade.cidade_internal_id,
            "distancia_km": self.distancia_km,
            "arrival": self.arrival,
            "departure": self.departure,
            "duracao": timedelta_to_milliseconds(self.duracao) or 0,
            "tempo_embarque": timedelta_to_milliseconds(self.tempo_embarque),
        }
        if local:
            if self.local:
                d["local"] = self.local.to_dict_json()
                # caso local nao tenha uf, o cidade_internal nao está setado, entao pega do checkpoint
                if not d["local"]["uf"]:
                    d["local"]["uf"] = self.uf
                    d["local"]["name"] = self.name
            else:
                d["local"] = {
                    "name": self.name,
                    "uf": self.uf,
                    "nickname": self.nickname,
                    "id_external": self.id_external,
                }
        return d


class TipoAssento(models.Model):
    class TipoAssentoBuser(models.TextChoices):
        CONVENCIONAL = "convencional"
        EXECUTIVO = "executivo"
        SEMI_LEITO = "semi leito"
        LEITO = "leito"
        LEITO_CAMA = "leito cama"
        CAMA_PREMIUM = "cama premium"
        LEITO_INDIVIDUAL = "leito individual"
        LEITO_CAMA_INDIVIDUAL = "leito cama individual"
        CAMA_PREMIUM_INDIVIDUAL = "cama premium individual"

    id: int
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    tipo_assento_parceiro = models.CharField(max_length=50)
    tipo_assento_buser_preferencial = models.CharField(max_length=50, null=True, choices=TipoAssentoBuser.choices)
    tipos_assentos_buser_match = ArrayField(
        models.CharField(max_length=50, choices=TipoAssentoBuser.choices), null=True, blank=True
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = [
            ("company", "tipo_assento_parceiro"),
        ]


class TrechoVendido(AutoFieldAbstract):
    class StatusPreco(models.TextChoices):
        PENDENTE = "pendente"
        OK = "ok"
        NAO_ENCONTRADO = "nao_encontrado"
        IMPOSSIVEL_BUSCAR = "impossivel_buscar"

    EXPIRATION_TIME = timedelta(days=5)
    objects = SerializableManager(
        select_related=["tipo_assento"],
    )
    id_internal = models.IntegerField(db_index=True, null=True, blank=True)
    rota_id: int
    rota = models.ForeignKey(Rota, on_delete=models.CASCADE)
    origem_id: int
    origem = models.ForeignKey(LocalEmbarque, related_name="+", null=True, on_delete=models.SET_NULL)
    destino_id: int
    destino = models.ForeignKey(LocalEmbarque, related_name="+", null=True, on_delete=models.SET_NULL)
    classe = models.CharField(max_length=50, blank=True, null=True)
    capacidade_classe = models.IntegerField(blank=True, null=True)
    distancia = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    duracao = models.DurationField(null=True, blank=True)
    preco = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    rotinas = models.ManyToManyField(Rotina, through="RotinaTrechoVendido")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    ativo = models.BooleanField(default=True)
    tipo_assento = models.ForeignKey(TipoAssento, null=True, on_delete=models.SET_NULL)
    status_preco = models.TextField(choices=StatusPreco.choices, default=StatusPreco.OK)

    # Campo para saber a qual datetime_ida o preco se refere
    datetimeida_preco = models.DateTimeField(null=True)

    @property
    def classe_buser(self):
        from rodoviaria.service.class_match_svc import get_buser_class_by_trecho_vendido

        return get_buser_class_by_trecho_vendido(self)

    def to_dict_json(self):
        return {
            "id": self.id,
            "id_internal": self.id_internal,
            "origem": self.origem.nickname,
            "destino": self.destino.nickname,
            "origem_id": self.origem.local_embarque_internal_id,
            "destino_id": self.destino.local_embarque_internal_id,
            "distancia": self.distancia,
            "classe_api": self.classe if self.classe is not None else "",
            "classe_buser": self.classe_buser if self.classe is not None else "",
            "duracao": str(self.duracao),
            "preco": self.preco,
            "ativo": self.ativo,
        }


class RotinaTrechoVendido(AutoFieldAbstract):
    datetime_ida_trecho_vendido = models.DateTimeField()
    rotina_id: int
    rotina = models.ForeignKey(Rotina, on_delete=models.CASCADE)
    trecho_vendido_id: int
    trecho_vendido = models.ForeignKey(TrechoVendido, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = [
            ("rotina", "trecho_vendido"),
        ]


class TaskStatus(AutoFieldAbstract):
    class Name(models.TextChoices):
        FETCH_TRECHOS_VENDIDOS = "fetch_trechos_vendidos"
        FETCH_ROTINAS = "fetch_rotinas"
        DESCOBRIR_ROTAS = "descobrir_rotas"
        DESCOBRIR_OPERACAO = "descobrir_operacao"
        FETCH_DATA_LIMITE = "fetch_data_limite"

    class Status(models.TextChoices):
        NOT_STARTED = "NOT_STARTED"
        PENDING = "PENDING"
        SUCCESS = "SUCCESS"
        FAILURE = "FAILURE"

    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    rota_id: int
    rota = models.ForeignKey(Rota, null=True, blank=True, on_delete=models.CASCADE)
    task_name = models.CharField(max_length=40, choices=Name.choices)
    task_id = models.CharField(max_length=50, null=True, blank=True)
    status = models.CharField(max_length=20, choices=Status.choices)
    last_success_at = models.DateTimeField(null=True, blank=True)
    last_finished_at = models.DateTimeField(null=True, blank=True)

    # log de alteração
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Task status"
        unique_together = [
            ("company_id", "rota_id", "task_name"),
        ]


class RodoviariaUser(AutoFieldAbstract):
    user_internal_id = models.IntegerField(db_index=True)


class Grupo(AutoFieldAbstract):
    grupo_internal_id = models.IntegerField(db_index=True, null=True, blank=True)
    company_integracao_id: int
    company_integracao = models.ForeignKey(
        Company, null=True, blank=True, on_delete=models.SET_NULL
    )  # usando esse modelo para caso alguma empresa use mais de uma integração
    datetime_ida = models.DateTimeField()
    rota_id: int
    rota = models.ForeignKey(Rota, null=True, blank=True, on_delete=models.SET_NULL)
    linha = models.TextField(null=True, blank=True)
    external_datetime_ida = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    changed_by_id: int
    changed_by = models.ForeignKey(RodoviariaUser, null=True, blank=True, on_delete=models.CASCADE)
    history = HistoricalRecords(user_model=RodoviariaUser)

    class Meta:
        verbose_name_plural = "Integrações com grupos"
        verbose_name = "Integração com grupo"
        indexes = [
            models.Index(fields=["-datetime_ida"], name="grupo_datetime_ida_idx"),
        ]

    def __str__(self):
        return f"{self.grupo_internal_id} - {self.company_integracao}"

    @property
    def _history_user(self):
        return self.changed_by

    @_history_user.setter
    def _history_user(self, value):
        self.changed_by = value


class GrupoClasse(models.Model):
    id: int
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    grupoclasse_internal_id = models.IntegerField(db_index=True, null=True, blank=True)
    tipo_assento_internal = models.CharField(max_length=64, blank=True, null=True)
    tipo_assento_external = models.CharField(max_length=64, blank=True, null=True)


class TaggedTrechoClasse(TaggedItemBase):
    id: int
    content_object = models.ForeignKey("TrechoClasse", on_delete=models.CASCADE, db_index=True)

    class Meta:
        unique_together = [
            ("content_object", "tag"),
        ]


class TrechoClasse(AutoFieldAbstract):
    objects = SerializableManager()
    tags = TaggableManager(through=TaggedTrechoClasse, blank=True)

    trechoclasse_internal_id = models.IntegerField(db_index=True, null=True, blank=True)
    provider_data = models.TextField(null=True, blank=True)
    grupo_id: int
    grupo = models.ForeignKey(Grupo, null=False, blank=False, on_delete=models.CASCADE)
    grupo_classe_id: int
    grupo_classe = models.ForeignKey(GrupoClasse, null=True, blank=True, on_delete=models.CASCADE)
    datetime_ida = models.DateTimeField(null=True)
    origem_id: int
    origem = models.ForeignKey(LocalEmbarque, related_name="+", null=True, on_delete=models.SET_NULL)
    destino_id: int
    destino = models.ForeignKey(LocalEmbarque, related_name="+", null=True, on_delete=models.SET_NULL)
    external_id = models.CharField(max_length=50, null=True, blank=True, db_index=True)
    external_datetime_ida = models.DateTimeField(null=True, blank=True)
    preco_rodoviaria = models.DecimalField(max_digits=6, decimal_places=2, null=True)
    vagas = models.IntegerField(null=True, blank=True)
    external_id_tipo_veiculo = models.CharField(max_length=10, null=True, blank=True)
    desconto = models.DecimalField(max_digits=6, decimal_places=2, null=True)
    external_id_desconto = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    active = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = "Integrações com trechoclasses"
        verbose_name = "Integração com trechoclasse"
        constraints = [
            models.UniqueConstraint(
                fields=["trechoclasse_internal_id"],
                condition=Q(active=True),
                name="internal_id_active_unique",
            )
        ]

    @property
    def conexao(self) -> dict | None:
        provider_data = json.loads(self.provider_data) if self.provider_data else {}
        return provider_data.get("conexao")

    @property
    def is_conexao(self) -> bool:
        provider_data = json.loads(self.provider_data) if self.provider_data else {}
        return bool(provider_data.get("conexao") or provider_data.get("conexoes"))

    def __str__(self):
        return f"{self.trechoclasse_internal_id} - {self.grupo}"

    def tags_set(self):
        return set(TaggedTrechoClasse.objects.filter(content_object_id=self.id).values_list("tag__name", flat=True))


class TaggedPassagem(TaggedItemBase):
    id: int
    content_object = models.ForeignKey("Passagem", on_delete=models.CASCADE)


class Passagem(AutoFieldAbstract):
    class CategoriaEspecial(models.TextChoices):
        IDOSO_100 = "idoso_100", r"Idoso 100%"
        IDOSO_50 = "idoso_50", r"Idoso 50%"
        JOVEM_100 = "jovem_100", r"Jovem 100%"
        JOVEM_50 = "jovem_50", r"Jovem 50%"
        CRIANCA = "crianca", "Criança (sem poltrona extra)"
        PCD = "pcd", "PCD"
        ESPACO_MULHER = "espaco_mulher", "Espaço Mulher"
        ESPACO_PET = "espaco_pet", "Espaço Pet"
        NORMAL = "normal", "Normal"
        IGNORADO = "ignorado", "Ignorado"

    class Status(models.TextChoices):
        CONFIRMADA = "confirmada"
        CANCELADA = "cancelada"
        INCOMPLETA = "incompleta"
        ERRO = "erro"
        DEVOLVIDA = "devolvida"  # usuário faz a troca no guichê por outra

    class TipoTaxaEmbarque(models.TextChoices):
        QRCODE = "qr_code"
        BARCODE = "bar_code"

    STATUS_CONFIRMADA_LIST = (Status.CONFIRMADA, Status.DEVOLVIDA)

    objects = SerializableManager()
    tags = TaggableManager(through=TaggedPassagem, blank=True)

    trechoclasse_integracao_id: int
    trechoclasse_integracao = models.ForeignKey(TrechoClasse, on_delete=models.CASCADE)
    company_integracao_id: int
    company_integracao = models.ForeignKey(
        Company, null=True, blank=True, on_delete=models.SET_NULL
    )  # desacoplando passagem de grupo
    buseiro_internal_id = models.IntegerField(db_index=True)
    poltrona_external_id = models.IntegerField(null=True, blank=True)
    travel_internal_id = models.IntegerField(null=True, blank=True)
    localizador = models.CharField(null=True, blank=True, max_length=100)
    pedido_external_id = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=Status.choices, null=True, blank=True)
    valor_cheio = models.DecimalField(null=True, decimal_places=2, max_digits=12)
    bpe_qrcode = models.URLField(max_length=500, null=True, blank=True)
    bpe_monitriip_code = models.TextField(blank=True, null=True)
    bpe_em_contingencia = models.BooleanField(default=False, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    erro = models.TextField(null=True, blank=True)
    erro_cancelamento = models.TextField(null=True, blank=True)
    datetime_cancelamento = models.DateTimeField(null=True, blank=True)
    numero_passagem = models.CharField(null=True, blank=True, max_length=30)
    id_estabelecimento_external = models.CharField(null=True, blank=True, max_length=30)
    multa = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)
    plataforma = models.CharField(null=True, blank=True, max_length=25)
    provider_data = models.JSONField(null=True, blank=True)
    # taxas
    preco_base = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)
    taxa_embarque = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)
    seguro = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)
    pedagio = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)
    outras_taxas = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)
    outros_tributos = models.TextField(null=True, blank=True)
    preco_rodoviaria = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)
    preco_poltrona = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)
    desconto = models.DecimalField(null=True, max_digits=7, decimal_places=2, blank=True)

    # dados bpe
    chave_bpe = models.CharField(max_length=44, null=True, blank=True)
    numero_bpe = models.CharField(max_length=10, null=True, blank=True)
    serie_bpe = models.CharField(max_length=4, null=True, blank=True)
    protocolo_autorizacao = models.CharField(max_length=18, null=True, blank=True)
    numero_bilhete = models.CharField(max_length=16, null=True, blank=True)
    data_autorizacao = models.DateTimeField(null=True, blank=True)
    prefixo = models.CharField(max_length=24, null=True, blank=True)
    linha = models.TextField(null=True, blank=True)
    cnpj = models.CharField(max_length=18, null=True)
    inscricao_estadual = models.CharField(max_length=20, null=True, blank=True)
    endereco_empresa = models.TextField(null=True, blank=True)
    nome_agencia = models.CharField(max_length=100, null=True, blank=True)
    tipo_emissao = models.CharField(max_length=100, null=True, blank=True)

    # w2i taxa de embarque
    tipo_taxa_embarque = models.CharField(max_length=20, choices=TipoTaxaEmbarque.choices, null=True, blank=True)
    codigo_taxa_embarque = models.CharField(max_length=100, null=True, blank=True)
    numero_bilhete_embarque = models.CharField(max_length=20, null=True, blank=True)
    embarque_eletronico = models.TextField(null=True, blank=True)

    # conexão
    origem = models.CharField(max_length=100, null=True, blank=True)
    destino = models.CharField(max_length=100, null=True, blank=True)
    # TODO: tech-debt -> Mudar o campo data_hora_partida para um DateTimeField
    data_hora_partida = models.CharField(max_length=50, null=True, blank=True)

    history = HistoricalRecords()
    categoria_especial = models.TextField(
        choices=CategoriaEspecial.choices, null=True, blank=True, default=CategoriaEspecial.NORMAL
    )

    class Meta:
        indexes = [
            models.Index(fields=["travel_internal_id"]),
        ]

    def to_dict_json(self):
        return {
            "id": self.id,
            "localizador": self.localizador,
            "bpe": self.bpe_qrcode,
            "monitriip": self.bpe_monitriip_code,
            "buseiro_id": self.buseiro_internal_id,
            "travel_id": self.travel_internal_id,
            "poltrona": self.poltrona_external_id,
            "categoria_especial": self.categoria_especial,
            "status": self.status,
            "datetime_ida": self.trechoclasse_integracao.grupo.datetime_ida.strftime("%Y-%m-%d"),
        }

    def __str__(self):
        return f"{self.travel_internal_id} - {self.trechoclasse_integracao}"

    def save_canceled(self):
        self.status = self.Status.CANCELADA
        self.datetime_cancelamento = timezone.now()
        self.tags.remove("cancelamento_pendente")
        self.save()

    def save_returned(self):
        self.status = self.Status.DEVOLVIDA
        self.datetime_cancelamento = timezone.now()
        self.tags.remove("cancelamento_pendente")
        self.save()

    def save_confirmed(self):
        self.status = self.Status.CONFIRMADA
        self.erro = None
        self.save()

    async def asave_confirmed(self):
        self.status = self.Status.CONFIRMADA
        self.erro = None
        await self.asave()

    def save_error(self, message):
        self.status = self.Status.ERRO
        self.erro = message
        self.save()

    async def asave_error(self, message):
        self.status = self.Status.ERRO
        self.erro = message
        await self.asave()

    def save_error_cancelamento(self, message):
        self.datetime_cancelamento = timezone.now()
        self.erro_cancelamento = message
        self.save()

    def tags_set(self):
        return set(TaggedPassagem.objects.filter(content_object_id=self.id).values_list("tag__name", flat=True))

    @staticmethod
    def get_desconto(categoria: CategoriaEspecial) -> int:
        desconto_por_categoria = {
            Passagem.CategoriaEspecial.PCD: 100,
            Passagem.CategoriaEspecial.IDOSO_100: 100,
            Passagem.CategoriaEspecial.JOVEM_100: 100,
            Passagem.CategoriaEspecial.CRIANCA: 100,
            Passagem.CategoriaEspecial.IDOSO_50: 50,
            Passagem.CategoriaEspecial.JOVEM_50: 50,
        }
        return desconto_por_categoria.get(categoria, 0)


class Remanejamento(AutoFieldAbstract):
    class Status(models.TextChoices):
        PENDENTE = "pendente"
        COMPLETO = "completo"
        ERRO = "erro"

    class Etapas(models.TextChoices):
        EMISSAO = "emissao"
        CANCELAMENTO = "cancelamento"

    travel_destino_internal_id = models.IntegerField(null=True, blank=True)
    travel_origem_internal_id = models.IntegerField(null=True, blank=True)
    grupo_destino_internal_id = models.IntegerField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.PENDENTE)
    etapa_erro = models.CharField(max_length=20, choices=Etapas.choices, null=True, blank=True)
    count_seats = models.IntegerField(null=True, blank=True)
    erro = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)


class TaggedTrechoClasseError(TaggedItemBase):
    id: int
    content_object_id: int
    content_object = models.ForeignKey("TrechoClasseError", on_delete=models.CASCADE)


class TrechoClasseError(AutoFieldAbstract):
    class Motivo(models.TextChoices):
        MISMATCH_DE_CLASSE = "CLASS_MISMATCH"
        MISMATCH_DE_HORARIO = "DEPARTURE_MISMATCH"
        SEM_SERVICO = "NO_SERVICE"

    company_id: int
    company = models.ForeignKey(Company, null=True, on_delete=models.CASCADE)
    trechoclasse_internal_id = models.IntegerField(db_index=True, unique=True)
    tipo_assento = models.CharField(max_length=50)
    datetime_ida = models.DateTimeField(null=True)
    origem_id: int
    origem = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE, related_name="+")
    destino_id: int
    destino = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE, related_name="+")
    servicos_proximos = models.JSONField()
    tags = TaggableManager(through=TaggedTrechoClasseError, blank=True)
    servicos_proximos_parseados = models.JSONField(null=True, blank=True)
    motivo = models.CharField(max_length=64, choices=Motivo.choices, null=True, blank=True)

    # log de alteração
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def tags_set(self):
        return set(
            TaggedTrechoClasseError.objects.filter(content_object_id=self.id).values_list("tag__name", flat=True)
        )

    @property
    def motivo_fechamento(self):
        motivo_fechamento = f"[{self.motivo}]"
        if self.servicos_proximos_parseados:
            motivo_fechamento += f" {sorted(self.servicos_proximos_parseados, key=lambda k: k[0] )}"
        return motivo_fechamento


class Motorista(models.Model):
    id: int
    # relações
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    internal_id = models.IntegerField()
    external_id_usuario = models.IntegerField(null=True, blank=True)
    external_id_usuario_empresa = models.IntegerField(null=True, blank=True)
    external_id_motorista = models.IntegerField(null=True, blank=True)

    # dados básicos
    nome = models.TextField(blank=True)
    email = models.EmailField(blank=True)
    telefone = models.CharField(max_length=16, blank=True)
    cpf = models.CharField(max_length=16, blank=True)

    # documentos
    cnh_numero = models.CharField(max_length=11, blank=True)
    cnh_validade = models.DateField(null=True, blank=True)
    cnh_categoria = models.CharField(max_length=2, blank=True)
    cnh_orgao_emissor = models.TextField(blank=True)
    cnh_uf = models.CharField(max_length=2, blank=True)
    antt_numero = models.CharField(max_length=32, blank=True)
    antt_validade = models.DateField(null=True, blank=True)

    # log de alteração
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = [
            ("company", "internal_id"),
        ]


class ConexaoRota(models.Model):
    id: int
    internal_grupo_id = models.IntegerField(db_index=True)
    internal_rota_id = models.IntegerField(db_index=True)
    external_rota_id_inicial = models.IntegerField(db_index=True)
    external_rota_id_final = models.IntegerField(db_index=True)
    local_corte_id: int
    local_corte = models.ForeignKey(LocalEmbarque, null=True, on_delete=models.SET_NULL)


class ConexaoTrechoClasse(models.Model):
    id: int
    conexao_rota_id: int
    conexao_rota = models.ForeignKey(ConexaoRota, on_delete=models.CASCADE)
    trechoclasse_internal_id = models.IntegerField(db_index=True)
    trechoclasse_internal_inicial_id = models.IntegerField()
    trechoclasse_internal_final_id = models.IntegerField()


class CronogramaAtualizacaoOperacao(models.Model):
    class Tipos(models.TextChoices):
        FETCH_TRECHOS_VENDIDOS = "fetch_trechos_vendidos"
        DESCOBRIR_ROTAS = "descobrir_rotas"
        DESCOBRIR_OPERACAO = "descobrir_operacao"
        FETCH_DATA_LIMITE = "fetch_data_limite"
        INTEGRACAO_AUTOMATICA = "integracao_automatica"

    id: int
    tipo_atualizacao = models.TextField(choices=Tipos.choices)
    dia_semana = models.PositiveIntegerField(choices=WeekDay.choices)
    horario = models.TimeField()
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

    history = HistoricalRecords()

    class Meta:
        unique_together = [
            ("company", "tipo_atualizacao", "dia_semana"),
        ]


class RotinaAtualizacaoTrecho(models.Model):
    class TipoAtualizacao(models.TextChoices):
        INTERVALO_FIXO = "intervalo_fixo"
        INTERVALO_DINAMICO = "intervalo_dinamico"

    tipo_atualizacao = models.TextField(choices=TipoAtualizacao.choices, null=True, db_index=True)
    cidade_origem = models.ForeignKey(CidadeInternal, related_name="+", on_delete=models.CASCADE)
    cidade_destino = models.ForeignKey(CidadeInternal, related_name="+", on_delete=models.CASCADE)
    intervalo_execucao_minutos = models.PositiveIntegerField()
    ultima_execucao = models.DateTimeField(auto_now_add=True)
    dias_busca_intervalo_dinamico = models.PositiveIntegerField(null=True, blank=True)
    margem_inicio_intervalo_dinamico = models.PositiveIntegerField(null=True, blank=True)
    data_inicio_intervalo_fixo = models.DateField(null=True, blank=True)
    data_fim_intervalo_fixo = models.DateField(null=True, blank=True)
    ativo = models.BooleanField(default=True)

    @property
    def is_intervalo_fixo(self):
        return self.tipo_atualizacao == self.TipoAtualizacao.INTERVALO_FIXO

    @property
    def is_intervalo_dinamico(self):
        return self.tipo_atualizacao == self.TipoAtualizacao.INTERVALO_DINAMICO


class ViagemAPILogger(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    origem = models.ForeignKey(LocalEmbarque, related_name="+", null=True, on_delete=models.SET_NULL)
    destino = models.ForeignKey(LocalEmbarque, related_name="+", null=True, on_delete=models.SET_NULL)
    datetime_ida = models.DateTimeField()
    tipo_assento_parceiro = models.TextField()
    preco = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    vagas = models.PositiveIntegerField(null=True)
    id_external = models.CharField(max_length=50, null=True, blank=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    rotina_atualizacao = models.ForeignKey(RotinaAtualizacaoTrecho, null=True, blank=True, on_delete=models.SET_NULL)

    def to_dict_json(self):
        return {
            "company_rodoviaria_id": self.company_id,
            "origem_rodoviaria_id": self.origem_id,
            "destino_rodoviaria_id": self.destino_id,
            "datetime_ida": self.datetime_ida,
            "tipo_assento_parceiro": self.tipo_assento_parceiro,
            "preco": self.preco,
            "id_external": self.id_external,
        }


class CompanyCategoriaEspecial(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    categoria_id_external = models.CharField(max_length=50, null=True, blank=True, db_index=True)
    descricao_external = models.CharField()
    categoria_especial = models.TextField(choices=Passagem.CategoriaEspecial.choices, null=True, db_index=True)

    class Meta:
        unique_together = [
            ("company", "categoria_id_external"),
        ]

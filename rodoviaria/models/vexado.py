from functools import cached_property

from django.db import models
from django_cryptography.fields import encrypt
from django_qserializer import SerializableManager

from rodoviaria.api.vexado import models as forms
from rodoviaria.models import Rota
from rodoviaria.models.core import AutoFieldAbstract, Company, GrupoClasse


class VexadoLogin(AutoFieldAbstract):
    id: int
    DEFAULT_URL_BASE = "https://api.vexado.com.br"

    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    user = models.CharField(max_length=100)
    password = encrypt(models.CharField(max_length=100))
    site = models.CharField(max_length=100, default="adminVexado")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = SerializableManager()

    def __str__(self):
        return self.company.__str__()

    def to_dict_json(self):
        return {"user": self.user, "password": self.password}

    @property
    def cliente(self) -> str:
        return str(self.user)


class MapaVeiculo(AutoFieldAbstract):
    id: int
    id_external = models.IntegerField(db_index=True, null=True, blank=True)
    has_dois_andares = models.BooleanField(default=False)
    quantidade_poltronas_primeiro_andar = models.IntegerField(null=True, blank=True)
    quantidade_poltronas_segundo_andar = models.IntegerField(null=True, blank=True)
    provider_data = models.TextField(null=True, blank=True)

    def __str__(self):
        if self.quantidade_poltronas_segundo_andar:
            return (
                f"{self.quantidade_poltronas_primeiro_andar} Inferior /"
                f" {self.quantidade_poltronas_segundo_andar} Superior"
            )
        return f"{self.quantidade_poltronas_primeiro_andar} Poltronas"


class Veiculo(models.Model):
    id: int
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    id_external = models.IntegerField(db_index=True, blank=True, null=True)
    id_internal = models.IntegerField(db_index=True, blank=True, null=True)
    descricao = models.CharField(max_length=255, blank=True)
    mapa_veiculo_id: int
    mapa_veiculo = models.ForeignKey(MapaVeiculo, null=True, blank=True, on_delete=models.CASCADE)

    def to_dict_json(self):
        return {
            "company_internal_id": self.company.company_internal_id,
            "id_external": self.id_external,
            "id_internal": self.id_internal,
            "descricao": self.descricao,
            "mapa_veiculo": str(self.mapa_veiculo),
        }


class VexadoRota(models.Model):
    id: int
    rota_internal_id = models.IntegerField(db_index=True, null=True, blank=True)
    rota_external_id = models.IntegerField(db_index=True, null=True, blank=True)
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)


class VexadoGrupoClasse(models.Model):
    class Status(models.TextChoices):
        INCOMPLETO = "incomplete"
        PENDENTE = "pending"
        CRIADO = "created"
        FECHADO = "closed"
        CANCELADO = "canceled"
        CANCELADO_DUPLO_CHECK = "canceled_double_check"
        CANCELADO_CHECK_ERRO = "canceled_check_error"

    id: int
    grupo_classe_id: int
    grupo_classe = models.ForeignKey(GrupoClasse, on_delete=models.CASCADE)
    veiculo_id: int
    veiculo = models.ForeignKey(Veiculo, on_delete=models.SET_NULL, null=True, blank=True)
    andar = models.IntegerField(default=1)
    rota_external_id = models.IntegerField(null=True, blank=True)
    vexado_rota_id: int
    vexado_rota = models.ForeignKey(VexadoRota, on_delete=models.SET_NULL, null=True, blank=True)
    status = models.CharField(
        max_length=25,
        choices=Status.choices,
        null=True,
        blank=True,
        default=Status.INCOMPLETO,
    )
    grupo_classe_external_id = models.IntegerField(null=True, blank=True)


class RotaVexado(Rota):
    id: int

    class Meta:
        proxy = True

    @cached_property
    def parsed_data(self):
        return forms.Itinerario.parse_raw(self.provider_data)

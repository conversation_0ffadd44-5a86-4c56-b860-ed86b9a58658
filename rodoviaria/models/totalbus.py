from functools import cached_property

from django.db import models
from django_cryptography.fields import encrypt
from django_qserializer import SerializableManager

from rodoviaria.api.totalbus import models as forms
from rodoviaria.models import Rota
from rodoviaria.models.core import AutoFieldAbstract, Company


class TotalbusLogin(AutoFieldAbstract):
    id: int
    DEFAULT_URL_BASE = "http://totalbus.buser.vpc/api-gateway"
    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    user = models.CharField(max_length=100)
    password = encrypt(models.CharField(max_length=100))
    tenant_id = models.CharField(max_length=50, null=True, blank=True)
    id_forma_pagamento = models.IntegerField(null=True, blank=True)
    forma_pagamento = models.CharField(null=True, blank=True, max_length=40)
    validar_multa = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = SerializableManager()

    def to_dict_json(self):
        return {
            "user": self.user,
            "password": self.password,
            "tenant_id": self.tenant_id,
            "id_forma_pagamento": self.id_forma_pagamento,
            "forma_pagamento": self.forma_pagamento,
            "validar_multa": self.validar_multa,
        }

    @property
    def cliente(self) -> str:
        return str(self.tenant_id)


class RotaTotalbus(Rota):
    id: int

    class Meta:
        proxy = True

    @cached_property
    def parsed_data(self):
        return forms.Itinerario.parse_raw(self.provider_data)

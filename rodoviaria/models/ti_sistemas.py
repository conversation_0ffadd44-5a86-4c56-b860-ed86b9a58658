import json
from functools import cached_property

from django.db import models
from django_cryptography.fields import encrypt
from django_qserializer import SerializableManager

from rodoviaria.api.ti_sistemas import models as forms
from rodoviaria.models.core import AutoFieldAbstract, Company, Rota


class TiSistemasLogin(AutoFieldAbstract):
    id: int
    DEFAULT_URL_BASE = "https://api.passagensbr.com.br"

    company_id: int
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    auth_key = encrypt(models.CharField(max_length=200))

    objects = SerializableManager()

    def to_dict_json(self):
        return {"auth_key": self.auth_key}

    @property
    def cliente(self) -> str:
        return str(self.company.name)

    @classmethod
    def get_active_auth_key(cls):
        try:
            return cls.objects.filter(company__features__len__gt=0).first().auth_key
        except AttributeError:
            return None


class RotaTiSistemas(Rota):
    id: int

    class Meta:
        proxy = True

    @cached_property
    def parsed_data(self):
        return forms.Itinerario.parse_obj(json.loads(self.provider_data))

from django_qserializer import BaseSerializer

from rodoviaria.models.core import LocalEmbarque


class LinksLocaisSerializer(BaseSerializer):
    select_related = ["cidade"]

    def serialize_object(self, obj: LocalEmbarque) -> dict:
        cidade = obj.cidade
        return {
            "id": obj.id,
            "empresa_id": cidade.company.company_internal_id,
            "empresa_rodoviaria_id": cidade.company_id,
            "cidade_external": f"{cidade.name} #{cidade.id_external}",
            "localidade_external": f"{obj.nickname} #{obj.id_external}",
            "local_embarque_buser": obj.local_embarque_internal_id,
            "buser_cidade_id": cidade.cidade_internal_id,
        }

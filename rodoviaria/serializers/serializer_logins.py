from django_qserializer import BaseSerializer


class TotalbusSerializer(BaseSerializer):
    select_related = ["company"]

    def serialize_object(self, obj):
        company = obj.company

        d = obj.to_dict_json()
        d["company_external_id"] = company.company_external_id
        return d


class PraxioSerializer(BaseSerializer):
    def serialize_object(self, obj):
        return obj.to_dict_json()


class VexadoSerializer(BaseSerializer):
    select_related = ["company"]

    def serialize_object(self, obj):
        company = obj.company

        d = obj.to_dict_json()
        d["company_external_id"] = company.company_external_id
        return d


class GuichepassSerializer(BaseSerializer):
    select_related = ["company"]

    def serialize_object(self, obj):
        company = obj.company

        d = obj.to_dict_json()
        d["company_external_id"] = company.company_external_id
        d["url_base"] = company.url_base
        return d


class EulabsSerializer(BaseSerializer):
    def serialize_object(self, obj):
        return obj.to_dict_json()


class SmartbusSerializer(BaseSerializer):
    def serialize_object(self, obj):
        return obj.to_dict_json()


class TiSistemasSerializer(BaseSerializer):
    select_related = ["company"]

    def serialize_object(self, obj):
        company = obj.company

        d = obj.to_dict_json()
        d["company_external_id"] = company.company_external_id
        return d

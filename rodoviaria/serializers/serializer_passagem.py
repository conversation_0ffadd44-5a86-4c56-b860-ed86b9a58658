from django_qserializer import BaseSerializer

from commons.dateutils import to_tz
from rodoviaria.models.core import Passagem


class PassagemBasicSerializer(BaseSerializer):
    def serialize_object(self, obj: Passagem) -> dict:
        return {
            "id": obj.id,
            "poltrona": obj.poltrona_external_id,
            "localizador": obj.localizador.split("-")[0] if obj.localizador else "",
            "numero_passagem": obj.numero_passagem,
            "buseiro_id": obj.buseiro_internal_id,
            "travel_id": obj.travel_internal_id,
            "status": obj.status,
            "origem": obj.origem,
            "destino": obj.destino,
        }


class PassagemSerializer(BaseSerializer):
    select_related = [
        "trechoclasse_integracao__grupo__company_integracao__integracao",
        "trechoclasse_integracao__origem__cidade",
        "company_integracao",
    ]

    prefetch_related = [
        "taggedpassagem_set__tag",
    ]

    def serialize_object(self, passagem: Passagem) -> dict:
        if passagem.company_integracao:
            company_name = passagem.company_integracao.name
            modelo_venda = passagem.company_integracao.modelo_venda
        else:
            company_name = passagem.trechoclasse_integracao.grupo.company_integracao.name
            modelo_venda = passagem.trechoclasse_integracao.grupo.company_integracao.modelo_venda
        sistema = passagem.trechoclasse_integracao.grupo.company_integracao.integracao.name
        tags = passagem.taggedpassagem_set.all()
        tags = [t.tag.name for t in tags]
        return {
            "passagem_id": passagem.id,
            "travel_internal_id": passagem.travel_internal_id,
            "buseiro_internal_id": passagem.buseiro_internal_id,
            "bpe": passagem.bpe_qrcode,
            "company_name": company_name,
            "modelo_venda": modelo_venda,
            "sistema": sistema,
            "tags": tags,
            "status": passagem.status,
            "trecho_datetime_ida": to_tz(
                passagem.trechoclasse_integracao.datetime_ida,
                passagem.trechoclasse_integracao.origem.cidade.timezone or "America/Sao_Paulo",
            ),
            "trechoclasse_internal_id": passagem.trechoclasse_integracao.trechoclasse_internal_id,
            "grupo_internal_id": passagem.trechoclasse_integracao.grupo.grupo_internal_id,
        }

from django_qserializer import BaseSerializer

from rodoviaria.models.core import TrechoClasse


class TrechoClasseSerializer(BaseSerializer):
    select_related = [
        "grupo",
        "grupo__company_integracao",
        "origem",
        "origem__cidade",
        "destino",
        "destino__cidade",
    ]

    def serialize_object(self, obj: TrechoClasse) -> dict:
        return {
            "id": obj.id,
            "company_name": obj.grupo and obj.grupo.company_integracao.name,
            "integracao": obj.grupo and obj.grupo.company_integracao.integracao.name,
            "company_internal_id": obj.grupo and obj.grupo.company_integracao.company_internal_id,
            "trechoclasse_internal_id": obj.trechoclasse_internal_id,
            "tags": list(obj.tags_set()),
            "datetime_ida": obj.datetime_ida and obj.datetime_ida.strftime("%Y-%m-%d %H:%M %Z"),
            "grupo_internal_id": obj.grupo.grupo_internal_id,
            "grupo_datetime_ida": (
                obj.grupo and obj.grupo.datetime_ida and obj.grupo.datetime_ida.strftime("%Y-%m-%d %H:%M %Z")
            ),
            "origem_internal_id": obj.origem and obj.origem.local_embarque_internal_id,
            "origem_nickname": obj.origem and obj.origem.nickname,
            "origem_cidade": obj.origem and obj.origem.cidade.name,
            "destino_internal_id": obj.destino and obj.destino.local_embarque_internal_id,
            "destino_nickname": obj.destino and obj.destino.nickname,
            "destino_cidade": obj.destino and obj.destino.cidade.name,
            "preco_rodoviaria": obj.preco_rodoviaria,
            "vagas": obj.vagas,
            "active": obj.active,
        }

from django_qserializer import BaseSerializer


class CompanySerializer(BaseSerializer):
    select_related = ["integracao"]

    def serialize_object(self, obj):
        return {
            "id": obj.id,
            "company_internal_id": obj.company_internal_id,
            "company_external_id": obj.company_external_id,
            "modelo_venda": obj.modelo_venda,
            "name": obj.name,
            "integracao": {"name": obj.integracao.name, "id": obj.integracao_id},
            "url_base": obj.url_base,
            "active": "active" in obj.features if obj.features else False,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at,
            "features": obj.features,
            "max_percentual_divergencia": obj.max_percentual_divergencia,
        }

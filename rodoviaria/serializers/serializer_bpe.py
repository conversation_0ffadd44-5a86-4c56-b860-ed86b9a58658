from django_qserializer import BaseSerializer

from commons.dateutils import to_default_tz
from rodoviaria.models.core import Company, Passagem
from rodoviaria.service.exceptions import RodoviariaBpeException


class DadosBpeSerializer(BaseSerializer):
    def serialize_object(self, travel_id, passagens_cadastradas=None):
        if not passagens_cadastradas:
            passagens_cadastradas = Passagem.objects.filter(
                travel_internal_id=travel_id, status=Passagem.Status.CONFIRMADA
            ).select_related(
                "trechoclasse_integracao__grupo__company_integracao__integracao",
                "company_integracao",
            )

        primeira_passagem = passagens_cadastradas and passagens_cadastradas[0]
        if not primeira_passagem:
            raise RodoviariaBpeException
        passagens = []
        for passagem in passagens_cadastradas:
            modelo_venda = (
                passagem.company_integracao.modelo_venda
                if passagem.company_integracao
                else passagem.trechoclasse_integracao.grupo.company_integracao.modelo_venda
            )
            data = None
            hora = None
            if passagem.data_hora_partida:
                data = passagem.data_hora_partida[:10]
                hora = passagem.data_hora_partida[-5:]
            passagens.append(
                {
                    "tarifa": passagem.preco_base,
                    "pedagio": passagem.pedagio,
                    "taxa_de_embarque": passagem.taxa_embarque,
                    "tipo_taxa_embarque": passagem.tipo_taxa_embarque,
                    "codigo_taxa_embarque": passagem.codigo_taxa_embarque,
                    "numero_bilhete_embarque": passagem.numero_bilhete_embarque,
                    "seguro": passagem.seguro,
                    "outras_taxas": passagem.outras_taxas,
                    "valor_total": passagem.preco_rodoviaria,
                    "desconto": passagem.desconto,
                    "valor_pgto": passagem.valor_cheio,
                    "valor_pago": passagem.valor_cheio,
                    "troco": "0.00",
                    "bpe_qrcode": passagem.bpe_qrcode,
                    "monitriip": passagem.bpe_monitriip_code,
                    "poltrona": passagem.poltrona_external_id,
                    "plataforma": passagem.plataforma,
                    "cnpj": passagem.cnpj,
                    "bpe": {
                        "chave": passagem.chave_bpe,
                        "numero": passagem.numero_bpe,
                        "serie": passagem.serie_bpe,
                        "tipo": "Normal",
                        "protocolo_autorizacao": passagem.protocolo_autorizacao,
                        "data_autorizacao": (
                            to_default_tz(passagem.data_autorizacao).strftime("%d/%m/%Y %H:%M:%S")
                            if passagem.data_autorizacao
                            else "-"
                        ),
                    },
                    "numero_bilhete": passagem.numero_bilhete,
                    "localizador": passagem.localizador,
                    "travel_id": passagem.travel_internal_id,
                    "buseiro_id": passagem.buseiro_internal_id,
                    "pedido_external_id": passagem.pedido_external_id if self.hibrido_e_vexado(passagem) else None,
                    "nome_agencia": passagem.nome_agencia,
                    "endereco_empresa": passagem.endereco_empresa,
                    "inscricao_estadual": passagem.inscricao_estadual,
                    "tipo_emissao": passagem.tipo_emissao,
                    "linha": passagem.linha if passagem.linha else passagem.trechoclasse_integracao.grupo.linha,
                    "prefixo": passagem.prefixo,
                    "origem": passagem.origem,
                    "destino": passagem.destino,
                    "data": data,
                    "hora": hora,
                    "servico": passagem.trechoclasse_integracao.external_id,
                    "outros_tributos": passagem.outros_tributos,
                    "data_emissao": to_default_tz(passagem.created_at).strftime("%d/%m/%Y %H:%M"),
                    "modelo_venda": modelo_venda,
                    "bpe_em_contingencia": passagem.bpe_em_contingencia,
                    "passagem_id": passagem.id,
                    "embarque_eletronico": passagem.embarque_eletronico,
                }
            )
        response = {
            "linha": primeira_passagem.trechoclasse_integracao.grupo.linha,
            "prefixo": primeira_passagem.prefixo,
            "servico": primeira_passagem.trechoclasse_integracao.external_id,
            "passagens": passagens,
            "outros_tributos": primeira_passagem.outros_tributos,
        }
        return response

    def hibrido_e_vexado(self, passagem):
        company = passagem.trechoclasse_integracao.grupo.company_integracao
        return company.modelo_venda == Company.ModeloVenda.HIBRIDO and company.integracao.name == "vexado"

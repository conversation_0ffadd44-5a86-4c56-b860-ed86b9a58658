import logging

from constance.signals import config_updated
from django.apps import AppConfig

from commons.serialization_utils import setup_json_encoder

logger = logging.getLogger(__name__)


class RodoviariaConfig(AppConfig):
    name = "rodoviaria"

    def ready(self):
        setup_json_encoder()
        config_updated.connect(log_config_changes)


def log_config_changes(sender, **kwargs):
    key = kwargs.get("key")
    old_value = kwargs.get("old_value")
    new_value = kwargs.get("new_value")

    logger.info("constance_config_changed: key=%s old_value=%s new_value=%s", key, old_value, new_value)

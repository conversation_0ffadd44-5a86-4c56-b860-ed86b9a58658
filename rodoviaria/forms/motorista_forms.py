from datetime import date
from enum import Enum
from typing import Optional

from pydantic import BaseModel, EmailStr, constr

from rodoviaria.forms.types import CPF, DigitsOnlyStr, Phone


class NumeroCNH(DigitsOnlyStr):
    max_length = 11


class CategoriaCNH(str, Enum):
    A = "A"
    AB = "AB"
    AC = "AC"
    AD = "AD"
    AE = "AE"
    B = "B"
    C = "C"
    D = "D"
    E = "E"


class UF(str, Enum):
    AC = "AC"
    AL = "AL"
    AM = "AM"
    AP = "AP"
    BA = "BA"
    CE = "CE"
    DF = "DF"
    ES = "ES"
    GO = "GO"
    MA = "MA"
    MG = "MG"
    MS = "MS"
    MT = "MT"
    PA = "PA"
    PB = "PB"
    PE = "PE"
    PI = "PI"
    PR = "PR"
    RJ = "RJ"
    RN = "RN"
    RO = "RO"
    RR = "RR"
    RS = "RS"
    SC = "SC"
    SE = "SE"
    SP = "SP"
    TO = "TO"


class CNH(BaseModel):
    numero: NumeroCNH
    validade: date
    categoria: CategoriaCNH
    orgao_emissor: constr(max_length=256)
    uf: UF


class NumeroRegistroANTT(DigitsOnlyStr):
    max_length = 32


class RegistroANTT(BaseModel):
    numero: NumeroRegistroANTT
    validade: date


class Motorista(BaseModel):
    user_id: int
    nome: constr(max_length=256)
    email: EmailStr
    telefone: Phone
    cpf: CPF
    cnh: Optional[CNH]
    registro_antt: Optional[RegistroANTT]

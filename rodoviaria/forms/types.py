class DigitsOnlyStr(str):
    min_length = None
    max_length = None

    @classmethod
    def __get_validators__(cls):
        yield cls.digits_only
        yield cls.length_validator

    @classmethod
    def digits_only(cls, value):
        return "".join(c for c in value if c.isdigit())

    @classmethod
    def length_validator(cls, value):
        length = len(value)

        min_length = cls.min_length
        if min_length is not None and length < min_length:
            raise ValueError(f"certifique-se de que este valor tenha pelo menos {min_length} dígitos")

        max_length = cls.max_length
        if max_length is not None and length > max_length:
            raise ValueError(f"certifique-se de que este valor tenha no máximo {max_length} dígitos")

        return value


class Phone(DigitsOnlyStr):
    min_length = 10
    max_length = 13


class CPF(DigitsOnlyStr):
    min_length = 11
    max_length = 11

from decimal import Decimal
from typing import Optional

from pydantic import BaseModel

from rodoviaria.models.core import Passagem, TipoAssento


class Assento(BaseModel):
    livre: bool
    x: int
    y: int
    numero: int
    tipo_assento: TipoAssento.TipoAssentoBuser
    preco: Optional[Decimal] = None
    categoria_especial: Optional[Passagem.CategoriaEspecial] = Passagem.CategoriaEspecial.NORMAL


class Deck(BaseModel):
    andar: int
    assentos: list[Assento]


class Onibus(BaseModel):
    layout: list[Deck]

from datetime import date, datetime
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel

from rodoviaria.api.guichepass.models import InfosCacheaveisBloqueioPoltrona as GuichepassInfoCache
from rodoviaria.api.totalbus.models import InfosCacheaveisBloqueioPoltrona as TotalbusInfoCache
from rodoviaria.models.core import Passagem


class DefaultForm(BaseModel):
    trechoclasse_id: int
    travel_id: int | None
    force_renew_link: bool | None


class VerificarPoltronaForm(DefaultForm):
    passageiros: int = 1
    categoria_especial: Passagem.CategoriaEspecial | None = None


class DadosBeneficioForm(BaseModel):
    data_expedicao: Optional[date]
    data_expiracao: Optional[date]
    numero_beneficio: Optional[str]
    renda: Optional[int]
    tipo_passe_livre: Optional[str]
    auxilio_embarque: Optional[bool]


class PassageiroForm(BaseModel):
    id: int
    name: str
    cpf: Optional[str]
    rg_number: str
    rg_orgao: Optional[str]
    tipo_documento: Optional[str]
    phone: Optional[str]
    birthday: Optional[date]
    dados_beneficio: Optional[DadosBeneficioForm]
    buyer_cpf: Optional[str] = ""


class ComprarForm(DefaultForm):
    valor_cheio: Decimal
    poltronas: list[int]
    passageiros: list[PassageiroForm]
    categoria_especial: Passagem.CategoriaEspecial = Passagem.CategoriaEspecial.NORMAL
    extra_poltronas: Optional[list[TotalbusInfoCache] | str | GuichepassInfoCache | dict] = None


class BloquearPoltronasForm(DefaultForm):
    poltronas: list[int]


class DesbloquearPoltronasForm(BloquearPoltronasForm): ...


class BloquearPoltronasResponse(BaseModel):
    seat: int
    best_before: datetime
    external_payload: list[TotalbusInfoCache] | str | dict | GuichepassInfoCache | None


class BloquearPoltronasFormV2(DefaultForm):
    trechoclasse_id: int
    poltrona: int
    categoria_especial: Passagem.CategoriaEspecial = Passagem.CategoriaEspecial.NORMAL

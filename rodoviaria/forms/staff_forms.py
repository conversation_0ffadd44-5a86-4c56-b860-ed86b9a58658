from decimal import Decimal
from typing import List, Optional, Set, Union

from ninja import Schema
from pydantic import BaseModel, Field, validator

from commons.utils import only_numbers, strip_punctuation
from rodoviaria.api.smartbus.models import SmartbusLoginForm
from rodoviaria.forms.compra_rodoviaria_forms import DefaultForm
from rodoviaria.models.core import TipoAssento


class PaginatorConfigForm(BaseModel):
    rows_per_page: Optional[int] = Field(default=10, alias="rowsPerPage")
    page: Optional[int] = 1
    sort_by: Optional[Union[str, List[str]]] = Field(alias="sortBy")
    descending: Optional[bool]

    @validator("sort_by")
    def default_sort_by(cls, v):
        if v is None:
            return "pk"
        return v

    @validator("descending")
    def default_descending(cls, v):
        if v is None:
            return False
        return v

    @property
    def order_by(self):
        fields = self.sort_by

        if isinstance(fields, str):
            fields = [fields]

        if self.descending:
            return [f"-{field}" for field in fields]

        return fields

    class Config:
        allow_population_by_field_name = True


class PaginableModel(BaseModel):
    LIST_ALL_ITEMS = -1
    paginator: Optional[PaginatorConfigForm] = None

    @property
    def has_pagination(self):
        return self.paginator and self.paginator.rows_per_page != self.LIST_ALL_ITEMS

    @property
    def order_by(self):
        if not self.paginator:
            return None

        return self.paginator.order_by


class ListLinksLocaisForm(PaginableModel):
    class Config:
        anystr_strip_whitespace = True

    search: Optional[str] = None
    empresa_id_filter: Optional[int] = 0
    associado_rota: Optional[bool] = None
    linkado_buser: Optional[bool] = None


class PaxForm(BaseModel):
    id: Optional[int]
    buseiro_id: Optional[int]
    name: str
    cpf: Optional[str]
    rg_number: str
    phone: Optional[str]
    tipo_documento: Optional[str]
    buyer_cpf: Optional[str] = ""

    @validator("cpf", "buyer_cpf")
    def parse_cpf(cls, cpf):
        return only_numbers(cpf) or ""

    @validator("phone")
    def parse_phone(cls, phone):
        return phone or ""


class CheckPaxForm(DefaultForm):
    valor_por_buseiro: float
    passenger: PaxForm
    id_origem: int
    id_destino: int
    poltrona: Optional[int]
    grupo_id: Optional[int]


class TravelForm(BaseModel):
    travel_id: int
    valor_por_buseiro: Decimal
    buseiros: List[PaxForm]


class CheckPaxBatchForm(BaseModel):
    passageiros: List[CheckPaxForm]


class CheckPaxMultipleForm(BaseModel):
    trechoclasse_id: int
    valor_por_buseiro: float
    id_origem: int
    id_destino: int
    travels: List[TravelForm]
    async_add: Optional[bool] = False

    @property
    def quantidade_passageiros(self):
        count = 0
        for travel in self.travels:
            count += len(travel.buseiros)
        return count

    @property
    def travels_ids(self):
        return {travel.travel_id for travel in self.travels}

    @property
    def buseiros_ids(self):
        ids_set = set()
        for travel in self.travels:
            for buseiro in travel.buseiros:
                ids_set.add(buseiro.id)
        return ids_set


class RemanejaPassageiroForm(BaseModel):
    travel_id: int
    travel_destino_id: int
    reservation_code: str
    travel_max_split_value: Decimal
    trechoclasse_origem_id: int
    trechoclasse_destino_id: int
    grupo_destino_id: Optional[int]
    company_origem_id: Optional[int] = None
    modelo_venda_origem: str
    company_destino_id: Optional[int]
    modelo_venda_destino: Optional[str]
    passengers: List[PaxForm]
    poltronas_destino: Optional[list]
    remanejamento_id: Optional[int] = None


class TravelGetPoltronas(BaseModel):
    travel_id: int
    numero_passageiros: int


class ItinerariosNaoIntegradosForm(PaginableModel):
    class Config:
        anystr_strip_whitespace = True

    search: Optional[str] = None
    integradas: Optional[str] = "todas"
    ativas: Optional[str] = "ativas"

    @property
    def search_term_list(self):
        if self.search:
            return list({term.upper() for term in self.search.split(" ") if term})

    @property
    def is_search_by_id(self):
        if self.search_term_list:
            return all(term.isdigit() for term in self.search_term_list)


class TotalbusNoCompanyLogin(BaseModel):
    user: str
    password: str
    tenant_id: str

    @property
    def integracao(self):
        return "totalbus"

    @property
    def cliente(self):
        return None


class IntegracaoUndefinedCompany(BaseModel):
    name: str


class TotalbusUndefinedCompany(BaseModel):
    id: None = None
    modelo_venda: str = "undefined"
    url_base: str = "http://totalbus.buser.vpc/api-gateway"
    integracao: IntegracaoUndefinedCompany = IntegracaoUndefinedCompany(name="totalbus")


class TotalbusUndefinedCompanyClient(TotalbusNoCompanyLogin):
    company: TotalbusUndefinedCompany = TotalbusUndefinedCompany()
    company_id: int = None


class PraxioUndefinedCompany(BaseModel):
    id: None = None
    modelo_venda: str = "undefined"
    url_base: str = "https://oci-parceiros2.praxioluna.com.br/Autumn"
    integracao: IntegracaoUndefinedCompany = IntegracaoUndefinedCompany(name="praxio")


class PraxioUndefinedCompanyClient:
    company: PraxioUndefinedCompany = PraxioUndefinedCompany()
    company_id: int = None

    @property
    def cliente(self):
        return None


class TotalbusLoginForm(TotalbusNoCompanyLogin):
    company_external_id: None | int = None
    id_forma_pagamento: None | int = None
    forma_pagamento: None | str = None
    validar_multa: bool = True


class PraxioLoginForm(Schema):
    name: str
    password: str
    cliente: str
    desconto_manual: Optional[bool] = True

    @property
    def integracao(self):
        return "praxio"


class VexadoAnonymousLogin(Schema):
    modelo_venda: str

    @property
    def integracao(self):
        return "vexado"


class VexadoLoginForm(Schema):
    company_external_id: int

    @property
    def integracao(self):
        return "vexado"


class GuichepassUndefinedCompany(BaseModel):
    id: None = None
    modelo_venda: str = "undefined"
    url_base: str
    integracao: str = "guichepass"


class GuichepassAnonymousLogin(Schema):
    url_base: str
    username: str
    password: str
    client_id: str

    @property
    def integracao(self):
        return "guichepass"


class GuichepassLoginForm(GuichepassAnonymousLogin):
    company_external_id: int


class GuichepassUndefinedCompanyLogin(GuichepassAnonymousLogin):
    company: GuichepassUndefinedCompany

    @property
    def cliente(self):
        return None

    @property
    def company_id(self):
        return self.company.id


class TiSistemasLoginForm(Schema):
    auth_key: str
    company_external_id: int

    @property
    def integracao(self):
        return "ti_sistemas"


LOGIN_FORMS = {
    "totalbus": TotalbusLoginForm,
    "praxio": PraxioLoginForm,
    "vexado": VexadoLoginForm,
    "guichepass": GuichepassLoginForm,
    "smartbus": SmartbusLoginForm,
    "ti_sistemas": TiSistemasLoginForm,
}


LOGIN_FORMS_COM_FORMAS_PAGAMENTO = {
    "totalbus": TotalbusLoginForm,
    "smartbus": SmartbusLoginForm,
}


class FetchFormasPagamentoForm(Schema):
    integracao: str
    login: Optional[dict]

    @validator("login", always=True)
    def validate_login(cls, login, values):
        integracao = values.get("integracao")
        is_integracao_com_formas_pagamento = integracao in LOGIN_FORMS_COM_FORMAS_PAGAMENTO
        if not login or not is_integracao_com_formas_pagamento:
            raise ValueError("Integracao sem formas de pagamento")
        return LOGIN_FORMS[integracao].parse_obj(login)


class CreateRodoviariaCompanyForm(Schema):
    name: str
    company_internal_id: int
    modelo_venda: str
    features: Set[str]
    integracao: str
    login: Optional[dict]
    max_percentual_divergencia: Optional[int] = None

    @validator("integracao", always=True)
    def validate_integracao(cls, integracao):
        if isinstance(integracao, str):
            return strip_punctuation(integracao).replace(
                " ",
                "_",
            )
        return integracao

    @validator("login", always=True)
    def validate_login(cls, login, values):
        integracao = values.get("integracao")
        is_integracao_conhecida = integracao in LOGIN_FORMS
        if not login or not is_integracao_conhecida:
            return {}
        return LOGIN_FORMS[integracao].parse_obj(login)


class CompanyForm(Schema):
    company_internal_id: int
    modelo_venda: str


class RodoviariaListarTiposAssentosParams(PaginableModel):
    search_tipo_assento: str = None
    search_company: str = None
    search_not_linked_only: bool = False
    search_active_companies_only: bool = True


class RodoviariaLinkarTiposAssentosParams(Schema):
    id_assento: str = None
    tipo_assento_buser_preferencial: TipoAssento.TipoAssentoBuser = None
    tipos_assentos_buser_match: list[TipoAssento.TipoAssentoBuser] = None

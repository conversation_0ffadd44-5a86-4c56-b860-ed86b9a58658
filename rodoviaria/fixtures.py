from datetime import datetime, timed<PERSON>ta
from decimal import Decimal as D
from types import SimpleNamespace

from django.utils import timezone
from model_bakery import baker

from commons.dateutils import to_default_tz
from core import models_company, models_grupo, models_rota


def buser_cidades():
    cidade_goiania = baker.make(
        models_rota.Cidade,
        name="Goiânia",
        sigla="GYN",
        uf="GO",
        timezone="America/Sao_Paulo",
        city_code_ibge="5208707",
        uf_code_ibge="52",
        slug="goiania-go",
    )
    cidade_inhumas = baker.make(
        models_rota.Cidade,
        name="Inhumas",
        sigla="INH",
        uf="GO",
        timezone="America/Sao_Paulo",
        city_code_ibge="5210000",
        uf_code_ibge="52",
        slug="inhumas-go",
    )
    cidade_recife = baker.make(
        models_rota.Cidade,
        name="Recife",
        sigla="RCF",
        uf="PE",
        timezone="America/Sao_Paulo",
        city_code_ibge="2611606",
        uf_code_ibge="26",
        slug="recife-pe",
    )
    cidade_adamantina = baker.make(
        models_rota.Cidade,
        name="Adamantina",
        sigla="ADM",
        uf="SP",
        timezone="America/Sao_Paulo",
        city_code_ibge="2611",
        uf_code_ibge="52",
        slug="adamantina-sp",
    )
    cidade_uberlandia = models_rota.Cidade.objects.filter(slug="uberlandia-mg").first()
    cidade_aurora = baker.make(
        models_rota.Cidade,
        name="Aurora",
        sigla="AUO",
        uf="CE",
        timezone="America/Fortaleza",
        city_code_ibge="2301703",
        uf_code_ibge="23",
        slug="aurora-ce",
    )

    return SimpleNamespace(
        cidade_goiania=cidade_goiania,
        cidade_inhumas=cidade_inhumas,
        cidade_uberlandia=cidade_uberlandia,
        cidade_recife=cidade_recife,
        cidade_adamantina=cidade_adamantina,
        cidade_aurora=cidade_aurora,
    )


def buser_locais(
    cidade_goiania,
    cidade_inhumas,
    cidade_uberlandia,
    cidade_recife,
    cidade_adamantina,
    cidade_aurora,
):
    local_go_terminal = baker.make(
        models_rota.LocalEmbarque,
        cidade=cidade_goiania,
        nickname="Terminal Rodoviário de Goiânia",
        nickname_slug="terminal-rodoviario-de-goiania",
        endereco_bairro="St. Central",
        bairro_slug="st-central",
        endereco_logradouro="Rua 44",
        ativo=True,
    )
    local_inh_terminal = baker.make(
        models_rota.LocalEmbarque,
        cidade=cidade_inhumas,
        nickname="Terminal Rodoviário de Inhumas",
        nickname_slug="terminal-rodoviario-de-inhumas",
        endereco_bairro="Centro",
        bairro_slug="centro",
        endereco_logradouro="Av. Bernardo Sayão",
        ativo=True,
    )
    local_rcf_terminal = baker.make(
        models_rota.LocalEmbarque,
        cidade=cidade_recife,
        nickname="Terminal Rodoviário de Recife",
        nickname_slug="terminal-rodoviario-de-recife",
        endereco_bairro="Varzea",
        bairro_slug="varzea",
        endereco_logradouro="Av. Pref. Antônio Pereira",
        ativo=True,
    )
    local_adm_terminal = baker.make(
        models_rota.LocalEmbarque,
        cidade=cidade_adamantina,
        nickname="Terminal Rodoviário de Adamantina",
        nickname_slug="terminal-rodoviario-de-adamantina",
        ativo=True,
    )
    local_aurora_terminal = baker.make(
        models_rota.LocalEmbarque,
        cidade=cidade_aurora,
        nickname="Terminal Rodoviário de Aurora",
        nickname_slug="terminal-rodoviario-de-aurora",
        endereco="R. Sebastião Alves Pereira 307-327",
        endereco_bairro="Centro",
        endereco_slug="av-bernardo-sayao",
        bairro_slug="centro",
        endereco_logradouro="R. Sebastião Alves Pereira 307-327",
        latitude=-6.9423407,
        longitude=-38.9675232,
        ativo=True,
    )
    local_ub_uberpoint = models_rota.LocalEmbarque.objects.get(cidade=cidade_uberlandia)

    return SimpleNamespace(
        local_go_terminal=local_go_terminal,
        local_inh_terminal=local_inh_terminal,
        local_ub_uberpoint=local_ub_uberpoint,
        local_rcf_terminal=local_rcf_terminal,
        local_adm_terminal=local_adm_terminal,
        local_aurora_terminal=local_aurora_terminal,
    )


def make_buser_rota(origem, destino, preco_rodoviaria):
    rota = baker.make(models_rota.Rota, ativo=True, origem=origem, destino=destino)
    if preco_rodoviaria:
        trechovendido = baker.make(
            models_rota.TrechoVendido,
            rota=rota,
            origem=origem,
            destino=destino,
            preco_rodoviaria=preco_rodoviaria,
        )
    else:
        trechovendido = baker.make(models_rota.TrechoVendido, rota=rota, origem=origem, destino=destino)
    rota.refresh_duracao_distancia()
    return SimpleNamespace(rota=rota, trechovendido=trechovendido)


def buser_rota_ida(origem, destino, preco_rodoviaria=None):
    return make_buser_rota(origem, destino, preco_rodoviaria)


def buser_rota_volta(origem, destino, preco_rodoviaria=None):
    return make_buser_rota(destino, origem, preco_rodoviaria)


def buser_company(cidade, integracao):
    company_expresso = models_company.Company.objects.create(name=f"{integracao} Transporte")
    company_expresso.has_accepted_contract = False
    company_expresso.cnpj = "05263312000101"
    company_expresso.vinculo = "marketplace"
    company_expresso.inscricao_estadual = "*********"
    company_expresso.razao_social = f"{integracao} TRANSPORTE TURISMO LTDA"
    company_expresso.nome_fantasia = f"{integracao}"
    company_expresso.endereco_logradouro = "R 137"
    company_expresso.endereco_numero = "556"
    company_expresso.endereco_bairro = "Setor Marista"
    company_expresso.endereco_cep = "74170120"
    company_expresso.phone = "6234340520"
    company_expresso.taf = "000000001234"
    company_expresso.cidade = cidade
    company_expresso.rating = 4.5
    company_expresso.emissao_nf_enabled = True
    company_expresso.has_sent_digital_certificate = True
    company_expresso.datetime_expiracao_certificado = to_default_tz(timezone.now() + timedelta(weeks=100))
    company_expresso.save()
    return company_expresso


def buser_company_cetro():
    company_cetro = models_company.Company.objects.create(name="Viação Cetro")
    company_cetro.has_accepted_contract = False
    company_cetro.cnpj = "03314223000111"
    company_cetro.vinculo = "marketplace"
    company_cetro.inscricao_estadual = "*********"
    company_cetro.razao_social = "Cetro Viação Transporte LTDA"
    company_cetro.nome_fantasia = "Cetro"
    company_cetro.endereco_cep = "41820790"
    company_cetro.rating = 4.5
    company_cetro.emissao_nf_enabled = True
    company_cetro.has_sent_digital_certificate = True
    company_cetro.datetime_expiracao_certificado = to_default_tz(timezone.now() + timedelta(weeks=100))
    company_cetro.save()
    return company_cetro


def _calc_max_split(preco_rodoviaria, tipo_assento):
    fator = D("0.65")
    return round(fator * preco_rodoviaria, 2)


def buser_grupo(rota, trechovendido, buser_company_expresso, datetime_ida):
    preco_rodoviaria = trechovendido.preco_rodoviaria
    tipo_assento = "leito"
    pessoas = 0
    duracao_ida = timedelta(hours=1)
    max_split_value = _calc_max_split(preco_rodoviaria, tipo_assento)
    grupo = baker.make(
        models_grupo.Grupo,
        rota=rota,
        datetime_ida=datetime_ida,
        status="travel_confirmed",
        confirming_probability="high",
        is_extra=False,
        modelo_venda=models_grupo.Grupo.ModeloVenda.MARKETPLACE,
        company=buser_company_expresso,
    )
    gc = baker.make(
        models_grupo.GrupoClasse,
        grupo=grupo,
        tipo_assento=tipo_assento,
        capacidade=24,
        pessoas=pessoas,
        closed=False,
    )
    tc = baker.make(
        models_grupo.TrechoClasse,
        grupo=grupo,
        datetime_ida=datetime_ida,
        grupo_classe=gc,
        trecho_vendido=trechovendido,
        preco_rodoviaria=preco_rodoviaria,
        max_split_value=max_split_value,
        ref_split_value=max_split_value,
        duracao_ida=duracao_ida,
        pessoas=pessoas,
    )
    return SimpleNamespace(grupo=grupo, gc=gc, tc=tc)


def populate_rodoviaria_database():
    cidades = buser_cidades()
    locais = buser_locais(
        cidades.cidade_goiania,
        cidades.cidade_inhumas,
        cidades.cidade_uberlandia,
        cidades.cidade_recife,
        cidades.cidade_adamantina,
        cidades.cidade_aurora,
    )
    _populate_rodoviaria_database_praxio(cidades, locais)
    _populate_rodoviaria_database_totalbus(cidades, locais)
    _populate_rodoviaria_database_guichepass(cidades, locais)


def _populate_rodoviaria_database_praxio(cidades, locais):
    rota_ida = buser_rota_ida(locais.local_go_terminal, locais.local_ub_uberpoint)
    rota_volta = buser_rota_volta(locais.local_go_terminal, locais.local_ub_uberpoint)
    company_expresso = buser_company(cidades.cidade_goiania, "Expresso")
    company_cetro = buser_company_cetro()
    grupo_ida = buser_grupo(
        rota_ida.rota,
        rota_ida.trechovendido,
        company_expresso,
        datetime_ida=to_default_tz(datetime(2021, 12, 1, 18, 00)),
    )
    grupo_volta = buser_grupo(
        rota_volta.rota,
        rota_volta.trechovendido,
        company_expresso,
        datetime_ida=to_default_tz(datetime(2021, 12, 2, 21, 00)),
    )

    # PRAXIO
    praxio_integracao = baker.make("rodoviaria.Integracao", name="praxio")
    praxio_company_expresso = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_expresso.id,
        integracao_id=praxio_integracao.id,
        url_base="http://loadbalancer-dotnet5-b26fdf826c843d6f.elb.us-east-1.amazonaws.com:8082/AUTUMN",
    )
    praxio_company_cetro = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_cetro.id,
        integracao_id=praxio_integracao.id,
        url_base="http://loadbalancer-dotnet5-b26fdf826c843d6f.elb.us-east-1.amazonaws.com:8082/AUTUMN",
    )

    # praxio expresso login
    baker.make(
        "rodoviaria.PraxioLogin",
        company=praxio_company_expresso,
        sistema="WINVR.EXE",
        name="buser.expresso",
        tipo_bd=0,
        empresa="AUTUMN",
        cliente="MOREIRA_VR",
        tipo_aplicacao=0,
        password="buser",  # noqa: S106
    )
    # praxio cetro login
    baker.make(
        "rodoviaria.PraxioLogin",
        company=praxio_company_cetro,
        sistema="WINVR.EXE",
        name="BUSER-BRASIL",
        tipo_bd=0,
        empresa="AUTUMN",
        cliente="VIACAOCETRO_VR",
        tipo_aplicacao=0,
        password="buser",  # noqa: S106
    )

    cidade_goiania = baker.make(
        "rodoviaria.Cidade",
        id_external="5208707",
        cidade_internal_id=cidades.cidade_goiania.id,
        company=praxio_company_expresso,
        name=cidades.cidade_goiania.name,
    )
    cidade_uberlandia = baker.make(
        "rodoviaria.Cidade",
        id_external="5210000",
        cidade_internal_id=cidades.cidade_uberlandia.id,
        company=praxio_company_expresso,
        name=cidades.cidade_uberlandia.name,
    )
    local_origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="2",
        cidade=cidade_goiania,
        local_embarque_internal_id=locais.local_go_terminal.id,
        nickname=locais.local_go_terminal.nickname,
    )
    local_destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="125",
        cidade=cidade_uberlandia,
        local_embarque_internal_id=locais.local_ub_uberpoint.id,
        nickname=locais.local_ub_uberpoint.nickname,
    )
    praxio_grupo_ida = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=grupo_ida.grupo.id,
        company_integracao=praxio_company_expresso,
        origem=local_origem,
        destino=local_destino,
        datetime_ida=grupo_ida.grupo.datetime_ida,
    )
    praxio_grupo_volta = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=grupo_volta.grupo.id,
        company_integracao=praxio_company_expresso,
        origem=local_destino,
        destino=local_origem,
        datetime_ida=grupo_volta.grupo.datetime_ida,
    )

    # praxio trechos classe
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=grupo_ida.tc.id,
        grupo=praxio_grupo_ida,
        external_id="16338",
        external_id_tipo_veiculo=2,
    )
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=grupo_volta.tc.id,
        grupo=praxio_grupo_volta,
        external_id="16150",
        external_id_tipo_veiculo=5,
    )


def _populate_rodoviaria_database_guichepass(cidades, locais):
    guiche_rota_ida = buser_rota_ida(
        locais.local_ub_uberpoint,
        locais.local_rcf_terminal,
        preco_rodoviaria=D("305.42"),
    )
    guiche_rota_volta = buser_rota_volta(
        locais.local_ub_uberpoint,
        locais.local_rcf_terminal,
        preco_rodoviaria=D("305.54"),
    )
    guiche_company_expresso = buser_company(cidades.cidade_goiania, "Expresso Virtual")
    guiche_grupo_ida = buser_grupo(
        guiche_rota_ida.rota,
        guiche_rota_ida.trechovendido,
        guiche_company_expresso,
        datetime_ida=to_default_tz(datetime(2021, 5, 1, 6, 00)),
    )
    guiche_grupo_volta = buser_grupo(
        guiche_rota_volta.rota,
        guiche_rota_volta.trechovendido,
        guiche_company_expresso,
        datetime_ida=to_default_tz(datetime(2021, 5, 2, 2, 00)),
    )

    guiche_integracao = baker.make("rodoviaria.Integracao", name="guichepass")
    guiche_company = baker.make(
        "rodoviaria.Company",
        company_internal_id=guiche_company_expresso.id,
        integracao_id=guiche_integracao.id,
        url_base="https://api-integration.guichepass.com.br",
        company_external_id=1,
    )

    # guichepass login
    baker.make(
        "rodoviaria.GuichepassLogin",
        company=guiche_company,
        username="buser",
        password="buser",  # noqa: S106
        client_id="WEB_SALE",
    )

    guiche_cidade_recife = baker.make(
        "rodoviaria.Cidade",
        id_external="42",
        cidade_internal_id=cidades.cidade_recife.id,
        company=guiche_company,
        name=cidades.cidade_recife.name,
    )
    guiche_cidade_uberlandia = baker.make(
        "rodoviaria.Cidade",
        id_external="121",
        cidade_internal_id=cidades.cidade_uberlandia.id,
        company=guiche_company,
        name=cidades.cidade_uberlandia.name,
    )
    guiche_local_destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="42",
        cidade=guiche_cidade_recife,
        local_embarque_internal_id=locais.local_rcf_terminal.id,
        nickname=locais.local_rcf_terminal.nickname,
    )
    guiche_local_origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="121",
        cidade=guiche_cidade_uberlandia,
        local_embarque_internal_id=locais.local_ub_uberpoint.id,
        nickname=locais.local_ub_uberpoint.nickname,
    )
    guiche_rodoviaria_grupo_ida = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=guiche_grupo_ida.grupo.id,
        company_integracao=guiche_company,
        origem=guiche_local_origem,
        destino=guiche_local_destino,
        datetime_ida=guiche_grupo_ida.grupo.datetime_ida,
    )
    guiche_rodoviaria_grupo_volta = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=guiche_grupo_volta.grupo.id,
        company_integracao=guiche_company,
        origem=guiche_local_destino,
        destino=guiche_local_origem,
        datetime_ida=guiche_grupo_volta.grupo.datetime_ida,
    )

    # guichepass trechos classe
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=guiche_grupo_ida.tc.id,
        grupo=guiche_rodoviaria_grupo_ida,
        external_id="870-1",
        preco_rodoviaria=guiche_grupo_ida.tc.preco_rodoviaria,
    )
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=guiche_grupo_volta.tc.id,
        grupo=guiche_rodoviaria_grupo_volta,
        external_id="841-1",
        preco_rodoviaria=guiche_grupo_volta.tc.preco_rodoviaria,
    )


def _populate_rodoviaria_database_totalbus(cidades, locais):
    totalbus_rota_ida = buser_rota_ida(locais.local_adm_terminal, locais.local_rcf_terminal)
    totalbus_rota_volta = buser_rota_volta(locais.local_adm_terminal, locais.local_rcf_terminal)
    totalbus_company_adamantina = buser_company(cidades.cidade_goiania, "totalbus")
    totalbus_company_adamantina = models_company.Company.objects.get(name="totalbus Transporte")
    totalbus_grupo_ida = buser_grupo(
        totalbus_rota_ida.rota,
        totalbus_rota_ida.trechovendido,
        totalbus_company_adamantina,
        datetime_ida=to_default_tz(datetime(2021, 5, 1, 6, 00)),
    )
    totalbus_grupo_volta = buser_grupo(
        totalbus_rota_volta.rota,
        totalbus_rota_volta.trechovendido,
        totalbus_company_adamantina,
        datetime_ida=to_default_tz(datetime(2021, 5, 2, 2, 00)),
    )

    totalbus_integracao = baker.make("rodoviaria.Integracao", name="totalbus")
    totalbus_company = baker.make(
        "rodoviaria.Company",
        company_internal_id=totalbus_company_adamantina.id,
        integracao_id=totalbus_integracao.id,
        url_base="https://api-integration.totalbus.com.br",
    )

    # totalbus login
    baker.make(
        "rodoviaria.TotalbusLogin",
        company=totalbus_company,
        user="buser",
        password="buser",  # noqa: S106
    )

    totalbus_cidade_recife = baker.make(
        "rodoviaria.Cidade",
        id_external="42",
        cidade_internal_id=cidades.cidade_recife.id,
        company=totalbus_company,
        name=cidades.cidade_recife.name,
    )
    totalbus_cidade_adamantina = baker.make(
        "rodoviaria.Cidade",
        id_external="121",
        cidade_internal_id=cidades.cidade_adamantina.id,
        company=totalbus_company,
        name=cidades.cidade_adamantina.name,
    )
    totalbus_local_destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="42",
        cidade=totalbus_cidade_recife,
        local_embarque_internal_id=locais.local_rcf_terminal.id,
        nickname=locais.local_rcf_terminal.nickname,
    )
    totalbus_local_origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="121",
        cidade=totalbus_cidade_adamantina,
        local_embarque_internal_id=locais.local_adm_terminal.id,
        nickname=locais.local_adm_terminal.nickname,
    )

    totalbus_rodoviaria_grupo_ida = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=totalbus_grupo_ida.grupo.id,
        company_integracao=totalbus_company,
        origem=totalbus_local_origem,
        destino=totalbus_local_destino,
        datetime_ida=totalbus_grupo_ida.grupo.datetime_ida,
        preco_rodoviaria=totalbus_grupo_ida.tc.preco_rodoviaria,
    )
    totalbus_rodoviaria_grupo_volta = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=totalbus_grupo_volta.grupo.id,
        company_integracao=totalbus_company,
        origem=totalbus_local_destino,
        destino=totalbus_local_origem,
        datetime_ida=totalbus_grupo_volta.grupo.datetime_ida,
        preco_rodoviaria=totalbus_grupo_volta.tc.preco_rodoviaria,
    )

    # totalbus trechos classe
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=totalbus_grupo_ida.tc.id,
        grupo=totalbus_rodoviaria_grupo_ida,
    )
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=totalbus_grupo_volta.tc.id,
        grupo=totalbus_rodoviaria_grupo_volta,
    )

# Generated by Django 4.0.10 on 2024-02-20 19:30

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rodoviaria", "0180_alter_rotinatrechovendido_unique_together"),
    ]

    operations = [
        migrations.CreateModel(
            name="TipoAssento",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("tipo_assento_parceiro", models.CharField(max_length=50)),
                (
                    "tipo_assento_buser_preferencial",
                    models.CharField(
                        choices=[
                            ("convencional", "Convencional"),
                            ("executivo", "Executivo"),
                            ("semi leito", "Semi Leito"),
                            ("leito", "Leito"),
                            ("leito cama", "Leito Cama"),
                            ("cama premium", "Cama Premium"),
                            ("leito individual", "Leito Individual"),
                            ("leito cama individual", "Leito Cama Individual"),
                            ("cama premium individual", "Cama Premium Individual"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "tipos_assentos_buser_match",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(
                            choices=[
                                ("convencional", "Convencional"),
                                ("executivo", "Executivo"),
                                ("semi leito", "Semi Leito"),
                                ("leito", "Leito"),
                                ("leito cama", "Leito Cama"),
                                ("cama premium", "Cama Premium"),
                                ("leito individual", "Leito Individual"),
                                ("leito cama individual", "Leito Cama Individual"),
                                ("cama premium individual", "Cama Premium Individual"),
                            ],
                            max_length=50,
                        ),
                        blank=True,
                        null=True,
                        size=None,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("company", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="rodoviaria.company")),
            ],
            options={
                "unique_together": {("company", "tipo_assento_parceiro")},
            },
        ),
        migrations.AddField(
            model_name="trechovendido",
            name="tipo_assento",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.SET_NULL, to="rodoviaria.tipoassento"
            ),
        ),
    ]

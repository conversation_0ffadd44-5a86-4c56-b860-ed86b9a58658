# Generated by Django 3.2.12 on 2023-05-10 16:37

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0164_servicos_possiveis"),
    ]

    operations = [
        migrations.RenameField(
            model_name="trechoclasseerror",
            old_name="servicos_possiveis",
            new_name="servicos_proximos_parseados",
        ),
        migrations.AddField(
            model_name="trechoclasseerror",
            name="motivo",
            field=models.CharField(
                blank=True,
                choices=[
                    ("CLASS_MISMATCH", "Mismatch De Classe"),
                    ("DEPARTURE_MISMATCH", "Mismatch De Horario"),
                    ("NO_SERVICO", "Sem Servico"),
                ],
                max_length=64,
                null=True,
            ),
        ),
    ]

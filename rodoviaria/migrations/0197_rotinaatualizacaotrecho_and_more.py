# Generated by Django 4.2.13 on 2024-08-28 21:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('rodoviaria', '0196_add_field_vagas_viagemapilogger'),
    ]

    operations = [
        migrations.CreateModel(
            name='RotinaAtualizacaoTrecho',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo_atualizacao', models.TextField(choices=[('intervalo_fixo', 'Intervalo Fixo'), ('intervalo_dinamico', 'Intervalo Dinamico')], db_index=True, null=True)),
                ('intervalo_execucao_minutos', models.PositiveIntegerField()),
                ('ultima_execucao', models.DateTimeField(auto_now_add=True)),
                ('dias_busca_intervalo_dinamico', models.PositiveIntegerField(blank=True, null=True)),
                ('margem_inicio_intervalo_dinamico', models.PositiveIntegerField(blank=True, null=True)),
                ('data_inicio_intervalo_fixo', models.DateField(blank=True, null=True)),
                ('data_fim_intervalo_fixo', models.DateField(blank=True, null=True)),
                ('ativo', models.BooleanField(default=True)),
                ('cidade_destino', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='+', to='rodoviaria.cidadeinternal')),
                ('cidade_origem', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='+', to='rodoviaria.cidadeinternal')),
            ],
        ),
        migrations.AddField(
            model_name='viagemapilogger',
            name='rotina_atualizacao',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='rodoviaria.rotinaatualizacaotrecho'),
        ),
    ]

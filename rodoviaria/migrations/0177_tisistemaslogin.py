# Generated by Django 3.2.12 on 2023-09-28 13:05

import django.db.models.deletion
import django_cryptography.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0176_grupo_datetime_ida_idx"),
    ]

    operations = [
        migrations.CreateModel(
            name="TiSistemasLogin",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("auth_key", django_cryptography.fields.encrypt(models.CharField(max_length=200))),
                ("company", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="rodoviaria.company")),
            ],
            options={
                "abstract": False,
            },
        ),
    ]

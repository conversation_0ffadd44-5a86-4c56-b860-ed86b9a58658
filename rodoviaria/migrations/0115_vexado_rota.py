# Generated by Django 3.2.12 on 2022-04-27 21:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0114_remove_passagem_totalbus_transacao"),
    ]

    operations = [
        migrations.CreateModel(
            name="VexadoRota",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rota_internal_id",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                (
                    "rota_external_id",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="vexadogrupoclasse",
            name="vexado_rota",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="rodoviaria.vexadorota",
            ),
        ),
    ]

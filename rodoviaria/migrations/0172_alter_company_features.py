# Generated by Django 3.2.12 on 2023-06-26 20:18

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0171_rotasmartbus"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="company",
            name="features",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("itinerario", "Itinerario"),
                        ("buscar_servico", "Buscar Servico"),
                        ("add_pax_staff", "Add Pax Staff"),
                        ("bpe", "Bpe"),
                        ("motorista", "Motorista"),
                        ("atualizar_preco", "Atualizar Preco"),
                        ("ignora_classe", "Ignora Classe"),
                        ("active", "Active"),
                        ("escalar_veiculos", "Escalar Veiculos"),
                        ("usar_preco_api", "Usar Preco Api"),
                        ("parent_company_no_bpe", "Parent Company No Bpe"),
                        ("fetch_trechos_further", "Fetch Trechos Further"),
                        ("atualizar_preco_checkout", "Atualizar Preco Checkout"),
                        ("auto_integra_operacao", "Auto Integra Operacao"),
                    ],
                    max_length=50,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
    ]

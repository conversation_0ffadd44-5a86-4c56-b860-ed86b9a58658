# Generated by Django 3.2.16 on 2022-10-04 11:40

from django.contrib.postgres.operations import AddIndexConcurrently
from django.db import migrations, models


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ("rodoviaria", "0144_auto_20220920_1201"),
    ]

    operations = [
        AddIndexConcurrently(
            model_name="passagem",
            index=models.Index(fields=["travel_internal_id"], name="rodoviaria__travel__2a4a8f_idx"),
        ),
    ]

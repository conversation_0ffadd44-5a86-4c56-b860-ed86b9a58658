# Generated by Django 4.0.10 on 2024-05-31 13:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('rodoviaria', '0186_cronogramaatualizacaooperacao'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalCronogramaAtualizacaoOperacao',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('tipo_atualizacao', models.TextField(choices=[('fetch_trechos_vendidos', 'Fetch Trechos Vendidos'), ('descobrir_rotas', 'Descobrir Rotas'), ('descobrir_operacao', 'Descobrir Operacao'), ('fetch_data_limite', 'Fetch Data Limite')])),
                ('dia_semana', models.PositiveIntegerField(choices=[(1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday'), (7, 'Sunday')])),
                ('horario', models.TimeField()),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('company', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='rodoviaria.company')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical cronograma atualizacao operacao',
                'verbose_name_plural': 'historical cronograma atualizacao operacaos',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]

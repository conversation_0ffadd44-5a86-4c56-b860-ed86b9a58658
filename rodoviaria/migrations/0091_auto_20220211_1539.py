# Generated by Django 3.2.6 on 2022-02-11 18:39

import datetime

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0090_merge_0085_checkpoint_0089_auto_20220211_0928"),
    ]

    operations = [
        migrations.AddField(
            model_name="checkpoint",
            name="arrival",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="checkpoint",
            name="departure",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="checkpoint",
            name="id_external",
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name="checkpoint",
            name="name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="checkpoint",
            name="nickname",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="checkpoint",
            name="uf",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="checkpoint",
            name="distancia_km",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AlterField(
            model_name="checkpoint",
            name="local",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="rodoviaria.localembarque",
            ),
        ),
        migrations.AlterField(
            model_name="checkpoint",
            name="tempo_embarque",
            field=models.DurationField(default=datetime.timedelta(seconds=60)),
        ),
    ]

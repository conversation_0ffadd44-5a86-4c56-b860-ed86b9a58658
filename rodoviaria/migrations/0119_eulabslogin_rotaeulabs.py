# Generated by Django 3.2.12 on 2022-05-30 01:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0118_rotina_ativo"),
    ]

    operations = [
        migrations.CreateModel(
            name="RotaEulabs",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("rodoviaria.rota",),
        ),
        migrations.CreateModel(
            name="EulabsLogin",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("api_id", models.CharField(blank=True, max_length=10, null=True)),
                ("api_key", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]

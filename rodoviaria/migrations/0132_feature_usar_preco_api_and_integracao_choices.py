# Generated by Django 3.2.12 on 2022-08-05 20:24

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0131_integracao_use_low_rate_limit"),
    ]

    operations = [
        migrations.AlterField(
            model_name="company",
            name="features",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("itinerario", "Itinerario"),
                        ("buscar_servico", "Buscar Servico"),
                        ("add_pax_staff", "Add Pax Staff"),
                        ("bpe", "Bpe"),
                        ("motorista", "Motorista"),
                        ("atualizar_preco", "Atualizar Preco"),
                        ("ignora_classe", "Ignora Classe"),
                        ("active", "Active"),
                        ("escalar_veiculos", "Escalar Veiculos"),
                        ("usar_preco_api", "Usar Preco Api"),
                    ],
                    max_length=50,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="integracao",
            name="name",
            field=models.CharField(
                blank=True,
                choices=[
                    ("totalbus", "Totalbus"),
                    ("praxio", "Praxio"),
                    ("vexado", "Vexado"),
                    ("guichepass", "Guiche"),
                    ("eulabs", "Eulabs"),
                ],
                max_length=50,
                null=True,
                unique=True,
            ),
        ),
    ]

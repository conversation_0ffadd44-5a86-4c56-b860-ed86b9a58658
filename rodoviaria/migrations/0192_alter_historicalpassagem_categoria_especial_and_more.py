# Generated by Django 4.2.13 on 2024-07-31 15:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('rodoviaria', '0191_historicalpassagem_categoria_especial_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalpassagem',
            name='categoria_especial',
            field=models.TextField(blank=True, choices=[('idoso_100', 'Idoso 100%'), ('idoso_50', 'Idoso 50%'), ('jovem_100', 'Jovem 100%'), ('jovem_50', 'Jovem 50%'), ('crianca', 'Criança (sem poltrona extra)'), ('pcd', 'PCD'), ('espaco_mulher', 'Espaço Mulher'), ('normal', 'Normal')], default='normal', null=True),
        ),
        migrations.AlterField(
            model_name='passagem',
            name='categoria_especial',
            field=models.TextField(blank=True, choices=[('idoso_100', 'Idoso 100%'), ('idoso_50', 'Idoso 50%'), ('jovem_100', 'Jovem 100%'), ('jovem_50', 'Jovem 50%'), ('crianca', 'Criança (sem poltrona extra)'), ('pcd', 'PCD'), ('espaco_mulher', 'Espaço Mulher'), ('normal', 'Normal')], default='normal', null=True),
        ),
        migrations.CreateModel(
            name='CompanyCategoriaEspecial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('categoria_id_external', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('descricao_external', models.CharField()),
                ('categoria_especial', models.TextField(choices=[('idoso_100', 'Idoso 100%'), ('idoso_50', 'Idoso 50%'), ('jovem_100', 'Jovem 100%'), ('jovem_50', 'Jovem 50%'), ('crianca', 'Criança (sem poltrona extra)'), ('pcd', 'PCD'), ('espaco_mulher', 'Espaço Mulher'), ('normal', 'Normal')], db_index=True, default='normal', null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rodoviaria.company')),
            ],
            options={
                'unique_together': {('company', 'categoria_id_external', 'categoria_especial')},
            },
        ),
    ]

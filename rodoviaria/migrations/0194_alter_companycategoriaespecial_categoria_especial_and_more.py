# Generated by Django 4.2.13 on 2024-07-31 21:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rodoviaria', '0193_alter_companycategoriaespecial_unique_together_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='companycategoriaespecial',
            name='categoria_especial',
            field=models.TextField(choices=[('idoso_100', 'Idoso 100%'), ('idoso_50', 'Idoso 50%'), ('jovem_100', 'Jovem 100%'), ('jovem_50', 'Jovem 50%'), ('crianca', 'Crian<PERSON> (sem poltrona extra)'), ('pcd', 'PCD'), ('espaco_mulher', 'Espaço Mulher'), ('espaco_pet', 'Espaço Pet'), ('normal', 'Normal'), ('ignorado', 'Ignorado')], db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpassagem',
            name='categoria_especial',
            field=models.TextField(blank=True, choices=[('idoso_100', 'Idoso 100%'), ('idoso_50', 'Idoso 50%'), ('jovem_100', 'Jovem 100%'), ('jovem_50', 'Jovem 50%'), ('crianca', 'Criança (sem poltrona extra)'), ('pcd', 'PCD'), ('espaco_mulher', 'Espaço Mulher'), ('espaco_pet', 'Espaço Pet'), ('normal', 'Normal'), ('ignorado', 'Ignorado')], default='normal', null=True),
        ),
        migrations.AlterField(
            model_name='passagem',
            name='categoria_especial',
            field=models.TextField(blank=True, choices=[('idoso_100', 'Idoso 100%'), ('idoso_50', 'Idoso 50%'), ('jovem_100', 'Jovem 100%'), ('jovem_50', 'Jovem 50%'), ('crianca', 'Criança (sem poltrona extra)'), ('pcd', 'PCD'), ('espaco_mulher', 'Espaço Mulher'), ('espaco_pet', 'Espaço Pet'), ('normal', 'Normal'), ('ignorado', 'Ignorado')], default='normal', null=True),
        ),
    ]

# Generated by Django 3.2.12 on 2022-08-12 19:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0133_alter_company_features"),
    ]

    operations = [
        migrations.CreateModel(
            name="RotinaTrechoVendido",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("datetime_ida_trecho_vendido", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "rotina",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.rotina",
                    ),
                ),
                (
                    "trecho_vendido",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.trechovendido",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="trechovendido",
            name="rotinas",
            field=models.ManyToManyField(through="rodoviaria.RotinaTrechoVendido", to="rodoviaria.Rotina"),
        ),
    ]

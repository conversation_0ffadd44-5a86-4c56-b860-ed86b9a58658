# Generated by Django 3.2 on 2022-07-06 19:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0122_auto_20220629_1452"),
    ]

    operations = [
        migrations.CreateModel(
            name="ConexaoRota",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("internal_grupo_id", models.IntegerField(db_index=True)),
                ("internal_rota_id", models.IntegerField(db_index=True)),
                ("external_rota_id_inicial", models.IntegerField(db_index=True)),
                ("external_rota_id_final", models.IntegerField(db_index=True)),
                (
                    "local_corte",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="rodoviaria.localembarque",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ConexaoTrechoClasse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("trechoclasse_internal_id", models.IntegerField(db_index=True)),
                ("trechoclasse_internal_inicial_id", models.IntegerField()),
                ("trechoclasse_internal_final_id", models.IntegerField()),
                (
                    "conexao_rota",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.conexaorota",
                    ),
                ),
            ],
        ),
    ]

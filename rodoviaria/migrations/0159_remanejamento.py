# Generated by Django 3.2.12 on 2023-02-06 17:45

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0158_alter_company_features"),
    ]

    operations = [
        migrations.CreateModel(
            name="Remanejamento",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "travel_destino_internal_id",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "travel_origem_internal_id",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pendente", "Pendente"),
                            ("completo", "Completo"),
                            ("erro", "Erro"),
                        ],
                        default="pendente",
                        max_length=20,
                    ),
                ),
                (
                    "etapa_erro",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("emissao", "Emissao"),
                            ("cancelamento", "Cancelamento"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                ("erro", models.TextField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
    ]

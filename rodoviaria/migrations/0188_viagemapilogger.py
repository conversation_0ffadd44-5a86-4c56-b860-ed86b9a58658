# Generated by Django 4.2.13 on 2024-06-26 13:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('rodoviaria', '0187_historicalcronogramaatualizacaooperacao'),
    ]

    operations = [
        migrations.CreateModel(
            name='ViagemAPILogger',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('datetime_ida', models.DateTimeField()),
                ('tipo_assento_parceiro', models.TextField()),
                ('preco', models.DecimalField(decimal_places=2, max_digits=12, null=True)),
                ('id_external', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rodoviaria.company')),
                ('destino', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='rodoviaria.localembarque')),
                ('origem', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='rodoviaria.localembarque')),
            ],
        ),
    ]

# Generated by Django 3.2.12 on 2022-02-28 18:47

import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
import django_cryptography.fields
import simple_history.models
import taggit.managers
from django.db import migrations, models


# Functions from the following migrations need manual copying.
# Move them and any dependencies into this file, then update the
# RunPython operations to refer to the local versions:
# rodoviaria.migrations.0076_change_data_from_id_external_to_id_hash
def change_hash_values_from_id_external_to_id_hash(apps, schema_editor):
    """
    We can't import the Rota model directly as it may be a newer
    version than this migration expects. We use the historical version.
    """
    Rota = apps.get_model("rodoviaria", "Rota")
    for rota in Rota.objects.all():
        rota.id_hash = rota.id_external
        rota.id_external = None
        rota.save()


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("taggit", "0003_taggeditem_add_unique_index"),
    ]

    operations = [
        migrations.CreateModel(
            name="Cidade",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("id_external", models.CharField(max_length=30)),
                ("name", models.CharField(max_length=256)),
                (
                    "cidade_internal_id",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="RodoviariaUser",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("user_internal_id", models.IntegerField(db_index=True)),
            ],
        ),
        migrations.CreateModel(
            name="SRVPSearch",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "destino",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="rodoviaria.cidade",
                    ),
                ),
                (
                    "origem",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="rodoviaria.cidade",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SRVPService",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("key", models.CharField(max_length=60, unique=True)),
                ("datetime_ida", models.DateTimeField()),
                ("datetime_chega", models.DateTimeField()),
                ("servico", models.CharField(max_length=30)),
                ("assentos_livres", models.IntegerField()),
                ("tipo", models.CharField(max_length=30)),
                ("comentario", models.CharField(max_length=100)),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.rodoviariauser",
                    ),
                ),
                (
                    "search",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.srvpsearch",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="HistoricalSRVPService",
            fields=[
                (
                    "id",
                    models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name="ID"),
                ),
                ("key", models.CharField(db_index=True, max_length=60)),
                ("datetime_ida", models.DateTimeField()),
                ("datetime_chega", models.DateTimeField()),
                ("servico", models.CharField(max_length=30)),
                ("assentos_livres", models.IntegerField()),
                ("tipo", models.CharField(max_length=30)),
                ("comentario", models.CharField(max_length=100)),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField()),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="rodoviaria.rodoviariauser",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="rodoviaria.rodoviariauser",
                    ),
                ),
                (
                    "search",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="rodoviaria.srvpsearch",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical srvp service",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": "history_date",
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("company_internal_id", models.IntegerField(db_index=True)),
                ("url_base", models.URLField()),
                ("password", models.CharField(max_length=200)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Integração com empresa",
                "verbose_name_plural": "Integrações com empresas",
            },
        ),
        migrations.CreateModel(
            name="Grupo",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("grupo_internal_id", models.IntegerField(db_index=True, unique=True)),
                ("external_id", models.CharField(max_length=50)),
                ("assentos_livres", models.IntegerField()),
                ("datetime_ida", models.DateTimeField()),
                ("datetime_chegada", models.DateTimeField()),
                (
                    "preco_rodoviaria",
                    models.DecimalField(decimal_places=2, max_digits=6),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.rodoviariauser",
                    ),
                ),
                (
                    "company_integracao",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="rodoviaria.company",
                    ),
                ),
                (
                    "destino",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="rodoviaria.cidade",
                    ),
                ),
                (
                    "origem",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="rodoviaria.cidade",
                    ),
                ),
            ],
            options={
                "verbose_name": "Integração com grupo",
                "verbose_name_plural": "Integrações com grupos",
            },
        ),
        migrations.CreateModel(
            name="Integracao",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, unique=True)),
                ("versao", models.CharField(max_length=30)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Integração",
                "verbose_name_plural": "Integrações",
            },
        ),
        migrations.CreateModel(
            name="Passagem",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("buseiro_internal_id", models.IntegerField(db_index=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "grupo_integracao",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.grupo",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="HistoricalGrupo",
            fields=[
                (
                    "id",
                    models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name="ID"),
                ),
                ("grupo_internal_id", models.IntegerField(db_index=True)),
                ("external_id", models.CharField(max_length=50)),
                ("assentos_livres", models.IntegerField()),
                ("datetime_ida", models.DateTimeField()),
                ("datetime_chegada", models.DateTimeField()),
                (
                    "preco_rodoviaria",
                    models.DecimalField(decimal_places=2, max_digits=6),
                ),
                ("created_at", models.DateTimeField(blank=True, editable=False)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField()),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="rodoviaria.rodoviariauser",
                    ),
                ),
                (
                    "company_integracao",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="rodoviaria.company",
                    ),
                ),
                (
                    "destino",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="rodoviaria.cidade",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="rodoviaria.rodoviariauser",
                    ),
                ),
                (
                    "origem",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="rodoviaria.cidade",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical Integração com grupo",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": "history_date",
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.AddField(
            model_name="company",
            name="integracao",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="rodoviaria.integracao"),
        ),
        migrations.AddField(
            model_name="cidade",
            name="company",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                to="rodoviaria.company",
            ),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name="LocalEmbarque",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("id_external", models.CharField(max_length=30)),
                ("name", models.CharField(max_length=256)),
                (
                    "local_embarque_internal_id",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="company",
            constraint=models.UniqueConstraint(
                fields=("integracao", "company_internal_id"),
                name="unique_rodoviaria_company",
            ),
        ),
        migrations.AddField(
            model_name="localembarque",
            name="cidade",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="rodoviaria.cidade"),
        ),
        migrations.AlterField(
            model_name="grupo",
            name="destino",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="rodoviaria.localembarque",
            ),
        ),
        migrations.AlterField(
            model_name="grupo",
            name="origem",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="rodoviaria.localembarque",
            ),
        ),
        migrations.AlterField(
            model_name="historicalgrupo",
            name="destino",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="rodoviaria.localembarque",
            ),
        ),
        migrations.AlterField(
            model_name="historicalgrupo",
            name="origem",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="rodoviaria.localembarque",
            ),
        ),
        migrations.AddField(
            model_name="passagem",
            name="poltrona_external_id",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="localizador",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.RemoveField(
            model_name="company",
            name="password",
        ),
        migrations.AlterModelOptions(
            name="cidade",
            options={
                "verbose_name": "Integração com cidade",
                "verbose_name_plural": "Integrações com cidades",
            },
        ),
        migrations.AlterModelOptions(
            name="localembarque",
            options={
                "verbose_name": "Integração com local de embarque",
                "verbose_name_plural": "Integrações com locais de embarque",
            },
        ),
        migrations.RemoveField(
            model_name="localembarque",
            name="name",
        ),
        migrations.AddField(
            model_name="localembarque",
            name="nickname",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.RemoveField(
            model_name="grupo",
            name="assentos_livres",
        ),
        migrations.RemoveField(
            model_name="grupo",
            name="external_id",
        ),
        migrations.RemoveField(
            model_name="historicalgrupo",
            name="assentos_livres",
        ),
        migrations.RemoveField(
            model_name="historicalgrupo",
            name="external_id",
        ),
        migrations.AddField(
            model_name="cidade",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="cidade",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="localembarque",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="localembarque",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.CreateModel(
            name="TrechoClasse",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("trechoclasse_internal_id", models.IntegerField(db_index=True)),
                (
                    "grupo",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.grupo",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("external_id", models.CharField(blank=True, max_length=50, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "guichepass_service",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
                ("active", models.BooleanField(default=True)),
            ],
            options={
                "verbose_name": "Integração com trechoclasse",
                "verbose_name_plural": "Integrações com trechoclasses",
            },
        ),
        migrations.AlterField(
            model_name="cidade",
            name="id_external",
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AlterField(
            model_name="cidade",
            name="name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="company",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="grupo",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="historicalgrupo",
            name="updated_at",
            field=models.DateTimeField(blank=True, default=django.utils.timezone.now, editable=False),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="integracao",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="passagem",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.RemoveField(
            model_name="passagem",
            name="grupo_integracao",
        ),
        migrations.AddField(
            model_name="grupo",
            name="external_id_tipo_veiculo",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="historicalgrupo",
            name="external_id_tipo_veiculo",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="trechoclasse_integracao",
            field=models.ForeignKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                to="rodoviaria.trechoclasse",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="passagem",
            name="status",
            field=models.CharField(
                blank=True,
                choices=[("confirmada", "Confirmada"), ("cancelada", "Cancelada")],
                max_length=20,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="PraxioLogin",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sistema", models.CharField(max_length=40)),
                ("name", models.CharField(max_length=40)),
                ("tipo_bd", models.IntegerField()),
                ("empresa", models.CharField(max_length=40)),
                ("cliente", models.CharField(max_length=40)),
                ("tipo_aplicacao", models.IntegerField()),
                (
                    "password",
                    django_cryptography.fields.encrypt(models.CharField(max_length=100)),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="passagem",
            name="travel_internal_id",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="company",
            name="active",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="grupo",
            name="totalbus_servico",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="historicalgrupo",
            name="totalbus_servico",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="totalbus_transacao",
            field=models.CharField(blank=True, max_length=254, null=True),
        ),
        migrations.CreateModel(
            name="TotalbusLogin",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("user", models.CharField(max_length=100)),
                (
                    "password",
                    django_cryptography.fields.encrypt(models.CharField(max_length=100)),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="cidade",
            name="company_external_id",
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AddField(
            model_name="company",
            name="company_external_id",
            field=models.IntegerField(blank=True, db_index=True, null=True, unique=True),
        ),
        migrations.CreateModel(
            name="GuichepassLogin",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("client_id", models.CharField(max_length=40)),
                ("username", models.CharField(max_length=40)),
                (
                    "password",
                    django_cryptography.fields.encrypt(models.CharField(max_length=100)),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
        ),
        migrations.RemoveField(
            model_name="grupo",
            name="datetime_chegada",
        ),
        migrations.RemoveField(
            model_name="historicalgrupo",
            name="datetime_chegada",
        ),
        migrations.AlterField(
            model_name="grupo",
            name="grupo_internal_id",
            field=models.IntegerField(db_index=True),
        ),
        migrations.CreateModel(
            name="Servico",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("totalbus_data", models.JSONField(blank=True, null=True)),
                ("guichepass_data", models.JSONField(blank=True, null=True)),
                ("praxio_data", models.JSONField(blank=True, null=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
                (
                    "destino",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="rodoviaria.localembarque",
                    ),
                ),
                (
                    "integracao",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.integracao",
                    ),
                ),
                (
                    "origem",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="rodoviaria.localembarque",
                    ),
                ),
                ("classe", models.CharField(blank=True, max_length=30, null=True)),
                ("datetime_ida", models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.AddField(
            model_name="company",
            name="name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.CreateModel(
            name="TrechoClasseError",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("trechoclasse_internal_id", models.IntegerField(db_index=True)),
                ("tipo_assento", models.CharField(max_length=50)),
                ("servicos_proximos", models.JSONField()),
                (
                    "destino",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="rodoviaria.localembarque",
                    ),
                ),
                (
                    "origem",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="+",
                        to="rodoviaria.localembarque",
                    ),
                ),
                ("datetime_ida", models.DateTimeField(null=True)),
                (
                    "company",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="passagem",
            name="valor_cheio",
            field=models.DecimalField(decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddConstraint(
            model_name="trechoclasse",
            constraint=models.UniqueConstraint(
                condition=models.Q(("active", True)),
                fields=("trechoclasse_internal_id",),
                name="internal_id_active_unique",
            ),
        ),
        migrations.AddField(
            model_name="passagem",
            name="bpe_qrcode",
            field=models.URLField(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="cidade",
            name="timezone",
            field=models.CharField(
                blank=True,
                choices=[
                    ("America/Araguaina", "America/Araguaina"),
                    ("America/Bahia", "America/Bahia"),
                    ("America/Belem", "America/Belem"),
                    ("America/Boa_Vista", "America/Boa_Vista"),
                    ("America/Campo_Grande", "America/Campo_Grande"),
                    ("America/Cuiaba", "America/Cuiaba"),
                    ("America/Eirunepe", "America/Eirunepe"),
                    ("America/Fortaleza", "America/Fortaleza"),
                    ("America/Maceio", "America/Maceio"),
                    ("America/Manaus", "America/Manaus"),
                    ("America/Noronha", "America/Noronha"),
                    ("America/Porto_Velho", "America/Porto_Velho"),
                    ("America/Recife", "America/Recife"),
                    ("America/Rio_Branco", "America/Rio_Branco"),
                    ("America/Santarem", "America/Santarem"),
                    ("America/Sao_Paulo", "America/Sao_Paulo"),
                ],
                max_length=256,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="Rota",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "id_internal",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                ("id_external", models.CharField(blank=True, max_length=50, null=True)),
                ("provider_data", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
                ("id_hash", models.CharField(default="empty", max_length=50)),
            ],
        ),
        migrations.AddField(
            model_name="grupo",
            name="rota",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="rodoviaria.rota",
            ),
        ),
        migrations.AddField(
            model_name="historicalgrupo",
            name="rota",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="rodoviaria.rota",
            ),
        ),
        migrations.AlterField(
            model_name="passagem",
            name="status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("confirmada", "Confirmada"),
                    ("cancelada", "Cancelada"),
                    ("incompleta", "Incompleta"),
                    ("erro", "Erro"),
                ],
                max_length=20,
                null=True,
            ),
        ),
        migrations.DeleteModel(
            name="Servico",
        ),
        migrations.RemoveField(
            model_name="cidade",
            name="company_external_id",
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="external_datetime_ida",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="RotaPraxio",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("rodoviaria.rota",),
        ),
        migrations.CreateModel(
            name="RotaTotalbus",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("rodoviaria.rota",),
        ),
        migrations.RemoveField(
            model_name="trechoclasse",
            name="guichepass_service",
        ),
        migrations.RemoveField(
            model_name="grupo",
            name="totalbus_servico",
        ),
        migrations.RemoveField(
            model_name="historicalgrupo",
            name="totalbus_servico",
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="datetime_ida",
            field=models.DateTimeField(null=True),
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="destino",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="rodoviaria.localembarque",
            ),
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="origem",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="rodoviaria.localembarque",
            ),
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="preco_rodoviaria",
            field=models.DecimalField(decimal_places=2, max_digits=6, null=True),
        ),
        migrations.RemoveField(
            model_name="grupo",
            name="destino",
        ),
        migrations.RemoveField(
            model_name="grupo",
            name="origem",
        ),
        migrations.RemoveField(
            model_name="grupo",
            name="preco_rodoviaria",
        ),
        migrations.RemoveField(
            model_name="historicalgrupo",
            name="destino",
        ),
        migrations.RemoveField(
            model_name="historicalgrupo",
            name="origem",
        ),
        migrations.RemoveField(
            model_name="historicalgrupo",
            name="preco_rodoviaria",
        ),
        migrations.AddField(
            model_name="passagem",
            name="bpe_monitriip_code",
            field=models.CharField(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="chave_bpe",
            field=models.CharField(blank=True, max_length=44, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="data_autorizacao",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="embarque",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="numero_bilhete",
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="numero_bpe",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="outras_taxas",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="outros_tributos",
            field=models.CharField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="pedagio",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="preco_base",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="prefixo",
            field=models.CharField(blank=True, max_length=24, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="protocolo_autorizacao",
            field=models.CharField(blank=True, max_length=18, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="seguro",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="serie_bpe",
            field=models.CharField(blank=True, max_length=4, null=True),
        ),
        migrations.AddField(
            model_name="totalbuslogin",
            name="tenant_id",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="grupo",
            name="linha",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="historicalgrupo",
            name="linha",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="preco_rodoviaria",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="external_id_tipo_veiculo",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.RemoveField(
            model_name="grupo",
            name="external_id_tipo_veiculo",
        ),
        migrations.RemoveField(
            model_name="historicalgrupo",
            name="external_id_tipo_veiculo",
        ),
        migrations.CreateModel(
            name="CidadeInternal",
            fields=[
                ("id", models.IntegerField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=256)),
                (
                    "uf",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("AC", "Acre"),
                            ("AL", "Alagoas"),
                            ("AP", "Amapá"),
                            ("AM", "Amazonas"),
                            ("BA", "Bahia"),
                            ("CE", "Ceará"),
                            ("DF", "Distrito Federal"),
                            ("ES", "Espírito Santo"),
                            ("GO", "Goiás"),
                            ("MA", "Maranhão"),
                            ("MT", "Mato Grosso"),
                            ("MS", "Mato Grosso do Sul"),
                            ("MG", "Minas Gerais"),
                            ("PA", "Pará"),
                            ("PB", "Paraíba"),
                            ("PR", "Paraná"),
                            ("PE", "Pernambuco"),
                            ("PI", "Piauí"),
                            ("RJ", "Rio de Janeiro"),
                            ("RN", "Rio Grande do Norte"),
                            ("RS", "Rio Grande do Sul"),
                            ("RO", "Rondônia"),
                            ("RR", "Roraima"),
                            ("SC", "Santa Catarina"),
                            ("SP", "São Paulo"),
                            ("SE", "Sergipe"),
                            ("TO", "Tocantins"),
                            ("EX", "Exterior"),
                        ],
                        max_length=2,
                        null=True,
                    ),
                ),
                ("sigla", models.CharField(blank=True, max_length=3, null=True)),
                (
                    "timezone",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("America/Araguaina", "America/Araguaina"),
                            ("America/Bahia", "America/Bahia"),
                            ("America/Belem", "America/Belem"),
                            ("America/Boa_Vista", "America/Boa_Vista"),
                            ("America/Campo_Grande", "America/Campo_Grande"),
                            ("America/Cuiaba", "America/Cuiaba"),
                            ("America/Eirunepe", "America/Eirunepe"),
                            ("America/Fortaleza", "America/Fortaleza"),
                            ("America/Maceio", "America/Maceio"),
                            ("America/Manaus", "America/Manaus"),
                            ("America/Noronha", "America/Noronha"),
                            ("America/Porto_Velho", "America/Porto_Velho"),
                            ("America/Recife", "America/Recife"),
                            ("America/Rio_Branco", "America/Rio_Branco"),
                            ("America/Santarem", "America/Santarem"),
                            ("America/Sao_Paulo", "America/Sao_Paulo"),
                        ],
                        max_length=256,
                        null=True,
                    ),
                ),
                (
                    "city_code_ibge",
                    models.CharField(blank=True, max_length=7, null=True),
                ),
                ("uf_code_ibge", models.CharField(blank=True, max_length=2, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.RemoveField(
            model_name="srvpsearch",
            name="destino",
        ),
        migrations.RemoveField(
            model_name="srvpsearch",
            name="origem",
        ),
        migrations.RemoveField(
            model_name="srvpservice",
            name="changed_by",
        ),
        migrations.RemoveField(
            model_name="srvpservice",
            name="search",
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="provider_data",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="company",
            name="features",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("itinerario", "Itinerario"),
                        ("buscar_servico", "Buscar Servico"),
                        ("add_pax_staff", "Add Pax Staff"),
                        ("bpe", "Bpe"),
                        ("ignora_classe", "Ignora Classe"),
                        ("active", "Active"),
                    ],
                    max_length=50,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
        migrations.DeleteModel(
            name="HistoricalSRVPService",
        ),
        migrations.DeleteModel(
            name="SRVPSearch",
        ),
        migrations.DeleteModel(
            name="SRVPService",
        ),
        migrations.AddField(
            model_name="passagem",
            name="erro",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="totalbuslogin",
            name="forma_pagamento",
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name="totalbuslogin",
            name="id_forma_pagamento",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="numero_passagem",
            field=models.CharField(blank=True, max_length=30, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="multa",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
        ),
        migrations.AddField(
            model_name="totalbuslogin",
            name="validar_multa",
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "content_object",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.passagem",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rodoviaria_tag_items",
                        to="taggit.tag",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="passagem",
            name="tags",
            field=taggit.managers.TaggableManager(
                help_text="A comma-separated list of tags.",
                through="rodoviaria.Tag",
                to="taggit.Tag",
                verbose_name="Tags",
            ),
        ),
        migrations.CreateModel(
            name="TrechoVendido",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "id_internal",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                ("classe", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "distancia",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("duracao", models.DurationField(blank=True, null=True)),
                (
                    "preco",
                    models.DecimalField(decimal_places=2, max_digits=6, null=True),
                ),
                (
                    "destino",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="rodoviaria.localembarque",
                    ),
                ),
                (
                    "origem",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="rodoviaria.localembarque",
                    ),
                ),
                (
                    "rota",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.rota",
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="TaggedPassagem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "content_object",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.passagem",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rodoviaria_taggedpassagem_items",
                        to="taggit.tag",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.DeleteModel(
            name="Tag",
        ),
        migrations.AlterField(
            model_name="passagem",
            name="tags",
            field=taggit.managers.TaggableManager(
                blank=True,
                help_text="A comma-separated list of tags.",
                through="rodoviaria.TaggedPassagem",
                to="taggit.Tag",
                verbose_name="Tags",
            ),
        ),
        migrations.CreateModel(
            name="TaggedTrechoClasse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rodoviaria_taggedtrechoclasse_items",
                        to="taggit.tag",
                    ),
                ),
                (
                    "content_object",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.trechoclasse",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="tags",
            field=taggit.managers.TaggableManager(
                blank=True,
                help_text="A comma-separated list of tags.",
                through="rodoviaria.TaggedTrechoClasse",
                to="taggit.Tag",
                verbose_name="Tags",
            ),
        ),
        migrations.RemoveField(
            model_name="company",
            name="active",
        ),
        migrations.AlterField(
            model_name="grupo",
            name="grupo_internal_id",
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalgrupo",
            name="grupo_internal_id",
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name="trechoclasse",
            name="trechoclasse_internal_id",
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.CreateModel(
            name="VexadoLogin",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("user", models.CharField(max_length=100)),
                (
                    "password",
                    django_cryptography.fields.encrypt(models.CharField(max_length=100)),
                ),
                ("site", models.CharField(max_length=100)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="passagem",
            name="datetime_cancelamento",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="erro_cancelamento",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.RunPython(change_hash_values_from_id_external_to_id_hash),
        migrations.AlterField(
            model_name="rota",
            name="id_external",
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name="rota",
            name="id_hash",
            field=models.CharField(max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name="rota",
            name="id_hash",
            field=models.CharField(max_length=50, unique=True),
        ),
        migrations.CreateModel(
            name="RotaVexado",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("rodoviaria.rota",),
        ),
        migrations.AddField(
            model_name="passagem",
            name="desconto",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
        ),
        migrations.AddField(
            model_name="grupo",
            name="external_datetime_ida",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="historicalgrupo",
            name="external_datetime_ida",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="GrupoClasse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "grupoclasse_internal_id",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                (
                    "tipo_assento_external",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
                (
                    "grupo",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.grupo",
                    ),
                ),
                (
                    "tipo_assento_internal",
                    models.CharField(blank=True, max_length=64, null=True),
                ),
            ],
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="grupo_classe",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="rodoviaria.grupoclasse",
            ),
        ),
        migrations.AlterField(
            model_name="rota",
            name="id_external",
            field=models.CharField(blank=True, db_index=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="company",
            name="features",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("itinerario", "Itinerario"),
                        ("buscar_servico", "Buscar Servico"),
                        ("add_pax_staff", "Add Pax Staff"),
                        ("bpe", "Bpe"),
                        ("motorista", "Motorista"),
                        ("ignora_classe", "Ignora Classe"),
                        ("active", "Active"),
                    ],
                    max_length=50,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
        migrations.CreateModel(
            name="Motorista",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("internal_id", models.IntegerField()),
                ("external_id_usuario", models.IntegerField(blank=True, null=True)),
                (
                    "external_id_usuario_empresa",
                    models.IntegerField(blank=True, null=True),
                ),
                ("external_id_motorista", models.IntegerField(blank=True, null=True)),
                ("nome", models.CharField(blank=True, max_length=256)),
                ("email", models.EmailField(blank=True, max_length=254)),
                ("telefone", models.CharField(blank=True, max_length=16)),
                ("cpf", models.CharField(blank=True, max_length=16)),
                ("cnh_numero", models.CharField(blank=True, max_length=11)),
                ("cnh_validade", models.DateField(blank=True, null=True)),
                ("cnh_categoria", models.CharField(blank=True, max_length=2)),
                ("cnh_orgao_emissor", models.CharField(blank=True, max_length=256)),
                ("cnh_uf", models.CharField(blank=True, max_length=2)),
                ("antt_numero", models.CharField(blank=True, max_length=32)),
                ("antt_validade", models.DateField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
            options={
                "unique_together": {("company", "internal_id")},
            },
        ),
        migrations.AddField(
            model_name="trechoclasse",
            name="external_id_desconto",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="praxiologin",
            name="desconto_manual",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="company",
            name="modelo_venda",
            field=models.CharField(
                choices=[("marketplace", "Marketplace"), ("hibrido", "Hibrido")],
                default="marketplace",
                max_length=15,
            ),
        ),
        migrations.AlterField(
            model_name="company",
            name="features",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("itinerario", "Itinerario"),
                        ("buscar_servico", "Buscar Servico"),
                        ("add_pax_staff", "Add Pax Staff"),
                        ("bpe", "Bpe"),
                        ("motorista", "Motorista"),
                        ("atualizar_preco", "Atualizar Preco"),
                        ("ignora_classe", "Ignora Classe"),
                        ("active", "Active"),
                    ],
                    max_length=50,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
        migrations.RemoveConstraint(
            model_name="company",
            name="unique_rodoviaria_company",
        ),
        migrations.AlterField(
            model_name="company",
            name="company_external_id",
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterUniqueTogether(
            name="company",
            unique_together={("company_internal_id", "modelo_venda")},
        ),
        migrations.CreateModel(
            name="MapaVeiculo",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "id_external",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                ("has_dois_andares", models.BooleanField(default=False)),
                (
                    "quantidade_poltronas_primeiro_andar",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "quantidade_poltronas_segundo_andar",
                    models.IntegerField(blank=True, null=True),
                ),
                ("provider_data", models.TextField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AlterField(
            model_name="company",
            name="features",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("itinerario", "Itinerario"),
                        ("buscar_servico", "Buscar Servico"),
                        ("add_pax_staff", "Add Pax Staff"),
                        ("bpe", "Bpe"),
                        ("motorista", "Motorista"),
                        ("atualizar_preco", "Atualizar Preco"),
                        ("ignora_classe", "Ignora Classe"),
                        ("active", "Active"),
                        ("escalar_veiculos", "Escalar Veiculos"),
                    ],
                    max_length=50,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
    ]

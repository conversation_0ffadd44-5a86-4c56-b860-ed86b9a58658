# Generated by Django 3.2 on 2022-06-29 17:52

from django.db import migrations


def fill_company_id_passagem(apps, schema_editor):
    Passagem = apps.get_model("rodoviaria", "Passagem")
    passagens = Passagem.objects.all().select_related("trechoclasse_integracao__grupo__company_integracao")
    for passagem in passagens:
        passagem.company_integracao = passagem.trechoclasse_integracao.grupo.company_integracao
    Passagem.objects.bulk_update(passagens, ["company_integracao"], batch_size=10000)


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0121_passagem_company_integracao"),
    ]

    operations = [
        migrations.RunPython(fill_company_id_passagem),
    ]

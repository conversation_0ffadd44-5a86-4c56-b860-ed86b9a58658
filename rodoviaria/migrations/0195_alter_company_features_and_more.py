# Generated by Django 4.2.13 on 2024-08-07 18:54

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rodoviaria', '0194_alter_companycategoriaespecial_categoria_especial_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='features',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('itinerario', 'Itinerario'), ('buscar_servico', 'Buscar Servico'), ('add_pax_staff', 'Add Pax Staff'), ('bpe', 'Bpe'), ('motorista', 'Motorista'), ('atualizar_preco', 'Atualizar Preco'), ('active', 'Active'), ('escalar_veiculos', 'Escalar Veiculos'), ('usar_preco_api', 'Usar Preco Api'), ('parent_company_no_bpe', 'Parent Company No Bpe'), ('fetch_trechos_further', 'Fetch Trechos Further'), ('atualizar_preco_checkout', 'Atualizar Preco Checkout'), ('auto_integra_operacao', 'Auto Integra Operacao'), ('atualiza_operacao_com_alta_frequencia', 'Atualiza Operacao High Freq'), ('risk_share', 'Risk Share'), ('assentos_ociosos', 'Assentos Ociosos')], max_length=50), blank=True, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='company',
            name='previous_features',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('itinerario', 'Itinerario'), ('buscar_servico', 'Buscar Servico'), ('add_pax_staff', 'Add Pax Staff'), ('bpe', 'Bpe'), ('motorista', 'Motorista'), ('atualizar_preco', 'Atualizar Preco'), ('active', 'Active'), ('escalar_veiculos', 'Escalar Veiculos'), ('usar_preco_api', 'Usar Preco Api'), ('parent_company_no_bpe', 'Parent Company No Bpe'), ('fetch_trechos_further', 'Fetch Trechos Further'), ('atualizar_preco_checkout', 'Atualizar Preco Checkout'), ('auto_integra_operacao', 'Auto Integra Operacao'), ('atualiza_operacao_com_alta_frequencia', 'Atualiza Operacao High Freq'), ('risk_share', 'Risk Share'), ('assentos_ociosos', 'Assentos Ociosos')], max_length=50), blank=True, null=True, size=None),
        ),
    ]

# Generated by Django 5.1 on 2024-11-26 17:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("rodoviaria", "0201_taskstatus_last_finished_at"),
    ]

    operations = [
        migrations.AlterField(
            model_name="cidadeinternal",
            name="name",
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name="grupo",
            name="linha",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalgrupo",
            name="linha",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalpassagem",
            name="bpe_monitriip_code",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalpassagem",
            name="endereco_empresa",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalpassagem",
            name="linha",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalpassagem",
            name="outros_tributos",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalpassagem",
            name="pedido_external_id",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="motorista",
            name="cnh_orgao_emissor",
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name="motorista",
            name="nome",
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name="passagem",
            name="bpe_monitriip_code",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="passagem",
            name="endereco_empresa",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="passagem",
            name="linha",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="passagem",
            name="outros_tributos",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="passagem",
            name="pedido_external_id",
            field=models.TextField(blank=True, null=True),
        ),
    ]

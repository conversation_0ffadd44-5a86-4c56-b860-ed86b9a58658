import datetime

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0001_squashed_0090_mapaveiculo"),
    ]

    operations = [
        migrations.CreateModel(
            name="Checkpoint",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("idx", models.IntegerField()),
                (
                    "internal_id",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                ("distancia_km", models.IntegerField(blank=True, null=True)),
                ("duracao", models.DurationField(blank=True, null=True)),
                (
                    "tempo_embarque",
                    models.DurationField(default=datetime.timedelta(seconds=1200)),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "local",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.localembarque",
                    ),
                ),
                (
                    "rota",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="itinerario",
                        to="rodoviaria.rota",
                    ),
                ),
            ],
            options={
                "verbose_name": "Integração com local de embarque",
                "verbose_name_plural": "Integrações com locais de embarque",
                "ordering": ("rota", "idx"),
                "unique_together": {("rota", "idx")},
            },
        ),
    ]

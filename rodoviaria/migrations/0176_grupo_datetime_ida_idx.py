# Generated by Django 3.2.12 on 2023-09-26 20:04

from django.contrib.postgres.operations import AddIndexConcurrently
from django.db import migrations, models


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ("rodoviaria", "0175_auto_20230918_1442"),
    ]

    operations = [
        AddIndexConcurrently(
            model_name="grupo",
            index=models.Index(fields=["-datetime_ida"], name="grupo_datetime_ida_idx"),
        ),
    ]

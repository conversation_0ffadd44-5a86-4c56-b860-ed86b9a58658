# Generated by Django 3.2.12 on 2022-06-30 18:24

import django.db.models.deletion
import taggit.managers
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("taggit", "0003_taggeditem_add_unique_index"),
        ("rodoviaria", "0122_auto_20220629_1452"),
    ]

    operations = [
        migrations.CreateModel(
            name="TaggedTrechoClasseError",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "content_object",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.trechoclasseerror",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rodoviaria_taggedtrechoclasseerror_items",
                        to="taggit.tag",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="trechoclasseerror",
            name="tags",
            field=taggit.managers.TaggableManager(
                blank=True,
                help_text="A comma-separated list of tags.",
                through="rodoviaria.TaggedTrechoClasseError",
                to="taggit.Tag",
                verbose_name="Tags",
            ),
        ),
    ]

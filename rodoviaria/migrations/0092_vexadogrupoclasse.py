# Generated by Django 3.2.6 on 2022-02-22 21:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0091_veiculo"),
    ]

    operations = [
        migrations.CreateModel(
            name="VexadoGrupoClasse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("andar", models.IntegerField(default=1)),
                (
                    "grupo_classe",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.grupoclasse",
                    ),
                ),
                (
                    "veiculo",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="rodoviaria.veiculo",
                    ),
                ),
            ],
        ),
    ]

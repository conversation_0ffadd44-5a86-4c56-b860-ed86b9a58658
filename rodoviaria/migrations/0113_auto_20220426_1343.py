# Generated by Django 3.2 on 2022-04-26 16:43

from django.db import migrations


def migrate_totalbus_transacao_to_pedido_external_id(apps, schema_editor):
    Passagem = apps.get_model("rodoviaria", "Passagem")
    passagens = Passagem.objects.filter(totalbus_transacao__isnull=False, pedido_external_id__isnull=True)
    for passagem in passagens:
        passagem.pedido_external_id = passagem.totalbus_transacao
    Passagem.objects.bulk_update(passagens, ["pedido_external_id"])


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0112_auto_20220422_1436"),
    ]

    operations = [
        migrations.RunPython(migrate_totalbus_transacao_to_pedido_external_id),
    ]

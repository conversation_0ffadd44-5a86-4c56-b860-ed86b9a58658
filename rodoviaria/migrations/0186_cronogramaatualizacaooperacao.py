# Generated by Django 4.0.10 on 2024-05-28 17:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('rodoviaria', '0185_alter_taskstatus_task_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='CronogramaAtualizacaoOperacao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo_atualizacao', models.TextField(choices=[('fetch_trechos_vendidos', 'Fetch Trechos Vendidos'), ('descobrir_rotas', 'Descobrir Rotas'), ('descobrir_operacao', 'Descobrir Operacao'), ('fetch_data_limite', 'Fetch Data Limite')])),
                ('dia_semana', models.PositiveIntegerField(choices=[(1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday'), (7, 'Sunday')])),
                ('horario', models.TimeField()),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rodoviaria.company')),
            ],
            options={
                'unique_together': {('company', 'tipo_atualizacao', 'dia_semana')},
            },
        ),
    ]

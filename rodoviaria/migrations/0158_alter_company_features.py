# Generated by Django 3.2.12 on 2023-01-26 17:18

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0157_company_max_percentual_divergencia"),
    ]

    operations = [
        migrations.AlterField(
            model_name="company",
            name="features",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("itinerario", "Itinerario"),
                        ("buscar_servico", "Buscar Servico"),
                        ("add_pax_staff", "Add Pax Staff"),
                        ("bpe", "Bpe"),
                        ("motorista", "Motorista"),
                        ("atualizar_preco", "Atualizar Preco"),
                        ("ignora_classe", "Ignora Classe"),
                        ("active", "Active"),
                        ("escalar_veiculos", "Escalar Veiculos"),
                        ("usar_preco_api", "Usar Preco Api"),
                        ("parent_company_no_bpe", "Parent Company No Bpe"),
                        ("fetch_trechos_further", "Fetch Trechos Further"),
                        ("atualizar_preco_checkout", "Atualizar Preco Checkout"),
                        ("auto_create_groups", "Auto Create Groups"),
                    ],
                    max_length=50,
                ),
                blank=True,
                null=True,
                size=None,
            ),
        ),
    ]

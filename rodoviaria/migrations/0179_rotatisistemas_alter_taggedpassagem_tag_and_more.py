# Generated by Django 4.0.10 on 2024-02-06 14:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("taggit", "0005_auto_20220424_2025"),
        ("rodoviaria", "0178_company_feature_alta_frequencia"),
    ]

    operations = [
        migrations.CreateModel(
            name="RotaTiSistemas",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("rodoviaria.rota",),
        ),
        migrations.AlterField(
            model_name="taggedpassagem",
            name="tag",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(app_label)s_%(class)s_items",
                to="taggit.tag",
            ),
        ),
        migrations.AlterField(
            model_name="taggedtrechoclasse",
            name="tag",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(app_label)s_%(class)s_items",
                to="taggit.tag",
            ),
        ),
        migrations.AlterField(
            model_name="taggedtrechoclasseerror",
            name="tag",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(app_label)s_%(class)s_items",
                to="taggit.tag",
            ),
        ),
    ]

# Generated by Django 3.2.12 on 2022-11-03 17:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0148_passagem_conexao"),
    ]

    operations = [
        migrations.CreateModel(
            name="TaskStatus",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "task_name",
                    models.CharField(
                        choices=[
                            ("fetch_trechos_vendidos", "Fetch Trechos Vendidos"),
                            ("fetch_rotinas", "Fetch Rotinas"),
                            ("descobrir_rotas", "Descobrir Rotas"),
                        ],
                        max_length=40,
                    ),
                ),
                ("task_id", models.CharField(max_length=50)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("success", "Success"),
                            ("failure", "Failure"),
                        ],
                        max_length=20,
                    ),
                ),
                ("last_success_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
                (
                    "rota",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.rota",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Task status",
                "unique_together": {("company_id", "rota_id", "task_name")},
            },
        ),
    ]

# Generated by Django 4.2.13 on 2024-07-29 20:29

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rodoviaria', '0190_alter_trechoclasseerror_trechoclasse_internal_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalpassagem',
            name='categoria_especial',
            field=models.TextField(blank=True, choices=[('idoso_100', 'Idoso 100'), ('idoso_50', 'Idoso 50'), ('jovem_100', 'Jovem 100'), ('jovem_50', 'Jovem 50'), ('crianca', 'Crianca'), ('pcd', 'Pcd'), ('espaco_mulher', 'Espaco Mulher'), ('normal', 'Normal')], null=True),
        ),
        migrations.AddField(
            model_name='passagem',
            name='categoria_especial',
            field=models.TextField(blank=True, choices=[('idoso_100', 'Idoso 100'), ('idoso_50', 'Idoso 50'), ('jovem_100', 'Jovem 100'), ('jovem_50', 'Jovem 50'), ('crianca', 'Crianca'), ('pcd', 'Pcd'), ('espaco_mulher', 'Espaco Mulher'), ('normal', 'Normal')], null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='features',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('itinerario', 'Itinerario'), ('buscar_servico', 'Buscar Servico'), ('add_pax_staff', 'Add Pax Staff'), ('bpe', 'Bpe'), ('motorista', 'Motorista'), ('atualizar_preco', 'Atualizar Preco'), ('ignora_classe', 'Ignora Classe'), ('active', 'Active'), ('escalar_veiculos', 'Escalar Veiculos'), ('usar_preco_api', 'Usar Preco Api'), ('parent_company_no_bpe', 'Parent Company No Bpe'), ('fetch_trechos_further', 'Fetch Trechos Further'), ('atualizar_preco_checkout', 'Atualizar Preco Checkout'), ('auto_integra_operacao', 'Auto Integra Operacao'), ('atualiza_operacao_com_alta_frequencia', 'Atualiza Operacao High Freq'), ('risk_share', 'Risk Share'), ('assentos_ociosos', 'Assentos Ociosos')], max_length=50), blank=True, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='company',
            name='previous_features',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('itinerario', 'Itinerario'), ('buscar_servico', 'Buscar Servico'), ('add_pax_staff', 'Add Pax Staff'), ('bpe', 'Bpe'), ('motorista', 'Motorista'), ('atualizar_preco', 'Atualizar Preco'), ('ignora_classe', 'Ignora Classe'), ('active', 'Active'), ('escalar_veiculos', 'Escalar Veiculos'), ('usar_preco_api', 'Usar Preco Api'), ('parent_company_no_bpe', 'Parent Company No Bpe'), ('fetch_trechos_further', 'Fetch Trechos Further'), ('atualizar_preco_checkout', 'Atualizar Preco Checkout'), ('auto_integra_operacao', 'Auto Integra Operacao'), ('atualiza_operacao_com_alta_frequencia', 'Atualiza Operacao High Freq'), ('risk_share', 'Risk Share'), ('assentos_ociosos', 'Assentos Ociosos')], max_length=50), blank=True, null=True, size=None),
        ),
    ]

# Generated by Django 3.2.12 on 2022-08-09 19:05

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0132_feature_usar_preco_api_and_integracao_choices"),
    ]

    operations = [
        migrations.AddField(
            model_name="trechoclasseerror",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="trechoclasseerror",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
    ]

# Generated by Django 3.2.12 on 2022-03-25 12:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0104_alter_vexadogrupoclasse_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="Rot<PERSON>",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("datetime_ida", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "rota",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.rota",
                    ),
                ),
            ],
            options={
                "verbose_name": "Rotina",
                "verbose_name_plural": "Rotinas",
                "ordering": ("rota",),
            },
        ),
    ]

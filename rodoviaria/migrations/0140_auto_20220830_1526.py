# Generated by Django 3.2.12 on 2022-08-30 18:26

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0139_alter_passagem_bpe_em_contingencia"),
    ]

    operations = [
        migrations.Rename<PERSON>ield(
            model_name="passagem",
            old_name="embarque",
            new_name="taxa_embarque",
        ),
        migrations.AddField(
            model_name="passagem",
            name="codigo_taxa_embarque",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="numero_bilhete_embarque",
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="passagem",
            name="tipo_taxa_embarque",
            field=models.CharField(
                choices=[("qr_code", "Qrcode"), ("bar_code", "Barcode")],
                max_length=20,
                null=True,
            ),
        ),
    ]

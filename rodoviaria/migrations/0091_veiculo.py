# Generated by Django 3.2.6 on 2022-02-15 21:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0091_auto_20220211_1539"),
    ]

    operations = [
        migrations.CreateModel(
            name="<PERSON>ei<PERSON><PERSON>",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "id_external",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                (
                    "id_internal",
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                ("descricao", models.CharField(blank=True, max_length=255)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
                (
                    "mapa_veiculo",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.mapaveiculo",
                    ),
                ),
            ],
        ),
    ]

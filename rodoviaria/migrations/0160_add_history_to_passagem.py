# Generated by Django 3.2.12 on 2023-03-17 20:57

import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("rodoviaria", "0159_remanejamento"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicalgrupo",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical Integração com grupo",
                "verbose_name_plural": "historical Integrações com grupos",
            },
        ),
        migrations.AlterField(
            model_name="historicalgrupo",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.CreateModel(
            name="HistoricalPassagem",
            fields=[
                (
                    "id",
                    models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name="ID"),
                ),
                ("buseiro_internal_id", models.IntegerField(db_index=True)),
                ("poltrona_external_id", models.IntegerField(blank=True, null=True)),
                ("travel_internal_id", models.IntegerField(blank=True, null=True)),
                ("localizador", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "pedido_external_id",
                    models.CharField(blank=True, max_length=254, null=True),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("confirmada", "Confirmada"),
                            ("cancelada", "Cancelada"),
                            ("incompleta", "Incompleta"),
                            ("erro", "Erro"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "valor_cheio",
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                ("bpe_qrcode", models.URLField(blank=True, max_length=500, null=True)),
                (
                    "bpe_monitriip_code",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "bpe_em_contingencia",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, editable=False)),
                ("updated_at", models.DateTimeField(blank=True, editable=False)),
                ("erro", models.TextField(blank=True, null=True)),
                ("erro_cancelamento", models.TextField(blank=True, null=True)),
                ("datetime_cancelamento", models.DateTimeField(blank=True, null=True)),
                (
                    "numero_passagem",
                    models.CharField(blank=True, max_length=30, null=True),
                ),
                (
                    "multa",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                ("plataforma", models.CharField(blank=True, max_length=25, null=True)),
                (
                    "preco_base",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                (
                    "taxa_embarque",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                (
                    "seguro",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                (
                    "pedagio",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                (
                    "outras_taxas",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                (
                    "outros_tributos",
                    models.CharField(blank=True, max_length=254, null=True),
                ),
                (
                    "preco_rodoviaria",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                (
                    "preco_poltrona",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                (
                    "desconto",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True),
                ),
                ("chave_bpe", models.CharField(blank=True, max_length=44, null=True)),
                ("numero_bpe", models.CharField(blank=True, max_length=10, null=True)),
                ("serie_bpe", models.CharField(blank=True, max_length=4, null=True)),
                (
                    "protocolo_autorizacao",
                    models.CharField(blank=True, max_length=18, null=True),
                ),
                (
                    "numero_bilhete",
                    models.CharField(blank=True, max_length=16, null=True),
                ),
                ("data_autorizacao", models.DateTimeField(blank=True, null=True)),
                ("prefixo", models.CharField(blank=True, max_length=24, null=True)),
                ("linha", models.CharField(blank=True, max_length=200, null=True)),
                ("cnpj", models.CharField(max_length=18, null=True)),
                (
                    "inscricao_estadual",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "endereco_empresa",
                    models.CharField(blank=True, max_length=254, null=True),
                ),
                (
                    "nome_agencia",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "tipo_emissao",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "tipo_taxa_embarque",
                    models.CharField(
                        blank=True,
                        choices=[("qr_code", "Qrcode"), ("bar_code", "Barcode")],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "codigo_taxa_embarque",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "numero_bilhete_embarque",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("origem", models.CharField(blank=True, max_length=100, null=True)),
                ("destino", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "data_hora_partida",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "company_integracao",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="rodoviaria.company",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "trechoclasse_integracao",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="rodoviaria.trechoclasse",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical passagem",
                "verbose_name_plural": "historical passagems",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]

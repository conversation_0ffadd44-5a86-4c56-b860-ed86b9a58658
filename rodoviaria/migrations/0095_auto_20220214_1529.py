# Generated by Django 3.2.6 on 2022-02-14 18:29

from django.db import migrations


def change_id_values_from_internal_id_to_foreign_key(apps, schema_editor):
    Cidades = apps.get_model("rodoviaria", "Cidade")
    for city in Cidades.objects.all():
        city.cidade_internal_id = city.internal_id
        city.internal_id = None
        city.save()


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0094_cidade_cidade_internal"),
    ]

    operations = [
        migrations.RunPython(change_id_values_from_internal_id_to_foreign_key),
    ]

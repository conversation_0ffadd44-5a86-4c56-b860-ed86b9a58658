# Generated by Django 3.2.12 on 2023-03-21 19:19

import django.db.models.deletion
import django_cryptography.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("rodoviaria", "0160_add_history_to_passagem"),
    ]

    operations = [
        migrations.AlterField(
            model_name="integracao",
            name="name",
            field=models.CharField(
                blank=True,
                choices=[
                    ("totalbus", "Totalbus"),
                    ("praxio", "Praxio"),
                    ("vexado", "Vexado"),
                    ("guichepass", "Guiche"),
                    ("eulabs", "Eulabs"),
                    ("smartbus", "Smartbus"),
                ],
                max_length=50,
                null=True,
                unique=True,
            ),
        ),
        migrations.CreateModel(
            name="SmartbusLogin",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("cliente", models.<PERSON><PERSON><PERSON><PERSON>(max_length=40)),
                ("username", models.Char<PERSON><PERSON>(max_length=40)),
                (
                    "password",
                    django_cryptography.fields.encrypt(models.Char<PERSON>ield(max_length=100)),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="rodoviaria.company",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]

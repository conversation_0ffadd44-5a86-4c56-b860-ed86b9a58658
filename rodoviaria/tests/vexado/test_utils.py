from datetime import timedelta
from decimal import Decimal

import pytest

from rodoviaria.api.vexado.utils import duracao_to_timedelta, format_preco, timedelta_to_duracao


def test_format_preco():
    valor = Decimal("12.34")
    result = format_preco(valor)
    assert result == "12,34"


def test_format_preco_add_precision():
    valor = Decimal("1.2")
    result = format_preco(valor)
    assert result == "1,20"


def test_format_preco_remove_precision():
    valor = Decimal("49.9999")
    result = format_preco(valor)
    assert result == "50,00"


def test_format_preco_add_zeros():
    valor = Decimal("12")
    result = format_preco(valor)
    assert result == "12,00"


def test_format_preco_no_thousand_dot():
    valor = Decimal("123456.78")
    result = format_preco(valor)
    assert result == "123456,78"


def test_duracao_to_timedelta():
    duracao_str = "10:30"
    duracao = duracao_to_timedelta(duracao_str)
    assert duracao == timedelta(hours=10, minutes=30)


def test_duracao_to_timedelta_formato_errado():
    duracao_str = "10h30min"
    with pytest.raises(ValueError, match="Formato de duração inadequado"):
        duracao_to_timedelta(duracao_str)


def test_duracao_to_timedelta_faltando_digito():
    duracao_str = "1:30"
    with pytest.raises(ValueError, match="Formato de duração inadequado"):
        duracao_to_timedelta(duracao_str)


def test_duracao_to_timedelta_format_duracao():
    duracao_str = "01:03"
    duracao = duracao_to_timedelta(duracao_str)
    assert duracao == timedelta(hours=1, minutes=3)


def test_timedelta_to_duracao():
    duracao_timdelta = timedelta(hours=10, minutes=30)
    duracao, days_ahead = timedelta_to_duracao(duracao_timdelta)
    assert (duracao, days_ahead) == ("10:30", 0)


def test_timedelta_to_duracao_format_duracao():
    duracao_timdelta = timedelta(hours=1, minutes=30)
    duracao, days_ahead = timedelta_to_duracao(duracao_timdelta)
    assert (duracao, days_ahead) == ("01:30", 0)


def test_timedelta_to_duracao_mais_de_um_dia():
    duracao_timdelta = timedelta(hours=25, minutes=30)
    duracao, days_ahead = timedelta_to_duracao(duracao_timdelta)
    assert (duracao, days_ahead) == ("01:30", 1)

import jwt

JSON = {
    "exp": 1699399731,
    "iat": 1699378131,
    "nomeUsuarioComNumero": "1400 - Marketing Place Buser",
    "nus": "Marketing Place Buser",
    "sub": "1400",
}
TOKEN_1 = jwt.encode(JSON, key="my_secret_key", headers={"alg": "HS256"})

JSON_2 = {
    "exp": 1699389731,
    "iat": 1699378131,
    "nomeUsuarioComNumero": "2400 - Marketing Place Buser 2",
    "nus": "Marketing Place Buser 2",
    "sub": "2400",
}

TOKEN_2 = jwt.encode(JSON_2, key="my_secret_key", headers={"alg": "HS256"})


class MockBase:
    pass


class MockLogin(MockBase):
    @staticmethod
    def response(token=TOKEN_1):
        return {
            "tokenServico": {
                "tokenJwt": token,
                "refreshToken": None,
                "userFingerPrint": None,
            },
            "empresas": [
                {
                    "id": 1,
                    "nome": "VEXADO APP",
                    "email": "<EMAIL>",
                    "seVinculadaMercadoPago": False,
                    "telefones": [],
                    "urlImagem": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/0ebb7f95-2124-4e6c-b378-b88562297444"
                    ),
                    "cnpj": "00.000.000/0000-00",
                    "nomeFantasia": "VEXADO APP",
                    "endereco": {
                        "id": 1,
                        "cep": "70305900",
                        "logradouro": "SCS Quadra 1 Bloco M Lote 30",
                        "complemento": "sala 508",
                        "bairro": "Asa Sul",
                        "codigoCidade": None,
                        "localidade": None,
                        "uf": None,
                        "nomeCidade": None,
                        "numero": None,
                    },
                    "nomeDono": None,
                    "ie": None,
                    "cnae": None,
                    "im": None,
                    "tar": None,
                    "telefoneDono": None,
                    "nomeSocio": None,
                    "telefoneSocio": "23",
                    "nomeGerente": None,
                    "telefoneGerente": None,
                    "opcoesTrechoAlternativo": True,
                    "valorKgEncomenda": "0,00",
                    "multaRemarcacaoCancelamentoDentroPrazo": None,
                    "multaRemarcacaoCancelamentoForaPrazo": None,
                    "habilitadoEncomenda": False,
                    "habilitadoSite": False,
                    "habilitadoBpe": False,
                    "nomeCertificado": None,
                    "senhaCertificado": None,
                    "bloqueada": False,
                    "mensagemAvisoBilhete": None,
                    "grupo": None,
                    "habilitadoBuscaReservaComBpe": False,
                    "dataIniciaoVigenciaBuscaReservaComBpe": None,
                    "sites": [],
                    "tempoVisualizacaoDados": None,
                },
                {
                    "id": 8,
                    "nome": "EXPRESSO JK",
                    "email": "<EMAIL>",
                    "seVinculadaMercadoPago": False,
                    "telefones": ["(61) 98405-3285"],
                    "urlImagem": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/88b1f546-b100-4291-84bf-dbaea90b0050"
                    ),
                    "cnpj": "03.590.924/0001-83",
                    "nomeFantasia": "PLANALTO TRANSPORTADORA TURISTICA LTDA",
                    "endereco": {
                        "id": 15,
                        "cep": "72265705",
                        "logradouro": "SMC Quadra 2",
                        "complemento": "LOTES ",
                        "bairro": "Setor de Materiais de Construção (Ceilândia)",
                        "codigoCidade": 3,
                        "localidade": None,
                        "uf": "DF",
                        "nomeCidade": "Taguatinga",
                        "numero": None,
                    },
                    "nomeDono": "ANDRE LUIZ CAETANO MACIEL ",
                    "ie": None,
                    "cnae": None,
                    "im": None,
                    "tar": None,
                    "telefoneDono": None,
                    "nomeSocio": " LASARO COSTA DE MORAIS",
                    "telefoneSocio": None,
                    "nomeGerente": None,
                    "telefoneGerente": None,
                    "opcoesTrechoAlternativo": True,
                    "valorKgEncomenda": "0,00",
                    "multaRemarcacaoCancelamentoDentroPrazo": "00.00",
                    "multaRemarcacaoCancelamentoForaPrazo": "00.00",
                    "habilitadoEncomenda": True,
                    "habilitadoSite": False,
                    "habilitadoBpe": False,
                    "nomeCertificado": None,
                    "senhaCertificado": None,
                    "bloqueada": False,
                    "mensagemAvisoBilhete": None,
                    "grupo": None,
                    "habilitadoBuscaReservaComBpe": False,
                    "dataIniciaoVigenciaBuscaReservaComBpe": None,
                    "sites": [],
                    "tempoVisualizacaoDados": None,
                },
                {
                    "id": 5,
                    "nome": "GIRO TURISMO",
                    "email": "<EMAIL>",
                    "seVinculadaMercadoPago": False,
                    "telefones": ["(61) 99979-4850"],
                    "urlImagem": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
                    ),
                    "cnpj": "12.402.506/0001-06",
                    "nomeFantasia": "GIRO TURISMO LTDA",
                    "endereco": {
                        "id": 8,
                        "cep": "72549360",
                        "logradouro": "Quadra AC 319 Conjunto B",
                        "complemento": "LOTE",
                        "bairro": "Santa Maria",
                        "codigoCidade": 13,
                        "localidade": None,
                        "uf": "DF",
                        "nomeCidade": "Santa Maria",
                        "numero": None,
                    },
                    "nomeDono": "THALIS DE OLIVEIRA ROZENDO",
                    "ie": None,
                    "cnae": None,
                    "im": None,
                    "tar": None,
                    "telefoneDono": "(61) 99996-6351",
                    "nomeSocio": "WERTON ALESSANDRO LOPO",
                    "telefoneSocio": "(61) 98319-5344",
                    "nomeGerente": "MARIA EDUARDA BITTENCOURT",
                    "telefoneGerente": "(61) 98502-9654",
                    "opcoesTrechoAlternativo": True,
                    "valorKgEncomenda": "0,00",
                    "multaRemarcacaoCancelamentoDentroPrazo": "00.00",
                    "multaRemarcacaoCancelamentoForaPrazo": "00.00",
                    "habilitadoEncomenda": True,
                    "habilitadoSite": True,
                    "habilitadoBpe": True,
                    "nomeCertificado": None,
                    "senhaCertificado": None,
                    "bloqueada": False,
                    "mensagemAvisoBilhete": None,
                    "grupo": None,
                    "habilitadoBuscaReservaComBpe": False,
                    "dataIniciaoVigenciaBuscaReservaComBpe": None,
                    "sites": ["vexado", "giro"],
                    "tempoVisualizacaoDados": None,
                },
                {
                    "id": 42,
                    "nome": "HOMOLOG TRANSPORTE TESTE ",
                    "email": "<EMAIL>",
                    "seVinculadaMercadoPago": False,
                    "telefones": ["(61) 99999-9999"],
                    "urlImagem": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/bfb35e3a-57fb-4301-b880-54975c30d82e"
                    ),
                    "cnpj": "00.000.000/0000-01",
                    "nomeFantasia": "HOMOLOG TRANSPORTE TESTE ",
                    "endereco": {
                        "id": 69,
                        "cep": "72241606",
                        "logradouro": "QNP 15 Conjunto F",
                        "complemento": "C",
                        "bairro": "Ceilândia Norte (Ceilândia)",
                        "codigoCidade": None,
                        "localidade": None,
                        "uf": None,
                        "nomeCidade": None,
                        "numero": None,
                    },
                    "nomeDono": None,
                    "ie": None,
                    "cnae": None,
                    "im": None,
                    "tar": None,
                    "telefoneDono": None,
                    "nomeSocio": None,
                    "telefoneSocio": None,
                    "nomeGerente": None,
                    "telefoneGerente": None,
                    "opcoesTrechoAlternativo": True,
                    "valorKgEncomenda": "0,00",
                    "multaRemarcacaoCancelamentoDentroPrazo": "10.00",
                    "multaRemarcacaoCancelamentoForaPrazo": "10.00",
                    "habilitadoEncomenda": False,
                    "habilitadoSite": False,
                    "habilitadoBpe": True,
                    "nomeCertificado": None,
                    "senhaCertificado": None,
                    "bloqueada": False,
                    "mensagemAvisoBilhete": " ",
                    "grupo": None,
                    "habilitadoBuscaReservaComBpe": False,
                    "dataIniciaoVigenciaBuscaReservaComBpe": None,
                    "sites": [],
                    "tempoVisualizacaoDados": None,
                },
            ],
        }


class MockBuscarServico(MockBase):
    @staticmethod
    def request():
        return {"origem": 1, "destino": 2932, "data": "2021-11-11"}

    @staticmethod
    def response():
        return {
            "passagensIda": [
                {
                    "idItinerario": 211,
                    "dataPartida": "11/nov",
                    "horaPartida": "10:20",
                    "dataHoraPartida": "2021-11-11T10:20:00",
                    "dataHoraChegada": "2021-11-11T19:40:00",
                    "dataChegada": "11/nov",
                    "horaChegada": "19:40",
                    "tipoVeiculo": "LEITO",
                    "descricaoTipoVeiculo": "Leito",
                    "assentosDisponiveis": 15,
                    "idEmpresa": 7,
                    "nomeEmpresa": "GIRO TURISMO",
                    "preco": "125.25",
                    "precoMeia": "62.62",
                    "logoMarcaEmpresa": None,
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "andar": 2,
                    "taxaEmbarque": "0.50",
                    "precoANTT": "125.25",
                    "precoANTTMeia": "62.62",
                    "descRota": "Taguatinga à Itacarambi",
                    "descontoMaximoUsuarioRota": None,
                    "veiculo": "JJK-7954 (1110)",
                    "duracao": "9h 20m ",
                    "qtdDias": 0,
                    "veiculoId": 732,
                    "idRota": 1427,
                },
                {
                    "idItinerario": 21136,
                    "dataPartida": "11/nov",
                    "horaPartida": "10:20",
                    "dataHoraPartida": "2021-11-11T10:20:00",
                    "dataHoraChegada": "2021-11-11T19:40:00",
                    "dataChegada": "11/nov",
                    "horaChegada": "19:40",
                    "tipoVeiculo": "LEITO",
                    "descricaoTipoVeiculo": "Leito",
                    "assentosDisponiveis": 8,
                    "idEmpresa": 5,
                    "nomeEmpresa": "GIRO TURISMO",
                    "preco": "125.25",
                    "precoMeia": "62.62",
                    "logoMarcaEmpresa": None,
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "andar": 2,
                    "taxaEmbarque": "0.50",
                    "precoANTT": "125.25",
                    "precoANTTMeia": "62.62",
                    "descRota": "Taguatinga à Itacarambi",
                    "descontoMaximoUsuarioRota": None,
                    "veiculo": "JJK-7954 (1110)",
                    "duracao": "9h 20m ",
                    "qtdDias": 0,
                    "veiculoId": 543,
                    "idRota": 1427,
                },
                {
                    "idItinerario": 19599,
                    "dataPartida": "11/nov",
                    "horaPartida": "19:50",
                    "dataHoraPartida": "2021-11-11T19:50:00",
                    "dataHoraChegada": "2021-11-12T05:10:00",
                    "dataChegada": "12/nov",
                    "horaChegada": "05:10",
                    "tipoVeiculo": "LEITO",
                    "descricaoTipoVeiculo": "Leito",
                    "assentosDisponiveis": 9,
                    "idEmpresa": 5,
                    "nomeEmpresa": "GIRO TURISMO",
                    "preco": "125.25",
                    "precoMeia": "62.62",
                    "logoMarcaEmpresa": None,
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "andar": 2,
                    "taxaEmbarque": "0.50",
                    "precoANTT": "125.25",
                    "precoANTTMeia": "62.62",
                    "descRota": "Taguatinga à Itacarambi",
                    "descontoMaximoUsuarioRota": None,
                    "veiculo": "OVS-6422",
                    "duracao": "9h 20m ",
                    "qtdDias": 0,
                    "veiculoId": 653,
                    "idRota": 1427,
                },
                {
                    "idItinerario": 19553,
                    "dataPartida": "11/nov",
                    "horaPartida": "19:55",
                    "dataHoraPartida": "2021-11-11T19:55:00",
                    "dataHoraChegada": "2021-11-12T05:10:00",
                    "dataChegada": "12/nov",
                    "horaChegada": "05:10",
                    "tipoVeiculo": "SEMI LEITO",
                    "descricaoTipoVeiculo": "Semi Leito",
                    "assentosDisponiveis": 48,
                    "idEmpresa": 5,
                    "nomeEmpresa": "GIRO TURISMO",
                    "preco": "125.25",
                    "precoMeia": "62.62",
                    "logoMarcaEmpresa": None,
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "andar": 1,
                    "taxaEmbarque": "0.50",
                    "precoANTT": "125.25",
                    "precoANTTMeia": "62.62",
                    "descRota": "Taguatinga à Itacarambi",
                    "descontoMaximoUsuarioRota": None,
                    "veiculo": "OVS-6422",
                    "duracao": "9h 20m ",
                    "qtdDias": 0,
                    "veiculoId": 972,
                    "idRota": 1427,
                },
            ],
            "passagensRetorno": [],
            "datasIdaProximas": None,
            "datasRetornoProximas": None,
        }

    @staticmethod
    def response_nenhum_servico():
        return {"passagensIda": []}

    @staticmethod
    def response_for_factory():
        return {
            "passagensIda": [
                {
                    "idItinerario": 211,
                    "dataPartida": "11/nov",
                    "horaPartida": "10:20",
                    "dataHoraPartida": "2021-05-18T23:00:00",
                    "dataHoraChegada": "2021-05-18T19:40:00",
                    "dataChegada": "11/nov",
                    "horaChegada": "19:40",
                    "tipoVeiculo": "SEMI LEITO",
                    "descricaoTipoVeiculo": "Semi Leito",
                    "assentosDisponiveis": 15,
                    "idEmpresa": 5,
                    "nomeEmpresa": "GIRO TURISMO",
                    "preco": "125.25",
                    "precoMeia": "62.62",
                    "logoMarcaEmpresa": None,
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "andar": 2,
                    "taxaEmbarque": None,
                    "precoANTT": "125.25",
                    "precoANTTMeia": "62.62",
                    "descRota": "Taguatinga à Itacarambi",
                    "descontoMaximoUsuarioRota": None,
                    "veiculo": "JJK-7954 (1110)",
                    "duracao": "9h 20m ",
                    "qtdDias": 0,
                    "veiculoId": 657,
                    "idRota": 1427,
                }
            ],
            "passagensRetorno": [],
            "datasIdaProximas": None,
            "datasRetornoProximas": None,
        }


class GetPoltronasLivres:
    @staticmethod
    def request():
        return {"origem": "1", "destino": "2932", "servico": "21136"}

    @staticmethod
    def request_for_n_poltronas(n):
        return {
            "origem": "1",
            "destino": "2932",
            "servico": "21136",
            "NumeroPoltronas": n,
        }

    @staticmethod
    def response_poltronas_insuficientes():
        return [
            {
                "id": 16,
                "numero": 4,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 10,
                        "numero": 52,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793227,
                            "numero": 10,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                    {
                        "id": 11,
                        "numero": 55,
                        "ordem": 2,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793228,
                            "numero": 11,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                ],
            }
        ]

    @staticmethod
    def response():
        return [
            {
                "id": 16,
                "numero": 4,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 145,
                        "numero": 49,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793226,
                            "numero": 49,
                            "reservas": [
                                {
                                    "id": 170864,
                                    "idTrechoOrigem": 171,
                                    "descCidadeOrigem": "Brasília - DF",
                                    "descCidadeDestino": "Montes Claros - MG",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "idCidadeOrigem": None,
                                    "idCidadeDestino": None,
                                    "poltronaId": 793226,
                                    "desconto": "0.00",
                                    "passageiroDto": {
                                        "id": 170864,
                                        "nome": "Erich",
                                        "documentoComFoto": "11111",
                                        "cpfPassageiro": "418.613.728-50",
                                        "poltronaId": None,
                                        "itinerarioId": None,
                                        "numeroPoltrona": 49,
                                        "dtNascimento": "16/10/1995",
                                        "criancaDto": None,
                                        "sentido": None,
                                        "tipoEmissao": "NORMAL",
                                        "descTipoEmissao": "Normal",
                                        "nis": None,
                                        "validade": None,
                                        "renda": "0,00",
                                        "cargoProfissao": None,
                                        "numeroDeficiente": None,
                                        "opcaoDescontoMeia": "ANTT",
                                        "telefone": "(12) 99160-7865",
                                        "matricula": None,
                                        "comAcompanhante": False,
                                        "tipoDocumento": "RG",
                                    },
                                    "empresaId": None,
                                    "valor": "125.25",
                                    "dataHoraPartida": "11/11/2021 10:20",
                                    "dataVenda": "18/10/2021 13:48",
                                    "dataHoraEmbarque": None,
                                    "dataHoraDesembarque": None,
                                    "nomeEmpresa": "GIRO TURISMO",
                                    "cnpjEmpresa": "12.402.506/0001-06",
                                    "urlLogoEmpresa": "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d",
                                    "itinerarioId": 21136,
                                    "embarque": "RODOVIARIA DE TAGUATINGA",
                                    "taxaEmbarque": None,
                                    "rota": "Taguatinga-DF à Itacarambi-MG",
                                    "prefixoRota": "*********",
                                    "numeroPoltrona": 49,
                                    "descricaoTipoPreco": "Leito",
                                    "pedidoId": 116876,
                                    "valorTotalReserva": "125,25",
                                    "situacaoReserva": "APROVADO",
                                    "custoCancelamento": "0.00",
                                    "valorEstorno": "0",
                                    "custoRemarcacao": None,
                                    "descricaoFormasPagamento": "Dinheiro",
                                    "comissao": "0.00",
                                    "impostoRetido": "0.00",
                                    "seRemarcacao": None,
                                    "valorEmpresa": "125.25",
                                    "nomeVendedor": "buser integração ",
                                    "nomeAgencia": None,
                                    "aliquotaICMS": "12,00",
                                    "valorICMS": "15.03",
                                    "tarifa": "125.25",
                                    "idRemarcada": None,
                                    "estornoImpostoRetido": None,
                                    "estornoComissao": None,
                                    "observacao": None,
                                    "transmissaoBpe": None,
                                    "endereco": None,
                                    "outrosTributos": None,
                                    "outrosDescontos": None,
                                    "icmsDesconto": None,
                                }
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                    {
                        "id": 3204,
                        "numero": None,
                        "ordem": 1,
                        "tipoAssento": "PORTA",
                        "poltrona": None,
                    },
                    {
                        "id": 10,
                        "numero": 52,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793227,
                            "numero": 10,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                    {
                        "id": 11,
                        "numero": 55,
                        "ordem": 2,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793228,
                            "numero": 11,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                ],
            },
            {"id": 15, "numero": 3, "exibeCorredor": True, "assentos": []},
            {
                "id": 14,
                "numero": 2,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 142,
                        "numero": 48,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793223,
                            "numero": 48,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                    {
                        "id": 143,
                        "numero": 51,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793224,
                            "numero": 51,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                    {
                        "id": 144,
                        "numero": 54,
                        "ordem": 2,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793225,
                            "numero": 54,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                ],
            },
            {
                "id": 13,
                "numero": 1,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 139,
                        "numero": 47,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793220,
                            "numero": 47,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                    {
                        "id": 140,
                        "numero": 50,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793221,
                            "numero": 50,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                    {
                        "id": 141,
                        "numero": 53,
                        "ordem": 2,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 793222,
                            "numero": 53,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": [],
                        },
                    },
                ],
            },
        ]

    @staticmethod
    def response_classe_mista():
        return [
            {
                "id": 1318,
                "numero": 4,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 11802,
                        "numero": 3,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10324895,
                            "numero": 3,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_INDIVIDUAL",
                            "descricaoTipoCategoria": "Leito Individual",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 11802,
                        "numero": 4,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10324895,
                            "numero": 4,
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_INDIVIDUAL",
                            "descricaoTipoCategoria": "Leito Individual",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                ],
            },
            {
                "id": 1317,
                "numero": 3,
                "exibeCorredor": True,
                "assentos": [
                    {"id": 11782, "numero": 4, "ordem": 0, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {
                        "id": 11783,
                        "numero": 8,
                        "ordem": 1,
                        "tipoAssento": "REMOVIDO",
                        "poltrona": {
                            "id": 10324882,
                            "numero": 8,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO",
                            "descricaoTipoCategoria": "Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 11783,
                        "numero": 9,
                        "ordem": 1,
                        "tipoAssento": "REMOVIDO",
                        "poltrona": {
                            "id": 10324882,
                            "numero": 9,
                            "reservada": False,
                            "bloqueada": True,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO",
                            "descricaoTipoCategoria": "Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                ],
            },
        ]

    @staticmethod
    def response_completa():
        return [
            {
                "id": 1346,
                "numero": 4,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 12322,
                        "numero": 3,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987121,
                            "numero": 3,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12323,
                        "numero": 7,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987122,
                            "numero": 7,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12324, "numero": None, "ordem": 2, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12325, "numero": None, "ordem": 3, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {
                        "id": 12326,
                        "numero": 15,
                        "ordem": 4,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987123,
                            "numero": 15,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12327,
                        "numero": 19,
                        "ordem": 5,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987124,
                            "numero": 19,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12328,
                        "numero": 23,
                        "ordem": 6,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987125,
                            "numero": 23,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12329,
                        "numero": 27,
                        "ordem": 7,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987126,
                            "numero": 27,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12330,
                        "numero": 31,
                        "ordem": 8,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987127,
                            "numero": 31,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Itajaí-SC",
                                    "descCidadeDestino": "Santa Maria do Pará-PA",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "ALDAIR AVIZ DA SILVA "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                }
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12331,
                        "numero": 35,
                        "ordem": 9,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987128,
                            "numero": 35,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Itajaí-SC",
                                    "descCidadeDestino": "Belém-PA",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "WASHINGTON DA SILVA ALFAIA "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                }
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12332,
                        "numero": 39,
                        "ordem": 10,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987129,
                            "numero": 39,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12333,
                        "numero": 43,
                        "ordem": 11,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987130,
                            "numero": 43,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12334,
                        "numero": 47,
                        "ordem": 12,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987131,
                            "numero": 47,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12335, "numero": None, "ordem": 13, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12336, "numero": None, "ordem": 14, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12337, "numero": None, "ordem": 15, "tipoAssento": "PORTA", "poltrona": None},
                    {
                        "id": 12338,
                        "numero": 51,
                        "ordem": 16,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987132,
                            "numero": 51,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12339,
                        "numero": 55,
                        "ordem": 17,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987133,
                            "numero": 55,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12340,
                        "numero": 59,
                        "ordem": 18,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987134,
                            "numero": 59,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Itajaí-SC",
                                    "descCidadeDestino": "Castanhal-PA",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "ELIZABETE DA SILVA SOUSA "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                }
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12341,
                        "numero": 63,
                        "ordem": 19,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987135,
                            "numero": 63,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Curitiba-PR",
                                    "descCidadeDestino": "Santa Maria do Pará-PA",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "MARIA SANDRA DA SILVA  LOPES "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                }
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12342, "numero": 67, "ordem": 20, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12343, "numero": 71, "ordem": 21, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12344, "numero": 75, "ordem": 22, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12345, "numero": 79, "ordem": 23, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12346, "numero": 83, "ordem": 24, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12347, "numero": 87, "ordem": 25, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12348, "numero": 91, "ordem": 26, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12349, "numero": 95, "ordem": 27, "tipoAssento": "REMOVIDO", "poltrona": None},
                ],
            },
            {
                "id": 1345,
                "numero": 3,
                "exibeCorredor": True,
                "assentos": [
                    {
                        "id": 12294,
                        "numero": 4,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987110,
                            "numero": 4,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12295,
                        "numero": 8,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987111,
                            "numero": 8,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12296, "numero": None, "ordem": 2, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12297, "numero": None, "ordem": 3, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {
                        "id": 12298,
                        "numero": 16,
                        "ordem": 4,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987112,
                            "numero": 16,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12299,
                        "numero": 20,
                        "ordem": 5,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987113,
                            "numero": 20,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12300,
                        "numero": 24,
                        "ordem": 6,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987114,
                            "numero": 24,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12301,
                        "numero": 28,
                        "ordem": 7,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987115,
                            "numero": 28,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12302,
                        "numero": 32,
                        "ordem": 8,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987116,
                            "numero": 32,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12303,
                        "numero": 36,
                        "ordem": 9,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987117,
                            "numero": 36,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12304,
                        "numero": 40,
                        "ordem": 10,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987118,
                            "numero": 40,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12305,
                        "numero": 44,
                        "ordem": 11,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987119,
                            "numero": 44,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12306,
                        "numero": 48,
                        "ordem": 12,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987120,
                            "numero": 48,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12307, "numero": None, "ordem": 13, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12308, "numero": None, "ordem": 14, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12309, "numero": None, "ordem": 15, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12310, "numero": 52, "ordem": 16, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12311, "numero": 56, "ordem": 17, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12312, "numero": 60, "ordem": 18, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12313, "numero": 64, "ordem": 19, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12314, "numero": 68, "ordem": 20, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12315, "numero": 72, "ordem": 21, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12316, "numero": 76, "ordem": 22, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12317, "numero": 80, "ordem": 23, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12318, "numero": 84, "ordem": 24, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12319, "numero": 88, "ordem": 25, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12320, "numero": 92, "ordem": 26, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12321, "numero": 96, "ordem": 27, "tipoAssento": "REMOVIDO", "poltrona": None},
                ],
            },
            {
                "id": 1344,
                "numero": 2,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 12265,
                        "numero": 2,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987093,
                            "numero": 2,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Florianópolis-SC",
                                    "descCidadeDestino": "Agudos do Sul-PR",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "Joelson da Silva Lima"},
                                    "nomeVendedor": "Marketing Place Buser",
                                    "nomeAgencia": None,
                                },
                                {
                                    "descCidadeOrigem": "Agudos do Sul-PR",
                                    "descCidadeDestino": "São Paulo-SP",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "Joelson da Silva Lima"},
                                    "nomeVendedor": "Marketing Place Buser",
                                    "nomeAgencia": None,
                                },
                                {
                                    "descCidadeOrigem": "Franca-SP",
                                    "descCidadeDestino": "Santa Maria do Pará-PA",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "RENE COSTA DA ROSA"},
                                    "nomeVendedor": "Tales Peres de Oliveira",
                                    "nomeAgencia": None,
                                },
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12266,
                        "numero": 6,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987094,
                            "numero": 6,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12267,
                        "numero": 10,
                        "ordem": 2,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987095,
                            "numero": 10,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12268,
                        "numero": 12,
                        "ordem": 3,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987096,
                            "numero": 12,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12269,
                        "numero": 14,
                        "ordem": 4,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987097,
                            "numero": 14,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12270,
                        "numero": 18,
                        "ordem": 5,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987098,
                            "numero": 18,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12271,
                        "numero": 22,
                        "ordem": 6,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987099,
                            "numero": 22,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12272,
                        "numero": 26,
                        "ordem": 7,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987100,
                            "numero": 26,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12273,
                        "numero": 30,
                        "ordem": 8,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987101,
                            "numero": 30,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12274,
                        "numero": 34,
                        "ordem": 9,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987102,
                            "numero": 34,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12275,
                        "numero": 38,
                        "ordem": 10,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987103,
                            "numero": 38,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12276,
                        "numero": 42,
                        "ordem": 11,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987104,
                            "numero": 42,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12277,
                        "numero": 46,
                        "ordem": 12,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987105,
                            "numero": 46,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12278, "numero": None, "ordem": 13, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12279, "numero": None, "ordem": 14, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12280, "numero": None, "ordem": 15, "tipoAssento": "PORTA", "poltrona": None},
                    {
                        "id": 12281,
                        "numero": 50,
                        "ordem": 16,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987106,
                            "numero": 50,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12282,
                        "numero": 54,
                        "ordem": 17,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987107,
                            "numero": 54,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12283,
                        "numero": 58,
                        "ordem": 18,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987108,
                            "numero": 58,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Curitiba-PR",
                                    "descCidadeDestino": "Ananindeua-PA",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "ELOA SOFIA PEREIRA COSTA "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                }
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12284,
                        "numero": 62,
                        "ordem": 19,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987109,
                            "numero": 62,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Curitiba-PR",
                                    "descCidadeDestino": "Itapecerica da Serra-SP",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "AMANDA BEATRIZ PEREIRA RABELO "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                },
                                {
                                    "descCidadeOrigem": "Itapecerica da Serra-SP",
                                    "descCidadeDestino": "Goiânia-GO",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "AMANDA BEATRIZ PEREIRA RABELO "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                },
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12285, "numero": 66, "ordem": 20, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12286, "numero": 70, "ordem": 21, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12287, "numero": 74, "ordem": 22, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12288, "numero": 78, "ordem": 23, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12289, "numero": 82, "ordem": 24, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12290, "numero": 86, "ordem": 25, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12291, "numero": 90, "ordem": 26, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12292, "numero": 94, "ordem": 27, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12293, "numero": 98, "ordem": 28, "tipoAssento": "REMOVIDO", "poltrona": None},
                ],
            },
            {
                "id": 1343,
                "numero": 1,
                "exibeCorredor": False,
                "assentos": [
                    {
                        "id": 12236,
                        "numero": 1,
                        "ordem": 0,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987076,
                            "numero": 1,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Florianópolis-SC",
                                    "descCidadeDestino": "Agudos do Sul-PR",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "Heide Manuele Barbosa Oliveira Lima"},
                                    "nomeVendedor": "Marketing Place Buser",
                                    "nomeAgencia": None,
                                },
                                {
                                    "descCidadeOrigem": "Agudos do Sul-PR",
                                    "descCidadeDestino": "São Paulo-SP",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "Heide Manuele Barbosa Oliveira Lima"},
                                    "nomeVendedor": "Marketing Place Buser",
                                    "nomeAgencia": None,
                                },
                                {
                                    "descCidadeOrigem": "Franca-SP",
                                    "descCidadeDestino": "Santa Maria do Pará-PA",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "WESLEY COSTA NASCIMENTO"},
                                    "nomeVendedor": "Tales Peres de Oliveira",
                                    "nomeAgencia": None,
                                },
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12237,
                        "numero": 5,
                        "ordem": 1,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987077,
                            "numero": 5,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12238,
                        "numero": 9,
                        "ordem": 2,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987078,
                            "numero": 9,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12239,
                        "numero": 11,
                        "ordem": 3,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987079,
                            "numero": 11,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12240,
                        "numero": 13,
                        "ordem": 4,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987080,
                            "numero": 13,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12241,
                        "numero": 17,
                        "ordem": 5,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987081,
                            "numero": 17,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12242,
                        "numero": 21,
                        "ordem": 6,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987082,
                            "numero": 21,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12243,
                        "numero": 25,
                        "ordem": 7,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987083,
                            "numero": 25,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12244,
                        "numero": 29,
                        "ordem": 8,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987084,
                            "numero": 29,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12245,
                        "numero": 33,
                        "ordem": 9,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987085,
                            "numero": 33,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12246,
                        "numero": 37,
                        "ordem": 10,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987086,
                            "numero": 37,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12247,
                        "numero": 41,
                        "ordem": 11,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987087,
                            "numero": 41,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12248,
                        "numero": 45,
                        "ordem": 12,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987088,
                            "numero": 45,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "SEMI_LEITO",
                            "descricaoTipoCategoria": "Semi Leito",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12249, "numero": None, "ordem": 13, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12250, "numero": None, "ordem": 14, "tipoAssento": "PORTA", "poltrona": None},
                    {"id": 12251, "numero": None, "ordem": 15, "tipoAssento": "PORTA", "poltrona": None},
                    {
                        "id": 12252,
                        "numero": 49,
                        "ordem": 16,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987089,
                            "numero": 49,
                            "reservada": False,
                            "bloqueada": True,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [
                                {
                                    "id": 1042121,
                                    "numeroPoltrona": 49,
                                    "trechoOrigem": "Itajaí - SC",
                                    "trechoDestino": "Guarulhos - SP",
                                    "dataBloqueio": "04/06/2025 10:46",
                                    "usuarioResponsavel": "n/a",
                                    "motivo": "n/a",
                                }
                            ],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12253,
                        "numero": 53,
                        "ordem": 17,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987090,
                            "numero": 53,
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12254,
                        "numero": 57,
                        "ordem": 18,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987091,
                            "numero": 57,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Curitiba-PR",
                                    "descCidadeDestino": "Ananindeua-PA",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "CRISTIANE PINTO PEREIRA "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                }
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {
                        "id": 12255,
                        "numero": 61,
                        "ordem": 19,
                        "tipoAssento": "NORMAL",
                        "poltrona": {
                            "id": 10987092,
                            "numero": 61,
                            "reservas": [
                                {
                                    "descCidadeOrigem": "Curitiba-PR",
                                    "descCidadeDestino": "Itapecerica da Serra-SP",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "VANESSA PINTO  PEREIRA "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                },
                                {
                                    "descCidadeOrigem": "Itapecerica da Serra-SP",
                                    "descCidadeDestino": "Goiânia-GO",
                                    "descCidadeOrigemAlternativa": None,
                                    "descCidadeDestinoAlternativa": None,
                                    "passageiroDto": {"nome": "VANESSA PINTO  PEREIRA "},
                                    "nomeVendedor": "Nayara de Lima Costa",
                                    "nomeAgencia": None,
                                },
                            ],
                            "reservada": True,
                            "bloqueada": False,
                            "dataSaida": None,
                            "tipoCategoria": "LEITO_ESPECIAL_1",
                            "descricaoTipoCategoria": "Leito especial 1",
                            "bloqueios": [],
                            "bloqueoPorTipoCategoria": False,
                            "agendamentoViagemComPassageiro": False,
                            "agendamentoViagemPassageiroDto": None,
                            "andar": 0,
                        },
                    },
                    {"id": 12256, "numero": 65, "ordem": 20, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12257, "numero": 69, "ordem": 21, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12258, "numero": 73, "ordem": 22, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12259, "numero": 77, "ordem": 23, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12260, "numero": 81, "ordem": 24, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12261, "numero": 85, "ordem": 25, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12262, "numero": 89, "ordem": 26, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12263, "numero": 93, "ordem": 27, "tipoAssento": "REMOVIDO", "poltrona": None},
                    {"id": 12264, "numero": 97, "ordem": 28, "tipoAssento": "REMOVIDO", "poltrona": None},
                ],
            },
        ]


class MockCancelarReservas(MockBase):
    @staticmethod
    def response_error():
        return {
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "erroCancelamentoReserva",
                    "mensagem": (
                        "ViolacaoDominio{tipo=ERRO, codigo='tempoCancelamentoExpirado',                        "
                        " mensagem='A reserva só pode ser cancelada até 3 horas antes do embarque.Verifique se há"
                        " alguma reserva que não atende a este requisito.'}"
                    ),
                }
            ]
        }

    @staticmethod
    def response_nao_pode_cancelar():
        return {
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "reservaNaoPodeSerCancelada",
                    "mensagem": ("As seguintes reservas não atendem aos requisitos para cancelamento: Kakaroto"),
                }
            ]
        }

    @staticmethod
    def response_pedido_nao_encontrado():
        return {
            "violacoes": [
                {
                    "codigo": "cancelarReservas",
                    "mensagem": "O pedido não foi encontrado, ou o pedido não pertence a empresa! ",
                    "tipo": "ERRO",
                }
            ]
        }


class MockErrors(MockBase):
    @staticmethod
    def response_no_json():
        return "<div>Erro Desconhecido</div>"

    @staticmethod
    def response_unauthorized():
        return {
            "timestamp": "2021-12-14T19:48:24.104+0000",
            "status": 401,
            "error": "Unauthorized",
            "message": "Full authentication is required to access this resource",
            "path": "/passagem/origem/1/destino/2/dataIda/2021-12-21",
        }

    @staticmethod
    def response_erro_interno():
        return {"mensagem": "Ocorreu um erro interno", "sigla": None}


class MockBloquearPoltrona(MockBase):
    @staticmethod
    def request():
        return {
            "poltronaId": 7373862,
            "trechoOrigemId": 39869,
            "trechoDestinoId": 39871,
            "itinerarioId": 236039,
        }

    @staticmethod
    def response():
        return {
            "id": 570555,
            "poltronaId": 7373852,
            "uuid": "6c5729b1-db8a-4a46-a397-2e942d1a8ecf",
            "preco": "620.89",
            "precoANTT": "1241.78",
            "precoMeia": "310.44",
            "precoANTTMeia": "620.89",
            "taxaEmbarque": "0",
            "pedagio": "0",
            "tarifaSeguro": "0",
            "garantiaPrecoId": 597934,
        }

    @staticmethod
    def response_error():
        return {
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "BloqueioPoltronaTemporarioDto",
                    "mensagem": (
                        "Não foi possível reservar a poltrona, porque já foi bloqueado temporariamente para venda!"
                    ),
                }
            ]
        }


class MockEfetuarReserva(MockBase):
    @staticmethod
    def request():
        return {
            "cidadeOrigem": 1,
            "cidadeDestino": 2932,
            "empresaId": 5,
            "reservas": [
                {
                    "passageiroDto": {
                        "poltronaId": 10,
                        "nome": "Claudio",
                        "documentoComFoto": "22222",
                        "cpfPassageiro": "506.361.200-82",
                        "valor": 125.25,
                        "tipoEmissao": "NORMAL",
                        "tipoDocumento": "RG",
                        "telefone": "(12) 99160-7865",
                    },
                    "poltronaId": 10,
                    "itinerarioId": 21136,
                    "idCidadeOrigem": 1,
                    "idCidadeDestino": 2932,
                    "valor": 125.25,
                },
                {
                    "passageiroDto": {
                        "poltronaId": 793228,
                        "nome": "Braulio",
                        "documentoComFoto": "33333",
                        "cpfPassageiro": "389.441.830-31",
                        "valor": 125.25,
                        "tipoEmissao": "NORMAL",
                        "tipoDocumento": "RG",
                        "telefone": "(12) 99160-7865",
                    },
                    "poltronaId": 793228,
                    "itinerarioId": 21136,
                    "idCidadeOrigem": 1,
                    "idCidadeDestino": 2932,
                    "valor": 125.25,
                },
            ],
            "formasPagamentoDto": [{"tipo": "Dinheiro", "valor": 250.50, "quantidadeParcelas": 1}],
            "dadosCompradorDto": {
                "cpfComprador": "506.361.200-82",
                "nomeComprador": "Claudio",
                "telefoneComprador": "(12) 99160-7865",
            },
        }

    @staticmethod
    def response():
        return 116896

    @staticmethod
    def response_error():
        return {"mensagem": "Ocorreu um erro interno", "sigla": None}

    @staticmethod
    def response_error_poltrona_ja_reservada(
        name_passageiro="Passageiro com Poltrona Bloqueada",
    ):
        return {
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "poltronaReservada",
                    "mensagem": (
                        f"Poltrona já reservada para o passageiro {name_passageiro} no trecho Curitiba-PR -> São"
                        " Paulo-SP"
                    ),
                }
            ]
        }

    @staticmethod
    def response_error_preco_nao_cadastrado():
        return {
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "erroReserva",
                    "mensagem": (
                        "ViolacaoDominio{tipo=ERRO, codigo='precoNaoCadastrado', mensagem='Preço do tipo Leito não"
                        " cadastrado para o trecho São Paulo-SP -> São Paulo-SP'}"
                    ),
                }
            ]
        }

    @staticmethod
    def response_error_valor_reserva_errado():
        return {
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "erroReserva",
                    "mensagem": (
                        "ViolacaoDominio{tipo=ERRO, codigo='valorReservaErrado', mensagem='Valor da reserva informado"
                        " está errado. '}"
                    ),
                }
            ]
        }


class MockCadastrarPreco(MockBase):
    @staticmethod
    def response_preco_ja_existe():
        return {
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "erroReserva",
                    "mensagem": "ViolacaoDominio{tipo=ERRO, codigo='precoJaExistte', mensagem='Preço já existe'}",
                }
            ]
        }


class MockRecuperarPedido(MockBase):
    @staticmethod
    def response_empresa_bloqueada():
        return {"mensagem": "Empresa 495 bloqueada.", "sigla": None}

    @staticmethod
    def response_unica_passagem():
        return {
            "id": 116896,
            "data": "26/10/2021 11:09",
            "dataViagem": "11/11/2021 10:20",
            "reservas": [
                {
                    "id": 170887,
                    "idTrechoOrigem": 171,
                    "descCidadeOrigem": "Brasília - DF",
                    "descCidadeDestino": "Montes Claros - MG",
                    "descCidadeOrigemAlternativa": None,
                    "descCidadeDestinoAlternativa": None,
                    "idCidadeOrigem": None,
                    "idCidadeDestino": None,
                    "poltronaId": 793227,
                    "desconto": "0.00",
                    "passageiroDto": {
                        "id": 170887,
                        "nome": "Braulio",
                        "documentoComFoto": "33333",
                        "cpfPassageiro": "389.441.830-31",
                        "poltronaId": None,
                        "itinerarioId": None,
                        "numeroPoltrona": 55,
                        "dtNascimento": None,
                        "criancaDto": None,
                        "sentido": None,
                        "tipoEmissao": "NORMAL",
                        "descTipoEmissao": "Normal",
                        "nis": None,
                        "validade": None,
                        "renda": "0,00",
                        "cargoProfissao": None,
                        "numeroDeficiente": None,
                        "opcaoDescontoMeia": None,
                        "telefone": "(12) 99160-7865",
                        "matricula": None,
                        "comAcompanhante": False,
                        "tipoDocumento": "RG",
                    },
                    "empresaId": None,
                    "valor": "125.25",
                    "dataHoraPartida": "11/11/2021 10:20",
                    "dataVenda": "26/10/2021 11:09",
                    "dataHoraEmbarque": None,
                    "dataHoraDesembarque": None,
                    "nomeEmpresa": "GIRO TURISMO",
                    "cnpjEmpresa": "12.402.506/0001-06",
                    "urlLogoEmpresa": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
                    ),
                    "itinerarioId": 21136,
                    "inscricaoEstadual": "133223091115",
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "taxaEmbarque": None,
                    "taxaServico": "4.23",
                    "rota": "Taguatinga-DF à Itacarambi-MG",
                    "prefixoRota": "*********",
                    "numeroPoltrona": 55,
                    "descricaoTipoPreco": "Leito",
                    "pedidoId": 116896,
                    "valorTotalReserva": "125,25",
                    "situacaoReserva": "APROVADO",
                    "custoCancelamento": "0.00",
                    "valorEstorno": "0",
                    "custoRemarcacao": None,
                    "descricaoFormasPagamento": "Dinheiro",
                    "comissao": "0.00",
                    "impostoRetido": "0.00",
                    "seRemarcacao": None,
                    "valorEmpresa": "125.25",
                    "nomeVendedor": "buser integração",
                    "nomeAgencia": None,
                    "aliquotaICMS": "12,00",
                    "valorICMS": "15.03",
                    "tarifa": "125.25",
                    "idRemarcada": None,
                    "estornoImpostoRetido": None,
                    "estornoComissao": None,
                    "observacao": None,
                    "transmissaoBpe": {
                        "qrCod": "https://dfe-portal.svrs.rs.gov.br/Bpe/QrCode?chBPe=53211012402506000106630000017088711341082995&tpAmb=2",
                        "protocoloTransmissao": "131200000185981",
                        "chaveAcesso": "53211012402506000106630000017088711341082995",
                        "dataAutorizacao": "2020-06-17T17:54:33-03:00",
                        "urlConsulta": "https://dfe-portal.svrs.rs.gov.br/Bpe/Consulta",
                        "emitidoEmContigencia": True,
                    },
                    "codigoQrcodeEmbarqueMonitrip": "41250508336161000162630000197851011",
                    "endereco": {
                        "id": None,
                        "cep": "58010-150",
                        "logradouro": "Rua Francisco Londres",
                        "localidade": "João Pessoa",
                        "complemento": "***",
                        "bairro": "Varadouro",
                        "uf": "PB",
                        "unidade": None,
                        "ibge": None,
                        "numero": None,
                        "codigoCidade": None,
                        "nomeCidade": None,
                        "cepNumerico": "58010150",
                    },
                    "outrosTributos": 1.99,
                    "outrosDescontos": None,
                    "icmsDesconto": None,
                }
            ],
            "idCliente": None,
            "situacaoPagamento": "Aprovado",
            "situacaoPedido": "Aprovado",
            "pontoVenda": "Terminal de vendas",
            "valor": 250.50,
            "itinerario": "Brasília-DF à Montes Claros-MG",
            "trecho": "Brasília à Montes Claros",
            "origem": "Brasília-DF",
            "destino": "Montes Claros-MG",
            "nomeEmpresa": "GIRO TURISMO",
            "nomeFantasiaEmpresa": "GIRO TURISMO LTDA",
            "urlLogoEmpresa": (
                "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
            ),
            "rota": "Taguatinga-DF à Itacarambi-MG",
            "nomeComprador": "Leandro",
            "cpfCnpjComprador": "506.361.200-82",
            "nomeVendedor": "buser integração ",
            "telefoneVendedor": "(61) 00000-0000",
            "embarque": "RODOVIARIA DE TAGUATINGA",
            "historicos": [
                {
                    "dataHora": "26/10/2021 11:09",
                    "descricaoSituacao": "Aprovado",
                    "situacaoPedido": "APROVADO",
                    "nomeUsuario": "buser integração ",
                    "observacao": None,
                }
            ],
            "ultimoHistorico": {
                "dataHora": "26/10/2021 11:09",
                "descricaoSituacao": "Aprovado",
                "situacaoPedido": "APROVADO",
                "nomeUsuario": "buser integração ",
                "observacao": None,
            },
            "formasPagamentoDto": [
                {
                    "idFormaPagamento": 117093,
                    "tipo": "Dinheiro",
                    "urlComprovante": "",
                    "base64Comprovante": None,
                    "numeroComprovante": None,
                    "quantidadeParcelas": 1,
                    "nomeComprovante": "",
                    "valor": 250.50,
                    "taxaParcelamento": None,
                }
            ],
            "sePodeCancelar": True,
            "sePodeAlterarFormaPagamento": True,
            "sePodeAlterarPassageiros": True,
            "idPagamento": 116885,
            "valorEstorno": "0",
            "valorTotalDesconto": "0.00",
            "outrosTributos": None,
            "outrosDescontos": None,
            "icmsDesconto": None,
            "icmsCobrado": None,
            "nomeAgencia": None,
            "idEmpresa": 5,
        }

    @staticmethod
    def response():
        return {
            "id": 116896,
            "data": "26/10/2021 11:09",
            "dataViagem": "11/11/2021 10:20",
            "reservas": [
                {
                    "id": 170887,
                    "idTrechoOrigem": 171,
                    "descCidadeOrigem": "Brasília - DF",
                    "descCidadeDestino": "Montes Claros - MG",
                    "descCidadeOrigemAlternativa": None,
                    "descCidadeDestinoAlternativa": None,
                    "idCidadeOrigem": None,
                    "idCidadeDestino": None,
                    "poltronaId": 793227,
                    "desconto": "0.00",
                    "passageiroDto": {
                        "id": 170887,
                        "nome": "Braulio",
                        "documentoComFoto": "33333",
                        "cpfPassageiro": "389.441.830-31",
                        "poltronaId": None,
                        "itinerarioId": None,
                        "numeroPoltrona": 55,
                        "dtNascimento": None,
                        "criancaDto": None,
                        "sentido": None,
                        "tipoEmissao": "NORMAL",
                        "descTipoEmissao": "Normal",
                        "nis": None,
                        "validade": None,
                        "renda": "0,00",
                        "cargoProfissao": None,
                        "numeroDeficiente": None,
                        "opcaoDescontoMeia": None,
                        "telefone": "(12) 99160-7865",
                        "matricula": None,
                        "comAcompanhante": False,
                        "tipoDocumento": "RG",
                    },
                    "empresaId": None,
                    "valor": "125.25",
                    "dataHoraPartida": "11/11/2021 10:20",
                    "dataVenda": "26/10/2021 11:09",
                    "dataHoraEmbarque": None,
                    "dataHoraDesembarque": None,
                    "nomeEmpresa": "GIRO TURISMO",
                    "cnpjEmpresa": "12.402.506/0001-06",
                    "urlLogoEmpresa": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
                    ),
                    "itinerarioId": 21136,
                    "inscricaoEstadual": "133223091115",
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "taxaEmbarque": None,
                    "taxaServico": "4.23",
                    "rota": "Taguatinga-DF à Itacarambi-MG",
                    "prefixoRota": "*********",
                    "numeroPoltrona": 55,
                    "descricaoTipoPreco": "Leito",
                    "pedidoId": 116896,
                    "valorTotalReserva": "125,25",
                    "situacaoReserva": "APROVADO",
                    "custoCancelamento": "0.00",
                    "valorEstorno": "0",
                    "custoRemarcacao": None,
                    "descricaoFormasPagamento": "Dinheiro",
                    "comissao": "0.00",
                    "impostoRetido": "0.00",
                    "seRemarcacao": None,
                    "valorEmpresa": "125.25",
                    "nomeVendedor": "buser integração",
                    "nomeAgencia": None,
                    "aliquotaICMS": "12,00",
                    "valorICMS": "15.03",
                    "tarifa": "125.25",
                    "idRemarcada": None,
                    "estornoImpostoRetido": None,
                    "estornoComissao": None,
                    "observacao": None,
                    "transmissaoBpe": {
                        "qrCod": "https://dfe-portal.svrs.rs.gov.br/Bpe/QrCode?chBPe=53211012402506000106630000017088711341082995&tpAmb=2",
                        "protocoloTransmissao": "131200000185981",
                        "chaveAcesso": "53211012402506000106630000017088711341082995",
                        "dataAutorizacao": "2020-06-17T17:54:33-03:00",
                        "urlConsulta": "https://dfe-portal.svrs.rs.gov.br/Bpe/Consulta",
                        "emitidoEmContigencia": True,
                    },
                    "codigoQrcodeEmbarqueMonitrip": "41250508336161000162630000197851011",
                    "endereco": {
                        "id": None,
                        "cep": "58010-150",
                        "logradouro": "Rua Francisco Londres",
                        "localidade": "João Pessoa",
                        "complemento": "***",
                        "bairro": "Varadouro",
                        "uf": "PB",
                        "unidade": None,
                        "ibge": None,
                        "numero": None,
                        "codigoCidade": None,
                        "nomeCidade": None,
                        "cepNumerico": "58010150",
                    },
                    "outrosTributos": 1.99,
                    "outrosDescontos": None,
                    "icmsDesconto": None,
                },
                {
                    "id": 170886,
                    "idTrechoOrigem": 171,
                    "descCidadeOrigem": "Brasília - DF",
                    "descCidadeDestino": "Montes Claros - MG",
                    "descCidadeOrigemAlternativa": None,
                    "descCidadeDestinoAlternativa": None,
                    "idCidadeOrigem": None,
                    "idCidadeDestino": None,
                    "poltronaId": 793228,
                    "desconto": "0.00",
                    "passageiroDto": {
                        "id": 170886,
                        "nome": "Claudio",
                        "documentoComFoto": "22222",
                        "cpfPassageiro": "506.361.200-82",
                        "poltronaId": None,
                        "itinerarioId": None,
                        "numeroPoltrona": 52,
                        "dtNascimento": None,
                        "criancaDto": None,
                        "sentido": None,
                        "tipoEmissao": "NORMAL",
                        "descTipoEmissao": "Normal",
                        "nis": None,
                        "validade": None,
                        "renda": "0,00",
                        "cargoProfissao": None,
                        "numeroDeficiente": None,
                        "opcaoDescontoMeia": None,
                        "telefone": "(12) 99160-7865",
                        "matricula": None,
                        "comAcompanhante": False,
                        "tipoDocumento": "RG",
                    },
                    "empresaId": None,
                    "valor": "125.25",
                    "dataHoraPartida": "11/11/2021 10:20",
                    "dataVenda": "26/10/2021 11:09",
                    "dataHoraEmbarque": None,
                    "dataHoraDesembarque": None,
                    "nomeEmpresa": "GIRO TURISMO",
                    "cnpjEmpresa": "12.402.506/0001-06",
                    "urlLogoEmpresa": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
                    ),
                    "itinerarioId": 21136,
                    "inscricaoEstadual": "133223091115",
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "taxaEmbarque": None,
                    "taxaServico": "4.23",
                    "rota": "Taguatinga-DF à Itacarambi-MG",
                    "prefixoRota": "*********",
                    "numeroPoltrona": 52,
                    "descricaoTipoPreco": "Leito",
                    "pedidoId": 116896,
                    "valorTotalReserva": "125,25",
                    "situacaoReserva": "APROVADO",
                    "custoCancelamento": "0.00",
                    "valorEstorno": "0",
                    "custoRemarcacao": None,
                    "descricaoFormasPagamento": "Dinheiro",
                    "comissao": "0.00",
                    "impostoRetido": "0.00",
                    "seRemarcacao": None,
                    "valorEmpresa": "125.25",
                    "nomeVendedor": "buser integração ",
                    "nomeAgencia": None,
                    "aliquotaICMS": "12,00",
                    "valorICMS": "15.03",
                    "tarifa": "125.25",
                    "idRemarcada": None,
                    "estornoImpostoRetido": None,
                    "estornoComissao": None,
                    "observacao": None,
                    "transmissaoBpe": {
                        "qrCod": "https://dfe-portal.svrs.rs.gov.br/Bpe/QrCode?chBPe=53211012402506000106630000017088611998403000&tpAmb=2",
                        "protocoloTransmissao": None,
                        "chaveAcesso": "53211012402506000106630000017088611998403000",
                        "dataAutorizacao": None,
                        "urlConsulta": "https://dfe-portal.svrs.rs.gov.br/Bpe/Consulta",
                        "emitidoEmContigencia": False,
                    },
                    "codigoQrcodeEmbarqueMonitrip": "41250508336161000162630000197851011",
                    "endereco": {
                        "id": None,
                        "cep": "58010-150",
                        "logradouro": "Rua Francisco Londres",
                        "localidade": "João Pessoa",
                        "complemento": "***",
                        "bairro": "Varadouro",
                        "uf": "PB",
                        "unidade": None,
                        "ibge": None,
                        "numero": None,
                        "codigoCidade": None,
                        "nomeCidade": None,
                        "cepNumerico": "58010150",
                    },
                    "outrosTributos": None,
                    "outrosDescontos": None,
                    "icmsDesconto": None,
                },
            ],
            "idCliente": None,
            "situacaoPagamento": "Aprovado",
            "situacaoPedido": "Aprovado",
            "pontoVenda": "Terminal de vendas",
            "valor": 250.50,
            "itinerario": "Brasília-DF à Montes Claros-MG",
            "trecho": "Brasília à Montes Claros",
            "origem": "Brasília-DF",
            "destino": "Montes Claros-MG",
            "nomeEmpresa": "GIRO TURISMO",
            "nomeFantasiaEmpresa": "GIRO TURISMO LTDA",
            "urlLogoEmpresa": (
                "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
            ),
            "rota": "Taguatinga-DF à Itacarambi-MG",
            "nomeComprador": "Leandro",
            "cpfCnpjComprador": "506.361.200-82",
            "nomeVendedor": "buser integração ",
            "telefoneVendedor": "(61) 00000-0000",
            "embarque": "RODOVIARIA DE TAGUATINGA",
            "historicos": [
                {
                    "dataHora": "26/10/2021 11:09",
                    "descricaoSituacao": "Aprovado",
                    "situacaoPedido": "APROVADO",
                    "nomeUsuario": "buser integração ",
                    "observacao": None,
                }
            ],
            "ultimoHistorico": {
                "dataHora": "26/10/2021 11:09",
                "descricaoSituacao": "Aprovado",
                "situacaoPedido": "APROVADO",
                "nomeUsuario": "buser integração ",
                "observacao": None,
            },
            "formasPagamentoDto": [
                {
                    "idFormaPagamento": 117093,
                    "tipo": "Dinheiro",
                    "urlComprovante": "",
                    "base64Comprovante": None,
                    "numeroComprovante": None,
                    "quantidadeParcelas": 1,
                    "nomeComprovante": "",
                    "valor": 250.50,
                    "taxaParcelamento": None,
                }
            ],
            "sePodeCancelar": True,
            "sePodeAlterarFormaPagamento": True,
            "sePodeAlterarPassageiros": True,
            "idPagamento": 116885,
            "valorEstorno": "0",
            "valorTotalDesconto": "0.00",
            "outrosTributos": None,
            "outrosDescontos": None,
            "icmsDesconto": None,
            "icmsCobrado": None,
            "nomeAgencia": None,
            "idEmpresa": 5,
        }

    @staticmethod
    def response_conexao_mesma_poltrona():
        return {
            "id": 116896,
            "data": "15/10/2022 18:37",
            "dataViagem": None,
            "reservas": [
                {
                    "id": 1131873,
                    "idTrechoOrigem": 34470,
                    "descCidadeOrigem": "Brasília-DF",
                    "descCidadeDestino": "São Paulo-SP",
                    "descCidadeOrigemAlternativa": None,
                    "descCidadeDestinoAlternativa": None,
                    "idCidadeOrigem": None,
                    "idCidadeDestino": None,
                    "poltronaId": 793227,
                    "desconto": "0.00",
                    "passageiroDto": {
                        "id": 1131875,
                        "nome": "Matheus Teste",
                        "documentoComFoto": "16.198.063-6",
                        "cpfPassageiro": "86700436370",
                        "poltronaId": None,
                        "itinerarioId": None,
                        "numeroPoltrona": 55,
                        "dtNascimento": None,
                        "criancaDto": None,
                        "sentido": None,
                        "tipoEmissao": "NORMAL",
                        "descTipoEmissao": "Normal",
                        "nis": None,
                        "validade": None,
                        "renda": "0,00",
                        "cargoProfissao": None,
                        "numeroDeficiente": None,
                        "opcaoDescontoMeia": None,
                        "telefone": "12912349876",
                        "matricula": None,
                        "comAcompanhante": False,
                        "tipoDocumento": "RG",
                        "vendaSeguro": False,
                    },
                    "empresaId": None,
                    "valor": "200.08",
                    "dataHoraPartida": "01/12/2022 13:00",
                    "dataVenda": "15/10/2022 18:37",
                    "dataHoraEmbarque": None,
                    "dataHoraChegada": "02/12/2022 06:40",
                    "dataHoraDesembarque": None,
                    "nomeEmpresa": "TRANSPORTE COLETIVO BRASIL LTDA",
                    "cnpjEmpresa": "05376934001460",
                    "urlLogoEmpresa": (
                        "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/8f0ac9b3-2243-4db5-ac92-c266526ff64a"
                    ),
                    "itinerarioId": 152819,
                    "inscricaoEstadual": "0764825800383",
                    "embarque": "Terminal Rodoviário",
                    "taxaEmbarque": "0.00",
                    "pedagio": "0.00",
                    "seguro": None,
                    "taxaServico": None,
                    "rota": "Fortaleza-CE à São Bernardo do Campo-SP",
                    "prefixoRota": "08943700",
                    "numeroPoltrona": 55,
                    "descricaoTipoPreco": "Leito",
                    "pedidoId": 116896,
                    "valorTotalReserva": "200.08",
                    "situacaoReserva": "APROVADO",
                    "custoCancelamento": "0.00",
                    "valorEstorno": "0",
                    "custoRemarcacao": None,
                    "descricaoFormasPagamento": "Dinheiro",
                    "comissao": "0.00",
                    "impostoRetido": "32.51",
                    "seRemarcacao": None,
                    "valorEmpresa": "167.57",
                    "nomeVendedor": "1224 - BUSER 01",
                    "nomeAgencia": None,
                    "aliquotaICMS": "12,00",
                    "valorICMS": "24.01",
                    "tarifa": "200.08",
                    "idRemarcada": None,
                    "estornoImpostoRetido": None,
                    "estornoComissao": None,
                    "observacao": None,
                    "transmissaoBpe": {
                        "qrCod": "https://dfe-portal.svrs.rs.gov.br/bpe/qrCode?chBPe=53221005376934001460630000113187311549122723&tpAmb=1",
                        "protocoloTransmissao": "353220001607417",
                        "chaveAcesso": "53221005376934001460630000113187311549122723",
                        "dataAutorizacao": "2022-10-15T18:37:45-03:00",
                        "urlConsulta": "https://dfe-portal.svrs.rs.gov.br/BPE/Consulta",
                        "emitidoEmContigencia": False,
                    },
                    "endereco": {
                        "id": None,
                        "cep": "70610-635",
                        "logradouro": "SMAS Trecho",
                        "localidade": "Brasília",
                        "complemento": "GUICHE MB-13- SETOR COMPLEMENTARES",
                        "bairro": "Setores Complementares",
                        "uf": "DF",
                        "unidade": None,
                        "ibge": None,
                        "numero": "04",
                        "codigoCidade": None,
                        "nomeCidade": None,
                        "cepNumerico": "70610635",
                    },
                    "outrosTributos": None,
                    "outrosDescontos": None,
                    "icmsDesconto": None,
                    "nomeResponsavelEmbarque": None,
                    "nomeResponsavelDesembarque": None,
                    "tarifaSeguro": "0.00",
                    "numeroSeguro": None,
                    "numeroBilheteRodoviaria": None,
                    "temEstornoSeguro": False,
                },
                {
                    "id": 1131874,
                    "idTrechoOrigem": 34473,
                    "descCidadeOrigem": "São Paulo-SP",
                    "descCidadeDestino": "Rio de Janeiro-RJ",
                    "descCidadeOrigemAlternativa": None,
                    "descCidadeDestinoAlternativa": None,
                    "idCidadeOrigem": None,
                    "idCidadeDestino": None,
                    "poltronaId": 793227,
                    "desconto": "0.00",
                    "passageiroDto": {
                        "id": 1131876,
                        "nome": "Matheus Teste",
                        "documentoComFoto": "16.198.063-6",
                        "cpfPassageiro": "86700436370",
                        "poltronaId": None,
                        "itinerarioId": None,
                        "numeroPoltrona": 55,
                        "dtNascimento": None,
                        "criancaDto": None,
                        "sentido": None,
                        "tipoEmissao": "NORMAL",
                        "descTipoEmissao": "Normal",
                        "nis": None,
                        "validade": None,
                        "renda": "0,00",
                        "cargoProfissao": None,
                        "numeroDeficiente": None,
                        "opcaoDescontoMeia": None,
                        "telefone": "12912349876",
                        "matricula": None,
                        "comAcompanhante": False,
                        "tipoDocumento": "RG",
                        "vendaSeguro": False,
                    },
                    "empresaId": None,
                    "valor": "200.08",
                    "dataHoraPartida": "02/12/2022 06:40",
                    "dataVenda": "15/10/2022 18:37",
                    "dataHoraEmbarque": None,
                    "dataHoraChegada": "02/12/2022 12:50",
                    "dataHoraDesembarque": None,
                    "nomeEmpresa": "TRANSPORTE COLETIVO BRASIL LTDA",
                    "cnpjEmpresa": "05376934000731",
                    "urlLogoEmpresa": (
                        "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/8f0ac9b3-2243-4db5-ac92-c266526ff64a"
                    ),
                    "itinerarioId": 152819,
                    "inscricaoEstadual": "133223091115",
                    "embarque": "Terminal Rodoviário",
                    "taxaEmbarque": "0.00",
                    "pedagio": "0.00",
                    "seguro": None,
                    "taxaServico": None,
                    "rota": "Porto Seguro-BA à Sena Madureira-AC",
                    "prefixoRota": "22940300",
                    "numeroPoltrona": 55,
                    "descricaoTipoPreco": "Leito",
                    "pedidoId": 116896,
                    "valorTotalReserva": "200.08",
                    "situacaoReserva": "APROVADO",
                    "custoCancelamento": "0.00",
                    "valorEstorno": "0",
                    "custoRemarcacao": None,
                    "descricaoFormasPagamento": "Dinheiro",
                    "comissao": "0.00",
                    "impostoRetido": "32.51",
                    "seRemarcacao": None,
                    "valorEmpresa": "167.57",
                    "nomeVendedor": "1224 - BUSER 01",
                    "nomeAgencia": None,
                    "aliquotaICMS": "12,00",
                    "valorICMS": "24.01",
                    "tarifa": "200.08",
                    "idRemarcada": None,
                    "estornoImpostoRetido": None,
                    "estornoComissao": None,
                    "observacao": None,
                    "transmissaoBpe": {
                        "qrCod": "https://bpe.fazenda.sp.gov.br/BPe/qrcode?chBPe=35221005376934000731630000113187411421773192&tpAmb=1",
                        "protocoloTransmissao": "135220125838721",
                        "chaveAcesso": "35221005376934000731630000113187411421773192",
                        "dataAutorizacao": "2022-10-15T18:37:46-03:00",
                        "urlConsulta": "https://bpe.fazenda.sp.gov.br/bpe",
                        "emitidoEmContigencia": False,
                    },
                    "endereco": {
                        "id": None,
                        "cep": "03050-030",
                        "logradouro": "Rua Paulo Afonso",
                        "localidade": "São Paulo",
                        "complemento": "186",
                        "bairro": "Brás",
                        "uf": "SP",
                        "unidade": None,
                        "ibge": None,
                        "numero": None,
                        "codigoCidade": None,
                        "nomeCidade": None,
                        "cepNumerico": "03050030",
                    },
                    "outrosTributos": None,
                    "outrosDescontos": None,
                    "icmsDesconto": None,
                    "nomeResponsavelEmbarque": None,
                    "nomeResponsavelDesembarque": None,
                    "tarifaSeguro": "0.00",
                    "numeroSeguro": None,
                    "numeroBilheteRodoviaria": None,
                    "temEstornoSeguro": False,
                },
            ],
            "idCliente": None,
            "situacaoPagamento": "Aprovado",
            "situacaoPedido": "Aprovado",
            "pontoVenda": "Terminal de vendas",
            "valor": 400.16,
            "itinerario": None,
            "trecho": None,
            "origem": None,
            "destino": None,
            "nomeEmpresa": "TRANSPORTE COLETIVO BRASIL LTDA",
            "nomeFantasiaEmpresa": "BUSER - TCB SOLUCAO TRANSPORTES E TURISMO",
            "urlLogoEmpresa": (
                "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/8f0ac9b3-2243-4db5-ac92-c266526ff64a"
            ),
            "rota": None,
            "nomeComprador": "Diego Miguel da Paz",
            "cpfCnpjComprador": "867.004.363-70",
            "nomeVendedor": "1224 - BUSER 01",
            "telefoneVendedor": "(11) 11111-1111",
            "embarque": None,
            "historicos": [
                {
                    "dataHora": "15/10/2022 18:37",
                    "descricaoSituacao": "Aprovado",
                    "situacaoPedido": "APROVADO",
                    "nomeUsuario": "1224 - BUSER 01",
                    "observacao": None,
                }
            ],
            "ultimoHistorico": {
                "dataHora": "15/10/2022 18:37",
                "descricaoSituacao": "Aprovado",
                "situacaoPedido": "APROVADO",
                "nomeUsuario": "1224 - BUSER 01",
                "observacao": None,
            },
            "formasPagamentoDto": [
                {
                    "idFormaPagamento": 735184,
                    "tipo": "Dinheiro",
                    "urlComprovante": "",
                    "base64Comprovante": None,
                    "numeroComprovante": None,
                    "quantidadeParcelas": 1,
                    "nomeComprovante": "",
                    "valor": 400.16,
                    "taxaParcelamento": None,
                }
            ],
            "sePodeCancelar": True,
            "sePodeAlterarFormaPagamento": True,
            "sePodeAlterarPassageiros": True,
            "idPagamento": 733939,
            "valorEstorno": "0",
            "valorTotalDesconto": "0.00",
            "outrosTributos": None,
            "outrosDescontos": None,
            "icmsDesconto": None,
            "icmsCobrado": None,
            "nomeAgencia": None,
            "delimitacao": None,
            "nomeAgenciaComNumero": None,
            "idEmpresa": 354,
        }

    def response_outras_poltronas(self):
        response = self.response()
        response["reservas"][0]["poltronaId"] = 793228
        response["reservas"][0]["numeroPoltrona"] = 11
        response["reservas"][1]["poltronaId"] = 793220
        response["reservas"][1]["numeroPoltrona"] = 47
        return response

    @staticmethod
    def response_multiple_two_passengers():
        return {
            "id": 116896,
            "data": "26/10/2021 11:09",
            "dataViagem": "11/11/2021 10:20",
            "reservas": [
                {
                    "id": 170887,
                    "idTrechoOrigem": 171,
                    "descCidadeOrigem": "Brasília - DF",
                    "descCidadeDestino": "Montes Claros - MG",
                    "descCidadeOrigemAlternativa": None,
                    "descCidadeDestinoAlternativa": None,
                    "idCidadeOrigem": None,
                    "idCidadeDestino": None,
                    "poltronaId": 793227,
                    "desconto": "0.00",
                    "passageiroDto": {
                        "id": 170887,
                        "nome": "Braulio",
                        "documentoComFoto": "33333",
                        "cpfPassageiro": "389.441.830-31",
                        "poltronaId": None,
                        "itinerarioId": None,
                        "numeroPoltrona": 55,
                        "dtNascimento": None,
                        "criancaDto": None,
                        "sentido": None,
                        "tipoEmissao": "NORMAL",
                        "descTipoEmissao": "Normal",
                        "nis": None,
                        "validade": None,
                        "renda": "0,00",
                        "cargoProfissao": None,
                        "numeroDeficiente": None,
                        "opcaoDescontoMeia": None,
                        "telefone": "(12) 99160-7865",
                        "matricula": None,
                        "comAcompanhante": False,
                        "tipoDocumento": "RG",
                    },
                    "empresaId": None,
                    "valor": "125.25",
                    "dataHoraPartida": "11/11/2021 10:20",
                    "dataVenda": "26/10/2021 11:09",
                    "dataHoraEmbarque": None,
                    "dataHoraDesembarque": None,
                    "nomeEmpresa": "GIRO TURISMO",
                    "cnpjEmpresa": "12.402.506/0001-06",
                    "urlLogoEmpresa": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
                    ),
                    "itinerarioId": 21136,
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "taxaEmbarque": None,
                    "taxaServico": "4.23",
                    "rota": "Taguatinga-DF à Itacarambi-MG",
                    "prefixoRota": "*********",
                    "numeroPoltrona": 55,
                    "descricaoTipoPreco": "Leito",
                    "pedidoId": 116896,
                    "valorTotalReserva": "125,25",
                    "situacaoReserva": "APROVADO",
                    "custoCancelamento": "0.00",
                    "valorEstorno": "0",
                    "custoRemarcacao": None,
                    "descricaoFormasPagamento": "Dinheiro",
                    "comissao": "0.00",
                    "impostoRetido": "0.00",
                    "seRemarcacao": None,
                    "valorEmpresa": "125.25",
                    "nomeVendedor": "buser integração ",
                    "nomeAgencia": None,
                    "aliquotaICMS": "12,00",
                    "valorICMS": "15.03",
                    "tarifa": "125.25",
                    "idRemarcada": None,
                    "estornoImpostoRetido": None,
                    "estornoComissao": None,
                    "observacao": None,
                    "transmissaoBpe": {
                        "qrCod": "https://dfe-portal.svrs.rs.gov.br/Bpe/QrCode?chBPe=53211012402506000106630000017088711341082995&tpAmb=2",
                        "protocoloTransmissao": "131200000185981",
                        "chaveAcesso": "53211012402506000106630000017088711341082995",
                        "dataAutorizacao": "2020-06-17T17:54:33-03:00",
                        "urlConsulta": "https://dfe-portal.svrs.rs.gov.br/Bpe/Consulta",
                        "emitidoEmContigencia": True,
                    },
                    "endereco": None,
                    "outrosTributos": None,
                    "outrosDescontos": None,
                    "icmsDesconto": None,
                },
                {
                    "id": 170886,
                    "idTrechoOrigem": 171,
                    "descCidadeOrigem": "Brasília - DF",
                    "descCidadeDestino": "Montes Claros - MG",
                    "descCidadeOrigemAlternativa": None,
                    "descCidadeDestinoAlternativa": None,
                    "idCidadeOrigem": None,
                    "idCidadeDestino": None,
                    "poltronaId": 793228,
                    "desconto": "0.00",
                    "passageiroDto": {
                        "id": 170886,
                        "nome": "Claudio",
                        "documentoComFoto": "22222",
                        "cpfPassageiro": "506.361.200-82",
                        "poltronaId": None,
                        "itinerarioId": None,
                        "numeroPoltrona": 52,
                        "dtNascimento": None,
                        "criancaDto": None,
                        "sentido": None,
                        "tipoEmissao": "NORMAL",
                        "descTipoEmissao": "Normal",
                        "nis": None,
                        "validade": None,
                        "renda": "0,00",
                        "cargoProfissao": None,
                        "numeroDeficiente": None,
                        "opcaoDescontoMeia": None,
                        "telefone": "(12) 99160-7865",
                        "matricula": None,
                        "comAcompanhante": False,
                        "tipoDocumento": "RG",
                    },
                    "empresaId": None,
                    "valor": "125.25",
                    "dataHoraPartida": "11/11/2021 10:20",
                    "dataVenda": "26/10/2021 11:09",
                    "dataHoraEmbarque": None,
                    "dataHoraDesembarque": None,
                    "nomeEmpresa": "GIRO TURISMO",
                    "cnpjEmpresa": "12.402.506/0001-06",
                    "urlLogoEmpresa": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
                    ),
                    "itinerarioId": 21136,
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "taxaEmbarque": None,
                    "rota": "Taguatinga-DF à Itacarambi-MG",
                    "prefixoRota": "*********",
                    "numeroPoltrona": 52,
                    "descricaoTipoPreco": "Leito",
                    "pedidoId": 116896,
                    "valorTotalReserva": "125,25",
                    "situacaoReserva": "APROVADO",
                    "custoCancelamento": "0.00",
                    "valorEstorno": "0",
                    "custoRemarcacao": None,
                    "descricaoFormasPagamento": "Dinheiro",
                    "comissao": "0.00",
                    "impostoRetido": "0.00",
                    "seRemarcacao": None,
                    "valorEmpresa": "125.25",
                    "nomeVendedor": "buser integração ",
                    "nomeAgencia": None,
                    "aliquotaICMS": "12,00",
                    "valorICMS": "15.03",
                    "tarifa": "125.25",
                    "idRemarcada": None,
                    "estornoImpostoRetido": None,
                    "estornoComissao": None,
                    "observacao": None,
                    "transmissaoBpe": {
                        "qrCod": "https://dfe-portal.svrs.rs.gov.br/Bpe/QrCode?chBPe=53211012402506000106630000017088611998403000&tpAmb=2",
                        "protocoloTransmissao": None,
                        "chaveAcesso": "53211012402506000106630000017088611998403000",
                        "dataAutorizacao": None,
                        "urlConsulta": "https://dfe-portal.svrs.rs.gov.br/Bpe/Consulta",
                        "emitidoEmContigencia": True,
                    },
                    "endereco": None,
                    "outrosTributos": None,
                    "outrosDescontos": None,
                    "icmsDesconto": None,
                },
            ],
            "idCliente": None,
            "situacaoPagamento": "Aprovado",
            "situacaoPedido": "Aprovado",
            "pontoVenda": "Terminal de vendas",
            "valor": 250.50,
            "itinerario": "Brasília-DF à Montes Claros-MG",
            "trecho": "Brasília à Montes Claros",
            "origem": "Brasília-DF",
            "destino": "Montes Claros-MG",
            "nomeEmpresa": "GIRO TURISMO",
            "nomeFantasiaEmpresa": "GIRO TURISMO LTDA",
            "urlLogoEmpresa": (
                "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
            ),
            "rota": "Taguatinga-DF à Itacarambi-MG",
            "nomeComprador": "Leandro",
            "cpfCnpjComprador": "506.361.200-82",
            "nomeVendedor": "buser integração ",
            "telefoneVendedor": "(61) 00000-0000",
            "embarque": "RODOVIARIA DE TAGUATINGA",
            "historicos": [
                {
                    "dataHora": "26/10/2021 11:09",
                    "descricaoSituacao": "Aprovado",
                    "situacaoPedido": "APROVADO",
                    "nomeUsuario": "buser integração ",
                    "observacao": None,
                }
            ],
            "ultimoHistorico": {
                "dataHora": "26/10/2021 11:09",
                "descricaoSituacao": "Aprovado",
                "situacaoPedido": "APROVADO",
                "nomeUsuario": "buser integração ",
                "observacao": None,
            },
            "formasPagamentoDto": [
                {
                    "idFormaPagamento": 117093,
                    "tipo": "Dinheiro",
                    "urlComprovante": "",
                    "base64Comprovante": None,
                    "numeroComprovante": None,
                    "quantidadeParcelas": 1,
                    "nomeComprovante": "",
                    "valor": 250.50,
                    "taxaParcelamento": None,
                }
            ],
            "sePodeCancelar": True,
            "sePodeAlterarFormaPagamento": True,
            "sePodeAlterarPassageiros": True,
            "idPagamento": 116885,
            "valorEstorno": "0",
            "valorTotalDesconto": "0.00",
            "outrosTributos": None,
            "outrosDescontos": None,
            "icmsDesconto": None,
            "icmsCobrado": None,
            "nomeAgencia": None,
            "idEmpresa": 5,
        }

    @staticmethod
    def response_multiple_one_passenger():
        return {
            "id": 116896,
            "data": "26/10/2021 11:09",
            "dataViagem": "11/11/2021 10:20",
            "reservas": [
                {
                    "id": 170887,
                    "idTrechoOrigem": 171,
                    "descCidadeOrigem": "Brasília - DF",
                    "descCidadeDestino": "Montes Claros - MG",
                    "descCidadeOrigemAlternativa": None,
                    "descCidadeDestinoAlternativa": None,
                    "idCidadeOrigem": None,
                    "idCidadeDestino": None,
                    "poltronaId": 793220,
                    "desconto": "0.00",
                    "passageiroDto": {
                        "id": 170887,
                        "nome": "Braulio",
                        "documentoComFoto": "33333",
                        "cpfPassageiro": "389.441.830-31",
                        "poltronaId": None,
                        "itinerarioId": None,
                        "numeroPoltrona": 55,
                        "dtNascimento": None,
                        "criancaDto": None,
                        "sentido": None,
                        "tipoEmissao": "NORMAL",
                        "descTipoEmissao": "Normal",
                        "nis": None,
                        "validade": None,
                        "renda": "0,00",
                        "cargoProfissao": None,
                        "numeroDeficiente": None,
                        "opcaoDescontoMeia": None,
                        "telefone": "(12) 99160-7865",
                        "matricula": None,
                        "comAcompanhante": False,
                        "tipoDocumento": "RG",
                    },
                    "empresaId": None,
                    "valor": "125.25",
                    "dataHoraPartida": "11/11/2021 10:20",
                    "dataVenda": "26/10/2021 11:09",
                    "dataHoraEmbarque": None,
                    "dataHoraDesembarque": None,
                    "nomeEmpresa": "GIRO TURISMO",
                    "cnpjEmpresa": "12.402.506/0001-06",
                    "urlLogoEmpresa": (
                        "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
                    ),
                    "itinerarioId": 21136,
                    "embarque": "RODOVIARIA DE TAGUATINGA",
                    "taxaEmbarque": None,
                    "taxaServico": "4.23",
                    "rota": "Taguatinga-DF à Itacarambi-MG",
                    "prefixoRota": "*********",
                    "numeroPoltrona": 55,
                    "descricaoTipoPreco": "Leito",
                    "pedidoId": 116896,
                    "valorTotalReserva": "125,25",
                    "situacaoReserva": "APROVADO",
                    "custoCancelamento": "0.00",
                    "valorEstorno": "0",
                    "custoRemarcacao": None,
                    "descricaoFormasPagamento": "Dinheiro",
                    "comissao": "0.00",
                    "impostoRetido": "0.00",
                    "seRemarcacao": None,
                    "valorEmpresa": "125.25",
                    "nomeVendedor": "buser integração ",
                    "nomeAgencia": None,
                    "aliquotaICMS": "12,00",
                    "valorICMS": "15.03",
                    "tarifa": "125.25",
                    "idRemarcada": None,
                    "estornoImpostoRetido": None,
                    "estornoComissao": None,
                    "observacao": None,
                    "transmissaoBpe": {
                        "qrCod": "https://dfe-portal.svrs.rs.gov.br/Bpe/QrCode?chBPe=53211012402506000106630000017088711341082995&tpAmb=2",
                        "protocoloTransmissao": "131200000185981",
                        "chaveAcesso": "53211012402506000106630000017088711341082995",
                        "dataAutorizacao": "2020-06-17T17:54:33-03:00",
                        "urlConsulta": "https://dfe-portal.svrs.rs.gov.br/Bpe/Consulta",
                        "emitidoEmContigencia": True,
                    },
                    "endereco": None,
                    "outrosTributos": None,
                    "outrosDescontos": None,
                    "icmsDesconto": None,
                }
            ],
            "idCliente": None,
            "situacaoPagamento": "Aprovado",
            "situacaoPedido": "Aprovado",
            "pontoVenda": "Terminal de vendas",
            "valor": 250.50,
            "itinerario": "Brasília-DF à Montes Claros-MG",
            "trecho": "Brasília à Montes Claros",
            "origem": "Brasília-DF",
            "destino": "Montes Claros-MG",
            "nomeEmpresa": "GIRO TURISMO",
            "nomeFantasiaEmpresa": "GIRO TURISMO LTDA",
            "urlLogoEmpresa": (
                "https://imagens-vexado-sandbox.s3-sa-east-1.amazonaws.com/191aafe6-dad5-4563-a8ff-01354b190e0d"
            ),
            "rota": "Taguatinga-DF à Itacarambi-MG",
            "nomeComprador": "Leandro",
            "cpfCnpjComprador": "506.361.200-82",
            "nomeVendedor": "buser integração ",
            "telefoneVendedor": "(61) 00000-0000",
            "embarque": "RODOVIARIA DE TAGUATINGA",
            "historicos": [
                {
                    "dataHora": "26/10/2021 11:09",
                    "descricaoSituacao": "Aprovado",
                    "situacaoPedido": "APROVADO",
                    "nomeUsuario": "buser integração ",
                    "observacao": None,
                }
            ],
            "ultimoHistorico": {
                "dataHora": "26/10/2021 11:09",
                "descricaoSituacao": "Aprovado",
                "situacaoPedido": "APROVADO",
                "nomeUsuario": "buser integração ",
                "observacao": None,
            },
            "formasPagamentoDto": [
                {
                    "idFormaPagamento": 117093,
                    "tipo": "Dinheiro",
                    "urlComprovante": "",
                    "base64Comprovante": None,
                    "numeroComprovante": None,
                    "quantidadeParcelas": 1,
                    "nomeComprovante": "",
                    "valor": 250.50,
                    "taxaParcelamento": None,
                }
            ],
            "sePodeCancelar": True,
            "sePodeAlterarFormaPagamento": True,
            "sePodeAlterarPassageiros": True,
            "idPagamento": 116885,
            "valorEstorno": "0",
            "valorTotalDesconto": "0.00",
            "outrosTributos": None,
            "outrosDescontos": None,
            "icmsDesconto": None,
            "icmsCobrado": None,
            "nomeAgencia": None,
            "idEmpresa": 5,
        }


class MockRecuperarItinerario(MockBase):
    @staticmethod
    def response():
        return {
            "id": 27884,
            "dataPartida": "2021-12-30",
            "horaSaida": "22:22",
            "dataHoraPartida": "30/12/2021 22:22",
            "dataHoraChegada": "31/12/2021 12:03",
            "veiculoDto": {},
            "rotaDto": {
                "id": 10,
                "empresaDto": None,
                "cidadeOrigem": {
                    "id": 3,
                    "uf": "DF",
                    "descricaoUf": "Distrito Federal",
                    "nome": "Taguatinga",
                    "nomeComUf": "Taguatinga - DF",
                    "codigoIbge": "5300108",
                },
                "cidadeDestino": {
                    "id": 2796,
                    "uf": "MG",
                    "descricaoUf": "Minas Gerais",
                    "nome": "Itacarambi",
                    "nomeComUf": "Itacarambi - MG",
                    "codigoIbge": "3132107",
                },
                "trechosDto": [
                    {
                        "id": 171,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 1,
                            "uf": "DF",
                            "descricaoUf": "Distrito Federal",
                            "nome": "Brasília",
                            "nomeComUf": "Brasília - DF",
                            "codigoIbge": "5300108",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 1,
                        "duracao": "00:00",
                        "pontoEmbarque": "RODOVIARIA DE TAGUATINGA",
                        "taxaEmbarque": "0",
                        "quilometragem": "10",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 173,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 272,
                            "uf": "GO",
                            "descricaoUf": "Goiás",
                            "nome": "Valparaíso de Goiás",
                            "nomeComUf": "Valparaíso de Goiás - GO",
                            "codigoIbge": "5221858",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 2,
                        "duracao": "00:40",
                        "pontoEmbarque": "RODOVIARIA INTERESTADUAL DE BRASILIA-DF",
                        "taxaEmbarque": "0",
                        "quilometragem": "90,50",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 174,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 166,
                            "uf": "GO",
                            "descricaoUf": "Goiás",
                            "nome": "Luziânia",
                            "nomeComUf": "Luziânia - GO",
                            "codigoIbge": "5212501",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 3,
                        "duracao": "00:05",
                        "pontoEmbarque": "TERMINAL VALPARAISO",
                        "taxaEmbarque": "0",
                        "quilometragem": "100",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 175,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 102,
                            "uf": "GO",
                            "descricaoUf": "Goiás",
                            "nome": "Cristalina",
                            "nomeComUf": "Cristalina - GO",
                            "codigoIbge": "5206206",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 4,
                        "duracao": "01:00",
                        "pontoEmbarque": "POSTO LUZIANIA",
                        "taxaEmbarque": "0",
                        "quilometragem": "100",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 176,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2983,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Paracatu",
                            "nomeComUf": "Paracatu - MG",
                            "codigoIbge": "3147006",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 5,
                        "duracao": "01:20",
                        "pontoEmbarque": "RODOVIARIA DE CRISTALINA",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 177,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2843,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "João Pinheiro",
                            "nomeComUf": "João Pinheiro - MG",
                            "codigoIbge": "3136306",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 6,
                        "duracao": "01:00",
                        "pontoEmbarque": "POSTO TREVO 2",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 178,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 3031,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Pirapora",
                            "nomeComUf": "Pirapora - MG",
                            "codigoIbge": "3151206",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 7,
                        "duracao": "03:00",
                        "pontoEmbarque": "POSTO ",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 179,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2932,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Montes Claros",
                            "nomeComUf": "Montes Claros - MG",
                            "codigoIbge": "3143302",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 8,
                        "duracao": "02:00",
                        "pontoEmbarque": "RODOVIARIA PIRAPORA",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 180,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2916,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Mirabela",
                            "nomeComUf": "Mirabela - MG",
                            "codigoIbge": "3142007",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 9,
                        "duracao": "01:20",
                        "pontoEmbarque": "RODOVIARIA DE MONTES CLAROS",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 181,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2832,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Japonvar",
                            "nomeComUf": "Japonvar - MG",
                            "codigoIbge": "3135357",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 10,
                        "duracao": "00:30",
                        "pontoEmbarque": "POSTO",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 182,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2874,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Lontra",
                            "nomeComUf": "Lontra - MG",
                            "codigoIbge": "3138658",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 11,
                        "duracao": "00:10",
                        "pontoEmbarque": "POSTO",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 183,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 3006,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Pedras de Maria da Cruz",
                            "nomeComUf": "Pedras de Maria da Cruz - MG",
                            "codigoIbge": "3149150",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 12,
                        "duracao": "00:30",
                        "pontoEmbarque": "POSTO",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 184,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2830,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Januária",
                            "nomeComUf": "Januária - MG",
                            "codigoIbge": "3135209",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 13,
                        "duracao": "00:30",
                        "pontoEmbarque": "PONTO DE APOIO",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 185,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2796,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Itacarambi",
                            "nomeComUf": "Itacarambi - MG",
                            "codigoIbge": "3132107",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 14,
                        "duracao": "01:00",
                        "pontoEmbarque": "RODOVIARIA DE JANUARIA-MG",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 244,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2796,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Itacarambi",
                            "nomeComUf": "Itacarambi - MG",
                            "codigoIbge": "3132107",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 15,
                        "duracao": "00:00",
                        "pontoEmbarque": "RODOVIARIA DE ITACARAMBI-MG",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                ],
                "conexoes": [],
                "opcoesTrechoAlternativo": None,
                "prefixo": None,
                "prestador": None,
            },
            "motoristas": [
                {
                    "id": None,
                    "nome": "594- Suporte Vexado ",
                    "pessoa": None,
                    "email": None,
                    "cpfCnpj": None,
                    "telefone": None,
                    "idEmpresaCadastradora": None,
                    "plataforma": None,
                    "rolesName": [],
                    "comissao": None,
                    "impostoRetido": None,
                    "nomeAgencia": None,
                    "codigoAgencia": None,
                    "descTipoDescontoMeiaPassagem": None,
                    "valorTipoDescontoMeiaPassagem": None,
                    "agencias": None,
                    "usuarioEmpresaId": 119,
                    "bloqueado": None,
                    "venderValorANTT": None,
                },
                {
                    "id": None,
                    "nome": "Buser Sandbox",
                    "pessoa": None,
                    "email": None,
                    "cpfCnpj": None,
                    "telefone": None,
                    "idEmpresaCadastradora": None,
                    "plataforma": None,
                    "rolesName": [],
                    "comissao": None,
                    "impostoRetido": None,
                    "nomeAgencia": None,
                    "codigoAgencia": None,
                    "descTipoDescontoMeiaPassagem": None,
                    "valorTipoDescontoMeiaPassagem": None,
                    "agencias": None,
                    "usuarioEmpresaId": 6407,
                    "bloqueado": None,
                    "venderValorANTT": None,
                },
            ],
            "assentosDisponiveis": 48,
            "idEmpresa": 5,
            "nomeEmpresa": "GIRO TURISMO",
            "nomeFantasiaEmpresa": "GIRO TURISMO LTDA",
            "poltronas": [],
            "descricaoTipoVeiculo": "Leito",
            "preco": None,
            "quilometragem": "0,00",
            "tipoAjusteItinerario": None,
            "valorAjuste": None,
            "tipoPreco": {"tipoPreco": "LEITO", "descricaoTipoPreco": "Leito"},
            "andar": 1,
            "trechosBloqueados": [],
            "habilitadoSite": False,
            "assentosVendidos": None,
            "sePodeEmbarcar": False,
        }

    @staticmethod
    def response_com_locais_repetidos():
        return {
            "id": 27884,
            "dataPartida": "2021-12-30",
            "horaSaida": "22:22",
            "dataHoraPartida": "30/12/2021 22:22",
            "dataHoraChegada": "31/12/2021 12:03",
            "veiculoDto": {},
            "rotaDto": {
                "id": 10,
                "empresaDto": None,
                "cidadeOrigem": {
                    "id": 3,
                    "uf": "DF",
                    "descricaoUf": "Distrito Federal",
                    "nome": "Taguatinga",
                    "nomeComUf": "Taguatinga - DF",
                    "codigoIbge": "5300108",
                },
                "cidadeDestino": {
                    "id": 2796,
                    "uf": "MG",
                    "descricaoUf": "Minas Gerais",
                    "nome": "Itacarambi",
                    "nomeComUf": "Itacarambi - MG",
                    "codigoIbge": "3132107",
                },
                "trechosDto": [
                    {
                        "id": 171,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 1,
                            "uf": "DF",
                            "descricaoUf": "Distrito Federal",
                            "nome": "Brasília",
                            "nomeComUf": "Brasília - DF",
                            "codigoIbge": "5300108",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 1,
                        "duracao": "00:00",
                        "pontoEmbarque": "RODOVIARIA DE TAGUATINGA",
                        "taxaEmbarque": "0",
                        "quilometragem": "10",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 244,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 2796,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Itacarambi",
                            "nomeComUf": "Itacarambi - MG",
                            "codigoIbge": "3132107",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 15,
                        "duracao": "00:00",
                        "pontoEmbarque": "RODOVIARIA DE ITACARAMBI-MG",
                        "taxaEmbarque": "0",
                        "quilometragem": "0,00",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 173,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 272,
                            "uf": "GO",
                            "descricaoUf": "Goiás",
                            "nome": "Valparaíso de Goiás",
                            "nomeComUf": "Valparaíso de Goiás - GO",
                            "codigoIbge": "5221858",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 2,
                        "duracao": "00:40",
                        "pontoEmbarque": "RODOVIARIA INTERESTADUAL DE BRASILIA-DF",
                        "taxaEmbarque": "0",
                        "quilometragem": "90,50",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 174,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 166,
                            "uf": "GO",
                            "descricaoUf": "Goiás",
                            "nome": "Luziânia",
                            "nomeComUf": "Luziânia - GO",
                            "codigoIbge": "5212501",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 3,
                        "duracao": "00:00",
                        "pontoEmbarque": "TERMINAL VALPARAISO",
                        "taxaEmbarque": "0",
                        "quilometragem": "100",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                    {
                        "id": 174,
                        "idEmpresa": 5,
                        "cidadeDestino": {
                            "id": 166,
                            "uf": "GO",
                            "descricaoUf": "Goiás",
                            "nome": "Luziânia",
                            "nomeComUf": "Luziânia - GO",
                            "codigoIbge": "5212501",
                        },
                        "cidadeOrigem": None,
                        "cidadeOrigemAlternativa": None,
                        "cidadeDestinoAlternativa": None,
                        "rotaDto": {
                            "id": 10,
                            "empresaDto": None,
                            "cidadeOrigem": {
                                "id": 3,
                                "uf": "DF",
                                "descricaoUf": "Distrito Federal",
                                "nome": "Taguatinga",
                                "nomeComUf": "Taguatinga - DF",
                                "codigoIbge": "5300108",
                            },
                            "cidadeDestino": {
                                "id": 2796,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "Itacarambi",
                                "nomeComUf": "Itacarambi - MG",
                                "codigoIbge": "3132107",
                            },
                            "trechosDto": [],
                            "conexoes": [],
                            "opcoesTrechoAlternativo": None,
                            "prefixo": None,
                            "prestador": None,
                        },
                        "ordem": 3,
                        "duracao": "00:00",
                        "pontoEmbarque": "TERMINAL VALPARAISO",
                        "taxaEmbarque": "0",
                        "quilometragem": "100",
                        "conexao": None,
                        "habilitadoTrechoAlternativoLimeteEstadual": None,
                    },
                ],
                "conexoes": [],
                "opcoesTrechoAlternativo": None,
                "prefixo": None,
                "prestador": None,
            },
            "motoristas": [
                {
                    "id": None,
                    "nome": "594- Suporte Vexado ",
                    "pessoa": None,
                    "email": None,
                    "cpfCnpj": None,
                    "telefone": None,
                    "idEmpresaCadastradora": None,
                    "plataforma": None,
                    "rolesName": [],
                    "comissao": None,
                    "impostoRetido": None,
                    "nomeAgencia": None,
                    "codigoAgencia": None,
                    "descTipoDescontoMeiaPassagem": None,
                    "valorTipoDescontoMeiaPassagem": None,
                    "agencias": None,
                    "usuarioEmpresaId": 119,
                    "bloqueado": None,
                    "venderValorANTT": None,
                },
                {
                    "id": None,
                    "nome": "Buser Sandbox",
                    "pessoa": None,
                    "email": None,
                    "cpfCnpj": None,
                    "telefone": None,
                    "idEmpresaCadastradora": None,
                    "plataforma": None,
                    "rolesName": [],
                    "comissao": None,
                    "impostoRetido": None,
                    "nomeAgencia": None,
                    "codigoAgencia": None,
                    "descTipoDescontoMeiaPassagem": None,
                    "valorTipoDescontoMeiaPassagem": None,
                    "agencias": None,
                    "usuarioEmpresaId": 6407,
                    "bloqueado": None,
                    "venderValorANTT": None,
                },
            ],
            "assentosDisponiveis": 48,
            "idEmpresa": 5,
            "nomeEmpresa": "GIRO TURISMO",
            "nomeFantasiaEmpresa": "GIRO TURISMO LTDA",
            "poltronas": [],
            "descricaoTipoVeiculo": "Leito",
            "preco": None,
            "quilometragem": "0,00",
            "tipoAjusteItinerario": None,
            "valorAjuste": None,
            "tipoPreco": {"tipoPreco": "LEITO", "descricaoTipoPreco": "Leito"},
            "andar": 1,
            "trechosBloqueados": [],
            "habilitadoSite": False,
            "assentosVendidos": None,
            "sePodeEmbarcar": False,
        }

    @staticmethod
    def response_error():
        return {"mensagem": "Ocorreu um erro interno", "sigla": None}


class MockGetCidades(MockBase):
    @staticmethod
    def response():
        return [
            {
                "id": 9999,
                "uf": "SP",
                "descricaoUf": "São Paulo",
                "nome": "São José dos Campos Posto da Gruta",
                "nomeComUf": "São José dos Campos Posto da Gruta - SP",
                "codigoIbge": "3549904",
            },
            {
                "id": 5843,
                "uf": "SP",
                "descricaoUf": "São Paulo",
                "nome": "São José dos Campos",
                "nomeComUf": "São José dos Campos - SP",
                "codigoIbge": "3549904",
            },
        ]


class MockCidadesEmpresas(MockBase):
    @staticmethod
    def response():
        return [
            {
                "id": 1,
                "uf": "DF",
                "descricaoUf": "Distrito Federal",
                "nome": "Brasília",
                "nomeComUf": "Brasília - DF",
                "nomeComUfNormalizado": "brasilia-df",
                "codigoIbge": "5300108",
                "codigoAntt": "60",
                "urlImagem": None,
            },
            {
                "id": 2498,
                "uf": "MG",
                "descricaoUf": "Minas Gerais",
                "nome": "Belo Horizonte",
                "nomeComUf": "Belo Horizonte - MG",
                "nomeComUfNormalizado": "belohorizonte-mg",
                "codigoIbge": "3106200",
                "codigoAntt": "72",
                "urlImagem": None,
            },
        ]


class MockGetPrecos(MockBase):
    @staticmethod
    def response():
        return {
            "precos": [
                {
                    "id": 16415631,
                    "empresaDto": {
                        "id": 558,
                        "nome": "CS VIP LOGTUR TRANSPORTES E TURISMO LTDA",
                        "email": "<EMAIL>",
                        "telefones": ["(62) 83022-054 "],
                        "urlImagem": "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/a07f4793-4075-4c1b-9a93-a615e03e6700",
                        "cnpj": "38.478.982/0001-02",
                        "nomeFantasia": "VIP BRASIL",
                        "endereco": {
                            "id": 595,
                            "cep": "74583-864",
                            "logradouro": "Rua MN5",
                            "complemento": "QUADRA 20 LOTE",
                            "bairro": "Jardim Gramado I",
                            "codigoCidade": 126,
                            "localidade": None,
                            "uf": "GO",
                            "nomeCidade": "Goiânia",
                            "numero": None,
                        },
                        "nomeDono": None,
                        "ie": None,
                        "cnae": None,
                        "im": None,
                        "tar": None,
                        "telefoneDono": None,
                        "nomeSocio": None,
                        "telefoneSocio": None,
                        "nomeGerente": None,
                        "telefoneGerente": None,
                        "opcoesTrechoAlternativo": True,
                        "valorKgEncomenda": "0,00",
                        "multaRemarcacaoCancelamentoDentroPrazo": "00.00",
                        "multaRemarcacaoCancelamentoForaPrazo": "00.00",
                        "habilitadoEncomenda": True,
                        "habilitadoSite": True,
                        "habilitadoBpe": True,
                        "nomeCertificado": None,
                        "senhaCertificado": None,
                        "bloqueada": None,
                        "mensagemAvisoBilhete": "...",
                        "grupo": None,
                        "habilitadoBuscaReservaComBpe": False,
                        "dataIniciaoVigenciaBuscaReservaComBpe": None,
                        "dataCriacao": "05/12/2023",
                        "sites": ["vexado", "brasilpassagens", "vipbrasil"],
                        "tempoVisualizacaoDados": None,
                        "habilitadaVendaSeguro": False,
                        "habilitadoAgencia": True,
                        "habilitadoValorAntt": False,
                        "impostoPraticar": "00.00",
                        "comissaoPraticar": "15.00",
                        "agenteTipoUm": "05.00",
                        "agenteTipoDois": "05.00",
                        "agenteTipoTres": "05.00",
                        "notaEmpresa": "4.80",
                        "habilitadoBpeSites": True,
                        "rodoviaria": True,
                        "turismo": False,
                        "senhaAntt": None,
                        "habilitadoEnvioWpp": True,
                        "integracoes": [],
                        "numeroNotaCte": None,
                        "numeroNotaCteOs": None,
                        "habilitadoRegraBilhete": True,
                        "tipoControleEmissaoBPe": {"chave": "PADRAO", "valor": "Padrão"},
                        "habilitadoRegraConexaoTrecho": True,
                        "nomeFantasiaNormalizado": "vip-brasil",
                    },
                    "cidadeOrigem": {
                        "id": 47,
                        "uf": "GO",
                        "descricaoUf": "Goiás",
                        "nome": "Anápolis",
                        "nomeComUf": "Anápolis - GO",
                        "nomeComUfNormalizado": "anapolis-go",
                        "codigoIbge": "5201108",
                        "codigoAntt": "2769",
                        "urlImagem": None,
                    },
                    "cidadeDestino": {
                        "id": 5848,
                        "uf": "SP",
                        "descricaoUf": "São Paulo",
                        "nome": "São Paulo",
                        "nomeComUf": "São Paulo - SP",
                        "nomeComUfNormalizado": "saopaulo-sp",
                        "codigoIbge": "3550308",
                        "codigoAntt": "70",
                        "urlImagem": None,
                    },
                    "tipoPreco": "LEITO",
                    "descricaoTipoPreco": "Leito",
                    "valor": "229,99",
                    "valorAntt": "300,00",
                    "tarifaSeguro": "0,00",
                },
                {
                    "id": 16451776,
                    "empresaDto": {
                        "id": 558,
                        "nome": "CS VIP LOGTUR TRANSPORTES E TURISMO LTDA",
                        "email": "<EMAIL>",
                        "telefones": ["(62) 83022-054 "],
                        "urlImagem": "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/a07f4793-4075-4c1b-9a93-a615e03e6700",
                        "cnpj": "38.478.982/0001-02",
                        "nomeFantasia": "VIP BRASIL",
                        "endereco": {
                            "id": 595,
                            "cep": "74583-864",
                            "logradouro": "Rua MN5",
                            "complemento": "QUADRA 20 LOTE",
                            "bairro": "Jardim Gramado I",
                            "codigoCidade": 126,
                            "localidade": None,
                            "uf": "GO",
                            "nomeCidade": "Goiânia",
                            "numero": None,
                        },
                        "nomeDono": None,
                        "ie": None,
                        "cnae": None,
                        "im": None,
                        "tar": None,
                        "telefoneDono": None,
                        "nomeSocio": None,
                        "telefoneSocio": None,
                        "nomeGerente": None,
                        "telefoneGerente": None,
                        "opcoesTrechoAlternativo": True,
                        "valorKgEncomenda": "0,00",
                        "multaRemarcacaoCancelamentoDentroPrazo": "00.00",
                        "multaRemarcacaoCancelamentoForaPrazo": "00.00",
                        "habilitadoEncomenda": True,
                        "habilitadoSite": True,
                        "habilitadoBpe": True,
                        "nomeCertificado": None,
                        "senhaCertificado": None,
                        "bloqueada": None,
                        "mensagemAvisoBilhete": "...",
                        "grupo": None,
                        "habilitadoBuscaReservaComBpe": False,
                        "dataIniciaoVigenciaBuscaReservaComBpe": None,
                        "dataCriacao": "05/12/2023",
                        "sites": ["vexado", "brasilpassagens", "vipbrasil"],
                        "tempoVisualizacaoDados": None,
                        "habilitadaVendaSeguro": False,
                        "habilitadoAgencia": True,
                        "habilitadoValorAntt": False,
                        "impostoPraticar": "00.00",
                        "comissaoPraticar": "15.00",
                        "agenteTipoUm": "05.00",
                        "agenteTipoDois": "05.00",
                        "agenteTipoTres": "05.00",
                        "notaEmpresa": "4.80",
                        "habilitadoBpeSites": True,
                        "rodoviaria": True,
                        "turismo": False,
                        "senhaAntt": None,
                        "habilitadoEnvioWpp": True,
                        "integracoes": [],
                        "numeroNotaCte": None,
                        "numeroNotaCteOs": None,
                        "habilitadoRegraBilhete": True,
                        "tipoControleEmissaoBPe": {"chave": "PADRAO", "valor": "Padrão"},
                        "habilitadoRegraConexaoTrecho": True,
                        "nomeFantasiaNormalizado": "vip-brasil",
                    },
                    "cidadeOrigem": {
                        "id": 47,
                        "uf": "GO",
                        "descricaoUf": "Goiás",
                        "nome": "Anápolis",
                        "nomeComUf": "Anápolis - GO",
                        "nomeComUfNormalizado": "anapolis-go",
                        "codigoIbge": "5201108",
                        "codigoAntt": "2769",
                        "urlImagem": None,
                    },
                    "cidadeDestino": {
                        "id": 584,
                        "uf": "TO",
                        "descricaoUf": "Tocantins",
                        "nome": "Palmeirante",
                        "nomeComUf": "Palmeirante - TO",
                        "nomeComUfNormalizado": "palmeirante-to",
                        "codigoIbge": "1715705",
                        "codigoAntt": None,
                        "urlImagem": None,
                    },
                    "tipoPreco": "SEMI_LEITO",
                    "descricaoTipoPreco": "Semi Leito",
                    "valor": "430,00",
                    "valorAntt": "430,00",
                    "tarifaSeguro": "0,00",
                },
            ],
            "totalRegistros": 434,
        }

    @staticmethod
    def response_no_price():
        return {
            "precos": [],
            "totalRegistros": 0,
        }


class MockGetPrecosSimplificado(MockBase):
    @staticmethod
    def response():
        return {
            "trechosItinerarioEmpresas": [
                {
                    "idCidadeOrigem": 47,
                    "nomeCidadeOrigem": "anapolis-go",
                    "idCidadeDestino": 5848,
                    "nomeCidadeDestino": "saopaulo-sp",
                    "dataPartida": "2024-03-28",
                    "horaPartida": "17:00",
                    "dataChegada": "2024-03-29",
                    "horaChegada": "07:14",
                    "preco": "229.99",
                    "tipoPreco": "LEITO",
                    "quantidadesAssentosDisponiveis": "36",
                },
                {
                    "idCidadeOrigem": 47,
                    "nomeCidadeOrigem": "anapolis-go",
                    "idCidadeDestino": 5848,
                    "nomeCidadeDestino": "saopaulo-sp",
                    "dataPartida": "2024-03-28",
                    "horaPartida": "17:00",
                    "dataChegada": "2024-03-29",
                    "horaChegada": "07:14",
                    "preco": "279.99",
                    "tipoPreco": "LEITO_INDIVIDUAL",
                    "quantidadesAssentosDisponiveis": "10",
                },
                {
                    "idCidadeOrigem": 47,
                    "nomeCidadeOrigem": "anapolis-go",
                    "idCidadeDestino": 584,
                    "nomeCidadeDestino": "palmeirante-to",
                    "dataPartida": "2024-03-28",
                    "horaPartida": "17:00",
                    "dataChegada": "2024-03-29",
                    "horaChegada": "07:14",
                    "preco": "229.99",
                    "tipoPreco": "SEMI_LEITO",
                    "quantidadesAssentosDisponiveis": "36",
                },
            ],
            "total": 6768,
            "totalColecao": 1000,
            "paginaAtual": 1,
            "totalPagina": 1,
            "limite": 1000,
        }


class MockMapasVeiculos(MockBase):
    @staticmethod
    def response():
        return {
            "mapasVeiculos": [
                {
                    "id": 9,
                    "fileirasPrimeiroAndar": [
                        {
                            "id": 60,
                            "numero": 4,
                            "exibeCorredor": False,
                            "assentos": [
                                {
                                    "id": 442,
                                    "numero": 3,
                                    "ordem": 0,
                                    "tipoAssento": "NORMAL",
                                    "poltrona": None,
                                },
                                {
                                    "id": 443,
                                    "numero": 7,
                                    "ordem": 1,
                                    "tipoAssento": "PORTA",
                                    "poltrona": None,
                                },
                            ],
                        },
                        {"id": 59, "numero": 3, "exibeCorredor": True, "assentos": []},
                        {
                            "id": 58,
                            "numero": 2,
                            "exibeCorredor": False,
                            "assentos": [
                                {
                                    "id": 433,
                                    "numero": 2,
                                    "ordem": 0,
                                    "tipoAssento": "NORMAL",
                                    "poltrona": None,
                                }
                            ],
                        },
                    ],
                    "fileirasSegundoAndar": None,
                    "seDoisAndares": False,
                    "quantidadeAssentos": 28,
                    "modelo": "1200 LEITO ",
                },
                {
                    "id": 103,
                    "fileirasPrimeiroAndar": [
                        {
                            "id": 550,
                            "numero": 4,
                            "exibeCorredor": False,
                            "assentos": [
                                {
                                    "id": 4980,
                                    "numero": 47,
                                    "ordem": 0,
                                    "tipoAssento": "NORMAL",
                                    "poltrona": None,
                                },
                                {
                                    "id": 4981,
                                    "numero": 50,
                                    "ordem": 1,
                                    "tipoAssento": "NORMAL",
                                    "poltrona": None,
                                },
                            ],
                        }
                    ],
                    "fileirasSegundoAndar": [
                        {
                            "id": 498,
                            "numero": 4,
                            "exibeCorredor": False,
                            "assentos": [
                                {
                                    "id": 4409,
                                    "numero": 3,
                                    "ordem": 0,
                                    "tipoAssento": "NORMAL",
                                    "poltrona": None,
                                },
                                {
                                    "id": 4410,
                                    "numero": 7,
                                    "ordem": 1,
                                    "tipoAssento": "REMOVIDO",
                                    "poltrona": None,
                                },
                            ],
                        }
                    ],
                    "seDoisAndares": True,
                    "quantidadeAssentos": 12,
                    "modelo": "2 INFERIOR, 2 SUPERIOR ",
                },
            ]
        }


class MockListarTrechos(MockBase):
    @staticmethod
    def response():
        return [
            {
                "id": 19480,
                "idEmpresa": 397,
                "cidadeDestino": {
                    "id": 1023,
                    "uf": "CE",
                    "descricaoUf": "Ceará",
                    "nome": "Fortaleza",
                    "nomeComUf": "Fortaleza - CE",
                    "nomeComUfNormalizado": "fortaleza-ce",
                    "codigoIbge": "2304400",
                    "urlImagem": None,
                },
                "cidadeOrigem": "João Pessoa - PB",
                "cidadeOrigemAlternativa": None,
                "cidadeDestinoAlternativa": None,
                "rotaDto": {
                    "id": 1567,
                    "empresaDto": None,
                    "cidadeOrigem": {
                        "id": 5830,
                        "uf": "SP",
                        "descricaoUf": "São Paulo",
                        "nome": "São Bernardo do Campo",
                        "nomeComUf": "São Bernardo do Campo - SP",
                        "nomeComUfNormalizado": "saobernardodocampo-sp",
                        "codigoIbge": "3548708",
                        "urlImagem": None,
                    },
                    "cidadeDestino": {
                        "id": 1023,
                        "uf": "CE",
                        "descricaoUf": "Ceará",
                        "nome": "Fortaleza",
                        "nomeComUf": "Fortaleza - CE",
                        "nomeComUfNormalizado": "fortaleza-ce",
                        "codigoIbge": "2304400",
                        "urlImagem": None,
                    },
                    "trechosDto": [],
                    "conexoes": [],
                    "opcoesTrechoAlternativo": None,
                    "prefixo": "08-9437-00",
                    "prestador": None,
                    "descricao": None,
                },
                "ordem": 7,
                "duracao": "00:00",
                "pontoEmbarque": " Avenida Hilton Souto Maior, s/n - Mangabeira ",
                "taxaEmbarque": "0,00",
                "quilometragem": "0,00",
                "conexao": None,
                "habilitadoTrechoAlternativoLimeteEstadual": None,
            },
            {
                "id": 19479,
                "idEmpresa": 397,
                "cidadeDestino": {
                    "id": 2304,
                    "uf": "PB",
                    "descricaoUf": "Paraíba",
                    "nome": "João Pessoa",
                    "nomeComUf": "João Pessoa - PB",
                    "nomeComUfNormalizado": "joaopessoa-pb",
                    "codigoIbge": "2507507",
                    "urlImagem": None,
                },
                "cidadeOrigem": "Recife - PE",
                "cidadeOrigemAlternativa": None,
                "cidadeDestinoAlternativa": None,
                "rotaDto": {
                    "id": 1567,
                    "empresaDto": None,
                    "cidadeOrigem": {
                        "id": 5830,
                        "uf": "SP",
                        "descricaoUf": "São Paulo",
                        "nome": "São Bernardo do Campo",
                        "nomeComUf": "São Bernardo do Campo - SP",
                        "nomeComUfNormalizado": "saobernardodocampo-sp",
                        "codigoIbge": "3548708",
                        "urlImagem": None,
                    },
                    "cidadeDestino": {
                        "id": 1023,
                        "uf": "CE",
                        "descricaoUf": "Ceará",
                        "nome": "Fortaleza",
                        "nomeComUf": "Fortaleza - CE",
                        "nomeComUfNormalizado": "fortaleza-ce",
                        "codigoIbge": "2304400",
                        "urlImagem": None,
                    },
                    "trechosDto": [],
                    "conexoes": [],
                    "opcoesTrechoAlternativo": None,
                    "prefixo": "08-9437-00",
                    "prestador": None,
                    "descricao": None,
                },
                "ordem": 6,
                "duracao": "02:00",
                "pontoEmbarque": "Avenida Presidente Dutra, 517 - Imbiribeira",
                "taxaEmbarque": "0,00",
                "quilometragem": "124,00",
                "conexao": None,
                "habilitadoTrechoAlternativoLimeteEstadual": None,
            },
            {
                "id": 19478,
                "idEmpresa": 397,
                "cidadeDestino": {
                    "id": 3417,
                    "uf": "PE",
                    "descricaoUf": "Pernambuco",
                    "nome": "Recife",
                    "nomeComUf": "Recife - PE",
                    "nomeComUfNormalizado": "recife-pe",
                    "codigoIbge": "2611606",
                    "urlImagem": None,
                },
                "cidadeOrigem": "Maceió - AL",
                "cidadeOrigemAlternativa": None,
                "cidadeDestinoAlternativa": None,
                "rotaDto": {
                    "id": 1567,
                    "empresaDto": None,
                    "cidadeOrigem": {
                        "id": 5830,
                        "uf": "SP",
                        "descricaoUf": "São Paulo",
                        "nome": "São Bernardo do Campo",
                        "nomeComUf": "São Bernardo do Campo - SP",
                        "nomeComUfNormalizado": "saobernardodocampo-sp",
                        "codigoIbge": "3548708",
                        "urlImagem": None,
                    },
                    "cidadeDestino": {
                        "id": 1023,
                        "uf": "CE",
                        "descricaoUf": "Ceará",
                        "nome": "Fortaleza",
                        "nomeComUf": "Fortaleza - CE",
                        "nomeComUfNormalizado": "fortaleza-ce",
                        "codigoIbge": "2304400",
                        "urlImagem": None,
                    },
                    "trechosDto": [],
                    "conexoes": [],
                    "opcoesTrechoAlternativo": None,
                    "prefixo": "08-9437-00",
                    "prestador": None,
                    "descricao": None,
                },
                "ordem": 5,
                "duracao": "05:20",
                "pontoEmbarque": "Avenida Menino Marcelo, 15 - Serraria",
                "taxaEmbarque": "0,00",
                "quilometragem": "249,00",
                "conexao": None,
                "habilitadoTrechoAlternativoLimeteEstadual": None,
            },
            {
                "id": 19477,
                "idEmpresa": 397,
                "cidadeDestino": {
                    "id": 701,
                    "uf": "AL",
                    "descricaoUf": "Alagoas",
                    "nome": "Maceió",
                    "nomeComUf": "Maceió - AL",
                    "nomeComUfNormalizado": "maceio-al",
                    "codigoIbge": "2704302",
                    "urlImagem": None,
                },
                "cidadeOrigem": "Aracaju - SE",
                "cidadeOrigemAlternativa": None,
                "cidadeDestinoAlternativa": None,
                "rotaDto": {
                    "id": 1567,
                    "empresaDto": None,
                    "cidadeOrigem": {
                        "id": 5830,
                        "uf": "SP",
                        "descricaoUf": "São Paulo",
                        "nome": "São Bernardo do Campo",
                        "nomeComUf": "São Bernardo do Campo - SP",
                        "nomeComUfNormalizado": "saobernardodocampo-sp",
                        "codigoIbge": "3548708",
                        "urlImagem": None,
                    },
                    "cidadeDestino": {
                        "id": 1023,
                        "uf": "CE",
                        "descricaoUf": "Ceará",
                        "nome": "Fortaleza",
                        "nomeComUf": "Fortaleza - CE",
                        "nomeComUfNormalizado": "fortaleza-ce",
                        "codigoIbge": "2304400",
                        "urlImagem": None,
                    },
                    "trechosDto": [],
                    "conexoes": [],
                    "opcoesTrechoAlternativo": None,
                    "prefixo": "08-9437-00",
                    "prestador": None,
                    "descricao": None,
                },
                "ordem": 4,
                "duracao": "05:35",
                "pontoEmbarque": " Avenida Santos Dumont, s/n - Coroa do Meio ",
                "taxaEmbarque": "0,00",
                "quilometragem": "288,00",
                "conexao": None,
                "habilitadoTrechoAlternativoLimeteEstadual": None,
            },
            {
                "id": 19476,
                "idEmpresa": 397,
                "cidadeDestino": {
                    "id": 5211,
                    "uf": "SE",
                    "descricaoUf": "Sergipe",
                    "nome": "Aracaju",
                    "nomeComUf": "Aracaju - SE",
                    "nomeComUfNormalizado": "aracaju-se",
                    "codigoIbge": "2800308",
                    "urlImagem": None,
                },
                "cidadeOrigem": "Salvador - BA",
                "cidadeOrigemAlternativa": None,
                "cidadeDestinoAlternativa": None,
                "rotaDto": {
                    "id": 1567,
                    "empresaDto": None,
                    "cidadeOrigem": {
                        "id": 5830,
                        "uf": "SP",
                        "descricaoUf": "São Paulo",
                        "nome": "São Bernardo do Campo",
                        "nomeComUf": "São Bernardo do Campo - SP",
                        "nomeComUfNormalizado": "saobernardodocampo-sp",
                        "codigoIbge": "3548708",
                        "urlImagem": None,
                    },
                    "cidadeDestino": {
                        "id": 1023,
                        "uf": "CE",
                        "descricaoUf": "Ceará",
                        "nome": "Fortaleza",
                        "nomeComUf": "Fortaleza - CE",
                        "nomeComUfNormalizado": "fortaleza-ce",
                        "codigoIbge": "2304400",
                        "urlImagem": None,
                    },
                    "trechosDto": [],
                    "conexoes": [],
                    "opcoesTrechoAlternativo": None,
                    "prefixo": "08-9437-00",
                    "prestador": None,
                    "descricao": None,
                },
                "ordem": 3,
                "duracao": "05:40",
                "pontoEmbarque": " Avenida Luís Viana Filho, 8544 - Patamares ",
                "taxaEmbarque": "0,00",
                "quilometragem": "315,00",
                "conexao": None,
                "habilitadoTrechoAlternativoLimeteEstadual": None,
            },
        ]


class MockListarVeiculos(MockBase):
    @staticmethod
    def response():
        return {
            "veiculos": [
                {
                    "id": 775,
                    "descricao": "APJ8F14",
                    "empresaDto": None,
                    "mapaVeiculoDto": {"id": 111},
                    "idRastreamento": None,
                },
                {
                    "id": 721,
                    "descricao": "JVT3603",
                    "empresaDto": None,
                    "mapaVeiculoDto": {"id": 104},
                    "idRastreamento": None,
                },
            ],
            "total": 2,
        }

    @staticmethod
    def response_sem_veiculos():
        return {"veiculos": [], "total": 0}


class MockBuscarRotas(MockBase):
    @staticmethod
    def response():
        return {
            "rotas": [
                {
                    "id": 4635,
                    "empresaDto": {
                        "id": 558,
                        "nome": "CS VIP LOGTUR TRANSPORTES E TURISMO LTDA",
                        "email": "<EMAIL>",
                        "telefones": ["(62) 83022-054 "],
                        "urlImagem": "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/a07f4793-4075-4c1b-9a93-a615e03e6700",
                        "cnpj": "38.478.982/0001-02",
                        "nomeFantasia": "VIP BRASIL",
                        "endereco": {
                            "id": 595,
                            "cep": "74583-864",
                            "logradouro": "Rua MN5",
                            "complemento": "QUADRA 20 LOTE",
                            "bairro": "Jardim Gramado I",
                            "codigoCidade": 126,
                            "localidade": None,
                            "uf": "GO",
                            "nomeCidade": "Goiânia",
                            "numero": None,
                        },
                        "nomeDono": None,
                        "ie": None,
                        "cnae": None,
                        "im": None,
                        "tar": None,
                        "telefoneDono": None,
                        "nomeSocio": None,
                        "telefoneSocio": None,
                        "nomeGerente": None,
                        "telefoneGerente": None,
                        "opcoesTrechoAlternativo": True,
                        "valorKgEncomenda": "0,00",
                        "multaRemarcacaoCancelamentoDentroPrazo": "00.00",
                        "multaRemarcacaoCancelamentoForaPrazo": "00.00",
                        "habilitadoEncomenda": True,
                        "habilitadoSite": True,
                        "habilitadoBpe": True,
                        "nomeCertificado": None,
                        "senhaCertificado": None,
                        "bloqueada": None,
                        "mensagemAvisoBilhete": "...",
                        "grupo": None,
                        "habilitadoBuscaReservaComBpe": False,
                        "dataIniciaoVigenciaBuscaReservaComBpe": None,
                        "dataCriacao": "05/12/2023",
                        "sites": ["vexado", "brasilpassagens", "vipbrasil"],
                        "tempoVisualizacaoDados": None,
                        "habilitadaVendaSeguro": False,
                        "habilitadoAgencia": True,
                        "habilitadoValorAntt": False,
                        "impostoPraticar": "00.00",
                        "comissaoPraticar": "15.00",
                        "agenteTipoUm": "05.00",
                        "agenteTipoDois": "05.00",
                        "agenteTipoTres": "05.00",
                        "notaEmpresa": "4.80",
                        "habilitadoBpeSites": True,
                        "rodoviaria": True,
                        "turismo": False,
                        "senhaAntt": None,
                        "habilitadoEnvioWpp": True,
                        "integracoes": [],
                        "numeroNotaCte": None,
                        "numeroNotaCteOs": None,
                        "habilitadoRegraBilhete": True,
                        "tipoControleEmissaoBPe": {"chave": "PADRAO", "valor": "Padrão"},
                        "habilitadoRegraConexaoTrecho": True,
                        "nomeFantasiaNormalizado": "vip-brasil",
                    },
                    "cidadeOrigem": {
                        "id": 216,
                        "uf": "GO",
                        "descricaoUf": "Goiás",
                        "nome": "Pirenópolis",
                        "nomeComUf": "Pirenópolis - GO",
                        "nomeComUfNormalizado": "pirenopolis-go",
                        "codigoIbge": "5217302",
                        "codigoAntt": "2772",
                        "urlImagem": None,
                    },
                    "cidadeDestino": {
                        "id": 5848,
                        "uf": "SP",
                        "descricaoUf": "São Paulo",
                        "nome": "São Paulo",
                        "nomeComUf": "São Paulo - SP",
                        "nomeComUfNormalizado": "saopaulo-sp",
                        "codigoIbge": "3550308",
                        "codigoAntt": "70",
                        "urlImagem": None,
                    },
                    "trechosDto": [
                        {
                            "id": 46848,
                            "idEmpresa": 558,
                            "cidadeDestino": {
                                "id": 47,
                                "uf": "GO",
                                "descricaoUf": "Goiás",
                                "nome": "Anápolis",
                                "nomeComUf": "Anápolis - GO",
                                "nomeComUfNormalizado": "anapolis-go",
                                "codigoIbge": "5201108",
                                "codigoAntt": "2769",
                                "urlImagem": None,
                            },
                            "cidadeOrigem": None,
                            "cidadeOrigemAlternativa": None,
                            "cidadeDestinoAlternativa": None,
                            "rotaDto": {
                                "id": 4635,
                                "empresaDto": None,
                                "cidadeOrigem": {
                                    "id": 216,
                                    "uf": "GO",
                                    "descricaoUf": "Goiás",
                                    "nome": "Pirenópolis",
                                    "nomeComUf": "Pirenópolis - GO",
                                    "nomeComUfNormalizado": "pirenopolis-go",
                                    "codigoIbge": "5217302",
                                    "codigoAntt": "2772",
                                    "urlImagem": None,
                                },
                                "cidadeDestino": {
                                    "id": 5848,
                                    "uf": "SP",
                                    "descricaoUf": "São Paulo",
                                    "nome": "São Paulo",
                                    "nomeComUf": "São Paulo - SP",
                                    "nomeComUfNormalizado": "saopaulo-sp",
                                    "codigoIbge": "3550308",
                                    "codigoAntt": "70",
                                    "urlImagem": None,
                                },
                                "trechosDto": [],
                                "conexoes": [],
                                "opcoesTrechoAlternativo": None,
                                "prefixo": "12-0767-00 FT",
                                "delimitacao": None,
                                "descricao": None,
                                "nomeFantasiaEmpresa": None,
                                "empresaId": None,
                            },
                            "ordem": 1,
                            "duracao": "00:00",
                            "pontoEmbarque": "Rodoviária",
                            "plataformaEmbarque": None,
                            "taxaEmbarque": "0,00",
                            "pedagio": "0,00",
                            "configuracaoBilhete": [],
                            "quilometragem": "0,00",
                            "conexao": None,
                            "habilitadoTrechoAlternativoLimeteEstadual": None,
                            "latitude": None,
                            "longitude": None,
                        },
                        {
                            "id": 46849,
                            "idEmpresa": 558,
                            "cidadeDestino": {
                                "id": 126,
                                "uf": "GO",
                                "descricaoUf": "Goiás",
                                "nome": "Goiânia",
                                "nomeComUf": "Goiânia - GO",
                                "nomeComUfNormalizado": "goiania-go",
                                "codigoIbge": "5208707",
                                "codigoAntt": "74",
                                "urlImagem": None,
                            },
                            "cidadeOrigem": None,
                            "cidadeOrigemAlternativa": None,
                            "cidadeDestinoAlternativa": None,
                            "rotaDto": {
                                "id": 4635,
                                "empresaDto": None,
                                "cidadeOrigem": {
                                    "id": 216,
                                    "uf": "GO",
                                    "descricaoUf": "Goiás",
                                    "nome": "Pirenópolis",
                                    "nomeComUf": "Pirenópolis - GO",
                                    "nomeComUfNormalizado": "pirenopolis-go",
                                    "codigoIbge": "5217302",
                                    "codigoAntt": "2772",
                                    "urlImagem": None,
                                },
                                "cidadeDestino": {
                                    "id": 5848,
                                    "uf": "SP",
                                    "descricaoUf": "São Paulo",
                                    "nome": "São Paulo",
                                    "nomeComUf": "São Paulo - SP",
                                    "nomeComUfNormalizado": "saopaulo-sp",
                                    "codigoIbge": "3550308",
                                    "codigoAntt": "70",
                                    "urlImagem": None,
                                },
                                "trechosDto": [],
                                "conexoes": [],
                                "opcoesTrechoAlternativo": None,
                                "prefixo": "12-0767-00 FT",
                                "delimitacao": None,
                                "descricao": None,
                                "nomeFantasiaEmpresa": None,
                                "empresaId": None,
                            },
                            "ordem": 2,
                            "duracao": "00:40",
                            "pontoEmbarque": "SALA VIP BRASIL",
                            "plataformaEmbarque": None,
                            "taxaEmbarque": "0,00",
                            "pedagio": "0,00",
                            "configuracaoBilhete": [],
                            "quilometragem": "0,00",
                            "conexao": None,
                            "habilitadoTrechoAlternativoLimeteEstadual": None,
                            "latitude": None,
                            "longitude": None,
                        },
                        {
                            "id": 47159,
                            "idEmpresa": 558,
                            "cidadeDestino": {
                                "id": 50,
                                "uf": "GO",
                                "descricaoUf": "Goiás",
                                "nome": "Aparecida de Goiânia",
                                "nomeComUf": "Aparecida de Goiânia - GO",
                                "nomeComUfNormalizado": "aparecidadegoiania-go",
                                "codigoIbge": "5201405",
                                "codigoAntt": "3629",
                                "urlImagem": None,
                            },
                            "cidadeOrigem": None,
                            "cidadeOrigemAlternativa": None,
                            "cidadeDestinoAlternativa": None,
                            "rotaDto": {
                                "id": 4635,
                                "empresaDto": None,
                                "cidadeOrigem": {
                                    "id": 216,
                                    "uf": "GO",
                                    "descricaoUf": "Goiás",
                                    "nome": "Pirenópolis",
                                    "nomeComUf": "Pirenópolis - GO",
                                    "nomeComUfNormalizado": "pirenopolis-go",
                                    "codigoIbge": "5217302",
                                    "codigoAntt": "2772",
                                    "urlImagem": None,
                                },
                                "cidadeDestino": {
                                    "id": 5848,
                                    "uf": "SP",
                                    "descricaoUf": "São Paulo",
                                    "nome": "São Paulo",
                                    "nomeComUf": "São Paulo - SP",
                                    "nomeComUfNormalizado": "saopaulo-sp",
                                    "codigoIbge": "3550308",
                                    "codigoAntt": "70",
                                    "urlImagem": None,
                                },
                                "trechosDto": [],
                                "conexoes": [],
                                "opcoesTrechoAlternativo": None,
                                "prefixo": "12-0767-00 FT",
                                "delimitacao": None,
                                "descricao": None,
                                "nomeFantasiaEmpresa": None,
                                "empresaId": None,
                            },
                            "ordem": 3,
                            "duracao": "00:30",
                            "pontoEmbarque": "Terminal Rodoviário",
                            "plataformaEmbarque": None,
                            "taxaEmbarque": "0,00",
                            "pedagio": "0,00",
                            "configuracaoBilhete": [],
                            "quilometragem": "12,00",
                            "conexao": None,
                            "habilitadoTrechoAlternativoLimeteEstadual": None,
                            "latitude": None,
                            "longitude": None,
                        },
                        {
                            "id": 47157,
                            "idEmpresa": 558,
                            "cidadeDestino": {
                                "id": 3197,
                                "uf": "MG",
                                "descricaoUf": "Minas Gerais",
                                "nome": "São Tomás de Aquino",
                                "nomeComUf": "São Tomás de Aquino - MG",
                                "nomeComUfNormalizado": "saotomasdeaquino-mg",
                                "codigoIbge": "3165107",
                                "codigoAntt": "2155",
                                "urlImagem": None,
                            },
                            "cidadeOrigem": None,
                            "cidadeOrigemAlternativa": None,
                            "cidadeDestinoAlternativa": None,
                            "rotaDto": {
                                "id": 4635,
                                "empresaDto": None,
                                "cidadeOrigem": {
                                    "id": 216,
                                    "uf": "GO",
                                    "descricaoUf": "Goiás",
                                    "nome": "Pirenópolis",
                                    "nomeComUf": "Pirenópolis - GO",
                                    "nomeComUfNormalizado": "pirenopolis-go",
                                    "codigoIbge": "5217302",
                                    "codigoAntt": "2772",
                                    "urlImagem": None,
                                },
                                "cidadeDestino": {
                                    "id": 5848,
                                    "uf": "SP",
                                    "descricaoUf": "São Paulo",
                                    "nome": "São Paulo",
                                    "nomeComUf": "São Paulo - SP",
                                    "nomeComUfNormalizado": "saopaulo-sp",
                                    "codigoIbge": "3550308",
                                    "codigoAntt": "70",
                                    "urlImagem": None,
                                },
                                "trechosDto": [],
                                "conexoes": [],
                                "opcoesTrechoAlternativo": None,
                                "prefixo": "12-0767-00 FT",
                                "delimitacao": None,
                                "descricao": None,
                                "nomeFantasiaEmpresa": None,
                                "empresaId": None,
                            },
                            "ordem": 4,
                            "duracao": "00:00",
                            "pontoEmbarque": "Terminal Rodoviário",
                            "plataformaEmbarque": None,
                            "taxaEmbarque": "0,00",
                            "pedagio": "0,00",
                            "configuracaoBilhete": [],
                            "quilometragem": "0,00",
                            "conexao": None,
                            "habilitadoTrechoAlternativoLimeteEstadual": None,
                            "latitude": None,
                            "longitude": None,
                        },
                        {
                            "id": 46850,
                            "idEmpresa": 558,
                            "cidadeDestino": {
                                "id": 5830,
                                "uf": "SP",
                                "descricaoUf": "São Paulo",
                                "nome": "São Bernardo do Campo",
                                "nomeComUf": "São Bernardo do Campo - SP",
                                "nomeComUfNormalizado": "saobernardodocampo-sp",
                                "codigoIbge": "3548708",
                                "codigoAntt": "2124",
                                "urlImagem": None,
                            },
                            "cidadeOrigem": None,
                            "cidadeOrigemAlternativa": None,
                            "cidadeDestinoAlternativa": None,
                            "rotaDto": {
                                "id": 4635,
                                "empresaDto": None,
                                "cidadeOrigem": {
                                    "id": 216,
                                    "uf": "GO",
                                    "descricaoUf": "Goiás",
                                    "nome": "Pirenópolis",
                                    "nomeComUf": "Pirenópolis - GO",
                                    "nomeComUfNormalizado": "pirenopolis-go",
                                    "codigoIbge": "5217302",
                                    "codigoAntt": "2772",
                                    "urlImagem": None,
                                },
                                "cidadeDestino": {
                                    "id": 5848,
                                    "uf": "SP",
                                    "descricaoUf": "São Paulo",
                                    "nome": "São Paulo",
                                    "nomeComUf": "São Paulo - SP",
                                    "nomeComUfNormalizado": "saopaulo-sp",
                                    "codigoIbge": "3550308",
                                    "codigoAntt": "70",
                                    "urlImagem": None,
                                },
                                "trechosDto": [],
                                "conexoes": [],
                                "opcoesTrechoAlternativo": None,
                                "prefixo": "12-0767-00 FT",
                                "delimitacao": None,
                                "descricao": None,
                                "nomeFantasiaEmpresa": None,
                                "empresaId": None,
                            },
                            "ordem": 5,
                            "duracao": "14:40",
                            "pontoEmbarque": ".Brás",
                            "plataformaEmbarque": None,
                            "taxaEmbarque": "0,00",
                            "pedagio": "0,00",
                            "configuracaoBilhete": [],
                            "quilometragem": "0,00",
                            "conexao": None,
                            "habilitadoTrechoAlternativoLimeteEstadual": None,
                            "latitude": None,
                            "longitude": None,
                        },
                        {
                            "id": 47158,
                            "idEmpresa": 558,
                            "cidadeDestino": {
                                "id": 5848,
                                "uf": "SP",
                                "descricaoUf": "São Paulo",
                                "nome": "São Paulo",
                                "nomeComUf": "São Paulo - SP",
                                "nomeComUfNormalizado": "saopaulo-sp",
                                "codigoIbge": "3550308",
                                "codigoAntt": "70",
                                "urlImagem": None,
                            },
                            "cidadeOrigem": None,
                            "cidadeOrigemAlternativa": None,
                            "cidadeDestinoAlternativa": None,
                            "rotaDto": {
                                "id": 4635,
                                "empresaDto": None,
                                "cidadeOrigem": {
                                    "id": 216,
                                    "uf": "GO",
                                    "descricaoUf": "Goiás",
                                    "nome": "Pirenópolis",
                                    "nomeComUf": "Pirenópolis - GO",
                                    "nomeComUfNormalizado": "pirenopolis-go",
                                    "codigoIbge": "5217302",
                                    "codigoAntt": "2772",
                                    "urlImagem": None,
                                },
                                "cidadeDestino": {
                                    "id": 5848,
                                    "uf": "SP",
                                    "descricaoUf": "São Paulo",
                                    "nome": "São Paulo",
                                    "nomeComUf": "São Paulo - SP",
                                    "nomeComUfNormalizado": "saopaulo-sp",
                                    "codigoIbge": "3550308",
                                    "codigoAntt": "70",
                                    "urlImagem": None,
                                },
                                "trechosDto": [],
                                "conexoes": [],
                                "opcoesTrechoAlternativo": None,
                                "prefixo": "12-0767-00 FT",
                                "delimitacao": None,
                                "descricao": None,
                                "nomeFantasiaEmpresa": None,
                                "empresaId": None,
                            },
                            "ordem": 6,
                            "duracao": "02:00",
                            "pontoEmbarque": "Terminal Rodoviário",
                            "plataformaEmbarque": None,
                            "taxaEmbarque": "0,00",
                            "pedagio": "0,00",
                            "configuracaoBilhete": [],
                            "quilometragem": "21,00",
                            "conexao": None,
                            "habilitadoTrechoAlternativoLimeteEstadual": None,
                            "latitude": None,
                            "longitude": None,
                        },
                    ],
                    "conexoes": [
                        {
                            "id": 62,
                            "cidadeOrigem": {
                                "id": 216,
                                "uf": "GO",
                                "descricaoUf": "Goiás",
                                "nome": "Pirenópolis",
                                "nomeComUf": "Pirenópolis - GO",
                                "nomeComUfNormalizado": "pirenopolis-go",
                                "codigoIbge": "5217302",
                                "codigoAntt": "2772",
                                "urlImagem": None,
                            },
                            "cidadeDestino": {
                                "id": 5848,
                                "uf": "SP",
                                "descricaoUf": "São Paulo",
                                "nome": "São Paulo",
                                "nomeComUf": "São Paulo - SP",
                                "nomeComUfNormalizado": "saopaulo-sp",
                                "codigoIbge": "3550308",
                                "codigoAntt": "70",
                                "urlImagem": None,
                            },
                            "prefixo": "12-0767-00",
                            "rotaId": 4635,
                            "trechoId": None,
                        }
                    ],
                    "opcoesTrechoAlternativo": False,
                    "prefixo": "12-0767-00 FT",
                    "delimitacao": "Anápolis GO- Goiânia GO São Bernardo dos Campos SP",
                    "descricao": "12-0767-00 FT: Pirenópolis-GO -> São Paulo-SP / Anápolis GO- Goiânia GO São Bernardo dos Campos SP",
                    "nomeFantasiaEmpresa": None,
                    "empresaId": None,
                }
            ]
        }


class MockListarViagensRota(MockBase):
    @staticmethod
    def response():
        return {
            "itinerarios": [
                {
                    "id": 289141,
                    "dataPartida": "2024-08-12",
                    "horaSaida": "19:00",
                    "dataHoraPartida": "12/08/2024 19:00",
                    "dataHoraChegada": "13/08/2024 06:20",
                    "veiculoDto": {
                        "id": 883,
                        "descricao": "RFB1G79",
                        "empresaDto": None,
                        "mapaVeiculoDto": {
                            "id": 147,
                            "fileirasPrimeiroAndar": [
                                {
                                    "id": 802,
                                    "numero": 4,
                                    "exibeCorredor": False,
                                    "assentos": [
                                        {
                                            "id": 6850,
                                            "numero": 3,
                                            "ordem": 0,
                                            "tipoAssento": "NORMAL",
                                            "poltrona": None,
                                        },
                                        {
                                            "id": 6851,
                                            "numero": 7,
                                            "ordem": 1,
                                            "tipoAssento": "REMOVIDO",
                                            "poltrona": None,
                                        },
                                        {
                                            "id": 6852,
                                            "numero": 7,
                                            "ordem": 2,
                                            "tipoAssento": "NORMAL",
                                            "poltrona": None,
                                        },
                                        {
                                            "id": 6853,
                                            "numero": 11,
                                            "ordem": 3,
                                            "tipoAssento": "NORMAL",
                                            "poltrona": None,
                                        },
                                        {
                                            "id": 6854,
                                            "numero": 14,
                                            "ordem": 4,
                                            "tipoAssento": "NORMAL",
                                            "poltrona": None,
                                        },
                                    ],
                                }
                            ],
                            "fileirasSegundoAndar": [
                                {
                                    "id": 806,
                                    "numero": 4,
                                    "exibeCorredor": False,
                                    "assentos": [
                                        {
                                            "id": 6873,
                                            "numero": 33,
                                            "ordem": 0,
                                            "tipoAssento": "NORMAL",
                                            "poltrona": None,
                                        },
                                        {
                                            "id": 6874,
                                            "numero": 37,
                                            "ordem": 1,
                                            "tipoAssento": "NORMAL",
                                            "poltrona": None,
                                        },
                                        {
                                            "id": 6875,
                                            "numero": 41,
                                            "ordem": 2,
                                            "tipoAssento": "NORMAL",
                                            "poltrona": None,
                                        },
                                        {
                                            "id": 6876,
                                            "numero": 15,
                                            "ordem": 3,
                                            "tipoAssento": "REMOVIDO",
                                            "poltrona": None,
                                        },
                                    ],
                                }
                            ],
                            "seDoisAndares": True,
                            "quantidadeAssentos": 45,
                            "modelo": "32 LEITO SUPERIOR 13 LEITO INFERIOR",
                        },
                        "idRastreamento": None,
                        "seDoisAndares": True,
                        "imagens": None,
                        "placa": None,
                        "modelo": None,
                        "tipoVeiculo": None,
                        "quilometragem": None,
                        "chassi": None,
                        "renavam": None,
                        "anoFabricacao": None,
                        "numeroEixo": None,
                        "dataVencimentoCsv": None,
                        "exibirDetalhe": False,
                        "tipoServico": [],
                    },
                    "rotaDto": {
                        "id": 4592,
                        "empresaDto": None,
                        "cidadeOrigem": {
                            "id": 2504,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Betim",
                            "nomeComUf": "Betim - MG",
                            "nomeComUfNormalizado": "betim-mg",
                            "codigoIbge": "3106705",
                            "codigoAntt": "4200",
                            "urlImagem": None,
                        },
                        "cidadeDestino": {
                            "id": 1,
                            "uf": "DF",
                            "descricaoUf": "Distrito Federal",
                            "nome": "Brasília",
                            "nomeComUf": "Brasília - DF",
                            "nomeComUfNormalizado": "brasilia-df",
                            "codigoIbge": "5300108",
                            "codigoAntt": "60",
                            "urlImagem": None,
                        },
                        "trechosDto": [
                            {
                                "id": 46279,
                                "idEmpresa": 531,
                                "cidadeDestino": {
                                    "id": 2504,
                                    "uf": "MG",
                                    "descricaoUf": "Minas Gerais",
                                    "nome": "Betim",
                                    "nomeComUf": "Betim - MG",
                                    "nomeComUfNormalizado": "betim-mg",
                                    "codigoIbge": "3106705",
                                    "codigoAntt": "4200",
                                    "urlImagem": None,
                                },
                                "cidadeOrigem": None,
                                "cidadeOrigemAlternativa": None,
                                "cidadeDestinoAlternativa": None,
                                "rotaDto": {
                                    "id": 4592,
                                    "empresaDto": None,
                                    "cidadeOrigem": {
                                        "id": 2504,
                                        "uf": "MG",
                                        "descricaoUf": "Minas Gerais",
                                        "nome": "Betim",
                                        "nomeComUf": "Betim - MG",
                                        "nomeComUfNormalizado": "betim-mg",
                                        "codigoIbge": "3106705",
                                        "codigoAntt": "4200",
                                        "urlImagem": None,
                                    },
                                    "cidadeDestino": {
                                        "id": 1,
                                        "uf": "DF",
                                        "descricaoUf": "Distrito Federal",
                                        "nome": "Brasília",
                                        "nomeComUf": "Brasília - DF",
                                        "nomeComUfNormalizado": "brasilia-df",
                                        "codigoIbge": "5300108",
                                        "codigoAntt": "60",
                                        "urlImagem": None,
                                    },
                                    "trechosDto": [],
                                    "conexoes": [],
                                    "opcoesTrechoAlternativo": None,
                                    "prefixo": "12-0525-00",
                                    "delimitacao": None,
                                    "descricao": None,
                                    "nomeFantasiaEmpresa": None,
                                    "empresaId": None,
                                },
                                "ordem": 1,
                                "duracao": "00:00",
                                "pontoEmbarque": None,
                                "plataformaEmbarque": None,
                                "taxaEmbarque": "0",
                                "pedagio": "0",
                                "configuracaoBilhete": [],
                                "quilometragem": "0",
                                "conexao": None,
                                "habilitadoTrechoAlternativoLimeteEstadual": None,
                                "latitude": None,
                                "longitude": None,
                            },
                            {
                                "id": 46280,
                                "idEmpresa": 531,
                                "cidadeDestino": {
                                    "id": 2498,
                                    "uf": "MG",
                                    "descricaoUf": "Minas Gerais",
                                    "nome": "Belo Horizonte",
                                    "nomeComUf": "Belo Horizonte - MG",
                                    "nomeComUfNormalizado": "belohorizonte-mg",
                                    "codigoIbge": "3106200",
                                    "codigoAntt": "72",
                                    "urlImagem": None,
                                },
                                "cidadeOrigem": None,
                                "cidadeOrigemAlternativa": None,
                                "cidadeDestinoAlternativa": None,
                                "rotaDto": {
                                    "id": 4592,
                                    "empresaDto": None,
                                    "cidadeOrigem": {
                                        "id": 2504,
                                        "uf": "MG",
                                        "descricaoUf": "Minas Gerais",
                                        "nome": "Betim",
                                        "nomeComUf": "Betim - MG",
                                        "nomeComUfNormalizado": "betim-mg",
                                        "codigoIbge": "3106705",
                                        "codigoAntt": "4200",
                                        "urlImagem": None,
                                    },
                                    "cidadeDestino": {
                                        "id": 1,
                                        "uf": "DF",
                                        "descricaoUf": "Distrito Federal",
                                        "nome": "Brasília",
                                        "nomeComUf": "Brasília - DF",
                                        "nomeComUfNormalizado": "brasilia-df",
                                        "codigoIbge": "5300108",
                                        "codigoAntt": "60",
                                        "urlImagem": None,
                                    },
                                    "trechosDto": [],
                                    "conexoes": [],
                                    "opcoesTrechoAlternativo": None,
                                    "prefixo": "12-0525-00",
                                    "delimitacao": None,
                                    "descricao": None,
                                    "nomeFantasiaEmpresa": None,
                                    "empresaId": None,
                                },
                                "ordem": 2,
                                "duracao": "00:00",
                                "pontoEmbarque": "*",
                                "plataformaEmbarque": None,
                                "taxaEmbarque": "0,00",
                                "pedagio": "0,00",
                                "configuracaoBilhete": [],
                                "quilometragem": "0,00",
                                "conexao": None,
                                "habilitadoTrechoAlternativoLimeteEstadual": None,
                                "latitude": None,
                                "longitude": None,
                            },
                            {
                                "id": 46281,
                                "idEmpresa": 531,
                                "cidadeDestino": {
                                    "id": 1,
                                    "uf": "DF",
                                    "descricaoUf": "Distrito Federal",
                                    "nome": "Brasília",
                                    "nomeComUf": "Brasília - DF",
                                    "nomeComUfNormalizado": "brasilia-df",
                                    "codigoIbge": "5300108",
                                    "codigoAntt": "60",
                                    "urlImagem": None,
                                },
                                "cidadeOrigem": None,
                                "cidadeOrigemAlternativa": None,
                                "cidadeDestinoAlternativa": None,
                                "rotaDto": {
                                    "id": 4592,
                                    "empresaDto": None,
                                    "cidadeOrigem": {
                                        "id": 2504,
                                        "uf": "MG",
                                        "descricaoUf": "Minas Gerais",
                                        "nome": "Betim",
                                        "nomeComUf": "Betim - MG",
                                        "nomeComUfNormalizado": "betim-mg",
                                        "codigoIbge": "3106705",
                                        "codigoAntt": "4200",
                                        "urlImagem": None,
                                    },
                                    "cidadeDestino": {
                                        "id": 1,
                                        "uf": "DF",
                                        "descricaoUf": "Distrito Federal",
                                        "nome": "Brasília",
                                        "nomeComUf": "Brasília - DF",
                                        "nomeComUfNormalizado": "brasilia-df",
                                        "codigoIbge": "5300108",
                                        "codigoAntt": "60",
                                        "urlImagem": None,
                                    },
                                    "trechosDto": [],
                                    "conexoes": [],
                                    "opcoesTrechoAlternativo": None,
                                    "prefixo": "12-0525-00",
                                    "delimitacao": None,
                                    "descricao": None,
                                    "nomeFantasiaEmpresa": None,
                                    "empresaId": None,
                                },
                                "ordem": 3,
                                "duracao": "11:20",
                                "pontoEmbarque": "Terminal JK",
                                "plataformaEmbarque": None,
                                "taxaEmbarque": "0,00",
                                "pedagio": "0,00",
                                "configuracaoBilhete": [],
                                "quilometragem": "732,00",
                                "conexao": None,
                                "habilitadoTrechoAlternativoLimeteEstadual": None,
                                "latitude": None,
                                "longitude": None,
                            },
                            {
                                "id": 46282,
                                "idEmpresa": 531,
                                "cidadeDestino": {
                                    "id": 1,
                                    "uf": "DF",
                                    "descricaoUf": "Distrito Federal",
                                    "nome": "Brasília",
                                    "nomeComUf": "Brasília - DF",
                                    "nomeComUfNormalizado": "brasilia-df",
                                    "codigoIbge": "5300108",
                                    "codigoAntt": "60",
                                    "urlImagem": None,
                                },
                                "cidadeOrigem": None,
                                "cidadeOrigemAlternativa": None,
                                "cidadeDestinoAlternativa": None,
                                "rotaDto": {
                                    "id": 4592,
                                    "empresaDto": None,
                                    "cidadeOrigem": {
                                        "id": 2504,
                                        "uf": "MG",
                                        "descricaoUf": "Minas Gerais",
                                        "nome": "Betim",
                                        "nomeComUf": "Betim - MG",
                                        "nomeComUfNormalizado": "betim-mg",
                                        "codigoIbge": "3106705",
                                        "codigoAntt": "4200",
                                        "urlImagem": None,
                                    },
                                    "cidadeDestino": {
                                        "id": 1,
                                        "uf": "DF",
                                        "descricaoUf": "Distrito Federal",
                                        "nome": "Brasília",
                                        "nomeComUf": "Brasília - DF",
                                        "nomeComUfNormalizado": "brasilia-df",
                                        "codigoIbge": "5300108",
                                        "codigoAntt": "60",
                                        "urlImagem": None,
                                    },
                                    "trechosDto": [],
                                    "conexoes": [],
                                    "opcoesTrechoAlternativo": None,
                                    "prefixo": "12-0525-00",
                                    "delimitacao": None,
                                    "descricao": None,
                                    "nomeFantasiaEmpresa": None,
                                    "empresaId": None,
                                },
                                "ordem": 4,
                                "duracao": "00:00",
                                "pontoEmbarque": "Hotel Nacional - Sala VIP Transbrasil",
                                "plataformaEmbarque": None,
                                "taxaEmbarque": "0,00",
                                "pedagio": "0,00",
                                "configuracaoBilhete": [],
                                "quilometragem": "0,00",
                                "conexao": None,
                                "habilitadoTrechoAlternativoLimeteEstadual": None,
                                "latitude": None,
                                "longitude": None,
                            },
                        ],
                        "conexoes": [],
                        "opcoesTrechoAlternativo": None,
                        "prefixo": "12-0525-00",
                        "delimitacao": "78540",
                        "descricao": None,
                        "nomeFantasiaEmpresa": None,
                        "empresaId": None,
                    },
                    "motoristas": [],
                    "assentosDisponiveis": 32,
                    "idEmpresa": 531,
                    "nomeEmpresa": "VIACAO AMARELINHO TRANSPORTE DE PASSAGEIROS LTDA",
                    "nomeFantasiaEmpresa": "BUSER - AMAR - VIVITUR",
                    "poltronas": [
                        {
                            "id": 10323416,
                            "numero": 1,
                            "tipoPreco": "LEITO",
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": None,
                            "andar": None,
                        },
                        {
                            "id": 10323417,
                            "numero": 5,
                            "tipoPreco": "LEITO",
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": None,
                            "andar": None,
                        },
                        {
                            "id": 10323417,
                            "numero": 5,
                            "tipoPreco": "LEITO",
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": None,
                            "andar": None,
                        },
                        {
                            "id": 10323417,
                            "numero": 5,
                            "tipoPreco": "LEITO",
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": None,
                            "andar": None,
                        },
                        {
                            "id": 10323417,
                            "numero": 6,
                            "tipoPreco": "LEITO_INDIVIDUAL",
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": None,
                            "andar": None,
                        },
                        {
                            "id": 10323417,
                            "numero": 7,
                            "tipoPreco": "LEITO_INDIVIDUAL",
                            "reservada": False,
                            "bloqueada": False,
                            "dataSaida": None,
                            "bloqueios": None,
                            "andar": None,
                        },
                    ],
                    "descricaoTipoVeiculo": "Leito",
                    "quantidadeAssentos": None,
                    "preco": None,
                    "quilometragem": "732,00",
                    "tipoAjusteItinerario": None,
                    "valorAjuste": None,
                    "tipoPreco": {"tipoPreco": "LEITO", "descricaoTipoPreco": "Leito"},
                    "andar": 1,
                    "trechosBloqueados": [
                        {
                            "trechoOrigem": {"chave": "46280", "valor": "Belo Horizonte-MG"},
                            "trechoDestino": {"chave": "46281", "valor": "Brasília-DF"},
                        }
                    ],
                    "cidadesBloqueados": [{"cidade": {"chave": "2504", "valor": "Betim-MG"}}],
                    "trechosBloqueadosDesconto": [],
                    "habilitadoSite": True,
                    "assentosVendidos": None,
                    "sePodeEmbarcar": False,
                    "tipoConfiguracaoPreco": None,
                    "horaPartidaRegra": None,
                    "quantidadePoltronasRegra": None,
                    "ativo": True,
                    "prestador": None,
                    "complemento": None,
                    "tipoCategoria": "UNICA",
                    "valorTipoPrecoPadrao": None,
                    "tipoPrecoPadrao": None,
                },
                {
                    "id": 289142,
                    "dataPartida": "2024-08-12",
                    "horaSaida": "19:00",
                    "dataHoraPartida": "12/08/2024 19:00",
                    "dataHoraChegada": "13/08/2024 06:20",
                    "veiculoDto": {
                        "id": 883,
                        "descricao": "RFB1G79",
                        "empresaDto": None,
                        "mapaVeiculoDto": {
                            "id": 147,
                            "fileirasPrimeiroAndar": [
                                {
                                    "id": 802,
                                    "numero": 4,
                                    "exibeCorredor": False,
                                    "assentos": [
                                        {"id": 6850, "numero": 3, "ordem": 0, "tipoAssento": "NORMAL", "poltrona": None}
                                    ],
                                }
                            ],
                            "fileirasSegundoAndar": [],
                            "seDoisAndares": False,
                            "quantidadeAssentos": 45,
                            "modelo": "13 LEITO INFERIOR",
                        },
                        "idRastreamento": None,
                        "seDoisAndares": True,
                        "imagens": None,
                        "placa": None,
                        "modelo": None,
                        "tipoVeiculo": None,
                        "quilometragem": None,
                        "chassi": None,
                        "renavam": None,
                        "anoFabricacao": None,
                        "numeroEixo": None,
                        "dataVencimentoCsv": None,
                        "exibirDetalhe": False,
                        "tipoServico": [],
                    },
                    "rotaDto": {
                        "id": 4592,
                        "empresaDto": None,
                        "cidadeOrigem": {
                            "id": 2504,
                            "uf": "MG",
                            "descricaoUf": "Minas Gerais",
                            "nome": "Betim",
                            "nomeComUf": "Betim - MG",
                            "nomeComUfNormalizado": "betim-mg",
                            "codigoIbge": "3106705",
                            "codigoAntt": "4200",
                            "urlImagem": None,
                        },
                        "cidadeDestino": {
                            "id": 1,
                            "uf": "DF",
                            "descricaoUf": "Distrito Federal",
                            "nome": "Brasília",
                            "nomeComUf": "Brasília - DF",
                            "nomeComUfNormalizado": "brasilia-df",
                            "codigoIbge": "5300108",
                            "codigoAntt": "60",
                            "urlImagem": None,
                        },
                        "trechosDto": [
                            {
                                "id": 46279,
                                "idEmpresa": 531,
                                "cidadeDestino": {
                                    "id": 2504,
                                    "uf": "MG",
                                    "descricaoUf": "Minas Gerais",
                                    "nome": "Betim",
                                    "nomeComUf": "Betim - MG",
                                    "nomeComUfNormalizado": "betim-mg",
                                    "codigoIbge": "3106705",
                                    "codigoAntt": "4200",
                                    "urlImagem": None,
                                },
                                "cidadeOrigem": None,
                                "cidadeOrigemAlternativa": None,
                                "cidadeDestinoAlternativa": None,
                                "rotaDto": {
                                    "id": 4592,
                                    "empresaDto": None,
                                    "cidadeOrigem": {
                                        "id": 2504,
                                        "uf": "MG",
                                        "descricaoUf": "Minas Gerais",
                                        "nome": "Betim",
                                        "nomeComUf": "Betim - MG",
                                        "nomeComUfNormalizado": "betim-mg",
                                        "codigoIbge": "3106705",
                                        "codigoAntt": "4200",
                                        "urlImagem": None,
                                    },
                                    "cidadeDestino": {
                                        "id": 1,
                                        "uf": "DF",
                                        "descricaoUf": "Distrito Federal",
                                        "nome": "Brasília",
                                        "nomeComUf": "Brasília - DF",
                                        "nomeComUfNormalizado": "brasilia-df",
                                        "codigoIbge": "5300108",
                                        "codigoAntt": "60",
                                        "urlImagem": None,
                                    },
                                    "trechosDto": [],
                                    "conexoes": [],
                                    "opcoesTrechoAlternativo": None,
                                    "prefixo": "12-0525-00",
                                    "delimitacao": None,
                                    "descricao": None,
                                    "nomeFantasiaEmpresa": None,
                                    "empresaId": None,
                                },
                                "ordem": 1,
                                "duracao": "00:00",
                                "pontoEmbarque": None,
                                "plataformaEmbarque": None,
                                "taxaEmbarque": "0",
                                "pedagio": "0",
                                "configuracaoBilhete": [],
                                "quilometragem": "0",
                                "conexao": None,
                                "habilitadoTrechoAlternativoLimeteEstadual": None,
                                "latitude": None,
                                "longitude": None,
                            },
                            {
                                "id": 46280,
                                "idEmpresa": 531,
                                "cidadeDestino": {
                                    "id": 2498,
                                    "uf": "MG",
                                    "descricaoUf": "Minas Gerais",
                                    "nome": "Belo Horizonte",
                                    "nomeComUf": "Belo Horizonte - MG",
                                    "nomeComUfNormalizado": "belohorizonte-mg",
                                    "codigoIbge": "3106200",
                                    "codigoAntt": "72",
                                    "urlImagem": None,
                                },
                                "cidadeOrigem": None,
                                "cidadeOrigemAlternativa": None,
                                "cidadeDestinoAlternativa": None,
                                "rotaDto": {
                                    "id": 4592,
                                    "empresaDto": None,
                                    "cidadeOrigem": {
                                        "id": 2504,
                                        "uf": "MG",
                                        "descricaoUf": "Minas Gerais",
                                        "nome": "Betim",
                                        "nomeComUf": "Betim - MG",
                                        "nomeComUfNormalizado": "betim-mg",
                                        "codigoIbge": "3106705",
                                        "codigoAntt": "4200",
                                        "urlImagem": None,
                                    },
                                    "cidadeDestino": {
                                        "id": 1,
                                        "uf": "DF",
                                        "descricaoUf": "Distrito Federal",
                                        "nome": "Brasília",
                                        "nomeComUf": "Brasília - DF",
                                        "nomeComUfNormalizado": "brasilia-df",
                                        "codigoIbge": "5300108",
                                        "codigoAntt": "60",
                                        "urlImagem": None,
                                    },
                                    "trechosDto": [],
                                    "conexoes": [],
                                    "opcoesTrechoAlternativo": None,
                                    "prefixo": "12-0525-00",
                                    "delimitacao": None,
                                    "descricao": None,
                                    "nomeFantasiaEmpresa": None,
                                    "empresaId": None,
                                },
                                "ordem": 2,
                                "duracao": "00:00",
                                "pontoEmbarque": "*",
                                "plataformaEmbarque": None,
                                "taxaEmbarque": "0,00",
                                "pedagio": "0,00",
                                "configuracaoBilhete": [],
                                "quilometragem": "0,00",
                                "conexao": None,
                                "habilitadoTrechoAlternativoLimeteEstadual": None,
                                "latitude": None,
                                "longitude": None,
                            },
                            {
                                "id": 46281,
                                "idEmpresa": 531,
                                "cidadeDestino": {
                                    "id": 1,
                                    "uf": "DF",
                                    "descricaoUf": "Distrito Federal",
                                    "nome": "Brasília",
                                    "nomeComUf": "Brasília - DF",
                                    "nomeComUfNormalizado": "brasilia-df",
                                    "codigoIbge": "5300108",
                                    "codigoAntt": "60",
                                    "urlImagem": None,
                                },
                                "cidadeOrigem": None,
                                "cidadeOrigemAlternativa": None,
                                "cidadeDestinoAlternativa": None,
                                "rotaDto": {
                                    "id": 4592,
                                    "empresaDto": None,
                                    "cidadeOrigem": {
                                        "id": 2504,
                                        "uf": "MG",
                                        "descricaoUf": "Minas Gerais",
                                        "nome": "Betim",
                                        "nomeComUf": "Betim - MG",
                                        "nomeComUfNormalizado": "betim-mg",
                                        "codigoIbge": "3106705",
                                        "codigoAntt": "4200",
                                        "urlImagem": None,
                                    },
                                    "cidadeDestino": {
                                        "id": 1,
                                        "uf": "DF",
                                        "descricaoUf": "Distrito Federal",
                                        "nome": "Brasília",
                                        "nomeComUf": "Brasília - DF",
                                        "nomeComUfNormalizado": "brasilia-df",
                                        "codigoIbge": "5300108",
                                        "codigoAntt": "60",
                                        "urlImagem": None,
                                    },
                                    "trechosDto": [],
                                    "conexoes": [],
                                    "opcoesTrechoAlternativo": None,
                                    "prefixo": "12-0525-00",
                                    "delimitacao": None,
                                    "descricao": None,
                                    "nomeFantasiaEmpresa": None,
                                    "empresaId": None,
                                },
                                "ordem": 3,
                                "duracao": "11:20",
                                "pontoEmbarque": "Terminal JK",
                                "plataformaEmbarque": None,
                                "taxaEmbarque": "0,00",
                                "pedagio": "0,00",
                                "configuracaoBilhete": [],
                                "quilometragem": "732,00",
                                "conexao": None,
                                "habilitadoTrechoAlternativoLimeteEstadual": None,
                                "latitude": None,
                                "longitude": None,
                            },
                            {
                                "id": 46282,
                                "idEmpresa": 531,
                                "cidadeDestino": {
                                    "id": 1,
                                    "uf": "DF",
                                    "descricaoUf": "Distrito Federal",
                                    "nome": "Brasília",
                                    "nomeComUf": "Brasília - DF",
                                    "nomeComUfNormalizado": "brasilia-df",
                                    "codigoIbge": "5300108",
                                    "codigoAntt": "60",
                                    "urlImagem": None,
                                },
                                "cidadeOrigem": None,
                                "cidadeOrigemAlternativa": None,
                                "cidadeDestinoAlternativa": None,
                                "rotaDto": {
                                    "id": 4592,
                                    "empresaDto": None,
                                    "cidadeOrigem": {
                                        "id": 2504,
                                        "uf": "MG",
                                        "descricaoUf": "Minas Gerais",
                                        "nome": "Betim",
                                        "nomeComUf": "Betim - MG",
                                        "nomeComUfNormalizado": "betim-mg",
                                        "codigoIbge": "3106705",
                                        "codigoAntt": "4200",
                                        "urlImagem": None,
                                    },
                                    "cidadeDestino": {
                                        "id": 1,
                                        "uf": "DF",
                                        "descricaoUf": "Distrito Federal",
                                        "nome": "Brasília",
                                        "nomeComUf": "Brasília - DF",
                                        "nomeComUfNormalizado": "brasilia-df",
                                        "codigoIbge": "5300108",
                                        "codigoAntt": "60",
                                        "urlImagem": None,
                                    },
                                    "trechosDto": [],
                                    "conexoes": [],
                                    "opcoesTrechoAlternativo": None,
                                    "prefixo": "12-0525-00",
                                    "delimitacao": None,
                                    "descricao": None,
                                    "nomeFantasiaEmpresa": None,
                                    "empresaId": None,
                                },
                                "ordem": 4,
                                "duracao": "00:00",
                                "pontoEmbarque": "Hotel Nacional - Sala VIP Transbrasil",
                                "plataformaEmbarque": None,
                                "taxaEmbarque": "0,00",
                                "pedagio": "0,00",
                                "configuracaoBilhete": [],
                                "quilometragem": "0,00",
                                "conexao": None,
                                "habilitadoTrechoAlternativoLimeteEstadual": None,
                                "latitude": None,
                                "longitude": None,
                            },
                        ],
                        "conexoes": [],
                        "opcoesTrechoAlternativo": None,
                        "prefixo": "12-0525-00",
                        "delimitacao": "78540",
                        "descricao": None,
                        "nomeFantasiaEmpresa": None,
                        "empresaId": None,
                    },
                    "motoristas": [],
                    "assentosDisponiveis": 13,
                    "idEmpresa": 531,
                    "nomeEmpresa": "VIACAO AMARELINHO TRANSPORTE DE PASSAGEIROS LTDA",
                    "nomeFantasiaEmpresa": "BUSER - AMAR - VIVITUR",
                    "poltronas": [],
                    "descricaoTipoVeiculo": "Leito Individual",
                    "quantidadeAssentos": None,
                    "preco": None,
                    "quilometragem": "732,00",
                    "tipoAjusteItinerario": None,
                    "valorAjuste": None,
                    "tipoPreco": {"tipoPreco": "LEITO_INDIVIDUAL", "descricaoTipoPreco": "Leito Individual"},
                    "andar": 2,
                    "trechosBloqueados": [],
                    "cidadesBloqueados": [{"cidade": {"chave": "47", "valor": "Betim-MG"}}],
                    "trechosBloqueadosDesconto": [],
                    "habilitadoSite": True,
                    "assentosVendidos": None,
                    "sePodeEmbarcar": False,
                    "tipoConfiguracaoPreco": None,
                    "horaPartidaRegra": None,
                    "quantidadePoltronasRegra": None,
                    "ativo": True,
                    "prestador": None,
                    "complemento": None,
                    "tipoCategoria": "UNICA",
                    "valorTipoPrecoPadrao": None,
                    "tipoPrecoPadrao": None,
                },
            ],
            "total": 2,
        }


class MockCadastrarGrupo(MockBase):
    @staticmethod
    def response(qtd_andares):
        return [43809 + i for i in range(qtd_andares)]


class MockListarReservasViagem(MockBase):
    @staticmethod
    def response_ids():
        return [638842, 639607]

    @staticmethod
    def response():
        return [
            {
                "id": 638842,
                "idTrechoOrigem": 19384,
                "descCidadeOrigem": "Recife-PE",
                "descCidadeDestino": "Salvador-BA",
                "descCidadeOrigemAlternativa": None,
                "descCidadeDestinoAlternativa": None,
                "idCidadeOrigem": None,
                "idCidadeDestino": None,
                "poltronaId": 2270120,
                "desconto": "0.00",
                "passageiroDto": {
                    "id": 638842,
                    "nome": "Passageiro Um,",
                    "documentoComFoto": "439061325",
                    "cpfPassageiro": "10101001090",
                    "poltronaId": None,
                    "itinerarioId": None,
                    "numeroPoltrona": 10,
                    "dtNascimento": None,
                    "criancaDto": None,
                    "sentido": None,
                    "tipoEmissao": "NORMAL",
                    "descTipoEmissao": "Normal",
                    "nis": None,
                    "validade": None,
                    "renda": "0,00",
                    "cargoProfissao": None,
                    "numeroDeficiente": None,
                    "opcaoDescontoMeia": None,
                    "telefone": "75988543673",
                    "matricula": None,
                    "comAcompanhante": False,
                    "tipoDocumento": "RG",
                },
                "empresaId": None,
                "valor": "109.90",
                "dataHoraPartida": "01/04/2022 21:20",
                "dataVenda": "01/04/2022 14:23",
                "dataHoraEmbarque": "04/04/2022 20:03",
                "dataHoraDesembarque": None,
                "nomeEmpresa": "TRANSPORTE COLETIVO BRASIL LTDA - MEU BRASIL TUR",
                "cnpjEmpresa": "56.957.445/0001-19",
                "urlLogoEmpresa": (
                    "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/02ef7bb5-7df1-472e-965c-2007ad800547"
                ),
                "itinerarioId": 61387,
                "inscricaoEstadual": "133223091115",
                "embarque": " Avenida Hilton Souto Maior, s/n - Mangabeira ",
                "taxaEmbarque": "0.00",
                "taxaServico": None,
                "rota": "Fortaleza-CE à São Bernardo do Campo-SP",
                "prefixoRota": "08-9437-00",
                "numeroPoltrona": 10,
                "descricaoTipoPreco": "Semi Leito",
                "pedidoId": 406037,
                "valorTotalReserva": "109.90",
                "situacaoReserva": "APROVADO",
                "custoCancelamento": "0.00",
                "valorEstorno": "0",
                "custoRemarcacao": None,
                "descricaoFormasPagamento": "Dinheiro",
                "comissao": "0.00",
                "impostoRetido": "17.86",
                "seRemarcacao": None,
                "valorEmpresa": "92.04",
                "nomeVendedor": "1224 - BUSER 01",
                "nomeAgencia": None,
                "aliquotaICMS": "12,00",
                "valorICMS": "13.19",
                "tarifa": "109.90",
                "idRemarcada": None,
                "estornoImpostoRetido": None,
                "estornoComissao": None,
                "observacao": None,
                "transmissaoBpe": None,
                "endereco": None,
                "outrosTributos": None,
                "outrosDescontos": None,
                "icmsDesconto": None,
                "nomeResponsavelEmbarque": None,
                "nomeResponsavelDesembarque": None,
            },
            {
                "id": 639607,
                "idTrechoOrigem": 19383,
                "descCidadeOrigem": "João Pessoa-PB",
                "descCidadeDestino": "Recife-PE",
                "descCidadeOrigemAlternativa": None,
                "descCidadeDestinoAlternativa": None,
                "idCidadeOrigem": None,
                "idCidadeDestino": None,
                "poltronaId": 2270120,
                "desconto": "0.00",
                "passageiroDto": {
                    "id": 639607,
                    "nome": "Passageiro Dois",
                    "documentoComFoto": "405873001",
                    "cpfPassageiro": "10101001090",
                    "poltronaId": None,
                    "itinerarioId": None,
                    "numeroPoltrona": 11,
                    "dtNascimento": None,
                    "criancaDto": None,
                    "sentido": None,
                    "tipoEmissao": "NORMAL",
                    "descTipoEmissao": "Normal",
                    "nis": None,
                    "validade": None,
                    "renda": "0,00",
                    "cargoProfissao": None,
                    "numeroDeficiente": None,
                    "opcaoDescontoMeia": None,
                    "telefone": "21996874836",
                    "matricula": None,
                    "comAcompanhante": False,
                    "tipoDocumento": "RG",
                },
                "empresaId": None,
                "valor": "35.90",
                "dataHoraPartida": "01/04/2022 19:00",
                "dataVenda": "01/04/2022 17:19",
                "dataHoraEmbarque": "04/04/2022 20:03",
                "dataHoraDesembarque": None,
                "nomeEmpresa": "TRANSPORTE COLETIVO BRASIL LTDA - MEU BRASIL TUR",
                "cnpjEmpresa": "56.957.445/0001-19",
                "urlLogoEmpresa": (
                    "https://imagens-vexado-live.s3-sa-east-1.amazonaws.com/02ef7bb5-7df1-472e-965c-2007ad800547"
                ),
                "itinerarioId": 61387,
                "inscricaoEstadual": "133223091115",
                "embarque": "*",
                "taxaEmbarque": "0.00",
                "taxaServico": None,
                "rota": "Fortaleza-CE à São Bernardo do Campo-SP",
                "prefixoRota": "08-9437-00",
                "numeroPoltrona": 11,
                "descricaoTipoPreco": "Semi Leito",
                "pedidoId": 406037,
                "valorTotalReserva": "35.90",
                "situacaoReserva": "APROVADO",
                "custoCancelamento": "0.00",
                "valorEstorno": "0",
                "custoRemarcacao": None,
                "descricaoFormasPagamento": "Dinheiro",
                "comissao": "0.00",
                "impostoRetido": "5.83",
                "seRemarcacao": None,
                "valorEmpresa": "30.07",
                "nomeVendedor": "1224 - BUSER 01",
                "nomeAgencia": None,
                "aliquotaICMS": "12,00",
                "valorICMS": "4.31",
                "tarifa": "35.90",
                "idRemarcada": None,
                "estornoImpostoRetido": None,
                "estornoComissao": None,
                "observacao": None,
                "transmissaoBpe": None,
                "endereco": {
                    "id": None,
                    "cep": "58010-150",
                    "logradouro": "Rua Francisco Londres",
                    "localidade": "João Pessoa",
                    "complemento": "***",
                    "bairro": "Varadouro",
                    "uf": "PB",
                    "unidade": None,
                    "ibge": None,
                    "numero": None,
                    "codigoCidade": None,
                    "nomeCidade": None,
                    "cepNumerico": "58010150",
                },
                "outrosTributos": None,
                "outrosDescontos": None,
                "icmsDesconto": None,
                "nomeResponsavelEmbarque": None,
                "nomeResponsavelDesembarque": None,
            },
        ]

    @staticmethod
    def response_nenhuma_reserva():
        return []

    @staticmethod
    def response_itinerario_vazio():
        return {
            "violacoes": [
                {
                    "tipo": "ERRO",
                    "codigo": "itinerarioVazio",
                    "mensagem": "Itinerário sem reservas efetuadas",
                }
            ]
        }

from model_bakery import baker

from rodoviaria.api.vexado.auth import VexadoAuth


def test_get_login_from_company():
    company = baker.make("rodoviaria.Company")
    vexado_login = baker.make("rodoviaria.VexadoLogin", company=company)
    auth_obj = VexadoAuth.from_company(company)
    assert auth_obj.site == vexado_login.site
    assert auth_obj.username == vexado_login.user
    assert auth_obj.password == vexado_login.password


def test_get_login_from_client():
    company = baker.make("rodoviaria.Company")
    vexado_login = baker.make("rodoviaria.VexadoLogin", company=company)
    auth_obj = VexadoAuth.from_client(vexado_login)
    assert auth_obj.site == vexado_login.site
    assert auth_obj.username == vexado_login.user
    assert auth_obj.password == vexado_login.password

import pytest

from rodoviaria.api.vexado.models import LocalidadeParada, Violacao, ViolacaoDominio


def test_violacao_parse_obj_simple():
    obj = {
        "tipo": "ERRO",
        "codigo": "usuarioCadastrado",
        "mensagem": "Esse usuário já está cadastrado na empresa.",
    }
    violacao = Violacao.parse_obj(obj)
    result = violacao.dict()

    assert result == {
        "tipo": "ERRO",
        "codigo": "usuarioCadastrado",
        "mensagem": "Esse usuário já está cadastrado na empresa.",
        "violacao_dominio": None,
    }


def test_violacao_parse_obj_complex():
    obj = {
        "tipo": "ERRO",
        "codigo": "erroReserva",
        "mensagem": (
            "ViolacaoDominio{tipo=ERRO, codigo='valorReservaErrado', mensagem='Valor da reserva informado está"
            " errado.'}"
        ),
    }
    violacao = Violacao.parse_obj(obj)
    result = violacao.dict()

    assert result == {
        "tipo": "ERRO",
        "codigo": "erroReserva",
        "mensagem": (
            "ViolacaoDominio{tipo=ERRO, codigo='valorReservaErrado', mensagem='Valor da reserva informado está"
            " errado.'}"
        ),
        "violacao_dominio": {
            "tipo": "ERRO",
            "codigo": "valorReservaErrado",
            "mensagem": "Valor da reserva informado está errado.",
        },
    }


def test_violacaodominio_validate_wrong_type():
    with pytest.raises(TypeError) as excinfo:
        ViolacaoDominio.validate({})
        assert "string expected" in str(excinfo.value)


def test_violacaodominio_validate_str_message():
    value = "Esse usuário já está cadastrado na empresa."
    result = ViolacaoDominio.validate(value)
    assert result is None


def test_violacaodominio_validate_only_tipo():
    value = "ViolacaoDominio{tipo=ERRO}"
    result = ViolacaoDominio.validate(value)
    assert result == {
        "tipo": "ERRO",
        "codigo": None,
        "mensagem": None,
    }


def test_violacaodominio_validate_only_codigo():
    value = "ViolacaoDominio{codigo='valorReservaErrado'}"
    result = ViolacaoDominio.validate(value)
    assert result == {
        "tipo": None,
        "codigo": "valorReservaErrado",
        "mensagem": None,
    }


def test_violacaodominio_validate_only_mensagem():
    value = "ViolacaoDominio{mensagem='Valor da reserva informado está errado.'}"
    result = ViolacaoDominio.validate(value)
    assert result == {
        "tipo": None,
        "codigo": None,
        "mensagem": "Valor da reserva informado está errado.",
    }


def test_violacaodominio_validate_complete():
    value = (
        "ViolacaoDominio{tipo=ERRO, codigo='valorReservaErrado', mensagem='Valor da reserva informado está errado.'}"
    )
    result = ViolacaoDominio.validate(value)
    assert result == {
        "tipo": "ERRO",
        "codigo": "valorReservaErrado",
        "mensagem": "Valor da reserva informado está errado.",
    }


def test_localidade_form():
    entrada = {
        "nome": "Sao Paulo",
        "id": "20",
        "uf": "SP",
        "codigoIbge": "321",
        "nomeComUf": "Sao Paulo - SP",
    }
    localidade = LocalidadeParada.parse_obj(entrada)
    assert localidade.descricao == "Sao Paulo - SP"
    assert localidade.external_local_id == "20"
    assert localidade.external_cidade_id == "20"
    assert localidade.uf == "SP"
    assert localidade.nome_cidade == "Sao Paulo"
    assert localidade.id_cidade_ibge == 321

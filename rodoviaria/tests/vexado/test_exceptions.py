from rodoviaria.api.vexado.exceptions import VexadoAPIError
from rodoviaria.api.vexado.models import Violacao


def test_vexado_api_error_codigos():
    violacoes = [
        Violacao(
            tipo="ERRO",
            codigo="usuarioCadastrado",
            mensagem="Esse usuário já está cadastrado na empresa.",
        ),
        Violacao(
            tipo="ERRO",
            codigo="erroReserva",
            mensagem=(
                "ViolacaoDominio{tipo=ERRO, codigo='valorReservaErrado', mensagem='Valor da reserva informado está"
                " errado.'}"
            ),
        ),
        <PERSON>cao(
            tipo="ERRO",
            mensagem="Erro genérico!",
        ),
    ]
    exc = VexadoAPIError("mensagem de erro", violacoes=violacoes)

    assert exc.codigos == ["usuarioCadastrado", "erroReserva", "valorReservaErrado"]

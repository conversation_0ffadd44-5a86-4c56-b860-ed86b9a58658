import json
import re
from types import SimpleNamespace

import pytest
import responses
from model_bakery import baker
from responses.matchers import query_param_matcher

import rodoviaria.api.vexado.endpoints as endpoints
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.models.core import Company
from rodoviaria.tests.vexado import mocker as mocker_vexado


@pytest.fixture
def vexado_company_marketplace(vexado_company):
    vexado_company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_company.save()


@pytest.fixture
def vexado_cidades(buser_cidades, vexado_company):
    cidade_goiania = baker.make(
        "rodoviaria.Cidade",
        id_external="1",
        cidade_internal_id=buser_cidades.cidade_goiania.id,
        company=vexado_company,
        name="Goiânia",
        timezone="America/Sao_Paulo",
    )
    cidade_inhumas = baker.make(
        "rodoviaria.Cidade",
        id_external="2",
        cidade_internal_id=buser_cidades.cidade_inhumas.id,
        company=vexado_company,
        name="Inhumas",
        timezone="America/Bahia",
    )
    yield SimpleNamespace(cidade_goiania=cidade_goiania, cidade_inhumas=cidade_inhumas)
    cidade_goiania.delete()
    cidade_inhumas.delete()


@pytest.fixture
def vexado_locais(vexado_cidades, buser_locais):
    origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="1",
        cidade=vexado_cidades.cidade_goiania,
        local_embarque_internal_id=buser_locais.local_go_terminal.id,
        nickname="Terminal Rodoviario de Goiania",
    )
    destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="2932",
        cidade=vexado_cidades.cidade_inhumas,
        local_embarque_internal_id=buser_locais.local_inh_terminal.id,
        nickname="Terminal Rodoviário de Inhumas",
    )
    yield SimpleNamespace(origem=origem, destino=destino)
    origem.delete()
    destino.delete()


@pytest.fixture
def vexado_grupos(vexado_company, vexado_grupos_mockado):
    def vexado_grupo(grupo):
        record = baker.make(
            "rodoviaria.Grupo",
            grupo_internal_id=grupo.id,
            company_integracao_id=vexado_company.id,
            linha="GOIÂNIA (GO) X INHUMAS (GO)",
        )
        return record

    ida = vexado_grupo(vexado_grupos_mockado.ida.grupo)
    volta = vexado_grupo(vexado_grupos_mockado.volta.grupo)
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def vexado_trechoclasses(vexado_grupos, vexado_locais, vexado_grupos_mockado):
    def vexado_trechoclasse(internal_trechoclasse, grupo, origem, destino, preco_rodoviaria):
        return baker.make(
            "rodoviaria.TrechoClasse",
            trechoclasse_internal_id=internal_trechoclasse.id,
            provider_data=json.dumps(mocker_vexado.MockBuscarServico.response()["passagensIda"][0]),
            grupo=grupo,
            external_id="21136",
            origem=origem,
            destino=destino,
            datetime_ida=internal_trechoclasse.datetime_ida,
            preco_rodoviaria=preco_rodoviaria,
        )

    ida = vexado_trechoclasse(
        vexado_grupos_mockado.ida.trechoclasse,
        vexado_grupos.ida,
        vexado_locais.origem,
        vexado_locais.destino,
        vexado_grupos_mockado.ida.trechoclasse.preco_rodoviaria,
    )
    volta = vexado_trechoclasse(
        vexado_grupos_mockado.volta.trechoclasse,
        vexado_grupos.volta,
        vexado_locais.destino,
        vexado_locais.origem,
        vexado_grupos_mockado.volta.trechoclasse.preco_rodoviaria,
    )
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def vexado_api(vexado_login):
    return VexadoAPI(vexado_login.company)


@pytest.fixture
def vexado_api_marketplace(vexado_login):
    vexado_login.company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_login.company.save()
    return VexadoAPI(vexado_login.company)


@pytest.fixture
def mock_login(requests_mock, vexado_api):
    yield requests_mock.add(
        responses.POST,
        f"{vexado_api.base_url}/{endpoints.GetToken.path}",
        json=mocker_vexado.MockLogin.response(),
    )
    requests_mock.remove(responses.POST, f"{vexado_api.base_url}/{endpoints.GetToken.path}")


@pytest.fixture
def mock_login_twice(requests_mock, vexado_api):
    requests_mock.add(
        responses.POST,
        f"{vexado_api.base_url}/{endpoints.GetToken.path}",
        json=mocker_vexado.MockLogin.response(mocker_vexado.TOKEN_1),
    )
    requests_mock.add(
        responses.POST,
        f"{vexado_api.base_url}/{endpoints.GetToken.path}",
        json=mocker_vexado.MockLogin.response(mocker_vexado.TOKEN_2),
    )
    yield requests_mock
    requests_mock.remove(responses.POST, f"{vexado_api.base_url}/{endpoints.GetToken.path}")


@pytest.fixture
@responses.activate
def mock_connection_error(vexado_api, requests_mock):
    pass


@pytest.fixture
def mock_buscar_servico(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.BuscarServico.path}".format(origem=1, destino=2932, data_ida="2021-11-11")
    requests_mock.add(responses.GET, url, json=mocker_vexado.MockBuscarServico.response())


@pytest.fixture
def mock_buscar_servico_meia_noite(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.BuscarServico.path}".format(origem=1, destino=2932, data_ida="2021-11-12")
    response = mocker_vexado.MockBuscarServico.response()
    response["passagensIda"][1]["dataHoraPartida"] = "2021-11-12T00:00:00"
    requests_mock.add(responses.GET, url, json=response)


@pytest.fixture
def mock_nenhum_servico(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.BuscarServico.path}".format(origem=1, destino=2932, data_ida="2021-11-11")
    requests_mock.add(responses.GET, url, json=mocker_vexado.MockBuscarServico.response_nenhum_servico())


@pytest.fixture
def mock_get_poltronas(vexado_api, mock_login, requests_mock):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(responses.GET, url, json=mocker_vexado.GetPoltronasLivres.response_completa())


@pytest.fixture
def mock_get_poltronas_livres(vexado_api, mock_login, requests_mock):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(responses.GET, url, json=mocker_vexado.GetPoltronasLivres.response())


@pytest.fixture
def mock_get_poltronas_livres_classe_mista(vexado_api, mock_login, requests_mock):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(responses.GET, url, json=mocker_vexado.GetPoltronasLivres.response_classe_mista())


@pytest.fixture
def mock_poltronas_insuficientes(vexado_api, mock_login, requests_mock):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(
        responses.GET,
        url,
        json=mocker_vexado.GetPoltronasLivres.response_poltronas_insuficientes(),
    )


@pytest.fixture
def mock_cancela_venda(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.CancelarReservas.path}"
    requests_mock.add(responses.POST, url, status=200)
    yield requests_mock


@pytest.fixture
def mock_cancela_venda_error(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.CancelarReservas.path}"
    requests_mock.add(
        responses.POST,
        url,
        json=mocker_vexado.MockCancelarReservas.response_error(),
        status=412,
    )


@pytest.fixture
def mock_cancela_venda_nao_pode_cancelar(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.CancelarReservas.path}"
    requests_mock.add(
        responses.POST,
        url,
        json=mocker_vexado.MockCancelarReservas.response_nao_pode_cancelar(),
        status=412,
    )
    yield requests_mock


@pytest.fixture
def mock_cancela_venda_pedido_nao_encontrado(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.CancelarReservas.path}"
    requests_mock.add(
        responses.POST,
        url,
        json=mocker_vexado.MockCancelarReservas.response_pedido_nao_encontrado(),
        status=412,
    )
    requests_mock.add(responses.POST, url, status=200)
    yield requests_mock


@pytest.fixture
def mock_cancelar_no_json_error_504(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.CancelarReservas.path}"
    requests_mock.add(responses.POST, url, mocker_vexado.MockErrors.response_no_json(), status=504)
    yield requests_mock


@pytest.fixture
def mock_cancelar_no_json_error_502(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.CancelarReservas.path}"
    requests_mock.add(responses.POST, url, mocker_vexado.MockErrors.response_no_json(), status=502)
    yield requests_mock


@pytest.fixture
def mock_cancelar_unauthorized(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.CancelarReservas.path}"
    requests_mock.add(responses.POST, url, json=mocker_vexado.MockErrors.response_unauthorized(), status=401)
    yield requests_mock


@pytest.fixture
def mock_bloquear_poltrona(requests_mock, mock_login, vexado_api):
    url_bloquear_poltrona = f"{vexado_api.base_url}/{endpoints.BloquearPoltrona.path}"
    requests_mock.add(
        responses.POST,
        url_bloquear_poltrona,
        json=mocker_vexado.MockBloquearPoltrona.response(),
        status=200,
    )


@pytest.fixture
def mock_bloquear_poltrona_ja_bloqueada_segunda_tentativa(requests_mock, mock_login, vexado_api):
    url_bloquear_poltrona = f"{vexado_api.base_url}/{endpoints.BloquearPoltrona.path}"
    requests_mock.add(
        responses.POST,
        url_bloquear_poltrona,
        json=mocker_vexado.MockBloquearPoltrona.response(),
        status=200,
    )
    requests_mock.add(
        responses.POST,
        url_bloquear_poltrona,
        json=mocker_vexado.MockBloquearPoltrona.response_error(),
        status=412,
    )


@pytest.fixture
def mock_bloquear_poltrona_ja_bloqueada(requests_mock, mock_login, vexado_api):
    url_bloquear_poltrona = f"{vexado_api.base_url}/{endpoints.BloquearPoltrona.path}"
    requests_mock.add(
        responses.POST,
        url_bloquear_poltrona,
        json=mocker_vexado.MockBloquearPoltrona.response_error(),
        status=412,
    )


@pytest.fixture
def mock_desbloquear_poltrona(requests_mock, mock_login, vexado_api):
    url_desbloquear_poltrona = f"{vexado_api.base_url}/{endpoints.DesbloquearPoltrona.path}"
    requests_mock.add(responses.POST, url_desbloquear_poltrona, status=200)


@pytest.fixture
def mock_comprar(requests_mock, mock_login, vexado_api):
    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )
    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response(),
        status=200,
    )


@pytest.fixture
def mock_comprar_unica_passagem(requests_mock, mock_login, vexado_api):
    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )
    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response_unica_passagem(),
        status=200,
    )


@pytest.fixture
def mock_comprar_conexao_mesma_poltrona(requests_mock, mock_login, vexado_api):
    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )
    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response_conexao_mesma_poltrona(),
        status=200,
    )


@pytest.fixture
def mock_comprar_error(requests_mock, mock_login, vexado_api):
    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error(),
        status=500,
    )


@pytest.fixture
def mock_comprar_no_json_error_504(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(responses.POST, url, mocker_vexado.MockErrors.response_no_json(), status=504)
    yield requests_mock


@pytest.fixture
def mock_recuperar_itinerario(vexado_api, mock_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        f"{vexado_api.base_url}/{endpoints.BuscarItinerarioCorrida.path}".format(itinerario=23123, id_empresa=5),
        json=mocker_vexado.MockRecuperarItinerario.response(),
    )


@pytest.fixture
def mock_recuperar_itinerario_com_locais_repetidos(vexado_api, mock_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        f"{vexado_api.base_url}/{endpoints.BuscarItinerarioCorrida.path}".format(itinerario=23123, id_empresa=5),
        json=mocker_vexado.MockRecuperarItinerario.response_com_locais_repetidos(),
    )


@pytest.fixture
def mock_recuperar_itinerario_nao_encontrado(vexado_api, mock_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        f"{vexado_api.base_url}/{endpoints.BuscarItinerarioCorrida.path}".format(itinerario=23123, id_empresa=5),
        json=mocker_vexado.MockRecuperarItinerario.response_error(),
        status=500,
    )


@pytest.fixture
def mock_buscar_cidades_por_nome(requests_mock, vexado_api):
    url_buscar_cidades_por_nome = f"{vexado_api.base_url}/{endpoints.GetCidades.path}".format(
        nome_cidade="S%C3%A3o%20Jos%C3%A9%20dos%20Campos"
    )
    requests_mock.add(
        responses.GET,
        url_buscar_cidades_por_nome,
        json=mocker_vexado.MockGetCidades.response(),
    )


@pytest.fixture
def mock_buscar_cidades_por_empresa(requests_mock, vexado_api, mock_login):
    url_buscar_cidades_por_empresa = f"{vexado_api.base_url}/{endpoints.CidadesEmpresa.path}".format(
        company_external_id=str(vexado_api.company.company_external_id)
    )
    requests_mock.add(
        responses.GET,
        url_buscar_cidades_por_empresa,
        json=mocker_vexado.MockCidadesEmpresas.response(),
    )


@pytest.fixture
def mock_buscar_cidades_por_nome_vazio(requests_mock, vexado_api):
    url_buscar_cidades_por_nome = f"{vexado_api.base_url}/{endpoints.GetCidades.path}".format(
        nome_cidade="S%C3%A3o%20Jo%C3%A3o%20do%20Biriti"
    )
    requests_mock.add(responses.GET, url_buscar_cidades_por_nome, json=[])


@pytest.fixture
def mock_comprar_multiple(requests_mock, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(responses.GET, url, json=mocker_vexado.GetPoltronasLivres.response())
    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )
    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response_multiple_two_passengers(),
        status=200,
    )
    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response_multiple_one_passenger(),
        status=200,
    )


@pytest.fixture
def mock_recuperar_pedido(request, requests_mock, vexado_api, mock_login):
    pedido_id, empresa_id = request.param
    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=pedido_id, empresa_id=empresa_id
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response(),
        status=200,
    )


@pytest.fixture
def mock_recuperar_pedidos_cancelados(request, requests_mock, vexado_api, mock_login):
    pedidos = request.param
    response_recuperar_pedido = mocker_vexado.MockRecuperarPedido.response()
    for reserva in response_recuperar_pedido["reservas"]:
        reserva["situacaoReserva"] = "CANCELADO"
    for pedido_id, empresa_id in pedidos:
        url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
            id_pedido=pedido_id, empresa_id=empresa_id
        )
        requests_mock.add(
            responses.GET,
            url_recuperar_pedido,
            json=response_recuperar_pedido,
            status=200,
        )
    yield requests_mock


@pytest.fixture
def mock_comprar_multiple_valor_errado_segunda_compra(requests_mock, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(responses.GET, url, json=mocker_vexado.GetPoltronasLivres.response())

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )

    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response_multiple_two_passengers(),
        status=200,
    )

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error_valor_reserva_errado(),
        status=412,
    )

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )

    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response_multiple_one_passenger(),
        status=200,
    )


@pytest.fixture
def mock_comprar_sem_preco(requests_mock, mock_login, vexado_api):
    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error_preco_nao_cadastrado(),
        status=412,
    )

    url_cadastrar_preco = f"{vexado_api.base_url}/{endpoints.CadastrarPreco.path}"
    requests_mock.add(responses.POST, url_cadastrar_preco)

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )

    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response(),
        status=200,
    )


@pytest.fixture
def mock_comprar_poltrona_ja_reservada(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(responses.GET, url, json=mocker_vexado.GetPoltronasLivres.response())

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error_poltrona_ja_reservada(),
        status=412,
    )

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )

    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido().response_outras_poltronas(),
        status=200,
    )


@pytest.fixture
def mock_comprar_erro_selecionar_poltronas(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(responses.GET, url, json=mocker_vexado.GetPoltronasLivres.response())

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error_poltrona_ja_reservada(),
        status=412,
    )
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error_poltrona_ja_reservada(),
        status=412,
    )
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error_poltrona_ja_reservada(),
        status=412,
    )


@pytest.fixture
def mock_comprar_erro_selecionar_poltronas_insuficientes(requests_mock, mock_login, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints.RetornaPoltronas.path}".format(origem=1, destino=2932, itinerario="21136")
    requests_mock.add(
        responses.GET,
        url,
        json=mocker_vexado.GetPoltronasLivres.response_poltronas_insuficientes(),
    )

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error_poltrona_ja_reservada(),
        status=412,
    )


@pytest.fixture
def mock_comprar_preco_errado(requests_mock, mock_login, vexado_api):
    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response_error_valor_reserva_errado(),
        status=412,
    )

    url_buscar_precos = f"{vexado_api.base_url}/{endpoints.BuscarPrecos.path}".format(company_external_id=5)
    params = {"origem": "1", "destino": "2932", "tipoPreco": "LEITO"}
    requests_mock.add(
        responses.GET,
        url_buscar_precos,
        match=[query_param_matcher(params)],
        json=mocker_vexado.MockGetPrecos.response(),
    )

    url_alterar_preco = f"{vexado_api.base_url}/{endpoints.AlterarPreco.path}"
    requests_mock.add(responses.POST, url_alterar_preco)

    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )

    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response(),
        status=200,
    )


@pytest.fixture
def mock_comprar_com_erro_recuperar_pedido(requests_mock, mock_login, vexado_api):
    url_efetuar_reserva = f"{vexado_api.base_url}/{endpoints.EfetuarReserva.path}"
    requests_mock.add(
        responses.POST,
        url_efetuar_reserva,
        json=mocker_vexado.MockEfetuarReserva.response(),
        status=200,
    )
    url_recuperar_pedido = f"{vexado_api.base_url}/{endpoints.RecuperarPedido.path}".format(
        id_pedido=116896, empresa_id=5
    )
    requests_mock.add(
        responses.GET,
        url_recuperar_pedido,
        json=mocker_vexado.MockRecuperarPedido.response_empresa_bloqueada(),
        status=423,
    )


@pytest.fixture
def mock_cadastrar_grupo_um_andar(requests_mock, mock_login, vexado_api):
    url_cadastrar_grupo = f"{vexado_api.base_url}/{endpoints.CadastrarGrupo.path}"
    requests_mock.add(
        responses.POST,
        url_cadastrar_grupo,
        json=mocker_vexado.MockCadastrarGrupo.response(qtd_andares=1),
    )
    yield requests_mock


@pytest.fixture
def mock_cadastrar_grupo_dois_andares(requests_mock, mock_login, vexado_api):
    url_cadastrar_grupo = f"{vexado_api.base_url}/{endpoints.CadastrarGrupo.path}"
    requests_mock.add(
        responses.POST,
        url_cadastrar_grupo,
        json=mocker_vexado.MockCadastrarGrupo.response(qtd_andares=2),
    )
    yield requests_mock


@pytest.fixture
def mock_cadastrar_preco(requests_mock, mock_login, vexado_api):
    url_cadastrar_preco = f"{vexado_api.base_url}/{endpoints.CadastrarPreco.path}"
    requests_mock.add(responses.POST, url_cadastrar_preco)


@pytest.fixture
def mock_cadastrar_preco_ja_cadastrado(requests_mock, mock_login, vexado_api):
    url_cadastrar_preco = f"{vexado_api.base_url}/{endpoints.CadastrarPreco.path}"
    requests_mock.add(
        responses.POST,
        url_cadastrar_preco,
        json=mocker_vexado.MockCadastrarPreco.response_preco_ja_existe(),
        status=412,
    )

    url_buscar_precos = f"{vexado_api.base_url}/{endpoints.BuscarPrecos.path}".format(company_external_id=5)
    params = {"origem": "1", "destino": "2", "tipoPreco": "executivo", "size": 1000}
    requests_mock.add(
        responses.GET,
        url_buscar_precos,
        match=[query_param_matcher(params)],
        json=mocker_vexado.MockGetPrecos.response(),
    )

    url_alterar_preco = f"{vexado_api.base_url}/{endpoints.AlterarPreco.path}"
    requests_mock.add(responses.POST, url_alterar_preco)


@pytest.fixture
def mock_buscar_precos(request, requests_mock, mock_login, vexado_api):
    url_buscar_precos = f"{vexado_api.base_url}/{endpoints.BuscarPrecos.path}".format(
        company_external_id=vexado_api.company.company_external_id
    )
    requests_mock.add(
        responses.GET,
        url_buscar_precos,
        json=mocker_vexado.MockGetPrecos.response(),
    )


@pytest.fixture
def mock_buscar_precos_simplificado(request, requests_mock, mock_login, vexado_api):
    url_buscar_precos = f"{vexado_api.base_url}/{endpoints.BuscarPrecosEmpresaSimplificado.path}".format(
        company_external_id=vexado_api.company.company_external_id
    )
    requests_mock.add(
        responses.GET,
        url_buscar_precos,
        json=mocker_vexado.MockGetPrecosSimplificado.response(),
    )


@pytest.fixture
def mock_buscar_precos_inexistentes(request, requests_mock, mock_login, vexado_api):
    (company_external_id, origem, destino, classe) = request.param
    url_buscar_precos = f"{vexado_api.base_url}/{endpoints.BuscarPrecos.path}".format(
        company_external_id=company_external_id
    )
    params = {"origem": f"{origem}", "destino": f"{destino}", "tipoPreco": f"{classe}"}
    requests_mock.add(
        responses.GET,
        url_buscar_precos,
        match=[query_param_matcher(params)],
        json=mocker_vexado.MockGetPrecos.response_no_price(),
    )


@pytest.fixture
def mock_atualizar_preco(requests_mock, mock_login, vexado_api):
    url_alterar_preco = f"{vexado_api.base_url}/{endpoints.AlterarPreco.path}"
    requests_mock.add(responses.POST, url_alterar_preco)


@pytest.fixture
def mock_cadastrar_rota(requests_mock, mock_login, vexado_api):
    url_cadastrar_rota = f"{vexado_api.base_url}/{endpoints.CadastrarRota.path}"
    requests_mock.add(responses.POST, url_cadastrar_rota)


@pytest.fixture
def mock_inativar_itinerarios_bulk(requests_mock, mock_login, vexado_api):
    url_inativar_itinerarios = f"{vexado_api.base_url}/{endpoints.InativarItinerariosBulk.path}"
    requests_mock.add(responses.POST, url_inativar_itinerarios, json={})


@pytest.fixture
def mock_listar_mapas_veiculos(requests_mock, mock_login, vexado_api):
    url_mapas_veiculos = f"{vexado_api.base_url}/{endpoints.MapasVeiculos.path}?size=300"
    requests_mock.add(responses.GET, url_mapas_veiculos, json=mocker_vexado.MockMapasVeiculos.response())


@pytest.fixture
def mock_listar_veiculos(requests_mock, mock_login, vexado_api):
    endpoint = endpoints.ListarVeiculos.path.format(company_external_id=vexado_api.company.company_external_id)
    url_listar_veiculos = f"{vexado_api.base_url}/{endpoint}?size=1000"
    requests_mock.add(responses.GET, url_listar_veiculos, json=mocker_vexado.MockListarVeiculos.response())


@pytest.fixture
def mock_listar_veiculos_sem_veiculos(requests_mock, mock_login, vexado_api):
    endpoint = endpoints.ListarVeiculos.path.format(company_external_id=vexado_api.company.company_external_id)
    url_listar_veiculos = f"{vexado_api.base_url}/{endpoint}?size=1000"
    requests_mock.add(
        responses.GET,
        url_listar_veiculos,
        json=mocker_vexado.MockListarVeiculos.response_sem_veiculos(),
    )


@pytest.fixture
def mock_create_veiculos_api(requests_mock, mock_login, vexado_api):
    endpoint = endpoints.CadastrarVeiculo.path
    url_cadastrar_veiculo = f"{vexado_api.base_url}/{endpoint}"
    requests_mock.add(responses.POST, url_cadastrar_veiculo)

    endpoint = endpoints.ListarVeiculos.path.format(company_external_id=vexado_api.company.company_external_id)
    url_listar_veiculos = f"{vexado_api.base_url}/{endpoint}?size=1000"
    requests_mock.add(responses.GET, url_listar_veiculos, json=mocker_vexado.MockListarVeiculos.response())


@pytest.fixture
def mock_alterar_veiculo(requests_mock, mock_login, vexado_api):
    url_alterar_veiculo = f"{vexado_api.base_url}/{endpoints.AlterarVeiculo.path}"
    requests_mock.add(responses.POST, url_alterar_veiculo)


@pytest.fixture
def mock_buscar_rotas(requests_mock, mock_login, vexado_api):
    url_buscar_rotas = f"{vexado_api.base_url}/{endpoints.BuscarRotas.path}".format(
        company_external_id=vexado_api.company.company_external_id
    )
    requests_mock.add(responses.GET, url_buscar_rotas, json=mocker_vexado.MockBuscarRotas.response())


@pytest.fixture
def mock_buscar_rotas_vazio(requests_mock, mock_login, vexado_api):
    url_buscar_rotas = f"{vexado_api.base_url}/{endpoints.BuscarRotas.path}".format(
        company_external_id=vexado_api.company.company_external_id
    )
    requests_mock.add(responses.GET, url_buscar_rotas, json={"rotas": None})


@pytest.fixture
def mock_escalar_veiculo_error(requests_mock, mock_login, vexado_api):
    url_alterar_veiculo = f"{vexado_api.base_url}/{endpoints.AlterarVeiculo.path}"
    requests_mock.add(
        responses.POST,
        url_alterar_veiculo,
        json=mocker_vexado.MockErrors.response_erro_interno(),
        status=500,
    )


@pytest.fixture
def mock_escalar_veiculo(requests_mock, mock_login, vexado_api):
    url_alterar_veiculo = f"{vexado_api.base_url}/{endpoints.AlterarVeiculo.path}"
    requests_mock.add(responses.POST, url_alterar_veiculo)


@pytest.fixture
def mock_listar_viagens_rota(requests_mock, mock_login, vexado_api):
    url_listar_viagens = f"{vexado_api.base_url}/{endpoints.ListarViagensRota.path}".format(
        company_external_id=r"\w+", id_rota=r"\w+"
    )
    requests_mock.add(
        responses.GET, re.compile(url_listar_viagens), json=mocker_vexado.MockListarViagensRota.response()
    )


@pytest.fixture
def mock_listar_viagens_rota_trechos_bloqueados(requests_mock, mock_login, vexado_api):
    url_listar_viagens = f"{vexado_api.base_url}/{endpoints.ListarViagensRota.path}".format(
        company_external_id=r"\w+", id_rota=r"\w+"
    )
    response = mocker_vexado.MockListarViagensRota.response()
    response["itinerarios"][0]["trechosBloqueados"][0]["trechoOrigem"]["chave"] = "47"
    response["itinerarios"][0]["trechosBloqueados"][0]["trechoDestino"]["chave"] = "5848"
    requests_mock.add(responses.GET, re.compile(url_listar_viagens), json=response)


@pytest.fixture
def mock_listar_viagens_rota_viagens_inativos(requests_mock, mock_login, vexado_api):
    url_listar_viagens = f"{vexado_api.base_url}/{endpoints.ListarViagensRota.path}".format(
        company_external_id=r"\w+", id_rota=r"\w+"
    )
    response = mocker_vexado.MockListarViagensRota.response()
    response["itinerarios"][0]["ativo"] = False
    response["itinerarios"][1]["ativo"] = False
    requests_mock.add(responses.GET, re.compile(url_listar_viagens), json=response)


@pytest.fixture
def mock_cadastrar_checkpoint(requests_mock, mock_login, vexado_api):
    url_criar_trecho = f"{vexado_api.base_url}/{endpoints.CriarTrecho.path}"
    requests_mock.add(responses.POST, url_criar_trecho)


@pytest.fixture
def mock_mover_posicao_checkpoint_para_baixo(requests_mock, mock_login, vexado_api):
    trecho_id = 94230
    endpoint = endpoints.AlterarPosicao.path.format(
        empresa_id=vexado_api.company.company_external_id,
        trecho_id=trecho_id,
        ordem="BAIXO",
    )
    url_criar_trecho = f"{vexado_api.base_url}/{endpoint}"
    requests_mock.add(responses.POST, url_criar_trecho, json=True)
    yield SimpleNamespace(trecho_id=trecho_id)


@pytest.fixture
def mock_listar_trechos(requests_mock, mock_login, vexado_api):
    url_criar_trecho = f"{vexado_api.base_url}/{endpoints.ListarTrechos.path}".format(
        id_empresa=vexado_api.company.company_external_id, id_rota=7799
    )
    requests_mock.add(responses.GET, url_criar_trecho, json=mocker_vexado.MockListarTrechos.response())


@pytest.fixture
def mock_lista_reservas_viagem(requests_mock, mock_login, vexado_api):
    url_criar_trecho = f"{vexado_api.base_url}/{endpoints.ListarReservasViagemIds.path}".format(
        company_external_id=vexado_api.company.company_external_id, itinerario=21136
    )
    requests_mock.add(
        responses.GET,
        url_criar_trecho,
        json=mocker_vexado.MockListarReservasViagem.response_ids(),
    )


@pytest.fixture
def mock_lista_reservas_viagem_detalhadas(requests_mock, mock_login, vexado_api):
    url_criar_trecho = f"{vexado_api.base_url}/{endpoints.ListarReservasViagem.path}".format(
        company_external_id=vexado_api.company.company_external_id, itinerario=21136
    )
    requests_mock.add(responses.GET, url_criar_trecho, json=mocker_vexado.MockListarReservasViagem.response())


@pytest.fixture
def mock_lista_reservas_viagem_nenhuma_reserva(requests_mock, mock_login, vexado_api):
    url_criar_trecho = f"{vexado_api.base_url}/{endpoints.ListarReservasViagemIds.path}".format(
        company_external_id=vexado_api.company.company_external_id, itinerario=21136
    )
    requests_mock.add(
        responses.GET,
        url_criar_trecho,
        json=mocker_vexado.MockListarReservasViagem.response_nenhuma_reserva(),
    )


@pytest.fixture
def mock_lista_reservas_viagem_detalhadas_nenhuma_reserva(requests_mock, mock_login, vexado_api):
    url_criar_trecho = f"{vexado_api.base_url}/{endpoints.ListarReservasViagem.path}".format(
        company_external_id=vexado_api.company.company_external_id, itinerario=21136
    )
    requests_mock.add(
        responses.GET,
        url_criar_trecho,
        json=mocker_vexado.MockListarReservasViagem.response_nenhuma_reserva(),
    )


@pytest.fixture
def mock_lista_reservas_viagem_itinerario_vazio(requests_mock, mock_login, vexado_api):
    url_criar_trecho = f"{vexado_api.base_url}/{endpoints.ListarReservasViagemIds.path}".format(
        company_external_id=vexado_api.company.company_external_id, itinerario=21136
    )
    requests_mock.add(
        responses.GET,
        url_criar_trecho,
        json=mocker_vexado.MockListarReservasViagem.response_itinerario_vazio(),
        status=412,
    )


@pytest.fixture
def mock_lista_reservas_viagem_erro(requests_mock, mock_login, vexado_api):
    url_criar_trecho = f"{vexado_api.base_url}/{endpoints.ListarReservasViagemIds.path}".format(
        company_external_id=vexado_api.company.company_external_id, itinerario=21136
    )
    requests_mock.add(
        responses.GET,
        url_criar_trecho,
        json=mocker_vexado.MockErrors.response_erro_interno(),
        status=500,
    )


@pytest.fixture
def mock_inativar_itinerario(requests_mock, mock_login, vexado_api):
    itinerario_id = 21136
    url_inativar_itinerario = f"{vexado_api.base_url}/{endpoints.InativarItinerario.path}".format(
        company_external_id=vexado_api.company.company_external_id, itinerario=itinerario_id
    )
    requests_mock.add(responses.POST, url_inativar_itinerario, status=200)
    yield SimpleNamespace(itinerario_id=itinerario_id)

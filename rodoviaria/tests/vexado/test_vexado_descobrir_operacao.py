import json
from datetime import datetime, timedelta

import pytest
import responses
import time_machine
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import midnight, to_tz
from rodoviaria.api.vexado import descobrir_operacao
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.api.vexado.endpoints import BuscarPrecosEmpresaSimplificado
from rodoviaria.api.vexado.models import RotaDetalhada, Viagem
from rodoviaria.models.core import (
    LocalEmbarque,
    Rota,
    Rotina,
    RotinaTrechoVendido,
    TaskStatus,
    TipoAssento,
    TrechoVendido,
)
from rodoviaria.tests.vexado import mocker as mocker_vexado


def test_descobrir_operacao_cria_rota(mocker, vexado_api: VexadoAPI, mock_buscar_rotas):
    assert Rota.objects.filter(company=vexado_api.company).count() == 0
    mocker.patch("rodoviaria.api.vexado.descobrir_operacao._buscar_e_salvar_rotas_dos_servicos")
    descobrir_operacao.descobrir_operacao(vexado_api.login)
    rota = Rota.objects.get(company=vexado_api.company)
    assert rota.id_hash == "475848f9fc1bfbe3355deba02d4c31e38740fc"
    assert len(rota.get_itinerario()) == 6


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_descobrir_operacao_atualiza_rota(mocker, vexado_api: VexadoAPI, mock_buscar_rotas):
    rota = baker.make(Rota, company=vexado_api.company, ativo=False, id_hash="475848f9fc1bfbe3355deba02d4c31e38740fc")
    mocker.patch("rodoviaria.api.vexado.descobrir_operacao._buscar_e_salvar_rotas_dos_servicos")
    descobrir_operacao.descobrir_operacao(vexado_api.login)
    rota.refresh_from_db()
    assert rota.ativo is True
    assert rota.updated_at == timezone.now()


def test_buscar_e_salvar_rotas_dos_servicos(mocker, vexado_api: VexadoAPI, mock_login):
    mock_chain = mocker.patch("rodoviaria.api.vexado.descobrir_operacao.chain")
    response = descobrir_operacao._buscar_e_salvar_rotas_dos_servicos(
        vexado_api.login, {"475848f9fc1bfbe3355deba02d4c31e38740fc": [123]}, 7, 2
    )
    assert response == mock_chain.return_value.on_error.return_value
    mock_chain.return_value.on_error.return_value.assert_called_once()
    assert (
        mock_chain.call_args[0][0].tasks[0].task
        == "rodoviaria.api.vexado.descobrir_operacao._buscar_rotinas_e_trechos_vendidos"
    )
    assert (
        mock_chain.call_args[0][1].task == "rodoviaria.api.vexado.descobrir_operacao.finisher_descobrir_operacao_vexado"
    )


def test_buscar_e_salvar_rotas_dos_servicos_return_task_object(mocker, vexado_api: VexadoAPI, mock_login):
    mock_chain = mocker.patch("rodoviaria.api.vexado.descobrir_operacao.chain")
    response = descobrir_operacao._buscar_e_salvar_rotas_dos_servicos(
        vexado_api.login, {"475848f9fc1bfbe3355deba02d4c31e38740fc": [123]}, 7, 2, return_task_object=True
    )
    assert response == mock_chain.return_value.on_error.return_value
    mock_chain.return_value.assert_not_called()
    assert (
        mock_chain.call_args[0][0].tasks[0].task
        == "rodoviaria.api.vexado.descobrir_operacao._buscar_rotinas_e_trechos_vendidos"
    )
    assert (
        mock_chain.call_args[0][1].task == "rodoviaria.api.vexado.descobrir_operacao.finisher_descobrir_operacao_vexado"
    )


@pytest.fixture
def mocked_rota(vexado_api):
    rota_api = RotaDetalhada.parse_obj(mocker_vexado.MockBuscarRotas.response()["rotas"][0])
    rota = baker.make(
        Rota,
        company=vexado_api.company,
        id_hash=rota_api.itinerario.hash,
        id_external=rota_api.external_id,
        provider_data=json.dumps(rota_api.cleaned),
    )
    return rota


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_buscar_rotinas_e_trechos_vendidos_cria_rotina_e_trecho_vendido(
    mocked_rota, vexado_api: VexadoAPI, mock_buscar_precos_simplificado, mock_listar_viagens_rota
):
    data_viagem_api = mocker_vexado.MockListarViagensRota.response()["itinerarios"][0]["dataHoraPartida"]
    data_viagem_api = to_tz(datetime.strptime(data_viagem_api, "%d/%m/%Y %H:%M"), "America/Sao_Paulo")
    data_inicial = midnight(data_viagem_api) - timedelta(days=1)
    data_final = midnight(data_viagem_api) + timedelta(days=1)
    origem_trecho_vendido = baker.make(
        LocalEmbarque,
        id_external=mocker_vexado.MockGetPrecos.response()["precos"][0]["cidadeOrigem"]["id"],
        cidade__company=vexado_api.company,
    )
    destino_trecho_vendido = baker.make(
        LocalEmbarque,
        id_external=mocker_vexado.MockGetPrecos.response()["precos"][0]["cidadeDestino"]["id"],
        cidade__company=vexado_api.company,
    )
    descobrir_operacao._buscar_rotinas_e_trechos_vendidos(
        vexado_api.company.id, data_inicial.isoformat(), data_final.isoformat(), mocked_rota.id_hash, [123912]
    )
    rotina = Rotina.objects.get(rota=mocked_rota)
    assert rotina.ativo is True
    assert rotina.datetime_ida == data_viagem_api
    trechos_vendidos = TrechoVendido.objects.filter(rota=mocked_rota).order_by("classe")
    classes_esperadas = [("LEITO", 4), ("LEITO_INDIVIDUAL", 2)]
    for index, trecho_vendido in enumerate(trechos_vendidos):
        assert trecho_vendido.ativo is True
        assert trecho_vendido.origem == origem_trecho_vendido
        assert trecho_vendido.destino == destino_trecho_vendido
        assert trecho_vendido.classe == classes_esperadas[index][0]
        assert trecho_vendido.capacidade_classe == classes_esperadas[index][1]
        assert trecho_vendido.tipo_assento.tipo_assento_parceiro == classes_esperadas[index][0]
        assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=trecho_vendido)


def test_buscar_rotinas_e_trechos_vendidos_atualiza_rotina_e_trecho_vendido(
    mocked_rota, vexado_api: VexadoAPI, mock_buscar_precos_simplificado, mock_listar_viagens_rota
):
    data_viagem_api = mocker_vexado.MockListarViagensRota.response()["itinerarios"][0]["dataHoraPartida"]
    data_viagem_api = to_tz(datetime.strptime(data_viagem_api, "%d/%m/%Y %H:%M"), "America/Sao_Paulo")
    data_inicial = midnight(data_viagem_api) - timedelta(days=1)
    data_final = midnight(data_viagem_api) + timedelta(days=1)
    tipo_assento = baker.make(TipoAssento, tipo_assento_parceiro="LEITO", company=vexado_api.company)
    origem_trecho_vendido = baker.make(
        LocalEmbarque,
        id_external=mocker_vexado.MockGetPrecos.response()["precos"][0]["cidadeOrigem"]["id"],
        cidade__company=vexado_api.company,
    )
    destino_trecho_vendido = baker.make(
        LocalEmbarque,
        id_external=mocker_vexado.MockGetPrecos.response()["precos"][0]["cidadeDestino"]["id"],
        cidade__company=vexado_api.company,
    )
    trecho_vendido = baker.make(
        TrechoVendido,
        origem=origem_trecho_vendido,
        destino=destino_trecho_vendido,
        rota=mocked_rota,
        ativo=False,
        capacidade_classe=4,
        classe="LEITO",
    )
    rotina = baker.make(Rotina, rota=mocked_rota, datetime_ida=data_viagem_api, ativo=False)
    descobrir_operacao._buscar_rotinas_e_trechos_vendidos(
        vexado_api.company.id, data_inicial.isoformat(), data_final.isoformat(), mocked_rota.id_hash, [123912]
    )
    rotina.refresh_from_db()
    assert rotina.ativo is True
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is True
    assert trecho_vendido.tipo_assento == tipo_assento
    assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=trecho_vendido)


def test_buscar_rotinas_e_trechos_vendidos_inativa_rotina_e_trecho_vendido(
    mocked_rota, vexado_api: VexadoAPI, mock_buscar_precos_simplificado, mock_listar_viagens_rota
):
    data_viagem_api = mocker_vexado.MockListarViagensRota.response()["itinerarios"][0]["dataHoraPartida"]
    data_viagem_api = to_tz(datetime.strptime(data_viagem_api, "%d/%m/%Y %H:%M"), "America/Sao_Paulo")
    data_inicial = midnight(data_viagem_api) - timedelta(days=1)
    data_final = midnight(data_viagem_api) + timedelta(days=1)
    origem_trecho_vendido = baker.make(
        LocalEmbarque,
        id_external=mocker_vexado.MockGetPrecos.response()["precos"][0]["cidadeOrigem"]["id"],
        cidade__company=vexado_api.company,
    )
    destino_trecho_vendido = baker.make(
        LocalEmbarque,
        id_external=mocker_vexado.MockGetPrecos.response()["precos"][0]["cidadeDestino"]["id"],
        cidade__company=vexado_api.company,
    )
    trecho_vendido = baker.make(
        TrechoVendido,
        origem=origem_trecho_vendido,
        destino=destino_trecho_vendido,
        rota=mocked_rota,
        ativo=True,
        capacidade_classe=4,
        classe="Executivo",
    )  # classe errada
    rotina = baker.make(
        Rotina, rota=mocked_rota, datetime_ida=data_viagem_api + timedelta(hours=3), ativo=False
    )  # horario errado
    descobrir_operacao._buscar_rotinas_e_trechos_vendidos(
        vexado_api.company.id, data_inicial.isoformat(), data_final.isoformat(), mocked_rota.id_hash, [123912]
    )
    rotina.refresh_from_db()
    assert rotina.ativo is False
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is False


def test_buscar_rotinas_e_trechos_vendidos_trecho_vendido_bloqueado(
    mocked_rota, vexado_api: VexadoAPI, mock_buscar_precos_simplificado, mock_listar_viagens_rota_trechos_bloqueados
):
    data_viagem_api = mocker_vexado.MockListarViagensRota.response()["itinerarios"][0]["dataHoraPartida"]
    data_viagem_api = to_tz(datetime.strptime(data_viagem_api, "%d/%m/%Y %H:%M"), "America/Sao_Paulo")
    data_inicial = midnight(data_viagem_api) - timedelta(days=1)
    data_final = midnight(data_viagem_api) + timedelta(days=1)
    baker.make(
        LocalEmbarque,
        id_external=mocker_vexado.MockGetPrecos.response()["precos"][0]["cidadeOrigem"]["id"],
        cidade__company=vexado_api.company,
    )
    baker.make(
        LocalEmbarque,
        id_external=mocker_vexado.MockGetPrecos.response()["precos"][0]["cidadeDestino"]["id"],
        cidade__company=vexado_api.company,
    )
    descobrir_operacao._buscar_rotinas_e_trechos_vendidos(
        vexado_api.company.id, data_inicial.isoformat(), data_final.isoformat(), mocked_rota.id_hash, [123912]
    )
    rotina = Rotina.objects.get(rota=mocked_rota)
    assert rotina.ativo is True
    assert not TrechoVendido.objects.filter(rota=mocked_rota).exists()


def test_buscar_viagens_da_rota_com_viagem_inativa(vexado_api, mock_listar_viagens_rota_viagens_inativos):
    data_viagem_api = mocker_vexado.MockListarViagensRota.response()["itinerarios"][0]["dataHoraPartida"]
    data_viagem_api = to_tz(datetime.strptime(data_viagem_api, "%d/%m/%Y %H:%M"), "America/Sao_Paulo")
    data_inicial = midnight(data_viagem_api) - timedelta(days=1)
    data_final = midnight(data_viagem_api) + timedelta(days=1)
    viagens = descobrir_operacao._buscar_viagens_da_rota(
        vexado_api.login,
        [81231],
        data_inicial,
        data_final,
    )
    assert viagens == []


def test_calcula_classes_viagem():
    viagem = Viagem.parse_obj(mocker_vexado.MockListarViagensRota.response()["itinerarios"][0])
    viagem.andar = 1
    assert descobrir_operacao._calcula_classes_viagem(viagem) == [("LEITO", 4), ("LEITO_INDIVIDUAL", 2)]


def test_calcula_classes_viagem_sem_classe_mista():
    viagem = Viagem.parse_obj(mocker_vexado.MockListarViagensRota.response()["itinerarios"][0])
    viagem.poltronas = []
    viagem.andar = 1
    assert descobrir_operacao._calcula_classes_viagem(viagem) == [("LEITO", 4)]
    viagem.andar = 2
    assert descobrir_operacao._calcula_classes_viagem(viagem) == [("LEITO", 3)]


def test_finisher_descobrir_rotas_vexado():
    rota_sem_rotina = baker.make(Rota)
    descobrir_operacao.finisher_descobrir_operacao_vexado(rota_sem_rotina.company_id)
    rota_sem_rotina.refresh_from_db()
    assert rota_sem_rotina.ativo is False
    assert (
        TaskStatus.objects.get(company=rota_sem_rotina.company, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO).status
        == TaskStatus.Status.SUCCESS
    )


def test_finisher_descobrir_rotas_vexado_on_error():
    rota_sem_rotina = baker.make(Rota)
    descobrir_operacao.finisher_descobrir_operacao_vexado_on_error(None, None, None, rota_sem_rotina.company_id)
    rota_sem_rotina.refresh_from_db()
    assert rota_sem_rotina.ativo is False
    assert (
        TaskStatus.objects.get(company=rota_sem_rotina.company, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO).status
        == TaskStatus.Status.FAILURE
    )


def test_get_precos_cadastrados_varias_paginas(vexado_api, mock_login, requests_mock):
    url_buscar_precos = f"{vexado_api.base_url}/{BuscarPrecosEmpresaSimplificado.path}".format(
        company_external_id=vexado_api.company.company_external_id
    )
    response = mocker_vexado.MockGetPrecosSimplificado.response()
    response["totalPagina"] = 4
    len_uma_pagina = len(response["trechosItinerarioEmpresas"])
    requests_mock.add(
        responses.GET,
        url_buscar_precos,
        json=response,
    )

    precos_cadastrados = descobrir_operacao._get_precos_cadastrados(vexado_api.login)

    assert len(precos_cadastrados) == 4 * len_uma_pagina  # bateu 4 vezes e concatenou as respostas

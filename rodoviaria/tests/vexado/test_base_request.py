import pytest
import responses
from django.db import connections
from model_bakery import baker

from rodoviaria.api.vexado import endpoints
from rodoviaria.api.vexado.auth import VexadoAuth
from rodoviaria.service.exceptions import RodoviariaUnauthorizedError
from rodoviaria.tests.vexado import mocker as mocker_vexado


def test_auth_grava_header_request_from_company(mocker, mock_vexado_login, django_assert_num_queries):
    company = baker.make("rodoviaria.Company", url_base="http://vexado.com.br")
    login = baker.make(
        "rodoviaria.VexadoLogin", company=company, password="buser", user="vexado", site="AdminServer.com"
    )
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        auth = VexadoAuth.from_company(company)

    request = mocker.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["site"] == login.site
    assert request.headers["Authorization"] == f"Bearer {mocker_vexado.TOKEN_1}"


def test_auth_grava_header_request_from_client(mocker, mock_vexado_login, django_assert_num_queries):
    """Auth a partir do client não executa query no bd."""
    company = baker.make("rodoviaria.Company", url_base="http://vexado.com.br")
    client_login = baker.make(
        "rodoviaria.VexadoLogin", company=company, password="buser", user="vexado", site="AdminServer.com"
    )
    with django_assert_num_queries(0, connection=connections["rodoviaria"]):
        auth = VexadoAuth.from_client(client_login)
    request = mocker.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["site"] == client_login.site
    assert request.headers["Authorization"] == f"Bearer {mocker_vexado.TOKEN_1}"


def test_auth_get_token_when_needed(vexado_api, mock_vexado_login, mock_recuperar_itinerario):
    # Dada uma chamada para um endpoint que precisa de autenticação via token
    external_id = "23123"  # valor vem do conftest mock_recuperar_itinerario
    vexado_api.itinerario(external_id)

    # Espero que seja realizado uma chamada para o endpoint de signin
    assert mock_vexado_login.call_count == 1
    # e depois a requisição autenticada é executada
    assert mock_recuperar_itinerario.call_count == 1

    # e fazendo uma nova chamada
    vexado_api.itinerario(external_id)
    assert mock_recuperar_itinerario.call_count == 2

    # o token é recuperado da memória e não do endpoint
    assert mock_vexado_login.call_count == 1


@responses.activate
def test_base_request_renova_autenticacao_expirada(vexado_api, mock_login_twice):
    # dada uma chamada com sucesso, adquire um token
    external_id = "23123"
    with responses.RequestsMock() as rsps:
        rsps.add(
            responses.GET,
            f"{vexado_api.base_url}/{endpoints.BuscarItinerarioCorrida.path}".format(itinerario=23123, id_empresa=5),
            json=mocker_vexado.MockRecuperarItinerario.response(),
        )
        rsps.add(
            responses.POST,
            f"{vexado_api.base_url}/{endpoints.GetToken.path}",
            json=mocker_vexado.MockLogin.response(mocker_vexado.TOKEN_1),
        )
        req1 = vexado_api.itinerario(external_id)
    token1 = req1.request.auth._token
    assert token1 == {"auth": mocker_vexado.TOKEN_1, "new_login": False}

    # Quando o token expirar, fazer nova chamada na api pra adquirir token novo
    with responses.RequestsMock() as rsps:
        rsps.add(
            responses.GET,
            f"{vexado_api.base_url}/{endpoints.BuscarItinerarioCorrida.path}".format(itinerario=23123, id_empresa=5),
            json={"erro": "Token expirado"},
            status=401,
        )
        rsps.add(
            responses.POST,
            f"{vexado_api.base_url}/{endpoints.GetToken.path}",
            json=mocker_vexado.MockLogin.response(mocker_vexado.TOKEN_2),
        )
        with pytest.raises(RodoviariaUnauthorizedError):
            vexado_api.itinerario(external_id)

        # entao, novo token é registrado e armazenado em memória.
        auth = VexadoAuth.from_client(vexado_api.login)
        auth.get_token()
        assert auth._token["auth"] == mocker_vexado.TOKEN_2

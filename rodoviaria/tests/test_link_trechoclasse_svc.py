from datetime import timedelta

import pytest

from commons.dateutils import today_midnight
from rodoviaria.models.core import Company
from rodoviaria.service import link_trechoclasse_svc

TRECHOCLASSE_DATETIME_IDA = today_midnight() + timedelta(days=2)
TAG = "to_be_updated"


@pytest.fixture
def tagged_trechoclasse(mocker, buser_grupos, totalbus_trechoclasses):
    mocker.patch("rodoviaria.service.link_trechoclasse_svc._is_valid", return_value=True)
    grupo_classe = totalbus_trechoclasses.ida.grupo_classe
    grupo_classe.tipo_assento_internal = "leito cama"
    grupo_classe.save()

    internal_trechoclasse = buser_grupos.ida.trechoclasse
    internal_trechoclasse.datetime_ida = TRECHOCLASSE_DATETIME_IDA
    internal_trechoclasse.save()

    rodoviaria_grupo = totalbus_trechoclasses.ida.grupo
    rodoviaria_grupo.grupo_internal_id = buser_grupos.ida.grupo.id
    rodoviaria_grupo.save()

    rodoviaria_trechoclasse = totalbus_trechoclasses.ida
    rodoviaria_trechoclasse.datetime_ida = TRECHOCLASSE_DATETIME_IDA
    rodoviaria_trechoclasse.trechoclasse_internal_id = internal_trechoclasse.id
    rodoviaria_trechoclasse.save()
    rodoviaria_trechoclasse.tags.add(TAG)
    return rodoviaria_trechoclasse


@pytest.mark.parametrize("mock_buscar_servico_totalbus", [TRECHOCLASSE_DATETIME_IDA], indirect=True)
def test_update_trechoclasse_by_tag(
    tagged_trechoclasse,
    mock_totalbus_login,
    totalbus_login,
    mock_buscar_servico_totalbus,
):
    updated_trechoclasse = _get_updated_trechoclasse(tag=TAG)
    assert TAG in updated_trechoclasse["tags"]
    assert updated_trechoclasse["preco_rodoviaria"] != tagged_trechoclasse.preco_rodoviaria


@pytest.mark.parametrize("mock_buscar_servico_totalbus", [TRECHOCLASSE_DATETIME_IDA], indirect=True)
def test_update_trechoclasse_by_integracao(
    tagged_trechoclasse,
    mock_totalbus_login,
    totalbus_login,
    mock_buscar_servico_totalbus,
):
    updated_trechoclasse = _get_updated_trechoclasse(integracao="totalbus")
    assert updated_trechoclasse["integracao"] == "totalbus"
    assert updated_trechoclasse["preco_rodoviaria"] != tagged_trechoclasse.preco_rodoviaria


@pytest.mark.parametrize("mock_buscar_servico_totalbus", [TRECHOCLASSE_DATETIME_IDA], indirect=True)
def test_update_trechoclasse_by_company_id(
    tagged_trechoclasse,
    mock_totalbus_login,
    totalbus_login,
    mock_buscar_servico_totalbus,
):
    company_internal_id = tagged_trechoclasse.grupo.company_integracao.company_internal_id
    modelo_venda = tagged_trechoclasse.grupo.company_integracao.modelo_venda
    updated_trechoclasse = _get_updated_trechoclasse(company_id=company_internal_id, modelo_venda=modelo_venda)
    assert updated_trechoclasse["company_internal_id"] == company_internal_id
    assert updated_trechoclasse["preco_rodoviaria"] != tagged_trechoclasse.preco_rodoviaria


@pytest.mark.parametrize("mock_buscar_servico_totalbus", [TRECHOCLASSE_DATETIME_IDA], indirect=True)
def test_update_trechoclasse_by_grupo_id(
    tagged_trechoclasse,
    mock_totalbus_login,
    totalbus_login,
    mock_buscar_servico_totalbus,
):
    grupo_internal_id = tagged_trechoclasse.grupo.grupo_internal_id
    updated_trechoclasse = _get_updated_trechoclasse(grupo_id=grupo_internal_id)
    assert updated_trechoclasse["grupo_internal_id"] == grupo_internal_id
    assert updated_trechoclasse["preco_rodoviaria"] != tagged_trechoclasse.preco_rodoviaria


@pytest.mark.parametrize("mock_buscar_servico_totalbus", [TRECHOCLASSE_DATETIME_IDA], indirect=True)
def test_update_trechoclasse_by_id(
    tagged_trechoclasse,
    mock_totalbus_login,
    totalbus_login,
    mock_buscar_servico_totalbus,
):
    trechoclasse_internal_id = tagged_trechoclasse.trechoclasse_internal_id
    updated_trechoclasse = _get_updated_trechoclasse(trechoclasse_ids=[trechoclasse_internal_id])
    assert updated_trechoclasse["trechoclasse_internal_id"] == trechoclasse_internal_id
    assert updated_trechoclasse["preco_rodoviaria"] != tagged_trechoclasse.preco_rodoviaria


def _get_updated_trechoclasse(
    tag=None,
    integracao=None,
    company_id=None,
    modelo_venda=None,
    grupo_id=None,
    trechoclasse_ids=None,
):
    result = link_trechoclasse_svc.atualiza(
        tag=tag,
        integracao=integracao,
        company_id=company_id,
        modelo_venda=modelo_venda,
        grupo_id=grupo_id,
        trechoclasse_ids=trechoclasse_ids,
    )
    successes = result["successes"]
    updated_trechoclasse = successes[0]
    return updated_trechoclasse


def test_remove_tag(tagged_trechoclasse):
    tag = "to_be_updated"
    trechoclasse_ids = [tagged_trechoclasse.trechoclasse_internal_id]
    result = link_trechoclasse_svc.remove_tag(tag)
    assert result["mensagem"] == f"A tag {tag} foi removida dos trechos_classe {trechoclasse_ids}"


def test_add_tag(tagged_trechoclasse):
    tagged_trechoclasse.tags.remove(TAG)
    tag = "my_tag"
    trechoclasse_ids = [tagged_trechoclasse.trechoclasse_internal_id]
    result = link_trechoclasse_svc.add_tag(tag, trechoclasse_ids=trechoclasse_ids)
    assert result["mensagem"] == f"A tag {tag} foi adicionada aos trechos_classe {trechoclasse_ids}"


def test_get(tagged_trechoclasse):
    tag = "to_be_updated"
    result = link_trechoclasse_svc.get(tag)
    assert result["trechos"][0]["id"] == tagged_trechoclasse.id


def test_get_com_modelo_venda(totalbus_trechoclasses):
    result = link_trechoclasse_svc.get(
        company_id=totalbus_trechoclasses.ida.grupo.company_integracao.company_internal_id,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    assert len(result["trechos"]) == 2
    result = link_trechoclasse_svc.get(
        company_id=totalbus_trechoclasses.ida.grupo.company_integracao.company_internal_id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    assert result["trechos"] == []


def test_get_trechoclasse_inativo(tagged_trechoclasse):
    tagged_trechoclasse.active = False
    tagged_trechoclasse.save()
    tag = "to_be_updated"
    result = link_trechoclasse_svc.get(tag)
    assert result["trechos"] == []

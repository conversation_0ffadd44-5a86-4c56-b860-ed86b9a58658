from datetime import datetime, timed<PERSON>ta
from datetime import timezone as tzone
from decimal import Decimal as D
from unittest import mock

import time_machine
from django.core.management import call_command
from django.db import connections
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz
from rodoviaria.models.core import Passagem
from rodoviaria.service import cancela_passagens_pendentes_svc
from rodoviaria.views import passagens_sem_travel_correspondente


def test_cancela_passagens_pendentes_com_localizador(
    praxio_trechoclasses, praxio_login, mock_praxio_cancelar, praxio_company, mocker
):
    trechoclasse = praxio_trechoclasses.ida
    travel_ids = [0, 0, 1, 2]
    for i in range(1, 5):
        p = baker.make(
            "rodoviaria.Passagem",
            status=Passagem.Status.CONFIRMADA,
            travel_internal_id=travel_ids[i - 1],
            trechoclasse_integracao=trechoclasse,
            poltrona_external_id=i,
            buseiro_internal_id=i,
            localizador="30120-123@12",
            company_integracao=praxio_company,
            preco_rodoviaria=D("100"),
        )
        p.tags.add("cancelamento_pendente")
    # passagens_sem_cancelamento_pendente
    status_passagens = [
        Passagem.Status.ERRO,
        Passagem.Status.CANCELADA,
        Passagem.Status.CONFIRMADA,
        Passagem.Status.CONFIRMADA,
    ]
    ids = [1, 2, 3, 3]
    for i in range(4):
        baker.make(
            "rodoviaria.Passagem",
            status=status_passagens[i],
            travel_internal_id=travel_ids[ids[i]],
            trechoclasse_integracao=trechoclasse,
        )
    cancela_passagens_pendentes_svc.cancela_passagens_pendentes()
    assert Passagem.objects.filter(tags__name="cancelamento_pendente").count() == 0


def _make_passagem(travel_id, trecho_classe, company, created_at=None):
    passagem = baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=travel_id,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=trecho_classe,
        buseiro_internal_id=12,
        localizador="30120-123@12",
        poltrona_external_id=5,
        company_integracao=company,
        preco_rodoviaria=D("100"),
    )
    if created_at:
        passagem.created_at = created_at
        passagem.save()
    return passagem


def test_cancela_passagens_pendentes(praxio_trechoclasses, praxio_login, mock_praxio_cancelar, praxio_company):
    trechoclasse = praxio_trechoclasses.ida
    travel_ids = [0, 0, 1, 2]
    for i in range(1, 5):
        p = baker.make(
            "rodoviaria.Passagem",
            status=Passagem.Status.CONFIRMADA,
            travel_internal_id=travel_ids[i - 1],
            trechoclasse_integracao=trechoclasse,
            poltrona_external_id=i,
            buseiro_internal_id=i,
            numero_passagem="30120",
            serie_bpe="12",
            company_integracao=praxio_company,
            preco_rodoviaria=D("100"),
        )
        p.tags.add("cancelamento_pendente")
    # passagens_sem_cancelamento_pendente
    status_passagens = [
        Passagem.Status.ERRO,
        Passagem.Status.CANCELADA,
        Passagem.Status.CONFIRMADA,
        Passagem.Status.CONFIRMADA,
    ]
    ids = [1, 2, 3, 3]
    for i in range(4):
        baker.make(
            "rodoviaria.Passagem",
            status=status_passagens[i],
            travel_internal_id=travel_ids[ids[i]],
            trechoclasse_integracao=trechoclasse,
        )
    cancela_passagens_pendentes_svc.cancela_passagens_pendentes()
    assert Passagem.objects.filter(tags__name="cancelamento_pendente").count() == 0


def test_cancela_passagens_pendentes_passagem_ja_impressa(
    totalbus_trechoclasses,
    totalbus_login,
    totalbus_company,
    mock_totalbus_cancela_venda_considerado_embarcado,
):
    trechoclasse = totalbus_trechoclasses.ida
    p = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        travel_internal_id=7289,
        trechoclasse_integracao=trechoclasse,
        poltrona_external_id=7,
        buseiro_internal_id=8412,
        pedido_external_id="asdfasd",
        numero_passagem="30120",
        company_integracao=totalbus_company,
    )
    p.tags.add("cancelamento_pendente")
    cancela_passagens_pendentes_svc.cancela_passagens_pendentes()
    p.refresh_from_db()
    assert p.tags_set() == {"cancelamento_pendente", "passagem_impressa"}
    assert p.status == Passagem.Status.CONFIRMADA


def test_cancela_passagens_pendentes_passagem_poltrona_trocada(
    totalbus_trechoclasses,
    totalbus_login,
    totalbus_company,
    mock_totalbus_cancela_venda_erro_poltrona_trocada,
):
    trechoclasse = totalbus_trechoclasses.ida
    p = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        travel_internal_id=7289,
        trechoclasse_integracao=trechoclasse,
        poltrona_external_id=7,
        buseiro_internal_id=8412,
        pedido_external_id="asdfasd",
        numero_passagem="010000930269",
        company_integracao=totalbus_company,
    )
    p.tags.add("cancelamento_pendente")
    cancela_passagens_pendentes_svc.cancela_passagens_pendentes()
    p.refresh_from_db()
    assert p.tags_set() == {"cancelamento_pendente", "cancelamento_nao_permitido"}
    assert p.status == Passagem.Status.CONFIRMADA


def test_cancela_passagens_pendentes_nem_tenta_se_passagem_foi_impressa(
    totalbus_trechoclasses, totalbus_login, totalbus_company
):
    trechoclasse = totalbus_trechoclasses.ida
    p = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        travel_internal_id=7289,
        trechoclasse_integracao=trechoclasse,
        poltrona_external_id=7,
        buseiro_internal_id=8412,
        pedido_external_id="asdfasd",
        numero_passagem="30120",
        company_integracao=totalbus_company,
    )
    p.tags.add("cancelamento_pendente")
    p.tags.add("passagem_impressa")
    with mock.patch("rodoviaria.service.cancela_passagens_pendentes_svc.cancelar_task") as mock_cancelar_task:
        cancela_passagens_pendentes_svc.cancela_passagens_pendentes()
    p.refresh_from_db()
    assert p.tags_set() == {"cancelamento_pendente", "passagem_impressa"}
    assert p.status == Passagem.Status.CONFIRMADA
    mock_cancelar_task.assert_not_called()


def test_cancela_passagens_pendentes_nem_tenta_se_cancelamento_nao_permitido(
    totalbus_trechoclasses, totalbus_login, totalbus_company
):
    trechoclasse = totalbus_trechoclasses.ida
    p = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        travel_internal_id=7289,
        trechoclasse_integracao=trechoclasse,
        poltrona_external_id=7,
        buseiro_internal_id=8412,
        pedido_external_id="asdfasd",
        numero_passagem="30120",
        company_integracao=totalbus_company,
    )
    p.tags.add("cancelamento_pendente")
    p.tags.add("cancelamento_nao_permitido")
    with mock.patch("rodoviaria.service.cancela_passagens_pendentes_svc.cancelar_task") as mock_cancelar_task:
        cancela_passagens_pendentes_svc.cancela_passagens_pendentes()
    p.refresh_from_db()
    assert p.tags_set() == {"cancelamento_pendente", "cancelamento_nao_permitido"}
    assert p.status == Passagem.Status.CONFIRMADA
    mock_cancelar_task.assert_not_called()


def test_cancela_passagens_pendentes_com_erro(
    praxio_trechoclasses, praxio_login, mock_praxio_login, caplog, praxio_company
):
    trechoclasse = praxio_trechoclasses.ida
    travel_id = 10
    p = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        travel_internal_id=travel_id,
        trechoclasse_integracao=trechoclasse,
        poltrona_external_id=2,
        buseiro_internal_id=4,
        localizador="30120-123@12",
        company_integracao=praxio_company,
    )
    p.tags.add("cancelamento_pendente")
    cancela_passagens_pendentes_svc.cancela_passagens_pendentes()
    assert Passagem.objects.filter(tags__name="cancelamento_pendente").count() == 1
    assert Passagem.objects.filter(tags__name="cancelada_login_inativo").count() == 0


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_passagens_sem_travel_correspondente():
    company_buser = baker.make("core.Company")
    company_rodov = baker.make("rodoviaria.Company", company_internal_id=company_buser.id, features=["active"])
    datetime_now = timezone.now()
    datetime_ida = datetime_now + timedelta(days=2)
    grupo_buser = baker.make(
        "core.Grupo",
        company=company_buser,
        datetime_ida=datetime_ida,
    )
    trecho_classe_buser = baker.make("core.TrechoClasse", datetime_ida=datetime_ida)
    travel = baker.make("core.Travel", grupo=grupo_buser, trecho_classe=trecho_classe_buser)
    grupo_rodov = baker.make("rodoviaria.Grupo", datetime_ida=datetime_ida)
    trecho_classe_rodov = baker.make("rodoviaria.TrechoClasse", grupo=grupo_rodov, datetime_ida=datetime_ida)
    _make_passagem(
        travel.id,
        trecho_classe_rodov,
        company_rodov,
        created_at=datetime_now - timedelta(minutes=20),
    )  # passagem correta
    passagem_pendente = _make_passagem(
        travel.id * 1111,
        trecho_classe_rodov,
        company_rodov,
        created_at=datetime_now - timedelta(minutes=20),
    )
    _make_passagem(
        travel.id * 1111,
        trecho_classe_rodov,
        company_rodov,
        created_at=datetime_now - timedelta(minutes=10),
    )  # passagem recente
    response = cancela_passagens_pendentes_svc.passagens_sem_travel_correspondente()
    assert list(response) == [passagem_pendente]


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancelar_passagens_com_travel_correspondentes(praxio_trechoclasses):
    datetime_ida = timezone.now() + timedelta(days=2)
    praxio_trechoclasses.ida.datetime_ida = datetime_ida
    praxio_trechoclasses.ida.save()
    company_buser = baker.make("core.Company")
    company_rodov = baker.make("rodoviaria.Company", company_internal_id=company_buser.id, features=["active"])
    grupo_buser = baker.make(
        "core.Grupo",
        company=company_buser,
        datetime_ida=datetime_ida,
    )
    trecho_classe = baker.make("core.TrechoClasse", datetime_ida=datetime_ida)
    travel = baker.make("core.Travel", grupo=grupo_buser, trecho_classe=trecho_classe)
    now_menos_20_min = timezone.now() - timedelta(minutes=20)
    _make_passagem(travel.id, praxio_trechoclasses.ida, company_rodov, now_menos_20_min)  # passagem correta
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    assert response == {"exceptions": {}, "sucessos": {}}


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancelar_passagens_recentes_sem_travel_correspondentes(praxio_trechoclasses, praxio_company):
    praxio_trechoclasses.ida.datetime_ida = timezone.now() + timedelta(days=2)
    praxio_trechoclasses.ida.save()
    passagem_criada_menos_de_15_minutos = _make_passagem(
        24123,
        praxio_trechoclasses.ida,
        praxio_company,
        created_at=timezone.now() - timedelta(minutes=10),
    )
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    passagem_criada_menos_de_15_minutos.refresh_from_db()
    assert passagem_criada_menos_de_15_minutos.status == Passagem.Status.CONFIRMADA
    assert response == {"exceptions": {}, "sucessos": {}}


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancelar_passagens_sem_travel_correspondentes(
    praxio_trechoclasses, praxio_login, mock_praxio_cancelar, praxio_company
):
    praxio_trechoclasses.ida.datetime_ida = timezone.now() + timedelta(days=2)
    praxio_trechoclasses.ida.save()
    travels_ids = [23123, 12312]
    now_menos_20_min = timezone.now() - timedelta(minutes=20)
    passagens_erradas = [
        _make_passagem(
            travels_ids[i],
            praxio_trechoclasses.ida,
            praxio_company,
            created_at=now_menos_20_min,
        )
        for i in range(2)
    ]
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    passagens_erradas[0].refresh_from_db()
    assert passagens_erradas[0].status == Passagem.Status.CANCELADA
    passagens_erradas[1].refresh_from_db()
    assert passagens_erradas[1].status == Passagem.Status.CANCELADA
    assert response["exceptions"] == {}
    assert response["sucessos"]["travel:23123 - buseiro:12"]["Mensagem"] == "Passagem devolvida com sucesso."


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancelar_passagens_com_travel_correspondentes_horario_buser_django_atras(praxio_trechoclasses, praxio_company):
    company_buser = baker.make("core.Company")
    datetime_now = timezone.now()
    datetime_ida_django = datetime_now - timedelta(hours=1)
    datetime_ida_rodovi = datetime_now + timedelta(hours=4)
    baker.make("rodoviaria.Company", company_internal_id=company_buser.id, features=["active"])
    grupo = baker.make(
        "core.Grupo",
        company=company_buser,
        datetime_ida=datetime_ida_django,
    )
    trecho_classe = baker.make("core.TrechoClasse", datetime_ida=datetime_ida_django)
    travel = baker.make("core.Travel", grupo=grupo, trecho_classe=trecho_classe)
    praxio_trechoclasses.ida.datetime_ida = datetime_ida_rodovi
    praxio_trechoclasses.ida.save()
    passagem_certa = _make_passagem(travel.id, praxio_trechoclasses.ida, praxio_company)
    now_menos_20_min = datetime_now - timedelta(minutes=20)
    passagem_certa.created_at = now_menos_20_min
    passagem_certa.save()
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    passagem_certa.refresh_from_db()
    assert passagem_certa.status == Passagem.Status.CONFIRMADA
    assert response["exceptions"] == {}
    assert response["sucessos"] == {}


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancelar_passagens_com_travel_correspondentes_horario_buser_django_na_frente(
    praxio_trechoclasses, praxio_company
):
    company_buser = baker.make("core.Company")
    datetime_now = timezone.now()
    datetime_ida_django = datetime_now + timedelta(hours=6)
    datetime_ida_rodovi = datetime_now + timedelta(hours=4)
    baker.make("rodoviaria.Company", company_internal_id=company_buser.id, features=["active"])
    grupo = baker.make(
        "core.Grupo",
        company=company_buser,
        datetime_ida=datetime_ida_django,
    )
    trecho_classe = baker.make("core.TrechoClasse", datetime_ida=datetime_ida_django)
    travel = baker.make("core.Travel", grupo=grupo, trecho_classe=trecho_classe)
    praxio_trechoclasses.ida.datetime_ida = datetime_ida_rodovi
    praxio_trechoclasses.ida.save()
    passagem_certa = _make_passagem(travel.id, praxio_trechoclasses.ida, praxio_company)
    now_menos_20_min = datetime_now - timedelta(minutes=20)
    passagem_certa.created_at = now_menos_20_min
    passagem_certa.save()
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    passagem_certa.refresh_from_db()
    assert passagem_certa.status == Passagem.Status.CONFIRMADA
    assert response["exceptions"] == {}
    assert response["sucessos"] == {}


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancelar_passagens_sem_travel_correspondentes_horario_buser_django_atras(
    praxio_trechoclasses, praxio_company, praxio_login, mock_praxio_cancelar
):
    company_buser = baker.make("core.Company")
    datetime_now = timezone.now()
    datetime_ida_django = datetime_now - timedelta(hours=1)
    datetime_ida_rodovi = datetime_now + timedelta(hours=4)
    baker.make(
        "core.Grupo",
        company=company_buser,
        datetime_ida=datetime_ida_django,
    )
    baker.make("core.TrechoClasse", datetime_ida=datetime_ida_django)
    praxio_trechoclasses.ida.datetime_ida = datetime_ida_rodovi
    praxio_trechoclasses.ida.save()
    passagem_pendente = _make_passagem(89423, praxio_trechoclasses.ida, praxio_company)
    now_menos_20_min = datetime_now - timedelta(minutes=20)
    passagem_pendente.created_at = now_menos_20_min
    passagem_pendente.save()
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    passagem_pendente.refresh_from_db()
    assert passagem_pendente.status == Passagem.Status.CANCELADA
    assert response["exceptions"] == {}
    assert len(response["sucessos"]) == 1


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancelar_passagens_sem_travel_correspondentes_horario_buser_django_na_frente(
    praxio_trechoclasses, praxio_company, praxio_login, mock_praxio_cancelar
):
    company_buser = baker.make("core.Company")
    datetime_now = timezone.now()
    datetime_ida_django = datetime_now + timedelta(hours=6)
    datetime_ida_rodovi = datetime_now + timedelta(hours=4)
    baker.make("rodoviaria.Company", company_internal_id=company_buser.id, features=["active"])
    baker.make(
        "core.Grupo",
        company=company_buser,
        datetime_ida=datetime_ida_django,
    )
    baker.make("core.TrechoClasse", datetime_ida=datetime_ida_django)
    praxio_trechoclasses.ida.datetime_ida = datetime_ida_rodovi
    praxio_trechoclasses.ida.save()
    passagem_pendente = _make_passagem(83923, praxio_trechoclasses.ida, praxio_company)
    now_menos_20_min = datetime_now - timedelta(minutes=20)
    passagem_pendente.created_at = now_menos_20_min
    passagem_pendente.save()
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    passagem_pendente.refresh_from_db()
    assert passagem_pendente.status == Passagem.Status.CANCELADA
    assert response["exceptions"] == {}
    assert len(response["sucessos"]) == 1


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancelar_passagens_com_menos_de_3h(praxio_trechoclasses, praxio_company):
    company_buser = baker.make("core.Company")
    datetime_now = timezone.now()
    datetime_ida_django = datetime_now + timedelta(hours=6)
    datetime_ida_rodovi = datetime_now + timedelta(hours=2)
    baker.make("rodoviaria.Company", company_internal_id=company_buser.id, features=["active"])
    baker.make(
        "core.Grupo",
        company=company_buser,
        datetime_ida=datetime_ida_django,
    )
    baker.make("core.TrechoClasse", datetime_ida=datetime_ida_django)
    praxio_trechoclasses.ida.datetime_ida = datetime_ida_rodovi
    praxio_trechoclasses.ida.save()
    passagem_pendente = _make_passagem(83923, praxio_trechoclasses.ida, praxio_company)
    now_menos_20_min = datetime_now - timedelta(minutes=20)
    passagem_pendente.created_at = now_menos_20_min
    passagem_pendente.save()
    response = cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente()
    passagem_pendente.refresh_from_db()
    assert passagem_pendente.status == Passagem.Status.CONFIRMADA
    assert response["exceptions"] == {}
    assert response["sucessos"] == {}


def test_cancela_passagens_sem_travel_grupo_no_horario_do_cron():
    datetime_ida_grupo = to_default_tz(datetime(2021, 12, 25, 16, 30))
    company_buser = baker.make("core.Company")
    baker.make("rodoviaria.Company", company_internal_id=company_buser.id, features=["active"])
    grupo = baker.make("core.Grupo", datetime_ida=datetime_ida_grupo, company=company_buser)
    travel = baker.make("core.Travel", grupo=grupo)
    trecho_classe_rodov = baker.make(
        "rodoviaria.TrechoClasse",
        grupo=baker.make("rodoviaria.Grupo", datetime_ida=datetime_ida_grupo),
    )
    passagem = baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=travel.id,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=trecho_classe_rodov,
    )
    passagem.created_at = to_default_tz(datetime(2021, 12, 20, 16, 30))
    passagem.save()
    with mock.patch("rodoviaria.service.cancela_passagens_pendentes_svc.timezone.now") as mock_now:
        mock_now.return_value = datetime_ida_grupo
        passagens = cancela_passagens_pendentes_svc.passagens_sem_travel_correspondente()
    assert len(passagens) == 0


def test_command():
    with mock.patch("rodoviaria.service.cancela_passagens_pendentes_svc.cancela_passagens_pendentes") as mock_svc:
        call_command("cancela_passagens_pendentes")
    mock_svc.assert_called_once()


def test_num_serializer(rf, django_assert_num_queries, vexado_company):
    with time_machine.travel(datetime(1999, 1, 5, 16, 44, 59)):
        baker.make(
            Passagem,
            _fill_optional=[
                "travel_internal_id",
                "company_integracao",
            ],
            trechoclasse_integracao__grupo__company_integracao=vexado_company,
            trechoclasse_integracao__datetime_ida=datetime(1999, 5, 6, 17, tzinfo=tzone.utc),
            trechoclasse_integracao__origem__cidade__name="João Pessoa",
            status=Passagem.Status.CONFIRMADA,
            _quantity=20,
        )

    with time_machine.travel(datetime(1999, 1, 5, 17)):
        with django_assert_num_queries(2, connection=connections["rodoviaria"]), django_assert_num_queries(
            1, connection=connections["default"]
        ):
            request = rf.get("/v1/passagens_sem_travel_correspondente")
            passagens_sem_travel_correspondente(request)

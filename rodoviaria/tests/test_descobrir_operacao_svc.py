import json
from unittest import mock

import pytest
from django.core.management import call_command
from model_bakery import baker

from rodoviaria import views
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, TaskStatus
from rodoviaria.service import descobrir_operacao_svc
from rodoviaria.service.exceptions import HibridoNotAllowedException
from rodoviaria.service.map_marketplace_cidades_svc import MapMarketplaceCidadesSVC


def test_descobrir_operacao_proximos_dias(totalbus_login):
    with (
        mock.patch.object(OrchestrateRodoviaria, "descobrir_operacao_async") as mock_descobrir_operacao_async,
        mock.patch.object(MapMarketplaceCidadesSVC, "execute") as mock_map_cidades,
    ):
        mock_descobrir_operacao_async.return_value.id = "123"
        task = descobrir_operacao_svc.descobrir_operacao_proximos_dias(
            totalbus_login.company, next_days=14, shift_days=3
        )
    assert task["task_id"] == "123"
    assert task["mensagem"] == f"Descobre operacao da empresa {totalbus_login.company.name} nos próximos 14 dias"
    assert (
        TaskStatus.objects.get(
            company_id=totalbus_login.company_id, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO
        ).status
        == TaskStatus.Status.PENDING
    )
    mock_map_cidades.assert_called_once()


def test_descobrir_operacao_proximos_dias_map_cidades_not_implemented(totalbus_login):
    with (
        mock.patch.object(OrchestrateRodoviaria, "descobrir_operacao_async") as mock_descobrir_operacao_async,
        mock.patch.object(MapMarketplaceCidadesSVC, "execute", side_effect=NotImplementedError) as mock_map_cidades,
    ):
        mock_descobrir_operacao_async.return_value.id = "123"
        task = descobrir_operacao_svc.descobrir_operacao_proximos_dias(
            totalbus_login.company, next_days=14, shift_days=3
        )
    assert task["task_id"] == "123"
    assert task["mensagem"] == f"Descobre operacao da empresa {totalbus_login.company.name} nos próximos 14 dias"
    assert (
        TaskStatus.objects.get(
            company_id=totalbus_login.company_id, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO
        ).status
        == TaskStatus.Status.PENDING
    )
    mock_map_cidades.assert_called_once()


def test_descobrir_operacao_proximos_dias_failure(totalbus_login, mocker):
    mocker.patch.object(MapMarketplaceCidadesSVC, "execute", side_effect=NotImplementedError)
    with mock.patch.object(
        OrchestrateRodoviaria, "descobrir_operacao_async"
    ) as mock_descobrir_operacao_async, pytest.raises(Exception, match="Algum erro"):
        mock_descobrir_operacao_async.side_effect = Exception("Algum erro")
        descobrir_operacao_svc.descobrir_operacao_proximos_dias(totalbus_login.company, next_days=14, shift_days=3)
    assert (
        TaskStatus.objects.get(
            company_id=totalbus_login.company_id, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO
        ).status
        == TaskStatus.Status.NOT_STARTED
    )


def test_descobrir_operacao_proximos_dias_return_task(totalbus_login, mocker):
    mocker.patch.object(MapMarketplaceCidadesSVC, "execute", side_effect=NotImplementedError)
    with mock.patch.object(OrchestrateRodoviaria, "descobrir_operacao_async") as mock_descobrir_operacao_async:
        task = descobrir_operacao_svc.descobrir_operacao_proximos_dias(
            totalbus_login.company,
            next_days=14,
            shift_days=3,
            return_task_object=True,
        )
    assert task == mock_descobrir_operacao_async.return_value
    assert (
        TaskStatus.objects.get(
            company_id=totalbus_login.company_id, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO
        ).status
        == TaskStatus.Status.PENDING
    )


def test_view_descobrir_operacao_proximos_dias(rf):
    company = baker.make(Company, company_internal_id=231)
    request = rf.get("/v1/descobrir_operacao_proximos_dias")
    with mock.patch(
        "rodoviaria.service.descobrir_operacao_svc.descobrir_operacao_proximos_dias"
    ) as mock_descobrir_operacao:
        mock_descobrir_operacao.return_value = {"mensagem": "Descobrimento da operação iniciado"}
        response = views.descobrir_operacao_proximos_dias(request, 231, 14, 1, "marketplace")
    assert response.status_code == 200
    assert json.loads(response.content) == {"mensagem": "Descobrimento da operação iniciado"}
    mock_descobrir_operacao.assert_called_once_with(company=company, next_days=14, shift_days=1)


def test_command_descobrir_operacao(rf):
    company = baker.make(Company, company_internal_id=231, margem_dias_busca_operacao=140)
    with mock.patch(
        "rodoviaria.service.descobrir_operacao_svc.descobrir_operacao_proximos_dias", autospec=True
    ) as mock_descobrir_operacao:
        call_command("descobrir_operacao", company_internal_id=231, modelo_venda=Company.ModeloVenda.MARKETPLACE)
    mock_descobrir_operacao.assert_called_once_with(company, next_days=140)


def test_descobrir_operacao_proximos_dias_empresa_hibrido(totalbus_login):
    totalbus_login.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_login.company.save()
    with pytest.raises(HibridoNotAllowedException):
        descobrir_operacao_svc.descobrir_operacao_proximos_dias(totalbus_login.company, next_days=14, shift_days=3)

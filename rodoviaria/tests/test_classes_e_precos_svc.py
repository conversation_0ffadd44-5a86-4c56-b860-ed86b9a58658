from datetime import timedelta
from decimal import Decimal

import pytest
from django.utils import timezone
from model_bakery import baker

from rodoviaria.models.core import LocalEmbarque, TrechoVendido
from rodoviaria.service import classes_e_precos_svc
from rodoviaria.service.class_match_svc import buser_class


@pytest.fixture
def mock_trechos_vendidos_classes_e_precos(rota_mock, rotina_mock, totalbus_company):
    rotina_mock.datetime_ida = timezone.now() + timedelta(days=2)
    rotina_mock.save()
    rota_mock.id_internal = 9999
    rota_mock.company = totalbus_company
    rota_mock.save()
    capacidade_exec_1 = 18
    capacidade_exec_2 = 14
    capacidade_leito = 15

    trechos_vendidos = [
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem_id=1,
            destino_id=2,
            preco=12.99,
            classe="LEITO CAMA",
            id_internal=10,
            capacidade_classe=capacidade_leito,
        ),
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem_id=1,
            destino_id=2,
            preco=12.99,
            classe="EXECUTIVO",
            id_internal=10,
            capacidade_classe=capacidade_exec_1,
        ),
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem_id=1,
            destino_id=2,
            preco=12.99,
            classe="EXECUTIVO",
            id_internal=14,
            capacidade_classe=capacidade_exec_1,
        ),
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem_id=1,
            destino_id=3,
            preco=12.99,
            classe="EXECUTIVO",
            id_internal=20,
            capacidade_classe=capacidade_exec_2,
        ),
    ]
    for t in trechos_vendidos:
        (
            baker.make(
                "rodoviaria.RotinaTrechoVendido",
                rotina=rotina_mock,
                trecho_vendido=t,
            ),
        )
    return trechos_vendidos


def test_get_classes_e_precos_rota(rota_mock, mock_trechos_vendidos_classes_e_precos):
    leito = mock_trechos_vendidos_classes_e_precos[0]
    exec_1 = mock_trechos_vendidos_classes_e_precos[2]
    exec_2 = mock_trechos_vendidos_classes_e_precos[3]
    resp = classes_e_precos_svc.get_classes_e_precos(rota_mock.id_internal)
    assert resp["classes"][0]["tipo_assento"] == "executivo"
    assert resp["classes"][1]["tipo_assento"] == "leito cama"
    assert resp["classes"][0]["max_capacity"] == max(exec_1.capacidade_classe, exec_2.capacidade_classe)
    assert resp["classes"][1]["max_capacity"] == leito.capacidade_classe
    assert resp["trechos_classes"][leito.id_internal]["leito cama"] == Decimal("12.99")
    assert resp["trechos_classes"][exec_1.id_internal]["executivo"] == Decimal("12.99")
    assert resp["trechos_classes"][exec_2.id_internal]["executivo"] == Decimal("12.99")


def test_get_classes_e_precos_rota_filtro_ids_trecho_vendido(rota_mock, mock_trechos_vendidos_classes_e_precos):
    exec_1 = mock_trechos_vendidos_classes_e_precos[2]
    exec_2 = mock_trechos_vendidos_classes_e_precos[3]
    resp = classes_e_precos_svc.get_classes_e_precos(rota_mock.id_internal, [exec_1.id_internal, exec_2.id_internal])
    assert len(resp["classes"]) == 1
    assert resp["classes"][0]["tipo_assento"] == "executivo"
    assert resp["classes"][0]["max_capacity"] == max(exec_1.capacidade_classe, exec_2.capacidade_classe)
    assert len(resp["trechos_classes"]) == 2
    assert resp["trechos_classes"][exec_1.id_internal]["executivo"] == Decimal("12.99")
    assert resp["trechos_classes"][exec_2.id_internal]["executivo"] == Decimal("12.99")


def testmerge_trechos_similares(rota_mock):
    # Trechos que so variam em capacidade_classe devem ser mergeados
    # Trechos iguals (classes !=, porem buser_classe igual e capacidades diferentes)
    origem = baker.make(LocalEmbarque, id_external=13)
    destino = baker.make(LocalEmbarque, id_external=17)
    c1 = 20
    c2 = 6
    baker.make(
        TrechoVendido,
        rota=rota_mock,
        origem=origem,
        destino=destino,
        classe="LEITO CAMA",
        preco=229.9,
        distancia=358,
        ativo=True,
        capacidade_classe=c1,
        id_internal=345,
    )
    baker.make(
        TrechoVendido,
        rota=rota_mock,
        origem=origem,
        destino=destino,
        classe="LEITO CAMA",
        preco=229.9,
        distancia=358,
        ativo=True,
        capacidade_classe=c2,
        id_internal=345,
    )
    baker.make(
        TrechoVendido,
        rota=rota_mock,
        origem=destino,
        destino=origem,
        classe="LEITO CAMA",
        preco=229.9,
        distancia=358,
        ativo=True,
        capacidade_classe=c2,
        id_internal=346,
    )

    trechos_qs = (
        TrechoVendido.objects.select_related("rota")
        .filter(
            rota__id_internal=rota_mock.id_internal,
            id_internal__isnull=False,
            ativo=True,
        )
        .order_by("classe")
    )

    resp = classes_e_precos_svc.merge_trechos_similares(list(trechos_qs))

    assert len(resp) == 2
    assert {
        "id_internal": 345,
        "origem_id": origem.pk,
        "destino_id": destino.pk,
        "classe": buser_class("LEITO CAMA"),
        "preco": Decimal("229.90"),
        "duracao": None,
        "distancia": Decimal("358.00"),
        "rodoviaria_rota_id": rota_mock.pk,
        "capacidade_classe": c1 + c2,
    } in resp
    assert {
        "id_internal": 346,
        "origem_id": destino.pk,
        "destino_id": origem.pk,
        "classe": buser_class("LEITO CAMA"),
        "preco": Decimal("229.90"),
        "duracao": None,
        "distancia": Decimal("358.00"),
        "rodoviaria_rota_id": rota_mock.pk,
        "capacidade_classe": c2,
    } in resp

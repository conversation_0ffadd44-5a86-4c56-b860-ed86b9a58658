from django.core.management import call_command
from model_bakery import baker


def test_command(totalbus_company):
    rota = baker.make("rodoviaria.Rota", company=totalbus_company)
    trechos = [
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi-leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semileito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito individual"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito duplo"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="CAMA"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito cama"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito_cama"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito-cama"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO_CAMA_ESPECIAL_1"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO_CAMA_ESPECIAL_2"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="DD - LEITO-CAMA G8"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="cama premium"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="cama_premium"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe=""),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="executivo"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="cama diamante"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="convencional"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO - INDIVIDUAL"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="Conv - PLUS"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="Conv. PLUS"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="Conv. - PLUS"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="Conv. - PLUS"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="CONV. PLUS"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="Conv. DOUBLE-DECKER BUS"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="CONV -PLUS"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="DD - Double Decker Bus"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="DD - DOUBLE-DECKER"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="DD- DOUBLE DECK"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="DOUBLE DECK - LEITO"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="DD - LEITO"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO TOTAL"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO TOTAL."),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO TOTAL - CONJUGADO"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO TOTAL - NON-STOP"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito com ar condicionado"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="space individual semi-leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="space semi-leito"),
    ]

    call_command("popula_tipo_assento")
    preferencial_assert = [
        "semi leito",
        "semi leito",
        "semi leito",
        "leito individual",
        "leito",
        "leito",
        "leito cama",
        "leito cama",
        "leito cama",
        "leito cama",
        "cama premium",
        "cama premium individual",
        "leito cama",
        "cama premium",
        "cama premium",
        "",
        "executivo",
        "classe_nao_mapeada",
        "convencional",
        "leito individual",
        "executivo",
        "executivo",
        "executivo",
        "executivo",
        "executivo",
        "executivo",
        "executivo",
        "executivo",
        "executivo",
        "executivo",
        "leito",
        "leito",
        "leito",
        "leito",
        "leito",
        "leito",
        "leito",
        "leito individual",
        "leito",
    ]
    for idx, t in enumerate(trechos):
        t.refresh_from_db()
        assert t.tipo_assento is not None
        if preferencial_assert[idx] == "classe_nao_mapeada":
            assert t.tipo_assento.tipo_assento_buser_preferencial is None
            continue
        assert t.tipo_assento.tipo_assento_buser_preferencial == preferencial_assert[idx]


def test_command_match(totalbus_company):
    rota = baker.make("rodoviaria.Rota", company=totalbus_company)
    trechos = [
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi-leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semileito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe=""),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="SEMI LEITO"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito individual"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito_individual"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito_especial"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito duplo"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO TOTAL"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO TOTAL."),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO TOTAL - CONJUGADO"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO TOTAL - NON-STOP"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito com ar condicionado"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="cama_premium"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="cama premium"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO_CAMA_ESPECIAL_1"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO_CAMA_ESPECIAL_2"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito_cama"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito_especial"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito cama"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito-cama"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="DD - LEITO-CAMA G8"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe=""),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe=""),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="convencional"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="executivo"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="CAMA"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito_cama_individual"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito-cama individual"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="leito cama individual"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="LEITO - INDIVIDUAL"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="DD - DOUBLE-DECKER"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="SPACE INDIVIDUAL SEMI-LEITO"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="SPACE SEMI-LEITO"),
    ]

    call_command("popula_tipo_assento")
    match_assert = [
        "semi leito",
        "semi leito",
        "semi leito",
        "semi leito",
        "semi leito",
        "leito individual",
        "leito individual",
        "leito individual",
        "leito",
        "leito",
        "leito",
        "leito",
        "leito",
        "leito",
        "leito",
        "cama premium",
        "cama premium",
        "cama premium",
        "cama premium individual",
        "leito cama",
        "leito cama",
        "leito cama",
        "leito cama",
        "leito cama",
        "cama premium",
        "convencional",
        "convencional",
        "executivo",
        "leito cama",
        "leito cama individual",
        "leito cama individual",
        "leito cama individual",
        "leito individual",
        "executivo",
        "leito individual",
        "leito",
    ]
    for idx, t in enumerate(trechos):
        t.refresh_from_db()
        assert t.tipo_assento is not None
        assert match_assert[idx] in t.tipo_assento.tipos_assentos_buser_match

    # teste se nomes de classe repetidas criam apenas 1 registro em TipoAssento
    tipo_assentos_ids_de_trechos_classes_repetidas = {t.tipo_assento_id for t in trechos if t.classe == ""}
    assert len(tipo_assentos_ids_de_trechos_classes_repetidas) == 1


def test_command_size(totalbus_company):
    rota = baker.make("rodoviaria.Rota", company=totalbus_company)
    trechos = [
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi-leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semileito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi leito"),
    ]

    call_command("popula_tipo_assento", size=1)
    match_assert = [
        "semi leito",
        "semi leito",
        "semi leito",
    ]

    for t in trechos:
        t.refresh_from_db()

    assert trechos[0].tipo_assento is not None
    assert match_assert[0] in trechos[0].tipo_assento.tipos_assentos_buser_match

    assert trechos[1].tipo_assento is None
    assert trechos[2].tipo_assento is None

    call_command("popula_tipo_assento", size=4)
    match_assert = [
        "semi leito",
        "semi leito",
        "semi leito",
    ]

    for idx, t in enumerate(trechos):
        t.refresh_from_db()
        assert t.tipo_assento is not None
        assert match_assert[idx] in t.tipo_assento.tipos_assentos_buser_match


def test_command_match_not_repeated(totalbus_company):
    rota = baker.make("rodoviaria.Rota", company=totalbus_company)
    trechos = [
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi-leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi-leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi-leito"),
    ]

    call_command("popula_tipo_assento")

    trechos[0].refresh_from_db()
    trechos[2].refresh_from_db()
    assert trechos[0].tipo_assento_id == trechos[2].tipo_assento_id
    assert len(trechos[0].tipo_assento.tipos_assentos_buser_match) == 1
    assert trechos[0].tipo_assento.tipos_assentos_buser_match == ["semi leito"]


def test_command_update_tipo_assento(totalbus_company):
    rota = baker.make("rodoviaria.Rota", company=totalbus_company)
    tipo_assento = baker.make("rodoviaria.TipoAssento", company=totalbus_company, tipo_assento_parceiro="semi leito")
    trechos = [
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi leito", tipo_assento=tipo_assento),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi leito"),
        baker.make("rodoviaria.TrechoVendido", rota=rota, classe="semi leito"),
    ]

    call_command("popula_tipo_assento")

    for t in trechos:
        t.refresh_from_db()
        assert t.tipo_assento_id == tipo_assento.id
    tipo_assento.refresh_from_db()
    assert tipo_assento.tipos_assentos_buser_match == ["semi leito"]

from unittest import mock

import pytest
from requests import Response, Session
from simple_token_bucket import NotEnoughTokens

from rodoviaria.api.executors import RequestConfig
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.executors.middlewares.token_bucket_middleware import TokenBucketMiddleware


class TestRequestConfig(RequestConfig):
    pass


@pytest.fixture
def response_ok():
    response_mock = Response()
    response_mock.status_code = 200
    return response_mock


def test_request_ok(response_ok):
    request_config = TestRequestConfig()
    with mock.patch.object(Session, "request", return_value=response_ok):
        executor = get_http_executor()
        response = request_config.invoke(executor)
    assert response.status_code == 200


def test_not_enough_tokens():
    request_config = TestRequestConfig()
    with mock.patch.object(
        TokenBucketMiddleware, "preprocess_request", side_effect=NotEnoughTokens(remaining_seconds=12)
    ), pytest.raises(
        NotEnoughTokens,
        match="NotEnoughTokens; 12 seconds until next reset",
    ):
        executor = get_http_executor()
        request_config.invoke(executor)

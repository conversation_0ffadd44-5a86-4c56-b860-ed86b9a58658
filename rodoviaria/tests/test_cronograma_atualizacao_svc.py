import json
from datetime import time

import pytest
from model_bakery import baker

from rodoviaria import views
from rodoviaria.models.core import Company, CronogramaAtualizacaoOperacao
from rodoviaria.service.cronograma_atualizacao_svc import (
    get_cronograma_atualizacao,
    get_lista_crons,
    update_cronograma_atualizacao,
)
from rodoviaria.views_schemas import (
    CompanyParams,
    CronogramaAtualizacaoPaginatorParams,
    CronogramaEmpresaParams,
    HorariosAtualizacaoParams,
    UpdateCronogramaAtualizacaoParams,
)


def test_get_cronograma_atualizacao_sem_filtro():
    params = CronogramaAtualizacaoPaginatorParams(rowsPerPage=10, page=1)
    company = baker.make(
        Company,
        name="Empresa",
        company_internal_id=2,
        features=[
            Company.Feature.BUSCAR_SERVICO,
            Company.Feature.BPE,
            Company.Feature.ITINERARIO,
        ],
    )
    horario_descobrir_rotas = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS,
        company=company,
    )
    horario_integracao_automatica = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=4,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA,
        company=company,
    )
    horarios = get_cronograma_atualizacao(params)
    assert horarios["count"] == 1
    assert horarios["num_pages"] == 1
    assert horarios["horarios"][0] == {
        "empresa": {
            "id": company.id,
            "company_internal_id": company.company_internal_id,
            "modelo_venda": company.modelo_venda,
            "integracao_id": company.integracao_id,
            "name": company.name,
        },
        "integracao_has_descobrir_operacao": False,
        CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO: {
            "dias_semana": [],
            "horario": None,
        },
        CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS: {
            "dias_semana": [1],
            "horario": horario_descobrir_rotas.horario,
        },
        CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS: {
            "dias_semana": [],
            "horario": None,
        },
        CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE: {
            "dias_semana": [],
            "horario": None,
        },
        CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA: {
            "dias_semana": [4],
            "horario": horario_integracao_automatica.horario,
        },
    }


def test_get_cronograma_atualizacao_filtros():
    company_1 = baker.make(
        Company,
        company_internal_id=2,
        name="aserpmE",
        features=[
            Company.Feature.BUSCAR_SERVICO,
            Company.Feature.BPE,
            Company.Feature.ITINERARIO,
        ],
    )
    company_2 = baker.make(
        Company,
        company_internal_id=3,
        name="Empresa",
        integracao__name="ti_sistemas",
        features=[
            Company.Feature.BUSCAR_SERVICO,
            Company.Feature.BPE,
            Company.Feature.ITINERARIO,
        ],
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company_1,
    )
    horario_2 = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company_2,
    )
    horario_integracao_automatica = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=4,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA,
        company=company_2,
    )
    params = CronogramaAtualizacaoPaginatorParams(
        rowsPerPage=10, page=1, integracao_id_filter=horario_2.company.integracao_id
    )
    horarios = get_cronograma_atualizacao(params)
    assert horarios["count"] == 1
    assert horarios["num_pages"] == 1
    assert horarios["horarios"][0] == {
        "empresa": {
            "id": horario_2.company.id,
            "company_internal_id": horario_2.company.company_internal_id,
            "modelo_venda": horario_2.company.modelo_venda,
            "integracao_id": horario_2.company.integracao_id,
            "name": horario_2.company.name,
        },
        "integracao_has_descobrir_operacao": True,
        CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO: {
            "dias_semana": [1],
            "horario": horario_2.horario,
        },
        CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS: {
            "dias_semana": [],
            "horario": None,
        },
        CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS: {
            "dias_semana": [],
            "horario": None,
        },
        CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE: {
            "dias_semana": [],
            "horario": None,
        },
        CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA: {
            "dias_semana": [4],
            "horario": horario_integracao_automatica.horario,
        },
    }
    params.search = "ABACAXI"
    horarios = get_cronograma_atualizacao(params)
    assert horarios["count"] == 0
    assert horarios["num_pages"] == 1


def test_get_cronograma_atualizacao_considera_se_integracao_tem_descobrir_operacao():
    # dadas duas empresas, uma com implementação de descobrir_operacao e outra sem
    company_1 = baker.make(
        Company,
        company_internal_id=2,
        name="Empresa",
        integracao__name="abacate",  # sem implementação descobrir_operacao
        features=[
            Company.Feature.BUSCAR_SERVICO,
            Company.Feature.BPE,
            Company.Feature.ITINERARIO,
        ],
    )
    company_2 = baker.make(
        Company,
        company_internal_id=3,
        name="aserpmE",
        integracao__name="ti_sistemas",  # com implementação descobrir_operacao
        features=[
            Company.Feature.BUSCAR_SERVICO,
            Company.Feature.BPE,
            Company.Feature.ITINERARIO,
        ],
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company_1,
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS,
        company=company_1,
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company_2,
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS,
        company=company_2,
    )

    # ao buscar-se os cronogramas de atualização
    params = CronogramaAtualizacaoPaginatorParams(rowsPerPage=10, page=1)
    horarios = get_cronograma_atualizacao(params)

    assert horarios["count"] == 2
    assert horarios["num_pages"] == 1
    horarios["horarios"] = sorted(horarios["horarios"], key=lambda k: k["empresa"]["name"])
    # espera-se que apenas o horário de fetch_trechos_vendidos seja listado para a empresa sem a implementação
    assert horarios["horarios"][0]["empresa"] == {
        "id": company_1.id,
        "company_internal_id": company_1.company_internal_id,
        "modelo_venda": company_1.modelo_venda,
        "integracao_id": company_1.integracao_id,
        "name": company_1.name,
    }
    assert horarios["horarios"][0][CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO]["dias_semana"] == []
    assert horarios["horarios"][0][CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS]["dias_semana"] == []
    assert horarios["horarios"][0][CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE]["dias_semana"] == []
    assert horarios["horarios"][0][CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS]["dias_semana"] == [1]
    # espera-se que apenas o horário de descobrir_rotas seja listado para a empresa com a implementação
    assert horarios["horarios"][1]["empresa"] == {
        "id": company_2.id,
        "company_internal_id": company_2.company_internal_id,
        "modelo_venda": company_2.modelo_venda,
        "integracao_id": company_2.integracao_id,
        "name": company_2.name,
    }
    assert horarios["horarios"][1][CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO]["dias_semana"] == [1]
    assert horarios["horarios"][1][CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS]["dias_semana"] == []
    assert horarios["horarios"][1][CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE]["dias_semana"] == []
    assert horarios["horarios"][1][CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS]["dias_semana"] == []


def test_get_cronograma_atualizacao_empresa_sem_features():
    params = CronogramaAtualizacaoPaginatorParams(rowsPerPage=10, page=1)
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company__name="Empresa",
        company__company_internal_id=2,
        company__features=[Company.Feature.BUSCAR_SERVICO],
    )
    horarios = get_cronograma_atualizacao(params)
    assert horarios["count"] == 0
    assert horarios["num_pages"] == 1
    assert horarios["horarios"] == []


def test_update_cronograma_atualizacao_update():
    company = baker.make(Company, company_internal_id=2, name="aserpmE")
    cronograma = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company,
    )
    params = UpdateCronogramaAtualizacaoParams(
        horarios=[
            CronogramaEmpresaParams(
                empresa=CompanyParams(id=company.id),
                fetch_trechos_vendidos=None,
                fetch_data_limite=None,
                descobrir_rotas=None,
                descobrir_operacao=HorariosAtualizacaoParams(dias_semana=[1], horario=time(20, 0)),
            )
        ]
    )
    update_cronograma_atualizacao(params)
    cronograma.refresh_from_db()
    assert cronograma.horario == time(20, 0)


def test_update_cronograma_atualizacao_create():
    company = baker.make(Company, company_internal_id=2, name="aserpmE")
    params = UpdateCronogramaAtualizacaoParams(
        horarios=[
            CronogramaEmpresaParams(
                empresa=CompanyParams(id=company.id),
                fetch_trechos_vendidos=None,
                fetch_data_limite=None,
                descobrir_rotas=None,
                descobrir_operacao=HorariosAtualizacaoParams(dias_semana=[1], horario=time(20, 0)),
            )
        ]
    )
    update_cronograma_atualizacao(params)
    cronograma = list(CronogramaAtualizacaoOperacao.objects.filter(company=company))
    assert len(cronograma) == 1
    assert cronograma[0].horario == time(20, 0)


def test_update_cronograma_atualizacao_delete():
    company = baker.make(Company, company_internal_id=2, name="aserpmE")
    cronograma = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company,
    )
    params = UpdateCronogramaAtualizacaoParams(
        horarios=[
            CronogramaEmpresaParams(
                empresa=CompanyParams(id=company.id),
                fetch_trechos_vendidos=None,
                fetch_data_limite=None,
                descobrir_rotas=None,
                descobrir_operacao=HorariosAtualizacaoParams(dias_semana=[], horario=time(20, 0)),
            )
        ]
    )
    update_cronograma_atualizacao(params)
    with pytest.raises(CronogramaAtualizacaoOperacao.DoesNotExist):
        cronograma.refresh_from_db()


def test_view_get_cronograma_atualizacao_operacao(rf):
    company = baker.make(
        Company,
        company_internal_id=2,
        name="aserpmE",
        features=[
            Company.Feature.BUSCAR_SERVICO,
            Company.Feature.BPE,
            Company.Feature.ITINERARIO,
        ],
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS,
        company=company,
    )
    params = CronogramaAtualizacaoPaginatorParams(rowsPerPage=10, page=1)
    request = rf.get("/v1/get-cronograma-atualizazao-operacao")
    response = views.get_cronograma_atualizacao_operacao(request, params)
    assert json.loads(response.content) == {
        "horarios": [
            {
                "empresa": {
                    "id": company.id,
                    "company_internal_id": 2,
                    "modelo_venda": Company.ModeloVenda.MARKETPLACE,
                    "integracao_id": company.integracao_id,
                    "name": "aserpmE",
                },
                "integracao_has_descobrir_operacao": False,
                "descobrir_operacao": {"dias_semana": [], "horario": None},
                "fetch_trechos_vendidos": {"dias_semana": [], "horario": None},
                "fetch_data_limite": {"dias_semana": [], "horario": None},
                "descobrir_rotas": {"dias_semana": [1], "horario": "17:35:00"},
                "integracao_automatica": {"dias_semana": [], "horario": None},
            }
        ],
        "count": 1,
        "num_pages": 1,
    }


def test_view_update_cronograma_atualizacao_operacao(rf):
    company = baker.make(Company, company_internal_id=2, name="aserpmE")
    cronograma = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company,
    )
    params = UpdateCronogramaAtualizacaoParams(
        horarios=[
            CronogramaEmpresaParams(
                empresa=CompanyParams(id=company.id),
                fetch_trechos_vendidos=None,
                fetch_data_limite=None,
                descobrir_rotas=None,
                descobrir_operacao=HorariosAtualizacaoParams(dias_semana=[1], horario=time(20, 0)),
            )
        ]
    )
    request = rf.post("/v1/update-cronograma-atualizazao-operacao")
    response = views.update_cronograma_atualizacao_operacao(request, params)
    assert json.loads(response.content) == {}
    cronograma.refresh_from_db()
    assert cronograma.horario == time(20, 0)


def test_get_lista_crons():
    # dadas duas empresas, uma com implementação de descobrir_operacao e outra sem
    company_1 = baker.make(
        Company,
        company_internal_id=2,
        name="Empresa",
        integracao__name="abacate",  # sem implementação descobrir_operacao
        features=[
            Company.Feature.BUSCAR_SERVICO,
            Company.Feature.BPE,
            Company.Feature.ITINERARIO,
        ],
    )
    company_2 = baker.make(
        Company,
        company_internal_id=3,
        name="aserpmE",
        integracao__name="ti_sistemas",  # com implementação descobrir_operacao
        features=[
            Company.Feature.BUSCAR_SERVICO,
            Company.Feature.BPE,
            Company.Feature.ITINERARIO,
        ],
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35, 0),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company_1,
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35, 0),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS,
        company=company_1,
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35, 0),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company_2,
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=7,
        horario=time(17, 35, 0),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
        company=company_2,
    )
    baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=1,
        horario=time(17, 35, 0),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS,
        company=company_2,
    )

    # ao buscar-se a lista de crons a serem cadastrados
    lista_crons = get_lista_crons()

    # espera-se apenas o cron de descobrir_rotas para a empresa com implementação e apenas os outros tipos
    # de cron para a empresa sem implementação
    assert lista_crons == [
        (
            company_1.company_internal_id,
            Company.ModeloVenda.MARKETPLACE,
            CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS,
            "1",
            "17",
            "35",
        ),
        (
            company_2.company_internal_id,
            Company.ModeloVenda.MARKETPLACE,
            CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO,
            "1,0",
            "17",
            "35",
        ),
    ]

import json
from datetime import datetime
from types import SimpleNamespace
from unittest import mock

import pytest
from model_bakery import baker

from commons.dateutils import to_default_tz
from rodoviaria.api.vexado import endpoints as vexado_endpoints
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.data.transbrasil import MAP_CLASSES_VEXADO
from rodoviaria.models.core import Grupo, GrupoClasse
from rodoviaria.models.vexado import <PERSON>eiculo, VexadoGrupoClasse
from rodoviaria.service import cadastrar_grupos_hibridos_svc, veiculos_svc
from rodoviaria.service.cadastrar_grupos_hibridos_svc import CadastrarGrupoSVC, RotaNaoCadastradaException
from rodoviaria.views_schemas import CadastrarGruposParams


@pytest.fixture
def cadastrar_grupos_params(vexado_company_multimodelo):
    rota_internal_id = 4732
    params = CadastrarGruposParams.parse_obj(
        {
            "company_id": vexado_company_multimodelo.company_internal_id,
            "rota_external_id": 1424,
            "grupos": [
                {
                    "data_partida": "2022-04-01",
                    "hora_saida": "12:00",
                    "rota_internal_id": rota_internal_id,
                    "veiculo_internal_id": 7,
                    "veiculo_placa": "KDA1294",
                    "grupo_id": 140,
                    "classes": [
                        {
                            "grupo_classe_id": 155,
                            "tipo": "leito individual",
                            "capacidade": 24,
                        },
                        {
                            "grupo_classe_id": 156,
                            "tipo": "leito cama",
                            "capacidade": 24,
                        },
                    ],
                },
                {
                    "data_partida": "2022-04-02",
                    "hora_saida": "12:00",
                    "rota_internal_id": rota_internal_id,
                    "veiculo_internal_id": 3,
                    "veiculo_placa": "DEF5678",
                    "grupo_id": 141,
                    "classes": [
                        {"grupo_classe_id": 158, "tipo": "leito", "capacidade": 13},
                        {
                            "grupo_classe_id": 157,
                            "tipo": "semi leito",
                            "capacidade": 30,
                        },
                    ],
                },
            ],
        }
    )
    baker.make(
        "rodoviaria.VexadoRota",
        company=vexado_company_multimodelo,
        rota_internal_id=rota_internal_id,
        rota_external_id=1424,
    )
    yield SimpleNamespace(
        params=params,
        grupos_ids=[140, 141],
        grupos_classes_ids=[155, 156, 158, 157],
        rota_external_id=1424,
        rota_internal_id=4732,
    )


def test_cadastrar_grupos_por_rota_external_id(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    make_veiculos(vexado_company_multimodelo, params)
    mock_cadastrar_grupo = mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )
    mock_buscar_rotas = mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(
        params.grupos, rota_external_id=cadastrar_grupos_params.rota_external_id
    )

    assert mock_cadastrar_grupo.call_count == 3
    mock_buscar_rotas.assert_called_once()


def test_cadastrar_grupos_com_duas_origens_na_mesma_cidade(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    make_veiculos(vexado_company_multimodelo, params)
    rota_com_duas_origens_na_mesma_cidade = {
        "delimitacao": str(cadastrar_grupos_params.rota_internal_id),
        "itinerario": [
            {},
            {"cidadeDestino": {"id": 123}, "duracao": "00:00"},
            {"cidadeDestino": {"id": 123}, "duracao": "00:30"},
            {"cidadeDestino": {"id": 321}, "duracao": "10:00"},
            {"cidadeDestino": {"id": 321}, "duracao": "00:20"},
            {"cidadeDestino": {"id": 999}, "duracao": "05:00"},
            {},
        ],
    }
    mock_cadastrar_grupo = mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )
    mock_buscar_rotas = mocker.patch.object(
        VexadoAPI, "buscar_rotas", return_value=[rota_com_duas_origens_na_mesma_cidade]
    )

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(params.grupos)

    mock_buscar_rotas.assert_called_once()
    assert mock_cadastrar_grupo.call_count == 9
    itinerarios_cadastrados_horarios_partidas = sorted(
        [f"{call[0][0].data_partida} {call[0][0].hora_saida}" for call in mock_cadastrar_grupo.call_args_list]
    )
    assert sorted(itinerarios_cadastrados_horarios_partidas) == sorted(
        [
            "2022-04-01 12:00",
            "2022-04-01 12:00",
            "2022-04-01 12:30",
            "2022-04-01 12:30",
            "2022-04-01 12:50",
            "2022-04-01 12:50",
            "2022-04-02 12:00",
            "2022-04-02 12:30",
            "2022-04-02 12:50",
        ]
    )


def test_cadastrar_grupos_com_duas_origens_na_mesma_cidade_passando_por_meia_noite(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    params.grupos[0].hora_saida = "23:50"
    params.grupos[1].hora_saida = "23:50"
    make_veiculos(vexado_company_multimodelo, params)
    rota_com_duas_origens_na_mesma_cidade = {
        "delimitacao": str(cadastrar_grupos_params.rota_internal_id),
        "itinerario": [
            {},
            {"cidadeDestino": {"id": 123}, "duracao": "00:00"},
            {"cidadeDestino": {"id": 123}, "duracao": "00:30"},
            {"cidadeDestino": {"id": 321}, "duracao": "10:00"},
            {"cidadeDestino": {"id": 321}, "duracao": "00:20"},
            {"cidadeDestino": {"id": 999}, "duracao": "05:00"},
            {},
        ],
    }
    mock_cadastrar_grupo = mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )
    mock_buscar_rotas = mocker.patch.object(
        VexadoAPI, "buscar_rotas", return_value=[rota_com_duas_origens_na_mesma_cidade]
    )

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(params.grupos)

    mock_buscar_rotas.assert_called_once()
    assert mock_cadastrar_grupo.call_count == 9
    itinerarios_cadastrados_horarios_partidas = sorted(
        [f"{call[0][0].data_partida} {call[0][0].hora_saida}" for call in mock_cadastrar_grupo.call_args_list]
    )
    assert sorted(itinerarios_cadastrados_horarios_partidas) == sorted(
        [
            "2022-04-01 23:50",
            "2022-04-01 23:50",
            "2022-04-02 00:20",
            "2022-04-02 00:20",
            "2022-04-02 00:40",
            "2022-04-02 00:40",
            "2022-04-02 23:50",
            "2022-04-03 00:20",
            "2022-04-03 00:40",
        ]
    )


def test_cadastrar_grupos_rota_nao_encontrada(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    rota_internal_id = 7382
    params.grupos[0].rota_internal_id = rota_internal_id
    make_veiculos(vexado_company_multimodelo, params)
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])

    with pytest.raises(
        RotaNaoCadastradaException,
        match=f"Rota com id {rota_internal_id} não cadastrada no banco rodoviaria",
    ):
        CadastrarGrupoSVC(params.company_id).cadastrar_grupos(params.grupos)

    baker.make(
        "rodoviaria.VexadoRota",
        rota_internal_id=rota_internal_id,
        company=vexado_company_multimodelo,
    )

    with pytest.raises(
        RotaNaoCadastradaException,
        match=f"Rota com id {rota_internal_id} não cadastrada no banco rodoviaria",
    ):
        CadastrarGrupoSVC(params.company_id).cadastrar_grupos(params.grupos)


def make_veiculos(vexado_company_multimodelo, params):
    mapa_veiculo_1 = baker.make("rodoviaria.MapaVeiculo", quantidade_poltronas_primeiro_andar=24)
    mapa_veiculo_2 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=30,
        quantidade_poltronas_segundo_andar=13,
    )
    baker.make(
        "rodoviaria.Veiculo",
        company=vexado_company_multimodelo,
        id_internal=params.grupos[0].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_1,
        id_external=653,
        descricao="KDA1294",
    )
    baker.make(
        "rodoviaria.Veiculo",
        company=vexado_company_multimodelo,
        id_internal=params.grupos[0].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_1,
        id_external=654,
        descricao="KDA1294*",
    )
    baker.make(
        "rodoviaria.Veiculo",
        company=vexado_company_multimodelo,
        id_internal=params.grupos[1].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_2,
        id_external=423,
        descricao="DEF5678",
    )


def test_cadastrar_grupos_cancelados_ou_fechados(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    make_veiculos(vexado_company_multimodelo, params)
    grupo_classe_1 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=params.grupos[0].classes[0].grupo_classe_id,
    )
    grupo_classe_2 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=params.grupos[0].classes[1].grupo_classe_id,
    )
    grupo_classe_3 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=params.grupos[1].classes[0].grupo_classe_id,
    )
    grupo_classe_4 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=params.grupos[1].classes[1].grupo_classe_id,
    )
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_1,
        status=VexadoGrupoClasse.Status.CANCELADO_CHECK_ERRO,
    )
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_2,
        status=VexadoGrupoClasse.Status.CANCELADO,
    )
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_3,
        status=VexadoGrupoClasse.Status.FECHADO,
    )
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_4,
        status=VexadoGrupoClasse.Status.CANCELADO_DUPLO_CHECK,
    )
    mock_cadastrar_grupo = mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(
        params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
    )

    assert mock_cadastrar_grupo.call_count == 3


def test_cadastrar_grupos_ja_criados(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    make_veiculos(vexado_company_multimodelo, params)
    datetime_ida_1 = to_default_tz(
        datetime.strptime(
            f"{params.grupos[0].data_partida} {params.grupos[0].hora_saida}",
            "%Y-%m-%d %H:%M",
        )
    )
    datetime_ida_2 = to_default_tz(
        datetime.strptime(
            f"{params.grupos[1].data_partida} {params.grupos[1].hora_saida}",
            "%Y-%m-%d %H:%M",
        )
    )
    grupo_1 = baker.make("rodoviaria.Grupo", datetime_ida=datetime_ida_1)
    grupo_2 = baker.make("rodoviaria.Grupo", datetime_ida=datetime_ida_2)
    grupo_classe_1 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=params.grupos[0].classes[0].grupo_classe_id,
        grupo=grupo_1,
        tipo_assento_internal=params.grupos[0].classes[0].tipo,
    )
    grupo_classe_2 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=params.grupos[0].classes[1].grupo_classe_id,
        grupo=grupo_1,
        tipo_assento_internal=params.grupos[0].classes[1].tipo,
    )
    grupo_classe_3 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=params.grupos[1].classes[0].grupo_classe_id,
        grupo=grupo_2,
        tipo_assento_internal=params.grupos[1].classes[0].tipo,
    )
    grupo_classe_4 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=params.grupos[1].classes[1].grupo_classe_id,
        grupo=grupo_2,
        tipo_assento_internal=params.grupos[1].classes[1].tipo,
    )
    for grupo_classe in [
        grupo_classe_1,
        grupo_classe_2,
        grupo_classe_3,
        grupo_classe_4,
    ]:
        baker.make(
            "rodoviaria.VexadoGrupoClasse",
            grupo_classe=grupo_classe,
            status=VexadoGrupoClasse.Status.CRIADO,
            rota_external_id=params.rota_external_id,
        )

    mock_cadastrar_grupo = mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(
        params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
    )

    assert mock_cadastrar_grupo.call_count == 3


def test_cadastrar_grupos_sync_async(mocker, vexado_login, cadastrar_grupos_params, vexado_company_multimodelo):
    params = cadastrar_grupos_params.params
    make_veiculos(vexado_company_multimodelo, params)
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])

    with mock.patch("rodoviaria.service.cadastrar_grupos_hibridos_svc.group") as mock_celery_group:
        CadastrarGrupoSVC(params.company_id, assincrono=False).cadastrar_grupos(
            params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
        )

    mock_celery_group.return_value.apply.assert_called_once()
    mock_celery_group.return_value.apply_async.assert_not_called()

    with mock.patch("rodoviaria.service.cadastrar_grupos_hibridos_svc.group") as mock_celery_group:
        CadastrarGrupoSVC(params.company_id, assincrono=True).cadastrar_grupos(
            params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
        )

    mock_celery_group.return_value.apply.assert_not_called()
    mock_celery_group.return_value.apply_async.assert_called_once()


def test_cadastrar_grupos_update_grupo(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    make_veiculos(vexado_company_multimodelo, params)
    grupo_existente = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=params.grupos[0].grupo_id,
        company_integracao=vexado_company_multimodelo,
    )
    grupo_count = Grupo.objects.filter(grupo_internal_id__in=cadastrar_grupos_params.grupos_ids).count()
    mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(
        params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
    )

    assert Grupo.objects.filter(grupo_internal_id__in=cadastrar_grupos_params.grupos_ids).count() == grupo_count + 1
    grupo_existente.refresh_from_db()
    assert grupo_existente.datetime_ida == to_default_tz(
        datetime.strptime(
            f"{params.grupos[0].data_partida} {params.grupos[0].hora_saida}",
            "%Y-%m-%d %H:%M",
        )
    )


def test_cadastrar_grupos_update_grupo_create_grupo_classe(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    params.grupos = params.grupos[:1]
    params.grupos[0].classes = params.grupos[0].classes[:1]
    mapa_veiculo_1 = baker.make("rodoviaria.MapaVeiculo", quantidade_poltronas_primeiro_andar=24)
    baker.make(
        "rodoviaria.Veiculo",
        company=vexado_company_multimodelo,
        id_internal=params.grupos[0].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_1,
        id_external=653,
        descricao="KDA1294",
    )
    grupo_existente = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=params.grupos[0].grupo_id,
        company_integracao=vexado_company_multimodelo,
    )
    grupo_count = Grupo.objects.filter(grupo_internal_id__in=cadastrar_grupos_params.grupos_ids).count()
    grupo_classe_count = GrupoClasse.objects.filter(grupo=grupo_existente).count()
    mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(
        params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
    )

    assert Grupo.objects.filter(grupo_internal_id__in=cadastrar_grupos_params.grupos_ids).count() == grupo_count
    grupo_existente.refresh_from_db()
    assert grupo_existente.datetime_ida == to_default_tz(
        datetime.strptime(
            f"{params.grupos[0].data_partida} {params.grupos[0].hora_saida}",
            "%Y-%m-%d %H:%M",
        )
    )
    assert GrupoClasse.objects.filter(grupo=grupo_existente).count() == grupo_classe_count + 1


def test_cadastrar_grupo_task(mocker, vexado_api, vexado_login):
    vgc_1 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=baker.make("rodoviaria.GrupoClasse", tipo_assento_internal="leito"),
        status=VexadoGrupoClasse.Status.INCOMPLETO,
    )
    vgc_2 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=baker.make("rodoviaria.GrupoClasse", tipo_assento_internal="semi leito"),
        status=VexadoGrupoClasse.Status.INCOMPLETO,
    )
    grupo_params = json.dumps(
        {
            "data_partida": "2022-02-01",
            "hora_saida": "18:00",
            "id_rota": 999,
            "id_veiculo": 555,
            "classe_primeiro_andar": MAP_CLASSES_VEXADO["leito"],
            "classe_segundo_andar": MAP_CLASSES_VEXADO["semi leito"],
        }
    )
    mock_cadastrar_grupo = mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])
    mock_cadastrar_grupo.return_value = {"leito": 89900, "semi leito": 89901}

    cadastrar_grupos_hibridos_svc.cadastrar_grupo_task(
        vexado_api.company.company_internal_id, grupo_params, [vgc_1.id, vgc_2.id]
    )

    vgc_1.refresh_from_db()
    vgc_2.refresh_from_db()
    assert vgc_1.grupo_classe_external_id == 89900
    assert vgc_1.status == VexadoGrupoClasse.Status.CRIADO
    assert vgc_2.grupo_classe_external_id == 89901
    assert vgc_2.status == VexadoGrupoClasse.Status.CRIADO


def test_cadastrar_grupos_create_veiculo(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
    mock_create_veiculos_api,
):
    params = CadastrarGruposParams.parse_obj(
        {
            "company_id": vexado_company_multimodelo.company_internal_id,
            "rota_external_id": 1424,
            "grupos": [
                {
                    "data_partida": "2022-04-04",
                    "hora_saida": "15:00",
                    "veiculo_internal_id": 992312,
                    "rota_internal_id": cadastrar_grupos_params.rota_internal_id,
                    "veiculo_placa": "APJ8F14",  # valor retirado do mock
                    "grupo_id": 9430,
                    "classes": [{"grupo_classe_id": 43231, "tipo": "leito", "capacidade": 24}],
                },
                {
                    "data_partida": "2022-04-06",
                    "hora_saida": "15:00",
                    "veiculo_internal_id": 992312,
                    "rota_internal_id": cadastrar_grupos_params.rota_internal_id,
                    "veiculo_placa": "APJ8F14",  # valor retirado do mock
                    "grupo_id": 9431,
                    "classes": [{"grupo_classe_id": 43232, "tipo": "leito", "capacidade": 24}],
                },
            ],
        }
    )
    baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=24,
        quantidade_poltronas_segundo_andar=0,
    )
    assert not Veiculo.objects.filter(id_internal=992312).exists()
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])
    mock_cadastrar_grupo = mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(
        params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
    )

    assert mock_cadastrar_grupo.call_count == 2
    assert Veiculo.objects.filter(id_internal=992312).count() == 1
    mock_create_veiculos_api.assert_call_count(f"{vexado_api.base_url}/{vexado_endpoints.CadastrarVeiculo.path}", 1)


def test_cadastrar_grupos_veiculo_de_duas_empresas(
    mocker,
    vexado_api,
    vexado_login,
    cadastrar_grupos_params,
    vexado_company_multimodelo,
):
    params = cadastrar_grupos_params.params
    outra_company = baker.make("rodoviaria.Company", company_internal_id=params.company_id * 2)
    mapa_veiculo_1 = baker.make("rodoviaria.MapaVeiculo", quantidade_poltronas_primeiro_andar=24)
    mapa_veiculo_2 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=30,
        quantidade_poltronas_segundo_andar=13,
    )
    baker.make(
        "rodoviaria.Veiculo",
        company=vexado_company_multimodelo,
        id_internal=params.grupos[0].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_1,
        id_external=653,
        descricao="KDA1294",
    )
    baker.make(
        "rodoviaria.Veiculo",
        company=vexado_company_multimodelo,
        id_internal=params.grupos[0].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_1,
        id_external=654,
        descricao="KDA1294*",
    )
    baker.make(
        "rodoviaria.Veiculo",
        company=vexado_company_multimodelo,
        id_internal=params.grupos[1].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_2,
        id_external=423,
        descricao="DEF5678",
    )
    baker.make(
        "rodoviaria.Veiculo",
        company=outra_company,
        id_internal=params.grupos[0].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_1,
        id_external=653,
        descricao="KDA1294",
    )
    baker.make(
        "rodoviaria.Veiculo",
        company=outra_company,
        id_internal=params.grupos[0].veiculo_internal_id,
        mapa_veiculo=mapa_veiculo_1,
        id_external=654,
        descricao="KDA1294*",
    )
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])
    mock_cadastrar_grupo = mocker.patch.object(
        VexadoAPI,
        "cadastrar_grupo",
        return_value={
            "leito individual": 1,
            "leito cama": 1,
            "semi leito": 1,
            "leito": 1,
        },
    )

    CadastrarGrupoSVC(params.company_id).cadastrar_grupos(
        params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
    )

    assert mock_cadastrar_grupo.call_count == 3


def test_cadastrar_grupos_erro_veiculo(mocker, vexado_api, vexado_login, cadastrar_grupos_params):
    params = cadastrar_grupos_params.params
    mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[])
    with pytest.raises(
        veiculos_svc.GetOrCreateVeiculosError,
        match="Não foi encontrado um conjunto de mapas de veiculos para esse onibus",
    ):
        CadastrarGrupoSVC(params.company_id).cadastrar_grupos(
            params.grupos, rota_internal_id=cadastrar_grupos_params.rota_internal_id
        )


def test_cadastrar_grupos_params_no_last_option(vexado_api, vexado_company_multimodelo, vexado_login):
    rota_internal_id = 432
    baker.make(
        "rodoviaria.MapaVeiculo",
        id_external=4332,
        quantidade_poltronas_primeiro_andar=32,
    )

    with mock.patch.object(VexadoAPI, "buscar_rotas") as mock_buscar_rotas:
        mock_buscar_rotas.return_value = [{"id": 1}, {"id": 2}]
        response = cadastrar_grupos_hibridos_svc.cadastrar_grupos_params(
            vexado_company_multimodelo.company_internal_id, rota_internal_id
        )

    assert mock_buscar_rotas.call_count == 1
    assert response == {"rotas": [{"id": 1}, {"id": 2}], "last_option": None}


def test_cadastrar_grupos_params_with_last_option(vexado_api, vexado_company_multimodelo, vexado_login):
    rota_internal_id = 432
    rota_external_id = 2381
    baker.make(
        "rodoviaria.MapaVeiculo",
        id_external=4332,
        quantidade_poltronas_primeiro_andar=32,
    )
    baker.make(
        "rodoviaria.VexadoRota",
        company=vexado_company_multimodelo,
        rota_internal_id=rota_internal_id,
        rota_external_id=rota_external_id,
    )

    with mock.patch.object(VexadoAPI, "buscar_rotas") as mock_buscar_rotas:
        mock_buscar_rotas.return_value = [{"id": 943}, {"id": rota_external_id}]
        response = cadastrar_grupos_hibridos_svc.cadastrar_grupos_params(
            vexado_company_multimodelo.company_internal_id, rota_internal_id
        )

    assert mock_buscar_rotas.call_count == 1
    assert response == {
        "rotas": [{"id": 943}, {"id": rota_external_id}],
        "last_option": rota_external_id,
    }


def test_get_and_check_veiculo_only_check():
    veiculos_linkados_map = {3123: [{"veiculo": 1}]}
    grupo = SimpleNamespace(veiculo_internal_id=3123, veiculo_params={"parametros"})
    company = SimpleNamespace(id=13043)

    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo:
        mock_check_veiculo.return_value = ["veiculo"]
        response = cadastrar_grupos_hibridos_svc._get_and_check_veiculos(veiculos_linkados_map, grupo, company)

    assert response == ["veiculo"]
    mock_check_veiculo.assert_called_once()


def test_get_and_check_veiculo_update_veiculo():
    veiculos_linkados_map = {3123: [{"veiculo": 1}]}
    grupo = SimpleNamespace(veiculo_internal_id=3123, veiculo_params={"parametros"})
    company = SimpleNamespace(id=13043)

    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo, mock.patch(
        "rodoviaria.service.veiculos_svc._update_veiculo"
    ) as mock_update_veiculo:
        mock_check_veiculo.side_effect = veiculos_svc.GetOrCreateVeiculosError()
        mock_update_veiculo.return_value = ["veiculo"]
        response = cadastrar_grupos_hibridos_svc._get_and_check_veiculos(veiculos_linkados_map, grupo, company)

    assert response == ["veiculo"]
    mock_check_veiculo.assert_called_once()
    mock_update_veiculo.assert_called_once()


def test_get_and_check_veiculo_link():
    veiculos_linkados_map = {3123: [{"veiculo": 1}]}
    v1 = baker.make("rodoviaria.Veiculo", descricao="ABC1234")
    v2 = baker.make("rodoviaria.Veiculo", descricao="ABC1234*")
    grupo = SimpleNamespace(veiculo_internal_id=8932, veiculo_placa="ABC1234", veiculo_params={"parametros"})
    company = {"company"}

    with mock.patch("rodoviaria.service.veiculos_svc.link_or_create_veiculo") as mock_link_veiculo:
        mock_link_veiculo.return_value = [v1, v2]
        response = cadastrar_grupos_hibridos_svc._get_and_check_veiculos(veiculos_linkados_map, grupo, {"company"})

    assert response == [v1, v2]
    mock_link_veiculo.assert_called_once_with(grupo.veiculo_params, company)


def test_get_and_check_veiculo_error():
    veiculos_linkados_map = {3123: [{"veiculo": 1}]}
    grupo = SimpleNamespace(veiculo_internal_id=3123, veiculo_params={"parametros"})
    company = SimpleNamespace(id=13043)

    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo, pytest.raises(
        veiculos_svc.GetOrCreateVeiculosError, match="Mapa de veiculo nao encontrado"
    ):
        mock_check_veiculo.return_value = {"error": "Mapa de veiculo nao encontrado"}
        cadastrar_grupos_hibridos_svc._get_and_check_veiculos(veiculos_linkados_map, grupo, company)

    mock_check_veiculo.assert_called_once()

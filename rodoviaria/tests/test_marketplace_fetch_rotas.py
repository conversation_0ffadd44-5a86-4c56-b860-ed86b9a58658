import datetime
import logging
from itertools import cycle
from types import SimpleNamespace
from unittest import mock

import pytest
from celery.exceptions import Retry
from django.utils import timezone
from model_bakery import baker

from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import to_default_tz, to_tz
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.models.core import Company, Grupo, Rota, TrechoClasse
from rodoviaria.service.exceptions import RodoviariaCompanyNotFoundException, RodoviariaTooManyRequestsError
from rodoviaria.service.marketplace_fetch_rotas_svc import (
    _atualizar_rota_do_grupo_por_trecho_classe,
    _get_distinct_tc_external_id_and_datetime,
    fetch_rota_external,
    marketplace_fetch_rotas,
)

buserlogger = logging.getLogger("rodoviaria")
STR_DATA_PRAXIO = "2021-09-03T17:00:00"
EXTERNAL_DATETIME_IDA_PRAXIO = to_default_tz(datetime.datetime.strptime(STR_DATA_PRAXIO, "%Y-%m-%dT%H:%M:%S"))
EXTERNAL_DATETIME_IDA_TOTALBUS = datetime.datetime(2021, 9, 3, 8, 30)


def _mock_itinerario_praxio():
    cleaned = [
        {
            "DataBloqSemPassageiro": "0001-01-01T00:00:00",
            "HoraBloqSemPassageiro": None,
            "IDViagem": 232,
            "DataPartida": STR_DATA_PRAXIO,
            "HoraPartida": "1700",
            "Sentido": 0,
            "Plataforma": "31",
            "HoraChegada": None,
            "ControlaPoltrona": 0,
            "ControlaPassageiros": 0,
            "ControlaCliente": 0,
            "Localidade": {
                "ListEstabelecimentos": None,
                "BilheteEmbW2i": 0,
                "IDLocalidade": 1,
                "Descricao": "Goiania",
                "Sigla": "Gyn",
                "IdRegiao": None,
                "Uf": "GO",
                "IdEstabelecimento": 0,
                "IdCidade": 5208707,
                "TxEmbIdoso50": 0,
                "TxEmbIdoso100": 0,
                "TxEmbPasseLivre": 0,
                "PedagioIdoso100": 0,
                "PedagioIdoso50": 0,
                "Codigo": 0,
                "AgenciasCargas": None,
                "LastUpdate": None,
                "TxPedagioPasseLivre": None,
                "TxEmbIdosoDef": 0,
                "CodigoSgltar": 0,
            },
            "DataChegada": "0001-01-01T00:00:00",
            "Obs": None,
            "HoraTolerancia": None,
            "DataExibirViagem": "0001-01-01T00:00:00",
            "HoraExibirViagem": None,
            "QtdPoltronaBloqueio": 0,
        },
        {
            "DataBloqSemPassageiro": "0001-01-01T00:00:00",
            "HoraBloqSemPassageiro": None,
            "IDViagem": 1592,
            "DataPartida": "2021-09-03T21:30:00",
            "HoraPartida": "2130",
            "Sentido": 0,
            "Plataforma": "D",
            "HoraChegada": None,
            "ControlaPoltrona": 0,
            "ControlaPassageiros": 0,
            "ControlaCliente": 0,
            "Localidade": {
                "ListEstabelecimentos": None,
                "BilheteEmbW2i": 0,
                "IDLocalidade": 13,
                "Descricao": "FEIRA DE SANTANA (BA)",
                "Sigla": "FEI",
                "IdRegiao": None,
                "Uf": "BA",
                "IdEstabelecimento": 0,
                "IdCidade": 2910800,
                "TxEmbIdoso50": 0,
                "TxEmbIdoso100": 0,
                "TxEmbPasseLivre": 0,
                "PedagioIdoso100": 0,
                "PedagioIdoso50": 0,
                "Codigo": 4941,
                "AgenciasCargas": None,
                "LastUpdate": None,
                "TxPedagioPasseLivre": None,
                "TxEmbIdosoDef": 0,
                "CodigoSgltar": 0,
            },
            "DataChegada": "0001-01-01T00:00:00",
            "Obs": None,
            "HoraTolerancia": None,
            "DataExibirViagem": "0001-01-01T00:00:00",
            "HoraExibirViagem": None,
            "QtdPoltronaBloqueio": 0,
        },
    ]

    itinerario = SimpleNamespace(
        parsed=SimpleNamespace(hash="GynSPTI79ec6b6307e37bbfb82bc0994d9180dc"),
        cleaned=cleaned,
    )

    return itinerario


def _mock_itinerario_totalbus():
    datetime_ida = EXTERNAL_DATETIME_IDA_TOTALBUS
    cleaned = [
        {
            "localidade": {"id": 2063, "cidade": "FORTALEZA - CE", "uf": "CE"},
            "distancia": "69.7",
            "permanencia": "00:00",
            "data": datetime_ida.strftime("%Y-%m-%d"),
            "hora": datetime_ida.strftime("%H:%M"),
        },
        {
            "localidade": {"id": 2064, "cidade": "PACAEMBU - SP", "uf": "SP"},
            "distancia": "69.7",
            "permanencia": "00:00",
            "data": "2021-11-25",
            "hora": "15:30",
        },
    ]

    itinerario = SimpleNamespace(
        parsed=SimpleNamespace(hash="GynSPTI79ec6b6307e37bbfb82bc0994d9180dc"),
        cleaned=cleaned,
    )

    return itinerario


@pytest.fixture
def trecho_classe_fixture(praxio_company):
    grupo = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=232,
        company_integracao=praxio_company,
        datetime_ida=to_default_tz(timezone.now() + timezone.timedelta(hours=5)),
    )
    cidade = baker.make("rodoviaria.Cidade", id_external=18852, name="Mock", company=praxio_company)
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=1, cidade=cidade)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=57, cidade=cidade)
    mock_trecho_classes = [
        (
            9468521,
            timezone.now() + timezone.timedelta(hours=5),
            3467,
            timezone.now() + timezone.timedelta(hours=5),
            94.57,
            2,
        )
    ]
    for tc in mock_trecho_classes:
        baker.make(
            "rodoviaria.TrechoClasse",
            trechoclasse_internal_id=tc[0],
            grupo=grupo,
            external_id=tc[0],
            datetime_ida=to_default_tz(tc[1]),
            external_datetime_ida=to_default_tz(tc[3]),
            preco_rodoviaria=tc[4],
            external_id_tipo_veiculo=tc[5],
            destino=destino,
            origem=origem,
        )
    yield SimpleNamespace(grupo=grupo)


@pytest.fixture
def company_unbounded():
    company = Company()
    return company


@pytest.fixture
def orchestrator_mock(company_unbounded):
    orchestrator = OrchestrateRodoviaria(company_unbounded.company_internal_id, company_unbounded.modelo_venda)
    orchestrator._provider = mock.Mock(company=company_unbounded)
    return orchestrator


@pytest.fixture
def grupos_totalbus(totalbus_company):
    datetime_ida = to_default_tz(datetime.datetime.now() + datetime.timedelta(days=1))

    cidade_origem = baker.make("rodoviaria.Cidade", id=832, name="cidade_origem", timezone="America/Sao_Paulo")
    local_origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id=356,
        cidade=cidade_origem,
        nickname="local_origem",
    )

    grupo = baker.make(
        "rodoviaria.Grupo",
        company_integracao=totalbus_company,
        grupo_internal_id=312312,
        datetime_ida=datetime_ida,
    )
    grupo2 = baker.make(
        "rodoviaria.Grupo",
        company_integracao=totalbus_company,
        grupo_internal_id=1111,
        datetime_ida=datetime_ida + datetime.timedelta(days=10),
    )

    external_id = 123123
    baker.make(
        "rodoviaria.TrechoClasse",
        grupo=grupo,
        external_id=external_id,
        origem=local_origem,
    )
    baker.make(
        "rodoviaria.TrechoClasse",
        grupo=grupo2,
        external_id=external_id,
        origem=local_origem,
    )
    return SimpleNamespace(grupos=(grupo, grupo2), external_id=external_id)


def test_marktplace_fetch_rotas_sem_trechoclasse(orchestrator_mock, totalbus_company, totalbus_login, grupos_totalbus):
    assert marketplace_fetch_rotas(grupo_id=999) == {"error": "TrechoClasse não encontrado"}


def test_marktplace_fetch_rotas_400(orchestrator_mock, totalbus_company, totalbus_login, grupos_totalbus):
    with mock.patch.object(OrchestrateRodoviaria, "fetch_rota") as mock_fetch_rota:
        mock_fetch_rota.side_effect = RodoviariaTooManyRequestsError("algum erro")
        with pytest.raises(Retry):
            assert marketplace_fetch_rotas(totalbus_company.company_internal_id) == {"error": "algum erro"}


def test_marktplace_fetch_rotas_nao_pega_hibrido(orchestrator_mock, totalbus_company, totalbus_login, grupos_totalbus):
    totalbus_company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_company.save()
    with mock.patch("rodoviaria.service.marketplace_fetch_rotas_svc.group") as mock_celery_group:
        marketplace_fetch_rotas(totalbus_company.company_internal_id)
    mock_celery_group.assert_called_once_with([])


def test_marktplace_fetch_rotas_celery_by_company_id_totalbus(
    orchestrator_mock, totalbus_company, totalbus_login, grupos_totalbus
):
    with mock.patch.object(TotalbusAPI, "itinerario") as orchestrator:
        orchestrator.return_value = _mock_itinerario_totalbus()

        grupo, grupo2 = grupos_totalbus.grupos

        assert grupo.rota is None
        assert grupo.external_datetime_ida is None

        assert grupo2.rota is None
        assert grupo2.external_datetime_ida is None

        marketplace_fetch_rotas(
            totalbus_company.company_internal_id,
        )

        str_datetime = EXTERNAL_DATETIME_IDA_TOTALBUS.strftime("%Y-%m-%dT%H:%M")
        grupo.refresh_from_db()
        assert grupo.rota
        assert grupo.rota.id_external == str(grupos_totalbus.external_id)
        assert to_tz(grupo.external_datetime_ida, "America/Sao_Paulo").strftime("%Y-%m-%dT%H:%M") == str_datetime

        grupo2.refresh_from_db()
        assert grupo2.rota
        assert grupo2.rota.id_external == str(grupos_totalbus.external_id)
        assert to_tz(grupo2.external_datetime_ida, "America/Sao_Paulo").strftime("%Y-%m-%dT%H:%M") == str_datetime


def test_marktplace_fetch_rotas_celery_by_company_id_totalbus_com_rota_external_id(
    orchestrator_mock, totalbus_company, totalbus_login, grupos_totalbus
):
    with mock.patch.object(TotalbusAPI, "itinerario") as orchestrator:
        mock_itinerario = _mock_itinerario_totalbus()
        mock_itinerario.parsed.rota_external_id = 3284
        orchestrator.return_value = mock_itinerario

        grupo, grupo2 = grupos_totalbus.grupos

        marketplace_fetch_rotas(
            totalbus_company.company_internal_id,
        )
        grupo.refresh_from_db()
        assert grupo.rota.id_external == "3284"


def test_atualizar_rota_do_grupo_por_trecho_classe_totalbus(orchestrator_mock, totalbus_company, totalbus_login):
    with mock.patch.object(TotalbusAPI, "itinerario") as orchestrator:
        external_id = 23123
        orchestrator.return_value = _mock_itinerario_totalbus()

        cidade = baker.make(
            "rodoviaria.Cidade",
            id_external=2063,
            name="Mock",
            company=totalbus_company,
            timezone="America/Cuiaba",
        )
        (
            baker.make(
                "rodoviaria.LocalEmbarque",
                id_external=2063,
                nickname="FORTALEZA",
                cidade=cidade,
            ),
        )
        grupo = baker.make(
            "rodoviaria.Grupo",
            company_integracao=totalbus_company,
            grupo_internal_id=312312,
        )
        baker.make("rodoviaria.TrechoClasse", grupo=grupo, external_id=external_id)
        trecho_classe = TrechoClasse.objects.get(grupo_id=grupo.id)

        assert grupo.rota is None
        assert grupo.external_datetime_ida is None

        _atualizar_rota_do_grupo_por_trecho_classe.apply(
            args=(
                totalbus_company.company_internal_id,
                grupo.id,
                trecho_classe.external_id,
                grupo.datetime_ida,
            )
        ).get()

        grupo.refresh_from_db()
        assert grupo.rota
        assert grupo.rota.id_external == str(external_id)
        assert to_tz(grupo.external_datetime_ida, cidade.timezone).strftime(
            "%Y-%m-%dT%H:%M"
        ) == EXTERNAL_DATETIME_IDA_TOTALBUS.strftime("%Y-%m-%dT%H:%M")


def test_atualizar_rota_do_grupo_por_trecho_classe_praxio(
    trecho_classe_fixture, orchestrator_mock, praxio_company, praxio_login
):
    grupo = Grupo.objects.get(grupo_internal_id=232)
    trecho_classe = TrechoClasse.objects.get(grupo_id=grupo.id)
    with mock.patch.object(PraxioAPI, "itinerario") as orchestrator:
        orchestrator.return_value = _mock_itinerario_praxio()

        assert grupo.rota is None
        assert grupo.external_datetime_ida is None

        _atualizar_rota_do_grupo_por_trecho_classe.apply(
            args=(
                praxio_company.company_internal_id,
                grupo.id,
                trecho_classe.external_id,
                grupo.datetime_ida,
            )
        ).get()

        grupo.refresh_from_db()
        assert grupo.rota
        assert grupo.rota.id_external == str(trecho_classe.external_id)
        assert grupo.external_datetime_ida == EXTERNAL_DATETIME_IDA_PRAXIO


def test_atualizar_rota_do_grupo_por_trecho_classe_vexado_marketplace(
    vexado_company, vexado_login, mock_recuperar_itinerario_vexado
):
    vexado_company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_company.save()
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company, grupo_internal_id=312312)
    baker.make("rodoviaria.TrechoClasse", grupo=grupo, external_id=23123)
    trecho_classe = TrechoClasse.objects.get(grupo_id=grupo.id)

    assert grupo.rota is None

    _atualizar_rota_do_grupo_por_trecho_classe.apply(
        args=(
            vexado_company.company_internal_id,
            grupo.id,
            trecho_classe.external_id,
            grupo.datetime_ida,
        )
    ).get()

    grupo.refresh_from_db()
    assert grupo.rota
    assert grupo.rota.id_external == str(trecho_classe.external_id)


def test_atualizar_rota_do_grupo_por_trecho_classe_vexado_hibrido(vexado_company, vexado_login):
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company, grupo_internal_id=312312)
    baker.make("rodoviaria.TrechoClasse", grupo=grupo, external_id=23123)
    trecho_classe = TrechoClasse.objects.get(grupo_id=grupo.id)

    with pytest.raises(RodoviariaCompanyNotFoundException):
        _atualizar_rota_do_grupo_por_trecho_classe.apply(
            args=(
                vexado_company.company_internal_id,
                grupo.id,
                trecho_classe.external_id,
                grupo.datetime_ida,
            )
        ).get()


def test_atualizar_rota_do_grupo_por_trecho_classe_erro_circuit_breaker(vexado_company, vexado_login):
    with mock.patch("rodoviaria.service.marketplace_fetch_rotas_svc._fetch_rota") as mock__fetch_rota:
        mock__fetch_rota.side_effect = MyCircuitBreakerError("menssagem qualquer", remaining_seconds=10)

        grupo = baker.make(
            "rodoviaria.Grupo",
            company_integracao=vexado_company,
            grupo_internal_id=312312,
        )
        baker.make("rodoviaria.TrechoClasse", grupo=grupo, external_id=23123)
        trecho_classe = TrechoClasse.objects.get(grupo_id=grupo.id)

        assert grupo.rota is None

        with pytest.raises(Retry):
            _atualizar_rota_do_grupo_por_trecho_classe.apply(
                args=(
                    vexado_company.company_internal_id,
                    grupo.id,
                    trecho_classe.external_id,
                    grupo.datetime_ida,
                )
            ).get()

    grupo.refresh_from_db()
    assert grupo.rota is None


def test_atualizar_rota_do_grupo_por_trecho_classe_erro_too_many_requests_fetch_rotas(vexado_company, vexado_login):
    with mock.patch.object(OrchestrateRodoviaria, "fetch_rota") as mock_fetch_rota:
        mock_fetch_rota.side_effect = RodoviariaTooManyRequestsError("algum erro")
        grupo = baker.make(
            "rodoviaria.Grupo",
            company_integracao=vexado_company,
            grupo_internal_id=312312,
        )
        baker.make("rodoviaria.TrechoClasse", grupo=grupo, external_id=23123)
        trecho_classe = TrechoClasse.objects.get(grupo_id=grupo.id)

        assert grupo.rota is None

        with pytest.raises(Retry):
            _atualizar_rota_do_grupo_por_trecho_classe.apply(
                args=(
                    vexado_company.company_internal_id,
                    grupo.id,
                    trecho_classe.external_id,
                    grupo.datetime_ida,
                )
            ).get()

    grupo.refresh_from_db()
    assert grupo.rota is None


def test_marktplace_fetch_rotas_by_grupo_id(trecho_classe_fixture, orchestrator_mock, praxio_company, praxio_login):
    with mock.patch.object(PraxioAPI, "itinerario") as orchestrator:
        mock_itinerario = _mock_itinerario_praxio()
        mock_itinerario.parsed.rota_external_id = 9403
        orchestrator.return_value = mock_itinerario
        grupo = trecho_classe_fixture.grupo
        assert grupo.rota is None
        assert grupo.external_datetime_ida is None
        qtd_rotas = marketplace_fetch_rotas(grupo_id=grupo.grupo_internal_id)
        buserlogger.info(
            "Rotas atualizadas ou criadas para %s rotas da empresa de id %s",
            qtd_rotas,
            praxio_company.company_internal_id,
        )
        grupo.refresh_from_db()
        assert grupo.rota
        assert grupo.external_datetime_ida == EXTERNAL_DATETIME_IDA_PRAXIO
        assert grupo.rota.id_external == "9403"


def test_marktplace_fetch_rotas_by_grupo_id_multimodelo(
    orchestrator_mock, praxio_company, vexado_company, praxio_login, vexado_login
):
    rota = baker.make(Rota, provider_data="{}")
    company_internal_id = 123
    grupo_marketplace_id = 83723
    grupo_hibrido_id = 98321
    company_marketplace = praxio_company
    company_hibrido = vexado_company
    company_marketplace.company_internal_id = company_internal_id
    company_hibrido.company_internal_id = company_internal_id
    company_marketplace.save()
    company_hibrido.save()
    grupo_marketplace = baker.make(
        Grupo,
        grupo_internal_id=grupo_marketplace_id,
        company_integracao=company_marketplace,
        rota=rota,
    )
    grupo_hibrido = baker.make(
        Grupo,
        grupo_internal_id=grupo_hibrido_id,
        company_integracao=company_hibrido,
        rota=rota,
    )
    baker.make(TrechoClasse, grupo=grupo_marketplace)
    baker.make(TrechoClasse, grupo=grupo_hibrido)
    with mock.patch.object(PraxioAPI, "itinerario") as mock_itinerario_praxio, mock.patch.object(
        VexadoAPI, "itinerario"
    ) as mock_itinerario_vexado, mock.patch("rodoviaria.service.marketplace_fetch_rotas_svc.atualizar_rota_grupo"):
        marketplace_fetch_rotas(grupo_id=grupo_marketplace_id)
    mock_itinerario_praxio.assert_called_once()
    mock_itinerario_vexado.assert_not_called()

    with mock.patch.object(PraxioAPI, "itinerario") as mock_itinerario_praxio, mock.patch.object(
        VexadoAPI, "itinerario"
    ) as mock_itinerario_vexado, mock.patch("rodoviaria.service.marketplace_fetch_rotas_svc.atualizar_rota_grupo"):
        marketplace_fetch_rotas(grupo_id=grupo_hibrido_id)
    mock_itinerario_praxio.assert_not_called()
    mock_itinerario_vexado.assert_called_once()


def test_vexado_fetch_rotas(
    vexado_company,
    vexado_login,
    mock_recuperar_itinerario_vexado,
    mock_locais_do_response_de_recuperar_itinerario_vexado,
):
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company, grupo_internal_id=312312)
    baker.make("rodoviaria.TrechoClasse", grupo=grupo, external_id=23123)
    resp = marketplace_fetch_rotas(grupo_id=grupo.grupo_internal_id)
    assert "rota" in resp
    assert isinstance(resp["rota"], list)
    assert len(resp["rota"]) == 16


def test_marktplace_fetch_rota_external_sem_rota_external_id(orchestrator_mock, praxio_company, praxio_login):
    with mock.patch.object(PraxioAPI, "itinerario") as orchestrator:
        orchestrator.return_value = _mock_itinerario_praxio()
        rota = fetch_rota_external(
            company_internal_id=praxio_company.company_internal_id,
            id_external="123456",
            data="2022-04-14",
            modelo_venda=praxio_company.modelo_venda,
        )
        assert len(rota) == len(_mock_itinerario_praxio().cleaned)


def test_marktplace_fetch_rota_external_company_hibrido(orchestrator_mock, praxio_company, praxio_login):
    praxio_company.modelo_venda = Company.ModeloVenda.HIBRIDO
    praxio_company.save()
    with pytest.raises(RodoviariaCompanyNotFoundException):
        fetch_rota_external(
            company_internal_id=praxio_company.company_internal_id,
            id_external="123456",
            data="2022-04-14",
            modelo_venda=Company.ModeloVenda.MARKETPLACE,
        )


def test_marktplace_fetch_rota_external_com_external_id(orchestrator_mock, praxio_company, praxio_login):
    with mock.patch.object(PraxioAPI, "itinerario") as orchestrator:
        itinerario_mock = _mock_itinerario_praxio()
        itinerario_mock.parsed.rota_external_id = 8325
        orchestrator.return_value = itinerario_mock
        rota = fetch_rota_external(
            company_internal_id=praxio_company.company_internal_id,
            id_external="123456",
            data="2022-04-14",
            modelo_venda=praxio_company.modelo_venda,
        )
        assert len(rota) == len(_mock_itinerario_praxio().cleaned)
        assert Rota.objects.filter(id_external=8325).exists()


def test_get_distinct_tc_by_external_id_and_datetime(totalbus_api):
    company = totalbus_api.company
    company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    company.save()
    datetime_1 = timezone.now() + datetime.timedelta(days=1)
    datetime_2 = datetime_1 + datetime.timedelta(days=1)

    grupos = baker.make(
        "rodoviaria.Grupo",
        datetime_ida=cycle([datetime_1, datetime_2]),
        company_integracao=company,
        _quantity=4,
    )
    external_ids = ["10", "20"]
    baker.make("rodoviaria.TrechoClasse", external_id=cycle(external_ids), grupo=cycle(grupos), _quantity=4)

    tcs = _get_distinct_tc_external_id_and_datetime(company.company_internal_id)
    datetimes_tcs = [tc.grupo.datetime_ida for tc in tcs]
    external_ids_tcs = [tc.external_id for tc in tcs]

    assert len(tcs) == 2
    assert sorted(datetimes_tcs) == [datetime_1, datetime_2]
    assert sorted(external_ids_tcs) == external_ids

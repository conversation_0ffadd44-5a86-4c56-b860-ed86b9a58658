import logging
from unittest import mock

import pytest
from django.db import connections
from model_bakery import baker

from commons.token_bucket import (
    NotEnoughTokens,
    NullTokenBucket,
    TokenBucket,
    TokenBucketConfig,
    get_token_bucket_robust,
    integracao_config_map,
    integracao_login_map,
)
from rodoviaria.models.core import Integracao
from rodoviaria.models.praxio import PraxioLogin


def test_token_bucket_ok(caplog):
    with caplog.at_level(logging.INFO):
        token_bucket = TokenBucket("bucket_a", bucket_size=1)
    assert token_bucket.try_get_token()
    assert caplog.records[0].message == "TokenBucket: sucesso ao obter token!"


def test_token_bucket_raises_not_enough_tokens():
    token_bucket = TokenBucket("bucket_b", bucket_size=1)
    assert token_bucket.try_get_token()

    with pytest.raises(NotEnoughTokens):
        token_bucket.try_get_token()


def test_get_token_bucket_robust(mocker):
    mocker.patch(
        "commons.token_bucket.cliente_config_map",
        {"PartyBusEnterprises": TokenBucketConfig(bucket_size=15, refresh_interval=7)},
    )
    mocker.patch(
        "commons.token_bucket.integracao_config_map", {"praxio": TokenBucketConfig(bucket_size=20, refresh_interval=6)}
    )

    integracao = baker.make("rodoviaria.Integracao", name="praxio")
    company = baker.make("rodoviaria.Company", integracao=integracao, company_internal_id=456)
    # TypeError: field password type <class 'django_cryptography.fields.EncryptedCharField'> is not supported by baker
    # baker.make("rodoviaria.PraxioLogin", company=company, cliente="PartyBusEnterprises")
    login = PraxioLogin.objects.create(
        company=company, name="PartyBus", cliente="PartyBusEnterprises", password="supersecret"
    )

    token_bucket = get_token_bucket_robust(company.company_internal_id)

    assert token_bucket.name == f"{integracao.name}:{login.cliente}"
    assert token_bucket.bucket_size == 15
    assert token_bucket.refresh_interval == 7
    assert token_bucket.try_get_token()


def test_get_token_bucket_robust_cliente_nao_mapeado(mocker):
    mocker.patch("commons.token_bucket.cliente_config_map", {})
    mocker.patch(
        "commons.token_bucket.integracao_config_map",
        {"praxio": lambda: TokenBucketConfig(bucket_size=20, refresh_interval=6)},
    )

    integracao = baker.make("rodoviaria.Integracao", name="praxio")
    company = baker.make("rodoviaria.Company", integracao=integracao, company_internal_id=456)
    login = baker.make("rodoviaria.PraxioLogin", company=company, cliente="PartyBusEnterprises")

    token_bucket = get_token_bucket_robust(company.company_internal_id)

    assert token_bucket.name == f"{integracao.name}:{login.cliente}"
    assert token_bucket.bucket_size == 20
    assert token_bucket.refresh_interval == 6
    assert token_bucket.try_get_token()


def test_get_token_bucket_robust_cliente_e_integracao_nao_mapeados(mocker):
    mocker.patch("commons.token_bucket.cliente_config_map", {})
    mocker.patch("commons.token_bucket.integracao_config_map", {})

    integracao = baker.make("rodoviaria.Integracao", name="praxio")
    company = baker.make("rodoviaria.Company", integracao=integracao, company_internal_id=456)
    baker.make("rodoviaria.PraxioLogin", company=company, cliente="PartyBusEnterprises")

    token_bucket = get_token_bucket_robust(company.company_internal_id)
    assert isinstance(token_bucket, NullTokenBucket)
    assert token_bucket.try_get_token()


def test_get_token_bucket_robust_sem_login(mocker):
    mocker.patch(
        "commons.token_bucket.cliente_config_map",
        {"PartyBusEnterprises": TokenBucketConfig(bucket_size=15, refresh_interval=7)},
    )
    mocker.patch(
        "commons.token_bucket.integracao_config_map", {"praxio": TokenBucketConfig(bucket_size=20, refresh_interval=6)}
    )

    integracao = baker.make("rodoviaria.Integracao", name="praxio")
    company = baker.make("rodoviaria.Company", integracao=integracao, company_internal_id=456)
    with mock.patch("commons.token_bucket.integracao_login_map", {}):
        token_bucket = get_token_bucket_robust(company.company_internal_id)
    assert token_bucket.try_get_token()


def test_get_token_bucket_robust_num_queries(mocker, django_assert_num_queries):
    mocker.patch(
        "commons.token_bucket.cliente_config_map",
        {"PartyBusEnterprises": TokenBucketConfig(bucket_size=15, refresh_interval=7)},
    )
    mocker.patch(
        "commons.token_bucket.integracao_config_map", {"praxio": TokenBucketConfig(bucket_size=20, refresh_interval=6)}
    )

    integracao = baker.make("rodoviaria.Integracao", name="praxio")
    company = baker.make("rodoviaria.Company", integracao=integracao, company_internal_id=456)
    baker.make("rodoviaria.PraxioLogin", company=company, cliente="PartyBusEnterprises")

    get_token_bucket_robust(company.company_internal_id)
    # a segunda chamada não vai fazer nenhuma query por causa do memcache
    with django_assert_num_queries(0, connection=connections["rodoviaria"]):
        get_token_bucket_robust(company.company_internal_id)


def test_get_token_bucket_robust_por_modelo_venda(mocker):
    mocker.patch(
        "commons.token_bucket.cliente_config_map",
        {"PartyBusEnterprises": lambda: TokenBucketConfig(bucket_size=15, refresh_interval=7)},
    )
    mocker.patch(
        "commons.token_bucket.integracao_config_map",
        {"praxio": lambda: TokenBucketConfig(bucket_size=20, refresh_interval=6)},
    )

    integracao = baker.make("rodoviaria.Integracao", name="praxio")
    company1 = baker.make(
        "rodoviaria.Company", integracao=integracao, company_internal_id=789, modelo_venda="marketplace"
    )
    company2 = baker.make("rodoviaria.Company", integracao=integracao, company_internal_id=789, modelo_venda="hibrido")
    # TypeError: field password type <class 'django_cryptography.fields.EncryptedCharField'> is not supported by baker
    # baker.make("rodoviaria.PraxioLogin", company=company, cliente="PartyBusEnterprises")
    login1 = PraxioLogin.objects.create(
        company=company1, name="PartyBus", cliente="PartyBusEnterprises1", password="supersecret1"
    )
    login2 = PraxioLogin.objects.create(
        company=company2, name="PartyBus", cliente="PartyBusEnterprises2", password="supersecret2"
    )
    token_bucket = get_token_bucket_robust(789)
    assert token_bucket.name == f"{integracao.name}:{login1.cliente}"

    token_bucket = get_token_bucket_robust(789, "marketplace")
    assert token_bucket.name == f"{integracao.name}:{login1.cliente}"

    token_bucket = get_token_bucket_robust(789, "hibrido")
    assert token_bucket.name == f"{integracao.name}:{login2.cliente}"


def test_checa_integracoes_token_bucket():
    logins = integracao_login_map
    configuradas = integracao_config_map

    for integracao in Integracao.API.values:
        assert integracao in logins and integracao in configuradas


def test_integracoes_lazy():
    configuradas = integracao_config_map

    for integracao in configuradas.values():
        assert callable(integracao)


def test_logins_possuem_cliente():
    loginModels = integracao_login_map.values()
    for loginModel in loginModels:
        assert hasattr(loginModel, "cliente")


def test_constance_altera_valores_token_bucket(override_config):
    config_obj = integracao_config_map[Integracao.API.EULABS]
    config = config_obj()
    # verifica valores padrão
    bucket_size_default = config.bucket_size
    refresh_interval_defatul = config.refresh_interval

    # atribui novos valores
    novos_valores = {
        "BUCKET_SIZE_EULABS": 5,
        "REFRESH_INTERVAL_EULABS": 20,
    }
    with override_config(**novos_valores):
        # verifica se valores foram atualizados
        config = config_obj()
        assert config.bucket_size == 5
        assert config.refresh_interval == 20

    assert config.bucket_size != bucket_size_default
    assert config.refresh_interval != refresh_interval_defatul

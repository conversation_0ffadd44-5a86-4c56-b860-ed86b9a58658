import json
from unittest import mock

from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.models.core import Tre<PERSON><PERSON>lass<PERSON>, TrechoClasseError
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.tests.middleware import request_with_middleware


def test_get_poltronas(rf, praxio_trechoclasses, mock_praxio_get_poltronas, mocker):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = praxio_trechoclasses.ida.grupo
        mock_bloquear_poltronas = mocker.patch.object(PraxioAPI, "bloquear_poltronas")
        request = rf.get(
            "/rodoviaria/v1/get_poltronas",
            data={"trecho_classe_id": praxio_trechoclasses.ida.trechoclasse_internal_id, "num_passageiros": 2},
        )

        response = request_with_middleware(request)

        assert response.status_code == 200
        data = json.loads(response.content)
        assert data == [4, 5]
        mock_bloquear_poltronas.assert_called_once()


def test_get_poltronas_overbooking(rf, praxio_trechoclasses, mock_praxio_poltronas_insuficientes):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = praxio_trechoclasses.ida.grupo
        request = rf.get(
            "/rodoviaria/v1/get_poltronas",
            data={"trecho_classe_id": praxio_trechoclasses.ida.trechoclasse_internal_id, "num_passageiros": 2},
        )

        response = request_with_middleware(request)

        assert response.status_code == 444
        data = json.loads(response.content)
        assert data == {
            "error": "Apenas 1 poltrona disponível para esta viagem",
            "error_type": "overbooking",
            "vagas_disponiveis": 1,
        }


def test_get_poltronas_connection_error(rf, praxio_trechoclasses, mock_praxio_connection_error_antes_pagamento):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = praxio_trechoclasses.ida.grupo
        request = rf.get(
            "/rodoviaria/v1/get_poltronas",
            data={"trecho_classe_id": praxio_trechoclasses.ida.trechoclasse_internal_id, "num_passageiros": 2},
        )

        response = request_with_middleware(request)

        assert response.status_code == 504
        data = json.loads(response.content)
        assert data == {
            "error": "praxio "
            "https://oci-parceiros2.praxioluna.com.br/Autumn/Poltrona/retornaPoltronas "
            "connection error",
            "error_type": "connection_error",
        }


def test_service_not_found(
    rf,
    buser_grupos,
    praxio_company,
    mock_praxio_login,
    praxio_locais,
    mock_buscar_servico_praxio_sem_servico,
    buser_company_expresso,
):
    # adaptação enquanto a factory ainda depende do buser_django
    praxio_company.company_internal_id = buser_company_expresso.id
    praxio_company.save()

    request = rf.get(
        "/rodoviaria/v1/get_poltronas",
        data={"trecho_classe_id": buser_grupos.unlinked_ida.trechoclasse.id, "num_passageiros": 2},
    )

    response = request_with_middleware(request)

    assert response.status_code == 404
    data = json.loads(response.content)
    assert data == {
        "error": f"Serviço não encontrado na API: [{TrechoClasseError.Motivo.SEM_SERVICO}]",
        "error_type": "service_not_found",
    }

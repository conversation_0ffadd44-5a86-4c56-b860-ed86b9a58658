from http import HTTPMethod

import pytest
import responses

import rodoviaria.api.praxio.endpoints as endpoints
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.praxio.base import BaseRequestConfig
from rodoviaria.api.praxio.exceptions import PraxioAPIError
from rodoviaria.service.exceptions import RodoviariaUnauthorizedError

MOCK_ENDPOINT = "teste/testinho"


class MockRequest(BaseRequestConfig):
    method = HTTPMethod.POST

    @property
    def url(self):
        return self.endpoint(MOCK_ENDPOINT)


def test_login_expired_retry_request(praxio_api, requests_mock, mock_login):
    requests_mock.add(
        responses.POST,
        f"{praxio_api.login.company.url_base}/{MOCK_ENDPOINT}",
        json={"IdErro": "SE002"},
    )
    with pytest.raises(RodoviariaUnauthorizedError):
        MockRequest(praxio_api.login).invoke(get_http_executor())

    mock_requests = [r for r in requests_mock.calls if r.request.url == f"{praxio_api.base_url}/{MOCK_ENDPOINT}"]

    assert mock_login.call_count == 2
    assert len(mock_requests) == 1
    # primeiro login
    assert endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url in requests_mock.calls[0][0].url
    assert MOCK_ENDPOINT in requests_mock.calls[1][0].url  # recebe bloqueio de login expirado
    # refaz login
    assert endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url in requests_mock.calls[2][0].url


def test_login_expired_retry_request_erro_sessao_nao_existe(praxio_api, requests_mock, mock_login):
    requests_mock.add(
        responses.POST,
        f"{praxio_api.base_url}/{MOCK_ENDPOINT}",
        json={"IdErro": "SES002"},
    )
    with pytest.raises(RodoviariaUnauthorizedError):
        MockRequest(praxio_api.login).invoke(get_http_executor())

    mock_requests = [r for r in requests_mock.calls if r.request.url == f"{praxio_api.base_url}/{MOCK_ENDPOINT}"]

    assert mock_login.call_count == 1
    assert len(mock_requests) == 1
    # primeiro login
    assert endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url in requests_mock.calls[0][0].url
    assert MOCK_ENDPOINT in requests_mock.calls[1][0].url  # recebe bloqueio de login expirado
    # não refaz login abaixo do tempo de threshold do soft_renew


def test_login_expired_retry_request_erro_desconhecido(praxio_api, requests_mock, mock_login):
    requests_mock.add(
        responses.POST,
        f"{praxio_api.base_url}/{MOCK_ENDPOINT}",
        json={"IdErro": "SE_XTPO"},
    )
    with pytest.raises(RodoviariaUnauthorizedError):
        MockRequest(praxio_api.login).invoke(get_http_executor())

    mock_requests = [r for r in requests_mock.calls if r.request.url == f"{praxio_api.base_url}/{MOCK_ENDPOINT}"]

    assert mock_login.call_count == 1
    assert len(mock_requests) == 1
    # primeiro login
    assert endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url in requests_mock.calls[0][0].url
    assert MOCK_ENDPOINT in requests_mock.calls[1][0].url  # recebe bloqueio de login expirado
    # não refaz login abaixo do tempo de threshold do soft_renew


def test_error_request(praxio_api, requests_mock, mock_login):
    requests_mock.add(
        responses.POST,
        f"{praxio_api.base_url}/{MOCK_ENDPOINT}",
        json={"IdErro": "XPTO", "message": "Erro maneiro"},
    )
    with pytest.raises(PraxioAPIError) as ex:
        MockRequest(praxio_api.login).invoke(get_http_executor())
        assert ex.status_code == 200
        assert ex.message == f"{200} {'Erro maneiro'}"

    mock_requests = [r for r in requests_mock.calls if r.request.url == f"{praxio_api.base_url}/{MOCK_ENDPOINT}"]

    assert mock_login.call_count == 1
    assert len(mock_requests) == 1


def test_request(praxio_api, requests_mock, mock_login):
    requests_mock.add(
        responses.POST,
        f"{praxio_api.base_url}/{MOCK_ENDPOINT}",
        json={},
    )
    response = MockRequest(praxio_api.login).invoke(get_http_executor())
    mock_requests = [r for r in requests_mock.calls if r.request.url == f"{praxio_api.base_url}/{MOCK_ENDPOINT}"]

    assert response.json() == {}
    assert mock_login.call_count == 1
    assert len(mock_requests) == 1

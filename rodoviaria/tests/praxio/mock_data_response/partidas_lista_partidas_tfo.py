itinerario_vazio = {
    "ParametrosEntrada": {},
    "ListaTrechosTFO": None,
    "ListaPartidasTFO": [],
    "Xml": None,
    "IdErro": None,
    "Mensagem": None,
    "MensagemDetalhada": None,
    "Strings": [],
    "Integers": [],
    "Floats": [],
    "VarStr": None,
    "VarInt": 0,
    "VarFloat": 0.0,
    "Sucesso": False,
    "Advertencia": False,
}


itinerario = {
    "ParametrosEntrada": {},
    "ListaTrechosTFO": None,
    "ListaPartidasTFO": [
        {
            "DataBloqSemPassageiro": "0001-01-01T00:00:00",
            "HoraBloqSemPassageiro": None,
            "IDViagem": 1592,
            "DataPartida": "2021-06-05T02:30:00",
            "HoraPartida": "0230",
            "Sentido": 0,
            "Plataforma": "",
            "HoraChegada": None,
            "ControlaPoltrona": 0,
            "ControlaPassageiros": 0,
            "ControlaCliente": 0,
            "Localidade": {
                "ListEstabelecimentos": None,
                "BilheteEmbW2i": 0,
                "IDLocalidade": 176,
                "Descricao": "SAUIPE (BA) (ANP)",
                "Sigla": "SAU",
                "IdRegiao": None,
                "Uf": "BA",
                "IdEstabelecimento": 0,
                "IdCidade": 0,
                "TxEmbIdoso50": 0,
                "TxEmbIdoso100": 0,
                "TxEmbPasseLivre": 0,
                "PedagioIdoso100": 0,
                "PedagioIdoso50": 0,
                "Codigo": 0,
                "AgenciasCargas": None,
                "LastUpdate": None,
                "TxPedagioPasseLivre": None,
                "TxEmbIdosoDef": 0,
                "CodigoSgltar": 0,
            },
            "DataChegada": "0001-01-01T00:00:00",
            "Obs": None,
            "HoraTolerancia": None,
            "DataExibirViagem": "0001-01-01T00:00:00",
            "HoraExibirViagem": None,
            "QtdPoltronaBloqueio": 0,
        },
        {
            "DataBloqSemPassageiro": "0001-01-01T00:00:00",
            "HoraBloqSemPassageiro": None,
            "IDViagem": 1592,
            "DataPartida": "2021-06-05T04:30:00",
            "HoraPartida": "0430",
            "Sentido": 0,
            "Plataforma": "D",
            "HoraChegada": None,
            "ControlaPoltrona": 0,
            "ControlaPassageiros": 0,
            "ControlaCliente": 0,
            "Localidade": {
                "ListEstabelecimentos": None,
                "BilheteEmbW2i": 0,
                "IDLocalidade": 13,
                "Descricao": "FEIRA DE SANTANA (BA)",
                "Sigla": "FEI",
                "IdRegiao": None,
                "Uf": "BA",
                "IdEstabelecimento": 0,
                "IdCidade": 2910800,
                "TxEmbIdoso50": 0,
                "TxEmbIdoso100": 0,
                "TxEmbPasseLivre": 0,
                "PedagioIdoso100": 0,
                "PedagioIdoso50": 0,
                "Codigo": 4941,
                "AgenciasCargas": None,
                "LastUpdate": None,
                "TxPedagioPasseLivre": None,
                "TxEmbIdosoDef": 0,
                "CodigoSgltar": 0,
            },
            "DataChegada": "0001-01-01T00:00:00",
            "Obs": None,
            "HoraTolerancia": None,
            "DataExibirViagem": "0001-01-01T00:00:00",
            "HoraExibirViagem": None,
            "QtdPoltronaBloqueio": 0,
        },
        {
            "DataBloqSemPassageiro": "0001-01-01T00:00:00",
            "HoraBloqSemPassageiro": None,
            "IDViagem": 1592,
            "DataPartida": "2021-06-05T05:50:00",
            "HoraPartida": "0550",
            "Sentido": 0,
            "Plataforma": "",
            "HoraChegada": None,
            "ControlaPoltrona": 0,
            "ControlaPassageiros": 0,
            "ControlaCliente": 0,
            "Localidade": {
                "ListEstabelecimentos": None,
                "BilheteEmbW2i": 0,
                "IDLocalidade": 193,
                "Descricao": "CRUZ DAS ALMAS (BA)",
                "Sigla": "CRU",
                "IdRegiao": None,
                "Uf": "BA",
                "IdEstabelecimento": 0,
                "IdCidade": 2909802,
                "TxEmbIdoso50": 0,
                "TxEmbIdoso100": 0,
                "TxEmbPasseLivre": 0,
                "PedagioIdoso100": 0,
                "PedagioIdoso50": 0,
                "Codigo": 4825,
                "AgenciasCargas": None,
                "LastUpdate": None,
                "TxPedagioPasseLivre": None,
                "TxEmbIdosoDef": 0,
                "CodigoSgltar": 0,
            },
            "DataChegada": "0001-01-01T00:00:00",
            "Obs": None,
            "HoraTolerancia": None,
            "DataExibirViagem": "0001-01-01T00:00:00",
            "HoraExibirViagem": None,
            "QtdPoltronaBloqueio": 0,
        },
        {
            "DataBloqSemPassageiro": "0001-01-01T00:00:00",
            "HoraBloqSemPassageiro": None,
            "IDViagem": 1592,
            "DataPartida": "2021-06-05T06:40:00",
            "HoraPartida": "0640",
            "Sentido": 0,
            "Plataforma": "",
            "HoraChegada": None,
            "ControlaPoltrona": 0,
            "ControlaPassageiros": 0,
            "ControlaCliente": 0,
            "Localidade": {
                "ListEstabelecimentos": None,
                "BilheteEmbW2i": 0,
                "IDLocalidade": 177,
                "Descricao": "SANTO ANTONIO DE JESUS (BA)",
                "Sigla": "SAN",
                "IdRegiao": None,
                "Uf": "BA",
                "IdEstabelecimento": 0,
                "IdCidade": 2928703,
                "TxEmbIdoso50": 0,
                "TxEmbIdoso100": 0,
                "TxEmbPasseLivre": 0,
                "PedagioIdoso100": 0,
                "PedagioIdoso50": 0,
                "Codigo": 4819,
                "AgenciasCargas": None,
                "LastUpdate": None,
                "TxPedagioPasseLivre": None,
                "TxEmbIdosoDef": 0,
                "CodigoSgltar": 0,
            },
            "DataChegada": "0001-01-01T00:00:00",
            "Obs": None,
            "HoraTolerancia": None,
            "DataExibirViagem": "0001-01-01T00:00:00",
            "HoraExibirViagem": None,
            "QtdPoltronaBloqueio": 0,
        },
    ],
    "Xml": None,
    "IdErro": None,
    "Mensagem": None,
    "MensagemDetalhada": None,
    "Strings": [],
    "Integers": [],
    "Floats": [],
    "VarStr": None,
    "VarInt": 0,
    "VarFloat": 0.0,
    "Sucesso": False,
    "Advertencia": False,
}

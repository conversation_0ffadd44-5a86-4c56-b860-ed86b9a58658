from datetime import timed<PERSON><PERSON>
from http import HTTPStatus

import pytest
import responses
from memoize import delete_memoized

from rodoviaria.api.executors import Response
from rodoviaria.api.praxio import endpoints
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.tests.praxio import mocker
from rodoviaria.tests.praxio.mock_data_response import partidas_lista_partidas_tfo, partidas_lista_trechos_viagem


@pytest.fixture
def praxio_api(praxio_login):
    return PraxioAPI(praxio_login.company)


@pytest.fixture
def mock_login(requests_mock, praxio_api):
    yield requests_mock.add(
        responses.POST,
        endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockLogin.response(),
    )
    requests_mock.remove(responses.POST, endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url)


@pytest.fixture
def mock_login_sessao_vazia(requests_mock, praxio_api):
    yield requests_mock.add(
        responses.POST,
        endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockLogin.response_sessao_vazia(),
    )
    requests_mock.remove(responses.POST, endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url)


@pytest.fixture
def mock_unauthorized_login(requests_mock, praxio_api):
    yield requests_mock.add(
        responses.POST,
        endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockUnauthorizedLogin.response(),
    )
    requests_mock.remove(responses.POST, endpoints.EfetuaLoginConfig(praxio_api.login, with_auth=False).url)


@pytest.fixture
def mock_atualiza_origens(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.PartidasOrigensConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockAtualizaOrigens.response(),
    )


@pytest.fixture
def mock_cidades_destino(requests_mock, praxio_api):
    return requests_mock.add(
        responses.POST,
        endpoints.BuscarDestinosConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarDestinos.response(),
    )


@pytest.fixture
def mock_cidades_destino_none_xml(requests_mock, praxio_api):
    return requests_mock.add(
        responses.POST,
        endpoints.BuscarDestinosConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarDestinos.response_none_xml(),
    )


@pytest.fixture
def mock_lista_cidades(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        f"{praxio_api.base_url}/{endpoints.CIDADES_LISTAR}",
        json=mocker.MockCidadesListar.response(),
    )


@pytest.fixture
def mock_desbloqueia_poltrona(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.DesmarcaPoltronaConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockDesbloquearPoltrona.response(),
    )


@pytest.fixture
def mock_tempo_excedido(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.DesmarcaPoltronaConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockDesbloquearPoltrona.response(),
    )
    requests_mock.add(
        responses.POST,
        endpoints.GravaDevolucaoPassagemConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGravaDevolucao.response_tempo_excedido(),
    )


@pytest.fixture
def mock_dev23(requests_mock, http_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.DesmarcaPoltronaConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockDesbloquearPoltrona.response(),
    )
    http_mock.add(
        responses.POST,
        endpoints.GravaDevolucaoPassagemConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGravaDevolucao.response_dev23(),
    )


@pytest.fixture
def mock_voucher_ja_marcado(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.DesmarcaPoltronaConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockDesbloquearPoltrona.response_voucher_ja_marcado(),
    )


@pytest.fixture
def mock_valor_tipo_passageiro_idoso_100(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.ValorTipoPassageiro(praxio_api.login, with_auth=False).url,
        json=mocker.MockValorTipoPassageiro.response_idoso_100(),
    )


@pytest.fixture
def mock_bloqueia_poltrona(http_mock, praxio_api):
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBloquearPoltrona.response(),
    )


@pytest.fixture
def mock_bloqueia_poltrona_com_erro(http_mock, praxio_api):
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBloquearPoltrona.response_erro(),
    )


@pytest.fixture
def mock_cancela(http_mock, praxio_api):
    http_mock.add(
        responses.POST,
        endpoints.GravaDevolucaoPassagemConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGravaDevolucao.response(),
    )


@pytest.fixture
def mock_cancela_venda_bilhete_devolvido(http_mock, praxio_api):
    http_mock.add(
        responses.POST,
        endpoints.GravaDevolucaoPassagemConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGravaDevolucao.response_passagem_ja_devolvida(),
    )


@pytest.fixture
def mock_compra(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_with_bpe_for_two(),
    )


@pytest.fixture
def mock_compra_com_codigo_embarque_curitiba(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_with_bpe_and_codigo_embarque_curitiba(),
    )


@pytest.fixture
def mock_compra_conexao(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_compra_conexao(),
    )


@pytest.fixture
def mock_compra_erro_bpe(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_with_bpe_error(),
    )


@pytest.fixture
def mock_compra_erro_bpe_receita_out_of_service(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_with_bpe_error_receita_out_of_service(),
    )


@pytest.fixture
def mock_compra_erro_bpe_response_vazio(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_with_bpe_error_response_vazio(),
    )


@pytest.fixture
def mock_compra_erro_valor_menor(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_with_bpe_error_valor_menor(),
    )


@pytest.fixture
def mock_compra_voucher_erro_valor_menor(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemVoucherConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_voucher_error_valor_menor(),
    )


@pytest.fixture
def mock_compra_erro_desconto(requests_mock, praxio_api):
    response_erro_desconto = mocker.MockConfirmarVenda.response_com_erro()
    response_erro_desconto["IdErro"] = "VEN071"
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=response_erro_desconto,
    )


@pytest.fixture
def mock_compra_com_erro_vagas_insuficientes(requests_mock, praxio_api):
    response_erro_desconto = mocker.MockConfirmarVenda.response_com_erro()
    response_erro_desconto.update(
        {
            "IdErro": "VEN079",
            "Mensagem": "Que pena, não temos vagas suficientes nesse preço para a sua demanda de passageiros.",
        }
    )
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=response_erro_desconto,
    )


@pytest.fixture
def mock_compra_erro_desconto_generico(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_com_erro_desconto_generico(),
    )


@pytest.fixture
def mock_compra_erro_estabelecimento_fiscal(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_erro_falta_estabelecimento_fiscal(),
    )


@pytest.fixture
def mock_compra_voucher_com_erro(requests_mock, praxio_api):
    response_erro_desconto = mocker.MockConfirmarVenda.response_com_erro()
    response_erro_desconto["IdErro"] = "VEN071"
    response_erro_desconto["Mensagem"] = "Desconto Manual não permitido"
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemVoucherConfig(praxio_api.login, with_auth=False).url,
        json=response_erro_desconto,
    )


@pytest.fixture
def mock_compra_voucher(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemVoucherConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_for_two_voucher(),
    )


@pytest.fixture
def mock_compra_erro_valor_menor_voucher(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemVoucherConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_with_bpe_error_valor_menor(),
    )


@pytest.fixture
def mock_compra_erro_desconto_voucher(requests_mock, praxio_api):
    response_erro_desconto = mocker.MockConfirmarVenda.response_com_erro()
    response_erro_desconto["IdErro"] = "VEN071"
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemVoucherConfig(praxio_api.login, with_auth=False).url,
        json=response_erro_desconto,
    )


@pytest.fixture
def mock_compra_com_erro_voucher(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.VendaPassagemVoucherConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockConfirmarVenda.response_com_erro(),
    )


@pytest.fixture
def mock_trechos_viagens(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarTrechosVendidosConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockTrechosViagem.response(),
    )
    yield requests_mock


@pytest.fixture
def mock_trechos_viagens_sem_trechos(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarTrechosVendidosConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockTrechosViagem.response_sem_trechos(),
    )
    yield requests_mock


@pytest.fixture
def mock_get_poltronas_livres(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGetPoltronasLivres.response(),
    )


@pytest.fixture
def mock_get_poltronas_dois_andares(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGetPoltronasLivres.response_todas_poltronas_indisponiveis_andar_errado(),
    )
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGetPoltronasLivres.response_poltronas_insuficientes(),
    )


@pytest.fixture
def mock_get_poltronas_invalidas(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGetPoltronasLivres.response_todas_poltronas_indisponiveis_andar_errado(),
    )


@pytest.fixture
def mock_poltronas_insuficientes(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGetPoltronasLivres.response_poltronas_insuficientes(),
    )


@pytest.fixture
def mock_get_poltronas_livres_qtd_disponivel_bug(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockGetPoltronasLivres.response_com_bug_qtd_disponivel(),
    )


@pytest.fixture
def mock_buscar_servico(requests_mock, praxio_api):
    yield requests_mock.add(
        responses.POST,
        endpoints.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarServico.response(),
        # match=[
        #     custom_matchers.json_params_keys_matcher(
        #         [
        #             "IdSessaoOp",
        #             "IdEstabelecimentoVenda",
        #             "LocalidadeOrigem",
        #             "LocalidadeDestino",
        #             "DataPartida",
        #             "TempoPartida",
        #             "DescontoAutomatico",
        #             "sugestaoPassagem",
        #         ],
        #         # strict_match=False,
        #     )
        # ],
    )


@pytest.fixture
def mock_buscar_servico_sem_tipo_horario(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarServico.response_sem_tipo_horario(),
    )


@pytest.fixture
def mock_buscar_servico_sem_tipo_horario_ou_tipo_servico(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarServico.response_sem_tipo_horario_ou_tipo_servico(),
    )


@pytest.fixture
def mock_buscar_servico_preco_negativo(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarServico.response_service_negative_price(),
    )


@pytest.fixture
def mock_buscar_servico_unico(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarServico.response_for_only_one_service(),
    )


@pytest.fixture
def mock_nenhum_servico(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarServico.response_nenhum_servico(),
    )


@pytest.fixture
def mock_buscar_servico_com_conexao(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarServico.response_com_conexao(),
    )


@pytest.fixture
def mock_buscar_servico_com_conexao_invalida(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockBuscarServico.response_com_conexao_invalida(),
    )


@pytest.fixture
@responses.activate
def mock_connection_error(praxio_api, requests_mock):
    pass


@pytest.fixture
def mock_listar_viagens(requests_mock, praxio_api, mock_login):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockListarViagens.response(),
    )
    yield requests_mock


@pytest.fixture
def mock_listar_viagens_sem_viagens(requests_mock, praxio_api, mock_login):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockListarViagens.response_sem_viagens(),
    )
    yield requests_mock


@pytest.fixture
def mock_lista_partidas_tfo(requests_mock, praxio_api, mock_lista_partidas_tfo_cache):
    requests_mock.add(
        responses.POST,
        endpoints.ListaPartidasTFOConfig(praxio_api.login, with_auth=False).url,
        json=partidas_lista_partidas_tfo.itinerario,
    )
    yield requests_mock


@pytest.fixture
def mock_lista_partidas_tfo_to_many_requests(requests_mock, praxio_api, mock_lista_partidas_tfo_cache):
    yield requests_mock.add(
        responses.POST,
        endpoints.ListaPartidasTFOConfig(praxio_api.login, with_auth=False).url,
        json={},
        status=429,
    )


@pytest.fixture
def mock_lista_partidas_tfo_sessao_expirada(requests_mock, praxio_api, mock_lista_partidas_tfo_cache):
    requests_mock.add(
        responses.POST,
        endpoints.ListaPartidasTFOConfig(praxio_api.login, with_auth=False).url,
        json={
            "ParametrosEntrada": {},
            "ListaTrechosTFO": None,
            "ListaPartidasTFO": "None",
            "Xml": None,
            "IdErro": "SE002",
            "Mensagem": "Sessão não existe.",
            "MensagemDetalhada": "Sessão não existe.",
            "Strings": [],
            "Integers": [],
            "Floats": [],
            "VarStr": None,
            "VarInt": 0,
            "VarFloat": 0.0,
            "Sucesso": False,
            "Advertencia": False,
        },
    )


@pytest.fixture
def mock_lista_partidas_tfo_vazio(requests_mock, praxio_api, mock_lista_partidas_tfo_cache):
    yield requests_mock.add(
        responses.POST,
        endpoints.ListaPartidasTFOConfig(praxio_api.login, with_auth=False).url,
        json=partidas_lista_partidas_tfo.itinerario_vazio,
    )


@pytest.fixture
def mock_lista_trechos_viagem_vazio(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarTrechosVendidosConfig(praxio_api.login, with_auth=False).url,
        json=partidas_lista_trechos_viagem.trechos_vendidos_vazio,
    )
    yield requests_mock


@pytest.fixture
def mock_lista_trechos_connection_error(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST, endpoints.BuscarTrechosVendidosConfig(praxio_api.login, with_auth=False).url, status=500
    )
    yield requests_mock


@pytest.fixture
def mock_lista_trechos_viagem(requests_mock, praxio_api):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarTrechosVendidosConfig(praxio_api.login, with_auth=False).url,
        json=partidas_lista_trechos_viagem.trechos_vendidos,
    )
    yield requests_mock


@pytest.fixture
def mock_lista_partidas_tfo_cache():
    delete_memoized(endpoints.ListaPartidasTFOConfig.invoke)


@pytest.fixture
def mock_passagem_reimprimir_voucher(requests_mock, praxio_api, mock_login):
    requests_mock.add(
        responses.POST,
        endpoints.ReimprimePassagemConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockRetornadaPassagemReimprimir.response_venda_voucher(),
    )
    yield requests_mock


@pytest.fixture
def mock_passagem_reimprimir_bpe(requests_mock, praxio_api, mock_login):
    requests_mock.add(
        responses.POST,
        endpoints.ReimprimePassagemConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockRetornadaPassagemReimprimir.response_venda_bpe(),
    )
    yield requests_mock


@pytest.fixture
def mock_passagem_reimprimir_passagem_nao_encontrada(requests_mock, praxio_api, mock_login):
    requests_mock.add(
        responses.POST,
        endpoints.ReimprimePassagemConfig(praxio_api.login, with_auth=False).url,
        json=mocker.MockRetornadaPassagemReimprimir.response_passagem_nao_encontrada(),
    )
    yield requests_mock


class MockResponse(Response):
    def __init__(self, json):
        self._json = json

    def json(self):
        return self._json

    @property
    def status_code(self) -> HTTPStatus:
        # Praxio sempre retorna 200 mesmo que seja erro
        return HTTPStatus.OK

    @property
    def elapsed(self) -> timedelta:
        return timedelta(minutes=1)

from unittest import mock

import pytest
import requests

from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.praxio import endpoints
from rodoviaria.api.praxio.exceptions import RodoviariaCompraParcialPraxioException
from rodoviaria.tests.praxio.conftest import MockResponse


def test_comprar_todo_erro_compra_dispara_compra_parcial_exception(praxio_api):
    with mock.patch.object(
        requests.Session,
        "request",
        return_value=MockResponse(
            json={
                "oObj": {
                    "Passagem": {
                        "ListaPassagem": [
                            {
                                "IdErro": "ASDBBA",
                                "Mensagem": "Parabólica",
                            }
                        ]
                    }
                }
            }
        ),
    ), pytest.raises(RodoviariaCompraParcialPraxioException):
        executor = get_http_executor()
        endpoints.VendaPassagemBPEConfig(praxio_api.login, with_auth=False).invoke(executor, json={})

    # json_request = mock_request.call_args[1]["json"]

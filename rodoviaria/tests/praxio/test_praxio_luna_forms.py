import datetime
from decimal import Decimal

import pytest
from pydantic import ValidationError

import rodoviaria.api.praxio.models
from rodoviaria.api.praxio.models import LocalidadeParada


def teste_login_form():
    entrada = {
        "Nome": "str",
        "Senha": "str",
        "Sistema": "str",
        "TipoBD": 1,
        "Empresa": "str",
        "Cliente": "str",
        "TipoAplicacao": 1,
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.LoginForm(**entrada).dict(by_alias=True)
    assert form_dict["Nome"] == "str"
    assert form_dict["Senha"] == "str"
    assert form_dict["Sistema"] == "str"
    assert form_dict["TipoBD"] == 1
    assert form_dict["Empresa"] == "str"
    assert form_dict["Cliente"] == "str"
    assert form_dict["TipoAplicacao"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("Empresa")
        rodoviaria.api.praxio.models.LoginForm(**entrada)


def test_localidade_form():
    entrada = {
        "Descricao": "Sao Paulo (SP)",
        "IDLocalidade": "321",
        "IdCidade": "20",
        "Sigla": "SAO",
        "Uf": "SP",
    }
    localidade = LocalidadeParada.parse_obj(entrada)
    assert localidade.descricao == "Sao Paulo - SP"
    assert localidade.external_local_id == "321"
    assert localidade.external_cidade_id == "321"
    assert localidade.external_local_sigla == "SAO"
    assert localidade.uf == "SP"
    assert localidade.nome_cidade == "Sao Paulo"
    assert localidade.id_cidade_ibge is None


def teste_bloquear_poltrona_form():
    entrada = {
        "IdSessaoOp": "str",
        "IdViagem": 1,
        "IdPoltrona": 15,
        "FusoHorario": "str",
        "IdTipoVeiculo": 1,
        "IdLocOrigem": 1,
        "IdLocDestino": 1,
        "Andar": 1,
        "Bloqueia": 1,
        "VerificarSugestao": 1,
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.BloquearPoltronaForm(**entrada).dict(by_alias=True)
    assert form_dict["IdSessaoOp"] == "str"
    assert form_dict["IdViagem"] == 1
    assert form_dict["IdPoltrona"] == 15
    assert form_dict["FusoHorario"] == "str"
    assert form_dict["IdTipoVeiculo"] == 1
    assert form_dict["IdLocOrigem"] == 1
    assert form_dict["IdLocDestino"] == 1
    assert form_dict["Andar"] == 1
    assert form_dict["Bloqueia"] == 1
    assert form_dict["VerificarSugestao"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("IdViagem")
        rodoviaria.api.praxio.models.BloquearPoltronaForm(**entrada)


def teste_desbloquear_poltrona_form():
    entrada = {
        "IdSessaoOp": "str",
        "IdViagem": 1,
        "IdPoltrona": 15,
        "FusoHorario": "str",
        "IdTipoVeiculo": 1,
        "IdLocOrigem": 1,
        "IdLocDestino": 1,
        "Andar": 1,
        "Bloqueia": 1,
        "VerificarSugestao": 1,
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.DesbloquearPoltronaForm(**entrada).dict(by_alias=True)
    assert form_dict["IdSessaoOp"] == "str"
    assert form_dict["IdViagem"] == 1
    assert form_dict["IdPoltrona"] == 15
    assert form_dict["FusoHorario"] == "str"
    assert form_dict["IdTipoVeiculo"] == 1
    assert form_dict["IdLocOrigem"] == 1
    assert form_dict["IdLocDestino"] == 1
    assert form_dict["Andar"] == 1
    assert form_dict["Bloqueia"] == 1
    assert form_dict["VerificarSugestao"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("IdViagem")
        rodoviaria.api.praxio.models.DesbloquearPoltronaForm(**entrada)


def teste_busca_origem_form():
    entrada = {"IdSessaoOp": "str", "IdEstabelecimento": 1}

    # ok
    form_dict = rodoviaria.api.praxio.models.BuscaOrigemForm(**entrada).dict(by_alias=True)
    assert form_dict["IdSessaoOp"] == "str"
    assert form_dict["IdEstabelecimento"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("IdEstabelecimento")
        rodoviaria.api.praxio.models.BuscaOrigemForm(**entrada)


def teste_pagamento_xml_item():
    entrada = {
        "DataPagamento": datetime.datetime(2021, 3, 25, 17, 5, 38, 715929),
        "TipoPagamento": 0,
        "ValorPagamento": 100,
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.PagamentoXmlItem(**entrada).dict(by_alias=True)
    assert form_dict["DataPagamento"] == datetime.datetime(2021, 3, 25, 17, 5, 38, 715929)
    assert form_dict["TipoPagamento"] == 0
    assert form_dict["ValorPagamento"] == 100

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("DataPagamento")
        rodoviaria.api.praxio.models.PagamentoXmlItem(**entrada)


def teste_passagem_xml_item():
    entrada = {
        "IdEstabelecimento": 1,
        "SerieBloco": "str",
        "NumPassagem": 15,
        "Poltrona": 15,
        "Desconto": 10,
        "DescontoManual": 1,
        "IdDesconto": 0,
        "Pricing": 10.90,
        "IdRota": 0,
        "NomeCli": "str",
        "IdentidadeCli": "str",
        "CpfCnpjCli": "str",
        "Telefone1": "12999999999",
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.PassagemXmlItem(**entrada).dict(by_alias=True)
    assert form_dict["IdEstabelecimento"] == 1
    assert form_dict["SerieBloco"] == "str"
    assert form_dict["NumPassagem"] == 15
    assert form_dict["Poltrona"] == 15
    assert form_dict["Desconto"] == 10
    assert form_dict["DescontoManual"] == 1
    assert form_dict["IdDesconto"] == 0
    assert form_dict["Pricing"] == Decimal("10.90")
    assert form_dict["NomeCli"] == "str"
    assert form_dict["IdentidadeCli"] == "str"
    assert form_dict["CpfCnpjCli"] == "str"
    assert form_dict["Telefone1"] == "12999999999"

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("IdEstabelecimento")
        rodoviaria.api.praxio.models.PassagemXmlItem(**entrada)


def teste_vendas_xml_envio_item():
    entrada = {
        "IdSessaoOp": "str",
        "IdEstabelecimentoVenda": 1,
        "IdViagem": 1,
        "HoraPartida": "str",
        "IdOrigem": 1,
        "IdDestino": 1,
        "Embarque": "str",
        "Seguro": "str",
        "Excesso": "str",
        "IdCaixa": 1,
        "BPe": 1,
        "PassagemXml": [],
        "pagamentoXml": [],
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.VendaXmlEnvioItem(**entrada).dict(by_alias=True)
    assert form_dict["IdSessaoOp"] == "str"
    assert form_dict["IdEstabelecimentoVenda"] == 1
    assert form_dict["IdViagem"] == 1
    assert form_dict["HoraPartida"] == "str"
    assert form_dict["IdOrigem"] == 1
    assert form_dict["IdDestino"] == 1
    assert form_dict["Embarque"] == "str"
    assert form_dict["Seguro"] == "str"
    assert form_dict["Excesso"] == "str"
    assert form_dict["IdCaixa"] == 1
    assert form_dict["BPe"] == 1
    assert form_dict["PassagemXml"] == []
    assert form_dict["pagamentoXml"] == []

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("IdSessaoOp")
        rodoviaria.api.praxio.models.VendaXmlEnvioItem(**entrada)


def teste_confirmar_venda_form():
    entrada = {"listVendasXmlEnvio": []}

    # ok
    form_dict = rodoviaria.api.praxio.models.ConfirmarVendaFormBPE(**entrada).dict(by_alias=True)
    assert form_dict["listVendasXmlEnvio"] == []


def teste_grava_devolucao_voucher_form():
    entrada = {
        "IdSessaoOp": "str",
        "IdEstabelecimento": 201,
        "IdEstabelecimentoDevolucao": 201,
        "ValorVenda": Decimal("10.2"),
        "Passagem": {
            "SerieBloco": "@",
            "NumeroPassagem": 133572,
            "IdEstabelecimento": 201,
            "ValorDevolucao": Decimal("10.2"),
        },
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.GravaDevolucaoForm(**entrada).dict(by_alias=True)
    assert form_dict["IdSessaoOp"] == "str"
    assert form_dict["IdEstabelecimento"] == 201
    assert form_dict["IdEstabelecimentoDevolucao"] == 201
    assert form_dict["ValorVenda"] == Decimal("10.2")
    assert form_dict["Passagem"]["SerieBloco"] == "@"
    assert form_dict["Passagem"]["NumeroPassagem"] == 133572
    assert form_dict["Passagem"]["IdEstabelecimento"] == 201
    assert form_dict["Passagem"]["ValorDevolucao"] == Decimal("10.2")

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada["Passagem"].pop("ValorDevolucao")
        rodoviaria.api.praxio.models.GravaDevolucaoForm(**entrada)


def teste_bilhete_obj():
    entrada = {
        "ID_PARAMETRO": 1,
        "ID_ESTABELECIMENTO": 1,
        "ID_LOCALIDADE": 1,
        "NOME_TARIFA": "str",
        "CODIGO_TARIFA": "str",
        "KM": 1,
        "ORGAO_REGULADOR": "str",
        "DATA_HORA_CARGA": "str",
        "NomeEstab": "str",
        "NomeLoc": "str",
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.BilheteObj(**entrada).dict(by_alias=True)
    assert form_dict["ID_PARAMETRO"] == 1
    assert form_dict["ID_ESTABELECIMENTO"] == 1
    assert form_dict["ID_LOCALIDADE"] == 1
    assert form_dict["NOME_TARIFA"] == "str"
    assert form_dict["CODIGO_TARIFA"] == "str"
    assert form_dict["KM"] == 1
    assert form_dict["ORGAO_REGULADOR"] == "str"
    assert form_dict["DATA_HORA_CARGA"] == "str"
    assert form_dict["NomeEstab"] == "str"
    assert form_dict["NomeLoc"] == "str"

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("ID_PARAMETRO")
        rodoviaria.api.praxio.models.BilheteObj(**entrada)


def teste_buscar_itinerario_corrida_form():
    entrada = {"IdSessaoOp": "str", "IdViagem": 1, "FusoHorario": "str", "IdOrigem": 1}

    # ok
    form_dict = rodoviaria.api.praxio.models.BuscarItinerarioCorridaForm(**entrada).dict(by_alias=True)
    assert form_dict["IdSessaoOp"] == "str"
    assert form_dict["IdViagem"] == 1
    assert form_dict["FusoHorario"] == "str"
    assert form_dict["IdOrigem"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("IdViagem")
        rodoviaria.api.praxio.models.BuscarItinerarioCorridaForm(**entrada)


def teste_get_poltronas_livres_form():
    entrada = {
        "IdSessaoOp": "str",
        "IdViagem": 1,
        "FusoHorario": "str",
        "IdTipoVeiculo": 1,
        "IdLocOrigem": 1,
        "IdLocDestino": 1,
        "Andar": 1,
        "Bloqueia": 1,
        "VerificarSugestao": 1,
    }

    # ok
    form_dict = rodoviaria.api.praxio.models.RetornaPoltronasForm(**entrada).dict(by_alias=True)
    assert form_dict["IdSessaoOp"] == "str"
    assert form_dict["IdViagem"] == 1
    assert form_dict["FusoHorario"] == "str"
    assert form_dict["IdTipoVeiculo"] == 1
    assert form_dict["IdLocOrigem"] == 1
    assert form_dict["IdLocDestino"] == 1
    assert form_dict["Andar"] == 1
    assert form_dict["Bloqueia"] == 1
    assert form_dict["VerificarSugestao"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("IdViagem")
        rodoviaria.api.praxio.models.RetornaPoltronasForm(**entrada)


def teste_buscar_servico_form():
    entrada = {
        "IdSessaoOp": "str",
        "IdEstabelecimentoVenda": 1,
        "LocalidadeOrigem": 1,
        "LocalidadeDestino": 2,
        "DataPartida": datetime.date(2021, 3, 25),
        "TempoPartida": 1,
        "DescontoAutomatico": 1,
        "sugestaoPassagem": 1,
    }
    # ok
    form_dict = rodoviaria.api.praxio.models.BuscarServicoForm(**entrada).dict(by_alias=True)
    assert form_dict["IdSessaoOp"] == "str"
    assert form_dict["IdEstabelecimentoVenda"] == 1
    assert form_dict["LocalidadeOrigem"] == 1
    assert form_dict["LocalidadeDestino"] == 2
    assert form_dict["DataPartida"] == datetime.date(2021, 3, 25)
    assert form_dict["TempoPartida"] == 1
    assert form_dict["DescontoAutomatico"] == 1
    assert form_dict["sugestaoPassagem"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("DataPartida")
        rodoviaria.api.praxio.models.BuscarServicoForm(**entrada)

from commons.memcache import getmc_pymemcache
from rodoviaria.api.praxio.auth import CACHEKEY_PREFIX, PraxioAuth


def test_auth(praxio_api, mock_login):
    auth = PraxioAuth.from_client(praxio_api.login)
    assert (
        auth.id_sessao_op
        == "091C04D80EDB93FA27D1C587C19DF62E8kFZldUbRvOPE/ZhCMm3cEmd1YmdrdSH0zYeVquSlWbzsxG1WHwe94/ME19bb1ck"
    )
    assert auth.id_estabelecimento == 102
    assert auth.serie_bpe == 12


def test_auth_cached_login(praxio_api, mock_login):
    PraxioAuth.from_client(praxio_api.login)
    PraxioAuth.from_client(praxio_api.login)

    assert mock_login.call_count == 1


def test_auth_force_renew(praxio_api, mock_login):
    PraxioAuth.from_client(praxio_api.login)
    PraxioAuth.from_client(praxio_api.login, force_renew=True)

    assert mock_login.call_count == 2


def test_auth_soft_renew_below_threshold(praxio_api, mock_login):
    PraxioAuth.from_client(praxio_api.login)
    PraxioAuth.from_client(praxio_api.login, soft_renew=True)

    assert mock_login.call_count == 1


def test_auth_soft_renew_above_threshold(praxio_api, mock_login, time_machine):
    time_machine.move_to("2022-10-20 10:00:00", tick=False)
    PraxioAuth.from_client(praxio_api.login)
    time_machine.move_to("2022-10-20 10:05:01", tick=False)
    PraxioAuth.from_client(praxio_api.login, soft_renew=True)

    assert mock_login.call_count == 2


def test_auth_ja_tem_antigo_salvo_no_memcache(praxio_api):
    mc = getmc_pymemcache()
    cache_key = f"{CACHEKEY_PREFIX}{praxio_api.company_id}"
    mc.set(
        cache_key,
        {
            "id_sessao_op": "AAA",
            "id_estabelecimento": 666,
            "serie_bpe": 666,
            "new_login": True,
        },
        60,
    )
    resp = PraxioAuth.from_client(praxio_api.login)

    assert resp.id_sessao_op == "AAA"
    assert resp.id_estabelecimento == 666

import json
from datetime import date
from decimal import Decimal
from http import HTTPStatus

import pytest
import responses
from pydantic import parse_obj_as

from rodoviaria.api.executors.http_status import CloudflareHTTPStatus
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.praxio import endpoints, models
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.service.exceptions import RodoviariaConnectionError
from rodoviaria.tests.praxio.mocker import MockListarViagens


def test_buscar_itinerario(requests_mock, mock_lista_partidas_tfo, mock_login, praxio_api: PraxioAPI):
    response = endpoints.ListaPartidasTFOConfig(praxio_api.login).invoke(get_http_executor(), {"IdViagem": 1212})
    parsed = models.ListaPartidasTFO.parse_obj(response.json()["ListaPartidasTFO"])
    assert parsed[0].duracao == 0
    for checkpoint in parsed[1:]:
        assert checkpoint.duracao > 0
    assert len(parsed) == 4
    assert (
        json.loads(mock_lista_partidas_tfo.calls[1][0].body)["IdSessaoOp"]
        == "091C04D80EDB93FA27D1C587C19DF62E8kFZldUbRvOPE/ZhCMm3cEmd1YmdrdSH0zYeVquSlWbzsxG1WHwe94/ME19bb1ck"
    )


def test_buscar_itinerario_connection_error(requests_mock, mock_login, praxio_api: PraxioAPI):
    requests_mock.add(
        responses.POST,
        endpoints.ListaPartidasTFOConfig(praxio_api.login, with_auth=False).url,
        status=HTTPStatus.INTERNAL_SERVER_ERROR,
    )

    with pytest.raises(RodoviariaConnectionError):
        endpoints.ListaPartidasTFOConfig(praxio_api.login).invoke(get_http_executor(), {"IdViagem": 1212})


def test_buscar_trechos_vendidos_config(requests_mock, mock_lista_trechos_viagem, mock_login, praxio_api: PraxioAPI):
    response = endpoints.BuscarTrechosVendidosConfig(praxio_api.login).invoke(get_http_executor(), {"IdViagem": 1212})
    trechos_api = parse_obj_as(list[models.TrechoVendidoModel], response.json()["oObj"])
    assert isinstance(trechos_api[0], models.TrechoVendidoModel)
    assert len(trechos_api) == 3
    assert (
        json.loads(mock_lista_trechos_viagem.calls[1][0].body)["IdSessaoOp"]
        == "091C04D80EDB93FA27D1C587C19DF62E8kFZldUbRvOPE/ZhCMm3cEmd1YmdrdSH0zYeVquSlWbzsxG1WHwe94/ME19bb1ck"
    )
    assert trechos_api[0].preco == Decimal("149.61")


def test_buscar_trechos_vendidos_config_vazio(
    requests_mock, mock_lista_trechos_viagem_vazio, mock_login, praxio_api: PraxioAPI
):
    response = endpoints.BuscarTrechosVendidosConfig(praxio_api.login).invoke(get_http_executor(), {"IdViagem": 1212})
    assert response.json()["oObj"] == []
    assert (
        json.loads(mock_lista_trechos_viagem_vazio.calls[1][0].body)["IdSessaoOp"]
        == "091C04D80EDB93FA27D1C587C19DF62E8kFZldUbRvOPE/ZhCMm3cEmd1YmdrdSH0zYeVquSlWbzsxG1WHwe94/ME19bb1ck"
    )


def test_buscar_trechos_vendidos_connection_error(
    requests_mock, mock_lista_trechos_connection_error, mock_login, praxio_api: PraxioAPI
):
    with pytest.raises(RodoviariaConnectionError):
        endpoints.BuscarTrechosVendidosConfig(praxio_api.login).invoke(get_http_executor(), {"IdViagem": 1212})
    assert (
        json.loads(mock_lista_trechos_connection_error.calls[1][0].body)["IdSessaoOp"]
        == "091C04D80EDB93FA27D1C587C19DF62E8kFZldUbRvOPE/ZhCMm3cEmd1YmdrdSH0zYeVquSlWbzsxG1WHwe94/ME19bb1ck"
    )


def test_buscar_servicos_request(praxio_api, mock_login, requests_mock):
    request_config = endpoints.BuscarServicosConfig(praxio_api.login)
    requests_mock.add(
        request_config.method,
        request_config.url,
        status=CloudflareHTTPStatus.CLOUDFLARE_HANDSHAKE_TIMEOUT,
    )
    requests_mock.add(
        request_config.method,
        request_config.url,
        json=MockListarViagens.response(),
        status=HTTPStatus.OK,
    )
    viagens_por_periodo = endpoints.buscar_servicos_request(praxio_api.login, 1, date(2024, 11, 1), date(2024, 11, 30))
    # espera-se usar a politica de retry para obter a resposta saudável na segunda tentativa
    assert viagens_por_periodo == parse_obj_as(models.ViagensServicos, MockListarViagens.response()["oObj"])

from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from unittest import mock

import pytest
from django.db import connections
from model_bakery import baker

from commons.dateutils import timezone, to_default_tz
from rodoviaria.api.praxio import descobrir_operacao, models
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.models.core import LocalEmbarque, Rota, Rotina, RotinaTrechoVendido, TrechoVendido
from rodoviaria.tests.praxio.mock_data_response import partidas_lista_partidas_tfo, partidas_lista_trechos_viagem


def test_descobrir_operacao(mocker, praxio_api, mock_listar_viagens):
    mock_group = mocker.patch("rodoviaria.api.praxio.descobrir_operacao.chain")
    task = descobrir_operacao.descobrir_operacao(praxio_api.login, praxio_api.company.company_internal_id)
    mock_group.return_value.on_error.return_value.assert_called_once()
    assert task == mock_group.return_value.on_error.return_value
    assert mock_group.call_args[0][0].tasks[0].task == "rodoviaria.api.praxio.descobrir_operacao._buscar_rota_servico"


def test_descobrir_operacao_return_task_object(mocker, praxio_api, mock_listar_viagens):
    mock_group = mocker.patch("rodoviaria.api.praxio.descobrir_operacao.chain")
    task = descobrir_operacao.descobrir_operacao(
        praxio_api.login, praxio_api.company.company_internal_id, return_task_object=True
    )
    mock_group.return_value.on_error.return_value.assert_not_called()
    assert task == mock_group.return_value.on_error.return_value
    assert mock_group.call_args[0][0].tasks[0].task == "rodoviaria.api.praxio.descobrir_operacao._buscar_rota_servico"


def test_descobrir_operacao_inativa_rotas_e_rotinas_empresa(mocker, praxio_api, mock_listar_viagens):
    mocker.patch("rodoviaria.api.praxio.descobrir_operacao.group")
    rota = baker.make(Rota, company=praxio_api.company, ativo=True)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=timezone.now() + timedelta(days=3), ativo=True)
    trecho_vendido = baker.make(TrechoVendido, rota=rota, ativo=True)
    descobrir_operacao.descobrir_operacao(praxio_api.login, praxio_api.company.company_internal_id)
    rota.refresh_from_db()
    assert rota.ativo is False
    rotina.refresh_from_db()
    assert rotina.ativo is False
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is False


@pytest.fixture
def locais_embarque_itinerario(praxio_company):
    itinerario = models.ListaPartidasTFO.parse_obj(partidas_lista_partidas_tfo.itinerario["ListaPartidasTFO"])
    locais = {}
    for n, cp in enumerate(itinerario):
        locais[cp.local.external_local_id] = baker.make(
            LocalEmbarque,
            local_embarque_internal_id=n,
            id_external=cp.local.external_local_id,
            nickname=cp.local.descricao,
            cidade__company=praxio_company,
            cidade__name=cp.local.nome_cidade,
        )
    return locais


def test_buscar_rota_servico_cria_rota_rotina_trecho_vendido(
    django_assert_num_queries,
    praxio_api,
    mock_login,
    mock_lista_trechos_viagem,
    mock_lista_partidas_tfo,
    locais_embarque_itinerario,
):
    itinerario = models.ListaPartidasTFO.parse_obj(partidas_lista_partidas_tfo.itinerario["ListaPartidasTFO"])
    expected_hash = itinerario.hash
    expected_datetime_ida = to_default_tz(itinerario[0].datetime_ida)
    expected_trecho_vendido = partidas_lista_trechos_viagem.trechos_vendidos["oObj"][0]
    with django_assert_num_queries(27, connection=connections["rodoviaria"]):
        descobrir_operacao._buscar_rota_servico(praxio_api.company.id, id_viagem=123123)
    rota = Rota.objects.get(company=praxio_api.company, id_hash=expected_hash)
    rotina = Rotina.objects.get(rota=rota, datetime_ida=expected_datetime_ida)
    trecho_vendido = TrechoVendido.objects.get(
        rota=rota,
        origem__id_external=expected_trecho_vendido["IdOrigem"],
        destino__id_external=expected_trecho_vendido["IdDestino"],
        classe=expected_trecho_vendido["TipoHorario"],
        capacidade_classe=expected_trecho_vendido["Capacidade"],
    )
    assert trecho_vendido.ativo is True
    assert trecho_vendido.preco == Decimal("149.61")
    assert trecho_vendido.tipo_assento.tipo_assento_parceiro == expected_trecho_vendido["TipoHorario"]
    assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=trecho_vendido)


def test_buscar_rota_servico_cria_checkpoints(
    praxio_api,
    mock_login,
    mock_lista_trechos_viagem,
    mock_lista_partidas_tfo,
    locais_embarque_itinerario,
):
    itinerario = models.ListaPartidasTFO.parse_obj(partidas_lista_partidas_tfo.itinerario["ListaPartidasTFO"])
    expected_hash = itinerario.hash
    expected_checkpoints_len = len(itinerario)
    descobrir_operacao._buscar_rota_servico(praxio_api.company.id, id_viagem=123123)
    rota = Rota.objects.get(company=praxio_api.company, id_hash=expected_hash)
    assert rota.itinerario.count() == expected_checkpoints_len


def test_buscar_rota_servico_atualiza_rota_rotina(
    django_assert_num_queries,
    praxio_api,
    mock_login,
    mock_lista_trechos_viagem,
    mock_lista_partidas_tfo,
    locais_embarque_itinerario,
):
    itinerario = models.ListaPartidasTFO.parse_obj(partidas_lista_partidas_tfo.itinerario["ListaPartidasTFO"])
    rota = baker.make(Rota, company=praxio_api.company, id_hash=itinerario.hash, ativo=False)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=to_default_tz(itinerario[0].datetime_ida), ativo=False)
    expected_trecho_vendido = partidas_lista_trechos_viagem.trechos_vendidos["oObj"][0]
    trecho_vendido = baker.make(
        TrechoVendido,
        rota=rota,
        origem__id_external=expected_trecho_vendido["IdOrigem"],
        destino__id_external=expected_trecho_vendido["IdDestino"],
        classe=expected_trecho_vendido["TipoHorario"],
        capacidade_classe=expected_trecho_vendido["Capacidade"],
        ativo=False,
        origem=locais_embarque_itinerario[str(expected_trecho_vendido["IdOrigem"])],
        destino=locais_embarque_itinerario[str(expected_trecho_vendido["IdDestino"])],
    )
    with (
        django_assert_num_queries(22, connection=connections["rodoviaria"]),
        mock.patch("rodoviaria.service.atualiza_operacao_utils.atualiza_trecho_batch") as mock_atualiza_trechos,
    ):
        descobrir_operacao._buscar_rota_servico(praxio_api.company.id, id_viagem=123123)
    rota.refresh_from_db()
    assert rota.ativo is True
    rotina.refresh_from_db()
    assert rotina.ativo is True
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is True
    assert trecho_vendido.tipo_assento.tipo_assento_parceiro == expected_trecho_vendido["TipoHorario"]
    assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=trecho_vendido)
    mock_atualiza_trechos.delay.assert_called_once_with(
        [
            {
                "origem_internal_id": 0,
                "destino_internal_id": 3,
                "classe": "convencional",
                "preco": Decimal("149.61"),
                "datetime_ida": to_default_tz(datetime(2021, 6, 5, 2, 30)),
                "vagas": None,
            },
            {
                "origem_internal_id": 1,
                "destino_internal_id": 3,
                "classe": "convencional",
                "preco": Decimal("160.28"),
                "datetime_ida": to_default_tz(datetime(2021, 6, 5, 4, 30)),
                "vagas": None,
            },
            {
                "origem_internal_id": 2,
                "destino_internal_id": 3,
                "classe": "convencional",
                "preco": Decimal("303.28"),
                "datetime_ida": to_default_tz(datetime(2021, 6, 5, 5, 50)),
                "vagas": None,
            },
        ],
        praxio_api.company.company_internal_id,
    )


def test_buscar_rota_servico_atualiza_rota_rotina_sem_trechos_vendidos(
    django_assert_num_queries,
    praxio_api,
    mock_login,
    mock_lista_trechos_viagem_vazio,
    mock_lista_partidas_tfo,
):
    itinerario = models.ListaPartidasTFO.parse_obj(partidas_lista_partidas_tfo.itinerario["ListaPartidasTFO"])
    rota = baker.make(Rota, company=praxio_api.company, id_hash=itinerario.hash, ativo=False)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=to_default_tz(itinerario[0].datetime_ida), ativo=True)
    with django_assert_num_queries(6, connection=connections["rodoviaria"]):
        descobrir_operacao._buscar_rota_servico(praxio_api.company.id, id_viagem=123123)
    rota.refresh_from_db()
    assert rota.ativo is False
    rotina.refresh_from_db()
    assert rotina.ativo is True  # não altera o status
    assert rota.trechovendido_set.count() == 0


def test_buscar_rota_servico_nao_ativa_rota_e_rotina_sem_trecho_vendido(
    django_assert_num_queries, mocker, praxio_api, mock_lista_partidas_tfo, mock_login, locais_embarque_itinerario
):
    # setup mock
    mocker.patch.object(PraxioAPI, "buscar_trechos_vendidos").return_value = []

    itinerario = models.ListaPartidasTFO.parse_obj(partidas_lista_partidas_tfo.itinerario["ListaPartidasTFO"])
    rota = baker.make(Rota, company=praxio_api.company, id_hash=itinerario.hash, ativo=False)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=to_default_tz(itinerario[0].datetime_ida), ativo=False)
    with django_assert_num_queries(6, connection=connections["rodoviaria"]):
        descobrir_operacao._buscar_rota_servico(praxio_api.company.id, id_viagem=123123)
    rota.refresh_from_db()
    assert rota.ativo is False
    rotina.refresh_from_db()
    assert rotina.ativo is False

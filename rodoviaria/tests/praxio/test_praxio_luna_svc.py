from datetime import datetime, timedelta

import pytest
from django.core.management import call_command
from model_bakery import baker

from commons.dateutils import to_default_tz
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.praxio.models import RotaPraxio
from rodoviaria.service.rota_svc import create_or_update_rota


@pytest.fixture
def orchestrator(mock_login, praxio_login):
    return OrchestrateRodoviaria(praxio_login.company.company_internal_id, praxio_login.company.modelo_venda)


@pytest.fixture
def trecho_classe(praxio_company):
    grupo = baker.make(
        "rodoviaria.Grupo",
        datetime_ida=to_default_tz(datetime.now() + timedelta(hours=12)),
        company_integracao=praxio_company,
    )
    return baker.make("rodoviaria.TrechoClasse", grupo=grupo, external_id="EXTERNALID")


@pytest.fixture
def mock_locais_embarque_lista_partidas_tfo(praxio_company):
    # mock dos locais de embarque do mock_lista_partidas_tfo
    cidade = baker.make("rodoviaria.Cidade", id_external=18852, name="mock", company=praxio_company)
    baker.make("rodoviaria.LocalEmbarque", id_external=13, cidade=cidade)
    baker.make("rodoviaria.LocalEmbarque", id_external=177, cidade=cidade)
    baker.make("rodoviaria.LocalEmbarque", id_external=176, cidade=cidade)
    baker.make("rodoviaria.LocalEmbarque", id_external=193, cidade=cidade)


@pytest.fixture
def rota(orchestrator, trecho_classe, mock_lista_partidas_tfo):
    return orchestrator.fetch_rota(trecho_classe.external_id, trecho_classe.grupo.datetime_ida)


def test_create_or_update_rota(orchestrator, rota, trecho_classe, mock_locais_embarque_lista_partidas_tfo):
    rota_saved_on_db, _ = create_or_update_rota(
        orchestrator,
        trecho_classe.grupo.company_integracao_id,
        rota,
        id_external=232,
    )

    rota_praxio = RotaPraxio.objects.get(pk=rota_saved_on_db.pk)

    assert "SAU-FEI-CRU-SAN" == rota_praxio.parsed_data.sigla


def test_fetch_rota(rota):
    # Salva os dados originais.
    assert rota.cleaned[0]["IDViagem"]

    # rota_praxio = RotaPraxio.objects.get(pk=rota.pk)
    assert "SAU-FEI-CRU-SAN" == rota.parsed.sigla


def test_fetch_rota_vazio(orchestrator, trecho_classe, mock_lista_partidas_tfo_vazio):
    rota = orchestrator.fetch_rota(trecho_classe.external_id, trecho_classe.grupo.datetime_ida)
    assert rota is None


def test_command_fetch_rotas(
    praxio_company,
    trecho_classe,
    mock_login,
    mock_lista_partidas_tfo,
    mock_locais_embarque_lista_partidas_tfo,
):
    praxio_company.features = ["itinerario"]
    praxio_company.save()

    call_command("marketplace_fetch_rotas", company_id=praxio_company.company_internal_id)

    trecho_classe.grupo.refresh_from_db()
    assert trecho_classe.grupo.rota


def test_command_fetch_rotas_sem_feature_nao_define_rota(praxio_login, trecho_classe):
    praxio_company = praxio_login.company
    praxio_company.features.remove("itinerario")
    praxio_company.save()
    call_command("marketplace_fetch_rotas", company_id=praxio_company.company_internal_id)
    trecho_classe.grupo.refresh_from_db()
    assert not trecho_classe.grupo.rota

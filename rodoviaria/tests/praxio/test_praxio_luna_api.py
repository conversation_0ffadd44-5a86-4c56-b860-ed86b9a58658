import json
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal as D
from itertools import cycle
from types import SimpleNamespace
from unittest import mock

import pytest
from model_bakery import baker
from zoneinfo import ZoneInfo

import rodoviaria.api.praxio.endpoints as endpoints
import rodoviaria.tests.praxio.mocker as mocker_praxio
from commons.dateutils import midnight, to_default_tz, to_tz, today_midnight
from rodoviaria import views
from rodoviaria.api.forms import BuscarServicoForm, Localidade
from rodoviaria.api.praxio import api as praxio_api
from rodoviaria.api.praxio import models
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.api.praxio.auth import PraxioAuth
from rodoviaria.api.praxio.exceptions import RodoviariaCompraParcialPraxioException
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import ComprarForm, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.models import core
from rodoviaria.models.core import Company, Passagem
from rodoviaria.service.descobrir_rotas_praxio_async_svc import _busca_itinerario_viagem_circuit
from rodoviaria.service.exceptions import (
    PassengerTicketAlreadyPrintedException,
    PassengerTicketAlreadyReturnedException,
    RodoviariaBaseException,
    RodoviariaBlockingException,
    RodoviariaConnectionError,
    RodoviariaException,
    RodoviariaOverbookingException,
    RodoviariaTooManyRequestsError,
    RodoviariaUnauthorizedError,
)
from rodoviaria.tests.praxio import mocker
from rodoviaria.tests.praxio.conftest import MockResponse
from rodoviaria.tests.utils_testes import _comprar_params


def test_repr(praxio_api, mock_login):
    assert (
        endpoints.EfetuaLoginConfig(praxio_api, with_auth=False).__repr__()
        == f"PraxioAPI_EfetuaLoginConfig_{praxio_api.company.id}_{praxio_api.company.modelo_venda}"
    )


def test_atualiza_origens(mock_login, mock_atualiza_origens, praxio_api):
    resp = praxio_api.atualiza_origens()
    assert isinstance(resp, list)
    assert all(isinstance(o, Localidade) for o in resp)
    assert resp[0].descricao == "Goiania - GO - (ANP)"


def test_cidades_destino(mock_login, mock_cidades_destino, praxio_api):
    mocked_response = mocker.MockBuscarDestinos.response()["Xml"]["NewDataSet"]["Table"]
    resp = praxio_api.cidades_destino(123)
    assert isinstance(resp, list)
    assert resp == [str(r["IdLocalidade"]) for r in mocked_response]


def test_cidades_destino_none_xml(mock_login, mock_cidades_destino_none_xml, praxio_api):
    # https://buser.sentry.io/issues/4875512884/?project=5872905&query=is%3Aunresolved&referrer=issue-stream&statsPeriod=24h&stream_index=0&utc=true
    resp = praxio_api.cidades_destino(123)
    assert isinstance(resp, list)
    assert resp == []


def test_cidades_destino_clear_cache(mock_login, mock_cidades_destino, praxio_api):
    praxio_api.cidades_destino(392412)
    assert mock_cidades_destino.call_count == 1
    # requisições seguintes aproveitam cache
    praxio_api.cidades_destino(392412)
    assert mock_cidades_destino.call_count == 1
    # se limpar cache, faz nova requisição
    praxio_api.cidades_destino(392412, miss_cache=True)
    assert mock_cidades_destino.call_count == 2


def test_verifica_poltrona_uma_poltrona(
    mock_login, mock_get_poltronas_livres, praxio_api, praxio_trechoclasses, mocker
):
    poltronas = [2]
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    mock_bloquear_poltronas = mocker.patch.object(PraxioAPI, "bloquear_poltronas")
    assert (
        praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=1))
        == poltronas
    )
    mock_bloquear_poltronas.assert_called_once()


def test_verifica_poltrona_duas_poltrona_juntas(
    mock_login, mock_get_poltronas_livres, praxio_api, praxio_trechoclasses, mocker
):
    poltronas = [4, 5]
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    mock_bloquear_poltronas = mocker.patch.object(PraxioAPI, "bloquear_poltronas")
    assert (
        praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2))
        == poltronas
    )
    mock_bloquear_poltronas.assert_called_once()


def test_verifica_poltrona_duas_poltrona_juntas_e_uma_separada(
    mock_login, mock_get_poltronas_livres, praxio_api, praxio_trechoclasses, mocker
):
    poltronas = [2, 4, 5]
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    mock_bloquear_poltronas = mocker.patch.object(PraxioAPI, "bloquear_poltronas")
    assert (
        praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=3))
        == poltronas
    )
    mock_bloquear_poltronas.assert_called_once()


def test_verifica_poltrona_overbooking(mock_login, mock_get_poltronas_livres, praxio_api, praxio_trechoclasses):
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    with pytest.raises(
        RodoviariaOverbookingException,
        match=r"Apenas 3 poltronas disponíveis para esta viagem",
    ):
        praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=4))


def test_praxio_verifica_poltrona_cache(
    mock_login, mock_get_poltronas_livres, praxio_api, praxio_trechoclasses, time_machine, mocker
):
    mocker.patch.object(PraxioAPI, "bloquear_poltronas")
    time_machine.move_to(datetime(2024, 8, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    assert praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)) == [
        4,
        5,
    ]
    assert praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=1)) == [2]
    # não vai mais caber 2
    with pytest.raises(RodoviariaOverbookingException):
        praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2))

    # dados em cache expiram após 10 minutos
    time_machine.move_to(datetime(2024, 8, 19, 17, 3, 35, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
    # e agora cabe os 2
    assert praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)) == [
        4,
        5,
    ]


def test_get_map_poltronas_typed(mock_login, mock_get_poltronas_livres, praxio_api, praxio_trechoclasses):
    resp = praxio_api.get_map_poltronas_typed(trechoclasse_id=praxio_trechoclasses.ida.trechoclasse_internal_id)
    assert resp == [
        models.MapaPoltronaItem(numero="01", disponivel=False, categoria_especial=Passagem.CategoriaEspecial.NORMAL),
        models.MapaPoltronaItem(numero="02", disponivel=True, categoria_especial=Passagem.CategoriaEspecial.NORMAL),
        models.MapaPoltronaItem(numero="03", disponivel=False, categoria_especial=Passagem.CategoriaEspecial.NORMAL),
        models.MapaPoltronaItem(numero="04", disponivel=True, categoria_especial=Passagem.CategoriaEspecial.NORMAL),
        models.MapaPoltronaItem(numero="05", disponivel=True, categoria_especial=Passagem.CategoriaEspecial.NORMAL),
    ]


def test_get_map_poltronas_typed_categorias_especiais(mock_login, praxio_api, praxio_trechoclasses):
    with mock.patch.object(
        PraxioAPI,
        "_retorna_poltronas_request",
        return_value=[{"Caption": "02", "Situacao": -7}, {"Caption": "01", "Situacao": -10}],
    ):
        resp = praxio_api.get_map_poltronas_typed(trechoclasse_id=praxio_trechoclasses.ida.trechoclasse_internal_id)
    assert resp == [
        models.MapaPoltronaItem(numero="01", disponivel=True, categoria_especial=Passagem.CategoriaEspecial.IDOSO_100),
        models.MapaPoltronaItem(numero="02", disponivel=True, categoria_especial=Passagem.CategoriaEspecial.JOVEM_100),
    ]


def test_poltrona_disponivel(praxio_api):
    assert praxio_api._poltrona_disponivel(-10, Passagem.CategoriaEspecial.IDOSO_100)
    assert praxio_api._poltrona_disponivel(-4, Passagem.CategoriaEspecial.IDOSO_50)
    assert praxio_api._poltrona_disponivel(-7, Passagem.CategoriaEspecial.JOVEM_100)
    assert praxio_api._poltrona_disponivel(-6, Passagem.CategoriaEspecial.JOVEM_50)
    assert praxio_api._poltrona_disponivel(-2, Passagem.CategoriaEspecial.PCD)

    assert praxio_api._poltrona_disponivel(0, Passagem.CategoriaEspecial.NORMAL)
    assert praxio_api._poltrona_disponivel(4, Passagem.CategoriaEspecial.NORMAL)
    assert praxio_api._poltrona_disponivel(5, Passagem.CategoriaEspecial.NORMAL)

    assert praxio_api._poltrona_disponivel(3, Passagem.CategoriaEspecial.NORMAL) is False


def test_categoria_especial(praxio_api):
    assert praxio_api._categoria_especial(-10) == Passagem.CategoriaEspecial.IDOSO_100
    assert praxio_api._categoria_especial(-4) == Passagem.CategoriaEspecial.IDOSO_50
    assert praxio_api._categoria_especial(-7) == Passagem.CategoriaEspecial.JOVEM_100
    assert praxio_api._categoria_especial(-6) == Passagem.CategoriaEspecial.JOVEM_50
    assert praxio_api._categoria_especial(-2) == Passagem.CategoriaEspecial.PCD

    assert praxio_api._categoria_especial(3) == Passagem.CategoriaEspecial.NORMAL
    assert praxio_api._categoria_especial(0) == Passagem.CategoriaEspecial.NORMAL
    assert praxio_api._categoria_especial(4) == Passagem.CategoriaEspecial.NORMAL
    assert praxio_api._categoria_especial(5) == Passagem.CategoriaEspecial.NORMAL


def test_get_map_poltronas(mock_login, mock_get_poltronas_livres, praxio_api, praxio_trechoclasses):
    resp = praxio_api.get_map_poltronas(trechoclasse_id=praxio_trechoclasses.ida.trechoclasse_internal_id)
    assert resp == {"": "ocupada", "01": "ocupada", "02": "livre", "03": "ocupada", "04": "livre", "05": "livre"}


def test_get_map_poltronas_segundo_andar(mock_login, mock_get_poltronas_dois_andares, praxio_api, praxio_trechoclasses):
    resp = praxio_api.get_map_poltronas(trechoclasse_id=praxio_trechoclasses.ida.trechoclasse_internal_id)
    assert resp == {"01": "ocupada", "02": "livre"}


def test_get_map_poltronas_dois_andares_invalidos(
    mock_login, requests_mock, mock_get_poltronas_invalidas, praxio_api, praxio_trechoclasses
):
    resp = praxio_api.get_map_poltronas(trechoclasse_id=praxio_trechoclasses.ida.trechoclasse_internal_id)
    assert resp == {"": "ocupada"}
    requests_mock.assert_call_count(endpoints.RetornaPoltronasConfig(praxio_api.login, with_auth=False).url, 2)


def test_connection_error(mock_login, mock_connection_error, praxio_api, praxio_trechoclasses):
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    with pytest.raises(
        RodoviariaConnectionError,
        match=r"praxio https://oci-parceiros2.praxioluna.com.br/Autumn/Poltrona/retornaPoltronas connection error",
    ):
        praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=4))


def test_get_poltronas_livres_poltronas_insuficientes_qtd_disponivel_bug(
    mock_login, mock_get_poltronas_livres_qtd_disponivel_bug, praxio_api, praxio_trechoclasses
):
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    with pytest.raises(RodoviariaOverbookingException) as exc:
        praxio_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=1))
    assert "Não há mais poltronas disponíveis para esta viagem" in exc.value.message


def test_cancela_venda_sem_passagem(mock_login, praxio_trechoclasses, praxio_api):
    d = CancelaVendaForm(trechoclasse_id=praxio_trechoclasses.ida.trechoclasse_internal_id, travel_id=1)
    resp = praxio_api.cancela_venda(d)
    assert resp == {}


def test_cancela_venda_passagem_impressa(mock_login, praxio_trechoclasses, praxio_api):
    d = CancelaVendaForm(trechoclasse_id=praxio_trechoclasses.ida.trechoclasse_internal_id, travel_id=1)
    with mock.patch.object(PraxioAPI, "_cancela_dict_to_cancela_params") as mock_params, mock.patch.object(
        PraxioAPI, "_desbloquear_poltrona"
    ) as mock_desbloquear:
        mock_params.return_value = {
            "passagens_cadastradas": "",
            "desbloquear_poltrona_params": "",
        }
        mock_desbloquear.side_effect = PassengerTicketAlreadyPrintedException("Passagem já impressa")
        with pytest.raises(
            PassengerTicketAlreadyPrintedException,
            match=r"Passagem já impressa",
        ):
            praxio_api.cancela_venda(d)


def test_cancela_venda_bilhete_ja_cancelado(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_cancela_venda_bilhete_devolvido,
    mock_praxio_passagem_confirmada,
):
    trechoclasse_internal_id = mock_praxio_passagem_confirmada.trechoclasse_integracao.trechoclasse_internal_id
    travel_id = mock_praxio_passagem_confirmada.travel_internal_id
    d = CancelaVendaForm(trechoclasse_id=trechoclasse_internal_id, travel_id=travel_id, pax_valido=False)
    with mock.patch.object(PraxioAPI, "_desbloquear_poltrona"), pytest.raises(PassengerTicketAlreadyReturnedException):
        praxio_api.cancela_venda(d)

    mock_praxio_passagem_confirmada.refresh_from_db()
    assert mock_praxio_passagem_confirmada.status == Passagem.Status.CONFIRMADA
    assert mock_praxio_passagem_confirmada.erro_cancelamento == "Passagem devolvida."


def test_reserva_dict_to_comprar_params_pricing(mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api):
    trecho_classe = praxio_trechoclasses.ida
    trechos = [
        models.TrechoCompra(
            pricing=D("10.00"),
            desconto=D("0"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    trecho_classe.external_datetime_ida = datetime(1990, 1, 1, 8, 7)
    trecho_classe.save()
    params = _get_reservar_params("123456", praxio_grupos_mockado, praxio_trechoclasses)
    confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
    pass_xml = confirmar_venda_params["listVendasXmlEnvio"][0]["PassagemXml"][0]
    assert pass_xml["Pricing"] == D("10.00")


def test_reserva_dict_to_comprar_params_tipo_pagamento(
    mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api, override_config
):
    trecho_classe = praxio_trechoclasses.ida
    trechos = [
        models.TrechoCompra(
            pricing=D("10.00"),
            desconto=D("0"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    trecho_classe.external_datetime_ida = datetime(1990, 1, 1, 8, 7)
    trecho_classe.save()
    params = _get_reservar_params("123456", praxio_grupos_mockado, praxio_trechoclasses)
    confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
    pag_xml = confirmar_venda_params["listVendasXmlEnvio"][0]["pagamentoXml"][0]
    assert pag_xml["TipoPagamento"] == 0

    with override_config(PRAXIO_TIPO_PAGAMENTO_POR_EMPRESA=json.dumps({praxio_api.company_id: 7})):
        confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
        pag_xml = confirmar_venda_params["listVendasXmlEnvio"][0]["pagamentoXml"][0]
        assert pag_xml["TipoPagamento"] == 7


def test_reserva_dict_to_comprar_params_external_datetime_ida(
    mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api
):
    trecho_classe = praxio_trechoclasses.ida
    trechos = [
        models.TrechoCompra(
            pricing=D("0"),
            desconto=D("0"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    trecho_classe.external_datetime_ida = datetime(1990, 1, 1, 8, 7)
    trecho_classe.save()
    params = _get_reservar_params("123456", praxio_grupos_mockado, praxio_trechoclasses)
    confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
    hora_partida = confirmar_venda_params["listVendasXmlEnvio"][0]["HoraPartida"]
    assert hora_partida == trecho_classe.external_datetime_ida.strftime("%H%M")


def test_reserva_dict_to_comprar_params_invalid_rg(mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api):
    certidao_nasc = "19980101552020174588502837666681"
    params = _get_reservar_params(certidao_nasc, praxio_grupos_mockado, praxio_trechoclasses)
    trecho_classe = praxio_trechoclasses.ida
    trechos = [
        models.TrechoCompra(
            pricing=D("0"),
            desconto=D("0"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
    pass_xml = confirmar_venda_params["listVendasXmlEnvio"][0]["PassagemXml"][0]
    rg = pass_xml["IdentidadeCli"]
    assert rg == certidao_nasc[:20]


def test_reserva_dict_to_comprar_params_float_value(
    mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api
):
    trecho_classe = praxio_trechoclasses.ida
    trechos = [
        models.TrechoCompra(
            pricing=D("0"),
            desconto=D("10"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    certidao_nasc = "19980101552020174588502837666681"
    params = _get_reservar_params(certidao_nasc, praxio_grupos_mockado, praxio_trechoclasses)
    params.valor_cheio = 91.2
    confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
    pass_xml = confirmar_venda_params["listVendasXmlEnvio"][0]["PassagemXml"][0]
    rg = pass_xml["IdentidadeCli"]
    assert rg == certidao_nasc[:20]


def test_reserva_dict_to_comprar_params_com_desconto_manual(
    mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api
):
    trecho_classe = praxio_trechoclasses.ida
    trechos = [
        models.TrechoCompra(
            pricing=D("0"),
            desconto=D("10"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    rg_number = "123456789"
    params = _get_reservar_params(rg_number, praxio_grupos_mockado, praxio_trechoclasses)
    desconto = praxio_trechoclasses.ida.desconto
    id_desconto = praxio_trechoclasses.ida.external_id_desconto
    params.valor_cheio -= desconto
    confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
    pass_xml = confirmar_venda_params["listVendasXmlEnvio"][0]["PassagemXml"][0]
    assert pass_xml["Desconto"] == D("10")
    assert pass_xml["DescontoManual"] == 1
    assert pass_xml["IdDesconto"] == id_desconto
    assert pass_xml["Pricing"] == D("0.00")


def test_reserva_dict_to_comprar_params_com_desconto_automatico(
    mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api
):
    trecho_classe = praxio_trechoclasses.ida
    trecho_classe.external_id_desconto = 1
    trecho_classe.save()
    trecho_classe = praxio_trechoclasses.ida
    trecho_classe.external_id_desconto = 1
    trecho_classe.save()
    praxio_api.login.desconto_manual = False
    praxio_api.login.save()
    id_desconto = trecho_classe.external_id_desconto
    rg_number = "123456789"
    trechos = [
        models.TrechoCompra(
            pricing=D("0"),
            desconto=D("10"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    params = _get_reservar_params(rg_number, praxio_grupos_mockado, praxio_trechoclasses)
    confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)

    pass_xml = confirmar_venda_params["listVendasXmlEnvio"][0]["PassagemXml"][0]
    assert pass_xml["Desconto"] == D("10")
    assert pass_xml["DescontoManual"] == 0
    assert pass_xml["IdDesconto"] == id_desconto


def test_reserva_dict_to_comprar_params_categoria_especial_idoso(
    mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api
):
    trecho_classe = praxio_trechoclasses.ida
    trechos = [
        models.TrechoCompra(
            pricing=D("0"),
            desconto=D("0"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    trecho_classe.external_datetime_ida = datetime(1990, 1, 1, 8, 7)
    trecho_classe.save()

    with mock.patch.object(
        endpoints.ValorTipoPassageiro,
        "invoke",
        return_value=MockResponse(json={"Valor": 0.00}),
    ) as mock_request_valor_pgto:
        params = _get_reservar_params(
            "123456",
            praxio_grupos_mockado,
            praxio_trechoclasses,
            Passagem.CategoriaEspecial.IDOSO_100,
        )
        confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
    resp = confirmar_venda_params["listVendasXmlEnvio"][0]
    hora_partida = resp["HoraPartida"]
    assert hora_partida == trecho_classe.external_datetime_ida.strftime("%H%M")

    valor_pgto = resp["pagamentoXml"][0]["ValorPagamento"]
    assert valor_pgto == D("0.00")
    json_request = mock_request_valor_pgto.call_args[1]["json"]
    assert json_request["TipoPassageiro"] == 4  # idoso 100%


def test_reserva_dict_to_comprar_params_categoria_especial_jovem(
    mock_login, praxio_grupos_mockado, praxio_trechoclasses, praxio_api
):
    trecho_classe = praxio_trechoclasses.ida
    trechos = [
        models.TrechoCompra(
            pricing=D("0"),
            desconto=D("0"),
            id_rota=0,
            origem=trecho_classe.origem,
            destino=trecho_classe.destino,
            preco=trecho_classe.preco_rodoviaria,
        )
    ]
    trecho_classe.external_datetime_ida = datetime(1990, 1, 1, 8, 7)
    trecho_classe.save()

    with mock.patch.object(
        endpoints.ValorTipoPassageiro,
        "invoke",
        return_value=MockResponse(json={"Valor": 12.00}),
    ) as mock_request_valor_pgto:
        params = _get_reservar_params(
            "123456",
            praxio_grupos_mockado,
            praxio_trechoclasses,
            Passagem.CategoriaEspecial.JOVEM_50,
        )
        confirmar_venda_params = praxio_api._comprar_params(params, trecho_classe, trechos)
    resp = confirmar_venda_params["listVendasXmlEnvio"][0]
    hora_partida = resp["HoraPartida"]
    assert hora_partida == trecho_classe.external_datetime_ida.strftime("%H%M")

    valor_pgto = resp["pagamentoXml"][0]["ValorPagamento"]
    assert valor_pgto == D("12.00")
    json_request = mock_request_valor_pgto.call_args[1]["json"]
    assert json_request["TipoPassageiro"] == 10  # jovem 50%


def test_get_tipo_passageiro_from_categoria_especial(praxio_api):
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.IDOSO_100) == 4
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.IDOSO_50) == 5
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.PCD) == 6
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.JOVEM_100) == 11
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.JOVEM_50) == 10
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.ESPACO_MULHER) == 18
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.ESPACO_PET) == 13
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.CRIANCA) == 1
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.NORMAL) is None
    assert praxio_api._get_tipo_passageiro_from_categoria_especial(Passagem.CategoriaEspecial.IGNORADO) is None


def test_compra_block_desconto_manual(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_erro_desconto,
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    with pytest.raises(RodoviariaBlockingException, match="Desconto Manual não permitido"):
        praxio_api.comprar(params)


def test_buscar_servico(mock_login, mock_buscar_servico, praxio_api, praxio_grupos_mockado):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-18 22:50", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semi leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    servico = resp.servicos[0]
    assert isinstance(resp, BuscarServicoForm)
    assert resp.found
    assert servico.provider_data["ViagemTFO"]["QtdPoltronas"] == 46
    assert servico.provider_data["ViagemTFO"]["TipoHorario"] == "SEMILEITO"
    assert servico.external_datetime_ida == to_default_tz(datetime.strptime("2021-05-18 23:00", "%Y-%m-%d %H:%M"))
    assert servico.external_id == "1405"
    assert servico.tipo_veiculo == 4
    assert servico.preco == D(str(176.18))
    assert servico.vagas == 1
    assert servico.desconto == D(str(18.82))
    assert servico.id_desconto == 0
    assert servico.classe == "SEMILEITO"


def test_buscar_servico_sem_tipo_horario(
    mock_login, mock_buscar_servico_sem_tipo_horario, praxio_api, praxio_grupos_mockado
):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2022-11-25 20:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "executivo"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found
    servico = resp.servicos[0]
    assert servico.provider_data["ViagemTFO"]["QtdPoltronas"] == 44
    assert servico.provider_data["ViagemTFO"]["DataHoraInicio"][-5:] == "20:00"
    assert servico.provider_data["ViagemTFO"]["TipoHorario"] == ""
    assert servico.provider_data["ViagemTFO"]["TipoServico"] == 6
    assert servico.external_id == "3924"
    assert servico.tipo_veiculo == 16
    assert servico.preco == D(str(27.48))
    assert servico.vagas == 0
    assert servico.desconto == D(str(0.0))
    assert servico.id_desconto == 0


def test_buscar_servico_sem_tipo_horario_ou_tipo_servico(
    mock_login,
    mock_buscar_servico_sem_tipo_horario_ou_tipo_servico,
    praxio_api,
    praxio_grupos_mockado,
):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2022-11-25 20:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "executivo"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found
    servico = resp.servicos[0]
    assert servico.provider_data["ViagemTFO"]["QtdPoltronas"] == 44
    assert servico.provider_data["ViagemTFO"]["DataHoraInicio"][-5:] == "20:00"
    assert servico.provider_data["ViagemTFO"]["TipoHorario"] == ""
    assert servico.provider_data["ViagemTFO"]["TipoServico"] is None
    assert servico.external_id == "3924"
    assert servico.tipo_veiculo == 16
    assert servico.preco == D(str(27.48))
    assert servico.vagas == 0
    assert servico.desconto == D(str(0.0))
    assert servico.id_desconto == 0


def test_buscar_servico_com_conexao(mock_login, mock_buscar_servico_com_conexao, praxio_api, praxio_grupos_mockado):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2024-09-13 21:20", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semi leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found
    servico = resp.servicos[0]
    assert servico.provider_data["ViagemTFO"]["QtdPoltronas"] == 40
    assert servico.provider_data["ViagemTFO"]["DataHoraInicio"][-5:] == "21:30"
    assert servico.external_id == "119882"
    assert servico.tipo_veiculo == 383
    assert servico.preco == D(str(549.86))
    assert servico.vagas == 39
    assert servico.desconto == D(str(33.0))
    assert servico.id_desconto == 0


def test_buscar_servico_com_conexao_invalida(
    mock_login,
    mock_buscar_servico_com_conexao_invalida,
    praxio_api,
    praxio_grupos_mockado,
):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2022-11-02 08:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "executivo"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert not resp.found
    assert len(resp.servicos) == 1


def test_buscar_servico_preco_negativo(
    mock_login, mock_buscar_servico_preco_negativo, praxio_api, praxio_grupos_mockado
):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-18 23:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "leito individual"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found
    servico = resp.servicos[0]
    assert servico.provider_data["ViagemTFO"]["QtdPoltronas"] == 4
    assert servico.provider_data["ViagemTFO"]["DataHoraInicio"][-5:] == "23:00"
    assert servico.external_id == "552"
    assert servico.tipo_veiculo == 6
    assert servico.preco == D(str(225))
    assert servico.vagas == 1


def test_buscar_servico_nenhum_servico(mock_login, mock_nenhum_servico, praxio_api, praxio_grupos_mockado):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-18 22:50", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semi leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is False
    assert resp.servicos == []


def test_buscar_servico_horario_fora_da_tolerancia(mock_login, mock_buscar_servico, praxio_api, praxio_grupos_mockado):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-18 21:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "semi leito"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is False
    assert len(resp.servicos) == 5


def test_buscar_servico_tipo_assento_incorreto(mock_login, mock_buscar_servico, praxio_api, praxio_grupos_mockado):
    trecho_classe = praxio_grupos_mockado.ida.trechoclasse
    trecho_classe.datetime_ida = to_default_tz(datetime.strptime("2021-05-18 23:00", "%Y-%m-%d %H:%M"))

    grupo_classe = trecho_classe.grupo_classe
    grupo_classe.tipo_assento = "cama"
    match_params = {
        "datetime_ida": trecho_classe.datetime_ida,
        "timezone": trecho_classe.trecho_vendido.origem.cidade.timezone,
        "tipo_assento": trecho_classe.grupo_classe.tipo_assento,
    }
    resp = praxio_api.buscar_corridas(mocker.MockBuscarServico.request(), match_params)

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is False
    assert len(resp.servicos) == 5


def test_buscar_servico_retorno_mismatch(praxio_api):
    timezone = "America/Sao_Paulo"
    servico = {
        "IdViagem": "2131",
        "TxEmbarque": "1.50",
        "Seguro": "3",
        "Pedagio": "5.50",
        "VlTarifa": "90",
        "Desconto": "5",
        "Pricing": "10",
        "ViagemTFO": {
            "DataHoraInicio": "2023-01-20T10:15",
            "TipoHorario": "semi leito",
            "PoltronasDisponiveis": 13,
        },
    }
    retorno = praxio_api._parse_retorno_buscar_servico([], timezone, [servico])
    assert retorno == BuscarServicoForm.parse_obj(
        {
            "found": False,
            "servicos": [
                {
                    "external_id": servico["IdViagem"],
                    "preco": D("85"),
                    "external_datetime_ida": to_tz(
                        datetime.strptime(servico["ViagemTFO"]["DataHoraInicio"], "%Y-%m-%dT%H:%M"),
                        timezone,
                    ),
                    "classe": servico["ViagemTFO"]["TipoHorario"],
                    "external_company_id": str(praxio_api.company.company_external_id),
                    "vagas": servico["ViagemTFO"]["PoltronasDisponiveis"],
                    "provider_data": servico,
                }
            ],
        }
    )


def test_itinerario(mock_login, mock_lista_partidas_tfo, praxio_api):
    response = praxio_api.itinerario(external_id=10, datetime_ida=None)
    parsed_response = response.parsed
    assert 4 == len(parsed_response)
    second_checkpoint = parsed_response[1]
    second_local = second_checkpoint.local
    assert second_checkpoint.tempo_embarque == 600
    assert second_checkpoint.duracao == 6600
    assert second_checkpoint.distancia == 110
    assert second_checkpoint.plataforma == "D"
    assert second_checkpoint.datetime_ida == datetime(2021, 6, 5, 4, 30)
    assert second_local.descricao == "Feira De Santana - BA"
    assert second_local.external_cidade_id == "13"
    assert second_local.external_local_id == "13"
    assert second_local.external_local_sigla == "FEI"
    assert second_local.nome_cidade == "Feira De Santana"
    assert second_local.uf == "BA"


def test_itinerario_sessao_expirada(mock_login, mock_lista_partidas_tfo_sessao_expirada, praxio_api):
    with pytest.raises(RodoviariaUnauthorizedError) as e:
        praxio_api.itinerario(external_id=10, datetime_ida=None)
        assert "Sessão Expirada" in str(e)


def test_itinerario_to_many_requests(mock_lista_partidas_tfo_to_many_requests, praxio_api, mock_login):
    with pytest.raises(RodoviariaTooManyRequestsError):
        praxio_api.itinerario(external_id=10, datetime_ida=None)


def test_cancela_voucher_ja_marcado(
    rf, praxio_company, mock_praxio_login, praxio_trechoclasses, mock_voucher_ja_marcado
):
    passagem = baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=999,
        buseiro_internal_id=999,
        status="confirmada",
        trechoclasse_integracao=praxio_trechoclasses.ida,
        poltrona_external_id=1,
        company_integracao=praxio_company,
    )
    request = rf.get("/rodoviaria/compra/cancela")
    response = views.efetua_cancelamento(
        request,
        travel_id=passagem.travel_internal_id,
        buseiro_id=passagem.buseiro_internal_id,
    )
    assert response.status_code == 403
    data = json.loads(response.content)
    assert data == {
        "error": "Passagem já foi impressa",
        "error_type": "passenger_ticket_already_printed",
    }
    passagem.refresh_from_db()
    assert passagem.erro_cancelamento == "Passagem já foi impressa"
    assert passagem.datetime_cancelamento is not None


def test_comprar_nao_passa_tipo_passageiro_compra_idoso_100(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_valor_tipo_passageiro_idoso_100,
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(
        trechoclasse_id, quantidade_passageiros=2, categoria_especial=Passagem.CategoriaEspecial.IDOSO_100
    )
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with mock.patch.object(PraxioAPI, "_deve_pegar_valor_categoria_especial", return_value=True), mock.patch.object(
        endpoints.VendaPassagemBPEConfig,
        "invoke",
        return_value=MockResponse(json=mocker.MockConfirmarVenda.response_with_bpe_for_two()),
    ) as mock_request_compra_bpe:
        praxio_api.comprar(params)

    json_request = mock_request_compra_bpe.call_args[1]["json"]
    assert json_request["listVendasXmlEnvio"][0]["PassagemXml"][0]["TipoPassageiro"] == "4"  # idoso_100
    assert json_request["listVendasXmlEnvio"][0]["PassagemXml"][1]["TipoPassageiro"] == "4"  # idoso_100


def test_comprar_nao_passa_tipo_passageiro_compra_normal(
    mock_login, praxio_api, praxio_trechoclasses, mock_bloqueia_poltrona
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    with mock.patch.object(
        endpoints.VendaPassagemBPEConfig,
        "invoke",
        return_value=MockResponse(json=mocker.MockConfirmarVenda.response_with_bpe_for_two()),
    ) as mock_request_compra_bpe:
        praxio_api.comprar(params)

    json_request = mock_request_compra_bpe.call_args[1]["json"]
    assert "TipoPassageiro" not in json_request["listVendasXmlEnvio"][0]["PassagemXml"][0]
    assert "TipoPassageiro" not in json_request["listVendasXmlEnvio"][0]["PassagemXml"][1]


def test_comprar(mock_login, praxio_api, praxio_trechoclasses, mock_bloqueia_poltrona, mock_compra):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "poltrona_external_id"
    )
    assert len(passagens) == 2
    assert passagens[0].preco_rodoviaria == praxio_trechoclasses.ida.preco_rodoviaria
    assert passagens[1].preco_rodoviaria == praxio_trechoclasses.ida.preco_rodoviaria
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert not passagem.erro
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.numero_passagem == "5"
    assert passagem.numero_bpe == "5"
    assert passagem.id_estabelecimento_external == "668"
    assert passagem.serie_bpe == "222"
    assert passagem.bpe_monitriip_code == (
        "3324055240632900139463064000156532200156533200015653208-9440-"
        "31202406031200000100000158000614000000000000000000002000001"
    )
    assert passagem.bpe_qrcode
    assert passagem.protocolo_autorizacao
    assert passagem.chave_bpe
    assert passagem.plataforma
    assert passagem.numero_bpe
    assert passagem.linha == "LUZILANDIA - BRASILIA / CONEXAO CANTO DO BURITI"
    assert passagem.numero_bilhete_embarque == "03483897598100"
    assert passagem.tipo_taxa_embarque == Passagem.TipoTaxaEmbarque.QRCODE


def test_comprar_com_codigo_embarque_curitiba(
    mock_login, praxio_api, praxio_trechoclasses, mock_bloqueia_poltrona, mock_compra_com_codigo_embarque_curitiba
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=1)
    praxio_api.comprar(params)
    passagem = Passagem.objects.get(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert passagem.numero_bilhete_embarque == "*********"
    assert passagem.tipo_taxa_embarque == Passagem.TipoTaxaEmbarque.QRCODE


def test_comprar_com_conexao(mock_login, praxio_api, mock_bloqueia_poltrona, mock_compra_conexao):
    cidades = baker.make(
        core.Cidade,
        name=cycle(["Goiania", "Conceicao dos Alagoas", "Sao Paulo"]),
        company=praxio_api.company,
        _quantity=3,
    )
    locais = baker.make(
        core.LocalEmbarque,
        nickname=cycle(["rodoviaria_goiania", "rodoviaria_conceicao_dos_alagoas", "rodoviaria_sao_paulo"]),
        cidade=cycle(cidades),
        id_external=cycle([270, 3081, 3087]),
        _quantity=3,
    )
    goiania, c_dos_alagoas, sao_paulo = locais
    provider_data = json.dumps(
        mocker_praxio.MockBuscarServico.response_com_conexao(
            goiania.id_external, c_dos_alagoas.id_external, sao_paulo.id_external
        )["ListaPartidas"][0]
    )
    trecho_classe = baker.make(
        core.TrechoClasse,
        trechoclasse_internal_id=8888,
        provider_data=provider_data,
        grupo=baker.make(core.Grupo),
        external_id=119882,
        origem=goiania,
        destino=sao_paulo,
        datetime_ida=datetime(2024, 8, 28, 21, 10),
        preco_rodoviaria=D("181.55"),
        external_id_tipo_veiculo=2,
        external_id_desconto=123,
        desconto=10,
    )
    trechoclasse_internal_id = trecho_classe.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_internal_id, quantidade_passageiros=2, valor_cheio=D("181.55"))
    assert not Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_internal_id
    ).exists()
    praxio_api.comprar(params)
    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_internal_id
    ).order_by("poltrona_external_id")
    # 2 pax em trecho com 1 conexao devem emitir 4 passagens
    assert len(passagens) == 4
    # Todas as passagens foram confirmadas
    assert all(passagem.status == Passagem.Status.CONFIRMADA for passagem in passagens)
    # Passagens emitidas sem divergência
    assert all(passagem.valor_cheio == passagem.preco_rodoviaria for passagem in passagens)

    # valida atributos por trecho
    passagens_primeira_perna = passagens.filter(origem=goiania, destino=c_dos_alagoas)
    passagens_segunda_perna = passagens.filter(origem=c_dos_alagoas, destino=sao_paulo)
    assert len(passagens_primeira_perna) == len(passagens_segunda_perna) == 2
    # a primeira e segunda pernas foram emitidas com os valores corretos
    # valor passagens primeira perna => D("199.00") + D("6.00") - D("33.00") == D("172.55")
    assert list(passagens_primeira_perna.values_list("preco_rodoviaria", flat=True)) == [D("172.55")] * 2
    # valor passagens segunda perna => D("9.00")
    assert list(passagens_segunda_perna.values_list("preco_rodoviaria", flat=True)) == [D("9.00")] * 2
    # a primeira e segunda pernas foram emitidas com os Descontos corretos
    assert list(passagens_primeira_perna.values_list("desconto", flat=True)) == [D("33.00")] * 2
    assert list(passagens_segunda_perna.values_list("desconto", flat=True)) == [D("200.00")] * 2

    # valida atributos por buseiro
    passagens_buseiro_1 = passagens.filter(buseiro_internal_id=15)
    assert all(passagem.poltrona_external_id == 10 for passagem in passagens_buseiro_1)
    assert sum([passagem.valor_cheio for passagem in passagens_buseiro_1]) == D("181.55")
    passagens_buseiro_2 = passagens.filter(buseiro_internal_id=16)
    assert all(passagem.poltrona_external_id == 11 for passagem in passagens_buseiro_2)
    assert sum([passagem.valor_cheio for passagem in passagens_buseiro_2]) == D("181.55")


def test_comprar_com_conexao_com_desconto(mock_login, praxio_api, mock_bloqueia_poltrona, mock_compra_conexao):
    cidades = baker.make(
        core.Cidade,
        name=cycle(["Goiania", "Conceicao dos Alagoas", "Sao Paulo"]),
        company=praxio_api.company,
        _quantity=3,
    )
    locais = baker.make(
        core.LocalEmbarque,
        nickname=cycle(["rodoviaria_goiania", "rodoviaria_conceicao_dos_alagoas", "rodoviaria_sao_paulo"]),
        cidade=cycle(cidades),
        id_external=cycle([270, 3081, 3087]),
        _quantity=3,
    )
    goiania, c_dos_alagoas, sao_paulo = locais
    provider_data = json.dumps(
        mocker_praxio.MockBuscarServico.response_com_conexao_com_desconto(
            goiania.id_external, c_dos_alagoas.id_external, sao_paulo.id_external
        )["ListaPartidas"][0]
    )
    trecho_classe = baker.make(
        core.TrechoClasse,
        trechoclasse_internal_id=8888,
        provider_data=provider_data,
        grupo=baker.make(core.Grupo),
        external_id=133266,
        origem=goiania,
        destino=sao_paulo,
        datetime_ida=datetime(2024, 8, 28, 21, 10),
        preco_rodoviaria=D("267.10"),
        external_id_tipo_veiculo=383,
        external_id_desconto=507,
    )
    trechoclasse_internal_id = trecho_classe.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_internal_id, quantidade_passageiros=2, valor_cheio=D("267.10"))
    assert not Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_internal_id
    ).exists()
    praxio_api.comprar(params)
    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_internal_id
    ).order_by("poltrona_external_id")
    # 2 pax em trecho com 1 conexao devem emitir 4 passagens
    assert len(passagens) == 4
    # Todas as passagens foram confirmadas
    assert all(passagem.status == Passagem.Status.CONFIRMADA for passagem in passagens)
    # Passagens emitidas sem divergência
    assert all(passagem.valor_cheio == passagem.preco_rodoviaria for passagem in passagens)
    passagens_primeira_perna = passagens.filter(origem=goiania, destino=c_dos_alagoas)
    assert all(passagem.preco_rodoviaria == D("18.70") for passagem in passagens_primeira_perna)
    assert all(passagem.desconto == D("-3.15") for passagem in passagens_primeira_perna)
    passagens_segunda_perna = passagens.filter(origem=c_dos_alagoas, destino=sao_paulo)
    assert all(passagem.preco_rodoviaria == D("248.40") for passagem in passagens_segunda_perna)
    assert all(passagem.desconto == D("-64.40") for passagem in passagens_segunda_perna)


def test_comprar_com_conexao_assert_payload(mock_login, praxio_api, mocker):
    cidades = baker.make(
        core.Cidade,
        name=cycle(["Goiania", "Conceicao dos Alagoas", "Sao Paulo"]),
        company=praxio_api.company,
        _quantity=3,
    )
    locais = baker.make(
        core.LocalEmbarque,
        nickname=cycle(["rodoviaria_goiania", "rodoviaria_conceicao_dos_alagoas", "rodoviaria_sao_paulo"]),
        cidade=cycle(cidades),
        id_external=cycle([270, 3081, 3087]),
        _quantity=3,
    )
    goiania, c_dos_alagoas, sao_paulo = locais
    provider_data = json.dumps(
        mocker_praxio.MockBuscarServico.response_com_conexao(
            goiania.id_external, c_dos_alagoas.id_external, sao_paulo.id_external
        )["ListaPartidas"][0]
    )
    trecho_classe = baker.make(
        core.TrechoClasse,
        trechoclasse_internal_id=8888,
        provider_data=provider_data,
        grupo=baker.make(core.Grupo),
        external_id=119882,
        origem=goiania,
        destino=sao_paulo,
        datetime_ida=datetime(2024, 8, 28, 21, 10),
        preco_rodoviaria=D("181.55"),
        external_id_tipo_veiculo=2,
        external_id_desconto=123,
        desconto=10,
    )
    trechoclasse_internal_id = trecho_classe.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_internal_id, quantidade_passageiros=2, valor_cheio=D("181.55"))

    mock_bloquear_poltronas = mocker.patch.object(PraxioAPI, "bloquear_poltronas")
    mock_comprar_passagens = mocker.patch.object(PraxioAPI, "comprar_passagens")
    praxio_api.comprar(params)
    mock_bloquear_poltronas.assert_called_once()
    mock_comprar_passagens.assert_called_once()
    payload_endpoint_compra_passagens = mock_comprar_passagens.call_args[0][1]
    perna_1 = payload_endpoint_compra_passagens["listVendasXmlEnvio"][0]
    perna_2 = payload_endpoint_compra_passagens["listVendasXmlEnvio"][1]

    # TODO esse valor está errado. O correto é enviar o valor individual de cada perna
    assert perna_1["pagamentoXml"][0]["ValorPagamento"] == D("181.55") * 2
    assert perna_2["pagamentoXml"][0]["ValorPagamento"] == D("181.55") * 2


def test_comprar_com_conexao_divergencia_valor_checkout(mock_login, praxio_api):
    cidades = baker.make(
        core.Cidade,
        name=cycle(["Goiania", "Conceicao dos Alagoas", "Sao Paulo"]),
        company=praxio_api.company,
        _quantity=3,
    )
    locais = baker.make(
        core.LocalEmbarque,
        nickname=cycle(["rodoviaria_goiania", "rodoviaria_conceicao_dos_alagoas", "rodoviaria_sao_paulo"]),
        cidade=cycle(cidades),
        id_external=cycle([270, 3081, 3087]),
        _quantity=3,
    )
    goiania, c_dos_alagoas, sao_paulo = locais
    provider_data = json.dumps(
        mocker_praxio.MockBuscarServico.response_com_conexao(
            goiania.id_external, c_dos_alagoas.id_external, sao_paulo.id_external
        )["ListaPartidas"][0]
    )
    trecho_classe = baker.make(
        core.TrechoClasse,
        trechoclasse_internal_id=8888,
        provider_data=provider_data,
        grupo=baker.make(core.Grupo, company_integracao=praxio_api.company),
        external_id=119882,
        origem=goiania,
        destino=sao_paulo,
        datetime_ida=datetime(2024, 8, 28, 21, 10),
        preco_rodoviaria=D("181.55"),
        external_id_tipo_veiculo=2,
        external_id_desconto=123,
        desconto=10,
    )
    trechoclasse_internal_id = trecho_classe.trechoclasse_internal_id
    # Dada uma compra com valor de compra do checkout desatualizado
    params = _comprar_params(trechoclasse_internal_id, quantidade_passageiros=2, valor_cheio=D("160.00"))
    assert not Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_internal_id
    ).exists()
    with pytest.raises(RodoviariaBaseException, match="PraxioAPI: Divergencia na compra de conexao."):
        praxio_api.comprar(params)
    passagens = Passagem.objects.filter(
        trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_internal_id
    ).order_by("poltrona_external_id")
    # a compra deve falhar e as passagens serão marcadas com status erro e com a msg de erro correspondente
    assert len(passagens) == 4
    assert all(passagem.status == Passagem.Status.ERRO for passagem in passagens)
    assert all(passagem.erro is not None for passagem in passagens)


def test_comprar_voucher(mock_login, praxio_api, praxio_trechoclasses, mock_bloqueia_poltrona, mock_compra_voucher):
    praxio_api.company.features.remove("bpe")
    praxio_api.company.save()
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "poltrona_external_id"
    )
    assert len(passagens) == 2
    assert passagens[0].preco_rodoviaria == praxio_trechoclasses.ida.preco_rodoviaria
    assert passagens[1].preco_rodoviaria == praxio_trechoclasses.ida.preco_rodoviaria
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert not passagem.erro
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.numero_passagem == "45542"
    assert passagem.id_estabelecimento_external == "102"
    assert passagem.plataforma
    assert passagem.linha == "GOIANIA (GO) - CURITIBA (PR)"
    assert not passagem.numero_bpe
    assert passagem.serie_bpe == "@"
    assert not passagem.bpe_monitriip_code
    assert not passagem.bpe_qrcode
    assert not passagem.protocolo_autorizacao
    assert not passagem.chave_bpe
    assert not passagem.numero_bilhete_embarque
    assert not passagem.tipo_taxa_embarque


def test_comprar_erro_bpe(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_erro_bpe,
):
    # Passagem com erro no BPe vai gerar uma passagem válida, com chave BPe e tudo mais.
    # Entretanto, não será um qrCode válido. Vai dar erro ao ler ele
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=1)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.preco_rodoviaria == praxio_trechoclasses.ida.preco_rodoviaria
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert not passagem.erro
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.numero_passagem == "961"
    assert passagem.numero_bpe == "961"
    assert passagem.serie_bpe == "212"
    assert passagem.bpe_monitriip_code == (
        "3324055240632900139463064000156532200156533200015653208-9440-"
        "31202406031200000100000158000614000000000000000000002000001"
    )
    assert passagem.bpe_qrcode
    assert not passagem.protocolo_autorizacao
    assert passagem.chave_bpe
    assert passagem.plataforma
    assert passagem.numero_bpe
    assert passagem.data_autorizacao is None


def test_comprar_erro_bpe_receita_out_of_service(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_erro_bpe_receita_out_of_service,
):
    # Passagem com erro no BPe vai gerar uma passagem válida, com chave BPe e tudo mais.
    # Entretanto, não será um qrCode válido. Vai dar erro ao ler ele
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=1)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.preco_rodoviaria == praxio_trechoclasses.ida.preco_rodoviaria
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert not passagem.erro
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.numero_passagem == "212"
    assert passagem.numero_bpe == "212"
    assert passagem.serie_bpe == "600"
    assert passagem.bpe_monitriip_code == (
        "5223051065187000042763600000000212200000213300000021200010801202305291200000"
        "100000289460000000000000000000000270000011"
    )
    assert passagem.bpe_qrcode
    assert not passagem.protocolo_autorizacao
    assert passagem.chave_bpe


def test_comprar_erro_bpe_response_vazio(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_erro_bpe_response_vazio,
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=1)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.preco_rodoviaria == praxio_trechoclasses.ida.preco_rodoviaria
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert not passagem.erro
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.numero_passagem == "212"
    assert passagem.numero_bpe == "212"
    assert passagem.serie_bpe == "600"
    assert not passagem.bpe_monitriip_code
    assert not passagem.bpe_qrcode
    assert not passagem.protocolo_autorizacao
    assert not passagem.chave_bpe


def test_comprar_erro_valor_menor(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_erro_valor_menor,
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with pytest.raises(
        RodoviariaCompraParcialPraxioException,
        match=r"Soma dos pagamentos menor que o valor da passagem",
    ):
        praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "poltrona_external_id"
    )
    assert len(passagens) == 2
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.erro is None
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.pk in list(passagens.filter(tags__name="cancelamento_pendente").values_list("pk", flat=True))

    passagem = passagens.last()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 11
    assert passagem.buseiro_internal_id == 16
    assert passagem.erro == "Soma dos pagamentos menor que o valor da passagem"
    assert passagem.status == Passagem.Status.ERRO
    assert passagem.pk not in list(passagens.filter(tags__name="cancelamento_pendente").values_list("pk", flat=True))


def test_comprar_voucher_erro_valor_menor(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_voucher_erro_valor_menor,
):
    # diferente do mesmo erro no endpoint de BPE,
    # quando tentamos comprar uma passagem e passamos um valor menor
    # ele da erro em toda a compra. Sem confirmar nenhuma passagem
    praxio_api.company.features.remove("bpe")
    praxio_api.company.save()
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with pytest.raises(
        RodoviariaCompraParcialPraxioException,
        match=r"Soma dos pagamentos menor que o valor da passagem",
    ):
        praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).order_by(
        "poltrona_external_id"
    )
    assert len(passagens) == 2
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.erro == "Soma dos pagamentos menor que o valor da passagem"
    assert passagem.status == Passagem.Status.ERRO
    assert passagem.pk not in list(passagens.filter(tags__name="cancelamento_pendente").values_list("pk", flat=True))

    passagem = passagens.last()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 11
    assert passagem.buseiro_internal_id == 16
    assert passagem.erro == "Soma dos pagamentos menor que o valor da passagem"
    assert passagem.status == Passagem.Status.ERRO
    assert passagem.pk not in list(passagens.filter(tags__name="cancelamento_pendente").values_list("pk", flat=True))


def test_comprar_erro_bloquear_poltrona(mock_login, praxio_api, praxio_trechoclasses, mock_bloqueia_poltrona_com_erro):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with pytest.raises(RodoviariaException, match=r"Falha ao bloquear poltrona"):
        praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.erro == "Falha ao bloquear poltrona"
    assert passagem.status == Passagem.Status.ERRO


def test_comprar_voucher_erro_confirmar_venda(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_voucher_com_erro,
):
    praxio_api.company.features.remove("bpe")
    praxio_api.company.save()
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with pytest.raises(RodoviariaCompraParcialPraxioException) as exc:
        praxio_api.comprar(params)
    assert exc.value.message == "Desconto Manual não permitido"
    assert exc.value.json_response == {
        "ParametrosEntrada": {},
        "QtdDisponivel": 0,
        "QtdGratuidade": 0,
        "NumeroPoltrona": 7,
        "Situacao": 3,
        "Caption": "",
        "Marcada": False,
        "LaypoltronaXml": None,
        "Xml": None,
        "IdErro": "VEN071",
        "Mensagem": "Desconto Manual não permitido",
        "MensagemDetalhada": "ALGUM ERRO AQUI",
        "Strings": [],
        "Integers": [],
        "Floats": [],
        "VarStr": None,
        "VarInt": 0,
        "VarFloat": 0.0,
        "Sucesso": False,
        "Advertencia": False,
    }
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.erro == "Desconto Manual não permitido"
    assert passagem.status == Passagem.Status.ERRO


def test_comprar_erro_confirmar_venda(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_com_erro_vagas_insuficientes,
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with pytest.raises(RodoviariaCompraParcialPraxioException) as exc:
        praxio_api.comprar(params)
    assert exc.value.message == "Que pena, não temos vagas suficientes nesse preço para a sua demanda de passageiros."
    assert exc.value.json_response == {
        "ParametrosEntrada": {},
        "QtdDisponivel": 0,
        "QtdGratuidade": 0,
        "NumeroPoltrona": 7,
        "Situacao": 3,
        "Caption": "",
        "Marcada": False,
        "LaypoltronaXml": None,
        "Xml": None,
        "IdErro": "VEN079",
        "Mensagem": "Que pena, não temos vagas suficientes nesse preço para a sua demanda de passageiros.",
        "MensagemDetalhada": "ALGUM ERRO AQUI",
        "Strings": [],
        "Integers": [],
        "Floats": [],
        "VarStr": None,
        "VarInt": 0,
        "VarFloat": 0.0,
        "Sucesso": False,
        "Advertencia": False,
    }
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.erro == "Que pena, não temos vagas suficientes nesse preço para a sua demanda de passageiros."
    assert passagem.status == Passagem.Status.ERRO


def test_comprar_erro_desconto_generico(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_erro_desconto_generico,
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with pytest.raises(RodoviariaCompraParcialPraxioException) as exc:
        praxio_api.comprar(params)
    err_msg = "Que pena, não temos vagas suficientes nesse preço para a sua demanda de passageiros."
    assert exc.value.message == err_msg

    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.erro == err_msg
    assert passagem.status == Passagem.Status.ERRO


def test_comprar_erro_estabelecimento_fiscal(
    mock_login,
    praxio_api,
    praxio_trechoclasses,
    mock_bloqueia_poltrona,
    mock_compra_erro_estabelecimento_fiscal,
):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with pytest.raises(RodoviariaCompraParcialPraxioException) as exc:
        praxio_api.comprar(params)
    err_msg = "Estabelecimento de venda sem Estabelecimento Fiscal cadastrado. Entre em contato com o parceiro."
    assert exc.value.message == err_msg

    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.erro == err_msg
    assert passagem.status == Passagem.Status.ERRO


def test_comprar_connection_error_confirmar_venda(mock_login, praxio_api, praxio_trechoclasses, mock_bloqueia_poltrona):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    with pytest.raises(
        RodoviariaConnectionError,
        match=r"praxio https://oci-parceiros2.praxioluna.com.br/Autumn/VendaPassagem/VendaPassagemBPE connection error",
    ):
        praxio_api.comprar(params)
    passagem = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).first()
    assert passagem.erro == (
        "praxio https://oci-parceiros2.praxioluna.com.br/Autumn/VendaPassagem/VendaPassagemBPE connection error"
    )
    assert passagem.status == Passagem.Status.ERRO


def test_buscar_corridas_request(mock_login, praxio_api, mock_buscar_servico):
    request_params = {"origem": 10, "destino": 20, "data": "2021-10-12"}
    corridas = praxio_api.buscar_corridas(request_params).servicos
    assert list(corridas[0].dict(exclude_unset=True).keys()) == [
        "external_id",
        "preco",
        "external_datetime_ida",
        "external_datetime_chegada",
        "vagas",
        "classe",
        "capacidade_classe",
        "distancia",
        "provider_data",
        "linha",
        "rota_external_id",
    ]


def test_buscar_corridas_com_conexao(mock_login, praxio_api, rota_praxio, mock_buscar_servico_com_conexao):
    request_params = {"origem": 175, "destino": 24, "data": "2021-10-12"}
    corridas = praxio_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 1
    assert corridas[0].provider_data["ViagemTFO"]["CodigoOrigem"] == 175
    assert corridas[0].provider_data["ViagemTFO"]["CodigoDestino"] == 2  # id conexao
    assert corridas[0].provider_data["conexoes"][0]["CodigoOrigem"] == 2  # id conexao
    assert corridas[0].provider_data["conexoes"][0]["CodigoDestino"] == 24
    assert corridas[0].provider_data["ViagemTFO"]["TipoHorario"] == "SEMI LEITO"
    assert corridas[0].external_datetime_ida == datetime.fromisoformat("2024-09-13T21:30:00")
    assert corridas[0].external_datetime_chegada == datetime.fromisoformat("2024-09-14T14:00:00")
    assert corridas[0].classe == "SEMI LEITO"
    assert corridas[0].capacidade_classe == 40
    assert corridas[0].distancia == D("947")
    assert corridas[0].preco == D("549.86")


def test_buscar_corridas_sem_tipo_horario(mock_login, praxio_api, rota_praxio, mock_buscar_servico_sem_tipo_horario):
    request_params = {"origem": 10, "destino": 20, "data": "2021-11-25"}
    corridas = praxio_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 1
    assert corridas[0].provider_data["ViagemTFO"]["CodigoOrigem"] == 1
    assert corridas[0].provider_data["ViagemTFO"]["CodigoDestino"] == 3
    assert corridas[0].provider_data["ViagemTFO"]["TipoHorario"] == ""
    assert corridas[0].provider_data["ViagemTFO"]["TipoServico"] == 6
    assert corridas[0].external_datetime_ida == datetime.fromisoformat("2022-11-25T20:00:00")
    assert corridas[0].external_datetime_chegada == datetime.fromisoformat("2022-11-25T21:50:00")
    assert corridas[0].capacidade_classe == 44
    assert corridas[0].classe == "Executivo"
    assert corridas[0].distancia == 88


def test_buscar_corridas_sem_tipo_horario_ou_tipo_servico(
    mock_login,
    praxio_api,
    rota_praxio,
    mock_buscar_servico_sem_tipo_horario_ou_tipo_servico,
):
    request_params = {"origem": 10, "destino": 20, "data": "2021-11-25"}
    corridas = praxio_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 1
    assert corridas[0].provider_data["ViagemTFO"]["CodigoOrigem"] == 1
    assert corridas[0].provider_data["ViagemTFO"]["CodigoDestino"] == 3
    assert corridas[0].provider_data["ViagemTFO"]["TipoHorario"] == ""
    assert corridas[0].provider_data["ViagemTFO"]["TipoServico"] is None
    assert corridas[0].external_datetime_ida == datetime.fromisoformat("2022-11-25T20:00:00")
    assert corridas[0].external_datetime_chegada == datetime.fromisoformat("2022-11-25T21:50:00")
    assert corridas[0].capacidade_classe == 44
    assert corridas[0].classe == "Convencional com sanitário"
    assert corridas[0].distancia == 88


def test_get_token(praxio_api, mock_login):
    praxio_api._get_token()

    assert isinstance(praxio_api._get_token(), PraxioAuth)
    assert (
        praxio_api._get_token().id_sessao_op
        == "091C04D80EDB93FA27D1C587C19DF62E8kFZldUbRvOPE/ZhCMm3cEmd1YmdrdSH0zYeVquSlWbzsxG1WHwe94/ME19bb1ck"
    )
    assert praxio_api._get_token().id_estabelecimento == 102
    assert praxio_api._get_token().serie_bpe == 12

    assert (
        praxio_api._id_sessao_op()
        == "091C04D80EDB93FA27D1C587C19DF62E8kFZldUbRvOPE/ZhCMm3cEmd1YmdrdSH0zYeVquSlWbzsxG1WHwe94/ME19bb1ck"
    )
    assert praxio_api._id_estabelecimento() == 102
    assert praxio_api._serie_bpe() == 12


def test_rota_id_from_trecho_classe(praxio_api):
    trecho_classe = baker.make("rodoviaria.TrechoClasse")
    # sem provider data
    assert praxio_api.rota_id_from_trecho_classe(trecho_classe) is None
    # com provider data mas sem IdCodigoLinha
    trecho_classe.provider_data = json.dumps({})
    assert praxio_api.rota_id_from_trecho_classe(trecho_classe) is None
    # com provider data e com IdCodigoLinha
    trecho_classe.provider_data = json.dumps({"IdCodigoLinha": 123321})
    assert praxio_api.rota_id_from_trecho_classe(trecho_classe) == 123321


def _get_reservar_params(
    rg_number,
    grupos,
    praxio_trechoclasses,
    categoria_especial: Passagem.CategoriaEspecial = Passagem.CategoriaEspecial.NORMAL,
):
    return ComprarForm.parse_obj(
        {
            "trechoclasse_id": praxio_trechoclasses.ida.trechoclasse_internal_id,
            "travel_id": 217,
            "poltronas": [9, 10],
            "valor_cheio": grupos.ida.trechoclasse.max_split_value,
            "categoria_especial": categoria_especial,
            "passageiros": [
                {
                    "id": 31,
                    "name": "Sophie Peixoto",
                    "cpf": "62287847065",
                    "rg_number": rg_number,
                    "rg_orgao": None,
                    "tipo_documento": "RG",
                    "phone": "52975156354",
                }
            ],
        }
    )


def test_fetch_rotinas_empresa(praxio_api, mock_login):
    next_days = 14
    shift_days = 5
    queue_name = "queue_name"
    return_task_object = False
    first_day = to_default_tz(datetime.today() + timedelta(days=shift_days))
    with mock.patch("rodoviaria.service.descobrir_rotas_praxio_async_svc.descobrir_rotas") as mock_descobrir_rotas:
        praxio_api.fetch_rotinas_empresa(next_days, first_day, queue_name)
    mock_descobrir_rotas.assert_called_once_with(
        praxio_api.login,
        praxio_api.company.company_internal_id,
        next_days,
        shift_days,
        queue_name,
        return_task_object,
    )


def test_fetch_rotinas_empresa_first_day_str(praxio_api, mock_login):
    next_days = 14
    queue_name = "queue_name"
    return_task_object = False
    first_day = today_midnight().isoformat()
    shift_days = (midnight(datetime.fromisoformat(first_day)) - today_midnight()).days
    with mock.patch("rodoviaria.service.descobrir_rotas_praxio_async_svc.descobrir_rotas") as mock_descobrir_rotas:
        praxio_api.fetch_rotinas_empresa(next_days, first_day, queue_name)
    mock_descobrir_rotas.assert_called_once_with(
        praxio_api.login,
        praxio_api.company.company_internal_id,
        next_days,
        shift_days,
        queue_name,
        return_task_object,
    )


def test_fetch_rotinas_empresa_first_day_none(praxio_api, mock_login):
    next_days = 14
    queue_name = "queue_name"
    return_task_object = False
    first_day = None
    with mock.patch("rodoviaria.service.descobrir_rotas_praxio_async_svc.descobrir_rotas") as mock_descobrir_rotas:
        praxio_api.fetch_rotinas_empresa(next_days, first_day, queue_name)
    mock_descobrir_rotas.assert_called_once_with(
        praxio_api.login,
        praxio_api.company.company_internal_id,
        next_days,
        0,
        queue_name,
        return_task_object,
    )


def test_verify_praxio_login(praxio_mock_login):
    nome = "empresa ltda"
    senha = "le senhe"
    cliente = "empresa_vr"

    resp = praxio_api.verify_praxio_login(nome, senha, cliente)
    resp_parsed = resp
    assert resp_parsed["IdSessaoOp"]
    assert resp_parsed["EstabelecimentoXml"]["IDEstabelecimento"] == 102


def test_verify_praxio_login_unauthorized(praxio_mock_unauthorized_login):
    nome = "empresa ltda"
    senha = "le senhe"
    cliente = "empresa_vr"

    with pytest.raises(RodoviariaUnauthorizedError):
        praxio_api.verify_praxio_login(nome, senha, cliente)


def test_buscar_corridas_filtra_por_company_external_id(praxio_api):
    # Filtra trechos de empresas Praxio por IdEstabelecimentoLinha (company_external_id)
    localidadeOrigem = 18
    LocalidadeDestino = 19
    DataPartida = datetime.strptime("2022-09-09", "%Y-%m-%d")
    company_external_id = praxio_api.company.company_external_id
    id_estabelecimento_venda = 18
    id_sessao_op = "22BC7855DD78632E6945"
    request_params = {
        "origem": localidadeOrigem,
        "destino": LocalidadeDestino,
        "data": DataPartida,
    }
    models.BuscarServicoForm.parse_obj(
        {
            "IdSessaoOp": id_sessao_op,
            "IdEstabelecimentoVenda": id_estabelecimento_venda,
            "LocalidadeOrigem": localidadeOrigem,
            "LocalidadeDestino": LocalidadeDestino,
            "DataPartida": DataPartida,
            "TempoPartida": 1,
            "DescontoAutomatico": 1,
            "sugestaoPassagem": 1,
            "IdEstabelecimento": company_external_id,
        }
    ).dict(by_alias=True, exclude_none=True)

    with mock.patch("rodoviaria.api.praxio.api.endpoints.BuscarServicoConfig") as mock_request, mock.patch(
        "rodoviaria.api.praxio.api.PraxioAPI._id_estabelecimento",
    ) as mock_id_estabelecimento_venda, mock.patch(
        "rodoviaria.api.praxio.api.PraxioAPI._id_sessao_op",
    ) as mock_id_sessao_op:
        mock_id_estabelecimento_venda.return_value = id_estabelecimento_venda
        mock_id_sessao_op.return_value = id_sessao_op

        praxio_api._buscar_corridas(request_params)

    mock_request.assert_called_once_with(praxio_api.login)


def test_get_conexao_valida(praxio_api, mock_login):
    corrida = {
        "IdViagem": 12,
        "ViagemTFO": {"TipoHorario": "EXECUTIVO", "DtaHoraChegada": "2022-11-02T08:00"},
        "conexoes": [
            {
                "IdViagem": 12,
                "viagemTFO": {
                    "TipoHorario": "EXECUTIVO",
                    "DataHoraInicio": "2022-11-02T08:00",
                },
            }
        ],
    }
    assert praxio_api._get_conexao_valida(corrida)


def test_get_conexao_valida_por_tipo_servico(praxio_api, mock_login):
    corrida = {
        "IdViagem": 12,
        "ViagemTFO": {"TipoServico": "6", "DtaHoraChegada": "2022-11-02T08:00"},
        "conexoes": [
            {
                "IdViagem": 12,
                "viagemTFO": {"TipoServico": "6", "DataHoraInicio": "2022-11-02T08:00"},
            }
        ],
    }
    assert praxio_api._get_conexao_valida(corrida)


def test_get_conexao_valida_chegada_diff_inicio(praxio_api, mock_login):
    corrida = {
        "IdViagem": 12,
        "ViagemTFO": {"TipoHorario": "EXECUTIVO", "DtaHoraChegada": "2022-11-02T08:10"},
        "conexoes": [
            {
                "IdViagem": 12,
                "viagemTFO": {
                    "TipoHorario": "EXECUTIVO",
                    "DataHoraInicio": "2022-11-02T08:00",
                },
            }
        ],
    }
    assert not praxio_api._get_conexao_valida(corrida)


def test_get_conexao_valida_id_diff(praxio_api, mock_login):
    corrida = {
        "IdViagem": 11,
        "ViagemTFO": {"TipoHorario": "EXECUTIVO", "DtaHoraChegada": "2022-11-02T08:10"},
        "conexoes": [
            {
                "IdViagem": 12,
                "viagemTFO": {
                    "TipoHorario": "EXECUTIVO",
                    "DataHoraInicio": "2022-11-02T08:00",
                },
            }
        ],
    }
    assert not praxio_api._get_conexao_valida(corrida)


def test_get_conexao_valida_classe_diff(praxio_api, mock_login):
    corrida = {
        "IdViagem": 11,
        "ViagemTFO": {"TipoHorario": "EXECUTIV", "DtaHoraChegada": "2022-11-02T08:10"},
        "conexoes": [
            {
                "IdViagem": 12,
                "viagemTFO": {
                    "TipoHorario": "EXECUTIVO",
                    "DataHoraInicio": "2022-11-02T08:00",
                },
            }
        ],
    }
    assert not praxio_api._get_conexao_valida(corrida)


def test_get_conexao_valida_mais_de_uma_conexao(praxio_api, mock_login):
    corrida = {
        "IdViagem": 11,
        "ViagemTFO": {"TipoHorario": "EXECUTIVO", "DtaHoraChegada": "2022-11-02T08:10"},
        "conexoes": [
            {
                "IdViagem": 12,
                "viagemTFO": {
                    "TipoHorario": "EXECUTIVO",
                    "DataHoraInicio": "2022-11-02T08:00",
                },
            },
            {
                "IdViagem": 12,
                "viagemTFO": {
                    "TipoHorario": "EXECUTIVO",
                    "DataHoraInicio": "2022-11-02T08:00",
                },
            },
        ],
    }
    assert not praxio_api._get_conexao_valida(corrida)


def test_get_classe(praxio_api):
    viagem_tfo = {"TipoHorario": "EXECUTIVO"}
    assert praxio_api._get_classe(viagem_tfo) == viagem_tfo["TipoHorario"]
    viagem_tfo = {"TipoHorario": "", "TipoServico": "6"}
    assert praxio_api._get_classe(viagem_tfo) == praxio_api.MAP_CLASSES_PRAXIO[6]

    viagem_tfo = {"TipoHorario": "", "TipoServico": None}
    assert praxio_api._get_classe(viagem_tfo) == "Convencional com sanitário"

    viagem_tfo = {"TipoHorario": "DIFERENCIADO", "TipoServico": "6"}
    assert praxio_api._get_classe(viagem_tfo) == "DIFERENCIADO"

    viagem_tfo = {"TipoHorario": "DIFERENCIADO", "TipoServico": None}
    assert praxio_api._get_classe(viagem_tfo) == "DIFERENCIADO"


def test_buscar_servicos_por_data_erro_mais_30_dias(praxio_api):
    data_inicio = datetime(2022, 4, 19)
    data_fim = datetime(2022, 6, 19)
    with pytest.raises(ValueError):
        praxio_api.buscar_servicos_por_data(data_inicio, data_fim)


def test_buscar_servicos_por_data(praxio_api, mock_login, mock_listar_viagens):
    result = praxio_api.buscar_servicos_por_data(today_midnight(), today_midnight())
    assert len(result) == 6
    assert result[265] == datetime(2021, 11, 19, 17, 0)


def test_buscar_servicos_por_data_pega_max_data(praxio_api, mock_login):
    external_id = 3469
    retorno_mock = [
        {
            "IdViagem": external_id,
            "DataViagem": "2022-07-03T00:00:00",
            "HoraViagem": "2000",
        },
        {
            "IdViagem": external_id,
            "DataViagem": "2022-07-04T00:00:00",
            "HoraViagem": "2000",
        },
        {
            "IdViagem": external_id,
            "DataViagem": "2022-07-04T00:00:00",
            "HoraViagem": "2010",
        },
    ]
    with mock.patch.object(PraxioAPI, "_call_buscar_servicos_por_data") as mock_buscar_servicos:
        mock_buscar_servicos.side_effect = [retorno_mock, None]
        result = praxio_api.buscar_servicos_por_data(today_midnight(), today_midnight())
    assert len(result) == 1
    assert result[external_id] == datetime(2022, 7, 4, 20, 10)


def test_salva_provider_da_compra(mock_login, praxio_api, praxio_trechoclasses, mock_bloqueia_poltrona, mock_compra):
    trechoclasse_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id, quantidade_passageiros=2)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    praxio_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    mock_response_conftest = mocker.MockConfirmarVenda.response_with_bpe_for_two()
    dict_info_passagens = {}

    # Verifica se comprou duas passagens
    assert len(passagens) == 2
    passagem_um = Passagem.objects.get(numero_passagem=passagens[0].numero_passagem)
    passagem_dois = Passagem.objects.get(numero_passagem=passagens[1].numero_passagem)

    # Verifica se os dados específicos da passagens constam apenas na passagem do pax
    assert passagem_um.provider_data != passagem_dois.provider_data
    dict_info_passagens[mock_response_conftest["oObj"]["Passagem"]["ListaPassagem"][0]["NumPassagem"]] = (
        mock_response_conftest["oObj"]["Passagem"]["ListaPassagem"][0]
    )
    dict_info_passagens[mock_response_conftest["oObj"]["Passagem"]["ListaPassagem"][1]["NumPassagem"]] = (
        mock_response_conftest["oObj"]["Passagem"]["ListaPassagem"][1]
    )
    assert (
        passagem_um.provider_data["ListaPassagem"]
        == dict_info_passagens[passagem_um.provider_data["ListaPassagem"]["NumPassagem"]]
    )
    assert (
        passagem_dois.provider_data["ListaPassagem"]
        == dict_info_passagens[passagem_dois.provider_data["ListaPassagem"]["NumPassagem"]]
    )

    # Verifica se os dados gerais do provider_data são comuns a ambas as passagens
    passagem_um.provider_data.pop("ListaPassagem")
    passagem_dois.provider_data.pop("ListaPassagem")
    mock_response_conftest["oObj"]["Passagem"].pop("ListaPassagem")
    assert passagem_um.provider_data == mock_response_conftest
    assert passagem_dois.provider_data == mock_response_conftest
    assert passagem_um.provider_data == passagem_dois.provider_data


def test_get_atualizacao_passagem_api_parceiro_praxio_voucher(
    praxio_api, praxio_company, mock_passagem_reimprimir_voucher
):
    dict_pass_retorno = mocker.MockRetornadaPassagemReimprimir.response_venda_voucher()["PassRetorno"]
    id_estabelecimento = dict_pass_retorno["IDEstabelecimento"]
    num_passagem = dict_pass_retorno["NumPassagem"]
    serie_bloco = dict_pass_retorno["SerieBloco"]
    poltrona = dict_pass_retorno["Poltrona"]
    passagem = baker.make(
        "rodoviaria.Passagem",
        id_estabelecimento_external=id_estabelecimento,
        localizador="45543-45526@102",
        numero_passagem=num_passagem,
        numero_bpe=serie_bloco,
        company_integracao=praxio_company,
    )
    response = praxio_api.get_atualizacao_passagem_api_parceiro(passagem)
    assert response.integracao == "Praxio"
    assert response.numero_passagem == str(num_passagem)
    assert response.numero_assento == poltrona
    assert response.birth is None
    assert response.bpe_id is None
    assert response.bpe_public_url is None
    assert response.data_partida == "01-09-2023 21:31:00"
    assert response.taxa_embarque == str(dict_pass_retorno["TaxaEmbarque"])


def test_get_atualizacao_passagem_api_parceiro_praxio_bpe(praxio_api, praxio_company, mock_passagem_reimprimir_bpe):
    dict_pass_retorno = mocker.MockRetornadaPassagemReimprimir.response_venda_bpe()["PassRetorno"]
    id_estabelecimento = dict_pass_retorno["IDEstabelecimento"]
    num_passagem = dict_pass_retorno["NumPassagem"]
    serie_bloco = dict_pass_retorno["SerieBloco"]
    poltrona = dict_pass_retorno["Poltrona"]
    chave_bpe = dict_pass_retorno["ChaveBPe"]
    url_qrcode_bpe = dict_pass_retorno["UrlQrCodeBPe"]
    passagem = baker.make(
        "rodoviaria.Passagem",
        id_estabelecimento_external=id_estabelecimento,
        numero_passagem=num_passagem,
        numero_bpe=serie_bloco,
        company_integracao=praxio_company,
    )
    response = praxio_api.get_atualizacao_passagem_api_parceiro(passagem)
    assert response.integracao == "Praxio"
    assert response.numero_passagem == str(num_passagem)
    assert response.numero_assento == poltrona
    assert response.birth == "30/12/1899"
    assert response.bpe_id == chave_bpe
    assert response.bpe_public_url == url_qrcode_bpe
    assert response.data_partida == "08-10-2023 21:00:00"
    assert response.taxa_embarque == str(dict_pass_retorno["TaxaEmbarque"])


def test_get_atualizacao_passagem_api_parceiro_praxio_nenhum_passagem(
    praxio_api, praxio_company, mock_passagem_reimprimir_passagem_nao_encontrada
):
    dict_pass_retorno = mocker.MockRetornadaPassagemReimprimir.response_passagem_nao_encontrada()["PassRetorno"]
    id_estabelecimento = dict_pass_retorno["IDEstabelecimento"]
    num_passagem = dict_pass_retorno["NumPassagem"]
    serie_bloco = dict_pass_retorno["SerieBloco"]
    passagem = baker.make(
        "rodoviaria.Passagem",
        id_estabelecimento_external=id_estabelecimento,
        numero_passagem=num_passagem,
        numero_bpe=serie_bloco,
        company_integracao=praxio_company,
    )
    with pytest.raises(RodoviariaException, match="Passagem não encontrada"):
        praxio_api.get_atualizacao_passagem_api_parceiro(passagem)


def test_busca_itinerario_viagem_circuit(praxio_login, mock_lista_partidas_tfo, mock_login):
    resp = _busca_itinerario_viagem_circuit(praxio_login.company_id, 123)
    expected = ["176", "13", "193", "177"]
    form = resp.parsed

    external_ids = [item.local.external_local_id for item in form]
    assert sorted(external_ids) == sorted(expected)


def test_descobrir_rotas_async_descobrir_rotas(praxio_api):
    with mock.patch("rodoviaria.service.descobrir_rotas_praxio_async_svc.descobrir_rotas") as mock_descobrir_rotas:
        praxio_api.descobrir_rotas_async(7, 1, "queue_name", False, Company.ModeloVenda.MARKETPLACE)
    mock_descobrir_rotas.assert_called_once_with(
        praxio_api.login, praxio_api.company.company_internal_id, 7, 1, "queue_name", False
    )


def test_descobrir_operacao_async_descobrir_operacao(praxio_api):
    with mock.patch("rodoviaria.api.praxio.descobrir_operacao.descobrir_operacao") as mock_descobrir_operacao:
        praxio_api.descobrir_operacao_async(7, 1, "queue_name", False)
    mock_descobrir_operacao.assert_called_once_with(praxio_api.login, 7, 1, "queue_name", False)


def test_retorna_provider_data_bpe(praxio_api):
    id1 = *********
    id2 = *********
    lista_passagem = [{"NumPassagem": id1}]
    lista_passagem_2 = [{"NumPassagem": id2}]
    passagem = {
        "ListaPassagem": lista_passagem + lista_passagem_2,
        "ParametrosEntrada": {},
    }
    confirmar_venda_response = {
        "ParametrosEntrada": {},
        "oObj": {"Passagem": passagem},
    }
    passagens_response, dict_provider_data_por_identificador = praxio_api._retorna_provider_data(
        confirmar_venda_response
    )
    assert passagens_response == lista_passagem + lista_passagem_2
    assert id1 in dict_provider_data_por_identificador
    assert dict_provider_data_por_identificador[id1]["ListaPassagem"] == lista_passagem[0]
    assert dict_provider_data_por_identificador[id1]["ParametrosEntrada"] == {}
    assert dict_provider_data_por_identificador[id1]["oObj"] == {"Passagem": {"ParametrosEntrada": {}}}

    assert id2 in dict_provider_data_por_identificador
    assert dict_provider_data_por_identificador[id2]["ListaPassagem"] == lista_passagem[0]
    assert dict_provider_data_por_identificador[id2]["ParametrosEntrada"] == {}
    assert dict_provider_data_por_identificador[id2] == {
        "ListaPassagem": {"NumPassagem": *********},
        "ParametrosEntrada": {},
        "oObj": {"Passagem": {"ParametrosEntrada": {}}},
    }


def test_retorna_provider_data_voucher(praxio_api):
    id1 = *********
    id2 = *********
    lista_passagem = [{"NumPassagem": id1}]
    lista_passagem_2 = [{"NumPassagem": id2}]
    confirmar_venda_response = {
        "ListaPassagem": lista_passagem + lista_passagem_2,
        "ParametrosEntrada": {},
        "ListaEmAberto": [],
        "DadosVoucher": [],
    }
    passagens_response, dict_provider_data_por_identificador = praxio_api._retorna_provider_data(
        confirmar_venda_response
    )
    assert passagens_response == lista_passagem + lista_passagem_2
    assert id1 in dict_provider_data_por_identificador
    assert dict_provider_data_por_identificador[id1]["ListaPassagem"] == lista_passagem[0]
    assert dict_provider_data_por_identificador[id1]["ParametrosEntrada"] == {}
    assert dict_provider_data_por_identificador[id1] == {
        "ListaPassagem": {"NumPassagem": *********},
        "ParametrosEntrada": {},
        "DadosVoucher": [],
        "ListaEmAberto": [],
    }

    assert id2 in dict_provider_data_por_identificador
    assert dict_provider_data_por_identificador[id2]["ListaPassagem"] == lista_passagem[0]
    assert dict_provider_data_por_identificador[id2]["ParametrosEntrada"] == {}
    assert dict_provider_data_por_identificador[id2] == {
        "ListaPassagem": {"NumPassagem": *********},
        "ParametrosEntrada": {},
        "DadosVoucher": [],
        "ListaEmAberto": [],
    }


def test_garante_bloqueio_poltronas(praxio_api, mocker):
    praxio_api.cache.set_poltrona_key_cache(1, 1, True)
    praxio_api.cache.set_poltrona_key_cache(1, 2, True)
    praxio_api.cache.set_poltrona_key_cache(1, 3, True)
    mock_bloquear_poltronas = mocker.patch.object(PraxioAPI, "bloquear_poltronas")
    praxio_api._garante_bloqueio_poltronas(1, [1, 2, 3], [SimpleNamespace(id=0)])
    mock_bloquear_poltronas.assert_not_called()


def test_garante_bloqueio_poltronas_sem_uma_poltrona_no_cache(praxio_api, mocker):
    praxio_api.cache.set_poltrona_key_cache(1, 1, True)
    praxio_api.cache.set_poltrona_key_cache(1, 2, True)
    mock_bloquear_poltronas = mocker.patch.object(PraxioAPI, "bloquear_poltronas")
    praxio_api._garante_bloqueio_poltronas(1, [1, 2, 3], [SimpleNamespace(id=0)])
    mock_bloquear_poltronas.assert_called_once()


@pytest.mark.parametrize(
    "categoria_especial,expected_params",
    [
        (Passagem.CategoriaEspecial.NORMAL, {}),
        (Passagem.CategoriaEspecial.IDOSO_100, {"TipoPassageiro": 4, "PercDesconto": 100}),
        (Passagem.CategoriaEspecial.JOVEM_50, {"TipoPassageiro": 10, "PercDesconto": 50}),
        (Passagem.CategoriaEspecial.ESPACO_MULHER, {"TipoPassageiro": 18, "PercDesconto": 0}),
    ],
)
def test_add_categoria_especial_params(praxio_api, categoria_especial, expected_params):
    params = {
        "IdSessaoOp": "sessao",
        "IdEstabelecimento": 12,
        "IdEstabelecimentoDevolucao": 12,
        "ValorVenda": D("10.00"),
        "Passagem": {
            "SerieBloco": "601",
            "NumeroPassagem": 50,
            "IdEstabelecimento": 12,
            "ValorDevolucao": D("10.00"),
        },
    }
    passagem = baker.prepare(Passagem, categoria_especial=categoria_especial)
    result = praxio_api._add_categoria_especial_params(passagem, params)

    json = models.GravaDevolucaoForm.parse_obj(result).dict(by_alias=True, exclude_unset=True)
    for key, value in expected_params.items():
        assert json["Passagem"].get(key) == value


def test_get_desenho_mapa_poltronas(mock_login, mock_get_poltronas_livres, praxio_api, praxio_trechoclasses):
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    praxio_trechoclasses.ida.provider_data = json.dumps(
        {"ViagemTFO": {"TipoHorario": "leito individual"}, "Andares": "1"}
    )
    praxio_trechoclasses.ida.save()
    mapa_poltronas = praxio_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "leito individual"
    normal = Passagem.CategoriaEspecial.NORMAL
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "livre": False,
                        "x": 5,
                        "y": 1,
                        "numero": 3,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 1,
                        "numero": 4,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 1,
                        "numero": 2,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 1,
                        "numero": 1,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 2,
                        "numero": 5,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                ],
            }
        ]
    }


def test_desbloquear_poltronas(praxio_api, mock_login, praxio_trechoclasses, mock_desbloqueia_poltrona):
    tcid = praxio_trechoclasses.ida.trechoclasse_internal_id
    praxio_api.desbloquear_poltronas(tcid, [2])


def test_desbloquear_poltronas_voucher_ja_marcado(
    praxio_api, mock_login, praxio_trechoclasses, mock_voucher_ja_marcado
):
    tcid = praxio_trechoclasses.ida.trechoclasse_internal_id
    with pytest.raises(PassengerTicketAlreadyPrintedException):
        praxio_api.desbloquear_poltronas(tcid, [2])


@pytest.mark.parametrize(
    "input_data,expected",
    [
        ({}, None),
        ({"oObj": {}}, None),
        ({"oObj": {"Bpe": None}}, None),
        ({"oObj": {"Bpe": [{"Result": None}]}}, []),
        ({"oObj": {"Bpe": [{"Result": [{"bpe": "maneiro"}]}]}}, [{"bpe": "maneiro"}]),
    ],
)
def test_get_bpe_response_if_exists(praxio_api, input_data, expected):
    assert praxio_api._get_bpe_response_if_exists(input_data) == expected

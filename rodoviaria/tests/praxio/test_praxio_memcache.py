from unittest.mock import MagicMock, patch

import pytest

from rodoviaria.api.praxio.memcache import PraxioMC


@pytest.fixture
def praxio():
    company_id = 123
    return PraxioMC(company_id)


def test_initialization(praxio):
    expected_prefix = f"PRAXIO_COMPANY_{praxio.prefix.split('_')[-1]}"
    assert praxio.prefix == expected_prefix


@patch("rodoviaria.api.praxio.memcache.getmc_pymemcache")
def test_set_poltrona_key_cache(mock_mc, praxio):
    mock_mc.return_value = MagicMock()
    trecho_classe_id = 1
    poltrona = 10
    cache_value = "test_value"
    praxio.set_poltrona_key_cache(trecho_classe_id, poltrona, cache_value)
    mock_mc.return_value.add.assert_called_once_with(
        key=f"{praxio.prefix}_TRECHO_{trecho_classe_id}_POLTRONA_{poltrona}",
        value=cache_value,
        timeout=praxio.default_timeout,
    )


@patch("rodoviaria.api.praxio.memcache.getmc_pymemcache")
def test_get_poltrona_key_cache(mock_mc, praxio):
    mock_mc.return_value = MagicMock()
    trecho_classe_id = 1
    poltrona = 10
    expected_value = "test_value"
    mock_mc.return_value.get.return_value = expected_value
    cache_value = praxio.get_poltrona_key_cache(trecho_classe_id, poltrona)
    assert cache_value == expected_value
    mock_mc.return_value.get.assert_called_once_with(
        key=f"{praxio.prefix}_TRECHO_{trecho_classe_id}_POLTRONA_{poltrona}"
    )


@patch("rodoviaria.api.praxio.memcache.getmc_pymemcache")
def test_delete_poltrona_key_cache(mock_mc, praxio):
    mock_mc.return_value = MagicMock()
    trecho_classe_id = 1
    poltrona = 10
    praxio.delete_poltrona_key_cache(trecho_classe_id, poltrona)
    mock_mc.return_value.delete.assert_called_once_with(
        key=f"{praxio.prefix}_TRECHO_{trecho_classe_id}_POLTRONA_{poltrona}"
    )


@patch("rodoviaria.api.praxio.memcache.getmc_pymemcache")
def test_insert_poltrona_indisponivel_cache(mock_mc, praxio):
    mock_mc.return_value = MagicMock()
    trecho_classe_id = 1
    poltrona_indisponivel = 10
    mock_mc.return_value.get.return_value = set()

    praxio.insert_poltrona_indisponivel_cache(trecho_classe_id, poltrona_indisponivel)
    expected_key = f"{praxio.prefix}_TRECHO_{trecho_classe_id}_POLTRONA_INDISPONIVEL"
    mock_mc.return_value.set.assert_called_once_with(
        key=expected_key, value={poltrona_indisponivel}, timeout=praxio.default_timeout
    )


@patch("rodoviaria.api.praxio.memcache.getmc_pymemcache")
def test_get_poltrona_indisponivel_cache_empty(mock_mc, praxio):
    mock_mc.return_value = MagicMock()
    trecho_classe_id = 1
    mock_mc.return_value.get.return_value = None
    result = praxio.get_poltrona_indisponivel_cache(trecho_classe_id)
    assert result == set()


@patch("rodoviaria.api.praxio.memcache.getmc_pymemcache")
def test_delete_poltrona_indisponivel_cache(mock_mc, praxio):
    mock_mc.return_value = MagicMock()
    trecho_classe_id = 1
    praxio.delete_poltrona_indisponivel_cache(trecho_classe_id)
    mock_mc.return_value.delete.assert_called_once_with(
        key=f"{praxio.prefix}_TRECHO_{trecho_classe_id}_POLTRONA_INDISPONIVEL"
    )

from model_bakery import baker


def test_cidade_str(vexado_company):
    cidade = baker.make(
        "rodoviaria.Cidade",
        name="São Paulo",
        cidade_internal_id=10,
        company=vexado_company,
    )
    assert str(cidade) == "São Paulo - 10 - Giro Turismo"


def test_cidade_internal_str():
    cidade_internal = baker.make("rodoviaria.cidadeInternal", name="São Paulo", uf="SP")
    assert str(cidade_internal) == "São Paulo - SP"


def test_company_str(vexado_integracao):
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=999,
        name="Adamantina",
        integracao=vexado_integracao,
    )
    assert str(company) == "marketplace - vexado - Adamantina - 999"


def test_grupo_str(vexado_company):
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company, grupo_internal_id=888)
    assert str(grupo) == f"888 - hibrido - vexado - Giro Turismo - {vexado_company.company_internal_id}"


def test_integracao_str(vexado_company):
    integracao = baker.make("rodoviaria.Integracao", name="abacate")
    assert str(integracao) == "abacate"


def test_local_embarque_str(vexado_company):
    local_embarque = baker.make("rodoviaria.LocalEmbarque", nickname="Gloria", local_embarque_internal_id=20)
    assert str(local_embarque) == "Gloria - 20"


def test_passagem_str(vexado_company):
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company, grupo_internal_id=888)
    trecho_classe = baker.make("rodoviaria.TrechoClasse", grupo=grupo, trechoclasse_internal_id=555)
    passagem = baker.make(
        "rodoviaria.Passagem",
        buseiro_internal_id=777,
        travel_internal_id=123,
        trechoclasse_integracao=trecho_classe,
    )
    assert str(passagem) == f"123 - 555 - 888 - hibrido - vexado - Giro Turismo - {vexado_company.company_internal_id}"


def test_trecho_classe_str(vexado_company):
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company, grupo_internal_id=888)
    trecho_classe = baker.make("rodoviaria.TrechoClasse", grupo=grupo, trechoclasse_internal_id=555)
    assert str(trecho_classe) == f"555 - 888 - hibrido - vexado - Giro Turismo - {vexado_company.company_internal_id}"


def test_mapa_veiculo_str():
    mapa_veiculo_1_andar = baker.make(
        "rodoviaria.MapaVeiculo",
        has_dois_andares=False,
        quantidade_poltronas_primeiro_andar=32,
    )
    mapa_veiculo_2_andares = baker.make(
        "rodoviaria.MapaVeiculo",
        has_dois_andares=True,
        quantidade_poltronas_primeiro_andar=12,
        quantidade_poltronas_segundo_andar=15,
    )
    assert str(mapa_veiculo_1_andar) == "32 Poltronas"
    assert str(mapa_veiculo_2_andares) == "12 Inferior / 15 Superior"

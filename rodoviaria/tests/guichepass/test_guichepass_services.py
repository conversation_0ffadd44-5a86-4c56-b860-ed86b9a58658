from rodoviaria.api.guichepass import endpoints, services


def test_get_api_token_from_client_request(mock_guichepass_client_from_undefined_company, mock_guichepass_login):
    token = services.get_api_token_from_client(mock_guichepass_client_from_undefined_company)
    assert token["access_token"] == "ACCESS_TOKEN"  # noqa: S105
    assert token["new_login"] is True


def test_get_api_token_from_client_get_from_cache_on_second_call_and_from_api_if_forced(
    mock_guichepass_client_from_undefined_company, mock_guichepass_login
):
    # first call, get Token from api
    token = services.get_api_token_from_client(mock_guichepass_client_from_undefined_company)
    assert token["access_token"] == "ACCESS_TOKEN"  # noqa: S105
    assert token["new_login"] is True
    assert mock_guichepass_login.call_count == 1

    # second call get cached Token
    token = services.get_api_token_from_client(mock_guichepass_client_from_undefined_company)
    assert mock_guichepass_login.call_count == 1

    # But if forced to renew, gets from api again
    token = services.get_api_token_from_client(mock_guichepass_client_from_undefined_company, force_renew=True)
    assert mock_guichepass_login.call_count == 2


def test_retry_if_login_failed(
    mock_guichepass_anonymous_login,
    mock_poltronas_insuficientes,
    mock_failed_and_succes_login,
    guiche_trechoclasses,
    guichepass_api,
):
    trechoclasse_internal_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    resp = guichepass_api.get_map_poltronas(trechoclasse_id=trechoclasse_internal_id)
    mock_failed_and_succes_login.assert_call_count(endpoints.EfetuaLoginConfig(guichepass_api.login).url, 2)
    mock_poltronas_insuficientes.assert_call_count(endpoints.RetornaPoltronasConfig(guichepass_api.login).url, 1)
    assert resp == {1: "ocupada", 2: "livre", 3: "livre"}

import pytest
from pydantic import ValidationError

from rodoviaria.api.guichepass import models


def teste_bloquear_poltrona_form():
    entrada = {
        "origin": 1,
        "destination": 2,
        "date": "2020-01-01",
        "service": "870-1",
        "busCompany": 1,
        "seat": 4,
    }

    # ok
    form_dict = models.BloquearPoltronaForm(**entrada).dict()
    assert form_dict["origin_id"] == 1
    assert form_dict["destination_id"] == 2
    assert form_dict["date"] == "2020-01-01"
    assert form_dict["service"] == "870-1"
    assert form_dict["bus_company"] == 1
    assert form_dict["seat"] == 4

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("seat")
        models.BloquearPoltronaForm(**entrada)


def teste_retorna_poltronas_form():
    entrada = {
        "origin": 1,
        "destination": 2,
        "date": "2020-01-01",
        "service": "870-1",
        "busCompany": 1,
    }

    # ok
    form_dict = models.RetornaPoltronasForm(**entrada).dict()
    assert form_dict["origin_id"] == 1
    assert form_dict["destination_id"] == 2
    assert form_dict["date"] == "2020-01-01"
    assert form_dict["service"] == "870-1"
    assert form_dict["bus_company"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("busCompany")
        models.RetornaPoltronasForm(**entrada)


def teste_confirmar_venda_form():
    entrada = {
        "name": "str",
        "id": 2,
        "price": 35,
        "discount": 3.50,
        "document": "897.823.840-89",
    }
    form_dict = models.ConfirmarVendaForm(**entrada).dict()
    assert form_dict["name"] == "str"
    assert form_dict["reserve_id"] == 2
    assert form_dict["price"] == 35
    assert form_dict["discount"] == 3.50
    assert form_dict["document"] == "897.823.840-89"


def teste_cancelar_venda_form():
    entrada = {"id": 2}
    form_dict = models.CancelarVendaForm(**entrada).dict()
    assert form_dict["reserve_id"] == 2


def test_consultar_bilhete_form():
    entrada = {"id": 24}
    form_dict = models.ConsultarBilheteForm(**entrada)
    assert form_dict.reserve_id == 24

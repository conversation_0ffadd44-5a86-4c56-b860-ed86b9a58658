import pytest
import responses

import rodoviaria.api.guichepass.endpoints as endpoints
from rodoviaria.api.guichepass.api import GuichepassAPI
from rodoviaria.forms.staff_forms import (
    GuichepassAnonymousLogin,
    GuichepassUndefinedCompany,
    GuichepassUndefinedCompanyLogin,
)
from rodoviaria.models import GuichepassLogin
from rodoviaria.tests.guichepass import mocker


@pytest.fixture
def guichepass_api(guiche_login):
    return GuichepassAPI(guiche_login.company)


@pytest.fixture
def mock_guichepass_login(requests_mock, guiche_login):
    return requests_mock.add(
        responses.POST,
        endpoints.EfetuaLoginConfig(guiche_login).url,
        json=mocker.MockLogin.response(),
    )


@pytest.fixture
def mock_guichepass_anonymous_login():
    return GuichepassAnonymousLogin.parse_obj(
        {
            "url_base": GuichepassLogin.DEFAULT_URL_BASE,
            "username": "buser",
            "password": "buser",
            "client_id": "WEB_SALE",
        }
    )


@pytest.fixture
def mock_guichepass_client_from_undefined_company(mock_guichepass_anonymous_login):
    company = GuichepassUndefinedCompany(url_base=mock_guichepass_anonymous_login.url_base)
    return GuichepassUndefinedCompanyLogin(**mock_guichepass_anonymous_login.dict(), company=company)


@pytest.fixture
def mock_unauthorized_login(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.EfetuaLoginConfig(guiche_login).url,
        status=401,
    )


@pytest.fixture
def mock_failed_and_succes_login(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.EfetuaLoginConfig(guiche_login).url,
        status=401,
    )
    requests_mock.add(
        responses.POST,
        endpoints.EfetuaLoginConfig(guiche_login).url,
        json=mocker.MockLogin.response(),
    )
    return requests_mock


@pytest.fixture
def mock_login_service_unavailable(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.EfetuaLoginConfig(guiche_login).url,
        status=503,
    )


@pytest.fixture
def mock_cancela_venda(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(guiche_login).url,
        json=mocker.MockCancelarVenda.response(),
    )


@pytest.fixture
def mock_nao_cancela_venda_com_ticket_impresso(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(guiche_login).url,
        json=mocker.MockCancelarVenda.response_nao_cancela_ticket(),
        status=422,
    )


@pytest.fixture
def mock_bloquear_poltrona(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(guiche_login).url,
        json=mocker.MockBloquearPoltrona.response(),
    )


@pytest.fixture
def mock_confirma_venda(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaConfig(guiche_login).url,
        json=mocker.MockConfirmarVenda.response(),
    )
    return requests_mock


@pytest.fixture
def mock_confirma_venda_com_erro(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaConfig(guiche_login).url,
        json=mocker.MockConfirmarVenda.response_com_erro(),
        status=400,
    )


@pytest.fixture
def mock_bloquear_poltrona_com_erro(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(guiche_login).url,
        json=mocker.MockBloquearPoltrona.response_com_erro(),
        status=422,
    )


@pytest.fixture
def mock_get_poltronas_livres(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(guiche_login).url,
        json=mocker.MockGetPoltronasLivres.response(),
    )


@pytest.fixture
def mock_poltronas_insuficientes(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(guiche_login).url,
        json=mocker.MockGetPoltronasLivres.response_poltronas_insuficientes(),
    )
    return requests_mock


@pytest.fixture
def mock_buscar_poltronas_force_login(requests_mock, guiche_login):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(guiche_login).url,
        json={},
        status=401,
    )
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(guiche_login).url,
        json=mocker.MockGetPoltronasLivres.response_poltronas_insuficientes(),
    )


@pytest.fixture
def mock_tipos_onibus(requests_mock, guiche_login):
    requests_mock.add(
        responses.GET,
        endpoints.TiposOnibusConfig(guiche_login).url,
        json=mocker.MockTiposOnibus.response(),
    )


@pytest.fixture
def mock_atualiza_origens(requests_mock, guiche_login):
    requests_mock.add(
        responses.GET,
        endpoints.AtualizaOrigensConfig(guiche_login).url,
        json=mocker.MockAtualizaOrigens.response(),
    )


@pytest.fixture
@responses.activate
def mock_connection_error(requests_mock, guichepass_api):
    pass


@pytest.fixture
def mock_guichepass_consultar_empresas(requests_mock, guiche_login):
    return requests_mock.add(
        responses.GET,
        endpoints.ConsultarEmpresasConfig(guiche_login).url,
        json=mocker.ConsultarEmpresas.response(),
    )


@pytest.fixture
def mock_consultar_bilhete(requests_mock, guiche_login):
    return requests_mock.add(
        responses.POST,
        endpoints.ConsultarBilheteConfig(guiche_login).url,
        json=mocker.MockConsultarBilhete.response(),
    )


@pytest.fixture
def mock_buscar_corridas(requests_mock, guiche_login):
    return requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasConfig(guiche_login).url,
        json=mocker.MockBuscarCorridas.response(),
    )

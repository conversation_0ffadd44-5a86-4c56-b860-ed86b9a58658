from datetime import datetime, timedelta, timezone


class MockLogin:
    @staticmethod
    def response():
        return {
            "accessToken": "ACCESS_TOKEN",
            "refreshToken": "REFRESH_TOKEN",
            "accessTokenSso": "ACCESS_TOKEN_SSO",
            "refreshTokenSso": "REFRESH_TOKEN_SSO",
            "expiresAt": (datetime.now(timezone.utc) + timedelta(seconds=719)).isoformat(),
            "expiresSec": 719,
            "currentDate": "2021-04-08",
        }


class MockTiposOnibus:
    @staticmethod
    def response():
        return [
            {"name": "CONV", "id": 1},
            {"name": "EXEC", "id": 6},
            {"name": "EXP", "id": 4},
            {"name": "LEITO", "id": 5},
            {"name": "MEIA", "id": 7},
            {"name": "SEMI", "id": 3},
            {"name": "URB", "id": 2},
        ]


class MockBloquearPoltrona:
    @staticmethod
    def response():
        return {
            "origin": "RECIFE",
            "destination": "UBERLANDIA",
            "departure": "2021-02-24T06:00:00",
            "arrival": "2021-02-24T21:30:00",
            "service": "790",
            "busCompany": "2",
            "busType": "1",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "id": 62714,
            "seat": "3",
            "priceInfo": {
                "basePrice": 88,
                "insurancePrice": 0,
                "taxPrice": 0,
                "otherPrice": 0,
                "tollPrice": 0,
                "boardingPrice": 3.32,
                "commission": 0,
                "companyDiscount": 8.8,
                "discounts": [],
                "cancelationFee": None,
                "priceClassification": None,
                "priceWithoutInsurance": 82.52,
                "totalCompanyDiscount": 0,
                "originalPrice": 82.52,
                "originalPriceWithoutInsurance": 82.52,
                "priceWithBusCompanyDiscount": 82.52,
                "priceWithInsurance": 82.52,
                "price": 82.52,
                "totalDiscount": 0,
            },
            "alternativePrices": [],
            "status": "RESERVED",
            "name": None,
            "document": None,
            "ticket": None,
            "ticketNumber": None,
            "message": None,
            "metaData": {},
            "orderId": None,
            "buyerInfoId": None,
            "optInInsurance": False,
            "noSeatNumberRequired": False,
            "barCodes": None,
            "timestamp": "2021-02-24T10:15:49.000",
            "bpeInfo": None,
            "channel": None,
            "insuranceSelected": False,
        }

    @staticmethod
    def response_com_erro():
        return {
            "text": "Assento Reservado",
            "code": "reserved-seat.reserved",
            "status": "UNPROCESSABLE_ENTITY",
            "children": None,
            "properties": [{"name": "description", "value": "Assento Reservado", "type": "string"}],
            "timestamp": "2021-09-09T09:27:10.337",
        }


class MockCancelarVenda:
    @staticmethod
    def response():
        return {
            "origin": "RECIFE",
            "destination": "UBERLANDIA",
            "departure": "2021-02-24T20:00:00",
            "arrival": "2021-02-25T08:00:00",
            "service": "834",
            "busCompany": "1",
            "busType": "5",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "id": 62624,
            "seat": "3",
            "priceInfo": {
                "basePrice": 3.33,
                "insurancePrice": 0,
                "taxPrice": 0,
                "otherPrice": 0,
                "tollPrice": 0,
                "boardingPrice": 3.32,
                "commission": 0,
                "companyDiscount": 0,
                "discounts": [],
                "cancelationFee": 0,
                "price": 6.65,
                "totalCompanyDiscount": 0,
                "originalPriceWithoutInsurance": 6.65,
                "priceWithBusCompanyDiscount": 6.65,
                "priceWithoutInsurance": 6.65,
                "originalPrice": 6.65,
                "totalDiscount": 0,
                "priceWithInsurance": 6.65,
            },
            "alternativePrices": [],
            "status": "CANCELED",
            "name": "ALINE CAPPELLI -FORCE",
            "document": "4572080",
            "ticket": None,
            "ticketNumber": "1T5X6M",
            "message": None,
            "metaData": {},
            "orderId": None,
            "buyerInfoId": None,
            "optInInsurance": False,
            "noSeatNumberRequired": False,
            "barCodes": [
                {
                    "id": None,
                    "value": "1T5X6M",
                    "application": "BOARDING_PASS",
                    "standard": "CODE_128",
                }
            ],
            "timestamp": "2021-02-24T10:22:15.000",
            "bpeInfo": None,
            "channel": None,
            "insuranceSelected": False,
        }

    @staticmethod
    def response_nao_cancela_ticket():
        return {
            "text": None,
            "code": "cancellation.ticket_printed",
            "status": "UNPROCESSABLE_ENTITY",
            "children": None,
            "properties": [{"name": "transactionId", "value": "144398", "type": "string"}],
            "timestamp": "2023-05-03T18:10:47.852",
        }


class MockConfirmarVenda:
    @staticmethod
    def response():
        return {
            "origin": "RECIFE",
            "destination": "UBERLANDIA",
            "departure": "2021-02-24T20:00:00",
            "arrival": "2021-02-25T08:00:00",
            "service": "834",
            "busCompany": "1",
            "busType": "5",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "id": 62714,
            "seat": "4",
            "priceInfo": {
                "basePrice": 3.33,
                "insurancePrice": 0,
                "taxPrice": 0,
                "otherPrice": 0,
                "tollPrice": 0,
                "boardingPrice": 3.32,
                "commission": 0,
                "companyDiscount": 0,
                "discounts": [],
                "cancelationFee": 0,
                "price": 6.65,
                "totalDiscount": 0,
                "priceWithoutInsurance": 6.65,
                "priceWithInsurance": 6.65,
                "totalCompanyDiscount": 0,
                "originalPrice": 6.65,
                "originalPriceWithoutInsurance": 6.65,
                "priceWithBusCompanyDiscount": 6.65,
            },
            "alternativePrices": [],
            "status": "CONFIRMED",
            "name": "JULIA CAPPELI",
            "document": "4555688",
            "ticket": None,
            "ticketNumber": "1T7QRV",
            "message": None,
            "metaData": {},
            "orderId": None,
            "buyerInfoId": None,
            "optInInsurance": False,
            "noSeatNumberRequired": False,
            "barCodes": [
                {
                    "id": None,
                    "value": "1T7QRV",
                    "application": "BOARDING_PASS",
                    "standard": "CODE_128",
                }
            ],
            "timestamp": "2021-02-24T13:58:35.000",
            "bpeInfo": {
                "bpeQrCode": "https://dfe-portal.svrs.rs.gov.br/bpe/qrCode?chBPe=26210224441891000180630000000201961061129410&tpAmb=2",
                "platform": None,
                "prefix": "6868",
                "line": "RECIFE / SAO PAULO",
                "totalAmount": 6.65,
                "discountAmount": 0,
                "paymentAmount": 6.65,
                "bpeAccessKey": "BPe26210224441891000180630000000201961061129410",
                "contactTel": None,
                "specialContactTel": None,
                "bpeQueryUrl": "https://bpe.svrs.rs.gov.br/ws/bpeConsulta/bpeConsulta.asmx",
                "paymentMethod": "Cartão de Crédito",
                "paymentMethodAmount": 6.65,
                "changeAmount": 0,
                "discountType": None,
                "bpeNumber": "20196",
                "bpeSeries": "0",
                "bpeAuthProtocol": "326210000001975",
                "bpeAuthorizationDate": "2021-02-24T13:58:38-03:00",
                "systemNumber": "62714",
                "otherTributes": "Tributos Totais Incidentes(Lei Federal 12.741/2012) - R$1.13",
                "contingency": False,
                "anttOriginCode": None,
                "anttDestinationCode": None,
                "bpeMonitriipCode": None,
                "taxAmount": 0,
                "tollAmount": 0,
                "boardingTaxAmount": 3.32,
                "insuranceAmount": 0,
                "othersAmounts": 0,
                "agencyHeaderDistrict": None,
                "agencyHeaderCity": None,
                "agencyHeaderCnpj": None,
                "agencyHeaderAddress": None,
                "agencyHeaderNumber": None,
                "agencyHeaderCompanyName": None,
                "agencyHeaderState": None,
                "emitterHeaderDistrict": "Curado",
                "emitterHeaderPostalCode": "",
                "emitterHeaderCity": "RECIFE",
                "emitterHeaderCnpj": "24441891000180",
                "emitterHeaderAddress": "Rua Dr. George Wiliam Butler",
                "emitterHeaderStateRegistration": "",
                "emitterHeaderNumber": "863",
                "emitterHeaderCompanyName": "Rodoviária Borborema Ltda",
                "emitterHeaderState": "PE",
                "optionalMessage": None,
            },
            "channel": None,
            "insurancePolicy": None,
            "insuranceSelected": False,
        }

    @staticmethod
    def response_com_erro():
        return {
            "text": "Reserva não encontrada",
            "code": "reservation_not_found",
            "status": "UNPROCESSABLE_ENTITY",
            "children": None,
            "properties": [{"name": "description", "value": "Reserva não encontrada", "type": "string"}],
            "timestamp": "2021-09-09T09:27:10.337",
        }


class MockGetPoltronasLivres:
    @staticmethod
    def request():
        return {
            "origin": "42",
            "destination": "121",
            "date": "2021-02-24",
            "busCompany": "2",
            "service": "790-1",
        }

    @staticmethod
    def request_for_n_poltronas(n):
        return {
            "origin": "42",
            "destination": "121",
            "date": "2021-02-24",
            "busCompany": "2",
            "service": "790-1",
            "NumeroPoltronas": n,
        }

    @staticmethod
    def response_poltronas_insuficientes():
        return {
            "seats": [
                {
                    "status": "RESERVED",
                    "x": 0,
                    "y": 4,
                    "z": 0,
                    "number": "1",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                },
                {
                    "status": "FREE",
                    "x": 0,
                    "y": 3,
                    "z": 0,
                    "number": "2",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                },
                {
                    "status": "FREE",
                    "x": 0,
                    "y": 1,
                    "z": 0,
                    "number": "3",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                },
            ],
            "travel": {
                "origin": "121",
                "destination": "42",
                "departure": "2024-08-20T06:00:00",
                "arrival": "2024-08-20T20:50:00",
                "service": "870-1",
                "busCompany": "EXPRESSO VIRTUAL LTDA R B",
                "busType": "1",
                "operationType": "DEFAULT",
                "amenities": None,
                "distance": None,
                "stopover": False,
                "freeSeats": 2,
                "price": 305.42,
                "kind": None,
                "message": "",
                "hasIntinerary": False,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 0,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "expirationDate": None,
            },
            "priceInfo": {
                "basePrice": 88,
                "insurancePrice": 0,
                "taxPrice": 0,
                "otherPrice": 0,
                "tollPrice": 0,
                "boardingPrice": 3.32,
                "commission": 0,
                "companyDiscount": 8.8,
                "discounts": [],
                "cancelationFee": None,
                "priceWithoutInsurance": 82.52,
                "totalCompanyDiscount": 0,
                "originalPrice": 82.52,
                "originalPriceWithoutInsurance": 82.52,
                "priceWithBusCompanyDiscount": 82.52,
                "priceWithInsurance": 82.52,
                "price": 82.52,
                "totalDiscount": 0,
            },
            "listQuotas": [],
        }

    @staticmethod
    def response():
        return {
            "seats": [
                {
                    "status": "RESERVED",
                    "x": 0,
                    "y": 4,
                    "z": 0,
                    "number": "1",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                },
                {
                    "status": "RESERVED",
                    "x": 0,
                    "y": 3,
                    "z": 0,
                    "number": "2",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                },
                {
                    "status": "RESERVED",
                    "x": 0,
                    "y": 1,
                    "z": 0,
                    "number": "3",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                },
                {
                    "status": "FREE",
                    "x": 0,
                    "y": 0,
                    "z": 0,
                    "number": "4",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        },
                        {
                            "id": 8,
                            "description": "PNE 100%",
                            "salesStrategy": "DEFICIENT",
                            "priceValue": 0.00,
                            "isNominalSale": True,
                            "allowsPromotion": False,
                            "code": "7",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        },
                    ],
                },
                {
                    "status": "FREE",
                    "x": 1,
                    "y": 4,
                    "z": 0,
                    "number": "6",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        },
                        {
                            "id": 8,
                            "description": "PNE 100%",
                            "salesStrategy": "DEFICIENT",
                            "priceValue": 0.00,
                            "isNominalSale": True,
                            "allowsPromotion": False,
                            "code": "7",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        },
                    ],
                },
                {
                    "status": "FREE",
                    "x": 1,
                    "y": 3,
                    "z": 0,
                    "number": "5",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                },
                {
                    "status": "FREE",
                    "x": 1,
                    "y": 1,
                    "z": 0,
                    "number": "7",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                },
                {
                    "status": "FREE",
                    "x": 1,
                    "y": 0,
                    "z": 0,
                    "number": "8",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        },
                        {
                            "id": 6,
                            "description": "50% Idoso ANTT",
                            "salesStrategy": "ELDER_50",
                            "priceValue": 155.42,
                            "isNominalSale": False,
                            "allowsPromotion": False,
                            "code": "Idoso 50",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        },
                    ],
                },
                {"status": "FREE", "x": 2, "y": 3, "z": 0, "number": "9", "description": "CONV", "ticketType": []},
                {
                    "status": "FREE",
                    "x": 2,
                    "y": 1,
                    "z": 0,
                    "number": "10",
                    "description": "CONV",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "PASSAGEM",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 305.42,
                            "isNominalSale": False,
                            "allowsPromotion": True,
                            "code": "1",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        },
                        {
                            "id": 6,
                            "description": "50% Idoso ANTT",
                            "salesStrategy": "ELDER_50",
                            "priceValue": 155.42,
                            "isNominalSale": False,
                            "allowsPromotion": False,
                            "code": "Idoso 50",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        },
                    ],
                },
            ],
            "travel": {
                "origin": "42",
                "destination": "121",
                "departure": "2021-02-24T06:00:00",
                "arrival": "2021-02-24T21:30:00",
                "service": "790-1",
                "busCompany": "VIRTUAL TRANSPORTES LTDA L C",
                "busType": "1",
                "operationType": "DEFAULT",
                "amenities": None,
                "distance": None,
                "stopover": False,
                "freeSeats": 11,
                "price": 82.52,
                "kind": None,
                "message": "",
                "hasIntinerary": False,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 8.8,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "expirationDate": None,
            },
            "priceInfo": {
                "basePrice": 88,
                "insurancePrice": 0,
                "taxPrice": 0,
                "otherPrice": 0,
                "tollPrice": 0,
                "boardingPrice": 3.32,
                "commission": 0,
                "companyDiscount": 8.8,
                "discounts": [],
                "cancelationFee": None,
                "priceWithoutInsurance": 82.52,
                "totalCompanyDiscount": 0,
                "originalPrice": 82.52,
                "originalPriceWithoutInsurance": 82.52,
                "priceWithBusCompanyDiscount": 82.52,
                "priceWithInsurance": 82.52,
                "price": 82.52,
                "totalDiscount": 0,
            },
            "listQuotas": [
                {
                    "id": 13,
                    "description": "Idoso ANTT 100%",
                    "salesStrategy": "ELDER",
                    "priceValue": 0,
                    "isNominalSale": False,
                    "allowsPromotion": False,
                    "code": "Idoso 100 ANTT",
                    "validateRule": False,
                    "numberSeat": 0,
                    "amount": 2,
                    "quotType": "DYNAMIC_QUOTAS",
                },
                {
                    "id": 12,
                    "description": "Jovem 50%",
                    "salesStrategy": "YOUNG_LOW_INCOME_50",
                    "priceValue": 0,
                    "isNominalSale": False,
                    "allowsPromotion": False,
                    "code": "Jovem 50",
                    "validateRule": False,
                    "numberSeat": 0,
                    "amount": 2,
                    "quotType": "DYNAMIC_QUOTAS",
                },
                {
                    "id": 11,
                    "description": "Jovem 100%",
                    "salesStrategy": "YOUNG_LOW_INCOME",
                    "priceValue": 0,
                    "isNominalSale": False,
                    "allowsPromotion": False,
                    "code": "Jovem 100",
                    "validateRule": False,
                    "numberSeat": 0,
                    "amount": 2,
                    "quotType": "DYNAMIC_QUOTAS",
                },
                {
                    "id": 8,
                    "description": "PNE 100%",
                    "salesStrategy": "DEFICIENT",
                    "priceValue": 0,
                    "isNominalSale": False,
                    "allowsPromotion": False,
                    "code": "PNE 100",
                    "validateRule": False,
                    "numberSeat": 4,
                    "amount": 1,
                    "quotType": "SEAT_MARKED_QUOTAS",
                },
                {
                    "id": 8,
                    "description": "PNE 100%",
                    "salesStrategy": "DEFICIENT",
                    "priceValue": 0,
                    "isNominalSale": False,
                    "allowsPromotion": False,
                    "code": "PNE 100",
                    "validateRule": False,
                    "numberSeat": 6,
                    "amount": 1,
                    "quotType": "SEAT_MARKED_QUOTAS",
                },
            ],
        }


class MockAtualizaOrigens:
    @staticmethod
    def response():
        return [
            {
                "id": 129,
                "name": "ABADIA DOS DOURADOS",
                "city": "ABADIA DOS DOURADOS",
                "state": "MG",
                "ibgeCityCode": "3100104",
            },
            {
                "id": 135,
                "name": "SÃO PAULO",
                "city": "SÃO PAULO",
                "state": "SP",
                "ibgeCityCode": "2611606",
            },
        ]


class ConsultarEmpresas:
    @staticmethod
    def response():
        return [
            {
                "id": 2,
                "name": "VIRTUAL TRANSPORTES LTDA L C",
                "cnpj": "02.787.690/0001-04",
            },
            {
                "id": 1,
                "name": "EXPRESSO VIRTUAL LTDA R B",
                "cnpj": "37.607.467/0001-04",
            },
        ]


class MockConsultarBilhete:
    @staticmethod
    def response():
        return {
            "origin": "UBERLANDIA",
            "destination": "RECIFE",
            "departure": "2021-04-10T06:00:00",
            "arrival": "2021-04-10T20:50:00",
            "service": "870",
            "busCompany": "1",
            "busType": "1",
            "operationType": "DEFAULT",
            "amenities": None,
            "distance": None,
            "stopover": False,
            "id": 62251,
            "seat": 24,
            "priceInfo": {
                "basePrice": 300.00,
                "insurancePrice": 0.00,
                "taxPrice": 0,
                "otherPrice": 0.00,
                "tollPrice": 0.00,
                "boardingPrice": 5.42,
                "commission": 0,
                "companyDiscount": 0.00,
                "discounts": [],
                "cancelationFee": 0.00,
                "totalDiscount": 0,
                "price": 305.42,
                "priceWithoutInsurance": 305.42,
                "totalCompanyDiscount": 0,
                "originalPrice": 305.42,
                "originalPriceWithoutInsurance": 305.42,
                "priceWithBusCompanyDiscount": 305.42,
                "priceWithInsurance": 305.42,
            },
            "alternativePrices": [],
            "status": "CANCELED",
            "name": "FULANO DE TAL",
            "document": "1234567",
            "ticket": None,
            "ticketNumber": "1SPBVQ",
            "message": None,
            "metaData": None,
            "orderId": None,
            "buyerInfoId": None,
            "optInInsurance": False,
            "noSeatNumberRequired": False,
            "barCodes": [{"id": None, "value": "1SPBVQ", "application": "BOARDING_PASS", "standard": "CODE_128"}],
            "timestamp": "2021-04-07T18:56:45.000",
            "bpeInfo": {
                "bpeQrCode": "https://dfe-portal.svrs.rs.gov.br/bpe/qrCode?chBPe=26210224441891000180630000000201961061129410&tpAmb=2",
                "platform": None,
                "prefix": "6868",
                "line": "RECIFE / SAO PAULO",
                "totalAmount": 6.65,
                "discountAmount": 0,
                "paymentAmount": 6.65,
                "bpeAccessKey": "BPe26210224441891000180630000000201961061129410",
                "contactTel": None,
                "specialContactTel": None,
                "bpeQueryUrl": "https://bpe.svrs.rs.gov.br/ws/bpeConsulta/bpeConsulta.asmx",
                "paymentMethod": "Cartão de Crédito",
                "paymentMethodAmount": 6.65,
                "changeAmount": 0,
                "discountType": None,
                "bpeNumber": "20196",
                "bpeSeries": "0",
                "bpeAuthProtocol": "326210000001975",
                "bpeAuthorizationDate": "2021-02-24T13:58:38-03:00",
                "systemNumber": "62714",
                "otherTributes": "Tributos Totais Incidentes(Lei Federal 12.741/2012) - R$1.13",
                "contingency": False,
                "anttOriginCode": None,
                "anttDestinationCode": None,
                "bpeMonitriipCode": None,
                "taxAmount": 0,
                "tollAmount": 0,
                "boardingTaxAmount": 3.32,
                "insuranceAmount": 0,
                "othersAmounts": 0,
                "agencyHeaderDistrict": None,
                "agencyHeaderCity": None,
                "agencyHeaderCnpj": None,
                "agencyHeaderAddress": None,
                "agencyHeaderNumber": None,
                "agencyHeaderCompanyName": None,
                "agencyHeaderState": None,
                "emitterHeaderDistrict": "Curado",
                "emitterHeaderPostalCode": "",
                "emitterHeaderCity": "RECIFE",
                "emitterHeaderCnpj": "24441891000180",
                "emitterHeaderAddress": "Rua Dr. George Wiliam Butler",
                "emitterHeaderStateRegistration": "",
                "emitterHeaderNumber": "863",
                "emitterHeaderCompanyName": "Rodoviária Borborema Ltda",
                "emitterHeaderState": "PE",
                "optionalMessage": None,
            },
            "channel": None,
            "insurancePolicy": None,
            "insuranceSelected": False,
        }


class MockBuscarCorridas:
    @staticmethod
    def response():
        return [
            {
                "origin": "121",
                "destination": "42",
                "departure": "2024-06-23T06:00:00",
                "arrival": "2024-06-23T20:50:00",
                "service": "870-1",
                "busCompany": "1",
                "busType": "1",
                "operationType": "DEFAULT",
                "amenities": None,
                "distance": None,
                "stopover": False,
                "freeSeats": 37,
                "price": 305.42,
                "kind": None,
                "message": "",
                "hasIntinerary": False,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 0,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "valueToAdd": None,
                "expirationDate": None,
                "inTransit": False,
            },
            {
                "origin": "121",
                "destination": "42",
                "departure": "2024-06-23T22:00:00",
                "arrival": "2024-06-24T12:40:00",
                "service": "817-1",
                "busCompany": "1",
                "busType": "1",
                "operationType": "DEFAULT",
                "amenities": None,
                "distance": None,
                "stopover": False,
                "freeSeats": 37,
                "price": 305.42,
                "kind": None,
                "message": "",
                "hasIntinerary": False,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 0,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "valueToAdd": None,
                "expirationDate": None,
                "inTransit": False,
            },
        ]

from django.db import connections
from model_bakery import baker

from rodoviaria.api.guichepass.auth import GuichepassAuth


def test_get_login_from_company(guichepass_api):
    company = baker.make("rodoviaria.Company")
    vexado_login = baker.make("rodoviaria.GuichepassLogin", company=company)

    auth_obj = GuichepassAuth.from_company(company)
    assert auth_obj.client == vexado_login
    assert auth_obj.company == company
    assert auth_obj.client_id == vexado_login.client_id
    assert auth_obj._token is None


def test_get_login_from_client(guichepass_api):
    company = baker.make("rodoviaria.Company")
    vexado_login = baker.make("rodoviaria.GuichepassLogin", company=company)

    auth_obj = GuichepassAuth.from_client(vexado_login)
    assert auth_obj.client == vexado_login
    assert auth_obj.company == company
    assert auth_obj.client_id == vexado_login.client_id
    assert auth_obj._token is None


def test_auth_grava_header_request_from_company(mocker, django_assert_num_queries):
    mocker.patch.object(GuichepassAuth, "refresh_token", return_value="auth_token_guichepass")
    company = baker.make("rodoviaria.Company", url_base="http://guichepass.com.br")
    baker.make(
        "rodoviaria.GuichepassLogin", company=company, client_id="102030", password="buser", username="guichepass"
    )
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        auth = GuichepassAuth.from_company(company)

    request = mocker.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["X-Authorization"] == "auth_token_guichepass"
    assert request.headers["ClientId"] == "102030"


def test_auth_grava_header_request_from_client(mocker, django_assert_num_queries):
    """Auth a partir do client não executa query no bd."""
    mocker.patch.object(GuichepassAuth, "refresh_token", return_value="auth_token_guichepass")
    company = baker.make("rodoviaria.Company", url_base="http://guichepass.com.br")
    client_login = baker.make(
        "rodoviaria.GuichepassLogin", company=company, client_id="102030", password="buser", username="guichepass"
    )
    with django_assert_num_queries(0, connection=connections["rodoviaria"]):
        auth = GuichepassAuth.from_client(client_login)
    request = mocker.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["X-Authorization"] == "auth_token_guichepass"
    assert request.headers["ClientId"] == "102030"

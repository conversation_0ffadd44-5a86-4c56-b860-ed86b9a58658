import datetime
import json
from decimal import Decimal as D
from types import SimpleNamespace
from unittest import mock

import pytest
import responses
import time_machine
from model_bakery import baker
from pydantic import parse_obj_as
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz
from rodoviaria import views
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.guichepass import api as guichepass_api
from rodoviaria.api.guichepass import endpoints, models
from rodoviaria.api.guichepass.models import Localidade
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import BloquearPoltronasResponse, VerificarPoltronaForm
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.forms.staff_forms import GuichepassAnonymousLogin
from rodoviaria.models.core import Cidade, LocalEmbarque, Passagem, TipoAssento, TrechoClasse
from rodoviaria.service import reserva_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import (
    RodoviariaConnectionError,
    RodoviariaOTAException,
    RodoviariaOverbookingException,
    RodoviariaUnauthorizedError,
)
from rodoviaria.tests.guichepass import mocker
from rodoviaria.tests.utils_testes import _comprar_params
from rodoviaria.views_schemas import ListaEmpresasAPIParams


@pytest.fixture
def mock_cache_poltrona(mocker, guichepass_api):
    reserva_id = 1234
    preco = D("102.50")
    mocker.patch.object(
        guichepass_api.cache,
        "get_poltrona_bloqueada_cache",
        return_value=models.InfosCacheaveisBloqueioPoltrona(reserva_id=reserva_id, preco=preco),
    )
    return {"reserva_id": reserva_id, "preco": preco}


def test_get_poltronas_livres_uma_poltrona(mock_guichepass_login, mock_get_poltronas_livres, guichepass_api):
    params = mocker.MockGetPoltronasLivres.request()
    params = models.RetornaPoltronasForm.parse_obj(params)
    resp = guichepass_api.get_poltronas_livres(params, numero_poltronas=1)
    assert isinstance(resp, list)
    assert len(resp) == 1


def test_get_poltronas_livres_varias_poltronas(mock_guichepass_login, mock_get_poltronas_livres, guichepass_api):
    params = mocker.MockGetPoltronasLivres.request()
    params = models.RetornaPoltronasForm.parse_obj(params)
    resp = guichepass_api.get_poltronas_livres(params, numero_poltronas=2)
    assert isinstance(resp, list)
    assert len(resp) == 2


def test_get_poltronas_livres_poltronas_insuficientes(mock_guichepass_login, mock_get_poltronas_livres, guichepass_api):
    params = mocker.MockGetPoltronasLivres.request()
    params = models.RetornaPoltronasForm.parse_obj(params)
    with pytest.raises(
        RodoviariaOverbookingException,
        match=r"Apenas 11 poltronas disponíveis para esta viagem",
    ):
        guichepass_api.get_poltronas_livres(params, numero_poltronas=12)


def test_connection_error(mock_login_service_unavailable, guichepass_api):
    params = mocker.MockGetPoltronasLivres.request()
    params = models.RetornaPoltronasForm.parse_obj(params)
    err_msg = r"^guichepass .* connection error$"
    with pytest.raises(RodoviariaConnectionError, match=err_msg):
        guichepass_api.get_poltronas_livres(params, numero_poltronas=1)


def test_reserva_dict_to_comprar_params_com_rg(guichepass_api, guiche_trechoclasses):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    reserva_id = 1234
    preco = guiche_trechoclasses.ida.preco_rodoviaria
    guichepass_api.cache.set_poltrona_bloqueada_cache(
        trecho_classe_id=trechoclasse_id,
        poltrona=params.poltronas[0],
        cache_value=models.InfosCacheaveisBloqueioPoltrona(
            reserva_id=reserva_id, preco=D("150"), categoria_external_id=10
        ),
    )
    reserva_params = guichepass_api._reserva_dict_to_comprar_params(params)
    assert reserva_params[0]["confirmar_venda_form"].document == params.passageiros[0].rg_number
    assert reserva_params[0]["confirmar_venda_form"].reserve_id == reserva_id
    assert reserva_params[0]["confirmar_venda_form"].birthdate == params.passageiros[0].birthday.strftime("%Y-%m-%m")
    assert reserva_params[0]["confirmar_venda_form"].ticketTypeId is None
    assert reserva_params[0]["confirmar_venda_form"].price == preco
    assert reserva_params[0]["passagem"].preco_rodoviaria == preco


def test_reserva_dict_to_comprar_params_com_categoria_especial(guichepass_api, guiche_trechoclasses):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    params.categoria_especial = Passagem.CategoriaEspecial.IDOSO_50
    reserva_id = 1234
    preco = D("105.20")
    guichepass_api.cache.set_poltrona_bloqueada_cache(
        trecho_classe_id=trechoclasse_id,
        poltrona=params.poltronas[0],
        cache_value=models.InfosCacheaveisBloqueioPoltrona(
            reserva_id=reserva_id, categoria_external_id=10, preco=preco
        ),
    )
    reserva_params = guichepass_api._reserva_dict_to_comprar_params(params)
    assert reserva_params[0]["confirmar_venda_form"].reserve_id == reserva_id
    assert reserva_params[0]["confirmar_venda_form"].ticketTypeId == 10
    assert reserva_params[0]["confirmar_venda_form"].price == preco
    assert reserva_params[0]["passagem"].preco_rodoviaria == preco


def test_reserva_dict_to_comprar_params_extra_poltronas(guichepass_api, guiche_trechoclasses):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    params.categoria_especial = Passagem.CategoriaEspecial.IDOSO_50
    reserva_id = 1234
    preco = D("105.20")
    params.extra_poltronas = models.InfosCacheaveisBloqueioPoltrona(
        reserva_id=reserva_id, categoria_external_id=10, preco=preco
    )
    reserva_params = guichepass_api._reserva_dict_to_comprar_params(params)
    assert reserva_params[0]["confirmar_venda_form"].reserve_id == reserva_id
    assert reserva_params[0]["confirmar_venda_form"].ticketTypeId == 10
    assert reserva_params[0]["confirmar_venda_form"].price == preco
    assert reserva_params[0]["passagem"].preco_rodoviaria == preco


def test_reserva_dict_to_comprar_params_sem_rg_com_cpf(mock_cache_poltrona, guichepass_api, guiche_trechoclasses):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    params.passageiros[0].rg_number = ""
    reserva_params = guichepass_api._reserva_dict_to_comprar_params(params)
    assert reserva_params[0]["confirmar_venda_form"].document == params.passageiros[0].cpf


def test_reserva_dict_to_comprar_params_sem_rg_sem_cpf(guichepass_api, guiche_trechoclasses, mock_cache_poltrona):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    params.passageiros[0].rg_number = ""
    params.passageiros[0].cpf = ""
    reserva_params = guichepass_api._reserva_dict_to_comprar_params(params)
    assert reserva_params[0]["confirmar_venda_form"].document == ""


def test_reserva_dict_to_comprar_params_com_rg_muito_grande(guichepass_api, guiche_trechoclasses, mock_cache_poltrona):
    rg = "292939201029391098582931029192"
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    params.passageiros[0].rg_number = rg
    params.passageiros[0].cpf = ""
    reserva_params = guichepass_api._reserva_dict_to_comprar_params(params)
    assert reserva_params[0]["confirmar_venda_form"].document == rg[:20]


def test_comprar(mock_guichepass_login, guichepass_api, guiche_trechoclasses, mock_confirma_venda, mock_cache_poltrona):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    reserva_id = mock_cache_poltrona["reserva_id"]
    guichepass_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert not passagem.erro
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.localizador == "1T7QRV"
    assert passagem.numero_passagem == str(reserva_id)
    assert passagem.categoria_especial == Passagem.CategoriaEspecial.NORMAL
    assert "ticketTypeId" not in json.loads(mock_confirma_venda.calls[1].request.body)


def test_comprar_com_categoria_especial(
    mock_guichepass_login,
    guichepass_api,
    guiche_trechoclasses,
    mock_confirma_venda,
):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    params.categoria_especial = Passagem.CategoriaEspecial.IDOSO_100
    reserva_id = 1234
    preco = D("130")
    guichepass_api.cache.set_poltrona_bloqueada_cache(
        trecho_classe_id=trechoclasse_id,
        poltrona=params.poltronas[0],
        cache_value=models.InfosCacheaveisBloqueioPoltrona(
            reserva_id=reserva_id, categoria_external_id=99, preco=preco
        ),
    )
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    guichepass_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert not passagem.erro
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.localizador == "1T7QRV"
    assert passagem.numero_passagem == str(reserva_id)
    assert passagem.categoria_especial == Passagem.CategoriaEspecial.IDOSO_100
    request_body = json.loads(mock_confirma_venda.calls[1].request.body)
    assert request_body["ticketTypeId"] == 99
    assert request_body["price"] == str(preco)


def test_comprar_erro_confirmar_venda(
    mock_guichepass_login, guichepass_api, guiche_trechoclasses, mock_confirma_venda_com_erro, mock_cache_poltrona
):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    reserva_id = mock_cache_poltrona["reserva_id"]
    with pytest.raises(
        RodoviariaOTAException,
        match="reservation_not_found",
    ):
        guichepass_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)
    assert len(passagens) == 1
    passagem = passagens.first()
    assert passagem.trechoclasse_integracao.trechoclasse_internal_id == trechoclasse_id
    assert passagem.poltrona_external_id == 10
    assert passagem.buseiro_internal_id == 15
    assert passagem.numero_passagem == str(reserva_id)
    assert "reservation_not_found" in passagem.erro
    assert passagem.status == Passagem.Status.ERRO


def test_cancela_venda(mock_guichepass_login, guichepass_api, mock_cancela_venda):
    trecho_classe = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=4558441, external_id=50)
    passagem = baker.make(
        "rodoviaria.Passagem",
        poltrona_external_id=1,
        travel_internal_id=10,
        buseiro_internal_id=20,
        trechoclasse_integracao=trecho_classe,
        status=Passagem.Status.CONFIRMADA,
        numero_passagem="231293",
    )
    params = CancelaVendaForm(
        trechoclasse_id=trecho_classe.trechoclasse_internal_id,
        travel_id=passagem.travel_internal_id,
    )
    response = guichepass_api.cancela_venda(params)
    passagem.refresh_from_db()
    assert response[0]["status"] == "CANCELED"
    assert passagem.status == Passagem.Status.CANCELADA


def test_cancela_venda_sem_passagens_cadastradas(guichepass_api):
    trecho_classe = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=4558441, external_id=50)
    params = CancelaVendaForm(
        trechoclasse_id=trecho_classe.trechoclasse_internal_id,
        travel_id=42312,
    )
    response = guichepass_api.cancela_venda(params)
    assert response == []


def test_add_pax_na_lista_passageiros_viagem_pax_novo(
    mock_guichepass_login, mock_confirma_venda, guichepass_api, guiche_trechoclasses, mock_cache_poltrona
):
    params = _add_pax_params(guiche_trechoclasses.ida.trechoclasse_internal_id)
    params.poltrona = 1
    reserva_id = mock_cache_poltrona["reserva_id"]
    resp = guichepass_api.add_pax_na_lista_passageiros_viagem(params)
    passagens = Passagem.objects.filter(travel_internal_id=params.travel_id)
    assert passagens.count() == 1
    assert passagens.first().numero_passagem == str(reserva_id)
    assert resp == {"sucesso": True, "already_on_api": False}


def test_add_pax_na_lista_passageiros_viagem_sem_poltrona(
    mock_guichepass_login,
    mock_get_poltronas_livres,
    mock_bloquear_poltrona,
    mock_confirma_venda,
    guichepass_api,
    guiche_trechoclasses,
):
    params = _add_pax_params(guiche_trechoclasses.ida.trechoclasse_internal_id)
    resp = guichepass_api.add_pax_na_lista_passageiros_viagem(params)
    passagens = Passagem.objects.filter(travel_internal_id=params.travel_id, status=Passagem.Status.CONFIRMADA)
    assert passagens.count() == 1
    assert passagens.first().numero_passagem == "62714"
    assert resp == {"sucesso": True, "already_on_api": False}


def test_add_pax_na_lista_passageiros_viagem_pax_existente(guichepass_api, guiche_trechoclasses):
    passagem = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        buseiro_internal_id=1972911,
        travel_internal_id=4558441,
        trechoclasse_integracao__trechoclasse_internal_id=981238,
    )
    passagem_count = Passagem.objects.count()
    params = _add_pax_params(981238)
    resp = guichepass_api.add_pax_na_lista_passageiros_viagem(params)
    new_passagem_count = Passagem.objects.count()

    assert resp == {"sucesso": True, "already_on_api": True}
    assert new_passagem_count == passagem_count
    passagem.delete()


def _add_pax_params(trecho_classe_internal_id):
    params = SimpleNamespace()
    passenger = SimpleNamespace()

    passenger.buseiro_id = 1972911
    passenger.cpf = "85891347334"
    passenger.name = "Tony Calleri França"
    passenger.rg_number = "5368700"
    passenger.phone = "1291822398"
    passenger.buyer_cpf = "1291822398"

    params.id_destino = 651
    params.id_origem = 903
    params.passenger = passenger
    params.travel_id = 4558441
    params.trechoclasse_id = trecho_classe_internal_id
    params.valor_por_buseiro = 158.6
    return params


@pytest.mark.parametrize("horario_servico,answer", [("2022-11-10T13:15:00", 15), ("2022-11-10T12:45:00", -15)])
def test_get_diff_datetime_ida_in_minutes(guichepass_api, horario_servico, answer):
    datetime_ida = to_default_tz("2022-11-10T13:00:00")
    datetime_ida_servico = datetime.datetime.fromisoformat(horario_servico)
    diff = guichepass_api._get_diff_datetime_ida_in_minutes(datetime_ida, "America/Sao_Paulo", datetime_ida_servico)
    assert diff == answer


@pytest.mark.parametrize(
    "horario_servico,answer",
    [("2022-11-10T12:55:00", False), ("2022-11-10T13:05:00", True)],
)
def test_match_datetime_ida_servico(guichepass_api, horario_servico, answer):
    datetime_ida = to_default_tz("2022-11-10T13:00:00")
    datetime_ida_servico = datetime.datetime.fromisoformat(horario_servico)

    match = guichepass_api._match_datetime_ida_servico(datetime_ida, "America/Sao_Paulo", datetime_ida_servico)
    assert answer is match


def test_get_tipos_onibus(mock_guichepass_login, mock_tipos_onibus, guichepass_api):
    resp = guichepass_api._tipos_de_onibus()
    assert isinstance(resp, list)
    assert len(resp) == 7


def test_buscar_servico_retorno_not_found(guichepass_api):
    retorno = guichepass_api._parse_retorno_buscar_servico(mismatches=[])
    assert retorno == BuscarServicoForm(found=False, servicos=[])


def test_buscar_servico_retorno_mismatch(guichepass_api):
    timezone = "America/Sao_Paulo"
    servicos_proximos = [
        {
            "service": "2131",
            "price": "200.50",
            "departure": "2023-01-20T10:15:00",
            "busType": "1",
            "freeSeats": 13,
        },
        {
            "service": "4212",
            "price": "300.50",
            "departure": "2023-01-20T10:15:00",
            "busType": "2",
            "freeSeats": 13,
        },
    ]
    with mock.patch.object(
        guichepass_api,
        "_tipos_de_onibus",
        return_value=[{"id": "1", "name": "SEMI"}, {"id": "2", "name": "LEITO"}],
    ) as mock_tipos_de_onibus:
        retorno = guichepass_api._parse_retorno_buscar_servico([], timezone, servicos_proximos)
    mock_tipos_de_onibus.assert_called_once()
    assert retorno == BuscarServicoForm.parse_obj(
        {
            "found": False,
            "servicos": [
                {
                    "external_id": servicos_proximos[0]["service"],
                    "preco": D(servicos_proximos[0]["price"]),
                    "external_datetime_ida": to_tz(
                        datetime.datetime.strptime(servicos_proximos[0]["departure"], "%Y-%m-%dT%H:%M:%S"),
                        timezone,
                    ),
                    "classe": "SEMI",
                    "external_company_id": str(guichepass_api.company.company_external_id),
                    "vagas": servicos_proximos[0]["freeSeats"],
                    "provider_data": servicos_proximos[0],
                },
                {
                    "external_id": servicos_proximos[1]["service"],
                    "preco": D(servicos_proximos[1]["price"]),
                    "external_datetime_ida": to_tz(
                        datetime.datetime.strptime(servicos_proximos[1]["departure"], "%Y-%m-%dT%H:%M:%S"),
                        timezone,
                    ),
                    "classe": "LEITO",
                    "external_company_id": str(guichepass_api.company.company_external_id),
                    "vagas": servicos_proximos[1]["freeSeats"],
                    "provider_data": servicos_proximos[1],
                },
            ],
        }
    )


def test_buscar_servico_retorno_found(guichepass_api, mock_tipos_onibus, mock_guichepass_login):
    servico = {
        "service": "123-4",
        "freeSeats": "30",
        "price": "45.70",
        "departure": "2022-11-10T13:00:00",
        "busType": "1",
        "busCompany": "1",
        "companyDiscount": 0,
    }
    resp = guichepass_api._parse_retorno_buscar_servico(servico_encontrado=servico, timezone="America/Sao_Paulo")
    servico = resp.servicos[0]

    assert isinstance(resp, BuscarServicoForm)
    assert resp.found is True
    assert servico.external_datetime_ida == to_default_tz(datetime.datetime(2022, 11, 10, 13, 0))
    assert servico.external_id == "123-4"
    assert servico.desconto == 0
    assert servico.preco == D("45.70")
    assert servico.tipo_veiculo == 1
    assert servico.classe == "CONV"
    assert servico.vagas == 30


def test_buscar_corridas(guichepass_api, mock_tipos_onibus, mock_buscar_corridas, mock_guichepass_login):
    request_params = {"origem": 121, "destino": 42, "data": "2022-05-31"}
    corridas = guichepass_api.buscar_corridas(request_params)
    corridas_api = mocker.MockBuscarCorridas.response()
    assert corridas == BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                provider_data=corrida,
                external_id=corrida["service"],
                tipo_veiculo=corrida["busType"],
                classe="CONV",
                vagas=corrida["freeSeats"],
                preco=corrida["price"],
                desconto=corrida["companyDiscount"],
                external_datetime_ida=datetime.datetime.strptime(corrida["departure"], "%Y-%m-%dT%H:%M:%S"),
            )
            for corrida in corridas_api
        ],
    )


def test_buscar_servicos(guichepass_api, mock_tipos_onibus, mock_buscar_corridas, mock_guichepass_login):
    request_params = {"origem": 121, "destino": 42, "data": "2022-05-31"}
    match_params = {
        "timezone": "America/Sao_Paulo",
        "datetime_ida": to_default_tz(datetime.datetime(2024, 6, 23, 6, 0)),
        "tipo_assento": "convencional",
    }
    corridas = guichepass_api.buscar_corridas(request_params, match_params)
    corridas_api = mocker.MockBuscarCorridas.response()
    assert corridas == BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                provider_data=corridas_api[0],
                external_id=corridas_api[0]["service"],
                tipo_veiculo=corridas_api[0]["busType"],
                classe="CONV",
                vagas=corridas_api[0]["freeSeats"],
                preco=corridas_api[0]["price"],
                desconto=corridas_api[0]["companyDiscount"],
                external_datetime_ida=to_default_tz(datetime.datetime(2024, 6, 23, 6, 0)),
            )
        ],
    )


def test_buscar_servicos_sem_match(guichepass_api, mock_tipos_onibus, mock_buscar_corridas, mock_guichepass_login):
    request_params = {"origem": 121, "destino": 42, "data": "2022-05-31"}
    match_params = {
        "timezone": "America/Sao_Paulo",
        "datetime_ida": to_default_tz(datetime.datetime(2024, 6, 23, 3, 12)),
        "tipo_assento": "convencional",
    }
    corridas = guichepass_api.buscar_corridas(request_params, match_params)
    corridas_api = mocker.MockBuscarCorridas.response()
    assert corridas == BuscarServicoForm(
        found=False,
        servicos=[
            ServicoForm(
                external_id=corrida["service"],
                preco=corrida["price"],
                external_datetime_ida=to_default_tz(
                    datetime.datetime.strptime(corrida["departure"], "%Y-%m-%dT%H:%M:%S")
                ),
                classe="CONV",
                external_company_id=guichepass_api.company.company_external_id,
                vagas=corrida["freeSeats"],
                provider_data=corrida,
            )
            for corrida in corridas_api
        ],
    )


def test_atualiza_origens(mock_guichepass_login, mock_atualiza_origens, guichepass_api):
    resp = guichepass_api.atualiza_origens()
    assert resp == [
        Localidade(
            nome_cidade="ABADIA DOS DOURADOS",
            external_cidade_id=129,
            uf="MG",
            external_local_id=129,
            complemento=None,
        ),
        Localidade(
            nome_cidade="SÃO PAULO",
            external_cidade_id=135,
            uf="SP",
            external_local_id=135,
            complemento=None,
        ),
    ]


def test_lista_empresas_api(mock_guichepass_login, mock_guichepass_anonymous_login, guiche_mock_consultar_empresas):
    params = mock_guichepass_anonymous_login

    resp = guichepass_api.lista_empresas_api(params)

    assert len(resp) == 2
    for empresa in resp:
        assert "id" in empresa
        assert "name" in empresa
        assert "cnpj" in empresa


def test_lista_empresas_api_login_invalido(mock_unauthorized_login, mock_guichepass_anonymous_login):
    params = mock_guichepass_anonymous_login

    with pytest.raises(RodoviariaUnauthorizedError):
        guichepass_api.lista_empresas_api(params)


def test_get_map_poltronas(mock_guichepass_login, guichepass_api, guiche_trechoclasses, mock_poltronas_insuficientes):
    trechoclasse_internal_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    resp = guichepass_api.get_map_poltronas(trechoclasse_id=trechoclasse_internal_id)
    assert resp == {1: "ocupada", 2: "livre", 3: "livre"}


def test_force_login(
    requests_mock,
    mock_guichepass_login,
    guichepass_api,
    guiche_trechoclasses,
):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(guichepass_api.login).url,
        json={},
        status=401,
    )
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(guichepass_api.login).url,
        json=mocker.MockGetPoltronasLivres.response_poltronas_insuficientes(),
    )
    trechoclasse_internal_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    resp = guichepass_api.get_map_poltronas(trechoclasse_id=trechoclasse_internal_id)
    requests_mock.assert_call_count(endpoints.EfetuaLoginConfig(guichepass_api.login).url, 2)
    assert resp == {1: "ocupada", 2: "livre", 3: "livre"}


def test_cancela_voucher_ja_marcado_guichepass(
    rf,
    guiche_company,
    mock_guichepass_login,
    guiche_trechoclasses,
    mock_nao_cancela_venda_com_ticket_impresso,
):
    passagem = baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=999,
        buseiro_internal_id=999,
        status="confirmada",
        trechoclasse_integracao=guiche_trechoclasses.ida,
        poltrona_external_id=1,
        company_integracao=guiche_company,
        numero_passagem=1,
    )
    request = rf.get("/rodoviaria/compra/cancela")
    response = views.efetua_cancelamento(
        request,
        travel_id=passagem.travel_internal_id,
        buseiro_id=passagem.buseiro_internal_id,
    )
    assert response.status_code == 403
    data = json.loads(response.content)
    assert data == {
        "error": "Passagem já foi impressa",
        "error_type": "passenger_ticket_already_printed",
    }
    passagem.refresh_from_db()
    assert passagem.erro_cancelamento == "Passagem já foi impressa"
    assert passagem.datetime_cancelamento is not None


def test_salva_provider_da_compra(
    mock_guichepass_login, guichepass_api, guiche_trechoclasses, mock_confirma_venda, mock_cache_poltrona
):
    trechoclasse_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    params = _comprar_params(trechoclasse_id)
    assert not Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id).exists()
    guichepass_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_id)

    # Verifica se comprou a passagem
    assert len(passagens) == 1
    passagem = passagens.first()

    # Verifica se os dados específicos da passagens corresponde apenas a passagem do pax
    assert passagem.provider_data == mocker.MockConfirmarVenda.response()


def test_consultar_bilhete_guichepass(mock_guichepass_login, guichepass_api, mock_consultar_bilhete, guiche_company):
    passagem = baker.make(
        "rodoviaria.Passagem",
        poltrona_external_id=24,
        travel_internal_id=10,
        buseiro_internal_id=20,
        status=Passagem.Status.CONFIRMADA,
        numero_passagem="231293",
        company_integracao=guiche_company,
    )
    response = guichepass_api.get_atualizacao_passagem_api_parceiro(passagem)
    response = response.dict()
    mock_consultar_bilhetito = mocker.MockConsultarBilhete.response()
    assert response["localizador"] == mock_consultar_bilhetito["ticketNumber"]
    assert response["status"] == "cancelada"
    assert response["numero_assento"] == mock_consultar_bilhetito["seat"]
    assert response["numero_assento"] == passagem.poltrona_external_id
    assert response["primeiro_nome_pax"] == mock_consultar_bilhetito["name"]
    assert response["numero_documento"] == mock_consultar_bilhetito["document"]
    assert response["data_partida"] == mock_consultar_bilhetito["departure"]
    assert response["duracao"] == mock_consultar_bilhetito["distance"]


def test_get_atualizacao_passagem_api_parceiro_consulta_parceiro_guichepass(
    mock_guichepass_login, guichepass_api, mock_consultar_bilhete, guiche_company
):
    passagemRecemComprada = baker.make(
        "rodoviaria.Passagem",
        pedido_external_id=66666,
        poltrona_external_id=12,
        numero_passagem="231293",
        company_integracao=guiche_company,
        status=Passagem.Status.CONFIRMADA,
    )
    response = guichepass_api.get_atualizacao_passagem_api_parceiro(passagemRecemComprada)
    response = response.dict()
    mock_consultar_bilhetito = mocker.MockConsultarBilhete.response()
    assert response["localizador"] == mock_consultar_bilhetito["ticketNumber"]
    assert response["numero_assento"] == mock_consultar_bilhetito["seat"]
    assert response["numero_assento"] != passagemRecemComprada.poltrona_external_id
    assert response["status"] == "cancelada"


def test_login_guichepass(guichepass_api, guiche_login):
    assert guichepass_api.login == guiche_login


def test_view_lista_empresas_guichepass(
    rf, mock_guichepass_login, mock_guichepass_anonymous_login, guiche_mock_consultar_empresas
):
    guichepass_form = GuichepassAnonymousLogin(
        url_base="http://api-gravataense.buson.com.br", username="buser", password="buser", client_id="WEB_SALE"
    )

    params = ListaEmpresasAPIParams.parse_obj({"login_params": guichepass_form})
    request = rf.post("/v1/lista-empresas-api")
    response = views.lista_empresas_api(request, data=params)
    assert response.status_code == 200

    response_parsed = json.loads(response.content)["external_companies"]
    expected_response = mocker.ConsultarEmpresas.response()
    response_company_ids = [response["id"] for response in response_parsed]
    expected_company_ids = [response["id"] for response in expected_response]

    assert sorted(response_company_ids) == sorted(expected_company_ids)


def test_vagas_por_categoria_especial(guichepass_api, mock_guichepass_login, mock_get_poltronas_livres):
    guichepass_api.company.company_external_id = 88126
    guichepass_api.company.save()
    trecho_classe = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=1515,
        external_id="870-1",
        origem__id_external="1",
        destino__id_external="2",
        datetime_ida=to_default_tz(datetime.datetime(2024, 7, 10, 10, 0)),
    )
    vagas_por_categoria = guichepass_api.vagas_por_categoria_especial(trecho_classe.trechoclasse_internal_id)
    assert vagas_por_categoria == {
        Passagem.CategoriaEspecial.NORMAL: 7,
        Passagem.CategoriaEspecial.IDOSO_50: 2,
        Passagem.CategoriaEspecial.IDOSO_100: 2,
        Passagem.CategoriaEspecial.JOVEM_50: 2,
        Passagem.CategoriaEspecial.JOVEM_100: 2,
        Passagem.CategoriaEspecial.PCD: 2,
    }


def test_vagas_por_categoria_especial_sem_vagas_especiais(
    guichepass_api, mock_guichepass_login, mock_poltronas_insuficientes
):
    guichepass_api.company.company_external_id = 88126
    guichepass_api.company.save()
    trecho_classe = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=1515,
        external_id="870-1",
        origem__id_external="1",
        destino__id_external="2",
        datetime_ida=to_default_tz(datetime.datetime(2024, 7, 10, 10, 0)),
    )
    vagas_por_categoria = guichepass_api.vagas_por_categoria_especial(trecho_classe.trechoclasse_internal_id)
    assert vagas_por_categoria == {Passagem.CategoriaEspecial.NORMAL: 2}


def test_verifica_poltronas(guichepass_api, mock_guichepass_login, mock_get_poltronas_livres, mock_bloquear_poltrona):
    trecho_classe_internal_id = 1515
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_internal_id,
        external_id="870-1",
        origem__id_external="1",
        destino__id_external="2",
        datetime_ida=to_default_tz(datetime.datetime(2024, 7, 10, 10, 0)),
    )
    poltronas = guichepass_api.verifica_poltrona(
        VerificarPoltronaForm(trechoclasse_id=trecho_classe_internal_id, passageiros=2)
    )
    assert poltronas == [4, 5]
    for poltrona in poltronas:
        cache = guichepass_api.cache.get_poltrona_bloqueada_cache(trecho_classe_internal_id, poltrona)
        assert cache == models.InfosCacheaveisBloqueioPoltrona(
            reserva_id=62714, categoria_external_id=1, preco=D("305.42")
        )


def test_verifica_poltronas_idoso(
    guichepass_api, mock_guichepass_login, mock_get_poltronas_livres, mock_bloquear_poltrona
):
    trecho_classe_internal_id = 1515
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_internal_id,
        external_id="870-1",
        origem__id_external="1",
        destino__id_external="2",
        datetime_ida=to_default_tz(datetime.datetime(2024, 7, 10, 10, 0)),
    )
    poltronas = guichepass_api.verifica_poltrona(
        VerificarPoltronaForm(
            trechoclasse_id=trecho_classe_internal_id,
            passageiros=1,
            categoria_especial=Passagem.CategoriaEspecial.IDOSO_50,
        )
    )
    assert poltronas == [8]
    for poltrona in poltronas:
        cache = guichepass_api.cache.get_poltrona_bloqueada_cache(trecho_classe_internal_id, poltrona)
        assert cache == models.InfosCacheaveisBloqueioPoltrona(
            reserva_id=62714, categoria_external_id=6, preco=D("155.42")
        )


def test_verifica_poltronas_jovem(
    guichepass_api, mock_guichepass_login, mock_get_poltronas_livres, mock_bloquear_poltrona
):
    trecho_classe_internal_id = 1515
    baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_internal_id,
        external_id="870-1",
        origem__id_external="1",
        destino__id_external="2",
        datetime_ida=to_default_tz(datetime.datetime(2024, 7, 10, 10, 0)),
    )
    poltronas = guichepass_api.verifica_poltrona(
        VerificarPoltronaForm(
            trechoclasse_id=trecho_classe_internal_id,
            passageiros=2,
            categoria_especial=Passagem.CategoriaEspecial.JOVEM_100,
        )
    )
    assert poltronas == [4, 5]
    for poltrona in poltronas:
        cache = guichepass_api.cache.get_poltrona_bloqueada_cache(trecho_classe_internal_id, poltrona)
        assert cache == models.InfosCacheaveisBloqueioPoltrona(reserva_id=62714, categoria_external_id=11, preco=D("0"))


@pytest.fixture
def mock_fluxo_compra(guichepass_api, guichepass_grupos_mockado):
    grupo_buser_django = guichepass_grupos_mockado.ida.grupo
    grupo_buser_django.company_id = guichepass_api.company.company_internal_id
    trecho_classe_buser_django_infos = guichepass_grupos_mockado.ida.trecho_classe_infos
    trecho_classe_id = 482739
    expected_servico = mocker.MockBuscarCorridas.response()[0]
    tipo_onibus_map = {to["id"]: to["name"] for to in mocker.MockTiposOnibus.response()}
    timezone = trecho_classe_buser_django_infos.cidade_origem.timezone
    trecho_classe_buser_django_infos.trechoclasse_id = trecho_classe_id
    trecho_classe_buser_django_infos.trecho_datetime_ida = to_tz(
        datetime.datetime.fromisoformat(expected_servico["departure"]), timezone
    )
    baker.make(
        TipoAssento,
        company=guichepass_api.company,
        tipo_assento_parceiro=tipo_onibus_map[int(expected_servico["busType"])],
        tipo_assento_buser_preferencial=trecho_classe_buser_django_infos.tipo_assento,
    )

    cidade_origem, cidade_destino = baker.make(Cidade, company=guichepass_api.company, _quantity=2, timezone=timezone)
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_origem_id,
        cidade=cidade_origem,
        id_external=2312,
    )
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_destino_id,
        cidade=cidade_destino,
        id_external=93282,
    )

    yield grupo_buser_django, trecho_classe_buser_django_infos, trecho_classe_id


def test_compra_fluxo_completo(
    guichepass_api,
    mock_guichepass_login,
    mock_tipos_onibus,
    mock_buscar_corridas,
    mock_get_poltronas_livres,
    mock_bloquear_poltrona,
    mock_confirma_venda,
    mock_cancela_venda,
    mock_fluxo_compra,
    mock_dispara_atualizacao_trecho,
):
    (
        grupo_buser_django,
        trecho_classe_buser_django_infos,
        trecho_classe_id,
    ) = mock_fluxo_compra
    with (
        mock.patch.object(CompraRodoviariaSVC, "_get_internal_grupo", return_value=grupo_buser_django),
        mock.patch(
            "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
            return_value=trecho_classe_buser_django_infos,
        ),
    ):
        params_verifica_poltrona = VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)
        poltronas = CompraRodoviariaSVC(params_verifica_poltrona).verifica_poltrona(params_verifica_poltrona)
    assert len(poltronas) == 2

    preco = json.loads(mock_buscar_corridas.body)[0]["price"]
    comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2, valor_cheio=preco)
    comprar_params.poltronas = poltronas
    CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    passagens_compradas = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id)
    assert passagens_compradas.count() == 2
    for p in passagens_compradas:
        assert p.status == Passagem.Status.CONFIRMADA
        assert p.company_integracao_id == guichepass_api.company.id
    reserva_svc.efetua_cancelamento(
        travel_id=comprar_params.travel_id,
        buseiro_id=passagens_compradas[0].buseiro_internal_id,
    )
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CONFIRMADA
    reserva_svc.efetua_cancelamento(travel_id=comprar_params.travel_id)
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CANCELADA


def test_get_desenho_mapa_poltronas(
    mock_get_poltronas_livres, guichepass_api, guiche_trechoclasses, mock_guichepass_login, mock_tipos_onibus
):
    trecho_classe_id = guiche_trechoclasses.ida.trechoclasse_internal_id

    guiche_trechoclasses.ida.provider_data = json.dumps({"busType": "5"})
    guiche_trechoclasses.ida.save()
    mapa_poltronas = guichepass_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "leito"
    normal = Passagem.CategoriaEspecial.NORMAL
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "livre": False,
                        "x": 5,
                        "y": 1,
                        "numero": 1,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 1,
                        "numero": 2,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 1,
                        "numero": 3,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 1,
                        "numero": 4,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 5,
                        "y": 2,
                        "numero": 6,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 2,
                        "numero": 5,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 2,
                        "numero": 7,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 1,
                        "y": 2,
                        "numero": 8,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 3,
                        "numero": 9,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 3,
                        "numero": 10,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                ],
            }
        ]
    }


def test_get_desenho_mapa_poltronas_force_login(
    mock_buscar_poltronas_force_login, guichepass_api, guiche_trechoclasses, mock_guichepass_login, mock_tipos_onibus
):
    trecho_classe_id = guiche_trechoclasses.ida.trechoclasse_internal_id

    guiche_trechoclasses.ida.provider_data = json.dumps({"busType": "5"})
    guiche_trechoclasses.ida.save()
    mapa_poltronas = guichepass_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "leito"
    normal = Passagem.CategoriaEspecial.NORMAL
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "livre": False,
                        "x": 5,
                        "y": 1,
                        "numero": 1,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 4,
                        "y": 1,
                        "numero": 2,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 1,
                        "numero": 3,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": normal,
                        "preco": None,
                    },
                ],
            }
        ]
    }


@time_machine.travel(datetime.datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_bloquear_poltronas_v2(
    mock_guichepass_login, mock_get_poltronas_livres, mock_bloquear_poltrona, guichepass_api, guiche_trechoclasses
):
    trecho_classe_id = guiche_trechoclasses.ida.trechoclasse_internal_id
    expected_best_before = datetime.datetime(
        2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")
    ) + datetime.timedelta(minutes=20)
    poltrona = 2
    result = guichepass_api.bloquear_poltronas_v2(trecho_classe_id, poltrona, Passagem.CategoriaEspecial.NORMAL)
    expected_cache = parse_obj_as(
        models.InfosCacheaveisBloqueioPoltrona, {"reserva_id": 62714, "categoria_external_id": 1, "preco": D("305.42")}
    )
    assert guichepass_api.cache.get_poltrona_bloqueada_cache(trecho_classe_id, poltrona) == expected_cache
    assert result == BloquearPoltronasResponse(
        seat=poltrona,
        best_before=expected_best_before,
        external_payload=expected_cache,
    )

import rodoviaria.api.guichepass.api as guichepass_api_module


def test_auth_get_token_when_needed(
    mock_guichepass_anonymous_login, mock_guichepass_consultar_empresas, mock_guichepass_login
):
    # Dada uma chamada para um endpoint que precisa de autenticação via token
    params = mock_guichepass_anonymous_login
    guichepass_api_module.lista_empresas_api(params)

    # Espero que seja realizado uma chamada para o endpoint de signin
    assert mock_guichepass_login.call_count == 1
    # e depois a requisição autenticada é executada
    assert mock_guichepass_consultar_empresas.call_count == 1

    # e fazendo uma nova chamada
    guichepass_api_module.lista_empresas_api(params)
    assert mock_guichepass_consultar_empresas.call_count == 2

    # o token é recuperado da memória e não do endpoint
    assert mock_guichepass_login.call_count == 1

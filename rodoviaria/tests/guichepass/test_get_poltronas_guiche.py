import json
from unittest import mock

from commons.circuit_breaker import MyCircuitBreakerError
from rodoviaria import views
from rodoviaria.models.core import TrechoClasse
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.tests.middleware import request_with_middleware


def test_get_poltronas(
    rf, guiche_trechoclasses, mock_guichepass_login, mock_get_poltronas_livres, mock_bloquear_poltrona
):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = guiche_trechoclasses.ida.grupo
        request = rf.get("/rodoviaria/get_poltronas")
        response = views.get_poltronas(
            request,
            trecho_classe_id=guiche_trechoclasses.ida.trechoclasse_internal_id,
            num_passageiros=2,
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data == [4, 5]


def test_get_poltronas_overbooking(
    rf,
    guiche_trechoclasses,
    mock_guichepass_login,
    mock_poltronas_insuficientes,
):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = guiche_trechoclasses.ida.grupo
        request = rf.get(
            "/rodoviaria/v1/get_poltronas",
            data={"trecho_classe_id": guiche_trechoclasses.ida.trechoclasse_internal_id, "num_passageiros": 3},
        )

        response = request_with_middleware(request)

        assert response.status_code == 444
        data = json.loads(response.content)
        assert data == {
            "error": "Apenas 2 poltronas disponíveis para esta viagem",
            "error_type": "overbooking",
            "vagas_disponiveis": 2,
        }


def test_get_poltronas_connection_error(rf, guiche_trechoclasses, mock_guiche_connection_error_antes_pagamento):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = guiche_trechoclasses.ida.grupo
        request = rf.get(
            "/rodoviaria/v1/get_poltronas",
            data={"trecho_classe_id": guiche_trechoclasses.ida.trechoclasse_internal_id, "num_passageiros": 3},
        )

        response = request_with_middleware(request)

        assert response.status_code == 504
        data = json.loads(response.content)
        assert "guichepass http://api-gravataense.buson.com.br/web-sale/bus-layout connection error" == data["error"]
        assert "connection_error" in data["error_type"]


def test_get_poltronas_circuit_breaker_error(rf, guiche_trechoclasses):
    with mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.__init__", return_value=None
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.verifica_poltrona"
    ) as mock_get_poltronas:
        mock_get_poltronas.side_effect = MyCircuitBreakerError("menssagem qualquer", remaining_seconds=10)
        request = rf.get(
            "/rodoviaria/v1/get_poltronas",
            data={"trecho_classe_id": guiche_trechoclasses.ida.trechoclasse_internal_id, "num_passageiros": 3},
        )
        response = request_with_middleware(request)
        assert response.status_code == 429
        data = json.loads(response.content)
        assert data["message"] == "menssagem qualquer"

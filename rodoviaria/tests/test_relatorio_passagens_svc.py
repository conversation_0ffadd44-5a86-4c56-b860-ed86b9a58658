import json
from datetime import datetime, timedelta
from decimal import Decimal

import pytest
import time_machine
from model_bakery import baker

from rodoviaria import views
from rodoviaria.models.core import Passagem, TrechoClasse
from rodoviaria.service.relatorio_passagens_svc import _get_fator_conexao_by_localizador, get_passagens_empresa
from rodoviaria.views_schemas import PassagensPorEmpresaParams


@pytest.fixture
def passagens(totalbus_company, eulabs_company):
    return [
        baker.make(
            Passagem,
            company_integracao=totalbus_company,
            status=Passagem.Status.CANCELADA,
            datetime_cancelamento=datetime.now(),
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now()),
            travel_internal_id=998,
        ),
        baker.make(
            Passagem,
            company_integracao=totalbus_company,
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now()),
            travel_internal_id=999,
        ),
        baker.make(
            Passagem,
            company_integracao=eulabs_company,
            status=Passagem.Status.CANCELADA,
            datetime_cancelamento=datetime.now(),
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now()),
            travel_internal_id=898,
        ),
        baker.make(
            Passagem,
            company_integracao=eulabs_company,
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now()),
            travel_internal_id=899,
        ),
    ]


@pytest.fixture
def passagens_conexao(totalbus_company):
    return [
        baker.make(
            Passagem,
            company_integracao=totalbus_company,
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now()),
            buseiro_internal_id=999,
            travel_internal_id=999,
            preco_rodoviaria=Decimal("10"),
            origem="Carvahall",
            destino="Narnia",
            localizador="0001",
        ),
        baker.make(
            Passagem,
            company_integracao=totalbus_company,
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now()),
            buseiro_internal_id=999,
            travel_internal_id=999,
            origem="Narnia",
            destino="Ratanabá",
            preco_rodoviaria=Decimal("90"),
            localizador="0002",
        ),
    ]


@pytest.fixture
def passagens_conexao_sem_origem_e_destino(totalbus_company):
    return [
        baker.make(
            Passagem,
            company_integracao=totalbus_company,
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now()),
            buseiro_internal_id=999,
            travel_internal_id=999,
            preco_rodoviaria=Decimal("10"),
            origem=None,
            destino=None,
            localizador="0001",
        ),
        baker.make(
            Passagem,
            company_integracao=totalbus_company,
            status=Passagem.Status.CONFIRMADA,
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now()),
            buseiro_internal_id=999,
            travel_internal_id=999,
            origem=None,
            destino=None,
            preco_rodoviaria=Decimal("90"),
            localizador="0002",
        ),
    ]


def test_get_passagens_empresa_passagens_conexao(totalbus_company, passagens_conexao):
    data = PassagensPorEmpresaParams(company_id=totalbus_company.company_internal_id)
    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 2
    assert len(fator_conexoes) == 2
    for r in retorno_passagens:
        assert r["localizador"] in fator_conexoes


def test_get_passagens_empresa(totalbus_company, passagens):
    data = PassagensPorEmpresaParams(company_id=totalbus_company.company_internal_id)
    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 2
    retorno_passagens[0]["status"] = "cancelada"
    retorno_passagens[1]["status"] = "confirmada"

    campos_passagens = {
        "company_id",
        "travel_internal_id",
        "buseiro_internal_id",
        "status",
        "localizador",
        "numero_passagem",
        "preco_rodoviaria",
        "poltrona",
        "datetime_compra",
        "datetime_cancelamento",
        "erro_cancelamento",
    }
    assert campos_passagens.issubset(retorno_passagens[0].keys())


def test_get_passagens_empresa_por_data_compra(totalbus_company, passagens):
    with time_machine.travel("2023-11-14"):
        # passagem fora do filtro de data
        baker.make(Passagem, company_integracao=totalbus_company, travel_internal_id=19)
    data = PassagensPorEmpresaParams(
        company_id=totalbus_company.company_internal_id,
        start_date_compra_e_cancelamento=(datetime.now() - timedelta(hours=1)).isoformat(),
        end_date_compra_e_cancelamento=datetime.now().isoformat(),
    )

    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 2


def test_get_passagens_empresa_por_data_cancelamento(totalbus_company, passagens):
    agora = datetime.now()
    with time_machine.travel("2023-11-14"):
        # passagem fora do filtro de data no created_at, mas dentro no datetime_cancelamento
        baker.make(
            Passagem,
            company_integracao=totalbus_company,
            travel_internal_id=19,
            status=Passagem.Status.CANCELADA,
            datetime_cancelamento=agora - timedelta(minutes=10),
        )
    data = PassagensPorEmpresaParams(
        company_id=totalbus_company.company_internal_id,
        start_date_compra_e_cancelamento=(agora - timedelta(hours=1)).isoformat(),
        end_date_compra_e_cancelamento=agora.isoformat(),
    )

    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 3


def test_get_passagens_empresa_date_compra_e_cancelamento_passagem_data_saida_no_range(totalbus_company, passagens):
    agora = datetime.now()
    with time_machine.travel("2023-11-14"):
        # passagem fora do filtro de data no created_at, mas dentro no datetime_ida
        baker.make(
            Passagem,
            company_integracao=totalbus_company,
            travel_internal_id=19,
            status=Passagem.Status.CANCELADA,
            trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=agora - timedelta(minutes=10)),
        )
    data = PassagensPorEmpresaParams(
        company_id=totalbus_company.company_internal_id,
        start_date_compra_e_cancelamento=(agora - timedelta(hours=1)).isoformat(),
        end_date_compra_e_cancelamento=agora.isoformat(),
    )

    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 3


def test_get_passagens_empresa_por_data_saida(totalbus_company, passagens):
    baker.make(
        Passagem,
        company_integracao=totalbus_company,
        travel_internal_id=19,
        trechoclasse_integracao=baker.make(TrechoClasse, datetime_ida=datetime.now() - timedelta(days=30)),
    )
    data = PassagensPorEmpresaParams(
        company_id=totalbus_company.company_internal_id,
        start_date_saida=(datetime.now() - timedelta(hours=1)).isoformat(),
        end_date_saida=datetime.now().isoformat(),
    )

    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 2


def test_get_passagens_empresa_por_travel_ids(totalbus_company, passagens):
    baker.make(
        Passagem,
        company_integracao=totalbus_company,
        status=Passagem.Status.CONFIRMADA,
        travel_internal_id=19,
    )
    data = PassagensPorEmpresaParams(company_id=totalbus_company.company_internal_id, travel_ids=[19])

    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 1
    assert retorno_passagens[0]["travel_internal_id"] == 19


def test_get_passagens_empresa_status(totalbus_company, passagens):
    data = PassagensPorEmpresaParams(company_id=totalbus_company.company_internal_id, status_passagens="cancelada")

    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 1
    assert retorno_passagens[0]["status_rodoviaria"] == "cancelada"


def test_get_passagens_empresa_paginator(totalbus_company, passagens):
    baker.make(
        Passagem,
        company_integracao=totalbus_company,
        travel_internal_id=19,
    )
    data = PassagensPorEmpresaParams(company_id=totalbus_company.company_internal_id, current_page=1, items_per_page=1)

    retorno_passagens, fator_conexoes, _ = get_passagens_empresa(data)
    assert len(retorno_passagens) == 1


def test_integracao_list_passagens_company_id(eulabs_company, passagens, rf):
    data = PassagensPorEmpresaParams(company_id=eulabs_company.company_internal_id)
    request = rf.post("/v1/passagens/list_passagens_company_id", data.dict(), content_type="application/json")
    response = views.list_passagens_by_company_id(request)
    resp = json.loads(response.content)
    assert response.status_code == 200
    assert len(resp["passagens"]) == 2


def test_get_fator_conexao_by_localizador(passagens_conexao):
    p1 = passagens_conexao[0]
    p2 = passagens_conexao[1]
    valor_total = p1.preco_rodoviaria + p2.preco_rodoviaria
    result = _get_fator_conexao_by_localizador(Passagem.objects.all())
    assert result
    assert result[p1.localizador] == p1.preco_rodoviaria / valor_total
    assert result[p2.localizador] == p2.preco_rodoviaria / valor_total


def test_get_fator_conexao_by_localizador_passagens_sem_origem_e_destino(passagens_conexao_sem_origem_e_destino):
    result = _get_fator_conexao_by_localizador(Passagem.objects.all())
    assert not result

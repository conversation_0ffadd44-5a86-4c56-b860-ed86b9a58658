import json
from collections import namedtuple
from datetime import datetime, timedelta
from functools import reduce
from unittest import mock

from model_bakery import baker

from commons.dateutils import to_default_tz, to_tz
from core.models_rota import Cidade as CoreCidade
from rodoviaria.models.core import Company, Rota
from rodoviaria.service.compare_rotas_svc import comparar_rotas

MockParamsChk = namedtuple(
    "MockParamsChk",
    ["sigla", "duracao", "timezone"],
    defaults=(None, None, "America/Sao_Paulo"),
)


def criar_cidades_buser_django(checkpoints):
    cidades = []
    for c in checkpoints:
        sigla = c.sigla[0:3].upper()
        try:
            cidade_existente = CoreCidade.objects.get(sigla=sigla)
            cidades.append(cidade_existente)
        except CoreCidade.DoesNotExist:
            city_code_ibge = reduce(lambda a, b: a + ord(b), sigla, ord(sigla[0]))
            cidades.append(
                baker.make(
                    "core.Cidade",
                    name=c.sigla,
                    city_code_ibge=city_code_ibge,
                    sigla=sigla,
                    uf=sigla[0:2],
                    timezone=c.timezone,
                )
            )
    return cidades


def criar_locais_embarque_buser_django(checkpoints):
    cidades_buser_django = criar_cidades_buser_django(checkpoints)
    return [baker.make("core.LocalEmbarque", cidade=cidade) for cidade in cidades_buser_django]


def criar_rota_buser_django(company_internal_id, checkpoints):
    locais_embarque_buser_django = criar_locais_embarque_buser_django(checkpoints)
    rota_django = baker.make("core.Rota")
    chekpoints = []
    for idx, local in enumerate(locais_embarque_buser_django):
        tempo_embarque = timedelta() if idx == 0 else timedelta(10)
        duracao = checkpoints[idx].duracao - tempo_embarque
        chekpoints.append(
            baker.make(
                "core.Checkpoint",
                rota=rota_django,
                local=local,
                idx=idx,
                duracao=duracao,
                tempo_embarque=tempo_embarque,
            )
        )
    return rota_django, chekpoints


def criar_cidades_rodoviaria(company_id, checkpoints):
    cidades_buser_django = criar_cidades_buser_django(checkpoints)
    cidades = []
    for cidade_django in cidades_buser_django:
        cidade_internal = baker.make(
            "rodoviaria.CidadeInternal",
            id=cidade_django.id,
            name=cidade_django.name,
            sigla=cidade_django.sigla,
            city_code_ibge=cidade_django.city_code_ibge,
            uf=cidade_django.uf,
            timezone=cidade_django.timezone,
        )
        cidades.append(
            baker.make(
                "rodoviaria.Cidade",
                company_id=company_id,
                name=cidade_django.name,
                cidade_internal=cidade_internal,
                timezone=cidade_django.timezone,
            )
        )
    return cidades


def criar_locais_embarque_rodoviaria(company_id, checkpoints):
    cidades_rodoviaria = criar_cidades_rodoviaria(company_id, checkpoints)
    locais_embarque_buser_django = criar_locais_embarque_buser_django(checkpoints)
    locais = []
    for idx, cidade in enumerate(cidades_rodoviaria):
        locais.append(
            baker.make(
                "rodoviaria.LocalEmbarque",
                cidade=cidade,
                id_external=9000 + idx,
                local_embarque_internal_id=locais_embarque_buser_django[idx].id,
            )
        )

    return list(locais)


def criar_rota_rodoviaria(company_id, id_internal, checkpoints):
    locais_embarque_rodoviaria = criar_locais_embarque_rodoviaria(company_id, checkpoints)
    provider_data = []
    data_padrao = to_default_tz(datetime(2022, 7, 9, 0, 0, 0))
    for idx, local in enumerate(locais_embarque_rodoviaria):
        data_padrao = data_padrao + checkpoints[idx].duracao
        data_padrao = to_tz(data_padrao, checkpoints[idx].timezone)
        provider_data.append(
            {
                "localidade": {
                    "id": local.id_external,
                    "cidade": local.cidade.cidade_internal.name,
                    "uf": local.cidade.cidade_internal.uf,
                },
                "distancia": idx * 2 + 10,
                "permanencia": "00:00",
                "data": data_padrao.strftime("%Y-%m-%d"),
                "hora": data_padrao.strftime("%H:%M"),
            }
        )
    rota_rodoviaria = baker.make(
        "rodoviaria.Rota",
        company_id=company_id,
        id_internal=id_internal,
        provider_data=json.dumps(provider_data),
    )
    return rota_rodoviaria


def test_comparar_rotas(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta(), "America/Cuiaba"),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30), "America/Sao_Paulo"),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30), "America/Cuiaba"),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30), "America/Sao_Paulo"),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30), "America/Sao_Paulo"),
    ]
    checkpoints_django = [
        MockParamsChk("Sao Paulo", timedelta(), "America/Cuiaba"),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
    ]
    rota_buser_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    rota_rodoviaria = criar_rota_rodoviaria(totalbus_login.company.id, rota_buser_django[0].id + 1, checkpoints_rodo)
    result = comparar_rotas(totalbus_login.company.company_internal_id)
    assert result[rota_rodoviaria.id]
    assert result[rota_rodoviaria.id]["sugestoes"][0]["id_internal"] == rota_buser_django[0].id


def test_comparar_rotas_nao_sao_match(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
    ]
    checkpoints_django = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30)),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30)),
    ]
    rota_buser_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    rota_rodoviaria = criar_rota_rodoviaria(totalbus_login.company.id, rota_buser_django[0].id + 1, checkpoints_rodo)
    result = comparar_rotas(totalbus_login.company.company_internal_id)
    assert len(result[rota_rodoviaria.id]["sugestoes"]) == 0


def test_comparar_rotas_duas_rotas_compativeis(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30)),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30)),
    ]
    checkpoints_django = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
    ]

    rota_buser_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    rodo_rota = criar_rota_rodoviaria(totalbus_login.company.id, rota_buser_django[0].id + 1, checkpoints_rodo)
    rodo_rota_2 = criar_rota_rodoviaria(totalbus_login.company.id, rota_buser_django[0].id + 1, checkpoints_django)

    result = comparar_rotas(totalbus_login.company.company_internal_id)

    assert result[rodo_rota.id]["sugestoes"][0]["id_internal"] == rota_buser_django[0].id
    assert result[rodo_rota_2.id]["sugestoes"][0]["id_internal"] == rota_buser_django[0].id


def test_comparar_rotas_mesmo_checkpoints_ordem_diferente(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30)),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30)),
    ]
    checkpoints_django = [
        MockParamsChk("Belo Horizonte", timedelta()),
        MockParamsChk("Sao Paulo", timedelta(hours=1, minutes=30)),
    ]

    rota_buser_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    rodo_rota = criar_rota_rodoviaria(totalbus_login.company.id, rota_buser_django[0].id + 1, checkpoints_rodo)

    result = comparar_rotas(totalbus_login.company.company_internal_id)

    assert len(result[rodo_rota.id]["sugestoes"]) == 0


def test_comparar_rotas_duas_rotas_django_duracao_incompativel(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30)),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30)),
    ]
    checkpoints_django = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
    ]

    checkpoints_django_2 = [
        MockParamsChk("Belo Horizonte", timedelta()),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
    ]

    rota_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django_2)
    rodo_rota = criar_rota_rodoviaria(totalbus_login.company.id, rota_django[0].id + 10, checkpoints_rodo)

    result = comparar_rotas(totalbus_login.company.company_internal_id)

    assert len(result[rodo_rota.id]["sugestoes"]) == 1
    assert result[rodo_rota.id]["sugestoes"][0]["id_internal"] == rota_django[0].id


def test_comparar_rotas_duas_rotas_django_duracao_compativel(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30)),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30)),
    ]
    checkpoints_django = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
    ]

    checkpoints_django_2 = [
        MockParamsChk("Belo Horizonte", timedelta()),
        MockParamsChk("Palmas", timedelta(hours=3, minutes=0)),
    ]

    rota_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    rota_django_2 = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django_2)
    rodo_rota = criar_rota_rodoviaria(totalbus_login.company.id, rota_django_2[0].id + 1, checkpoints_rodo)

    result = comparar_rotas(totalbus_login.company.company_internal_id)

    assert result[rodo_rota.id]["sugestoes"][0]["id_internal"] == rota_django[0].id
    assert result[rodo_rota.id]["sugestoes"][1]["id_internal"] == rota_django_2[0].id


def test_comparar_rotas_link_ja_correto(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30)),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30)),
    ]

    checkpoints_django = [
        MockParamsChk("Belo Horizonte", timedelta()),
        MockParamsChk("Palmas", timedelta(hours=3, minutes=0)),
    ]

    rota_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    criar_rota_rodoviaria(totalbus_login.company.id, rota_django[0].id, checkpoints_rodo)

    result = comparar_rotas(totalbus_login.company.company_internal_id)

    assert result == {}


def test_comparar_rotas_link_duracao_errada(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30)),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30)),
    ]
    checkpoints_django = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
    ]

    checkpoints_django_2 = [
        MockParamsChk("Belo Horizonte", timedelta()),
        MockParamsChk("Palmas", timedelta(hours=2, minutes=0)),
    ]

    rota_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    rota_django_2 = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django_2)
    rodo_rota = criar_rota_rodoviaria(totalbus_login.company.id, rota_django_2[0].id, checkpoints_rodo)

    result = comparar_rotas(totalbus_login.company.company_internal_id)

    assert len(result[rodo_rota.id]["sugestoes"]) == 1
    assert result[rodo_rota.id]["sugestoes"][0]["id_internal"] == rota_django[0].id


def test_comparar_rotas_rota_com_grupo(totalbus_login):
    checkpoints_rodo = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
        MockParamsChk("Manaus", timedelta(hours=1, minutes=30)),
        MockParamsChk("Palmas", timedelta(hours=1, minutes=30)),
        MockParamsChk("Blumenau", timedelta(hours=1, minutes=30)),
    ]
    checkpoints_django = [
        MockParamsChk("Sao Paulo", timedelta()),
        MockParamsChk("Belo Horizonte", timedelta(hours=1, minutes=30)),
    ]

    rota_django = criar_rota_buser_django(totalbus_login.company.company_internal_id, checkpoints_django)
    company_django = baker.make("core.Company", id=totalbus_login.company.company_internal_id)
    baker.make(
        "core.Grupo",
        company=company_django,
        datetime_ida=to_default_tz(datetime(2022, 7, 2, 14)),
        rota=rota_django[0],
    )

    rodo_rota = criar_rota_rodoviaria(totalbus_login.company.id, rota_django[0].id + 1, checkpoints_rodo)

    result = comparar_rotas(totalbus_login.company.company_internal_id)

    assert len(result[rodo_rota.id]["sugestoes"]) == 1
    assert result[rodo_rota.id]["sugestoes"][0]["id_internal"] == rota_django[0].id


def test_comparar_rotas_apenas_rotas_marketplace():
    company_internal_id = 768
    company_marketplace = baker.make(
        Company,
        company_internal_id=company_internal_id,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    company_hibrido = baker.make(
        Company,
        company_internal_id=company_internal_id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    rota_marketplace = baker.make(Rota, company=company_marketplace, id_internal=231)
    baker.make(Rota, company=company_hibrido, id_internal=783)
    with mock.patch(
        "rodoviaria.service.compare_rotas_svc.tenta_achar_match_no_django"
    ) as mock_tenta_achar_match_no_django:
        comparar_rotas(company_internal_id, apenas_linkadas_erradas=False)
    assert list(mock_tenta_achar_match_no_django.call_args.args[1]) == [rota_marketplace]

from datetime import date, datetime, timedelta
from types import SimpleNamespace
from unittest import mock

import pytest
from celery.exceptions import Retry
from celery.result import GroupResult
from model_bakery import baker

from commons.celery_utils import DefaultQueueNames
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import TaskStatus
from rodoviaria.service import fetch_data_limite_rotas_svc
from rodoviaria.service.exceptions import RodoviariaException, RodoviariaTooManyRequestsError


def test_fetch_data_limite_rotas(praxio_company, praxio_login):
    id_hash = "asdf1234ASDF"
    rota = baker.make("rodoviaria.Rota", id_hash=id_hash)
    with (
        mock.patch.object(OrchestrateRodoviaria, "fetch_data_limite_servicos") as mock_fetch_data_limite_servicos,
        mock.patch.object(OrchestrateRodoviaria, "fetch_rota") as mock_fetch_rota,
        mock.patch("celery.result.EagerResult.parent") as mock_chord_parent,
    ):
        mock_chord_parent.return_value = GroupResult("123")
        mock_fetch_rota.return_value = SimpleNamespace(parsed=SimpleNamespace(hash=id_hash))
        mock_fetch_data_limite_servicos.return_value = {
            12: datetime(2022, 3, 2),
            11: datetime(2022, 2, 1),
            13: datetime(2022, 2, 2),
        }
        fetch_data_limite_rotas_svc.fetch_data_limite_rotas(praxio_company.company_internal_id)

        assert rota.data_limite is None
        rota.refresh_from_db()
        assert rota.data_limite == date(2022, 3, 2)


def test_fetch_data_limite_rotas_use_low_rate_limit(praxio_company, praxio_login):
    praxio_company.integracao.use_low_rate_limit = True
    praxio_company.integracao.save()

    with (
        mock.patch.object(OrchestrateRodoviaria, "fetch_data_limite_servicos") as mock_fetch_data_limite_servicos,
        mock.patch("rodoviaria.service.fetch_data_limite_rotas_svc._generate_tasks") as mock_generate_tasks,
    ):
        mock_fetch_data_limite_servicos.return_value = {
            12: datetime(2022, 3, 2),
        }
        fetch_data_limite_rotas_svc.fetch_data_limite_rotas(praxio_company.company_internal_id)
    mock_generate_tasks.assert_called_once_with(
        praxio_company.id,
        [
            {
                "company_internal_id": praxio_company.company_internal_id,
                "datetime_ida": datetime(2022, 3, 2).strftime("%Y-%m-%dT%H:%M:%S"),
                "id_external": 12,
                "use_low_rate_limit": True,
            }
        ],
        DefaultQueueNames.FETCH_DATA_LIMITE,
    )
    assert (
        TaskStatus.objects.get(company=praxio_company, task_name=TaskStatus.Name.FETCH_DATA_LIMITE).status
        == TaskStatus.Status.PENDING
    )


def test_buscar_hash_rota_too_many_requests(
    totalbus_api,
):
    with mock.patch.object(OrchestrateRodoviaria, "fetch_rota") as mock_fetch_rota:
        mock_fetch_rota.side_effect = RodoviariaTooManyRequestsError("algum erro")

        with pytest.raises(Retry):
            fetch_data_limite_rotas_svc.buscar_hash_rota_default_rate_limit.apply(
                args=(
                    {
                        "company_internal_id": totalbus_api.company.company_internal_id,
                        "datetime_ida": datetime(2022, 3, 2).strftime("%Y-%m-%dT%H:%M:%S"),
                        "id_external": 12,
                        "use_low_rate_limit": True,
                    },
                )
            ).get()


def test_buscar_hash_rota_erro_qualquer(
    totalbus_api,
):
    with mock.patch.object(OrchestrateRodoviaria, "fetch_rota") as mock_fetch_rota:
        mock_fetch_rota.side_effect = RodoviariaException("algum erro")

        retorno = fetch_data_limite_rotas_svc.buscar_hash_rota_default_rate_limit.apply(
            args=(
                {
                    "company_internal_id": totalbus_api.company.company_internal_id,
                    "datetime_ida": datetime(2022, 3, 2).strftime("%Y-%m-%dT%H:%M:%S"),
                    "id_external": 12,
                    "use_low_rate_limit": True,
                },
            )
        ).get()
        assert retorno is None


def test_buscar_hash_rota_retorno_fetch_rotas_none(
    totalbus_api,
):
    with mock.patch.object(OrchestrateRodoviaria, "fetch_rota") as mock_fetch_rota:
        mock_fetch_rota.return_value = None

        retorno = fetch_data_limite_rotas_svc.buscar_hash_rota_default_rate_limit.apply(
            args=(
                {
                    "company_internal_id": totalbus_api.company.company_internal_id,
                    "datetime_ida": datetime(2022, 3, 2).strftime("%Y-%m-%dT%H:%M:%S"),
                    "id_external": 12,
                    "use_low_rate_limit": True,
                },
            )
        ).get()
        assert retorno is None


def test_get_data_limite_rotas_integradas(totalbus_api):
    cid = totalbus_api.company.company_internal_id
    r = baker.make(
        "rodoviaria.Rota",
        company=totalbus_api.company,
        id_internal=10,
        data_limite=date.today() + timedelta(days=7),
        ativo=True,
    )
    result = fetch_data_limite_rotas_svc.get_data_limite_rotas_integradas([cid])
    assert result[cid][r.id_internal] == r.data_limite

    # Caso tenha outra rota com a data_limite maior, usa ela
    r_data_maior = baker.make(
        "rodoviaria.Rota",
        company=totalbus_api.company,
        id_internal=10,
        data_limite=date.today() + timedelta(days=15),
        ativo=True,
    )
    result = fetch_data_limite_rotas_svc.get_data_limite_rotas_integradas([cid])
    assert result[cid][r.id_internal] == r_data_maior.data_limite


def test_batch_save_data_limite_on_rotas():
    data_limite = date.today() + timedelta(days=70)
    data_limite_str = data_limite.strftime("%Y-%m-%dT%H:%M:%S")
    hashes = [{"hash_key": "123abc", "datetime_ida": data_limite_str}]
    r = baker.make(
        "rodoviaria.Rota",
        id_hash=hashes[0]["hash_key"],
        id_internal=10,
        data_limite=date.today() + timedelta(days=7),
        ativo=True,
    )
    fetch_data_limite_rotas_svc.batch_save_data_limite_on_rotas(hashes, company_id=r.company_id)
    r.refresh_from_db()
    assert r.data_limite == data_limite
    assert (
        TaskStatus.objects.get(company=r.company, task_name=TaskStatus.Name.FETCH_DATA_LIMITE).status
        == TaskStatus.Status.SUCCESS
    )

from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal as D
from types import SimpleNamespace
from unittest import mock

import pytest
import responses
import time_machine
from model_bakery import baker
from pydantic import parse_obj_as
from responses import matchers
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz
from rodoviaria.api.forms import BuscarServicoForm
from rodoviaria.api.ti_sistemas import endpoints, models
from rodoviaria.api.ti_sistemas.api import TiSistemasAPI, async_desbloquear_poltronas
from rodoviaria.api.ti_sistemas.exceptions import (
    TiSistemasBloquearPoltronaException,
    TiSistemasCancelarCompraException,
    TiSistemasEfetuarCompraException,
)
from rodoviaria.api.ti_sistemas.memcache import TiSistemasMC
from rodoviaria.forms import compra_rodoviaria_forms
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import (
    BloquearPoltronasResponse,
    ComprarForm,
    PassageiroForm,
    VerificarPoltronaForm,
)
from rodoviaria.forms.mapa_poltronas_forms import Onibus
from rodoviaria.models.core import Cidade, Company, LocalEmbarque, Passagem
from rodoviaria.service import class_match_svc, reserva_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import PassengerTicketAlreadyPrintedException, PoltronaJaSelecionadaException
from rodoviaria.tests.ti_sistemas.mock_data_response import mock_corridas, mock_trechos_vendidos
from rodoviaria.tests.utils_testes import _comprar_params


@pytest.fixture
def trecho_classe_mock(ti_sistemas_company):
    trecho_classe_id = 173942
    cidade_origem = baker.make("rodoviaria.Cidade", timezone="America/Sao_Paulo")
    origem = baker.make("rodoviaria.LocalEmbarque", id_external=382, cidade=cidade_origem)
    destino = baker.make("rodoviaria.LocalEmbarque", id_external=983)
    datetime_ida = to_default_tz(datetime(2022, 5, 10, 14, 30))
    yield baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=trecho_classe_id,
        external_id="99103704",
        origem=origem,
        destino=destino,
        datetime_ida=datetime_ida,
        preco_rodoviaria=D("130"),
        grupo__company_integracao=ti_sistemas_company,
    )


@pytest.fixture
def passagens_para_cancelar():
    passagens = [
        baker.make(
            Passagem,
            poltrona_external_id=9,
            pedido_external_id=2,
            localizador="123",
            travel_internal_id=12345,
            status=Passagem.Status.CONFIRMADA,
        ),
        baker.make(
            Passagem,
            poltrona_external_id=10,
            pedido_external_id=2,
            localizador="1234",
            travel_internal_id=12345,
            status=Passagem.Status.CONFIRMADA,
        ),
    ]
    return passagens


@pytest.fixture
def mock_fluxo_compra(ti_sistemas_api, ti_sistemas_grupos_mockado):
    grupo_buser_django = ti_sistemas_grupos_mockado.ida.grupo
    grupo_buser_django.company_id = ti_sistemas_api.company.company_internal_id
    trecho_classe_buser_django_infos = ti_sistemas_grupos_mockado.ida.trecho_classe_infos
    trecho_classe_id = 482739
    expected_servico = mock_corridas.corridas[0]
    timezone = trecho_classe_buser_django_infos.cidade_origem.timezone
    trecho_classe_buser_django_infos.trechoclasse_id = trecho_classe_id
    trecho_classe_buser_django_infos.trecho_datetime_ida = to_tz(
        datetime.strptime(expected_servico["departure"], "%Y-%m-%dT%H:%M"),
        timezone,
    )
    trecho_classe_buser_django_infos.tipo_assento = class_match_svc.buser_class(expected_servico["service"])

    cidade_origem, cidade_destino = baker.make(Cidade, company=ti_sistemas_api.company, _quantity=2, timezone=timezone)
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_origem_id,
        cidade=cidade_origem,
        id_external=2312,
    )
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_destino_id,
        cidade=cidade_destino,
        id_external=93282,
    )

    yield grupo_buser_django, trecho_classe_buser_django_infos, trecho_classe_id


def test_atualiza_origens_api(ti_sistemas_api: TiSistemasAPI):
    origens = ti_sistemas_api.atualiza_origens()
    assert len(origens) == 0
    # assert len(origens) == 5566
    # assert all(isinstance(local, Localidade) for local in origens)


def test_buscar_itinerario_api(ti_sistemas_api: TiSistemasAPI, mock_buscar_itinerario):
    itinerario = ti_sistemas_api.buscar_itinerario({"id_viagem": 1212})
    assert itinerario.parsed.hash == "2275977d20547cc1a380fc4a61b1e9a5ebaa7f8"


def test_descobrir_rotas_async(override_config, ti_sistemas_api: TiSistemasAPI):
    next_days = 14
    shift_days = 1
    queue_name = "queue_name"
    return_task_object = False
    with mock.patch("rodoviaria.service.descobrir_rotas_ti_sistemas_async_svc.descobrir_rotas") as mock_descobrir_rotas:
        ti_sistemas_api.descobrir_rotas_async(
            next_days, shift_days, queue_name, return_task_object, Company.ModeloVenda.MARKETPLACE
        )
    mock_descobrir_rotas.assert_called_once_with(
        ti_sistemas_api.login,
        ti_sistemas_api.company.company_internal_id,
        next_days,
        shift_days,
        queue_name,
        return_task_object,
    )


def test_descobrir_operacao_async(override_config, ti_sistemas_api: TiSistemasAPI):
    next_days = 14
    shift_days = 1
    queue_name = "queue_name"
    return_task_object = False
    with mock.patch("rodoviaria.api.ti_sistemas.descobrir_operacao.descobrir_operacao") as mock_descobrir_operacao:
        ti_sistemas_api.descobrir_operacao_async(next_days, shift_days, queue_name, return_task_object)
    mock_descobrir_operacao.assert_called_once_with(
        ti_sistemas_api.login,
        next_days,
        shift_days,
        queue_name,
        return_task_object,
    )


def test_mapa_viagem_request_api(ti_sistemas_api, mock_mapa_viagem_detalhada):
    params = {
        "date_ida": "2024-02-06",
        "origem_external_id": 977,
        "destino_external_id": 5337,
        "company_external_id": 142,
        "viagem_id": 366,
    }
    mapa_viagem = ti_sistemas_api._mapa_viagem_request(params)
    assert isinstance(mapa_viagem, models.LayoutViagem)


def test_buscar_corridas(ti_sistemas_api, mock_buscar_corridas):
    request_params = {"origem": 977, "destino": 5337, "data": "2024-02-06"}
    corridas = ti_sistemas_api.buscar_corridas(request_params)

    assert isinstance(corridas, BuscarServicoForm)
    assert corridas.found is True
    assert len(corridas.servicos) == 2


def test_buscar_corridas_vazio(ti_sistemas_api, mock_buscar_corridas_vazio):
    request_params = {"origem": 977, "destino": 5337, "data": "2024-02-06"}
    corridas = ti_sistemas_api.buscar_corridas(request_params)

    assert corridas.found is False
    assert corridas.servicos == []


def test_buscar_corridas_request(ti_sistemas_api, mock_buscar_corridas):
    request_params = {"origem": 977, "destino": 5337, "data": "2024-02-06"}
    corridas = ti_sistemas_api._buscar_corridas_request(request_params)
    assert isinstance(corridas[0], models.Corrida)


@responses.activate
def test_buscar_corridas_request_retry_status_code_400(ti_sistemas_api, ti_sistemas_login):
    # data uma response com status_code 400 e json=None
    request_params = {"origem": 977, "destino": 5337, "data": "2024-02-06"}
    query_params = {"origin": 977, "destination": 5337, "date": "06-02-2024"}
    endpoint = endpoints.BuscarCorridasConfig(ti_sistemas_login).url + "?origin=977&destination=5337&date=06-02-2024"
    corridas = [
        {
            "id": 366,
            "origin": 977,
            "destination": 5337,
            "departure": "2024-02-06T21:15",
            "arrival": "2024-02-07T04:43",
            "service": "Convencional",
            "busCompany": "141",
            "busCompanyName": "Nobre (Buser)",
            "freeSeats": 33,
            "price": 222.55,
            "toll": 0,
            "busType": "C",
            "message": "",
        }
    ]

    responses.add(
        responses.GET,
        endpoint,
        match=[matchers.query_param_matcher(query_params)],
        status=400,
    )
    responses.add(
        responses.GET,
        endpoint,
        match=[matchers.query_param_matcher(query_params)],
        json=corridas,
        status=200,
    )

    # deve haver uma tentativa de retry
    resp = ti_sistemas_api._buscar_corridas_request(request_params)
    # e em caso de sucesso após retry, que a resposta esperada seja retornada.
    assert resp == parse_obj_as(list[models.Corrida], corridas)
    assert responses.assert_call_count(endpoint, 2)


def test_buscar_corridas_request_filtra_viagens_da_empresa(ti_sistemas_api, mock_buscar_corridas):
    ti_sistemas_api.company.company_external_id = 123
    ti_sistemas_api.company.save()
    request_params = {"origem": 977, "destino": 5337, "data": "2024-02-06"}
    corridas = ti_sistemas_api._buscar_corridas_request(request_params)
    assert corridas == []


def test_buscar_servico_com_match(ti_sistemas_api, mock_buscar_corridas):
    expected_servico = mock_corridas.corridas[0]
    request_params = {"origem": 977, "destino": 5337, "data": "2024-02-06"}
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime.strptime(expected_servico["departure"], "%Y-%m-%dT%H:%M"), timezone)
    tipo_assento = "convencional"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}

    servicos_form = ti_sistemas_api.buscar_corridas(request_params, match_params)
    servico_form = servicos_form.servicos[0]

    assert isinstance(servicos_form, BuscarServicoForm)
    assert servicos_form.found is True
    assert len(servicos_form.servicos) == 1
    assert servico_form.external_id == str(expected_servico["id"])
    assert servico_form.classe == expected_servico["service"]


def test_buscar_servico_sem_match(ti_sistemas_api, mock_buscar_corridas):
    request_params = {"origem": 977, "destino": 5337, "data": "2024-02-06"}
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime(2024, 2, 7), timezone)
    tipo_assento = "convencional"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}

    servicos_form = ti_sistemas_api.buscar_corridas(request_params, match_params)

    assert isinstance(servicos_form, BuscarServicoForm)
    assert servicos_form.found is False
    assert len(servicos_form.servicos) == 2


def test_bloquear_poltrona_api(ti_sistemas_api, mock_bloquear_uma_poltrona):
    params = {
        "date_ida": "2024-02-06",
        "company_external_id": 141,
        "origem_external_id": 977,
        "destino_external_id": 5337,
        "viagem_id": 366,
        "order_id": 1002,
        "seats": [10],
    }
    response = ti_sistemas_api._bloquear_poltrona_request(params)
    assert isinstance(response, models.BloquearPoltronaOutput)


def test_verifica_poltrona_uma_poltrona(
    ti_sistemas_api, trecho_classe_mock, mock_mapa_viagem_detalhada, mock_bloquear_uma_poltrona
):
    tc_id = trecho_classe_mock.trechoclasse_internal_id
    poltronas = ti_sistemas_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=tc_id, passageiros=1))
    assert poltronas == [9]
    for poltrona in poltronas:
        assert ti_sistemas_api.cache.get_poltrona_cache_key(tc_id, poltrona)


def test_verifica_poltrona_duas_poltronas(
    ti_sistemas_api, trecho_classe_mock, mock_mapa_viagem_detalhada, mock_bloquear_duas_poltronas
):
    tc_id = trecho_classe_mock.trechoclasse_internal_id
    poltronas = ti_sistemas_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=tc_id, passageiros=2))
    assert poltronas == [9, 10]
    for poltrona in poltronas:
        assert ti_sistemas_api.cache.get_poltrona_cache_key(tc_id, poltrona)


def test_get_map_poltronas(ti_sistemas_api, trecho_classe_mock, mock_mapa_viagem_detalhada):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    baker.make(
        Passagem, trechoclasse_integracao=trecho_classe_mock, poltrona_external_id=6, status=Passagem.Status.CONFIRMADA
    )
    map_poltronas = ti_sistemas_api.get_map_poltronas(trecho_classe_id)
    assert map_poltronas == {
        "01": "ocupada",
        "02": "ocupada",
        "03": "ocupada",
        "04": "ocupada",
        "05": "ocupada",
        "06": "ocupada",
        "07": "ocupada",
        "08": "ocupada",
        "09": "livre",
        "10": "livre",
    }


def test_bloquear_poltrona_erro_ja_ocupada(
    ti_sistemas_api, trecho_classe_mock, mock_bloquear_uma_poltrona_erro_ja_ocupada
):
    # ao tentar bloquear uma poltrona já selecionada, espero que uma exceção seja levantada
    with pytest.raises(PoltronaJaSelecionadaException):
        ti_sistemas_api.bloquear_poltronas(trecho_classe_mock.trechoclasse_internal_id, [9])

    # e que essa poltrona seja salva no cache como indisponivel
    poltrona = ti_sistemas_api.cache.get_poltrona_indisponivel_cache(trecho_classe_mock.trechoclasse_internal_id)
    assert poltrona.pop() == "09"


def test_verificar_poltrona_ja_ocupada_seleciona_outra_poltrona(
    mock_mapa_viagem_detalhada, ti_sistemas_api, ti_sistemas_login, trecho_classe_mock, requests_mock
):
    response_bloqueio_poltrona = {
        "orderId": "100001",
        "seats": [{"seatId": 1223757, "voucher": None, "seat": "10", "status": "Reservada"}],
    }

    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(ti_sistemas_login).url,
        json={"id": "", "code": "", "type": "", "message": "O banco 9 já está ocupado"},
        status=500,
    )
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(ti_sistemas_login).url,
        json=response_bloqueio_poltrona,
        status=200,
    )
    trechoclasse_id = trecho_classe_mock.trechoclasse_internal_id
    params = compra_rodoviaria_forms.VerificarPoltronaForm(trechoclasse_id=trechoclasse_id, passageiros=1)

    # ao verificar uma poltrona já selecionada, espero que mesmo que a primeira chamada retorna exceção, será feita
    # uma retentativa e uma poltrona seja selecionada
    poltronas_selecionadas = ti_sistemas_api.verifica_poltrona(params)
    assert poltronas_selecionadas == [10]
    # e que essa poltrona seja salva no cache como indisponivel
    poltrona = ti_sistemas_api.cache.get_poltrona_indisponivel_cache(trecho_classe_mock.trechoclasse_internal_id)
    assert poltrona.pop() == "09"


def test_bloquear_poltrona_erro_nao_encontrada_com_retry(
    ti_sistemas_api,
    requests_mock,
    trecho_classe_mock,
    mock_bloquear_uma_poltrona_erro,
):
    with pytest.raises(TiSistemasBloquearPoltronaException):
        ti_sistemas_api.bloquear_poltronas(trecho_classe_mock.trechoclasse_internal_id, [9])
    assert requests_mock.assert_call_count(endpoints.BloquearPoltronaConfig(ti_sistemas_api.login).url, 3)


def test_desbloquear_poltronas(ti_sistemas_api, trecho_classe_mock, mock_desbloquear_poltrona_sucesso):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    poltronas = [9]
    order_id = ti_sistemas_api._generate_order_id()
    bloqueio = {"seat_id": 1223757, "voucher": None, "seat": 9, "status": "Reservada"}
    cache_value = {"order_id": order_id, **bloqueio}
    ti_sistemas_api.cache.set_poltrona_cache_key(trecho_classe_id, bloqueio["seat"], cache_value)

    ti_sistemas_api.desbloquear_poltronas(trecho_classe_id, poltronas)

    assert ti_sistemas_api.cache.get_poltrona_cache_key(trecho_classe_id, bloqueio["seat"]) is None


def test_async_desbloquear_poltronas(ti_sistemas_api, trecho_classe_mock, mock_desbloquear_poltrona_sucesso):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    poltronas = [9]
    order_id = ti_sistemas_api._generate_order_id()
    bloqueio = {"seat_id": 1223757, "voucher": None, "seat": 9, "status": "Reservada"}
    cache_value = {"order_id": order_id, **bloqueio}
    ti_sistemas_api.cache.set_poltrona_cache_key(trecho_classe_id, bloqueio["seat"], cache_value)

    async_desbloquear_poltronas(ti_sistemas_api.company.id, trecho_classe_id, poltronas)

    assert ti_sistemas_api.cache.get_poltrona_cache_key(trecho_classe_id, bloqueio["seat"]) is None


def test_efetuar_compra(ti_sistemas_api, mock_efetuar_compra_sucesso):
    comprador = {"nome": "Buseiro da Silva", "cpf": "*********-00"}
    params = {
        "seat_id": "1223757",
        "order_id": 100001,
        "nome": "Buseiro da Silva",
        "documento": 123456,
        "comprador": comprador,
    }
    response = ti_sistemas_api._efetuar_compra_request(params)
    assert isinstance(response, models.EfetuarCompraOutput)


def test_compra_fluxo_completo(
    ti_sistemas_api,
    mock_mapa_viagem_detalhada,
    mock_buscar_corridas,
    mock_bloquear_duas_poltronas,
    mock_efetuar_compra_duas_poltronas_sucesso,
    mock_cancelar_compra_sucesso,
    mock_fluxo_compra,
    mock_dispara_atualizacao_trecho,
):
    (
        grupo_buser_django,
        trecho_classe_buser_django_infos,
        trecho_classe_id,
    ) = mock_fluxo_compra
    with (
        mock.patch.object(CompraRodoviariaSVC, "_get_internal_grupo", return_value=grupo_buser_django),
        mock.patch(
            "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
            return_value=trecho_classe_buser_django_infos,
        ),
    ):
        params_verifica_poltrona = VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)
        poltronas = CompraRodoviariaSVC(params_verifica_poltrona).verifica_poltrona(params_verifica_poltrona)

    preco = mock_corridas.corridas[0]["price"]
    comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2, valor_cheio=preco)
    comprar_params.poltronas = poltronas
    CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    passagens_compradas = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id)

    assert passagens_compradas.count() == 2
    for p in passagens_compradas:
        assert p.status == Passagem.Status.CONFIRMADA
        assert p.company_integracao_id == ti_sistemas_api.company.id
        assert p.poltrona_external_id in poltronas

    reserva_svc.efetua_cancelamento(
        travel_id=comprar_params.travel_id,
        buseiro_id=passagens_compradas[0].buseiro_internal_id,
    )
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CONFIRMADA

    reserva_svc.efetua_cancelamento(travel_id=comprar_params.travel_id)
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CANCELADA


def test_compra_duas_poltronas_da_erro_na_segunda(
    ti_sistemas_api,
    trecho_classe_mock,
    mock_bloquear_duas_poltronas,
    mock_efetuar_compra_duas_poltronas_da_erro_na_segunda,
    mock_desbloquear_poltrona_sucesso,
):
    tc_id = trecho_classe_mock.trechoclasse_internal_id
    params = _comprar_params(tc_id, quantidade_passageiros=2)
    params.poltronas = [9, 10]

    with pytest.raises(TiSistemasEfetuarCompraException):
        ti_sistemas_api.comprar(params)

    passagens = Passagem.objects.filter(trechoclasse_integracao=trecho_classe_mock).order_by("poltrona_external_id")

    assert passagens[0].tags_set() == {"cancelamento_pendente"}
    assert passagens[0].status == Passagem.Status.CONFIRMADA
    assert passagens[1].status == Passagem.Status.ERRO
    assert passagens[1].erro == "Reserva da poltrona não encontrada"


def test_comprar_erro_reserva_nao_encontrada_com_retry(
    ti_sistemas_api,
    requests_mock,
    trecho_classe_mock,
    mock_bloquear_duas_poltronas,
    mock_efetuar_compra_erro_reserva_nao_encontrada,
    mock_desbloquear_poltrona_sucesso,
):
    tc_id = trecho_classe_mock.trechoclasse_internal_id
    params = _comprar_params(tc_id, quantidade_passageiros=2)
    params.poltronas = [9, 10]

    with pytest.raises(TiSistemasEfetuarCompraException, match="Reserva não encontrada"):
        ti_sistemas_api.comprar(params)

    passagens = Passagem.objects.filter(trechoclasse_integracao=trecho_classe_mock).order_by("poltrona_external_id")

    assert len(passagens) == 2
    assert passagens[0].status == Passagem.Status.ERRO
    assert passagens[0].erro == "Reserva não encontrada"
    assert passagens[1].status == Passagem.Status.INCOMPLETA

    call_urls = [call.request.url for call in requests_mock.calls]
    assert len(call_urls) == 6
    assert [url.startswith(endpoints.EfetuarCompraConfig(ti_sistemas_api.login).url) for url in call_urls]


def test_cancela_venda_cancela_passagens(
    ti_sistemas_api,
    mock_cancelar_compra_sucesso,
    trecho_classe_mock,
    passagens_para_cancelar,
):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    travel_id = passagens_para_cancelar[0].travel_internal_id
    cancelar_params = CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": trecho_classe_id})

    ti_sistemas_api.cancela_venda(cancelar_params)

    for p in passagens_para_cancelar:
        p.refresh_from_db()
        assert p.status == Passagem.Status.CANCELADA


def test_cancela_venda_bilhete_ja_impresso(
    ti_sistemas_api,
    mock_cancelar_compra_bilhete_ja_impresso,
    trecho_classe_mock,
    passagens_para_cancelar,
):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    travel_id = passagens_para_cancelar[0].travel_internal_id
    cancelar_params = CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": trecho_classe_id})

    with pytest.raises(PassengerTicketAlreadyPrintedException):
        ti_sistemas_api.cancela_venda(cancelar_params)

    for p in passagens_para_cancelar:
        p.refresh_from_db()
        assert p.status == Passagem.Status.CONFIRMADA


def test_cancela_venda_duas_passagens_da_erro_na_segunda(
    ti_sistemas_api,
    mock_cancelar_compra_duas_passagens_da_erro_na_segunda,
    trecho_classe_mock,
    passagens_para_cancelar,
):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    travel_id = passagens_para_cancelar[0].travel_internal_id
    cancelar_params = CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": trecho_classe_id})

    with pytest.raises(TiSistemasCancelarCompraException, match="Reserva não encontrada"):
        ti_sistemas_api.cancela_venda(cancelar_params)

    for p in passagens_para_cancelar:
        p.refresh_from_db()

    assert passagens_para_cancelar[0].status == Passagem.Status.CANCELADA
    assert passagens_para_cancelar[1].status == Passagem.Status.CONFIRMADA
    assert passagens_para_cancelar[1].erro_cancelamento == "Reserva não encontrada"


def test_consultar_compra(ti_sistemas_api, mock_consultar_reserva_sucesso):
    params = {
        "seat_id": "1223757",
        "order_id": 100001,
    }
    response = ti_sistemas_api._consultar_reserva_request(params)
    assert isinstance(response, models.ConsultarReservaOutput)


def test_desbloquear_poltrona(ti_sistemas_api, mock_desbloquear_poltrona_sucesso):
    params = {
        "seat_id": "1223757",
        "order_id": 100001,
    }
    response = ti_sistemas_api._desbloquear_poltrona_request(params)
    assert response is None


@responses.activate
def test_desbloquear_poltrona_request_retry_status_code_400_response_none(ti_sistemas_api, ti_sistemas_login):
    # data uma response com status_code 400 e json=None
    params = {"seat_id": "1223757", "order_id": 100001}
    base_url = endpoints.DesbloquearPoltronaConfig(ti_sistemas_login).url
    endpoint = f"{base_url}?seatId={params['seat_id']}&orderId={params['order_id']}"
    responses.add(
        responses.DELETE,
        endpoint,
        status=400,
    )
    responses.add(
        responses.DELETE,
        endpoint,
        status=200,
    )

    # deve haver uma tentativa de retry
    resp = ti_sistemas_api._desbloquear_poltrona_request(params)
    # e em caso de sucesso após retry, que a resposta esperada seja retornada.
    assert resp is None
    assert responses.assert_call_count(endpoint, 2)


def test_cancelar_compra(ti_sistemas_api, mock_cancelar_compra_sucesso):
    params = {
        "seat_id": "1223757",
        "order_id": 100001,
    }
    response = ti_sistemas_api._cancelar_compra_request(params)
    assert response is True


def test_buscar_trechos_vendidos_origem_destino_iguais(
    ti_sistemas_api, mock_buscar_trechos_vendidos_origem_destino_igual
):
    response = ti_sistemas_api.buscar_trechos_vendidos(id_viagem="1212")
    assert response == [
        models.BuscarTrechosVendidosOutput.parse_obj(
            mock_trechos_vendidos.trechos_vendidos_origem_e_destino_iguais["list"][0]
        )
    ]


@pytest.mark.parametrize(
    "cpf_comprador,cpf_pax,cpf_buyer,cpf_result",
    [
        pytest.param(
            "cpf_comprador",
            "cpf_pax",
            "cpf_buyer",
            "cpf_buyer",
            id="usa_cpf_buyer",
        ),
        pytest.param(
            None,
            "",
            "cpf_buyer",
            "cpf_buyer",
            id="usa_cpf_buyer_2",
        ),
        pytest.param(
            "cpf_comprador",
            "cpf_pax",
            "",
            "cpf_comprador",
            id="usa_cpf_comprador",
        ),
        pytest.param(
            "",
            "cpf_pax",
            "",
            "cpf_pax",
            id="usa_cpf_pax",
        ),
        pytest.param(
            None,
            "",
            None,
            "",
            id="nao_envia_cpf",
        ),
    ],
)
def test_get_cpf_comprador(ti_sistemas_api, cpf_comprador, cpf_pax, cpf_buyer, cpf_result):
    assert (
        ti_sistemas_api._get_cpf_comprador(
            SimpleNamespace(cpf=cpf_comprador), SimpleNamespace(buyer_cpf=cpf_buyer, cpf=cpf_pax)
        )
        == cpf_result
    )


def test_get_desenho_mapa_poltronas(mock_mapa_viagem_detalhada, ti_sistemas_api, ti_sistemas_trechoclasses):
    trecho_classe_id = ti_sistemas_trechoclasses.ida.trechoclasse_internal_id
    baker.make(
        Passagem,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=ti_sistemas_trechoclasses.ida,
        poltrona_external_id="9",
    )
    mapa_poltronas = ti_sistemas_api.get_desenho_mapa_poltronas(trecho_classe_id)
    assert isinstance(mapa_poltronas, Onibus)
    tipo_assento = "convencional"
    categoria = Passagem.CategoriaEspecial.NORMAL
    assert mapa_poltronas.dict() == {
        "layout": [
            {
                "andar": 1,
                "assentos": [
                    {
                        "livre": False,
                        "x": 1,
                        "y": 1,
                        "numero": 1,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 1,
                        "numero": 2,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 1,
                        "numero": 3,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 1,
                        "numero": 4,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 2,
                        "numero": 5,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 2,
                        "y": 2,
                        "numero": 6,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 4,
                        "y": 2,
                        "numero": 7,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 5,
                        "y": 2,
                        "numero": 8,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": False,
                        "x": 1,
                        "y": 3,
                        "numero": 9,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                    {
                        "livre": True,
                        "x": 2,
                        "y": 3,
                        "numero": 10,
                        "tipo_assento": tipo_assento,
                        "categoria_especial": categoria,
                        "preco": None,
                    },
                ],
            }
        ]
    }


def test_bloquear_poltronas_v2_erro_ja_ocupada(
    ti_sistemas_api, trecho_classe_mock, mock_bloquear_uma_poltrona_erro_ja_ocupada
):
    with pytest.raises(PoltronaJaSelecionadaException):
        ti_sistemas_api.bloquear_poltronas_v2(
            trecho_classe_mock.trechoclasse_internal_id, 9, Passagem.CategoriaEspecial.NORMAL
        )

    poltrona = ti_sistemas_api.cache.get_poltrona_indisponivel_cache(trecho_classe_mock.trechoclasse_internal_id)
    assert poltrona.pop() == "09"


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_bloquear_poltronas_v2(mocker, ti_sistemas_api, trecho_classe_mock, mock_bloquear_uma_poltrona):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    poltrona = 9
    order_id = 185256644289
    mocker.patch.object(TiSistemasAPI, "_generate_order_id", return_value=order_id)
    response = ti_sistemas_api.bloquear_poltronas_v2(trecho_classe_id, poltrona, Passagem.CategoriaEspecial.NORMAL)
    assert isinstance(response, BloquearPoltronasResponse)
    expected_cache = {
        "order_id": order_id,
        "seat_id": 1223757,
        "voucher": None,
        "seat": poltrona,
        "status": "Reservada",
    }
    expected_best_before = datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")) + timedelta(
        minutes=20
    )
    assert TiSistemasMC().get_poltrona_cache_key(trecho_classe_id, poltrona) == expected_cache
    assert response == BloquearPoltronasResponse(
        seat=poltrona,
        best_before=expected_best_before,
        external_payload=expected_cache,
    )


def test_create_passagens(mocker, ti_sistemas_api, trecho_classe_mock):
    cache_value = {"seat": 1, "seat_id": 10, "order_id": 100}
    mocker.patch.object(TiSistemasAPI, "_get_cached_poltrona_ou_bloqueia", return_value=cache_value)
    params = ComprarForm(
        trechoclasse_id=trecho_classe_mock.trechoclasse_internal_id,
        travel_id=1521,
        valor_cheio=D("500.00"),
        preco_rodoviaria=D("650.00"),
        poltronas=[11],
        passageiros=[
            PassageiroForm(
                id=15,
                name="Buseiro de conexao",
                rg_number="*********",
                phone="*********12",
                cpf="10101001090",
            )
        ],
    )
    passagens = ti_sistemas_api._create_passagens(params)
    assert len(passagens) == 1
    assert passagens[0].poltrona_external_id == cache_value["seat"]
    assert passagens[0].localizador == cache_value["seat_id"]
    assert passagens[0].pedido_external_id == cache_value["order_id"]


def test_create_passagens_extra_poltronas(mocker, ti_sistemas_api, trecho_classe_mock):
    cache_value = {"seat": 1, "seat_id": 10, "order_id": 100}
    extra_poltronas = {"seat": 2, "seat_id": 30, "order_id": 300}
    mocker.patch.object(TiSistemasAPI, "_get_cached_poltrona_ou_bloqueia", return_value=cache_value)
    params = ComprarForm(
        trechoclasse_id=trecho_classe_mock.trechoclasse_internal_id,
        travel_id=1521,
        valor_cheio=D("500.00"),
        preco_rodoviaria=D("650.00"),
        poltronas=[11],
        passageiros=[
            PassageiroForm(
                id=15,
                name="Buseiro de conexao",
                rg_number="*********",
                phone="*********12",
                cpf="10101001090",
            )
        ],
        extra_poltronas=extra_poltronas,
    )
    passagens = ti_sistemas_api._create_passagens(params)
    assert len(passagens) == 1
    assert passagens[0].poltrona_external_id == extra_poltronas["seat"]
    assert passagens[0].localizador == extra_poltronas["seat_id"]
    assert passagens[0].pedido_external_id == extra_poltronas["order_id"]

from datetime import datetime, timed<PERSON><PERSON>
from unittest import mock

import pytest
import time_machine
from celery.result import GroupResult
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz
from rodoviaria.api.ti_sistemas import models
from rodoviaria.models.core import <PERSON><PERSON>, <PERSON><PERSON><PERSON>
from rodoviaria.service import descobrir_rotas_ti_sistemas_async_svc
from rodoviaria.tests.ti_sistemas.mock_data_response import mock_buscar_itinerario_normal, mock_lista_viagens


@pytest.mark.parametrize(
    "mock_buscar_lista_viagens", [{"data_inicio": "2023-01-12", "data_fim": "2023-01-19"}], indirect=True
)
@time_machine.travel(datetime(2023, 1, 12, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_descobrir_rotas(ti_sistemas_api, mock_buscar_lista_viagens):
    with mock.patch(
        "rodoviaria.service.descobrir_rotas_ti_sistemas_async_svc._buscar_e_salvar_rotas_dos_servicos"
    ) as mock_buscar_e_salvar_rotas_dos_servicos:
        descobrir_rotas_ti_sistemas_async_svc.descobrir_rotas(
            ti_sistemas_api.login, ti_sistemas_api.company.company_internal_id, next_days=10, shift_days=0
        )
    expected_viagens = [models.Viagem.parse_obj(v) for v in mock_lista_viagens.viagens["list"]]
    mock_buscar_e_salvar_rotas_dos_servicos.assert_called_once_with(
        expected_viagens,
        ti_sistemas_api.company.company_internal_id,
        10,
        descobrir_rotas_ti_sistemas_async_svc.DEFAULT_QUEUE_NAME,
        False,
    )


def test_buscar_e_salvar_rotas_dos_servicos_create_duas_rotas_mesmo_hash(ti_sistemas_api, mock_buscar_itinerario):
    viagens = [models.Viagem.parse_obj(v) for v in mock_lista_viagens.viagens["list"][:2]]
    expected_hash = models.Itinerario.parse_obj(mock_buscar_itinerario_normal.itinerario["list"]).hash
    with mock.patch("celery.result.EagerResult.parent") as mock_chord_parent:
        mock_chord_parent.return_value = GroupResult("123")
        descobrir_rotas_ti_sistemas_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            ti_sistemas_api.company.company_internal_id,
            7,
            descobrir_rotas_ti_sistemas_async_svc.DEFAULT_QUEUE_NAME,
            False,
        )
    assert Rota.objects.get(id_hash=expected_hash).id_external == str(viagens[0].id_viagem)


def test_buscar_e_salvar_rotas_dos_servicos_create_duas_rotas_hashes_diferentes(
    ti_sistemas_api, mock_buscar_itinerario_duas_vezes_hashs_diferentes
):
    viagens = [models.Viagem.parse_obj(v) for v in mock_lista_viagens.viagens["list"][:2]]
    with mock.patch("celery.result.EagerResult.parent") as mock_chord_parent:
        mock_chord_parent.return_value = GroupResult("123")
        descobrir_rotas_ti_sistemas_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            ti_sistemas_api.company.company_internal_id,
            7,
            descobrir_rotas_ti_sistemas_async_svc.DEFAULT_QUEUE_NAME,
            False,
        )
    assert (
        Rota.objects.get(id_external=viagens[0].id_viagem).id_hash
        != Rota.objects.get(id_external=viagens[1].id_viagem).id_hash
    )


def test_buscar_e_salvar_rotas_dos_servicos_criar_rotinas_da_mesma_rota(
    ti_sistemas_api, mock_buscar_itinerario_duas_vezes_com_rotinas_diferentes
):
    viagens = [models.Viagem.parse_obj(v) for v in mock_lista_viagens.viagens["list"][:2]]
    itinerario = mock_buscar_itinerario_normal.itinerario["list"]
    expected_hash = models.Itinerario.parse_obj(itinerario).hash
    with mock.patch("celery.result.EagerResult.parent") as mock_chord_parent:
        mock_chord_parent.return_value = GroupResult("123")
        descobrir_rotas_ti_sistemas_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            ti_sistemas_api.company.company_internal_id,
            7,
            descobrir_rotas_ti_sistemas_async_svc.DEFAULT_QUEUE_NAME,
            False,
        )
    datetime_ida_1 = to_tz(
        to_default_tz(datetime.strptime(itinerario[0]["dataHoraPartida"], "%Y-%m-%d %H:%M:%S")),
        "UTC",
    )
    datetime_ida_2 = datetime_ida_1 - timedelta(minutes=30)
    rota = Rota.objects.get(id_hash=expected_hash)
    rotinas = list(Rotina.objects.filter(rota=rota).values_list("datetime_ida", flat=True))
    assert rotinas == [datetime_ida_2, datetime_ida_1]


def test_buscar_e_salvar_rotas_dos_servicos_update(ti_sistemas_api, mock_buscar_itinerario):
    viagens = [models.Viagem.parse_obj(v) for v in mock_lista_viagens.viagens["list"][:1]]
    itinerario = mock_buscar_itinerario_normal.itinerario["list"]
    expected_hash = models.Itinerario.parse_obj(itinerario).hash
    rota = baker.make(
        Rota,
        id_hash=expected_hash,
        id_external="vai_ser_trocado",
        company=ti_sistemas_api.company,
    )
    with mock.patch("celery.result.EagerResult.parent") as mock_chord_parent, mock.patch(
        "rodoviaria.service.salva_rotas_bulk_svc._is_rota_valid_to_update", return_value=True
    ):
        mock_chord_parent.return_value = GroupResult("123")
        descobrir_rotas_ti_sistemas_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            ti_sistemas_api.company.company_internal_id,
            7,
            descobrir_rotas_ti_sistemas_async_svc.DEFAULT_QUEUE_NAME,
            False,
        )
    datetime_ida_rotina = to_tz(
        to_default_tz(datetime.strptime(itinerario[0]["dataHoraPartida"], "%Y-%m-%d %H:%M:%S")),
        "UTC",
    )
    rotina = Rotina.objects.get(rota=rota)
    assert rotina.datetime_ida == datetime_ida_rotina
    rota.refresh_from_db()
    assert rota.id_external == str(viagens[0].id_viagem)


def test_buscar_e_salvar_rotas_dos_servicos_return_task(ti_sistemas_api):
    with mock.patch("rodoviaria.service.descobrir_rotas_ti_sistemas_async_svc.chain") as mock_chain:
        viagens = [models.Viagem.parse_obj(v) for v in mock_lista_viagens.viagens["list"]]
        task = descobrir_rotas_ti_sistemas_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            ti_sistemas_api.company.company_internal_id,
            14,
            descobrir_rotas_ti_sistemas_async_svc.DEFAULT_QUEUE_NAME,
            True,
        )
    assert task == mock_chain.return_value
    assert len(mock_chain.call_args[0][0].tasks) == len(viagens)
    assert (
        mock_chain.call_args[0][1].task == "rodoviaria.service.salva_rotas_bulk_svc.salvar_rotas_achadas_em_bulk_task"
    )

from datetime import datetime, <PERSON><PERSON><PERSON>
from http import HTTPStatus

import pytest
import responses
import time_machine
from django.utils import timezone
from pydantic import parse_obj_as
from zoneinfo import ZoneInfo

from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.api.forms import RetornoItinerario
from rodoviaria.api.ti_sistemas import endpoints, models
from rodoviaria.api.ti_sistemas.api import TiSistemasAPI
from rodoviaria.service.exceptions import RodoviariaConnectionError, RodoviariaUnauthorizedError
from rodoviaria.tests.ti_sistemas.mock_data_response import mock_unauthorized


def test_unauthorized_request(requests_mock, ti_sistemas_api: TiSistemasAPI):
    requests_mock.add(
        responses.GET,
        endpoints.BuscarAgenciasConfig(ti_sistemas_api.login).url,
        json=mock_unauthorized.unauthorized,
        status=403,
    )
    with pytest.raises(RodoviariaUnauthorizedError):
        endpoints.BuscarAgenciasConfig(ti_sistemas_api.login).invoke(get_http_executor(), json={"value": "Brasilia"})


def test_connection_error_request(requests_mock, ti_sistemas_api: TiSistemasAPI):
    with pytest.raises(RodoviariaConnectionError):
        endpoints.BuscarAgenciasConfig(ti_sistemas_api.login).invoke(get_http_executor(), json={"value": "Brasilia"})


def test_buscar_agencias(mock_buscar_agencias, ti_sistemas_api: TiSistemasAPI):
    response = endpoints.BuscarAgenciasConfig(ti_sistemas_api.login).invoke(
        get_http_executor(), json={"value": "Brasilia"}
    )
    parsed = parse_obj_as(list[models.Local], response.json()["list"])
    assert isinstance(parsed[0], models.Local)
    assert mock_buscar_agencias.calls[0][0].headers["I-Auth"] == "Bearer auth_key_teste"


def test_buscar_itinerario(requests_mock, mock_buscar_itinerario, ti_sistemas_api: TiSistemasAPI):
    executor = get_http_executor()
    request_config = endpoints.BuscarItinerarioConfig(ti_sistemas_api.login, company_external_id=1, id_viagem=1212)
    response = request_config.invoke(
        executor,
    )
    response_json = response.json()
    response = RetornoItinerario(
        raw=response_json,
        cleaned=response_json["list"],
        parsed=parse_obj_as(models.Itinerario, response.json()["list"]),
    )
    assert isinstance(response.parsed, models.Itinerario)
    assert response.parsed[0].duracao == 0
    for checkpoint in response.parsed[1:]:
        assert checkpoint.duracao > 0
    assert len(response.parsed) == 12
    assert mock_buscar_itinerario.calls[0][0].headers["I-Auth"] == "Bearer auth_key_teste"


@time_machine.travel(datetime(2023, 1, 12, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_buscar_lista_viagens(mock_buscar_lista_viagens, ti_sistemas_api: TiSistemasAPI):
    data_inicio = timezone.now()
    data_fim = data_inicio + timedelta(days=10)
    executor = get_http_executor()
    request_config = endpoints.BuscarListaViagensConfig(ti_sistemas_api.login, 141, data_inicio, data_fim)
    response = request_config.invoke(
        executor,
    )
    parsed_response = parse_obj_as(list[models.Viagem], response.json()["list"])
    assert isinstance(parsed_response[0], models.Viagem)
    assert mock_buscar_lista_viagens.calls[0][0].headers["I-Auth"] == "Bearer auth_key_teste"


def test_mapa_viagem(mock_mapa_viagem_detalhada, ti_sistemas_api):
    params = models.MapaViagemInput(
        date_ida="2024-02-06",
        origem_external_id=977,
        destino_external_id=5337,
        company_external_id=142,
        viagem_id=366,
    )
    params = models.MapaViagemInput.parse_obj(params)
    executor = get_http_executor()
    request_config = endpoints.MapaViagemConfig(ti_sistemas_api.login)
    response = request_config.invoke(executor, json=params.dict(by_alias=True))
    response_parsed = parse_obj_as(models.LayoutViagem, response.json())
    assert isinstance(response_parsed, models.LayoutViagem)


def test_buscar_corridas_endpoint(mock_buscar_corridas, ti_sistemas_login):
    params = models.BuscarCorridasInput(origem=977, destino=5337, data="2024-02-06")
    executor = get_http_executor()
    request_config = endpoints.BuscarCorridasConfig(ti_sistemas_login)
    response = request_config.invoke(executor, json=params.dict(by_alias=True))
    corridas = parse_obj_as(list[models.Corrida], response.json())
    # response = endpoints.BuscarCorridas(ti_sistemas_api.login).send(params=request_params.dict(by_alias=True))
    assert isinstance(corridas[0], models.Corrida)


def test_bloquear_uma_poltrona(mock_bloquear_uma_poltrona, ti_sistemas_login):
    params = models.BloquearPoltronaInput(
        date_ida="2024-02-06",
        company_external_id=141,
        origem_external_id=977,
        destino_external_id=5337,
        viagem_id=366,
        order_id=1002,
        seats=[10],
    )
    executor = get_http_executor()
    request_config = endpoints.BloquearPoltronaConfig(ti_sistemas_login)
    response = request_config.invoke(executor, json=params.dict(by_alias=True))
    response_parsed = parse_obj_as(models.BloquearPoltronaOutput, response.json())
    assert isinstance(response_parsed, models.BloquearPoltronaOutput)


def test_desbloquear_uma_poltrona(mock_desbloquear_poltrona_sucesso, ti_sistemas_login):
    params = models.DesbloquearPoltronaInput(seat_id=1, order_id=123456)
    executor = get_http_executor()
    request_config = endpoints.DesbloquearPoltronaConfig(ti_sistemas_login)
    response = request_config.invoke(executor, json=params.dict(by_alias=True))
    assert response.status_code == HTTPStatus.OK
    assert response.text() is None


def test_efetuar_compra(mock_efetuar_compra_sucesso, ti_sistemas_login):
    comprador = models.Comprador(
        email="<EMAIL>", nome="buseiro", cpf="12345678900", telefone="12999998888"
    )
    params = models.EfetuarCompraInput(
        seat_id=1223757, order_id=100001, nome="buseiro", documento="12345", comprador=comprador
    )
    executor = get_http_executor()
    request_config = endpoints.EfetuarCompraConfig(ti_sistemas_login)
    response = request_config.invoke(executor, json=params.dict(by_alias=True))
    response_parsed = parse_obj_as(models.EfetuarCompraOutput, response.json())
    assert response.status_code == HTTPStatus.OK
    assert response_parsed.order_id == "100001"


def test_consultar_reserva(mock_consultar_reserva_sucesso, ti_sistemas_login):
    params = models.ConsultarReservaInput(seat_id=1223757, orderId=100001)
    executor = get_http_executor()
    request_config = endpoints.ConsultarReservaConfig(ti_sistemas_login)
    response = request_config.invoke(executor, json=params.dict(by_alias=True))
    response_parsed = parse_obj_as(models.ConsultarReservaOutput, response.json())
    assert response.status_code == HTTPStatus.OK
    assert response_parsed.order_id == "100001"


def test_cancelar_reserva(mock_cancelar_compra_sucesso, ti_sistemas_login):
    params = models.ConsultarReservaInput(seat_id=1223757, orderId=100001)
    executor = get_http_executor()
    request_config = endpoints.CancelarCompraConfig(ti_sistemas_login)
    response = request_config.invoke(executor, json=params.dict(by_alias=True))
    assert response.status_code == HTTPStatus.OK


def test_buscar_trechos_vendidos(mock_buscar_trechos_vendidos, ti_sistemas_api: TiSistemasAPI):
    executor = get_http_executor()
    request_config = endpoints.BuscarTrechosVendidosConfig(ti_sistemas_api.login, 141, 1212)
    response = request_config.invoke(executor)
    response_parsed = parse_obj_as(list[models.BuscarTrechosVendidosOutput], response.json()["list"])
    assert isinstance(response_parsed[0], models.BuscarTrechosVendidosOutput)
    assert len(response_parsed) == 6
    assert mock_buscar_trechos_vendidos.calls[0][0].headers["I-Auth"] == "Bearer auth_key_teste"


@responses.activate
@pytest.mark.parametrize("http_status", [400, 429])
@time_machine.travel(datetime(2024, 7, 25, 9, 47, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_buscar_lista_viagens_request_retry(ti_sistemas_login, http_status):
    # data uma chamada para o endpoint BuscarListaViagens
    data_inicio = datetime.now().date()
    data_fim = data_inicio + timedelta(days=2)
    endpoint = endpoints.BuscarListaViagensConfig(
        ti_sistemas_login, company_external_id=141, data_inicio=data_inicio, data_fim=data_fim
    ).url
    viagens = {"list": [{"id": 20240206366, "idViagem": 20240206366, "dataHoraPartida": "2024-02-06 21:15:00.0"}]}
    # com retorno inicial com status_code 400 e response None
    responses.add(
        responses.GET,
        endpoint,
        status=http_status,
    )
    responses.add(
        responses.GET,
        endpoint,
        json=viagens,
        status=200,
    )
    # espero que ao chamar o método buscar_lista_viagens_request seja realizado ao menos um retry
    resp = endpoints.buscar_lista_viagens_request(
        ti_sistemas_login, company_external_id=141, data_inicio=data_inicio, data_fim=data_fim
    )

    # e em caso de sucesso após retry, que a resposta esperada seja retornada.
    assert resp == parse_obj_as(list[models.Viagem], viagens["list"])
    assert responses.assert_call_count(endpoint, 2)

from rodoviaria.tests.ti_sistemas.mock_data_response import mock_buscar_itinerario_normal

itinerario = mock_buscar_itinerario_normal.itinerario["list"]
trechos_vendidos = {
    "list": [
        {
            "id": 16347,
            "origemId": itinerario[0]["localId"],
            "destinoId": itinerario[2]["localId"],
            "vagas": 12,
            "classe": "LEITO COM AR CONDICIONADO",
            "capacidade": 12,
            "preco": 190.0,
        },
        {
            "id": 16349,
            "origemId": itinerario[0]["localId"],
            "destinoId": itinerario[3]["localId"],
            "vagas": 12,
            "classe": "LEITO COM AR CONDICIONADO",
            "capacidade": 12,
            "preco": 225.0,
        },
        {
            "id": 16353,
            "origemId": itinerario[0]["localId"],
            "destinoId": itinerario[4]["localId"],
            "vagas": 12,
            "classe": "LEITO COM AR CONDICIONADO",
            "capacidade": 12,
            "preco": 230.0,
        },
        {
            "id": 16358,
            "origemId": itinerario[1]["localId"],
            "destinoId": itinerario[2]["localId"],
            "vagas": 12,
            "classe": "LEITO COM AR CONDICIONADO",
            "capacidade": 12,
            "preco": 88.0,
        },
        {
            "id": 16362,
            "origemId": itinerario[1]["localId"],
            "destinoId": itinerario[3]["localId"],
            "vagas": 12,
            "classe": "LEITO COM AR CONDICIONADO",
            "capacidade": 12,
            "preco": 195.0,
        },
        {
            "id": 16364,
            "origemId": itinerario[1]["localId"],
            "destinoId": itinerario[4]["localId"],
            "vagas": 12,
            "classe": "LEITO COM AR CONDICIONADO",
            "capacidade": 12,
            "preco": 860.0,
        },
    ],
    "total": 6,
}


trechos_vendidos_origem_e_destino_iguais = {
    "list": [
        {
            "id": 16347,
            "origemId": itinerario[0]["localId"],
            "destinoId": itinerario[-1]["localId"],
            "vagas": 12,
            "classe": "LEITO COM AR CONDICIONADO",
            "capacidade": 12,
            "preco": 190.0,
        },
        {
            "id": 16349,
            "origemId": itinerario[1]["localId"],
            "destinoId": itinerario[1]["localId"],
            "vagas": 12,
            "classe": "LEITO COM AR CONDICIONADO",
            "capacidade": 12,
            "preco": 225.0,
        },
    ],
    "total": 1,
}

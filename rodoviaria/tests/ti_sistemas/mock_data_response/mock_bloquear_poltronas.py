response_uma_poltrona = {
    "orderId": "100001",
    "seats": [{"seatId": 1223757, "voucher": None, "seat": "9", "status": "Reservada"}],
}

response_uma_poltrona_erro = {"id": "", "code": "", "type": "", "message": None}

response_uma_poltrona_erro_ja_ocupada = {"id": "", "code": "", "type": "", "message": "O banco 9 já está ocupado"}

response_duas_poltronas = {
    "orderId": "100002",
    "seats": [
        {"seatId": 1223761, "voucher": None, "seat": "9", "status": "Cancelada"},
        {"seatId": 1224906, "voucher": None, "seat": "10", "status": "Reservada"},
    ],
}

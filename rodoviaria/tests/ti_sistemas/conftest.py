import re
from copy import deepcopy
from datetime import date, datetime, timedelta

import pytest
import responses

from rodoviaria.api.ti_sistemas import endpoints
from rodoviaria.api.ti_sistemas.api import TiSistemasAPI
from rodoviaria.tests.ti_sistemas.mock_data_response import (
    mock_agencias,
    mock_bloquear_poltronas,
    mock_buscar_itinerario_normal,
    mock_cancelar_compra,
    mock_consultar_reserva,
    mock_corridas,
    mock_efetuar_compra,
    mock_lista_viagens,
    mock_mapa_viagem,
    mock_trechos_vendidos,
)


@pytest.fixture
def ti_sistemas_api(ti_sistemas_login):
    return TiSistemasAPI(ti_sistemas_login.company)


@pytest.fixture
def mock_buscar_agencias(requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.GET,
        endpoints.BuscarAgenciasConfig(ti_sistemas_login).url,
        json=mock_agencias.agencias,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_itinerario(request, requests_mock, ti_sistemas_login):
    endpoint = endpoints.BuscarItinerarioConfig(
        ti_sistemas_login,
        company_external_id=r"\w+",
        id_viagem=r"\w+",
    ).url
    requests_mock.add(
        responses.GET,
        re.compile(endpoint),
        json=mock_buscar_itinerario_normal.itinerario,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_lista_viagens(request, requests_mock, ti_sistemas_login):
    endpoint = endpoints.BuscarListaViagensConfig(
        ti_sistemas_login, company_external_id=141, data_inicio=date(2023, 1, 12), data_fim=date(2023, 1, 22)
    ).url
    requests_mock.add(
        responses.GET,
        endpoint,
        json=mock_lista_viagens.viagens,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_itinerario_duas_vezes_hashs_diferentes(request, requests_mock, ti_sistemas_login):
    endpoint = endpoints.BuscarItinerarioConfig(
        ti_sistemas_login,
        company_external_id=r"\w+",
        id_viagem=r"\w+",
    ).url
    itinerario_1 = mock_buscar_itinerario_normal.itinerario.copy()
    itinerario_1["list"] = itinerario_1["list"][:3]
    requests_mock.add(
        responses.GET,
        re.compile(endpoint),
        json=itinerario_1,
        status=200,
    )
    itinerario_2 = mock_buscar_itinerario_normal.itinerario.copy()
    itinerario_2["list"] = itinerario_2["list"][3:]
    requests_mock.add(
        responses.GET,
        re.compile(endpoint),
        json=itinerario_2,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_itinerario_duas_vezes_com_rotinas_diferentes(request, requests_mock, ti_sistemas_login):
    endpoint = endpoints.BuscarItinerarioConfig(
        ti_sistemas_login,
        company_external_id=r"\w+",
        id_viagem=r"\w+",
    ).url
    itinerario_1 = mock_buscar_itinerario_normal.itinerario.copy()
    itinerario_2 = {"list": []}
    for cp in itinerario_1["list"]:
        data_hora_partida = datetime.strptime(cp["dataHoraPartida"], "%Y-%m-%d %H:%M:%S")
        cp_dict = cp.copy()
        cp_dict["dataHoraPartida"] = (data_hora_partida - timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S")
        itinerario_2["list"].append(cp_dict)
    requests_mock.add(
        responses.GET,
        re.compile(endpoint),
        json=itinerario_1,
        status=200,
    )
    requests_mock.add(
        responses.GET,
        re.compile(endpoint),
        json=itinerario_2,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_mapa_viagem_detalhada(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.GET,
        endpoints.MapaViagemConfig(ti_sistemas_login).url,
        json=mock_mapa_viagem.mapa_viagem,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_corridas(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(ti_sistemas_login).url,
        json=mock_corridas.corridas,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_corridas_vazio(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(ti_sistemas_login).url,
        json=[],
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_bloquear_uma_poltrona(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(ti_sistemas_login).url,
        json=mock_bloquear_poltronas.response_uma_poltrona,
        status=200,
    )


@pytest.fixture
def mock_bloquear_uma_poltrona_erro(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(ti_sistemas_login).url,
        json=mock_bloquear_poltronas.response_uma_poltrona_erro,
        status=500,
    )


@pytest.fixture
def mock_bloquear_uma_poltrona_erro_ja_ocupada(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(ti_sistemas_login).url,
        json=mock_bloquear_poltronas.response_uma_poltrona_erro_ja_ocupada,
        status=500,
    )


@pytest.fixture
def mock_bloquear_duas_poltronas(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(ti_sistemas_login).url,
        json=mock_bloquear_poltronas.response_duas_poltronas,
        status=200,
    )


@pytest.fixture
def mock_desbloquear_poltrona_sucesso(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.DELETE,
        endpoints.DesbloquearPoltronaConfig(ti_sistemas_login).url,
        status=200,
    )


@pytest.fixture
def mock_efetuar_compra_sucesso(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.PUT,
        endpoints.EfetuarCompraConfig(ti_sistemas_login).url,
        json=mock_efetuar_compra.response_reserva,
        status=200,
    )


@pytest.fixture
def mock_efetuar_compra_erro_reserva_nao_encontrada(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.PUT,
        endpoints.EfetuarCompraConfig(ti_sistemas_login).url,
        json=mock_efetuar_compra.response_erro_reserva_nao_encontrada,
        status=500,
    )


@pytest.fixture
def mock_efetuar_compra_duas_poltronas_da_erro_na_segunda(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.PUT,
        endpoints.EfetuarCompraConfig(ti_sistemas_login).url,
        json=mock_efetuar_compra.response_reserva,
        status=200,
    )

    requests_mock.add(
        responses.PUT,
        endpoints.EfetuarCompraConfig(ti_sistemas_login).url,
        json=mock_efetuar_compra.response_erro_reserva_da_poltrona_nao_encontrada,
        status=500,
    )


@pytest.fixture
def mock_efetuar_compra_duas_poltronas_sucesso(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.PUT,
        endpoints.EfetuarCompraConfig(ti_sistemas_login).url,
        json=mock_efetuar_compra.response_reserva,
        status=200,
    )

    mock_efetuar_compra.response_reserva["seats"][0]["seat"] = "10"

    requests_mock.add(
        responses.PUT,
        endpoints.EfetuarCompraConfig(ti_sistemas_login).url,
        json=mock_efetuar_compra.response_reserva,
        status=200,
    )


@pytest.fixture
def mock_consultar_reserva_sucesso(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.GET,
        endpoints.ConsultarReservaConfig(ti_sistemas_login).url,
        json=mock_consultar_reserva.response_reserva,
        status=200,
    )


@pytest.fixture
def mock_cancelar_compra_sucesso(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarCompraConfig(ti_sistemas_login).url,
        status=200,
    )


@pytest.fixture
def mock_cancelar_compra_bilhete_ja_impresso(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarCompraConfig(ti_sistemas_login).url,
        json=mock_cancelar_compra.response_erro_bilhete_ja_impresso,
        status=500,
    )


@pytest.fixture
def mock_cancelar_compra_duas_passagens_da_erro_na_segunda(request, requests_mock, ti_sistemas_login):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarCompraConfig(ti_sistemas_login).url,
        status=200,
    )

    requests_mock.add(
        responses.POST,
        endpoints.CancelarCompraConfig(ti_sistemas_login).url,
        json=mock_cancelar_compra.response_erro_reserva_nao_encontrada,
        status=500,
    )


@pytest.fixture
def mock_buscar_trechos_vendidos(request, requests_mock, ti_sistemas_login):
    endpoint = endpoints.BuscarTrechosVendidosConfig(ti_sistemas_login, company_external_id=141, id_viagem=1212).url
    requests_mock.add(
        responses.GET,
        endpoint,
        json=mock_trechos_vendidos.trechos_vendidos,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_trechos_vendidos_origem_destino_igual(request, requests_mock, ti_sistemas_login):
    endpoint = endpoints.BuscarTrechosVendidosConfig(ti_sistemas_login, company_external_id=141, id_viagem=1212).url
    requests_mock.add(
        responses.GET,
        endpoint,
        json=mock_trechos_vendidos.trechos_vendidos_origem_e_destino_iguais,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_trechos_vendidos_precos_absurdos(request, requests_mock, ti_sistemas_login):
    endpoint = endpoints.BuscarTrechosVendidosConfig(ti_sistemas_login, company_external_id=141, id_viagem=1212).url
    trechos_vendidos = deepcopy(mock_trechos_vendidos.trechos_vendidos)
    trechos_vendidos["list"][0]["preco"] = 0
    trechos_vendidos["list"][1]["preco"] = 10000
    trechos_vendidos["list"] = trechos_vendidos["list"][:2]
    requests_mock.add(
        responses.GET,
        endpoint,
        json=trechos_vendidos,
        status=200,
    )
    yield requests_mock

from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal

import pytest
import time_machine
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models import TrechoClasseError
from rodoviaria.models.core import TrechoClasse
from rodoviaria.service.exceptions import RodoviariaTrechoclasseFactoryException
from rodoviaria.service.update_trechoclasse_with_api_data import (
    get_api_data,
    update_trechoclasse_with_api_data,
)


def test_get_api_data_from_trechoclasse(totalbus_login, totalbus_trechoclasses, mock_buscar_servico_totalbus):
    tc = totalbus_trechoclasses.ida
    tc.grupo_classe.tipo_assento_internal = "leito cama"
    tc.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:05", "%Y-%m-%d %H:%M"))

    api_data = get_api_data(
        totalbus_login.company,
        OrchestrateRodoviaria(
            totalbus_login.company.company_internal_id,
            totalbus_login.company.modelo_venda,
        ),
        tc.trechoclasse_internal_id,
        tc.datetime_ida,
        tc.origem.cidade.timezone,
        tc.origem,
        tc.destino,
        tc.grupo_classe.tipo_assento_internal,
    )

    assert api_data.tipo_veiculo is None
    assert api_data.external_id == "712"
    assert api_data.external_datetime_ida == tc.datetime_ida
    assert api_data.veiculo_andar == api_data.veiculo_id and api_data.veiculo_id is None


def test_get_api_data_from_trechoclasse_not_found(totalbus_login, totalbus_trechoclasses, mock_buscar_servico_totalbus):
    tc = totalbus_trechoclasses.ida
    with pytest.raises(RodoviariaTrechoclasseFactoryException) as exc:
        get_api_data(
            totalbus_login.company,
            OrchestrateRodoviaria(
                totalbus_login.company.company_internal_id,
                totalbus_login.company.modelo_venda,
            ),
            tc.trechoclasse_internal_id,
            tc.datetime_ida,
            tc.origem.cidade.timezone,
            tc.origem,
            tc.destino,
            tc.grupo_classe.tipo_assento_internal,
        )
    assert "Serviço não encontrado na API" in str(exc.value)
    assert TrechoClasseError.objects.count() == 1
    trecho_classe_error = TrechoClasseError.objects.first()
    assert trecho_classe_error.company == totalbus_login.company
    assert trecho_classe_error.trechoclasse_internal_id == totalbus_trechoclasses.ida.trechoclasse_internal_id
    assert trecho_classe_error.tipo_assento == "leito"
    assert trecho_classe_error.datetime_ida == to_default_tz(totalbus_trechoclasses.ida.datetime_ida)
    assert trecho_classe_error.origem is not None
    assert trecho_classe_error.destino is not None
    assert isinstance(trecho_classe_error.servicos_proximos, list)
    assert len(trecho_classe_error.servicos_proximos) == 5


@time_machine.travel(datetime(2022, 10, 13, 13, 25, 23, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_get_api_data_from_trechoclasse_not_found_atualiza_antigo(
    totalbus_login, totalbus_trechoclasses, mock_buscar_servico_totalbus
):
    datetime_now = timezone.now()
    tc_error = baker.make(
        TrechoClasseError,
        trechoclasse_internal_id=totalbus_trechoclasses.ida.trechoclasse_internal_id,
    )
    TrechoClasseError.objects.filter(pk=tc_error.id).update(updated_at=datetime_now - timedelta(days=2))

    tc_error_antigo = baker.make(
        TrechoClasseError,
        trechoclasse_internal_id=totalbus_trechoclasses.volta.trechoclasse_internal_id,
    )
    TrechoClasseError.objects.filter(pk=tc_error_antigo.id).update(updated_at=datetime_now - timedelta(days=4))

    # teste de sanidade
    tc_error.refresh_from_db()
    tc_error_antigo.refresh_from_db()
    assert tc_error.updated_at == datetime_now - timedelta(days=2)
    assert tc_error_antigo.updated_at == datetime_now - timedelta(days=4)

    tc = totalbus_trechoclasses.ida
    with pytest.raises(RodoviariaTrechoclasseFactoryException) as exc:
        get_api_data(
            totalbus_login.company,
            OrchestrateRodoviaria(
                totalbus_login.company.company_internal_id,
                totalbus_login.company.modelo_venda,
            ),
            tc.trechoclasse_internal_id,
            tc.datetime_ida,
            tc.origem.cidade.timezone,
            tc.origem,
            tc.destino,
            tc.grupo_classe.tipo_assento_internal,
        )
    assert "Serviço não encontrado na API" in str(exc.value)
    tc_error.refresh_from_db()
    tc_error_antigo.refresh_from_db()
    assert tc_error.updated_at == datetime_now
    assert tc_error_antigo.updated_at == datetime_now - timedelta(days=4)  # atualiza só o menos antigo
    assert tc_error.motivo == TrechoClasseError.Motivo.SEM_SERVICO


@time_machine.travel(datetime(2022, 10, 13, 13, 25, 23, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_api_data_from_trechoclasse_not_found_servicos_possiveis(
    mocker, totalbus_login, totalbus_trechoclasses, mock_buscar_servico_totalbus
):
    datetime_now = timezone.now()
    tc_error = baker.make(
        TrechoClasseError, trechoclasse_internal_id=totalbus_trechoclasses.ida.trechoclasse_internal_id
    )
    TrechoClasseError.objects.filter(pk=tc_error.id).update(updated_at=datetime_now - timedelta(days=2))

    # teste de sanidade
    tc_error.refresh_from_db()
    assert tc_error.updated_at == datetime_now - timedelta(days=2)

    mocker.patch(
        "rodoviaria.service.update_trechoclasse_with_api_data.motivo_servico_nao_encontrado",
        return_value=(TrechoClasseError.Motivo.MISMATCH_DE_HORARIO, [("15:30", "semi leito"), ("15:45", "leito")]),
    )

    tc = totalbus_trechoclasses.ida
    with pytest.raises(RodoviariaTrechoclasseFactoryException) as exc:
        get_api_data(
            totalbus_login.company,
            OrchestrateRodoviaria(
                totalbus_login.company.company_internal_id,
                totalbus_login.company.modelo_venda,
            ),
            tc.trechoclasse_internal_id,
            tc.datetime_ida,
            tc.origem.cidade.timezone,
            tc.origem,
            tc.destino,
            tc.grupo_classe.tipo_assento_internal,
        )
    assert (
        str(exc.value)
        == f"Serviço não encontrado na API: [{TrechoClasseError.Motivo.MISMATCH_DE_HORARIO}] [('15:30', 'semi leito'),"
        " ('15:45', 'leito')]"
    )
    tc_error.refresh_from_db()
    assert tc_error.servicos_proximos_parseados == [["15:30", "semi leito"], ["15:45", "leito"]]
    assert tc_error.motivo == TrechoClasseError.Motivo.MISMATCH_DE_HORARIO


def test_update_trechoclasse_with_api_data(totalbus_login, totalbus_trechoclasses, mock_buscar_servico_totalbus):
    tc = totalbus_trechoclasses.ida
    tc.grupo_classe.tipo_assento_internal = "leito cama"
    tc.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:05", "%Y-%m-%d %H:%M"))

    trecho_classe_db_old = TrechoClasse.objects.get(id=tc.id)

    update_trechoclasse_with_api_data(
        totalbus_login.company,
        OrchestrateRodoviaria(
            totalbus_login.company.company_internal_id,
            totalbus_login.company.modelo_venda,
        ),
        tc,
    )

    trecho_classe_db_updated = TrechoClasse.objects.get(id=tc.id)

    assert trecho_classe_db_old.preco_rodoviaria == Decimal("80.00")
    assert trecho_classe_db_updated.preco_rodoviaria == Decimal("142.41")


def test_update_trechoclasse_with_api_data_not_found(
    totalbus_login, totalbus_trechoclasses, mock_buscar_servico_totalbus
):
    trecho_classe_db_old = TrechoClasse.objects.filter(id=totalbus_trechoclasses.ida.id).first()

    with pytest.raises(RodoviariaTrechoclasseFactoryException) as exc:
        update_trechoclasse_with_api_data(
            totalbus_login.company,
            OrchestrateRodoviaria(
                totalbus_login.company.company_internal_id,
                totalbus_login.company.modelo_venda,
            ),
            totalbus_trechoclasses.ida,
        )
    assert "Serviço não encontrado na API" in str(exc.value)
    assert TrechoClasseError.objects.count() == 1

    trecho_classe_db_updated = TrechoClasse.objects.filter(id=totalbus_trechoclasses.ida.id).first()
    assert trecho_classe_db_old.preco_rodoviaria == Decimal("80.00")
    assert trecho_classe_db_updated.preco_rodoviaria == Decimal("80.00")

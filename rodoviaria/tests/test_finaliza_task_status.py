from datetime import datetime, timedelta

import time_machine
from django.core.management import call_command
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from rodoviaria.models.core import TaskStatus


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_finaliza_task_status():
    task_nova = baker.make(TaskStatus, status=TaskStatus.Status.PENDING)
    TaskStatus.objects.filter(id=task_nova.id).update(updated_at=(timezone.now() - timedelta(hours=1)))
    task_antiga = baker.make(TaskStatus, status=TaskStatus.Status.PENDING)
    TaskStatus.objects.filter(id=task_antiga.id).update(updated_at=(timezone.now() - timedelta(hours=40)))
    call_command("finaliza_task_status")
    task_nova.refresh_from_db()
    assert task_nova.status == TaskStatus.Status.PENDING
    assert task_nova.updated_at == (timezone.now() - timedelta(hours=1))
    task_antiga.refresh_from_db()
    assert task_antiga.status == TaskStatus.Status.FAILURE
    assert task_antiga.updated_at == timezone.now()

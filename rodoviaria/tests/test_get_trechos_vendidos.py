from datetime import datetime, timedelta
from decimal import Decimal as D
from types import SimpleNamespace
from unittest import mock

import pytest
from django.core.management import call_command
from django.db import connections
from django.utils import timezone
from model_bakery import baker

from bp.celery import app
from commons.dateutils import to_default_tz, to_tz
from rodoviaria.api.forms import BuscarServicoForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.models.core import Company, LocalEmbarque, Rota, TrechoVendido
from rodoviaria.service import (
    fetch_trechos_vendidos_svc,
    trechos_vendidos_svc,
)
from rodoviaria.service.exceptions import (
    HibridoNotAllowedException,
    RodoviariaException,
    RodoviariaTrechoNotFoundException,
)

from .utils_testes import mock_buscar_corridas_response


def test_trecho_vendido_dict_json(rota_mock, paradas_mock):
    trecho_vendido = baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota_mock,
        origem=paradas_mock[0],
        destino=paradas_mock[1],
        distancia=D("123.2"),
        classe="LEITO CAMA",
        duracao=timedelta(seconds=7200),
        preco=D("65.99"),
    )
    assert trecho_vendido.to_dict_json() == {
        "id": trecho_vendido.id,
        "id_internal": trecho_vendido.id_internal,
        "origem": paradas_mock[0].nickname,
        "destino": paradas_mock[1].nickname,
        "origem_id": paradas_mock[0].local_embarque_internal_id,
        "destino_id": paradas_mock[1].local_embarque_internal_id,
        "distancia": D("123.2"),
        "classe_api": "LEITO CAMA",
        "classe_buser": "leito cama",
        "duracao": "2:00:00",
        "preco": D("65.99"),
        "ativo": True,
    }


def test_get_grupo_e_rota(rota_mock):
    grupo_internal_id = 3123
    datetime_now = to_default_tz(datetime(2022, 2, 10, 16, 45))
    grupo = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=grupo_internal_id,
        rota=rota_mock,
        datetime_ida=datetime_now - timedelta(days=1),
    )
    with mock.patch("rodoviaria.service.trechos_vendidos_svc.timezone.now") as mock_now:
        mock_now.return_value = datetime_now
        response1 = trechos_vendidos_svc._get_grupo_e_rota(grupo_internal_id, None)
        response2 = trechos_vendidos_svc._get_grupo_e_rota(None, rota_mock.id)
        grupo.datetime_ida = datetime_now + timedelta(days=3)
        grupo.save()
        response3 = trechos_vendidos_svc._get_grupo_e_rota(None, rota_mock.id)
    assert response1 == (grupo, rota_mock)
    assert response2 == (None, rota_mock)
    assert response3 == (grupo, rota_mock)


def test_get_trechos_vendidos_existentes(rota_mock, paradas_mock, totalbus_company, totalbus_login):
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=27, rota=rota_mock)
    trechos_vendidos = [
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem=paradas_mock[0],
            destino=paradas_mock[1],
            classe="leito cama",
        ),
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem=paradas_mock[0],
            destino=paradas_mock[2],
            classe="executivo",
        ),
    ]
    response = trechos_vendidos_svc.get_trechos_vendidos(grupo_id=grupo.grupo_internal_id, rodoviaria_rota_id=None)
    assert response == [trecho.to_dict_json() for trecho in trechos_vendidos]


def test_get_trechos_vendidos_by_rodoviaria_rota_id(rota_mock, paradas_mock, totalbus_company, totalbus_login):
    baker.make("rodoviaria.Grupo", grupo_internal_id=27, rota=rota_mock)
    trechos_vendidos = [
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem=paradas_mock[0],
            destino=paradas_mock[1],
            classe="leito cama",
        ),
    ]
    response = trechos_vendidos_svc.get_trechos_vendidos(grupo_id=None, rodoviaria_rota_id=rota_mock.id)
    assert response == [trecho.to_dict_json() for trecho in trechos_vendidos]


def test_get_trechos_vendidos_by_rodoviaria_rota_id_hibrido(rota_mock):
    rota_mock.company = baker.make(Company, modelo_venda=Company.ModeloVenda.HIBRIDO)
    rota_mock.save()
    baker.make("rodoviaria.Grupo", grupo_internal_id=27, rota=rota_mock)
    with pytest.raises(HibridoNotAllowedException):
        trechos_vendidos_svc.get_trechos_vendidos(grupo_id=None, rodoviaria_rota_id=rota_mock.id)


def test_get_trechos_vendidos_rota_inexistente(totalbus_company, totalbus_login):
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=27)
    response = trechos_vendidos_svc.get_trechos_vendidos(grupo_id=grupo.grupo_internal_id, rodoviaria_rota_id=None)
    assert response == {"error": "Grupo não possui uma rota vinculada no banco rodoviaria"}


def test_get_trechos_vendidos_rota_existente_trechos_vendidos_inexistentes(rota_mock, totalbus_company, totalbus_login):
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=27, rota=rota_mock)
    response = trechos_vendidos_svc.get_trechos_vendidos(grupo_id=grupo.grupo_internal_id, rodoviaria_rota_id=None)
    assert response == {"warning": "Trechos vendidos ainda não foram buscados"}


def test_fetch_trechos_vendidos_uma_rota_hibrido(rota_mock):
    grupo_internal_id = 3123
    rota_mock.company = baker.make(Company, modelo_venda=Company.ModeloVenda.HIBRIDO)
    rota_mock.save()
    baker.make("rodoviaria.Grupo", grupo_internal_id=grupo_internal_id, rota=rota_mock)
    with pytest.raises(HibridoNotAllowedException):
        trechos_vendidos_svc.fetch_trechos_vendidos_uma_rota(grupo_id=None, rodoviaria_rota_id=rota_mock.id)


def test_fetch_trechos_vendidos_uma_rota_endpoint(totalbus_login, rota_mock):
    grupo_internal_id = 3123
    rota_mock.company = totalbus_login.company
    rota_mock.save()
    baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=grupo_internal_id,
        rota=rota_mock,
        company_integracao=totalbus_login.company,
    )
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota"
    ) as mock_fetch_trecho_vendido_uma_rota:
        mock_fetch_trecho_vendido_uma_rota.return_value = app.AsyncResult("123")
        response = trechos_vendidos_svc.fetch_trechos_vendidos_uma_rota(grupo_internal_id, rota_mock.id)
    assert response["mensagem"] == f"Fetch de trechos vendidos da rota {rota_mock.id}"
    mock_fetch_trecho_vendido_uma_rota.assert_called_with(rota_mock)


def test_fetch_trechos_vendidos_uma_rota_endpoint_grupo_sem_rota(rota_mock):
    grupo_internal_id = 3123
    baker.make("rodoviaria.Grupo", grupo_internal_id=grupo_internal_id)
    response = trechos_vendidos_svc.fetch_trechos_vendidos_uma_rota(grupo_internal_id, rota_mock.id)
    assert response == {"error": "Grupo não possui uma rota vinculada no banco rodoviaria"}


def test_fetch_trechos_vendidos_command():
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trechos_by_company_id"
    ) as fetch_trechos_vendidos_svc_mock:
        call_command("fetch_trechos_vendidos")
    fetch_trechos_vendidos_svc_mock.assert_called_once()


def test_test_fetch_trechos_vendidos_nenhuma_rota():
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota"
    ) as fetch_trecho_vendido_uma_rota_mock:
        fetch_trechos_vendidos_svc.fetch_trechos_vendidos()
    fetch_trecho_vendido_uma_rota_mock.assert_not_called()


def test_fetch_trechos_vendidos(rota_mock):
    company = baker.make(
        "rodoviaria.Company",
        integracao=baker.make("rodoviaria.Integracao", name="totalbus"),
    )
    baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=27,
        rota=rota_mock,
        datetime_ida=timezone.now() + timedelta(days=2),
        company_integracao=company,
    )
    # sem trechos vendidos
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota"
    ) as fetch_trecho_vendido_uma_rota_mock:
        fetch_trechos_vendidos_svc.fetch_trechos_vendidos()
    fetch_trecho_vendido_uma_rota_mock.assert_called_once_with(rota_mock)
    # com trechos vendidos sem atualizar
    baker.make("rodoviaria.TrechoVendido", rota=rota_mock, _quantity=4)
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota"
    ) as fetch_trecho_vendido_uma_rota_mock:
        fetch_trechos_vendidos_svc.fetch_trechos_vendidos()
    fetch_trecho_vendido_uma_rota_mock.assert_not_called()
    # com trechos vendidos precisa atualizar
    rota_mock.trechovendido_set.update(updated_at=timezone.now() - TrechoVendido.EXPIRATION_TIME)
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota"
    ) as fetch_trecho_vendido_uma_rota_mock:
        fetch_trechos_vendidos_svc.fetch_trechos_vendidos()
    fetch_trecho_vendido_uma_rota_mock.assert_called_once_with(rota_mock)
    # company não é totalbus
    company.integracao.name = "praxio"
    company.integracao.save()
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota"
    ) as fetch_trecho_vendido_uma_rota_mock:
        fetch_trechos_vendidos_svc.fetch_trechos_vendidos()
    fetch_trecho_vendido_uma_rota_mock.assert_not_called()


def test_fetch_trechos_vendidos_uma_rota_com_grupo_async(mock_itinerario, mock_grupo_trecho_classe):
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.itinerario_parsed_data"
    ) as itinerario_parsed_data_mock, mock.patch.object(OrchestrateRodoviaria, "provider"), mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc_v2.async_fetch_trechos_vendidos"
    ) as async_fetch_mock:
        itinerario_parsed_data_mock.return_value = mock_itinerario.checkpoints
        fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota(mock_grupo_trecho_classe.rota)
    itinerario_parsed_data_mock.assert_called_once()
    async_fetch_mock.assert_called_once()


def test_fetch_trecho_vendido_uma_rota_timezone(mock_itinerario, mock_grupo_trecho_classe):
    origem, _, destino = mock_itinerario.checkpoints
    company = mock_grupo_trecho_classe.rota.company
    baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=origem.local.external_local_id,
        cidade__timezone="America/Manaus",
        cidade__company=company,
    )
    baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=destino.local.external_local_id,
        cidade__timezone="America/Manaus",
        cidade__company=company,
    )

    with mock.patch.object(OrchestrateRodoviaria, "provider"), mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.itinerario_parsed_data"
    ) as itinerario_parsed_data_mock, mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc_v2.async_fetch_trechos_vendidos"
    ) as async_fetch_mock:
        itinerario_parsed_data_mock.return_value = mock_itinerario.checkpoints
        fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota(mock_grupo_trecho_classe.rota)
    async_fetch_mock.assert_called_once_with(
        mock.ANY, mock.ANY, mock.ANY, mock.ANY, "America/Manaus", mock.ANY, mock.ANY
    )


def test_fetch_trecho_vendido_uma_rota_default_timezone(mock_itinerario, mock_grupo_trecho_classe):
    origem, _, destino = mock_itinerario.checkpoints
    company = mock_grupo_trecho_classe.rota.company
    baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=origem.local.external_local_id,
        cidade__company=company,
    )
    baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=destino.local.external_local_id,
        cidade__timezone="America/Manaus",
        cidade__company=company,
    )

    with mock.patch.object(OrchestrateRodoviaria, "provider"), mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.itinerario_parsed_data"
    ) as itinerario_parsed_data_mock, mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc_v2.async_fetch_trechos_vendidos"
    ) as async_fetch_mock:
        itinerario_parsed_data_mock.return_value = mock_itinerario.checkpoints
        fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota(mock_grupo_trecho_classe.rota)
    async_fetch_mock.assert_called_once_with(
        mock.ANY, mock.ANY, mock.ANY, mock.ANY, "America/Sao_Paulo", mock.ANY, mock.ANY
    )


def test_fetch_trechos_vendidos_uma_rota_com_grupo_sem_trechoclasse(mock_itinerario, rota_mock):
    baker.make("rodoviaria.Grupo", rota=rota_mock)
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.itinerario_parsed_data"
    ) as itinerario_parsed_data_mock, mock.patch.object(OrchestrateRodoviaria, "provider"), mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc_v2.async_fetch_trechos_vendidos"
    ) as async_fetch_mock:
        itinerario_parsed_data_mock.return_value = mock_itinerario.checkpoints
        fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota(rota_mock)
    async_fetch_mock.assert_called_once()


def test_fetch_trechos_vendidos_uma_rota_sem_grupo(mock_itinerario, rota_mock):
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.itinerario_parsed_data"
    ) as itinerario_parsed_data_mock, mock.patch.object(OrchestrateRodoviaria, "provider"), mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc_v2.async_fetch_trechos_vendidos"
    ) as async_fetch_mock:
        itinerario_parsed_data_mock.return_value = mock_itinerario.checkpoints
        fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota(rota_mock)
    async_fetch_mock.assert_called_once()


def _mock_find_trechos_vendidos(quantity=1):
    return [
        {
            "external_origem_id": i + 10,
            "external_destino_id": i + 32,
            "classe": "CONVENCIONAL",
            "capacidade_classe": i + 23,
            "duracao": 4200,
            "preco": 142.52,
            "distancia": 223,
            "datetime_ida": datetime.strftime(
                to_default_tz(
                    datetime(2022, 7, (20 + i) % 30) + timedelta(seconds=3600 * i),
                ),
                "%Y-%m-%dT%H:%M:%S%z",
            ),
        }
        for i in range(quantity)
    ]


def test_orchestrator_find_trecho_vendido_sem_servico(
    totalbus_company, totalbus_login, clean_cache_cached_buscar_corridas
):
    data = datetime(2021, 5, 15, 1, 20)
    origem_local = 11
    destino_local = 22
    rota_id = 3125
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock:
        buscar_corridas_mock.return_value = BuscarServicoForm(found=False, servicos=[])
        timezone_mock = "America/Sao_Paulo"
        assert not OrchestrateRodoviaria(
            totalbus_company.company_internal_id, totalbus_company.modelo_venda
        ).find_trecho_vendido(
            origem_local,
            destino_local,
            data.strftime("%Y-%m-%d"),
            timezone_mock,
            timezone_mock,
            rota_id,
            [data],
            mock.Mock(),
        )
    buscar_corridas_mock.assert_called_once_with({"origem": 11, "destino": 22, "data": "2021-05-15"}, None)


def test_orchestrator_find_trecho_vendido_cache_memoize(
    totalbus_company, totalbus_login, clean_cache_cached_buscar_corridas
):
    data = datetime(2021, 5, 15, 1, 20)
    origem_local = 11
    destino_local = 22
    rota_id = 3125
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock:
        buscar_corridas_mock.return_value = BuscarServicoForm(found=False, servicos=[])
        timezone_mock = "America/Sao_Paulo"
        # cria cache
        OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda).find_trecho_vendido(
            origem_local,
            destino_local,
            data.strftime("%Y-%m-%d"),
            timezone_mock,
            timezone_mock,
            rota_id,
            [data],
            mock.Mock(),
        )
        # usa o cache
        OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda).find_trecho_vendido(
            origem_local,
            destino_local,
            data.strftime("%Y-%m-%d"),
            timezone_mock,
            timezone_mock,
            rota_id,
            [data],
            mock.Mock(),
        )
    # bateu uma vez apenas
    buscar_corridas_mock.assert_called_once_with({"origem": 11, "destino": 22, "data": "2021-05-15"}, None)


def test_orchestrator_find_trecho_vendido_com_servico(
    rota_mock, totalbus_company, totalbus_login, clean_cache_cached_buscar_corridas
):
    origem_local_id = 11
    destino_local_id = 22
    rota_external_id = 111
    rota_mock.id_external = rota_external_id
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock:
        buscar_corridas_response = mock_buscar_corridas_response(rota_external_id=111)
        buscar_corridas_mock.return_value = buscar_corridas_response
        timezone_mock = "America/Sao_Paulo"
        partida = to_tz(buscar_corridas_response.servicos[0].external_datetime_ida, timezone_mock)
        trechos_vendidos = OrchestrateRodoviaria(
            totalbus_company.company_internal_id, totalbus_company.modelo_venda
        ).find_trecho_vendido(
            origem_local_id,
            destino_local_id,
            partida.strftime("%Y-%m-%d"),
            timezone_mock,
            timezone_mock,
            rota_mock.id,
            [partida.strftime("%Y-%m-%dT%H:%M:%S")],
            mock.Mock(),
        )
    buscar_corridas_mock.assert_called_once_with(
        {"origem": origem_local_id, "destino": destino_local_id, "data": partida.strftime("%Y-%m-%d")}, None
    )
    trecho_vendido = trechos_vendidos[0]
    assert trecho_vendido["external_origem_id"] == origem_local_id
    assert trecho_vendido["external_destino_id"] == destino_local_id
    assert trecho_vendido["classe"] == "LEITO CAMA"
    assert trecho_vendido["duracao"] == 15600
    assert trecho_vendido["preco"] == D("142.41")
    assert trecho_vendido["distancia"] == 352.0


def test_orchestrator_find_trecho_vendido_com_servico_que_nao_pertence_a_rota(
    rota_mock, totalbus_company, totalbus_login, clean_cache_cached_buscar_corridas
):
    origem_local_id = 11
    destino_local_id = 22
    rota_external_id = 111
    rota_mock.id_external = rota_external_id
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock:
        buscar_corridas_response = mock_buscar_corridas_response(rota_external_id=111)
        buscar_corridas_mock.return_value = buscar_corridas_response
        timezone_mock = "America/Sao_Paulo"
        partida = to_tz(datetime(2022, 10, 30, 20, 12), timezone_mock)
        trechos_vendidos = OrchestrateRodoviaria(
            totalbus_company.company_internal_id, totalbus_company.modelo_venda
        ).find_trecho_vendido(
            origem_local_id,
            destino_local_id,
            partida.strftime("%Y-%m-%d"),
            timezone_mock,
            timezone_mock,
            rota_mock.id,
            [partida.strftime("%Y-%m-%dT%H:%M:%S")],
            mock.Mock(),
        )
    buscar_corridas_mock.assert_called_once_with(
        {"origem": origem_local_id, "destino": destino_local_id, "data": partida.strftime("%Y-%m-%d")}, None
    )
    assert trechos_vendidos == []


def test_orchestrator_find_trecho_vendido_trecho_nao_localizado(
    rota_mock, totalbus_company, totalbus_login, clean_cache_cached_buscar_corridas
):
    data = datetime(2021, 5, 15, 1, 20)
    origem_local_id = 11
    destino_local_id = 22
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock:
        buscar_corridas_mock.side_effect = RodoviariaTrechoNotFoundException
        timezone_mock = "America/Sao_Paulo"
        trecho_vendido_exists = OrchestrateRodoviaria(
            totalbus_company.company_internal_id, totalbus_company.modelo_venda
        ).find_trecho_vendido(
            origem_local_id,
            destino_local_id,
            data.strftime("%Y-%m-%d"),
            timezone_mock,
            timezone_mock,
            rota_mock.id,
            [data],
            mock.Mock(),
        )
    buscar_corridas_mock.assert_called_once()
    assert not trecho_vendido_exists


def test_orchestrator_find_trecho_vendido_trecho_rodoviaria_exception(
    rota_mock, totalbus_company, totalbus_login, clean_cache_cached_buscar_corridas
):
    data = datetime(2021, 5, 15, 1, 20)
    origem_local_id = 11
    destino_local_id = 22
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock:
        buscar_corridas_mock.side_effect = RodoviariaException("Erro genérico da rodoviária")
        timezone_mock = "America/Sao_Paulo"
        trecho_vendido = OrchestrateRodoviaria(
            totalbus_company.company_internal_id, totalbus_company.modelo_venda
        ).find_trecho_vendido(
            origem_local_id,
            destino_local_id,
            data.strftime("%Y-%m-%d"),
            timezone_mock,
            timezone_mock,
            rota_mock.id,
            [data],
            mock.Mock(),
        )
    buscar_corridas_mock.assert_called_once()
    assert trecho_vendido == []


def test_orchestrator_find_trecho_vendido_timezones_diferentes(
    rota_mock, totalbus_company, totalbus_login, clean_cache_cached_buscar_corridas
):
    origem_local_id = 11
    destino_local_id = 22
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock:
        buscar_corridas_response = mock_buscar_corridas_response(rota_external_id=111)
        buscar_corridas_mock.return_value = buscar_corridas_response
        timezone_origem = "America/Campo_Grande"
        timezone_destino = "America/Sao_Paulo"
        partida = to_tz(buscar_corridas_response.servicos[0].external_datetime_ida, timezone_origem)
        trechos_vendidos = OrchestrateRodoviaria(
            totalbus_company.company_internal_id, totalbus_company.modelo_venda
        ).find_trecho_vendido(
            origem_local_id,
            destino_local_id,
            partida.strftime("%Y-%m-%d"),
            timezone_origem,
            timezone_destino,
            rota_mock.id,
            [partida.strftime("%Y-%m-%dT%H:%M:%S")],
            mock.Mock(),
        )
    trecho_vendido = trechos_vendidos[0]
    assert trecho_vendido["external_origem_id"] == 11
    assert trecho_vendido["external_destino_id"] == 22
    assert trecho_vendido["classe"] == "LEITO CAMA"
    assert trecho_vendido["capacidade_classe"] == 23
    assert trecho_vendido["duracao"] == 12000
    assert trecho_vendido["preco"] == pytest.approx(D("142.41"))
    assert trecho_vendido["distancia"] == pytest.approx(352.0)


def test_orchestrator_find_trecho_vendido_atualiza_trechos(rota_mock, totalbus_company, totalbus_login):
    l1, l2 = baker.make(
        LocalEmbarque,
        _quantity=2,
        cidade__company=totalbus_company,
        _bulk_create=True,
        _fill_optional=["local_embarque_internal_id"],
    )
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock, mock.patch(
        "bp.buserdjango_celery.atualiza_trecho_raw.delay"
    ) as atualiza_trechos_raw_mock:
        buscar_corridas_response = mock_buscar_corridas_response(rota_external_id=111)
        buscar_corridas_mock.return_value = buscar_corridas_response
        timezone_origem = "America/Campo_Grande"
        timezone_destino = "America/Sao_Paulo"
        partida = to_tz(buscar_corridas_response.servicos[0].external_datetime_ida, timezone_origem)
        OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda).find_trecho_vendido(
            l1.id_external,
            l2.id_external,
            partida.strftime("%Y-%m-%d"),
            timezone_origem,
            timezone_destino,
            rota_mock.id,
            [partida.strftime("%Y-%m-%dT%H:%M:%S")],
            mock.Mock(),
        )
        atualiza_trechos_raw_mock.assert_called_once()


def test_orchestrator_find_trecho_vendido_nao_atualiza_trechos(rota_mock, totalbus_company, totalbus_login):
    origem_local, destino_local = baker.make(
        LocalEmbarque,
        _quantity=2,
        cidade__company=totalbus_company,
        _bulk_create=True,
    )
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock, mock.patch(
        "bp.buserdjango_celery.atualiza_trecho_raw.delay"
    ) as atualiza_trechos_raw_mock:
        buscar_corridas_response = mock_buscar_corridas_response(rota_external_id=111)
        buscar_corridas_mock.return_value = buscar_corridas_response
        timezone_origem = "America/Campo_Grande"
        timezone_destino = "America/Sao_Paulo"
        partida = to_tz(buscar_corridas_response.servicos[0].external_datetime_ida, timezone_origem)
        OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda).find_trecho_vendido(
            origem_local.id_external,
            destino_local.id_external,
            partida.strftime("%Y-%m-%d"),
            timezone_origem,
            timezone_destino,
            rota_mock.id,
            [partida.strftime("%Y-%m-%dT%H:%M:%S")],
            mock.Mock(),
        )
        atualiza_trechos_raw_mock.assert_not_called()


def test_orchestrator_build_trechos_vendidos(
    rota_mock, totalbus_company, totalbus_login, clean_cache_cached_buscar_corridas
):
    origem_local_id = 11
    destino_local_id = 22
    rota_external_id = 111
    rota_mock.id_external = rota_external_id
    with mock.patch.object(TotalbusAPI, "buscar_corridas") as buscar_corridas_mock:
        buscar_corridas_response = mock_buscar_corridas_response(quantity=2, rota_external_id=111, time_diff=30)
        buscar_corridas_mock.return_value = buscar_corridas_response
        timezone_mock = "America/Sao_Paulo"
        partida_1 = to_tz(buscar_corridas_response.servicos[0].external_datetime_ida, timezone_mock)
        partida_2 = to_tz(buscar_corridas_response.servicos[1].external_datetime_ida, timezone_mock)
        all_trechos_vendidos, trechos_vendidos_same_datetime = OrchestrateRodoviaria(
            totalbus_company.company_internal_id, totalbus_company.modelo_venda
        ).build_trechos_vendidos(
            buscar_corridas_response,
            origem_local_id,
            destino_local_id,
            timezone_mock,
            timezone_mock,
            [partida_1.strftime("%Y-%m-%dT%H:%M:%S")],
        )

        assert len(all_trechos_vendidos) == 2
        assert len(trechos_vendidos_same_datetime) == 1
        assert all_trechos_vendidos[0]["datetime_ida"] == partida_1
        assert all_trechos_vendidos[1]["datetime_ida"] == partida_2
        assert trechos_vendidos_same_datetime[0]["datetime_ida"] == partida_1


@pytest.fixture
def mock_cidade_local_origem_destino(mock_grupo_trecho_classe, mock_itinerario):
    [origem, parada, destino] = mock_itinerario.checkpoints
    cidade_origem = baker.make(
        "rodoviaria.Cidade",
        company=mock_grupo_trecho_classe.rota.company,
        id_external=origem.local.external_cidade_id,
    )
    cidade_destino = baker.make(
        "rodoviaria.Cidade",
        company=mock_grupo_trecho_classe.rota.company,
        id_external=destino.local.external_cidade_id,
    )
    local_origem = baker.make(
        "rodoviaria.LocalEmbarque",
        cidade=cidade_origem,
        id_external=origem.local.external_local_id,
    )
    local_destino = baker.make(
        "rodoviaria.LocalEmbarque",
        cidade=cidade_destino,
        id_external=destino.local.external_local_id,
    )
    return SimpleNamespace(local_origem=local_origem, local_destino=local_destino)


def test_all_fluxo_fetch_trechos_vendidos_uma_rota_sem_rotina(
    mock_itinerario,
    mock_grupo_trecho_classe,
    django_assert_num_queries,
    mock_cidade_local_origem_destino,
):
    rota = mock_grupo_trecho_classe.rota
    baker.make(
        "rodoviaria.CidadeInternal",
        id=65634,
        name="Local2",
        city_code_ibge=231232,
        timezone="America/Sao_Paulo",
    )
    baker.make("rodoviaria.CidadeInternal", id=56542, name="Local1", timezone="America/Manaus")
    timezone_now = to_default_tz(datetime(2022, 2, 10, 18, 32))
    datetime_ida = timezone_now + timedelta(days=2)
    grupo = baker.make(
        "rodoviaria.Grupo",
        datetime_ida=datetime_ida,
        rota=rota,
        company_integracao=rota.company,
    )
    baker.make("rodoviaria.TrechoClasse", grupo=grupo, datetime_ida=datetime_ida)
    with (
        mock.patch("rodoviaria.service.fetch_trechos_vendidos_svc.itinerario_parsed_data") as itinerario_parsed_mock,
        mock.patch("rodoviaria.service.fetch_trechos_vendidos_svc.timezone.now") as now_mock,
        mock.patch.object(OrchestrateRodoviaria, "provider") as mock_provider,
        mock.patch("celery.result.EagerResult.parent") as mock_chord_parent,
        django_assert_num_queries(23, connection=connections["rodoviaria"]),
    ):
        # queries: get_locais_embarque, get_rotinas, get_rota, update_rota, SAVEPOINT1, get_task, SAVEPOINT2,
        # create_task, RELEASE SAVEPOINT2, RELEASE SAVEPOINT1.
        mock_chord_parent.id = "123"
        mock_provider.queue_name = "q"
        now_mock.return_value = timezone_now
        itinerario_parsed_mock.return_value = mock_itinerario.checkpoints
        fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota(rota)


def test_fetch_trechos_by_company_id():
    company = baker.make(Company, company_internal_id=191)
    rota_ativa = baker.make(Rota, company=company, ativo=True)
    baker.make(Rota, company=company, ativo=False)  # rota inativa
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_uma_rota"
    ) as mock_fetch_trecho_vendido_uma_rota, mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc_v2.dispara_tasks"
    ) as mock_dispara_tasks:
        fetch_trechos_vendidos_svc.fetch_trechos_by_company_id(company.company_internal_id, company.modelo_venda)
    mock_fetch_trecho_vendido_uma_rota.assert_called_once_with(rota=rota_ativa, return_task_object=True)
    mock_dispara_tasks.delay.assert_called_once()

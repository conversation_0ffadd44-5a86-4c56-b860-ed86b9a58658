from datetime import date, datetime, timedelta

import pytest
import time_machine
from django.core.management import call_command
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.models.core import (
    CidadeInternal,
    LocalEmbarque,
    RotinaAtualizacaoTrecho,
    TrechoVendido,
    ViagemAPILogger,
)
from rodoviaria.service.atualiza_precos_search_svc import buscar_viagens_todas_empresas_api_na_search


@pytest.fixture
def mock_group_task(mocker):
    mock_group = mocker.patch("rodoviaria.management.commands.dispara_rotina_atualizacao_trecho.group")
    return mock_group


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_dispara_rotina_atualizacao_trecho_fora_do_intervalo(mock_group_task):
    rotina_atualizacao = baker.make(
        RotinaAtualizacaoTrecho,
        tipo_atualizacao=RotinaAtualizacaoTrecho.TipoAtualizacao.INTERVALO_DINAMICO,
        dias_busca_intervalo_dinamico=2,
        margem_inicio_intervalo_dinamico=1,
        intervalo_execucao_minutos=10,
    )
    RotinaAtualizacaoTrecho.objects.filter(id=rotina_atualizacao.id).update(
        ultima_execucao=timezone.now() - timedelta(minutes=5)
    )
    call_command("dispara_rotina_atualizacao_trecho")
    rotina_atualizacao.refresh_from_db()
    mock_group_task.assert_not_called()


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_dispara_rotina_atualizacao_trecho_intervalo_dinamico(mock_group_task):
    rotina_atualizacao = baker.make(
        RotinaAtualizacaoTrecho,
        tipo_atualizacao=RotinaAtualizacaoTrecho.TipoAtualizacao.INTERVALO_DINAMICO,
        dias_busca_intervalo_dinamico=2,
        margem_inicio_intervalo_dinamico=1,
        intervalo_execucao_minutos=10,
    )
    RotinaAtualizacaoTrecho.objects.filter(id=rotina_atualizacao.id).update(
        ultima_execucao=timezone.now() - timedelta(minutes=10)
    )
    call_command("dispara_rotina_atualizacao_trecho")
    rotina_atualizacao.refresh_from_db()
    assert rotina_atualizacao.ultima_execucao == to_default_tz(datetime(2022, 10, 19, 16, 40, 0, 0))
    mock_group_task.assert_called_once()
    mock_group_task.return_value.apply_async.assert_called_once()
    assert mock_group_task.call_args[0][0] == [
        buscar_viagens_todas_empresas_api_na_search.s(
            rotina_atualizacao.cidade_origem_id,
            rotina_atualizacao.cidade_destino_id,
            "2022-10-20",
            rotina_atualizacao_id=rotina_atualizacao.id,
        ),
        buscar_viagens_todas_empresas_api_na_search.s(
            rotina_atualizacao.cidade_origem_id,
            rotina_atualizacao.cidade_destino_id,
            "2022-10-21",
            rotina_atualizacao_id=rotina_atualizacao.id,
        ),
        buscar_viagens_todas_empresas_api_na_search.s(
            rotina_atualizacao.cidade_origem_id,
            rotina_atualizacao.cidade_destino_id,
            "2022-10-22",
            rotina_atualizacao_id=rotina_atualizacao.id,
        ),
    ]


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_dispara_rotina_atualizacao_trecho_intervalo_fixo(mock_group_task):
    rotina_atualizacao = baker.make(
        RotinaAtualizacaoTrecho,
        tipo_atualizacao=RotinaAtualizacaoTrecho.TipoAtualizacao.INTERVALO_FIXO,
        data_inicio_intervalo_fixo=date(2022, 10, 18),
        data_fim_intervalo_fixo=date(2022, 10, 20),
        intervalo_execucao_minutos=10,
    )
    RotinaAtualizacaoTrecho.objects.filter(id=rotina_atualizacao.id).update(
        ultima_execucao=timezone.now() - timedelta(minutes=10)
    )
    call_command("dispara_rotina_atualizacao_trecho")
    rotina_atualizacao.refresh_from_db()
    assert rotina_atualizacao.ultima_execucao == to_default_tz(datetime(2022, 10, 19, 16, 40, 0, 0))
    mock_group_task.assert_called_once()
    mock_group_task.return_value.apply_async.assert_called_once()
    assert mock_group_task.call_args[0][0] == [
        buscar_viagens_todas_empresas_api_na_search.s(
            rotina_atualizacao.cidade_origem_id,
            rotina_atualizacao.cidade_destino_id,
            "2022-10-19",
            rotina_atualizacao_id=rotina_atualizacao.id,
        ),
        buscar_viagens_todas_empresas_api_na_search.s(
            rotina_atualizacao.cidade_origem_id,
            rotina_atualizacao.cidade_destino_id,
            "2022-10-20",
            rotina_atualizacao_id=rotina_atualizacao.id,
        ),
    ]


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_dispara_rotina_atualizacao_trecho_inativa_intervalo_fixo(mock_group_task):
    rotina_atualizacao = baker.make(
        RotinaAtualizacaoTrecho,
        tipo_atualizacao=RotinaAtualizacaoTrecho.TipoAtualizacao.INTERVALO_FIXO,
        data_inicio_intervalo_fixo=date(2022, 10, 11),
        data_fim_intervalo_fixo=date(2022, 10, 18),
        intervalo_execucao_minutos=10,
    )
    ultima_execucao = timezone.now() - timedelta(minutes=10)
    RotinaAtualizacaoTrecho.objects.filter(id=rotina_atualizacao.id).update(ultima_execucao=ultima_execucao)
    call_command("dispara_rotina_atualizacao_trecho")
    rotina_atualizacao.refresh_from_db()
    assert rotina_atualizacao.ultima_execucao == ultima_execucao
    assert rotina_atualizacao.ativo is False
    mock_group_task.assert_not_called()


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_fluxo_completo_dispara_rotina_atualizacao_trecho(mocker, totalbus_api: TotalbusAPI):
    # cria locais de embarque de cada empresa
    timezone_origem = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone_origem)
    cidade_internal_destino = baker.make(CidadeInternal)
    rotina_atualizacao_trecho = baker.make(
        RotinaAtualizacaoTrecho,
        tipo_atualizacao=RotinaAtualizacaoTrecho.TipoAtualizacao.INTERVALO_DINAMICO,
        dias_busca_intervalo_dinamico=0,
        cidade_origem=cidade_internal_origem,
        cidade_destino=cidade_internal_destino,
        intervalo_execucao_minutos=10,
    )
    RotinaAtualizacaoTrecho.objects.filter(id=rotina_atualizacao_trecho.id).update(
        ultima_execucao=timezone.now() - timedelta(minutes=10)
    )
    local_embarque_origem_totalbus = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        cidade__company=totalbus_api.company,
    )
    local_embarque_destino_totalbus = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        cidade__company=totalbus_api.company,
    )
    baker.make(
        TrechoVendido,
        origem=local_embarque_origem_totalbus,
        destino=local_embarque_destino_totalbus,
        ativo=True,
        rota__ativo=True,
        rota__company=totalbus_api.company,
    )

    # mock buscar corridas
    datetime_ida_corrida_totalbus = datetime(2022, 6, 10, 14, 30)
    corridas_totalbus = BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                tipo_veiculo=2,
                external_id="83123",
                external_datetime_ida=datetime_ida_corrida_totalbus,
                preco=120,
                vagas=21,
                provider_data={"id": "9323"},
                linha="SÃO PAULO X RIO DE JANEIRO",
                veiculo_andar="1",
                veiculo_id=43213,
                rota_external_id="3123",
                classe="leito_especial",
            )
        ],
    )
    mock_buscar_corridas_totalbus = mocker.patch.object(TotalbusAPI, "buscar_corridas", return_value=corridas_totalbus)

    call_command("dispara_rotina_atualizacao_trecho")

    # verifica se o log foi criado corretamente
    assert ViagemAPILogger.objects.filter(
        company=totalbus_api.company,
        origem=local_embarque_origem_totalbus,
        destino=local_embarque_destino_totalbus,
        datetime_ida=to_default_tz(datetime_ida_corrida_totalbus),
        tipo_assento_parceiro="leito_especial",
        rotina_atualizacao=rotina_atualizacao_trecho,
    ).exists()

    # verifica se chamou a API com os parâmetros corretos
    mock_buscar_corridas_totalbus.assert_called_once_with(
        {
            "origem": local_embarque_origem_totalbus.id_external,
            "destino": local_embarque_destino_totalbus.id_external,
            "data": "2022-10-19",
        },
        None,
    )

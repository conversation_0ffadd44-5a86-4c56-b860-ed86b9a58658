from commons.utils import str_contains, strip_punctuation


def test_strip_accents():
    assert strip_punctuation(None) is None
    assert strip_punctuation("Cora<PERSON>") == "coracao"
    assert strip_punctuation("Moz<PERSON>") == "mozao"
    assert strip_punctuation("paquet<PERSON>") == "paqueta"
    assert strip_punctuation("l<PERSON>l<PERSON><PERSON> yôu") == "lalale you"


def test_str_contains():
    assert not str_contains(None, "opa")
    assert not str_contains("Opa", None)
    assert not str_contains("paquet<PERSON>", "paquetx")
    assert str_contains("ela", "él<PERSON>")
    assert str_contains("Cora<PERSON>", "Racao")
    assert str_contains("Mo<PERSON><PERSON>", "MOZAO")

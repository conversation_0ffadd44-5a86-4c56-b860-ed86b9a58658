from datetime import datetime
from datetime import timezone as tzone
from decimal import Decimal
from unittest import mock

import pytest
from model_bakery import baker

from commons.dateutils import to_default_tz
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.forms.grupo_trechoclasse_form import CidadeInfos, TrechoClasseInternoInfos
from rodoviaria.models import Grupo, TrechoClasse, TrechoClasseError
from rodoviaria.models.core import Company, GrupoClasse
from rodoviaria.service.exceptions import RodoviariaTrechoclasseFactoryException
from rodoviaria.service.grupo_trechoclasse_factory import (
    GrupoTrechoClasseFactory,
    create_missing_grupo_and_trechoclasses,
    get_trechoclasse_from_buser_django,
)


def test_get_trechoclasse_from_buser_django(buser_grupos):
    datetime_ida = datetime(2022, 4, 29, 20, 55, 40, 976723, tzinfo=tzone.utc)
    grupo = buser_grupos.ida.grupo
    trecho_classe = buser_grupos.ida.trechoclasse
    grupo.datetime_ida = datetime_ida
    grupo.save()
    trecho_classe.datetime_ida = datetime_ida
    trecho_classe.save()
    trecho_classe_infos = get_trechoclasse_from_buser_django(buser_grupos.ida.trechoclasse.id)
    assert trecho_classe_infos == TrechoClasseInternoInfos(
        trechoclasse_id=trecho_classe.id,
        localembarque_origem_id=trecho_classe.trecho_vendido.origem.id,
        cidade_origem=CidadeInfos(
            id=trecho_classe.trecho_vendido.origem.cidade.id,
            timezone=trecho_classe.trecho_vendido.origem.cidade.timezone,
            name=trecho_classe.trecho_vendido.origem.cidade.name,
            city_code_ibge=trecho_classe.trecho_vendido.origem.cidade.city_code_ibge,
        ),
        localembarque_destino_id=trecho_classe.trecho_vendido.destino.id,
        cidade_destino=CidadeInfos(
            id=trecho_classe.trecho_vendido.destino.cidade.id,
            timezone=trecho_classe.trecho_vendido.destino.cidade.timezone,
            name=trecho_classe.trecho_vendido.destino.cidade.name,
            city_code_ibge=trecho_classe.trecho_vendido.destino.cidade.city_code_ibge,
        ),
        trecho_datetime_ida=datetime_ida,
        grupo_id=grupo.id,
        grupo_datetime_ida=datetime_ida,
        grupoclasse_id=trecho_classe.grupo_classe.id,
        tipo_assento=trecho_classe.grupo_classe.tipo_assento,
    )


def test_totalbus_cria_grupo_e_trechoclasse(totalbus_login, buser_grupos, mock_buscar_servico_totalbus):
    buser_grupos.ida.grupo.company_id = totalbus_login.company.company_internal_id
    buser_grupos.ida.grupo.save()
    _test_factory(
        totalbus_login, buser_grupos.ida.trechoclasse, "leito cama", "2021-05-14 20:05", 142.41, 6, "712", "LEITO CAMA"
    )


def test_totalbus_cria_grupo_e_trechoclasse_reusa_grupo(totalbus_login, buser_grupos, mock_buscar_servico_totalbus):
    buser_grupos.ida.grupo.company_id = totalbus_login.company.company_internal_id
    buser_grupos.ida.grupo.save()
    _test_factory(
        totalbus_login, buser_grupos.ida.trechoclasse, "leito cama", "2021-05-14 20:05", 142.41, 6, "712", "LEITO CAMA"
    )
    _test_factory(
        totalbus_login,
        buser_grupos.ida.trechoclasse,
        "leito cama",
        "2021-05-14 20:05",
        142.41,
        6,
        "712",
        "LEITO CAMA",
        reusa=True,
    )


def test_totalbus_edita_trechoclasse(totalbus_login, buser_grupos, mock_buscar_servico_totalbus):
    buser_grupos.ida.grupo.company_id = totalbus_login.company.company_internal_id
    buser_grupos.ida.grupo.save()
    trecho_classe_internal = buser_grupos.ida.trechoclasse
    trecho_classe_internal.datetime_ida = to_default_tz(datetime.strptime("2021-05-14 20:05", "%Y-%m-%d %H:%M"))
    trecho_classe_internal.save()
    cidade = baker.make("rodoviaria.Cidade", company=totalbus_login.company)
    baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=trecho_classe_internal.trecho_vendido.origem_id,
        id_external=19000,
        cidade=cidade,
    )
    baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=trecho_classe_internal.trecho_vendido.destino_id,
        id_external=21000,
        cidade=cidade,
    )
    grupo_classe = trecho_classe_internal.grupo_classe
    grupo_classe.tipo_assento = "leito cama"
    grupo_classe.save()
    trechoclasse_from_buser_django = get_trechoclasse_from_buser_django(trecho_classe_internal.id)
    factory_result = GrupoTrechoClasseFactory(
        totalbus_login.company,
        trechoclasse_from_buser_django,
        OrchestrateRodoviaria(
            trecho_classe_internal.grupo.company_id,
            trecho_classe_internal.grupo.modelo_venda,
        ),
    ).create()
    first_rodoviaria_trecho_classe = factory_result.trecho_classe
    grupo = factory_result.grupo
    grupo.company_integracao = baker.make("rodoviaria.Company")
    grupo.save()
    factory_result = GrupoTrechoClasseFactory(
        totalbus_login.company,
        trechoclasse_from_buser_django,
        OrchestrateRodoviaria(
            trecho_classe_internal.grupo.company_id,
            trecho_classe_internal.grupo.modelo_venda,
        ),
    ).create()
    second_rodoviaria_trecho_classe = factory_result.trecho_classe
    assert first_rodoviaria_trecho_classe.id == second_rodoviaria_trecho_classe.id
    assert "to_be_updated" in first_rodoviaria_trecho_classe.tags_set()
    assert "to_be_updated" in second_rodoviaria_trecho_classe.tags_set()
    # atualizou a company do grupo
    grupo.refresh_from_db()
    assert grupo.company_integracao == totalbus_login.company


def test_praxio_cria_grupo_e_trechoclasse(mock_praxio_login, praxio_login, buser_grupos, mock_buscar_servico_praxio):
    buser_grupos.ida.grupo.company_id = praxio_login.company.company_internal_id
    buser_grupos.ida.grupo.save()
    _test_factory(
        praxio_login, buser_grupos.ida.trechoclasse, "semileito", "2021-05-18 23:00", 176.18, 1, "1405", "SEMILEITO"
    )


def test_praxio_cria_trechoclasse_error_origem_id(mock_praxio_login, praxio_login, buser_grupos):
    buser_grupos.ida.grupo.company_id = praxio_login.company.company_internal_id
    buser_grupos.ida.grupo.save()
    with pytest.raises(RodoviariaTrechoclasseFactoryException) as exc:
        trechoclasse_from_buser_django = get_trechoclasse_from_buser_django(buser_grupos.ida.trechoclasse.id)
        GrupoTrechoClasseFactory(
            praxio_login.company,
            trechoclasse_from_buser_django,
            OrchestrateRodoviaria(
                buser_grupos.ida.trechoclasse.grupo.company_id,
                buser_grupos.ida.trechoclasse.grupo.modelo_venda,
            ),
        ).create()
    assert "Local de embarque de origem de id " in str(exc.value)


def test_praxio_cria_trechoclasse_error_destino_id(mock_praxio_login, praxio_login, buser_grupos):
    buser_grupos.ida.grupo.company_id = praxio_login.company.company_internal_id
    buser_grupos.ida.grupo.save()
    with pytest.raises(RodoviariaTrechoclasseFactoryException) as exc:
        cidade = baker.make("rodoviaria.Cidade", company=praxio_login.company)
        baker.make(
            "rodoviaria.LocalEmbarque",
            local_embarque_internal_id=buser_grupos.ida.trechoclasse.trecho_vendido.origem_id,
            id_external=19000,
            cidade=cidade,
        )
        trechoclasse_from_buser_django = get_trechoclasse_from_buser_django(buser_grupos.ida.trechoclasse.id)
        GrupoTrechoClasseFactory(
            praxio_login.company,
            trechoclasse_from_buser_django,
            OrchestrateRodoviaria(
                buser_grupos.ida.trechoclasse.grupo.company_id,
                buser_grupos.ida.trechoclasse.grupo.modelo_venda,
            ),
        ).create()
    assert "Local de embarque de destino de id " in str(exc.value)


def test_praxio_cria_trechoclasse_error(mock_praxio_login, praxio_login, buser_grupos, mock_buscar_servico_praxio):
    buser_grupos.ida.grupo.company_id = praxio_login.company.company_internal_id
    buser_grupos.ida.grupo.save()

    with pytest.raises(RodoviariaTrechoclasseFactoryException) as exc:
        _test_factory(praxio_login, buser_grupos.ida.trechoclasse, "cama", "2021-05-18 23:00", 195, 0, "1404", "")
    assert "Serviço não encontrado na API" in str(exc.value)
    assert TrechoClasseError.objects.count() == 1
    trecho_classe_error = TrechoClasseError.objects.first()
    assert trecho_classe_error.company == praxio_login.company
    assert trecho_classe_error.trechoclasse_internal_id == buser_grupos.ida.trechoclasse.id
    assert trecho_classe_error.tipo_assento == "cama"
    assert trecho_classe_error.datetime_ida == to_default_tz(buser_grupos.ida.trechoclasse.datetime_ida)
    assert trecho_classe_error.origem is not None
    assert trecho_classe_error.destino is not None
    assert isinstance(trecho_classe_error.servicos_proximos, list)
    assert len(trecho_classe_error.servicos_proximos) == 5


def _test_factory(
    login,
    trecho_classe_internal,
    tipo_assento,
    datetime_ida,
    preco_rodoviaria,
    vagas,
    external_id,
    external_tipo_assento,
    reusa=False,
):
    if not reusa:
        cidade = baker.make("rodoviaria.Cidade", company=login.company)
        local_1 = baker.make(
            "rodoviaria.LocalEmbarque",
            local_embarque_internal_id=trecho_classe_internal.trecho_vendido.origem_id,
            id_external=19000,
            cidade=cidade,
        )
        local_2 = baker.make(
            "rodoviaria.LocalEmbarque",
            local_embarque_internal_id=trecho_classe_internal.trecho_vendido.destino_id,
            id_external=21000,
            cidade=cidade,
        )

    trecho_classe_internal.datetime_ida = to_default_tz(datetime.strptime(datetime_ida, "%Y-%m-%d %H:%M"))
    trecho_classe_internal.save()

    grupo_classe = trecho_classe_internal.grupo_classe
    grupo_classe.tipo_assento = tipo_assento
    grupo_classe.save()

    grupo_count = Grupo.objects.count()
    trecho_classe_count = TrechoClasse.objects.count()
    grupo_classe_count = GrupoClasse.objects.count()
    trechoclasse_from_buser_django = get_trechoclasse_from_buser_django(trecho_classe_internal.id)
    factory_result = GrupoTrechoClasseFactory(
        login.company,
        trechoclasse_from_buser_django,
        OrchestrateRodoviaria(
            trecho_classe_internal.grupo.company_id,
            trecho_classe_internal.grupo.modelo_venda,
        ),
    ).create()

    if not reusa:
        assert Grupo.objects.count() == grupo_count + 1
        assert TrechoClasse.objects.count() == trecho_classe_count + 1
        assert GrupoClasse.objects.count() == grupo_classe_count + 1
    else:
        assert Grupo.objects.count() == grupo_count
        assert TrechoClasse.objects.count() == trecho_classe_count
        assert GrupoClasse.objects.count() == grupo_classe_count
    grupo = factory_result.grupo
    trecho_classe = factory_result.trecho_classe
    assert float(trecho_classe.preco_rodoviaria) == preco_rodoviaria
    assert trecho_classe.vagas == vagas

    if not reusa:
        assert trecho_classe.origem == local_1
        assert trecho_classe.destino == local_2
    assert trecho_classe.grupo == grupo
    assert isinstance(trecho_classe.provider_data, str)
    assert trecho_classe.provider_data != ""
    assert trecho_classe.trechoclasse_internal_id == trecho_classe_internal.id
    assert trecho_classe.external_id == external_id
    assert trecho_classe.external_datetime_ida == trecho_classe_internal.datetime_ida
    assert trecho_classe.grupo_classe.tipo_assento_external == external_tipo_assento
    assert TrechoClasseError.objects.count() == 0
    assert "to_be_updated" in trecho_classe.tags_set()
    trecho_classe.active = False
    trecho_classe.save()


def test_create_missing_grupo_and_trechoclasses(totalbus_login, buser_grupos, mock_buscar_servico_totalbus):
    company = totalbus_login.company
    buser_grupo = buser_grupos.ida.grupo
    buser_trechoclasse = buser_grupos.ida.trechoclasse
    grupo_classe = buser_trechoclasse.grupo_classe

    buser_grupo.company_id = company.company_internal_id
    buser_grupo.save()

    buser_trechoclasse.datetime_ida = to_default_tz(datetime(2021, 5, 14, 20, 5))
    buser_trechoclasse.save()

    grupo_classe.tipo_assento = "leito cama"
    grupo_classe.save()

    cidade = baker.make("rodoviaria.Cidade", company=company)
    local_1 = baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=buser_trechoclasse.trecho_vendido.origem_id,
        id_external=19000,
        cidade=cidade,
    )
    local_2 = baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=buser_trechoclasse.trecho_vendido.destino_id,
        id_external=21000,
        cidade=cidade,
    )

    create_missing_grupo_and_trechoclasses(buser_grupo.id)

    assert Grupo.objects.count() == 1
    assert TrechoClasse.objects.count() == 1
    assert TrechoClasseError.objects.count() == 0

    grupo = Grupo.objects.first()
    trecho_classe = TrechoClasse.objects.first()

    assert trecho_classe.trechoclasse_internal_id == buser_trechoclasse.id
    assert trecho_classe.provider_data != ""
    assert trecho_classe.grupo == grupo
    assert trecho_classe.origem == local_1
    assert trecho_classe.destino == local_2
    assert trecho_classe.external_id == "712"
    assert trecho_classe.external_datetime_ida == buser_trechoclasse.datetime_ida
    assert trecho_classe.preco_rodoviaria == Decimal("142.41")
    assert trecho_classe.vagas == 6


def test_vexado_cria_grupo_e_trechoclasse(mock_vexado_login, vexado_login, buser_grupos, mock_buscar_servico_vexado):
    buser_grupos.ida.grupo.company_id = vexado_login.company.company_internal_id
    buser_grupos.ida.grupo.modelo_venda = Company.ModeloVenda.HIBRIDO
    buser_grupos.ida.grupo.save()
    with mock.patch(
        "rodoviaria.service.update_grupos_hibridos_svc.link_vexado_grupo_classe"
    ) as mock_link_vexado_grupo_classe:
        _test_factory(
            vexado_login,
            buser_grupos.ida.trechoclasse,
            "semi leito",
            "2021-05-18 23:00",
            125.25,
            15,
            "211",
            "Semi Leito",
        )
    mock_link_vexado_grupo_classe.assert_called_once_with(mock.ANY, vexado_login.company, 657, 2, "211", 1427)

from unittest import mock

from django.core.management import call_command
from model_bakery import baker

from rodoviaria.api.vexado import api as vexado_api
from rodoviaria.api.vexado import exceptions
from rodoviaria.models.core import Passagem
from rodoviaria.models.vexado import VexadoGrupoClasse
from rodoviaria.service import verificar_grupos_cancelados_svc


def test_verificar_grupos_hibridos_cancelados_command():
    with mock.patch(
        "rodoviaria.service.verificar_grupos_cancelados_svc.check_passagens_canceladas_hibrido"
    ) as mock_check_passagens_canceladas_hibrido:
        call_command("verificar_grupos_hibridos_cancelados")
    mock_check_passagens_canceladas_hibrido.assert_called_once_with()


def test_check_passagens_canceladas_hibrido_sem_passagens_para_cancelar(vexado_company, vexado_login):
    grupo_classe_external_id = 8492
    grupo_classe = baker.make(
        "rodoviaria.GrupoClasse",
        grupo=baker.make("rodoviaria.Grupo", company_integracao=vexado_company),
    )
    vexado_grupo_classe = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe_external_id=grupo_classe_external_id,
        grupo_classe=grupo_classe,
        status=VexadoGrupoClasse.Status.CANCELADO,
    )
    with mock.patch.object(vexado_api.VexadoAPI, "lista_reservas_viagem") as mock_lista_reservas_viagem:
        mock_lista_reservas_viagem.return_value = []
        verificar_grupos_cancelados_svc.check_passagens_canceladas_hibrido()
    mock_lista_reservas_viagem.assert_called_once_with(grupo_classe_external_id)
    vexado_grupo_classe.refresh_from_db()
    assert vexado_grupo_classe.status == VexadoGrupoClasse.Status.CANCELADO_DUPLO_CHECK


def test_check_passagens_canceladas_hibrido_cancela_passagens_inexistente_no_rodoviaria(vexado_company, vexado_login):
    grupo_classe_external_id = 8492
    reserva_id = 53234
    grupo_classe = baker.make(
        "rodoviaria.GrupoClasse",
        grupo=baker.make("rodoviaria.Grupo", company_integracao=vexado_company),
    )
    vexado_grupo_classe = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe_external_id=grupo_classe_external_id,
        grupo_classe=grupo_classe,
        status=VexadoGrupoClasse.Status.CANCELADO,
    )
    with mock.patch.object(
        vexado_api.VexadoAPI, "lista_reservas_viagem"
    ) as mock_lista_reservas_viagem, mock.patch.object(
        vexado_api.VexadoAPI, "cancelar_reserva_por_localizador"
    ) as mock_cancelar_reserva_por_localizador:
        mock_lista_reservas_viagem.return_value = [reserva_id]
        response = verificar_grupos_cancelados_svc.check_passagens_canceladas_hibrido()
    mock_lista_reservas_viagem.assert_called_once_with(grupo_classe_external_id)
    mock_cancelar_reserva_por_localizador.assert_called_once_with(reserva_id)
    vexado_grupo_classe.refresh_from_db()
    assert vexado_grupo_classe.status == VexadoGrupoClasse.Status.CANCELADO_DUPLO_CHECK
    assert response == {"grupos_com_passagens_pendentes": [grupo_classe_external_id]}


def test_check_passagens_canceladas_hibrido_nao_busca_grupo_external_id_none(vexado_company, vexado_login):
    grupo_classe = baker.make(
        "rodoviaria.GrupoClasse",
        grupo=baker.make("rodoviaria.Grupo", company_integracao=vexado_company),
    )
    vexado_grupo_classe = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe_external_id=None,
        grupo_classe=grupo_classe,
        status=VexadoGrupoClasse.Status.CANCELADO,
    )
    with mock.patch.object(
        vexado_api.VexadoAPI, "lista_reservas_viagem"
    ) as mock_lista_reservas_viagem, mock.patch.object(
        vexado_api.VexadoAPI, "cancelar_reserva_por_localizador"
    ) as mock_cancelar_reserva_por_localizador:
        response = verificar_grupos_cancelados_svc.check_passagens_canceladas_hibrido()
    mock_lista_reservas_viagem.assert_not_called()
    mock_cancelar_reserva_por_localizador.assert_not_called()
    vexado_grupo_classe.refresh_from_db()
    assert vexado_grupo_classe.status == VexadoGrupoClasse.Status.CANCELADO
    assert response == {"grupos_com_passagens_pendentes": []}


def test_check_passagens_canceladas_hibrido_cancela_passagens_existente_no_rodoviaria(vexado_company, vexado_login):
    grupo_classe_external_id = 8492
    reserva_id = 53234
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company)
    grupo_classe = baker.make("rodoviaria.GrupoClasse", grupo=grupo)
    vexado_grupo_classe = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe_external_id=grupo_classe_external_id,
        grupo_classe=grupo_classe,
        status=VexadoGrupoClasse.Status.CANCELADO,
    )
    trecho_classe = baker.make("rodoviaria.TrechoClasse", external_id=grupo_classe_external_id, grupo=grupo)
    passagem = baker.make(
        "rodoviaria.Passagem",
        trechoclasse_integracao=trecho_classe,
        status=Passagem.Status.CONFIRMADA,
        localizador=reserva_id,
    )
    with mock.patch.object(
        vexado_api.VexadoAPI, "lista_reservas_viagem"
    ) as mock_lista_reservas_viagem, mock.patch.object(
        vexado_api.VexadoAPI, "cancelar_reserva_por_localizador"
    ) as mock_cancelar_reserva_por_localizador:
        mock_lista_reservas_viagem.return_value = [reserva_id]
        response = verificar_grupos_cancelados_svc.check_passagens_canceladas_hibrido()
    mock_lista_reservas_viagem.assert_called_once_with(grupo_classe_external_id)
    mock_cancelar_reserva_por_localizador.assert_called_once_with(reserva_id)
    vexado_grupo_classe.refresh_from_db()
    passagem.refresh_from_db()
    assert vexado_grupo_classe.status == VexadoGrupoClasse.Status.CANCELADO_DUPLO_CHECK
    assert passagem.status == Passagem.Status.CANCELADA
    assert response == {"grupos_com_passagens_pendentes": [grupo_classe_external_id]}


def test_check_passagens_canceladas_hibrido_cancela_passagens_error(vexado_company, vexado_login):
    grupo_classe_external_id = 8492
    reserva_id = 53234
    grupo_classe = baker.make(
        "rodoviaria.GrupoClasse",
        grupo=baker.make("rodoviaria.Grupo", company_integracao=vexado_company),
    )
    vexado_grupo_classe = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe_external_id=grupo_classe_external_id,
        grupo_classe=grupo_classe,
        status=VexadoGrupoClasse.Status.CANCELADO,
    )
    with mock.patch.object(
        vexado_api.VexadoAPI, "lista_reservas_viagem"
    ) as mock_lista_reservas_viagem, mock.patch.object(
        vexado_api.VexadoAPI, "cancelar_reserva_por_localizador"
    ) as mock_cancelar_reserva_por_localizador:
        mock_lista_reservas_viagem.return_value = [reserva_id]
        mock_cancelar_reserva_por_localizador.side_effect = exceptions.VexadoAPIError("erro")
        verificar_grupos_cancelados_svc.check_passagens_canceladas_hibrido()
    mock_lista_reservas_viagem.assert_called_once_with(grupo_classe_external_id)
    mock_cancelar_reserva_por_localizador.assert_called_once_with(reserva_id)
    vexado_grupo_classe.refresh_from_db()
    assert vexado_grupo_classe.status == VexadoGrupoClasse.Status.CANCELADO_CHECK_ERRO

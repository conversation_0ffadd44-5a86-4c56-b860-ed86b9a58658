from unittest import mock

import pytest
from django.db import connections
from model_bakery import baker

from rodoviaria.models.core import Passagem
from rodoviaria.service import get_passagens_svc
from rodoviaria.views_schemas import BulkGrupoTemPassagemEmitidaParams


@pytest.fixture
def passagens():
    ids = [(1, 10), (2, 20), (3, 30), (4, 40), (5, 50), (6, 60)]
    status = iter(
        (
            Passagem.Status.CONFIRMADA,
            Passagem.Status.CONFIRMADA,
            Passagem.Status.CANCELADA,
            Passagem.Status.CANCELADA,
            Passagem.Status.ERRO,
            Passagem.Status.INCOMPLETA,
        )
    )
    pax = []
    for t_id, b_id in ids:
        p = baker.make(
            Passagem,
            travel_internal_id=t_id,
            buseiro_internal_id=b_id,
            status=next(status),
        )
        pax.append(p)

    yield pax
    for p in pax:
        p.delete()


def test_get_passagens_no_status(passagens, django_assert_num_queries):
    travel_internal_ids_confirmadas = (1, 2)
    travel_internal_ids_canceladas = (3, 4)
    travel_internal_ids_improprias = (5, 6)
    travel_internal_ids = (
        travel_internal_ids_confirmadas + travel_internal_ids_canceladas + travel_internal_ids_improprias
    )
    buseiro_internal_ids_confirmadas = (10, 20)
    buseiro_internal_ids_canceladas = (30, 40)
    buseiro_internal_ids_improprias = (50, 60)
    buseiro_internal_ids = (
        buseiro_internal_ids_confirmadas + buseiro_internal_ids_canceladas + buseiro_internal_ids_improprias
    )
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        passagens = get_passagens_svc.get_passagens(travel_ids=travel_internal_ids, buseiro_ids=buseiro_internal_ids)

    assert len(passagens) == 4
    for p in passagens:
        assert p["travel_internal_id"] in (travel_internal_ids_confirmadas + travel_internal_ids_canceladas)
        assert p["buseiro_internal_id"] in (buseiro_internal_ids_confirmadas + buseiro_internal_ids_canceladas)
        assert p["status"] in (Passagem.Status.CONFIRMADA, Passagem.Status.CANCELADA)
        assert tuple(p.keys()) == (
            "travel_internal_id",
            "buseiro_internal_id",
            "status",
            "localizador",
            "numero_passagem",
        )


def test_get_passagens_confirmadas(passagens, django_assert_num_queries):
    travel_internal_ids_confirmadas = (1, 2)
    travel_internal_ids_canceladas = (3, 4)
    travel_internal_ids_improprias = (5, 6)
    travel_internal_ids = (
        travel_internal_ids_confirmadas + travel_internal_ids_canceladas + travel_internal_ids_improprias
    )
    buseiro_internal_ids_confirmadas = (10, 20)
    buseiro_internal_ids_canceladas = (30, 40)
    buseiro_internal_ids_improprias = (50, 60)
    buseiro_internal_ids = (
        buseiro_internal_ids_confirmadas + buseiro_internal_ids_canceladas + buseiro_internal_ids_improprias
    )
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        passagens = get_passagens_svc.get_passagens(
            travel_ids=travel_internal_ids,
            buseiro_ids=buseiro_internal_ids,
            status=Passagem.Status.CONFIRMADA,
        )

    assert len(passagens) == 2
    for p in passagens:
        assert p["travel_internal_id"] in travel_internal_ids_confirmadas
        assert p["buseiro_internal_id"] in buseiro_internal_ids_confirmadas
        assert p["status"] in Passagem.Status.CONFIRMADA
        assert tuple(p.keys()) == (
            "travel_internal_id",
            "buseiro_internal_id",
            "status",
            "localizador",
            "numero_passagem",
        )


def test_get_passagens_no_buseiro_ids(passagens, django_assert_num_queries):
    travel_internal_ids_confirmadas = (1, 2)
    travel_internal_ids_canceladas = (3, 4)
    travel_internal_ids_improprias = (5, 6)
    travel_internal_ids = (
        travel_internal_ids_confirmadas + travel_internal_ids_canceladas + travel_internal_ids_improprias
    )
    buseiro_internal_ids_confirmadas = (10, 20)
    buseiro_internal_ids_canceladas = (30, 40)

    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        passagens = get_passagens_svc.get_passagens(travel_ids=travel_internal_ids)

    assert len(passagens) == 4
    for p in passagens:
        assert p["travel_internal_id"] in (travel_internal_ids_confirmadas + travel_internal_ids_canceladas)
        assert p["buseiro_internal_id"] in (buseiro_internal_ids_confirmadas + buseiro_internal_ids_canceladas)
        assert p["status"] in (Passagem.Status.CONFIRMADA, Passagem.Status.CANCELADA)
        assert tuple(p.keys()) == (
            "travel_internal_id",
            "buseiro_internal_id",
            "status",
            "localizador",
            "numero_passagem",
        )


def test_get_passagens_show_preco_rodoviaria(passagens, django_assert_num_queries):
    travel_internal_ids_confirmadas = (1, 2)
    travel_internal_ids_canceladas = (3, 4)
    travel_internal_ids_improprias = (5, 6)
    travel_internal_ids = (
        travel_internal_ids_confirmadas + travel_internal_ids_canceladas + travel_internal_ids_improprias
    )
    buseiro_internal_ids_confirmadas = (10, 20)
    buseiro_internal_ids_canceladas = (30, 40)
    buseiro_internal_ids_improprias = (50, 60)
    buseiro_internal_ids = (
        buseiro_internal_ids_confirmadas + buseiro_internal_ids_canceladas + buseiro_internal_ids_improprias
    )
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        passagens = get_passagens_svc.get_passagens(
            travel_ids=travel_internal_ids,
            buseiro_ids=buseiro_internal_ids,
            with_preco_rodoviaria=True,
        )

    assert len(passagens) == 4
    for p in passagens:
        assert p["travel_internal_id"] in (travel_internal_ids_confirmadas + travel_internal_ids_canceladas)
        assert p["buseiro_internal_id"] in (buseiro_internal_ids_confirmadas + buseiro_internal_ids_canceladas)
        assert p["status"] in (Passagem.Status.CONFIRMADA, Passagem.Status.CANCELADA)
        assert tuple(p.keys()) == (
            "travel_internal_id",
            "buseiro_internal_id",
            "status",
            "localizador",
            "numero_passagem",
            "preco_rodoviaria",
        )


def test_grupos_com_emissao():
    params = BulkGrupoTemPassagemEmitidaParams.parse_obj(
        {
            "grupos_passenger_map": {
                "4232": [
                    {"travel_id": 555, "buseiro_id": 555},
                    {"travel_id": 123, "buseiro_id": 456},
                ],
                "8932": [{"travel_id": 999, "buseiro_id": 999}],
            }
        }
    )
    baker.make(
        Passagem,
        travel_internal_id=123,
        buseiro_internal_id=456,
        status=Passagem.Status.CONFIRMADA,
    )
    resposta = get_passagens_svc.grupos_com_emissao(params.grupos_passenger_map)
    assert resposta == {4232: True, 8932: False}


def test_grupos_com_emissao_buseiro_e_travel_separados():
    params = BulkGrupoTemPassagemEmitidaParams.parse_obj(
        {
            "grupos_passenger_map": {
                "4232": [
                    {"travel_id": 555, "buseiro_id": 555},
                    {"travel_id": 123, "buseiro_id": 456},
                ]
            }
        }
    )
    baker.make(
        Passagem,
        travel_internal_id=555,
        buseiro_internal_id=456,
        status=Passagem.Status.CONFIRMADA,
    )
    resposta = get_passagens_svc.grupos_com_emissao(params.grupos_passenger_map)
    assert resposta == {4232: False}


def test_get_passagem_info():
    p = baker.make(
        Passagem,
        travel_internal_id=555,
        buseiro_internal_id=456,
        status=Passagem.Status.CONFIRMADA,
    )
    passagem = Passagem.objects.filter(id=p.id).values()
    resposta = get_passagens_svc.get_passagem_info(travel_id=555, buseiro_id=456)
    assert resposta == list(passagem)


def test_get_passagem_info_conexao_com_duas_passagens():
    p1, p2 = baker.make(
        Passagem,
        travel_internal_id=555,
        buseiro_internal_id=456,
        status=Passagem.Status.CONFIRMADA,
        _quantity=2,
        _bulk_create=True,
    )
    passagens = Passagem.objects.filter(id__in=[p1.id, p2.id]).order_by("id").values()
    resposta = get_passagens_svc.get_passagem_info(travel_id=555, buseiro_id=456)
    assert sorted(resposta, key=lambda d: d["id"]) == sorted(passagens, key=lambda d: d["id"])


def test_get_atualizacao_passagem_api_parceiro_not_implemented_error(passagem_ze, totalbus_company, totalbus_login):
    buseiro_id = passagem_ze.buseiro_internal_id
    modelo_venda = "marketplace"
    travel_id = passagem_ze.travel_internal_id
    with mock.patch.object(
        get_passagens_svc.OrchestrateRodoviaria, "get_atualizacao_passagem_api_parceiro"
    ) as mock_info:
        mock_info.side_effect = NotImplementedError("bla")
        with pytest.raises(NotImplementedError) as exc_info:
            get_passagens_svc.get_infos_atualizacao_passagem(buseiro_id, modelo_venda, travel_id)
        assert str(exc_info.value) == "bla"
        assert exc_info.typename == "NotImplementedError"

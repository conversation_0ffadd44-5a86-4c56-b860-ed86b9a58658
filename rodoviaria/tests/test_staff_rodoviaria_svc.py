from unittest import mock

from rodoviaria.service import staff_rodoviaria_svc
from rodoviaria.views_schemas import BulkGetPoltronasParams, TravelGetPoltronas


def test_bulk_get_poltronas():
    trecho_classe_id = 84213
    params = BulkGetPoltronasParams(
        trecho_classe_id=trecho_classe_id,
        travels=[
            TravelGetPoltronas(travel_id=1234, numero_passageiros=2),
            TravelGetPoltronas(travel_id=4321, numero_passageiros=2),
        ],
    )
    with mock.patch("rodoviaria.service.staff_rodoviaria_svc.CompraRodoviariaSVC") as mock_compra_svc:
        mock_compra_svc.return_value.get_map_poltronas.return_value = {
            13: "livre",
            14: "ocupada",
            15: "livre",
            16: "livre",
            17: "ocupada",
            18: "livre",
        }
        poltronas = staff_rodoviaria_svc.bulk_get_poltronas(params)
    mock_compra_svc.assert_called_once_with(trecho_classe_id)
    assert poltronas == {1234: [15, 16], 4321: [13, 18]}

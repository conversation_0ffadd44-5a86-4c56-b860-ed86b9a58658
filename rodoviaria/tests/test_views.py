import copy
import json
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal as D
from types import SimpleNamespace
from unittest import mock

import pytest
import responses
import time_machine
from django.http import JsonResponse
from django.utils import timezone
from model_bakery import baker
from pydantic import BaseModel, ValidationError
from zoneinfo import ZoneInfo

from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import to_default_tz
from commons.taggit_utils import Tags
from core.models_company import Company as CompanyBdjango
from core.models_grupo import Grupo as GrupoBdjango
from core.models_grupo import GrupoClasse as GrupoClasseBdjango
from core.models_rota import Rota as RotaBdjango
from core.models_rota import TrechoVendido as TrechoVendidoBdjango
from rodoviaria import views
from rodoviaria.api.forms import RetornoConsultarBilheteForm
from rodoviaria.api.praxio import endpoints as praxio_endpoints
from rodoviaria.api.totalbus import endpoints as totalbus_endpoints
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.forms.compra_rodoviaria_forms import (
    BloquearPoltronasForm,
    BloquearPoltronasFormV2,
    BloquearPoltronasResponse,
    DefaultForm,
    DesbloquearPoltronasForm,
)
from rodoviaria.forms.mapa_poltronas_forms import Assento, Deck, Onibus
from rodoviaria.forms.motorista_forms import Motorista as MotoristaForm
from rodoviaria.forms.staff_forms import (
    CheckPaxBatchForm,
    CheckPaxForm,
    CreateRodoviariaCompanyForm,
    RodoviariaLinkarTiposAssentosParams,
    TotalbusNoCompanyLogin,
    VexadoAnonymousLogin,
)
from rodoviaria.models.core import Company, Integracao, Passagem, Rota, TaskStatus, TrechoClasse, TrechoClasseError
from rodoviaria.serializers.serializer_passagem import PassagemSerializer
from rodoviaria.service import cadastrar_grupos_hibridos_svc, veiculos_svc
from rodoviaria.service.cadastrar_grupos_hibridos_svc import CadastrarGrupoSVC
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import (
    RodoviariaCompanyNotFoundException,
    RodoviariaException,
    RodoviariaIntegracaoNotFoundException,
    RodoviariaLoginNotFoundException,
    RodoviariaNoLocalCheckpointException,
    RodoviariaNoTimezoneException,
    RodoviariaRotaNotFoundException,
    RodoviariaUnauthorizedError,
    TaskInExecutionException,
)
from rodoviaria.service.status_integracao_svc import StatusIntegracaoSVC
from rodoviaria.tests.conftest import ninja_client
from rodoviaria.tests.middleware import request_with_middleware
from rodoviaria.tests.praxio import mocker as praxio_mocker
from rodoviaria.tests.totalbus import mocker as totalbus_mocker
from rodoviaria.views_schemas import (
    AlterarGrupoParams,
    AtualizacaoCheckoutAsyncParams,
    BloquearPoltronasParams,
    BloquearPoltronasParamsV2,
    BulkGetPoltronasParams,
    CadastrarGruposParams,
    CadastrarTrechosParams,
    CadastrarVeiculoParams,
    CancelarGruposClasseParams,
    DesbloquearPoltronasParams,
    GetDetalhesRotinasParams,
    GrupoParams,
    LinkTrechosClassesAsyncParams,
    ListaEmpresasAPIParams,
    RemanejaPassageirosAsyncParams,
    SolicitaCancelamentoPorPassagemIdParams,
    SolicitaCancelamentoTravelsParams,
    TrechoClasseAddRemoveTagParams,
    TrechoClasseGetAtualizaParams,
    TrechoParams,
    UpdateGruposClasseLink,
)


@pytest.fixture
def empresa_com_feature():
    empresa = baker.make(
        "rodoviaria.Company",
        company_internal_id=999,
        features=[
            Company.Feature.ITINERARIO,
            Company.Feature.ADD_PAX_STAFF,
            Company.Feature.BUSCAR_SERVICO,
        ],
    )
    yield empresa
    empresa.delete()


def test_view_map_rotinas_integradas(rf, override_config):
    request = rf.get("/map_rotinas_integradas")
    with override_config(ROTINAS_INTEGRADAS_RODEROTAS="12,13,15"):
        response = views.map_rotinas_integradas(request)
    assert json.loads(response.content) == {"313": [12, 13, 15]}


def test_view_empresas_ativas(empresa_ativa, empresa_com_feature):
    response = ninja_client.get("/v1/empresas?features=active")
    data = json.loads(response.content)
    assert 1 == len(data["empresas"])
    assert data["empresas"][0]["company_internal_id"] == 33333
    assert data["empresas"][0]["nome"] == "Test"
    assert data["empresas"][0]["modelo_venda"] == "marketplace"
    assert data["empresas"][0]["integracao"] == "integracao1"
    assert data["empresas"][0]["features"] == ["active"]
    assert data["empresas"][0]["update_routes_trigger_time"] is None
    assert data["empresas"][0]["update_trechos_vendidos_trigger_time"] is None


def test_view_empresas_com_feature(empresa_ativa, empresa_com_feature):
    response = ninja_client.get("/v1/empresas?features=itinerario")
    data = json.loads(response.content)
    assert 1 == len(data["empresas"])
    assert data["empresas"][0]["company_internal_id"] == 999
    assert data["empresas"][0]["update_routes_trigger_time"] == "quarta-feira às 02:50h"
    assert data["empresas"][0]["update_trechos_vendidos_trigger_time"] == "sexta-feira às 02:50h"


def test_get_empresas_ativa_por_id(empresa_ativa):
    response = ninja_client.get(
        f"/v1/empresas?id={empresa_ativa.company_internal_id}&modelo_venda={empresa_ativa.modelo_venda}"
    )
    data = json.loads(response.content)
    assert data == {
        "id": empresa_ativa.id,
        "name": "Test",
        "active": True,
        "company_external_id": 42,
        "modelo_venda": Company.ModeloVenda.MARKETPLACE,
        "company_internal_id": 33333,
        "created_at": mock.ANY,
        "features": ["active"],
        "integracao": {"name": mock.ANY, "id": mock.ANY},
        "updated_at": mock.ANY,
        "url_base": "http://example.com",
        "max_percentual_divergencia": None,
    }


def test_empresas_inexistente(empresa_ativa):
    response = ninja_client.get(
        f"/v1/empresas?id={empresa_ativa.company_internal_id}&modelo_venda={Company.ModeloVenda.HIBRIDO}"
    )
    data = json.loads(response.content)
    assert data == {"message": "Not found."}


def test_empresas_paginadas(empresa_ativa):
    name = ""
    status = "ativas"
    modelo_venda = ""
    rows_per_page = 10
    page = 1
    order_by = "name"

    params = (
        f"/v1/empresas?name={name}"
        f"&status={status}"
        f"&modelo_venda={modelo_venda}"
        f"&rows_per_page={rows_per_page}"
        f"&page={page}"
        f"&order_By={order_by}"
    )

    with mock.patch("rodoviaria.service.company_svc.companies_paginator") as mock_companies_paginator:
        mock_companies_paginator.return_value = {
            "companies": [],
            "count": 0,
            "num_pages": 1,
        }
        response = ninja_client.get(params)

    assert response.status_code == 200
    assert json.loads(response.content) == {"companies": [], "count": 0, "num_pages": 1}


def test_view_get_poltronas(rf, guiche_trechoclasses, mock_guiche_get_poltronas, mock_guiche_bloquear_poltrona):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = guiche_trechoclasses.ida.grupo
        request = rf.get("/rodoviaria/get_poltronas")
        response = views.get_poltronas(
            request,
            trecho_classe_id=guiche_trechoclasses.ida.trechoclasse_internal_id,
            num_passageiros=2,
        )
        data = json.loads(response.content)
        assert data == [4, 5]


def test_view_get_poltronas_sem_trecho_classe(rf):
    request = rf.get("/rodoviaria/get_poltronas")
    response = views.get_poltronas(
        request,
        trecho_classe_id=None,
        num_passageiros=2,
    )
    data = json.loads(response.content)
    assert data == {"message": "Necessário passar o trecho_classe_id"}


def test_view_get_poltronas_sem_num_passageiros(rf, guiche_trechoclasses):
    request = rf.get("/rodoviaria/get_poltronas")
    response = views.get_poltronas(
        request,
        trecho_classe_id=guiche_trechoclasses.ida.trechoclasse_internal_id,
        num_passageiros=0,
    )
    data = json.loads(response.content)
    assert data == {"message": "Necessário passar o num_passageiros"}


def test_view_passageiros(rf, passagem_ze, totalbus_company, totalbus_login):
    totalbus_company.features = ["add_pax_staff"]
    totalbus_company.save()
    response = ninja_client.get(
        f"/v1/passageiros?company_id={totalbus_company.company_internal_id}&travels_ids={passagem_ze.travel_internal_id}&travels_ids=2"
    )
    assert response.status_code == 200
    data = json.loads(response.content)
    assert len(data) == 1
    data = data[0]
    assert data["id"] == passagem_ze.id
    assert data["poltrona"] == passagem_ze.poltrona_external_id
    assert data["localizador"] == passagem_ze.localizador
    assert data["numero_passagem"] == passagem_ze.numero_passagem
    assert data["buseiro_id"] == passagem_ze.buseiro_internal_id
    assert data["status"] == passagem_ze.status
    assert data["travel_id"] == passagem_ze.travel_internal_id
    assert data["origem"] is None
    assert data["destino"] is None


def test_view_passageiros_com_origem_e_destino(rf, passagem_ze, totalbus_company, totalbus_login):
    passagem_ze.origem = "ORIGEM"
    passagem_ze.destino = "DESTINO"
    passagem_ze.save()
    totalbus_company.features = ["add_pax_staff"]
    totalbus_company.save()
    response = ninja_client.get(
        f"/v1/passageiros?company_id={totalbus_company.company_internal_id}&travels_ids={passagem_ze.travel_internal_id}&travels_ids=2"
    )
    assert response.status_code == 200
    data = json.loads(response.content)
    assert len(data) == 1
    data = data[0]
    assert data["origem"] == "ORIGEM"
    assert data["destino"] == "DESTINO"


def test_view_fetch_rotas_erro(rf, passagem_ze, totalbus_company):
    request = rf.post("/rodoviaria/fetch_rotas")
    response = views.fetch_rotas(request)
    data = json.loads(response.content)
    assert response.status_code == 422
    assert data == {"message": "Necessário passar o grupo_id ou company_id"}


def test_view_fetch_rotas(rf, passagem_ze, totalbus_company):
    with mock.patch("rodoviaria.service.marketplace_fetch_rotas_svc.marketplace_fetch_rotas") as mock_fetch_rotas:
        mock_fetch_rotas.return_value = {"quantidade_rotas_criadas": 4}
        request = rf.post("/rodoviaria/fetch_rotas")
        response = views.fetch_rotas(request, company_id=totalbus_company.id)
        data = json.loads(response.content)
        assert data == {"quantidade_rotas_criadas": 4}


def test_view_efetua_cancelamento(rf, passagem_ze):
    request = rf.get("/rodoviaria/compra/cancela")
    with mock.patch("rodoviaria.service.reserva_svc.efetua_cancelamento") as mock_efetua_cancelamento:
        mock_efetua_cancelamento.return_value = JsonResponse({"passagens_canceladas": {}})
        response = views.efetua_cancelamento(
            request,
            travel_id=passagem_ze.travel_internal_id,
            buseiro_id=passagem_ze.buseiro_internal_id,
        )
    assert response.status_code == 200
    mock_efetua_cancelamento.assert_called_once_with(
        passagem_ze.travel_internal_id, passagem_ze.buseiro_internal_id, None
    )


def test_service_not_found_with_error_middleware(
    rf, buser_grupos, guiche_login, guiche_company, buser_company_expresso
):
    guiche_company.company_internal_id = buser_company_expresso.id
    guiche_company.save()

    trecho_classe_id = buser_grupos.unlinked_ida.trechoclasse.id
    num_passageiros = 2
    request = rf.get(
        "/rodoviaria/v1/get_poltronas", data={"trecho_classe_id": trecho_classe_id, "num_passageiros": num_passageiros}
    )

    response = request_with_middleware(request)

    local_embarque_origem_id = buser_grupos.unlinked_ida.trechoclasse.trecho_vendido.origem.id
    assert response.status_code == 404
    data = json.loads(response.content)
    assert "error" in data
    assert f"Local de embarque de origem de id {local_embarque_origem_id}" in data["error"]


def test_list_link_local_embarque(rf, guiche_locais):
    params = {
        "paginator": {
            "descending": "true",
            "page": 1,
            "rowsPerPage": 100,
            "sortBy": "id",
            "totalItems": 0,
        },
        "search": "",
        "empresa_id_filter": 0,
    }
    request = rf.post(
        "rodoviaria/local_embarque/list",
        data=json.dumps(params),
        content_type="application/json",
    )
    response = views.list_link_local_embarque(request)
    data = json.loads(response.content)
    items_ids = [i["id"] for i in data["items"]]
    assert data["count"] == 2
    assert len(items_ids) == 2
    assert guiche_locais.origem.id in items_ids
    assert guiche_locais.destino.id in items_ids


def test_atualiza_link_local_embarque(rf):
    params = {"link_id": 26, "local_embarque_id": 18, "cidade_internal_id": 72}
    with mock.patch("rodoviaria.service.links_locais_embarque_svc.update_link_local_embarque") as mock_update_link:
        mock_update_link.return_value = True
        request = rf.post("v1/local_embarque/atualiza", params=params)
        response = views.atualiza_link_local_embarque(request)
    assert response.status_code == 200
    data = json.loads(response.content)
    assert data is True


def test_dados_bpe_passagem(rf, totalbus_mock_login, buser_travel_ida_id, passagem_ze):
    req = rf.get("/dados_bpe_passagem")
    resp = views.dados_bpe_passagem(req, buser_travel_ida_id)
    assert resp.status_code == 200
    respj = json.loads(resp.content)
    passagens = respj["passagens"]
    passagem = passagens[0]
    assert isinstance(passagens, list)
    assert respj["linha"] == "GOIÂNIA (GO) X INHUMAS (GO)"
    assert respj["prefixo"] == "16015831"
    assert respj["servico"] == "1020"
    assert passagem["linha"] == "São Bernardo -> Fortaleza"
    assert passagem["prefixo"] == "16015831"
    assert passagem["servico"] == "1020"
    assert passagem["tarifa"] == "224.82"
    assert passagem["pedagio"] == "0.00"
    assert passagem["taxa_de_embarque"] == "5.49"
    assert passagem["seguro"] == "0.00"
    assert passagem["outras_taxas"] == "0.00"
    assert passagem["valor_total"] == "230.31"
    assert passagem["desconto"] == "60.70"
    assert passagem["valor_pgto"] == "169.61"
    assert passagem["valor_pago"] == "169.61"
    assert passagem["troco"] == "0.00"
    assert passagem["bpe_qrcode"] == (
        "https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe="
        "31200641550112006304630550000132511509753679&tpAmb=2"
    )
    assert passagem["monitriip"] == (
        "312006415501120063046305500001325115097536790000132510MARINHO20200618060000010000011917"
        "0000000000000000002565000072"
    )
    assert passagem["poltrona"] == 10
    assert passagem["bpe"]["chave"] == "41210891873372000692630010000037231014912221"
    assert passagem["bpe"]["numero"] == "3723"
    assert passagem["bpe"]["serie"] == "001"
    assert passagem["bpe"]["tipo"] == "Normal"
    assert passagem["bpe"]["protocolo_autorizacao"] == "141210006048505"
    assert passagem["bpe"]["data_autorizacao"]
    assert passagem["numero_bilhete"] == "10000000053566"
    assert passagem["localizador"] == "010000079011"
    assert passagem["cnpj"] == "12.402.506/0001-06"
    assert (
        passagem["endereco_empresa"] == "Rua do papagaio 1999, sala 15, Quinta das Frutas, São Paulo-SP, CEP: 12900-500"
    )
    assert passagem["inscricao_estadual"] == "*********"
    assert passagem["nome_agencia"] == "BUSER 1234"
    assert passagem["tipo_emissao"] == "NORMAL"
    assert passagem["data_emissao"] == to_default_tz(passagem_ze.created_at).strftime("%d/%m/%Y %H:%M")
    assert passagem["plataforma"] == "21"
    assert passagem["modelo_venda"] == "marketplace"
    assert passagem["outros_tributos"] == "ICMS:12,30 (10,00%) OUTROS TRIB:13,53 (11,00%)"
    assert passagem["bpe_em_contingencia"] is False
    assert passagem["passagem_id"] == passagem_ze.id
    assert respj["outros_tributos"] == "ICMS:12,30 (10,00%) OUTROS TRIB:13,53 (11,00%)"


def test_dados_bpe_passagem_no_feature_flag(
    rf, totalbus_mock_login, buser_travel_ida_id, passagem_ze, totalbus_company
):
    totalbus_company.features.remove("bpe")
    totalbus_company.save()
    req = rf.get("/dados_bpe_passagem")
    resp = views.dados_bpe_passagem(req, buser_travel_ida_id)
    assert resp.status_code == 403


def test_dados_bpe_passagem_batch(rf, totalbus_mock_login, buser_travel_ida_id, passagem_ze):
    resp = ninja_client.get(f"/v1/dados_bpe_passagem_batch?travel_ids={buser_travel_ida_id}")
    assert resp.status_code == 200
    respj = resp.json()
    passagens = respj[0]["passagens"]
    passagem = passagens[0]
    assert isinstance(passagens, list)
    assert respj[0]["travel_id"] == buser_travel_ida_id
    assert respj[0]["linha"] == "GOIÂNIA (GO) X INHUMAS (GO)"
    assert respj[0]["prefixo"] == "16015831"
    assert respj[0]["servico"] == "1020"
    assert respj[0]["outros_tributos"] == "ICMS:12,30 (10,00%) OUTROS TRIB:13,53 (11,00%)"
    assert passagem["tarifa"] == "224.82"
    assert passagem["pedagio"] == "0.00"
    assert passagem["taxa_de_embarque"] == "5.49"
    assert passagem["seguro"] == "0.00"
    assert passagem["outras_taxas"] == "0.00"
    assert passagem["valor_total"] == "230.31"
    assert passagem["desconto"] == "60.70"
    assert passagem["valor_pgto"] == "169.61"
    assert passagem["valor_pago"] == "169.61"
    assert passagem["troco"] == "0.00"
    assert passagem["bpe_qrcode"] == (
        "https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?"
        "chBPe=31200641550112006304630550000132511509753679&tpAmb=2"
    )
    assert passagem["monitriip"] == (
        "312006415501120063046305500001325115097536790000132510MARINHO"
        "202006180600000100000119170000000000000000002565000072"
    )
    assert passagem["poltrona"] == 10
    assert passagem["bpe"]["chave"] == "41210891873372000692630010000037231014912221"
    assert passagem["bpe"]["numero"] == "3723"
    assert passagem["bpe"]["serie"] == "001"
    assert passagem["bpe"]["tipo"] == "Normal"
    assert passagem["bpe"]["protocolo_autorizacao"] == "141210006048505"
    assert passagem["bpe"]["data_autorizacao"]
    assert passagem["numero_bilhete"] == "10000000053566"
    assert passagem["localizador"] == "010000079011"
    assert passagem["travel_id"] == 2048
    assert passagem["buseiro_id"] == 1337


def test_dados_bpe_passagem_batch_passagem_nao_cadastrada(rf, totalbus_mock_login, buser_travel_ida_id, passagem_ze):
    response = ninja_client.get("/v1/dados_bpe_passagem_batch?travel_ids=999987")
    assert response.json() == []


def test_has_bpe_with_bpe_qrcode(rf, totalbus_trechoclasses, totalbus_grupos_mockado, buser_travel_ida_id, passagem_ze):
    passagem_ze.bpe_qrcode = "qrcode"
    passagem_ze.embarque_eletronico = None
    passagem_ze.save()
    req = rf.get("/has_bpe")
    resp = views.has_bpe(req, totalbus_grupos_mockado.ida.trechoclasse.id, buser_travel_ida_id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp == {"has_bpe": True}


def test_has_bpe_with_embarque_eletronico(
    rf, totalbus_trechoclasses, totalbus_grupos_mockado, buser_travel_ida_id, passagem_ze
):
    passagem_ze.bpe_qrcode = None
    passagem_ze.embarque_eletronico = "embarque_eletronico"
    passagem_ze.save()
    req = rf.get("/has_bpe")
    resp = views.has_bpe(req, totalbus_grupos_mockado.ida.trechoclasse.id, buser_travel_ida_id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp == {"has_bpe": True}


def test_not_has_bpe(rf, totalbus_trechoclasses, totalbus_grupos_mockado, buser_travel_ida_id, passagem_ze):
    passagem_ze.bpe_qrcode = None
    passagem_ze.embarque_eletronico = None
    passagem_ze.save()
    req = rf.get("/has_bpe")
    resp = views.has_bpe(req, totalbus_grupos_mockado.ida.trechoclasse.id, buser_travel_ida_id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp == {"has_bpe": False}


def test_get_status_bpe(rf):
    passagem_id = 123456
    req = rf.get("/get-status-bpe")
    with mock.patch(
        "rodoviaria.service.bpe_svc.get_status_bpe",
        return_value={"em_contingenia": True},
    ) as mock_get_status_bpe:
        resp = views.get_status_bpe(req, passagem_id)
    mock_get_status_bpe.assert_called_once_with(passagem_id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp == {"em_contingenia": True}


def test_desbloquear_poltronas(rf):
    with mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.__init__", return_value=None
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.desbloquear_poltronas"
    ) as mock_desbloquear_poltronas:
        mock_desbloquear_poltronas.return_value = {}
        params = {"trecho_classe_id": 1234, "poltronas": [12, 13]}
        request = rf.post("/v1/desbloquear-poltronas")
        response = views.desbloquear_poltronas(request, DesbloquearPoltronasParams.parse_obj(params))
    assert response.status_code == 200
    assert json.loads(response.content) == {}
    expected_form = DesbloquearPoltronasForm(trechoclasse_id=1234, poltronas=[12, 13])
    mock_desbloquear_poltronas.assert_called_once_with(expected_form)


def test_desbloquear_poltronas_not_implemented(rf):
    with mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.__init__", return_value=None
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.desbloquear_poltronas"
    ) as mock_desbloquear_poltronas:
        mock_desbloquear_poltronas.side_effect = NotImplementedError
        params = {"trecho_classe_id": 1234, "poltronas": [12, 13]}
        request = rf.post("/rodoviaria/v1/desbloquear-poltronas", params, content_type="application/json")

        response = request_with_middleware(request)

    assert response.status_code == 501
    assert json.loads(response.content) == {"error": "", "error_type": "not_implemented"}
    expected_form = DesbloquearPoltronasForm(trechoclasse_id=1234, poltronas=[12, 13])
    mock_desbloquear_poltronas.assert_called_once_with(expected_form)


def test_get_map_poltronas(rf):
    trecho_classe_id = 2314

    with mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.__init__", return_value=None
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.get_map_poltronas"
    ) as get_map_poltronas:
        get_map_poltronas.return_value = {"1": "livre", "2": "ocupada"}
        response = ninja_client.get(f"/v1/get_map_poltronas?trecho_classe_id={trecho_classe_id}")

    get_map_poltronas.assert_called_once_with(DefaultForm(trechoclasse_id=trecho_classe_id, force_renew_link=True))
    assert response.status_code == 200
    assert response.json() == {"1": "livre", "2": "ocupada"}


def test_company_has_no_bpe_feature(
    rf, totalbus_trechoclasses, totalbus_grupos_mockado, buser_travel_ida_id, passagem_ze
):
    company = totalbus_trechoclasses.ida.grupo.company_integracao
    company.features.remove("bpe")
    company.save()
    req = rf.get("/has_bpe")
    resp = views.has_bpe(req, totalbus_grupos_mockado.ida.trechoclasse.id, buser_travel_ida_id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp == {"has_bpe": False}


def test_atualiza_status_integracao_batch():
    params = {"trecho_classe_ids": [1, 2, 3]}
    expected_response = {
        1: {"status": "integracao_ok", "preco_rodoviaria": 120},
        2: {"status": "integracao_nao_encontrada", "preco_rodoviaria": None},
        3: {"status": "integracao_pendente", "preco_rodoviaria": None},
    }
    with mock.patch.object(StatusIntegracaoSVC, "atualiza") as mock_atualiza_status_integracao_batch:
        mock_atualiza_status_integracao_batch.return_value = expected_response
        response = ninja_client.post("/v1/trecho_classe_integracao/batch/atualiza", json=params)
    mock_atualiza_status_integracao_batch.assert_called_once()
    assert response.status_code == 200
    assert response.json() == {str(key): value for key, value in expected_response.items()}


def test_atualiza_status_integracao():
    expected_response = {1: {"status": "integracao_ok", "preco_rodoviaria": 120}}
    with mock.patch.object(StatusIntegracaoSVC, "atualiza") as mock_atualiza_status_integracao:
        mock_atualiza_status_integracao.return_value = expected_response
        response = ninja_client.post("/v1/trecho_classe_integracao/atualiza?trecho_classe_id=1")
    mock_atualiza_status_integracao.assert_called_once()
    assert response.status_code == 200
    assert response.json() == {"1": {"status": "integracao_ok", "preco_rodoviaria": 120}}


def test_atualiza_status_integracao_checkout():
    expected_response = {
        "trecho": {"status": "integracao_ok", "preco_rodoviaria": 120},
        "atualiza": True,
        "trecho_id": 1,
    }
    with mock.patch(
        "rodoviaria.service.atualiza_trecho_checkout_svc.atualiza_trecho"
    ) as mock_atualiza_status_integracao_checkout:
        mock_atualiza_status_integracao_checkout.return_value = expected_response
        response = ninja_client.post(
            "/v1/trecho_classe_integracao/atualiza_checkout?trecho_classe_id=1&company_id=123&preco_atual=100.50"
        )
    mock_atualiza_status_integracao_checkout.assert_called_once_with(123, 1, D("100.50"))
    assert response.status_code == 200
    assert response.json() == expected_response


def test_atualiza_status_integracao_checkout_async():
    expected_response = [
        {
            "trecho": {"status": "integracao_ok", "preco_rodoviaria": 120},
            "atualiza": True,
            "trecho_id": 1,
        }
    ]
    params = {"trechos": [{"trecho_classe_id": 123, "company_id": 3}]}
    with mock.patch(
        "rodoviaria.service.atualiza_trecho_checkout_svc.atualiza_trecho_async"
    ) as mock_atualiza_trecho_async:
        mock_atualiza_trecho_async.return_value = expected_response
        response = ninja_client.post("/v1/trecho_classe_integracao/atualiza_checkout_async", json=params)
    mock_atualiza_trecho_async.assert_called_once_with(AtualizacaoCheckoutAsyncParams.parse_obj(params))
    assert response.status_code == 202
    assert response.json() == expected_response


def test_verifica_atualizacao_trecho_classe_integracao_checkout_async():
    expected_response = [
        {
            "trecho": {"status": "integracao_ok", "preco_rodoviaria": 120},
            "atualiza": True,
            "trecho_id": 1,
        }
    ]
    params = {
        "trechos": [
            {"trecho_classe_id": 123, "company_id": 3, "preco_atual": 150},
        ]
    }
    with mock.patch(
        "rodoviaria.service.atualiza_trecho_checkout_svc.verifica_atualizacao"
    ) as mock_verifica_atualizacao:
        mock_verifica_atualizacao.return_value = expected_response
        response = ninja_client.get("/v1/trecho_classe_integracao/verifica_atualizacao_checkout_async", json=params)
    mock_verifica_atualizacao.assert_called_once_with(AtualizacaoCheckoutAsyncParams.parse_obj(params))
    assert response.status_code == 200
    assert response.json() == expected_response


def test_atualiza_status_integracao_checkout_circuit_breaker_error(rf):
    with mock.patch("rodoviaria.service.atualiza_trecho_checkout_svc.atualiza_trecho") as mock_atualiza_trecho:
        mock_atualiza_trecho.side_effect = MyCircuitBreakerError("menssagem qualquer", remaining_seconds=10)
        request = rf.post(
            "/rodoviaria/v1/trecho_classe_integracao/atualiza_checkout?trecho_classe_id=1&company_id=123&preco_atual=100.50",
        )
        response = request_with_middleware(request)

        assert response.status_code == 429
        data = json.loads(response.content)
        assert data["message"] == "menssagem qualquer"


def test_atualiza_status_integracao_sem_dados():
    response = ninja_client.post("/v1/trecho_classe_integracao/atualiza")
    assert response.status_code == 422
    assert response.json() == {"message": "É preciso passar grupo_id ou trecho_classe_id."}


def test_verifica_status_integracao_sem_dados():
    response = ninja_client.get("/v1/trecho_classe_integracao/verifica")
    assert response.status_code == 422
    assert response.json() == {"message": "É preciso passar grupo_id ou trecho_classe_id."}


def test_verifica_status_integracao():
    expected_response = {1: {"status": "integracao_ok", "preco_rodoviaria": 120}}
    with mock.patch.object(StatusIntegracaoSVC, "verifica") as mock_verifica_status_integracao:
        mock_verifica_status_integracao.return_value = expected_response
        response = ninja_client.get("/v1/trecho_classe_integracao/verifica?grupo_id=1")
    mock_verifica_status_integracao.assert_called_once()
    assert response.status_code == 200
    assert response.json() == {"1": {"status": "integracao_ok", "preco_rodoviaria": 120}}


request_remaneja = {
    "travel_id": 1,
    "travel_destino_id": 2,
    "reservation_code": "STARK1",
    "travel_max_split_value": 1,
    "trechoclasse_origem_id": 1,
    "trechoclasse_destino_id": 2,
    "company_origem_id": 1,
    "modelo_venda_origem": Company.ModeloVenda.MARKETPLACE,
    "company_destino_id": 2,
    "modelo_venda_destino": Company.ModeloVenda.MARKETPLACE,
    "is_trechoclasse_origem_integrado": True,
    "is_trechoclasse_destino_integrado": True,
    "passengers": [
        {
            "id": 1,
            "name": "Ze",
            "rg_number": "*********",
            "cpf": "11122233345",
            "phone": "(12)*********",
            "tipo_documento": "RG",
        }
    ],
    "poltronas_destino": [2],
}


def test_remaneja_passageiro(rf):
    request_body = request_remaneja
    req = rf.post("/remaneja_passageiro", request_body, content_type="application/json")
    with mock.patch("rodoviaria.service.remanejamento_svc.remaneja_passageiro_sync") as mock_remaneja_passageiro:
        mock_remaneja_passageiro.return_value = None
        resp = views.remaneja_passageiro(req)
    content = json.loads(resp.content)
    assert resp.status_code == 200
    assert content is None


def test_remaneja_passageiros_async(rf):
    request_body = {"remanejamentos": [request_remaneja]}
    with mock.patch(
        "rodoviaria.service.remanejamento_svc.remaneja_passageiros_async",
        return_value={"message": "tasks disparadas"},
    ) as mock_remaneja_passageiro:
        response = ninja_client.post("/v1/remaneja_passageiros_async", json=request_body)
    mock_remaneja_passageiro.assert_called_once_with(RemanejaPassageirosAsyncParams.parse_obj(request_body))
    content = json.loads(response.content)
    assert response.status_code == 200
    assert content == {"message": "tasks disparadas"}


def test_bulk_get_poltronas(rf):
    request_body = {
        "trecho_classe_id": 21234,
        "travels": [{"travel_id": 1823, "numero_passageiros": 3}],
    }
    with mock.patch(
        "rodoviaria.service.staff_rodoviaria_svc.bulk_get_poltronas",
        return_value={1823: [4, 5, 10]},
    ) as mock_bulk_get_poltronas:
        response = ninja_client.post("/v1/bulk-get-poltronas", json=request_body)
    mock_bulk_get_poltronas.assert_called_once_with(BulkGetPoltronasParams.parse_obj(request_body))
    content = json.loads(response.content)
    assert response.status_code == 200
    assert content == {"1823": [4, 5, 10]}


def test_itinerario_rota_inexistente(rf, praxio_grupos, praxio_login, praxio_company):
    praxio_company.features = ["itinerario"]
    praxio_company.save()
    req = rf.get("/itinerario")
    resp = views.itinerario(
        req,
        praxio_grupos.ida.grupo_internal_id,
    )
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp == {"itinerario": None}


def test_itinerario(rf, praxio_grupos, praxio_login, praxio_company):
    with mock.patch("rodoviaria.service.rota_svc.itinerario") as mock_itinerario:
        mock_itinerario.return_value = [
            {
                "id": 31,
                "cidade_id": 12,
                "departure": "2022-04-02 18:00",
                "local_id": 12,
                "local": {
                    "id": 1,
                    "name": "RIO DE JANEIRO",
                    "uf": "RJ",
                    "nickname": "RIO DE JANEIRO - RJ",
                    "id_external": "22131",
                    "id_internal": 12,
                },
            }
        ]
        req = rf.get("/itinerario")
        resp = views.itinerario(
            req,
            praxio_grupos.ida.grupo_internal_id,
        )
        assert resp.status_code == 200
        resp = json.loads(resp.content)
        assert resp["itinerario"][0]["local"]


def test_get_rotas_empresa(rf, praxio_grupos, praxio_login, praxio_company):
    rota = baker.make(
        "rodoviaria.RotaPraxio",
        company=praxio_company,
        provider_data=json.dumps(itinerario_praxio),
        id_hash="vAbwuGmeecitVcfaNjUPlUbgBdsgymusbKuhegmFrykJESnEsx",
    )
    rota2 = baker.make(
        "rodoviaria.RotaPraxio",
        company=praxio_company,
        provider_data=json.dumps(itinerario_praxio),
        id_internal=12312,
        id_external=123456,
        id_hash="HCuJTCOarPPkOvwPWfeXkQGqkTdgQTgHmwyFfkYdaLSbXIuOVG",
    )
    rotina_datetime_ida = datetime.now() + timedelta(days=3)
    baker.make("rodoviaria.Rotina", rota=rota, datetime_ida=to_default_tz(rotina_datetime_ida))
    cria_mock_checkpoints_itinerario_praxio(rota)
    cria_mock_checkpoints_itinerario_praxio(rota2)
    praxio_company.features = ["itinerario"]
    praxio_company.save()
    praxio_grupos.ida.rota = rota
    praxio_grupos.ida.save()
    req = rf.get("/get_rotas_empresa")
    resp = views.get_rotas_empresa(req, praxio_grupos.ida.company_integracao.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert len(resp["items"]) == 2
    items = sorted(resp["items"], key=lambda x: str(x["id_internal"]))
    assert items[0]["checkpoints"][0]["local"]["name"] == "SAUIPE (BA)"
    assert items[0]["id_internal"] == 12312
    assert items[0]["id_external"] == "123456"
    assert items[1]["id_internal"] is None
    assert items[0]["id_hash"] == "HCuJTCOarPPkOvwPWfeXkQGqkTdgQTgHmwyFfkYdaLSbXIuOVG"
    assert items[0]["rotina"] == {}
    assert items[1]["id_hash"] == "vAbwuGmeecitVcfaNjUPlUbgBdsgymusbKuhegmFrykJESnEsx"
    rotina = items[1]["rotina"]
    assert len(rotina[rotina_datetime_ida.strftime("%H:%M")]) == 1


def test_get_rotas_empresa_sem_rotas_cadastradas(rf, praxio_grupos, praxio_login, praxio_company):
    praxio_company.features = ["itinerario"]
    praxio_company.save()
    req = rf.get("/get_rotas_empresa")
    resp = views.get_rotas_empresa(req, praxio_grupos.ida.company_integracao.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert len(resp["items"]) == 0


def test_get_rotas_empresa_erro_empresa_nao_existe(rf):
    req = rf.get("/get_rotas_empresa")
    resp = views.get_rotas_empresa(req, -999)
    assert resp.status_code == 422
    resp = json.loads(resp.content)
    assert resp["mensagem"] == "Não foi possível encontrar empresa com o ID -999"


def test_cadastrar_trechos(rf):
    with mock.patch("rodoviaria.service.rotas_transbrasil_svc.cadastrar_trechos") as mock_cadastrar_trechos:
        mock_cadastrar_trechos.return_value = {"sucesso": "precos cadastrados"}
        trechos = [
            {
                "cidade_origem_id": 1,
                "local_origem_id": 1,
                "cidade_destino_id": 1,
                "local_destino_id": 1,
                "classe": "Leito",
                "max_split_value": 15.50,
            }
        ]
        params = CadastrarTrechosParams.parse_obj({"company_id": 1, "trechos": trechos})
        request = rf.post("/hibrido/cadastrar-trechos")
        resp = views.cadastrar_trechos(request, params)
    assert resp.status_code == 200
    mock_cadastrar_trechos.assert_called_once_with(1, [TrechoParams.parse_obj(trechos[0])])


def test_solicita_cancelamento(rf):
    with mock.patch("rodoviaria.service.solicita_cancelamento_svc.solicita_cancelamento") as mock_solicita_cancelamento:
        request = rf.get("/solicita_cancelamento", travel_id=10, trechoclasse_id=5)
        resp = views.solicita_cancelamento(request, 10, 5)
    assert resp.status_code == 200
    assert json.loads(resp.content) == {"sucesso": "Cancelamento(s) solicitado"}
    mock_solicita_cancelamento.assert_called_once_with(10, 5)


def test_solicita_cancelamento_por_passagens_ids(rf):
    with mock.patch(
        "rodoviaria.service.solicita_cancelamento_svc.solicita_cancelamento_por_passagens_ids"
    ) as mock_solicita_cancelamento_por_passagens_ids:
        params = SolicitaCancelamentoPorPassagemIdParams.parse_obj({"passagens_ids": [5, 6]})
        request = rf.post("/solicita-cancelamento-por-passagens-ids")
        resp = views.solicita_cancelamento_por_passagens_ids(request, params)
    assert resp.status_code == 200
    assert json.loads(resp.content) == {"sucesso": "Cancelamento(s) solicitado"}
    mock_solicita_cancelamento_por_passagens_ids.assert_called_once_with([5, 6])


def test_solicita_cancelamento_travels(rf):
    with mock.patch(
        "rodoviaria.service.solicita_cancelamento_svc.solicita_cancelamento_travels"
    ) as mock_solicita_cancelamento_travels:
        params = SolicitaCancelamentoTravelsParams.parse_obj({"travel_ids": [5]})
        request = rf.post("/solicita_cancelamento_travels")
        resp = views.solicita_cancelamento_travels(request, params)
    assert resp.status_code == 200
    assert json.loads(resp.content) == {"sucesso": "Cancelamento(s) solicitado"}
    mock_solicita_cancelamento_travels.assert_called_once_with([5])


def test_cancela_passagens_pendentes(rf):
    request = rf.post("/cancela_passagens_pendentes")
    with mock.patch(
        "rodoviaria.service.cancela_passagens_pendentes_svc.cancela_passagens_pendentes"
    ) as mock_cancela_passagens_pendentes:
        views.cancela_passagens_pendentes(request)
    mock_cancela_passagens_pendentes.assert_called_once()


def test_get_trechos_vendidos_erro(rf):
    request = rf.get("/get_trechos_vendidos")
    response = views.get_trechos_vendidos(request)
    assert response.status_code == 422
    assert json.loads(response.content) == {"error": "É necessário passar grupo_id ou rodoviaria_rota_id"}


def test_get_trechos_vendidos(rf):
    request = rf.get("/get_trechos_vendidos")
    grupo_internal_id = 20
    with mock.patch("rodoviaria.service.trechos_vendidos_svc.get_trechos_vendidos") as mock_get_trechos_vendidos:
        mock_get_trechos_vendidos.return_value = [
            {
                "origem": "São Paulo",
                "destino": "Rio de Janeiro",
                "origem_id": 1,
                "destino_id": 2,
                "distancia": "123.2",
                "classe_api": "LEITO CAMA",
                "classe_buser": "cama premium",
                "duracao": "2:00:00",
                "preco": "65.99",
            }
        ]
        response = views.get_trechos_vendidos(request, grupo_id=grupo_internal_id)
    mock_get_trechos_vendidos.assert_called_once_with(grupo_internal_id, None)
    assert json.loads(response.content) == mock_get_trechos_vendidos.return_value


def test_get_trechos_vendidos_by_rodoviaria_rota_id(rf):
    request = rf.get("/get_trechos_vendidos")
    rodoviaria_rota_id = 10
    with mock.patch("rodoviaria.service.trechos_vendidos_svc.get_trechos_vendidos") as mock_get_trechos_vendidos:
        mock_get_trechos_vendidos.return_value = [
            {
                "origem": "São Paulo",
                "destino": "Rio de Janeiro",
                "origem_id": 1,
                "destino_id": 2,
                "distancia": "123.2",
                "classe_api": "LEITO CAMA",
                "classe_buser": "cama premium",
                "duracao": "2:00:00",
                "preco": "65.99",
            }
        ]
        response = views.get_trechos_vendidos(request, rodoviaria_rota_id=rodoviaria_rota_id)
    mock_get_trechos_vendidos.assert_called_once_with(None, rodoviaria_rota_id)
    assert json.loads(response.content) == mock_get_trechos_vendidos.return_value


def test_atualiza_link_trechos_classe(rf):
    request = rf.post("/trechoclasse/atualiza")
    with mock.patch("rodoviaria.service.link_trechoclasse_svc.atualiza") as mock_atualiza_link_trecho_classe:
        mock_atualiza_link_trecho_classe.return_value = {
            "successes": [],
            "exceptions": [],
        }
        params = TrechoClasseGetAtualizaParams.parse_obj({"tag": "myTag"})
        views.atualiza_link_trecho_classe(request, params=params)
    mock_atualiza_link_trecho_classe.assert_called_once()


def test_atualiza_link_trechos_classe_sem_params(rf):
    request = rf.post("/trechoclasse/atualiza")
    params = TrechoClasseGetAtualizaParams.parse_obj({})
    resp = views.atualiza_link_trecho_classe(request, params=params)
    respj = json.loads(resp.content)
    assert resp.status_code == 400
    assert respj["error"] == "É necessário algum parâmetro tag, integracao, company_id, grupo_id ou trechoclasse_ids"


def test_remove_trechos_classe_tags(rf):
    request = rf.post("/trechoclasse/remove_tag")
    tag = "to_be_updated"
    with mock.patch("rodoviaria.service.link_trechoclasse_svc.remove_tag") as mock_remove_trechos_classe_tags:
        mock_remove_trechos_classe_tags.return_value = {
            "mensagem": f"A tag {tag} foi removida dos trechos_classe [12345]"
        }
        params = TrechoClasseAddRemoveTagParams.parse_obj({"tag": tag})
        views.remove_trechos_classe_tags(request, params=params)
    mock_remove_trechos_classe_tags.assert_called_once()


def test_add_trechos_classe_tags(rf):
    request = rf.post("/trechoclasse/add_tag")
    tag = "my_tag"
    with mock.patch("rodoviaria.service.link_trechoclasse_svc.add_tag") as mock_add_trechos_classe_tags:
        mock_add_trechos_classe_tags.return_value = {
            "mensagem": f"A tag {tag} foi adicionada aos trechos_classe [12345]"
        }
        params = TrechoClasseAddRemoveTagParams.parse_obj({"tag": tag})
        views.add_trechos_classe_tags(request, params=params)
    mock_add_trechos_classe_tags.assert_called_once()


def test_get_trechos_classe(rf):
    request = rf.get("/trechoclasse/get")
    tag = "my_tag"
    with mock.patch("rodoviaria.service.link_trechoclasse_svc.get") as mock_get_trechos_classe:
        mock_get_trechos_classe.return_value = [{"id": 1, "trechoclasse_internal_id": 2}]
        params = TrechoClasseGetAtualizaParams.parse_obj({"tag": tag})
        views.get_trechos_classe(request, params=params)
    mock_get_trechos_classe.assert_called_once()


def test_get_link_trechos_classe_sem_params(rf):
    request = rf.get("/trechoclasse/get")
    params = TrechoClasseGetAtualizaParams.parse_obj({})
    resp = views.get_trechos_classe(request, params=params)
    respj = json.loads(resp.content)
    assert resp.status_code == 400
    assert respj["error"] == "É necessário algum parâmetro tag, integracao, company_id, grupo_id ou trechoclasse_ids"


def cria_mock_checkpoints_itinerario_praxio(rota):
    cidade = baker.make("rodoviaria.Cidade", timezone="America/Sao_Paulo")
    local = baker.make("rodoviaria.LocalEmbarque", cidade=cidade)
    baker.make("rodoviaria.Checkpoint", idx=0, rota=rota, name="SAUIPE (BA)", local=local)
    baker.make("rodoviaria.Checkpoint", idx=1, rota=rota, name="FEIRA DE SANTANA (BA)")
    baker.make("rodoviaria.Checkpoint", idx=2, rota=rota, name="CRUZ DAS ALMAS (BA)")
    baker.make("rodoviaria.Checkpoint", idx=3, rota=rota, name="SANTO ANTONIO DE JESUS (BA)")


itinerario_praxio = [
    {
        "IDViagem": 1592,
        "DataPartida": "2021-06-05T02:30:00",
        "Plataforma": "",
        "Localidade": {
            "IDLocalidade": 176,
            "Descricao": "SAUIPE (BA)",
            "Sigla": "SAU",
            "Uf": "BA",
            "IdCidade": 0,
            "Codigo": 0,
        },
    },
    {
        "IDViagem": 1592,
        "DataPartida": "2021-06-05T04:30:00",
        "Plataforma": "D",
        "Localidade": {
            "IDLocalidade": 13,
            "Descricao": "FEIRA DE SANTANA (BA)",
            "Sigla": "FEI",
            "Uf": "BA",
            "IdCidade": 2910800,
            "Codigo": 4941,
        },
    },
    {
        "IDViagem": 1592,
        "DataPartida": "2021-06-05T05:50:00",
        "Plataforma": "",
        "Localidade": {
            "IDLocalidade": 193,
            "Descricao": "CRUZ DAS ALMAS (BA)",
            "Sigla": "CRU",
            "Uf": "BA",
            "IdCidade": 2909802,
            "Codigo": 4825,
        },
    },
    {
        "IDViagem": 1592,
        "DataPartida": "2021-06-05T06:40:00",
        "Plataforma": "",
        "Localidade": {
            "IDLocalidade": 177,
            "Descricao": "SANTO ANTONIO DE JESUS (BA)",
            "Sigla": "SAN",
            "Uf": "BA",
            "IdCidade": 2928703,
            "Codigo": 4819,
        },
    },
]


def test_passagens_sem_travel_correspondente(rf, mock_passagem_to_remove_totalbus):
    with mock.patch(
        "rodoviaria.service.cancela_passagens_pendentes_svc.passagens_sem_travel_correspondente"
    ) as mock_passagens_sem_travel_correspondente:
        request = rf.get("/passagens_sem_travel_correspondente")
        mock_passagens_sem_travel_correspondente.return_value = Passagem.objects.to_serialize(
            PassagemSerializer
        ).filter(travel_internal_id=mock_passagem_to_remove_totalbus.travel_internal_id)
        response = views.passagens_sem_travel_correspondente(request)
    mock_passagens_sem_travel_correspondente.assert_called_once()
    assert json.loads(response.content)["quantidade"] == 1
    assert json.loads(response.content)["passagens"][0]["passagem_id"] is not None


def test_cancelar_passagens_sem_travel_correspondente(rf):
    with mock.patch(
        "rodoviaria.service.cancela_passagens_pendentes_svc.cancelar_passagens_sem_travel_correspondente"
    ) as mock_cancelar_passagens_sem_travel_correspondente:
        request = rf.post("/cancela_passagens_sem_travel_correspondente")
        mock_cancelar_passagens_sem_travel_correspondente.return_value = {
            "sucessos": {
                "travel:4 - buseiro:12": {
                    "id": 14,
                    "localizador": "30120-123@12",
                    "bpe": None,
                    "monitriip": None,
                    "buseiro_id": 12,
                    "travel_id": 4,
                    "poltrona": 5,
                    "status": "cancelada",
                    "datetime_ida": "2021-07-25",
                }
            },
            "exceptions": {},
        }
        response = views.cancela_passagens_sem_travel_correspondente(request)
    assert json.loads(response.content) == mock_cancelar_passagens_sem_travel_correspondente.return_value
    mock_cancelar_passagens_sem_travel_correspondente.assert_called_once()


def test_fetch_trechos_vendidos_uma_rota(rf):
    grupo_id = 123
    with mock.patch(
        "rodoviaria.service.trechos_vendidos_svc.fetch_trechos_vendidos_uma_rota"
    ) as mock_fetch_trechos_vendidos_uma_rota:
        mock_fetch_trechos_vendidos_uma_rota.return_value = {"mensagem": "Busca Iniciada"}
        request = rf.get(f"/fetch_trechos_vendidos_uma_rota?grupo_id={grupo_id}")
        response = views.fetch_trechos_vendidos_uma_rota(request, grupo_id=grupo_id)
    mock_fetch_trechos_vendidos_uma_rota.assert_called_with(grupo_id, None)
    assert json.loads(response.content) == {"mensagem": "Busca Iniciada"}


def test_fetch_trechos_vendidos_no_input(rf):
    request = rf.get("/fetch_trechos_vendidos_uma_rota")
    response = views.fetch_trechos_vendidos_uma_rota(request)
    assert json.loads(response.content) == {"error": "É necessário passar grupo_id ou rodoviaria_rota_id"}


def test_fetch_trechos_vendidos_uma_rota_em_execucao(rf):
    rodoviaria_rota_id = 123
    with mock.patch(
        "rodoviaria.service.trechos_vendidos_svc.fetch_trechos_vendidos_uma_rota"
    ) as mock_fetch_trechos_vendidos_uma_rota:
        mock_fetch_trechos_vendidos_uma_rota.side_effect = TaskInExecutionException
        request = rf.get(f"/fetch_trechos_vendidos_uma_rota?rodoviaria_rota_id={rodoviaria_rota_id}")
        response = views.fetch_trechos_vendidos_uma_rota(request, rodoviaria_rota_id=rodoviaria_rota_id)
    mock_fetch_trechos_vendidos_uma_rota.assert_called_with(None, rodoviaria_rota_id)
    assert json.loads(response.content) == {"mensagem": TaskInExecutionException.message}
    assert response.status_code == 400


def test_company_transbrasil_create(rf, vexado_login):
    params = {
        "company_internal_id": 8817923,
        "name": "abacate",
    }
    request = rf.post(
        "v1/hibrido/create-company",
        data=json.dumps(params),
        content_type="application/json",
    )
    response = views.company_transbrasil_create(
        request,
        data=SimpleNamespace(company_internal_id=8817923, name="abacate", company_external_id=399),
    )
    assert response == {"message": "Company criada"}


def test_add_multiple_pax_na_lista(rf, mock_add_multiple_pax):
    with mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.__init__", return_value=None
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.add_multiple_pax_na_lista_passageiros_viagem"
    ) as mock_add_multiple_pax_na_lista:
        body = mock_add_multiple_pax
        mock_add_multiple_pax_na_lista.return_value = {}
        req = rf.post(
            "v1/add_multiple_pax_na_lista",
            body,
            content_type="application/json",
        )
        views.add_multiple_pax_na_lista(req)

    mock_add_multiple_pax_na_lista.assert_called_once_with(
        DefaultForm(trechoclasse_id=mock_add_multiple_pax["trechoclasse_id"], force_renew_link=True)
    )


def test_add_multiple_pax_na_lista_async(rf, mock_add_multiple_pax):
    with mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.__init__", return_value=None
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.add_multiple_pax_na_lista_passageiros_viagem"
    ) as mock_add_multiple_pax_na_lista:
        body = mock_add_multiple_pax
        mock_add_multiple_pax_na_lista.return_value = {}
        body["async_add"] = True
        req = rf.post(
            "v1/add_multiple_pax_na_lista",
            body,
            content_type="application/json",
        )
        views.add_multiple_pax_na_lista(req)

    mock_add_multiple_pax_na_lista.assert_called_once_with(
        DefaultForm(trechoclasse_id=mock_add_multiple_pax["trechoclasse_id"], force_renew_link=True)
    )


def test_add_pax_na_lista_batch(rf):
    with mock.patch(
        "rodoviaria.service.reserva_svc.add_pax_na_lista_grupo_task.delay"
    ) as mock_add_pax_na_lista_grupo_task:
        request_body = {
            "passageiros": [
                {
                    "id_destino": 514,
                    "id_origem": 638,
                    "passenger": {
                        "buseiro_id": 341475,
                        "cpf": "11975923774",
                        "name": "Nome Teste",
                        "phone": "21969200000",
                        "rg_number": "8885000",
                    },
                    "travel_id": 5409394,
                    "trechoclasse_id": 13984203,
                    "grupo_id": 111,
                    "valor_por_buseiro": 85.0,
                },
                {
                    "id_destino": 514,
                    "id_origem": 638,
                    "passenger": {
                        "buseiro_id": 341475,
                        "cpf": "11975923774",
                        "name": "Nome Teste",
                        "phone": "21969200000",
                        "rg_number": "8885000",
                    },
                    "travel_id": 5409394,
                    "trechoclasse_id": 13984203,
                    "grupo_id": 111,
                    "valor_por_buseiro": 85.0,
                },
            ]
        }
        req = rf.post(
            "v1/add_pax_na_lista_batch",
            request_body,
            content_type="application/json",
        )
        views.add_pax_na_lista_batch(req)
    mock_add_pax_na_lista_grupo_task.assert_called_once_with(
        [
            CheckPaxBatchForm.parse_obj(request_body).passageiros[0].json(),
            CheckPaxBatchForm.parse_obj(request_body).passageiros[1].json(),
        ]
    )


@pytest.fixture
def escala_motorista_json():
    return {
        "grupo_id": 1234,
        "motorista": {
            "user_id": 5678,
            "nome": "Fulano de Tal",
            "email": "<EMAIL>",
            "telefone": "(11) 91234-5678",
            "cpf": "111.111.111-11",
            "cnh": {
                "numero": "*********01",
                "validade": "2030-10-20",
                "categoria": "C",
                "orgao_emissor": "SSP",
                "uf": "SP",
            },
            "registro_antt": {"numero": "*********01234", "validade": "2040-12-22"},
        },
    }


def test_escala_motorista(escala_motorista_json):
    record = baker.make("rodoviaria.Grupo", grupo_internal_id=1234)
    with mock.patch("rodoviaria.service.motorista_svc.escala_motorista") as escala_motorista_mock:
        response = ninja_client.post("/v1/motoristas/escala", json=escala_motorista_json)
        assert response.status_code == 200
        motorista = MotoristaForm.parse_obj(escala_motorista_json["motorista"])
        escala_motorista_mock.assert_called_with(motorista, 1234)
    record.delete()


def test_fetch_mapas_veiculos_api(rf):
    request = rf.post("/onibus/fetch-mapas-veiculos-api")
    with mock.patch("rodoviaria.service.veiculos_svc.fetch_mapas_veiculos_api") as mock_fetch_mapas_veiculos_api:
        mock_fetch_mapas_veiculos_api.return_value = {"mapas_veiculos_count": 118}
        response = views.fetch_mapas_veiculos_api(request)
    assert json.loads(response.content) == {"mapas_veiculos_count": 118}
    mock_fetch_mapas_veiculos_api.assert_called_once()


def test_fetch_veiculos_api(rf):
    company_internal_id = 324
    request = rf.post("/onibus/fetch-veiculos-api")
    with mock.patch("rodoviaria.service.veiculos_svc.fetch_veiculos_api") as mock_fetch_veiculos_api:
        mock_fetch_veiculos_api.return_value = {"quantidade_veiculos_da_empresa": 11}
        response = views.fetch_veiculos_api(request, company_id=company_internal_id)
    assert json.loads(response.content) == {"quantidade_veiculos_da_empresa": 11}
    mock_fetch_veiculos_api.assert_called_once_with(company_internal_id)


def test_update_grupos_hibridos_criados(rf):
    company_internal_id = 860
    request = rf.post("/hibrido/update-grupos-criados")
    with mock.patch(
        "rodoviaria.service.update_grupos_hibridos_svc.update_grupos_hibridos_criados"
    ) as mock_update_grupos_hibridos_criados:
        mock_update_grupos_hibridos_criados.return_value = {"('2022-01-03 15:30', 50233, 'semi leito')": "ok"}
        response = views.update_grupos_hibridos_criados(request, company_id=company_internal_id)
    assert json.loads(response.content) == {"('2022-01-03 15:30', 50233, 'semi leito')": "ok"}
    mock_update_grupos_hibridos_criados.assert_called_once_with(company_internal_id)


def test_fill_grupo_classe_external_id(rf):
    request = rf.post("/hibrido/fill-grupo-classe-external-id")
    with mock.patch(
        "rodoviaria.service.update_grupos_hibridos_svc.fill_grupo_classe_external_id"
    ) as mock_fill_grupo_classe_external_id:
        mock_fill_grupo_classe_external_id.return_value = {"grupos_classes_atualizados": 2}
        response = views.fill_grupo_classe_external_id(request)
    assert json.loads(response.content) == {"grupos_classes_atualizados": 2}
    mock_fill_grupo_classe_external_id.assert_called_once_with()


def test_fetch_grupos_criados_anteriormente(rf):
    company_internal_id = 860
    request = rf.post("/hibrido/fetch-grupos-criados-anteriormente")
    with mock.patch(
        "rodoviaria.service.update_grupos_hibridos_svc.fetch_grupos_criados_anteriormente"
    ) as mock_fetch_grupos_criados_anteriormente:
        mock_fetch_grupos_criados_anteriormente.return_value = {"grupos_classes_criados": 2}
        response = views.fetch_grupos_criados_anteriormente(request, company_id=company_internal_id)
    assert json.loads(response.content) == {"grupos_classes_criados": 2}
    mock_fetch_grupos_criados_anteriormente.assert_called_once_with(company_internal_id)


def test_cadastrar_grupos_params(rf):
    with mock.patch(
        "rodoviaria.service.cadastrar_grupos_hibridos_svc.cadastrar_grupos_params"
    ) as mock_cadastrar_grupos_params:
        mock_cadastrar_grupos_params.return_value = {"rotas": [], "last_option": None}
        request = rf.get("/hibrido/cadastrar-grupos-params")
        resp = views.cadastrar_grupos_params(request, company_id=123, rota_internal_id=483)
        assert resp.status_code == 200
        mock_cadastrar_grupos_params.assert_called_once_with(123, 483)


@pytest.fixture
def grupos_para_cadastrar():
    yield [
        {
            "data_partida": "2022-04-01",
            "hora_saida": "12:00",
            "veiculo_internal_id": 7,
            "veiculo_placa": "KDA1294",
            "grupo_id": 140,
            "classes": [
                {"grupo_classe_id": 155, "tipo": "leito individual", "capacidade": 24},
                {"grupo_classe_id": 156, "tipo": "leito cama", "capacidade": 24},
            ],
        }
    ]


def test_cadastrar_grupos(rf, grupos_para_cadastrar):
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=594,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    with mock.patch.object(CadastrarGrupoSVC, "cadastrar_grupos") as mock_cadastrar_grupos:
        mock_cadastrar_grupos.return_value = {"sucesso": "Grupos cadastrados"}
        params = CadastrarGruposParams.parse_obj(
            {
                "company_id": company.company_internal_id,
                "rota_internal_id": 423,
                "grupos": grupos_para_cadastrar,
            }
        )
        request = rf.post("/cadastrar-grupos")
        resp = views.cadastrar_grupos(request, params)
        assert resp.status_code == 200
        mock_cadastrar_grupos.assert_called_once_with(
            [GrupoParams.parse_obj(grupos_para_cadastrar[0])], rota_internal_id=423
        )


def test_cadastrar_grupos_error(rf, grupos_para_cadastrar):
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=594,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    with mock.patch.object(CadastrarGrupoSVC, "cadastrar_grupos") as mock_cadastrar_grupos:
        mock_cadastrar_grupos.side_effect = veiculos_svc.GetOrCreateVeiculosError(
            "Nenhum mapa de veiculos encontrado para este veiculo"
        )
        params = CadastrarGruposParams.parse_obj(
            {
                "company_id": company.company_internal_id,
                "rota_external_id": 54,
                "rota_internal_id": 423,
                "grupos": grupos_para_cadastrar,
            }
        )
        request = rf.post("/cadastrar-grupos")
        resp = views.cadastrar_grupos(request, params)
    assert resp.status_code == 422
    assert json.loads(resp.content) == {"error": "Nenhum mapa de veiculos encontrado para este veiculo"}


def test_cadastrar_grupos_error_rota_nao_cadastrada(rf, grupos_para_cadastrar):
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=594,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    with mock.patch.object(CadastrarGrupoSVC, "cadastrar_grupos") as mock_cadastrar_grupos:
        mock_cadastrar_grupos.side_effect = cadastrar_grupos_hibridos_svc.RotaNaoCadastradaException(482, 594)
        params = CadastrarGruposParams.parse_obj(
            {
                "company_id": company.company_internal_id,
                "rota_external_id": 54,
                "rota_internal_id": 423,
                "grupos": grupos_para_cadastrar,
            }
        )
        request = rf.post("/cadastrar-grupos")
        resp = views.cadastrar_grupos(request, params)
    assert resp.status_code == 412
    assert json.loads(resp.content) == {
        "error": "Rota com id 482 não cadastrada no banco rodoviaria para empresa 594",
        "error_type": "rota_nao_cadastrada",
        "company_id": 594,
        "rota_id": 482,
    }


def test_cancelar_grupos_classe(rf):
    with mock.patch(
        "rodoviaria.service.cancelar_grupos_hibridos_svc.cancelar_grupos_classe"
    ) as mock_cancelar_grupos_classe:
        mock_cancelar_grupos_classe.return_value = [], []
        params = CancelarGruposClasseParams.parse_obj({"company_id": 1, "grupos_classe_ids": [432, 5332, 423]})
        request = rf.post("/hibrido/cancelar-grupos-classe")
        resp = views.cancelar_grupos_classe(request, params)
        assert resp.status_code == 200
        mock_cancelar_grupos_classe.assert_called_once_with(1, [432, 5332, 423])


def test_fechar_grupos_classe(rf):
    with mock.patch(
        "rodoviaria.service.cancelar_grupos_hibridos_svc.fechar_grupos_classe"
    ) as mock_fechar_grupos_classe:
        mock_fechar_grupos_classe.return_value = [], []
        params = CancelarGruposClasseParams.parse_obj({"company_id": 1, "grupos_classe_ids": [432, 5332, 423]})
        request = rf.post("/hibrido/fechar-grupos-classe")
        resp = views.fechar_grupos_classe(request, params)
        assert resp.status_code == 200
        mock_fechar_grupos_classe.assert_called_once_with(1, [432, 5332, 423])


def test_create_or_link_onibus(rf):
    json_body = {
        "company_id": 304,
        "veiculo_id": 4912,
        "placa": "ABC0D12",
        "classes": [
            {"tipo": "leito", "capacidade": 15},
            {"tipo": "cemi leito", "capacidade": 42},
        ],
    }
    veiculos = [baker.make("rodoviaria.Veiculo", id_external=3132)]
    with mock.patch("rodoviaria.service.veiculos_svc.get_link_or_create_veiculo") as mock_get_link_or_create_veiculo:
        mock_get_link_or_create_veiculo.return_value = veiculos
        response = ninja_client.post("/v1/onibus/create-or-link", json=json_body)
    assert response.status_code == 200
    assert json.loads(response.content) == [v.to_dict_json() for v in veiculos]
    mock_get_link_or_create_veiculo.assert_called_once_with(CadastrarVeiculoParams.parse_obj(json_body))


def test_create_or_link_onibus_error(rf):
    json_body = {
        "company_id": 304,
        "veiculo_id": 4912,
        "placa": "ABC0D12",
        "classes": [
            {"tipo": "leito", "capacidade": 15},
            {"tipo": "cemi leito", "capacidade": 42},
        ],
    }
    with mock.patch("rodoviaria.service.veiculos_svc.get_link_or_create_veiculo") as mock_get_link_or_create_veiculo:
        mock_get_link_or_create_veiculo.side_effect = veiculos_svc.GetOrCreateVeiculosError(
            "Mapa de veiculo nao encontrado"
        )
        response = ninja_client.post("/v1/onibus/create-or-link", json=json_body)
    assert response.status_code == 422
    assert json.loads(response.content) == {"error": "Mapa de veiculo nao encontrado"}


def test_create_or_link_onibus_sem_company_id(rf):
    json_body = {
        "veiculo_id": 4912,
        "placa": "ABC0D12",
        "classes": [
            {"tipo": "leito", "capacidade": 15},
            {"tipo": "cemi leito", "capacidade": 42},
        ],
    }
    response = ninja_client.post("/v1/onibus/create-or-link", json=json_body)
    assert response.status_code == 400
    assert json.loads(response.content) == {"error": "Necessario passar o company_id"}


def test_pos_salvar_rota(rf, rota_totalbus, totalbus_login):
    tv1 = baker.make("rodoviaria.TrechoVendido", rota_id=rota_totalbus.id, origem_id=1, destino_id=2)
    tv2 = baker.make("rodoviaria.TrechoVendido", rota_id=rota_totalbus.id, origem_id=3, destino_id=4)
    json_body = {
        "rota": {"id_internal": 4912, "rodoviaria_rota_id": rota_totalbus.id},
        "trechos_vendidos": {tv1.id: 10, tv2.id: 11},
    }

    response = ninja_client.post("/v1/auto_integra_operacao/pos_salvar_rota", json=json_body)
    tv1.refresh_from_db()
    tv2.refresh_from_db()
    assert tv1.id_internal == 10
    assert tv2.id_internal == 11
    rota_totalbus.refresh_from_db()
    assert rota_totalbus.id_internal == 4912
    assert response.status_code == 200


def test_classes_e_precos_rota(rf, rota_mock):
    rota_mock.id_internal = 9999
    rota_mock.save()

    baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota_mock,
        preco=12.99,
        classe="leito cama",
        capacidade_classe=9,
        id_internal=1,
    )
    baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota_mock,
        preco=12.99,
        classe="executivo",
        capacidade_classe=12,
        id_internal=1,
    )
    baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota_mock,
        preco=12.99,
        classe="executivo",
        capacidade_classe=12,
        id_internal=2,
    )
    baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota_mock,
        preco=12.99,
        classe="leito cama",
        capacidade_classe=9,
        id_internal=3,
    )
    json_assert = {
        "classes": [
            {"max_capacity": 12, "tipo_assento": "executivo"},
            {"max_capacity": 9, "tipo_assento": "leito cama"},
        ],
        "trechos_classes": {
            "1": {"executivo": "12.99", "leito cama": "12.99"},
            "2": {"executivo": "12.99"},
            "3": {"leito cama": "12.99"},
        },
    }

    response = ninja_client.get("/v1/classes_e_precos_rota?rota_id=9999")
    assert response.status_code == 200
    assert json.loads(response.content) == json_assert


def test_classes_e_precos_rota_trecho_sem_capacidade_classe(rf, rota_mock):
    rota_mock.id_internal = 9999
    rota_mock.save()

    baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota_mock,
        preco=12.99,
        classe="leito cama",
        id_internal=1,
    )
    json_assert = {
        "classes": [
            {"max_capacity": 0, "tipo_assento": "leito cama"},
        ],
        "trechos_classes": {"1": {"leito cama": "12.99"}},
    }

    response = ninja_client.get("/v1/classes_e_precos_rota?rota_id=9999")
    assert response.status_code == 200
    assert json.loads(response.content) == json_assert


def test_classes_e_precos_rota_sem_rota(rf):
    json_assert = {"classes": [], "trechos_classes": {}}

    response = ninja_client.get("/v1/classes_e_precos_rota?rota_id=9999")
    assert response.status_code == 200
    assert json.loads(response.content) == json_assert


def test_classes_e_precos_rota_sem_trechos(rf, rota_mock):
    rota_mock.id_internal = 9999
    rota_mock.save()

    json_assert = {"classes": [], "trechos_classes": {}}

    response = ninja_client.get("/v1/classes_e_precos_rota?rota_id=9999")
    assert response.status_code == 200
    assert json.loads(response.content) == json_assert


def test_update_grupos_classe_rodoviaria(rf):
    json_body = {"updates": [{"new_id": 10020, "old_id": 8594}, {"new_id": 9993, "old_id": 2109}]}
    with mock.patch(
        "rodoviaria.service.status_integracao_svc.update_grupos_classe_link"
    ) as mock_update_grupos_classe_link:
        response = ninja_client.post("/v1/update-grupo-classe-internal-id", json=json_body)
    assert response.status_code == 200
    assert json.loads(response.content) == {"message": "Link de Grupos Classe atualizados"}
    mock_update_grupos_classe_link.assert_called_once_with(UpdateGruposClasseLink.parse_obj(json_body))


def test_fetch_rotina_no_local_checkpoint_error(rf):
    with mock.patch("rodoviaria.service.rotina_svc.fetch_rotina") as mock_rotina:
        mock_rotina.side_effect = RodoviariaNoLocalCheckpointException
        rodoviaria_rota_id = 1
        resp = ninja_client.get(f"/v1/fetch_rotina?rodoviaria_rota_id={rodoviaria_rota_id}")
    assert resp.status_code == 400
    respj = json.loads(resp.content)
    assert respj["error"] == RodoviariaNoLocalCheckpointException.message


def test_fetch_rotina_no_timezone_checkpoint_error(rf):
    with mock.patch("rodoviaria.service.rotina_svc.fetch_rotina") as mock_rotina:
        mock_rotina.side_effect = RodoviariaNoTimezoneException
        rodoviaria_rota_id = 1
        resp = ninja_client.get(f"/v1/fetch_rotina?rodoviaria_rota_id={rodoviaria_rota_id}")
    assert resp.status_code == 400
    respj = json.loads(resp.content)
    assert respj["error"] == RodoviariaNoTimezoneException.message


def test_fetch_rotina_first_day_wrong_format(rf):
    params = {"rodoviaria_rota_id": 1, "first_day": "2022-14-01", "async_fetch": True}
    req = rf.get("/fetch_rotina")
    resp = views.fetch_rotina(req, **params)
    assert resp.status_code == 400


def test_fetch_rotina_missing_params_error(rf):
    resp = ninja_client.get("/v1/fetch_rotina")
    assert resp.status_code == 400
    respj = json.loads(resp.content)
    assert respj["error"] == "É necessário passar rota_id ou rodoviaria_rota_id como parâmetro"


def test_fetch_rotina_rota_not_found_error(rf):
    with mock.patch("rodoviaria.service.rotina_svc.fetch_rotina") as mock_rotina:
        mock_rotina.side_effect = RodoviariaRotaNotFoundException
        rodoviaria_rota_id = 1
        resp = ninja_client.get(f"/v1/fetch_rotina?rodoviaria_rota_id={rodoviaria_rota_id}")
    assert resp.status_code == 404
    respj = json.loads(resp.content)
    assert respj["error"] == RodoviariaRotaNotFoundException.message


def test_fetch_rotina_by_internal_id(rf):
    with mock.patch("rodoviaria.service.rotina_svc.fetch_rotina_by_internal_id") as mock_fetch_rotina:
        mock_fetch_rotina.return_value = {"rotina": {}}
        rota_id = 1
        resp = ninja_client.get(f"/v1/fetch_rotina?rota_id={rota_id}")
        assert resp.status_code == 200
        respj = json.loads(resp.content)
        assert respj["rotina"] == {}


def test_fetch_rotina_async_by_internal_id(rf, rota_totalbus):
    with mock.patch("rodoviaria.service.rotina_svc._fetch_rotina_async") as mock_fetch_rotina:
        mock_fetch_rotina.return_value = {"status": "PENDING"}
        params = {"rota_id": rota_totalbus.id_internal, "async_fetch": True}
        req = rf.get("/fetch_rotina")
        resp = views.fetch_rotina(req, **params)
        assert resp.status_code == 200
        respj = json.loads(resp.content)
        assert respj["status"] == "PENDING"


def test_fetch_rotina(rf):
    with mock.patch("rodoviaria.service.rotina_svc.fetch_rotina") as mock_fetch_rotina:
        mock_fetch_rotina.return_value = {"rotina": {}}
        rodoviaria_rota_id = 1
        resp = ninja_client.get(f"/v1/fetch_rotina?rodoviaria_rota_id={rodoviaria_rota_id}")
        assert resp.status_code == 200
        respj = json.loads(resp.content)
        assert respj["rotina"] == {}


def test_fetch_rotina_async(rf, rota_totalbus):
    with mock.patch("rodoviaria.service.rotina_svc._fetch_rotina_async") as mock_fetch_rotina:
        mock_fetch_rotina.return_value = {"status": "PENDING"}
        params = {"rodoviaria_rota_id": rota_totalbus.id, "async_fetch": True}
        req = rf.get("/fetch_rotina")
        resp = views.fetch_rotina(req, **params)
        assert resp.status_code == 200
        respj = json.loads(resp.content)
        assert respj["status"] == "PENDING"


def test_get_rotina_no_local_checkpoint_error(rf):
    with mock.patch("rodoviaria.service.rotina_svc.get_rotina") as mock_rotina:
        mock_rotina.side_effect = RodoviariaNoLocalCheckpointException
        rodoviaria_rota_id = 1
        resp = ninja_client.get(f"/v1/get_rotina?rodoviaria_rota_id={rodoviaria_rota_id}")
    assert resp.status_code == 400
    respj = json.loads(resp.content)
    assert respj["error"] == RodoviariaNoLocalCheckpointException.message


def test_get_rotina_no_timezone_checkpoint_error(rf):
    with mock.patch("rodoviaria.service.rotina_svc.get_rotina") as mock_rotina:
        mock_rotina.side_effect = RodoviariaNoTimezoneException
        rodoviaria_rota_id = 1
        resp = ninja_client.get(f"/v1/get_rotina?rodoviaria_rota_id={rodoviaria_rota_id}")
    assert resp.status_code == 400
    respj = json.loads(resp.content)
    assert respj["error"] == RodoviariaNoTimezoneException.message


def test_get_rotina_missing_params_error(rf):
    resp = ninja_client.get("/v1/get_rotina")
    assert resp.status_code == 400
    respj = json.loads(resp.content)
    assert respj["error"] == "É necessário passar rota_id ou rodoviaria_rota_id como parâmetro"


def test_get_rotina_rota_not_found_error(rf):
    with mock.patch("rodoviaria.service.rotina_svc.get_rotina") as mock_rotina:
        mock_rotina.side_effect = RodoviariaRotaNotFoundException
        rodoviaria_rota_id = 1
        resp = ninja_client.get(f"/v1/get_rotina?rodoviaria_rota_id={rodoviaria_rota_id}")
    assert resp.status_code == 404
    respj = json.loads(resp.content)
    assert respj["error"] == RodoviariaRotaNotFoundException.message


def test_get_rotina_by_internal_id(rf):
    with mock.patch("rodoviaria.service.rotina_svc.get_rotina_by_internal_id") as mock_get_rotina:
        mock_get_rotina.return_value = {"rotina": {}}
        rota_id = 1
        resp = ninja_client.get(f"/v1/get_rotina?rota_id={rota_id}")
        assert resp.status_code == 200
        respj = json.loads(resp.content)
        assert respj["rotina"] == {}


def test_get_rotina(rf):
    with mock.patch("rodoviaria.service.rotina_svc.get_rotina") as mock_get_rotina:
        mock_get_rotina.return_value = {"rotina": {}}
        rodoviaria_rota_id = 1
        resp = ninja_client.get(f"/v1/get_rotina?rodoviaria_rota_id={rodoviaria_rota_id}")
        assert resp.status_code == 200
        respj = json.loads(resp.content)
        assert respj["rotina"] == {}


def test_escalar_veiculo(rf, alterar_grupo_request):
    with mock.patch("rodoviaria.service.alterar_grupo_svc.alterar_grupo") as mock_alterar_grupo:
        response = ninja_client.post("/v1/hibrido/onibus/escalar", json=alterar_grupo_request)
    assert response.status_code == 200
    mock_alterar_grupo.assert_called_once_with(AlterarGrupoParams.parse_obj(alterar_grupo_request))
    assert json.loads(response.content) == {}


def test_alterar_grupo(rf, alterar_grupo_request):
    with mock.patch("rodoviaria.service.alterar_grupo_svc.alterar_grupo") as mock_alterar_grupo:
        response = ninja_client.post("/v1/hibrido/alterar-grupo", json=alterar_grupo_request)
    assert response.status_code == 200
    mock_alterar_grupo.assert_called_once_with(AlterarGrupoParams.parse_obj(alterar_grupo_request))
    assert json.loads(response.content) == {}


def test_cadastrar_ou_editar_rota_view(rf):
    with mock.patch(
        "rodoviaria.service.rotas_transbrasil_svc.cadastrar_ou_editar_rota",
        return_value={},
    ) as mock_cadastrar_ou_editar_rota:
        request_body = {
            "company_id": 1,
            "cidade_destino_id": 1,
            "cidade_origem_id": 2,
            "prefixo": "Prefixo",
            "grupo_ids": [239, 323],
            "checkpoints": [
                {
                    "cidade_destino_id": 1,
                    "duracao": "01:30",
                    "ponto_embarque": "Posto da Gruta",
                    "km": 123,
                }
            ],
        }
        req = rf.post("/hibrido/cadastrar-ou-editar-rota", request_body)
        response = views.cadastrar_ou_editar_rota(req, request_body)
        assert response.status_code == 200
    mock_cadastrar_ou_editar_rota.assert_called_once_with(request_body)


def test_cadastrar_rota(rf):
    with mock.patch("rodoviaria.service.rotas_transbrasil_svc.cadastrar_rota", return_value={}) as mock_cadastrar_rota:
        request_body = {
            "company_id": 1,
            "cidade_destino_id": 1,
            "cidade_origem_id": 2,
            "prefixo": "Prefixo",
            "checkpoints": [
                {
                    "cidade_destino_id": 1,
                    "duracao": "01:30",
                    "ponto_embarque": "Posto da Gruta",
                    "km": 123,
                }
            ],
        }
        req = rf.post("/hibrido/cadastrar-rota", request_body)
        response = views.cadastrar_rota(req, request_body)
        assert response.status_code == 200
    mock_cadastrar_rota.assert_called_once_with(request_body)


def test_editar_rota(rf):
    with mock.patch("rodoviaria.service.rotas_transbrasil_svc.editar_rota", return_value={}) as mock_editar_rota:
        request_body = {
            "company_id": 1,
            "cidade_destino_id": 1,
            "cidade_origem_id": 2,
            "prefixo": "Prefixo",
            "grupo_ids": [],
        }
        req = rf.post("/hibrido/editar-rota", request_body)
        response = views.editar_rota(req, request_body)
        assert response.status_code == 200
    mock_editar_rota.assert_called_once_with(request_body)


def test_sincronizar_rota(rf):
    with mock.patch(
        "rodoviaria.service.rotas_transbrasil_svc.sincronizar_rota", return_value={}
    ) as mock_sincronizar_rota:
        request_body = {
            "company_id": 1,
            "cidade_destino_id": 1,
            "cidade_origem_id": 2,
            "prefixo": "Prefixo",
            "checkpoints": [
                {
                    "cidade_destino_id": 1,
                    "duracao": "01:30",
                    "ponto_embarque": "Posto da Gruta",
                    "km": 123,
                }
            ],
        }
        req = rf.post("/hibrido/sincronizar-rota", request_body)
        response = views.sincronizar_rota(req, request_body)
        assert response.status_code == 200
    mock_sincronizar_rota.assert_called_once_with(request_body)


def test_criar_itinerario(rf):
    with mock.patch(
        "rodoviaria.service.rotas_transbrasil_svc.criar_itinerario", return_value={}
    ) as mock_criar_itinerario:
        request_body = {
            "company_id": 1,
            "id_rota_external": 1,
            "checkpoints": [
                {
                    "cidade_destino_id": 1,
                    "duracao": "01:30",
                    "ponto_embarque": "Posto da Gruta",
                    "km": 123,
                }
            ],
        }
        req = rf.post("/hibrido/criar-itinerario", request_body)
        response = views.criar_itinerario(req, request_body)
        assert response.status_code == 200
    mock_criar_itinerario.assert_called_once_with(request_body)


def test_atualizar_locais_embarque(rf):
    with mock.patch(
        "rodoviaria.service.rotas_transbrasil_svc.atualizar_locais_embarque",
        return_value={},
    ) as mock_atualizar_locais:
        request_body = {
            "company_id": 1,
            "id_rota_external": 1,
            "checkpoints": [
                {
                    "cidade_destino_id": 1,
                    "duracao": "1:30",
                    "tempo_embarque": "0:20",
                    "tempo_total": "1:50",
                    "ponto_embarque": "Posto da Gruta",
                    "km": 1,
                    "id_rota_external": 1,
                    "local_embarque_id": 1,
                }
            ],
        }
        req = rf.post("/hibrido/atualizar-locais-embarque", request_body)
        response = views.atualizar_locais_embarque(req, request_body)
        assert response.status_code == 200
    mock_atualizar_locais.assert_called_once_with(request_body)


def test_atualizar_locais_embarque_batch(rf):
    with mock.patch(
        "rodoviaria.service.rotas_transbrasil_svc.atualizar_locais_embarque_batch",
        return_value={},
    ) as mock_atualizar_locais_batch:
        request_body = {
            "id_rota_external": 1,
            "checkpoints": [
                {
                    "cidade_destino_id": 1,
                    "duracao": "1:30",
                    "tempo_embarque": "0:20",
                    "tempo_total": "1:50",
                    "ponto_embarque": "Posto da Gruta",
                    "km": 1,
                    "local_embarque_id": 1,
                }
            ],
        }
        req = rf.post("/hibrido/atualizar-locais-embarque-batch", request_body)
        response = views.atualizar_locais_embarque_batch(req, request_body)
        assert response.status_code == 200
    mock_atualizar_locais_batch.assert_called_once_with(request_body)


def test_verificar_grupos_hibridos_cancelados(rf):
    with mock.patch(
        "rodoviaria.service.verificar_grupos_cancelados_svc.check_passagens_canceladas_hibrido"
    ) as mock_check_grupos_cancelados:
        mock_check_grupos_cancelados.return_value = {"grupos_com_passagens_pendentes": [12432]}
        req = rf.post("/hibrido/verificar-grupos-cancelados")
        views.verificar_grupos_hibridos_cancelados(req)
    mock_check_grupos_cancelados.assert_called_once_with(limit=10)


def test_fetch_rota_external(rf):
    with mock.patch("rodoviaria.service.marketplace_fetch_rotas_svc.fetch_rota_external") as mock_fetch_rota_external:
        mock_fetch_rota_external.return_value = {}
        params = {"company_id": 1, "id_external": 2, "data": "2022-04-14"}
        req = rf.get("/fetch_rota_external")
        resp = views.fetch_rota_external(req, **params)
        assert resp.status_code == 200
        respj = json.loads(resp.content)
        assert respj == {}


def test_fetch_rota_external_rodoviaria_exception(rf):
    with mock.patch("rodoviaria.service.marketplace_fetch_rotas_svc.fetch_rota_external") as mock_fetch_rota_external:
        mock_fetch_rota_external.side_effect = RodoviariaException("Erro")
        params = {"company_id": 1, "id_external": 2}
        req = rf.get("/fetch_rota_external")
        resp = views.fetch_rota_external(req, **params)
        assert resp.status_code == 400
        respj = json.loads(resp.content)
        assert respj["error"] == "Erro"


def test_fetch_rota_external_data_error(rf):
    params = {"company_id": 1, "id_external": 2, "data": "2022-04-"}
    req = rf.get("/fetch_rota_external")
    resp = views.fetch_rota_external(req, **params)
    assert resp.status_code == 400
    respj = json.loads(resp.content)
    assert respj["error"]


def test_fetch_rotinas_empresa(rf):
    with mock.patch("rodoviaria.service.rotina_svc.fetch_rotinas_empresa") as mock_rotina:
        mock_rotina.return_value = {}
        company_id = 1
        resp = ninja_client.get(f"/v1/fetch_rotinas_empresa?company_id={company_id}")
    assert json.loads(resp.content) == mock_rotina.return_value


def test_create_totalbus_company(rf):
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    req_body = {
        "name": "Busao express",
        "company_internal_id": 898,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "integracao": "totalbus",
    }

    with mock.patch("rodoviaria.service.company_svc.create_empresa") as mock_create_empresa:
        return_value = {"success": True}
        mock_create_empresa.return_value = return_value
        request = rf.post("/create_company", req_body, content_type="application/json")
        response = views.create_company(request)
        data = CreateRodoviariaCompanyForm.parse_obj(req_body)
        mock_create_empresa.assert_called_once_with(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=None,
        )

    assert response.status_code == 200
    assert json.loads(response.content) == return_value


def test_create_company_fetch_operacao_error_login_invalido_401(rf):
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    req_body = {
        "name": "Busao express",
        "company_internal_id": 898,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "integracao": "Totalbus",
    }

    with mock.patch(
        "rodoviaria.service.company_svc.create_empresa",
        side_effect=RodoviariaUnauthorizedError,
    ):
        request = rf.post("/create_company", req_body, content_type="application/json")
        response = views.create_company(request)

    assert response.status_code == 401
    assert json.loads(response.content) == {"error": RodoviariaUnauthorizedError.message}


def test_create_company_fetch_operacao_error_buscar_servicos(rf):
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    req_body = {
        "name": "Busao express",
        "company_internal_id": 898,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "integracao": "totalbus",
    }

    with mock.patch("rodoviaria.service.company_svc.create_empresa", side_effect=RodoviariaException):
        request = rf.post("/create_company", req_body, content_type="application/json")
        response = views.create_company(request)

    assert response.status_code == 400
    assert json.loads(response.content) == {"error": ""}


def test_create_praxio_company(rf):
    praxio_login = {
        "name": "admin",
        "password": "senha",
        "cliente": "CLIENT_VR",
        "desconto_manual": True,
        "max_percentual_divergencia": 304,
    }
    req_body = {
        "name": "Busao express",
        "company_internal_id": 898,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": praxio_login,
        "integracao": "praxio",
    }
    expected_resp = {"success": True}

    with mock.patch("rodoviaria.service.company_svc.create_empresa", return_value=expected_resp) as mock_create_empresa:
        request = rf.post("/create_company", req_body, content_type="application/json")
        response = views.create_company(request)
        data = CreateRodoviariaCompanyForm.parse_obj(req_body)
        mock_create_empresa.assert_called_once_with(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )

    assert response.status_code == 200
    assert json.loads(response.content) == expected_resp


def test_create_vexado_company(rf):
    vexado_login = {
        "user": "admin",
        "password": "senha",
        "company_external_id": 18,
    }
    req_body = {
        "name": "Busao express",
        "company_internal_id": 898,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": vexado_login,
        "integracao": "vexado",
    }
    expected_resp = {"success": True}

    with mock.patch("rodoviaria.service.company_svc.create_empresa", return_value=expected_resp) as mock_create_empresa:
        request = rf.post("/create_company", req_body, content_type="application/json")
        response = views.create_company(request)
        data = CreateRodoviariaCompanyForm.parse_obj(req_body)
        mock_create_empresa.assert_called_once_with(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=None,
        )

    assert response.status_code == 200
    assert json.loads(response.content) == expected_resp


def test_create_ti_sistemas_company(rf):
    ti_sistemas_login = {"auth_key": "abc", "company_external_id": 271}
    req_body = {
        "name": "Busao express",
        "company_internal_id": 181,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": ti_sistemas_login,
        "integracao": "ti_sistemas",
    }
    expected_resp = {"success": True}

    with mock.patch("rodoviaria.service.company_svc.create_empresa", return_value=expected_resp) as mock_create_empresa:
        request = rf.post("/create_company", req_body, content_type="application/json")
        response = views.create_company(request)
        data = CreateRodoviariaCompanyForm.parse_obj(req_body)
        mock_create_empresa.assert_called_once_with(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=None,
        )

    assert response.status_code == 200
    assert json.loads(response.content) == expected_resp


def test_get_auth_key_ti_sistemas(rf, ti_sistemas_login):
    request = rf.post("/v1/get_auth_key_ti_sistemas")

    response = views.get_auth_key_ti_sistemas(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"auth_key": ti_sistemas_login.auth_key}


def test_create_guichepass_company(rf):
    guichepass_login = {
        "url_base": "http://api-gravataense.buson.com.br",
        "username": "admin",
        "password": "senha",
        "client_id": "WEB_SALE",
        "company_external_id": 2,
    }
    req_body = {
        "name": "Busao express",
        "company_internal_id": 898,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": guichepass_login,
        "integracao": "guichepass",
    }
    expected_resp = {"success": True}

    with mock.patch("rodoviaria.service.company_svc.create_empresa", return_value=expected_resp) as mock_create_empresa:
        request = rf.post("/create_company", req_body, content_type="application/json")
        response = views.create_company(request)
        data = CreateRodoviariaCompanyForm.parse_obj(req_body)
        mock_create_empresa.assert_called_once_with(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=None,
        )

    assert response.status_code == 200
    assert json.loads(response.content) == expected_resp


def test_get_company_login(rf):
    integracao = "totalbus"
    company_id = 13
    modelo_venda = "marketplace"

    with mock.patch("rodoviaria.service.company_svc.get_login") as mock_get_login:
        expected_result = {
            "user": "my_user",
            "password": "my_password",
            "tenant_id": "db99a1e7-de88-41df-a5fe-6fe88275a0a8",
            "id_forma_pagamento": 7,
            "forma_pagamento": "BUSER",
            "validar_multa": True,
        }
        mock_get_login.return_value = expected_result
        request = rf.get("/v1/company_login")
        response = views.get_company_login(
            request,
            integracao=integracao,
            company_id=company_id,
            modelo_venda=modelo_venda,
        )

    assert response.status_code == 200
    assert json.loads(response.content) == {"login": expected_result}


def test_get_company_login_exceptions(rf):
    integracao = "totalbus"
    company_id = 13
    modelo_venda = "marketplace"

    with mock.patch("rodoviaria.service.company_svc.get_login") as mock.get_login:
        mock.get_login.side_effect = RodoviariaLoginNotFoundException(
            integracao=integracao, company_pk=company_id, modelo_venda=modelo_venda
        )
        request = rf.get("/v1/company_login")
        response = views.get_company_login(
            request,
            integracao=integracao,
            company_id=company_id,
            modelo_venda=modelo_venda,
        )
        assert response.status_code == 400
        assert "error" in json.loads(response.content)

        mock.get_login.side_effect = RodoviariaIntegracaoNotFoundException(integracao=integracao)
        response = views.get_company_login(
            request,
            integracao=integracao,
            company_id=company_id,
            modelo_venda=modelo_venda,
        )
        assert response.status_code == 400
        assert "error" in json.loads(response.content)


def test_update_totalbus_company(rf):
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    req_body = {
        "name": "Busao express",
        "company_internal_id": 898,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "integracao": "totalbus",
    }
    expected_resp = {"success": True}
    data = CreateRodoviariaCompanyForm.parse_obj(req_body)
    request = rf.post("/create_company", req_body, content_type="application/json")

    with mock.patch("rodoviaria.service.company_svc.update_empresa", return_value=expected_resp) as mock_update_empresa:
        response = views.update_company(request)
        mock_update_empresa.assert_called_once_with(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=None,
        )

    assert response.status_code == 200
    assert json.loads(response.content) == expected_resp


def test_update_company_exception(rf):
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    req_body = {
        "name": "Busao express",
        "company_internal_id": 898,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "integracao": "totalbus",
    }
    request = rf.post("/create_company", req_body, content_type="application/json")

    with mock.patch(
        "rodoviaria.service.company_svc.update_empresa",
        side_effect=RodoviariaCompanyNotFoundException,
    ):
        response = views.update_company(request)
        assert response.status_code == 400
        assert "error" in json.loads(response.content)


def test_verifica_rotas_integradas(rf):
    with mock.patch("rodoviaria.service.rotas_transbrasil_svc.verifica_link_rotas") as mock_verifica_link_rotas:
        mock_verifica_link_rotas.return_value = {
            "(24242, 9323)": "link nao cadastrado",
            "(48230, 3829)": "ok",
        }
        params = {
            "rotas": [
                {"company_id": 9323, "rota_id": 24242},
                {"company_id": 3829, "rota_id": 48230},
            ]
        }
        response = ninja_client.get("/v1/verifica-rotas-integradas", json=params)
        assert response.status_code == 200
        assert json.loads(response.content) == mock_verifica_link_rotas.return_value


def test_fetch_operacao_empresa_marketplace(rf):
    with mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
        return_value=None,
    ):
        request = rf.get("/fetch_operacao_empresa_marketplace")
        response = views.fetch_operacao_empresa_marketplace(request, company_internal_id=1)
        assert response.status_code == 200
        assert json.loads(response.content)["mensagem"] == "Busca de dados da operação iniciada"


def test_fetch_operacao_empresa_marketplace_unauthorized_error_401(rf):
    with mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
        side_effect=RodoviariaUnauthorizedError,
    ):
        request = rf.get("/fetch_operacao_empresa_marketplace")
        response = views.fetch_operacao_empresa_marketplace(request, company_internal_id=1)
        assert response.status_code == 401
        assert json.loads(response.content) == {"error": RodoviariaUnauthorizedError.message}


def test_fetch_external_totalbus_companies(rf):
    req_body = {
        "user": "user_anon",
        "password": "le_senhe",
        "tenant_id": "9e48bc2b-2707-4b79-85fd-6cf34253d8c5",
    }
    external_companies = [
        {"external_id": -1, "nome": "TODAS"},
        {"external_id": 1, "nome": "EMPRESA ALEATORIA LTDA", "cnpj": "09399871234571"},
    ]

    request = rf.post("/fetch_external_totalbus_companies", req_body, content_type="application/json")
    with mock.patch(
        "rodoviaria.api.totalbus.api.lista_empresas_api",
        return_value=external_companies,
    ) as mock_lista_empresas_api:
        response = views.fetch_external_totalbus_companies(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"external_companies": external_companies}
    mock_lista_empresas_api.assert_called_once_with(TotalbusNoCompanyLogin.parse_obj(req_body))


def test_fetch_external_totalbus_companies_unauthorized_login_401(rf):
    req_body = {
        "user": "user_anon",
        "password": "le_senhe",
        "tenant_id": "9e48bc2b-2707-4b79-85fd-6cf34253d8c5",
    }

    request = rf.post("/fetch_external_totalbus_companies", req_body, content_type="application/json")
    with mock.patch(
        "rodoviaria.api.totalbus.api.lista_empresas_api",
        side_effect=RodoviariaUnauthorizedError,
    ):
        response = views.fetch_external_totalbus_companies(request)

    assert response.status_code == 401
    assert json.loads(response.content) == {"error": RodoviariaUnauthorizedError.message}


def test_fetch_formas_pagamento_totalbus(rf):
    req_body = {
        "login": {
            "user": "user_anon",
            "password": "le_senhe",
            "tenant_id": "9e48bc2b-2707-4b79-85fd-6cf34253d8c5",
        },
        "integracao": "totalbus",
    }
    formas_pagamento = [
        {"id": 2, "descricao": "CRÉDITO"},
        {"id": 12, "descricao": "FATURADO BUSER", "tipoPago": 11},
    ]

    request = rf.post("/fetch_formas_pagamento", req_body, content_type="application/json")
    with mock.patch(
        "rodoviaria.api.totalbus.api.fetch_formas_pagamento",
        return_value=formas_pagamento,
    ):
        response = views.fetch_formas_pagamento(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"formas_pagamento": formas_pagamento}


def test_fetch_formas_pagamento_totalbus_unauthorized_login_401(rf):
    req_body = {
        "login": {
            "user": "user_anon",
            "password": "le_senhe",
            "tenant_id": "9e48bc2b-2707-4b79-85fd-6cf34253d8c5",
        },
        "integracao": "totalbus",
    }

    request = rf.post("/fetch_formas_pagamento", req_body, content_type="application/json")
    with mock.patch(
        "rodoviaria.api.totalbus.api.fetch_formas_pagamento",
        side_effect=RodoviariaUnauthorizedError,
    ):
        response = views.fetch_formas_pagamento(request)

    assert response.status_code == 401
    assert json.loads(response.content) == {"error": RodoviariaUnauthorizedError.message}


def test_fetch_formas_pagamento_smartbus(rf):
    req_body = {"login": {"username": "buser", "password": "buser", "cliente": "buser"}, "integracao": "smartbus"}
    formas_pagamento = [
        {"id": 2, "descricao": "CRÉDITO"},
        {"id": 12, "descricao": "FATURADO BUSER"},
    ]

    request = rf.post("/fetch_formas_pagamento", req_body, content_type="application/json")
    with mock.patch(
        "rodoviaria.api.smartbus.api.fetch_formas_pagamento",
        return_value=formas_pagamento,
    ):
        response = views.fetch_formas_pagamento(request)

    assert response.status_code == 200
    assert json.loads(response.content) == {"formas_pagamento": formas_pagamento}


def test_link_trechos_classes_async(rf):
    body = {
        "trechos": [
            {
                "company_internal_id": 1021,
                "origem_internal_id": 876,
                "destino_internal_id": 889,
                "datetime_ida": "2022-07-08T08:00:00",
                "origem_timezone": "America/Sao_Paulo",
                "trechoclasse_internal_id": 381,
                "grupo_internal_id": 3,
                "grupo_datetime_ida": "2022-07-08T08:00:00",
                "grupoclasse_internal_id": 3,
                "tipo_assento": "executivo",
            },
        ]
    }
    with mock.patch(
        "rodoviaria.service.link_trechoclasse_async_svc.link_trechoclasse_async",
    ):
        request = rf.post("/link_trechos_classes_async", body, content_type="application/json")
        response = views.link_trechos_classe_async(request)
        assert response.status_code == 200
        assert json.loads(response.content)["mensagem"] == "Link de trechos classe async foi iniciado"


def test_link_trechos_classes_async_use_update_price_queue(rf):
    body = {
        "trechos": [
            {
                "company_internal_id": 1021,
                "origem_internal_id": 876,
                "destino_internal_id": 889,
                "datetime_ida": "2022-07-08T08:00:00",
                "origem_timezone": "America/Sao_Paulo",
                "trechoclasse_internal_id": 381,
                "grupo_internal_id": 3,
                "grupo_datetime_ida": "2022-07-08T08:00:00",
                "grupoclasse_internal_id": 3,
                "tipo_assento": "executivo",
            },
        ]
    }
    with mock.patch(
        "rodoviaria.service.link_trechoclasse_async_svc.link_trechoclasse_async",
    ) as mock_link_trechoclasse_async:
        trechos = LinkTrechosClassesAsyncParams.parse_raw(json.dumps(body)).dict()["trechos"]

        request = rf.post("/link_trechos_classes_async", body, content_type="application/json")
        response = views.link_trechos_classe_async(request, use_update_price_queue=True)

        assert response.status_code == 200
        assert json.loads(response.content)["mensagem"] == "Link de trechos classe async foi iniciado"
        assert mock_link_trechoclasse_async.call_args[0][0] == trechos
        assert mock_link_trechoclasse_async.call_args[0][1] is True


def test_link_trechos_classes_async_use_hot_update_price_queue(rf):
    body = {
        "trechos": [
            {
                "company_internal_id": 1021,
                "origem_internal_id": 876,
                "destino_internal_id": 889,
                "datetime_ida": "2022-07-08T08:00:00",
                "origem_timezone": "America/Sao_Paulo",
                "trechoclasse_internal_id": 381,
                "grupo_internal_id": 3,
                "grupo_datetime_ida": "2022-07-08T08:00:00",
                "grupoclasse_internal_id": 3,
                "tipo_assento": "executivo",
            },
        ]
    }
    with mock.patch(
        "rodoviaria.service.link_trechoclasse_async_svc.link_trechoclasse_async",
    ) as mock_link_trechoclasse_async:
        trechos = LinkTrechosClassesAsyncParams.parse_raw(json.dumps(body)).dict()["trechos"]

        request = rf.post("/link_trechos_classes_async", body, content_type="application/json")
        response = views.link_trechos_classe_async(request, use_hot_update_price_queue=True)

        assert response.status_code == 200
        assert json.loads(response.content)["mensagem"] == "Link de trechos classe async foi iniciado"
        assert mock_link_trechoclasse_async.call_args[0][0] == trechos
        assert mock_link_trechoclasse_async.call_args[0][1] is False
        assert mock_link_trechoclasse_async.call_args[0][2] is True
        assert mock_link_trechoclasse_async.call_args[0][3] is False


def test_link_trechos_classes_async_use_update_top_divergencias(rf):
    body = {
        "trechos": [
            {
                "company_internal_id": 1021,
                "origem_internal_id": 876,
                "destino_internal_id": 889,
                "datetime_ida": "2022-07-08T08:00:00",
                "origem_timezone": "America/Sao_Paulo",
                "trechoclasse_internal_id": 381,
                "grupo_internal_id": 3,
                "grupo_datetime_ida": "2022-07-08T08:00:00",
                "grupoclasse_internal_id": 3,
                "tipo_assento": "executivo",
            },
        ]
    }
    with mock.patch(
        "rodoviaria.service.link_trechoclasse_async_svc.link_trechoclasse_async",
    ) as mock_link_trechoclasse_async:
        trechos = LinkTrechosClassesAsyncParams.parse_raw(json.dumps(body)).dict()["trechos"]

        request = rf.post("/link_trechos_classes_async", body, content_type="application/json")
        response = views.link_trechos_classe_async(request, use_update_top_divergencias=True)

        assert response.status_code == 200
        assert json.loads(response.content)["mensagem"] == "Link de trechos classe async foi iniciado"
        assert mock_link_trechoclasse_async.call_args[0][0] == trechos
        assert mock_link_trechoclasse_async.call_args[0][1] is False
        assert mock_link_trechoclasse_async.call_args[0][2] is False
        assert mock_link_trechoclasse_async.call_args[0][3] is True


def test_link_trechoclasse_async_get_tagged(rf):
    result = {
        "to_be_closed": [12, 13, 14],
        "to_be_updated": {"15": {"preco": 123.13}, "16": {"preco": 123.13}},
    }
    with mock.patch(
        "rodoviaria.service.link_trechoclasse_async_svc.get_tagged_trechos_classes",
        return_value=result,
    ):
        request = rf.get("/link_trechoclasse_async/get_tagged")
        response = views.link_trechoclasse_async_get_tagged(request)
        assert response.status_code == 200
        assert json.loads(response.content) == result


def test_link_trechoclasse_async_remover_tags(rf):
    body = {
        "to_be_closed": [12, 13, 14],
        "to_be_updated_in_django": [15, 16],
    }
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc.remover_tags_trechos_atualizados"):
        request = rf.post(
            "/link_trechoclasse_async/remover_tags",
            body,
            content_type="application/json",
        )
        response = views.link_trechoclasse_async_remover_tags(request)
        assert response.status_code == 200
        assert json.loads(response.content) == {}


def test_add_multiple_pax_async():
    pax_params = {
        "id_destino": 514,
        "id_origem": 638,
        "passenger": {
            "buseiro_id": 341475,
            "cpf": "11975923774",
            "name": "Nome Teste",
            "phone": "21969200000",
            "rg_number": "8885000",
        },
        "travel_id": 5409394,
        "trechoclasse_id": 13984203,
        "grupo_id": 111,
        "valor_por_buseiro": 85.0,
    }
    params = [pax_params]
    with mock.patch(
        "rodoviaria.service.passagens_pendentes_svc.emitir_passagens_pendentes_async"
    ) as mock_emitir_passagens_pendentes_async:
        response = ninja_client.post("/v1/add-multiples-pax-async", json=params)
    mock_emitir_passagens_pendentes_async.assert_called_once_with(
        passagens=[CheckPaxForm.parse_obj(pax_params)], modelo_venda="marketplace"
    )
    assert json.loads(response.content) == {}


def test_add_multiple_pax_async_with_modelo_venda():
    pax_params = {
        "id_destino": 514,
        "id_origem": 638,
        "passenger": {
            "buseiro_id": 341475,
            "cpf": "11975923774",
            "name": "Nome Teste",
            "phone": "21969200000",
            "rg_number": "8885000",
        },
        "travel_id": 5409394,
        "trechoclasse_id": 13984203,
        "grupo_id": 111,
        "valor_por_buseiro": 85.0,
    }
    params = {"modelo_venda": "hibrido", "passagens": [pax_params]}
    with mock.patch(
        "rodoviaria.service.passagens_pendentes_svc.emitir_passagens_pendentes_async"
    ) as mock_emitir_passagens_pendentes_async:
        response = ninja_client.post("/v1/add-multiples-pax-async", json=params)
    mock_emitir_passagens_pendentes_async.assert_called_once_with(
        passagens=[CheckPaxForm.parse_obj(pax_params)], modelo_venda="hibrido"
    )
    assert json.loads(response.content) == {}


def test_lista_passagens_confirmadas_por_empresa():
    return_value = [{"travel_id": 1234, "buseiro_id": 4321}]
    params = {
        "companies_ids": [312, 432],
        "data_inicial": datetime(2022, 3, 4, 18, 23).isoformat(),
        "data_final": datetime(2022, 3, 14, 18, 23).isoformat(),
    }
    params_str = "?"
    params_str += "companies_ids=321"
    params_str += "&companies_ids=432"
    params_str += f"&data_inicial={params['data_inicial']}"
    params_str += f"&data_final={params['data_final']}"
    with mock.patch(
        "rodoviaria.service.passagens_pendentes_svc.lista_passagens_confirmadas_por_empresa",
        return_value=return_value,
    ):
        response = ninja_client.get("/v1/list-passagens-confirmadas-por-company" + params_str)
    assert json.loads(response.content) == [{"travel_id": 1234, "buseiro_id": 4321}]


def test_atualizar_link_rota(rf):
    request = rf.post("/atualizar_link_rota")
    with mock.patch(
        "rodoviaria.service.link_rotas_svc.atualizar_id_internal_rota_por_internal_id_antigo",
        return_value=1,
    ):
        response = views.atualizar_link_rota(request, rota_internal_id_antigo=1, rota_internal_id_novo=2)

    assert response.status_code == 200
    assert json.loads(response.content) == {"message": "Foram atualizadas 1 rotas"}


def test_lista_empresas_vexado(rf):
    lista = [{"name": "VEXADO APP / VEXADO APP", "id": 1}]
    with mock.patch("rodoviaria.api.vexado.api.lista_empresas_api", return_value=lista):
        vexado_form = VexadoAnonymousLogin(modelo_venda="marketplace")
        params = ListaEmpresasAPIParams.parse_obj({"login_params": vexado_form})
        request = rf.post("/v1/lista-empresas-api")
        response = views.lista_empresas_api(request, data=params)
    assert response.status_code == 200
    assert json.loads(response.content) == {"external_companies": lista}


def test_lista_empresas_api_unauthorized(rf):
    login_params = {
        "user": "user_anon",
        "password": "le_senhe",
        "tenant_id": "9e48bc2b-2707-4b79-85fd-6cf34253d8c5",
    }

    request = rf.post("/fetch_external_totalbus_companies")
    with mock.patch(
        "rodoviaria.api.totalbus.api.lista_empresas_api",
        side_effect=RodoviariaUnauthorizedError,
    ):
        params = ListaEmpresasAPIParams.parse_obj(
            {
                "modelo_venda": "marketplace",
                "integracao": "totalbus",
                "login_params": login_params,
            }
        )
        response = views.lista_empresas_api(request, data=params)

    assert response.status_code == 401
    assert json.loads(response.content) == {"error": RodoviariaUnauthorizedError.message}


def test_verify_login_praxio(rf, praxio_mock_login):
    body = {"name": "empresa ltda", "password": "le senhe", "cliente": "empresa_vr"}
    request = rf.post("/v1/login/praxio", body, content_type="application/json")

    response = views.verify_praxio_login(request)

    assert response.status_code == 200
    assert "success" in json.loads(response.content)


def test_verify_login_praxio_unauthorized(rf, praxio_mock_unauthorized_login):
    body = {"name": "empresa ltda", "password": "le senhe", "cliente": "empresa_vr"}
    request = rf.post("/v1/login/praxio", body, content_type="application/json")

    response = views.verify_praxio_login(request)

    assert response.status_code == 401
    assert "error" in json.loads(response.content)


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_trechos_overbooking(rf):
    trecho_classe = baker.make(
        TrechoClasse,
        datetime_ida=to_default_tz(timezone.now() + timedelta(hours=2)),
        trechoclasse_internal_id=1,
    )
    trecho_classe.tags.add(Tags.OVERBOOKING)
    baker.make(
        TrechoClasse,
        datetime_ida=to_default_tz(timezone.now() + timedelta(hours=2)),
        trechoclasse_internal_id=2,
    )  # no overbooking
    request = rf.get("/v1/get-trechos-overbooking")
    response = views.get_trechos_overbooking(request)
    assert response.status_code == 200
    assert json.loads(response.content) == {"trechos": [trecho_classe.trechoclasse_internal_id]}


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_trechos_classe_error_hibrido(rf, vexado_integracao):
    company_hibrido = baker.make(Company, modelo_venda=Company.ModeloVenda.HIBRIDO, integracao=vexado_integracao)
    company_marketplace = baker.make(
        Company, modelo_venda=Company.ModeloVenda.MARKETPLACE, integracao=vexado_integracao
    )
    trecho_classe_hibrido = baker.make(
        TrechoClasseError,
        datetime_ida=to_default_tz(timezone.now() + timedelta(hours=2)),
        trechoclasse_internal_id=1,
        company=company_hibrido,
    )
    baker.make(
        TrechoClasseError,
        datetime_ida=to_default_tz(timezone.now() + timedelta(hours=2)),
        trechoclasse_internal_id=2,
        company=company_marketplace,
    )
    request = rf.get("/v1/hibrido/get-trechos-classe-error")
    response = views.get_trechos_classe_error_hibrido(request)
    assert response.status_code == 200
    assert json.loads(response.content) == {"trechos": [trecho_classe_hibrido.trechoclasse_internal_id]}


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_delete_trechos_classe_error_hibrido(rf):
    trechoclasse_internal_id = 32
    company_hibrido = baker.make(Company, modelo_venda=Company.ModeloVenda.HIBRIDO)
    company_marketplace = baker.make(Company, modelo_venda=Company.ModeloVenda.MARKETPLACE)
    trecho_classe_hibrido = baker.make(
        TrechoClasseError,
        datetime_ida=to_default_tz(timezone.now() + timedelta(hours=2)),
        trechoclasse_internal_id=trechoclasse_internal_id,
        company=company_hibrido,
    )
    trecho_classe_marketplace = baker.make(
        TrechoClasseError,
        datetime_ida=to_default_tz(timezone.now() + timedelta(hours=2)),
        trechoclasse_internal_id=2,
        company=company_marketplace,
    )
    response = ninja_client.post(
        "/v1/hibrido/delete-trechos-classe-error",
        json={"trechos_classe_ids": [trechoclasse_internal_id]},
    )
    assert response.json() == {}
    trecho_classe_marketplace.refresh_from_db()  # nao apagou
    with pytest.raises(TrechoClasseError.DoesNotExist):
        trecho_classe_hibrido.refresh_from_db()


def test_get_detalhes_rotinas(rf):
    request = rf.get("/v1/get_detalhes_rotinas")
    retorno = [
        {
            "horario": "12:00",
            "dia_semana": "dom",
            "datas": [{"data": "2022-02-02", "from_api": True}, {"data": "2022-02-12"}],
            "classes": [{"tipo_assento": "leito", "max_capacity": 12}],
            "trechos_vendidos": {"12": {"leito": 12.00}, "13": {"leito": 12.00}},
        },
    ]
    with mock.patch(
        "rodoviaria.service.auto_integra_operacao.auto_integra_operacao_svc.get_detalhes_rotinas_uma_rota",
        return_value=retorno,
    ):
        params = GetDetalhesRotinasParams(
            rodoviaria_rota_id=1999,
            ids_trechos_vendidos_filter="12,13",
            start_date=datetime(2022, 2, 2),
            end_date=datetime(2022, 2, 20),
        )
        response = views.get_detalhes_rotinas(request, params)
        assert json.loads(response.content) == retorno


@pytest.mark.parametrize("fetch_db_data", [True, False])
def test_get_detalhes_rotinas_por_rota(rf, mocker, override_config, fetch_db_data):
    utc = ZoneInfo("UTC")
    body = {
        "rodoviaria_rota_ids": [42, 12, 43],
        "trechos_vendidos_ids_por_rota_id": {
            "12": [15, 16],
            "42": [12, 13, 14],
            "43": [17],
        },
        "datetimes_a_ignorar_por_rota_id": {
            "12": [datetime(2022, 2, 2, 16, 0, tzinfo=utc), datetime(2022, 2, 2, 13, 0, tzinfo=utc)],
            "42": [datetime(2022, 2, 2, 3, 40, tzinfo=utc), datetime(2022, 2, 9, 3, 40, tzinfo=utc)],
        },
    }
    request_body = copy.deepcopy(body)

    if fetch_db_data:
        company = baker.make(Company, company_internal_id=123)
        company_bdjango = baker.make(CompanyBdjango, id=company.company_internal_id)
        for rota_id in body["rodoviaria_rota_ids"]:
            rota = baker.make(Rota, id=rota_id, company=company, id_internal=rota_id + 100)
            rota_bdjango = baker.make(RotaBdjango, id=rota.id_internal)

            tv_id_list = body["trechos_vendidos_ids_por_rota_id"].get(str(rota_id), [])
            [baker.make(TrechoVendidoBdjango, rota_id=rota_bdjango.id, id=tv_id) for tv_id in tv_id_list]

            datetime_a_ignorar_list = body["datetimes_a_ignorar_por_rota_id"].get(str(rota_id), [])

            grupos = [
                baker.make(
                    GrupoBdjango,
                    rota_id=rota_bdjango.id,
                    modelo_venda="marketplace",
                    status="travel_confirmed",
                    datetime_ida=datetime_ida,
                    company=company_bdjango,
                )
                for datetime_ida in datetime_a_ignorar_list
            ]
            [baker.make(GrupoClasseBdjango, grupo=grupo) for grupo in grupos]

        request_body["datetimes_a_ignorar_por_rota_id"] = {}
        request_body["trechos_vendidos_ids_por_rota_id"] = {}

    request = rf.post("/v1/get_detalhes_rotinas_por_rota", request_body, content_type="application/json")
    retorno = {
        "1999": [
            {
                "horario": "12:00",
                "dia_semana": "dom",
                "datas": [
                    {"data": "2022-02-02", "from_api": True},
                    {"data": "2022-02-12"},
                ],
                "classes": [{"tipo_assento": "leito", "max_capacity": 12}],
                "trechos_vendidos": {"12": {"leito": 12.00}, "13": {"leito": 12.00}},
            },
        ]
    }
    svc = mocker.patch(
        "rodoviaria.service.auto_integra_operacao.integracao_grupos_svc.get_detalhes_rotinas",
        return_value=retorno,
    )

    response = views.get_detalhes_rotinas_por_rota(request)

    assert json.loads(response.content) == retorno
    trechos_vendidos_ids_por_rota_id = {
        int(key): value for key, value in body["trechos_vendidos_ids_por_rota_id"].items()
    }
    datetime_a_ignorar_por_rota_id = {int(key): value for key, value in body["datetimes_a_ignorar_por_rota_id"].items()}
    assert svc.call_args[0][0] == body["rodoviaria_rota_ids"]
    for rota_id, tv_ids_list in svc.call_args[0][1].items():
        assert set(tv_ids_list) == set(trechos_vendidos_ids_por_rota_id[rota_id])

    for rota_id, datetime_list in svc.call_args[0][2].items():
        assert set(datetime_list) == set(datetime_a_ignorar_por_rota_id[rota_id])


def test_update_trechos_vendidos_de_rota_integrada(rf):
    rota_internal_id = 18
    trechos_internal_ids = {"1": 10, "2": 20, "3": 30}
    body = {
        "rota_internal_id": rota_internal_id,
        "trechos_internal_ids": trechos_internal_ids,
    }
    request = rf.post(
        "/v1/update_trechos_vendidos_de_rota_integrada",
        body,
        content_type="application/json",
    )

    with mock.patch(
        "rodoviaria.service.rota_svc.update_trechos_vendidos_internal_ids"
    ) as mock_update_trechos_internal_ids:
        resp = views.update_trechos_vendidos_de_rota_integrada(request)

    assert resp.status_code == 200
    assert json.loads(resp.content) == {"success": True}
    mock_update_trechos_internal_ids.assert_called_once_with(rota_internal_id, trechos_internal_ids)


def test_atualizar_checkpoints_sem_company_id_e_sem_rota_id(rf):
    request = rf.get("/v1/atualizar_checkpoints")
    response = views.atualizar_checkpoints(request)
    assert response.status_code == 404
    assert json.loads(response.content) == {"error": "necessário passar rota_id ou (company_id e modelo_venda)"}


def test_atualizar_checkpoints_com_company_id_e_sem_modelo_venda(rf):
    company_id = 84123
    request = rf.get("/v1/atualizar_checkpoints")
    response = views.atualizar_checkpoints(request, company_id=company_id)
    assert response.status_code == 404
    assert json.loads(response.content) == {"error": "necessário passar o modelo_venda"}


def test_atualizar_checkpoints_por_company_id_e_modelo_venda(rf):
    company_id = 85412
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    request = rf.get("/v1/atualizar_checkpoints")
    with mock.patch(
        "rodoviaria.service.rota_svc.atualizar_checkpoints_rotas_por_empresa"
    ) as mock_atualizar_checkpoints_rotas_por_empresa:
        response = views.atualizar_checkpoints(request, company_id=company_id, modelo_venda=modelo_venda)
    mock_atualizar_checkpoints_rotas_por_empresa.assert_called_once_with(company_id, modelo_venda)
    assert response.status_code == 200
    assert json.loads(response.content) == {
        "mensagem": f"checkpoints das rotas da empresa ({company_id}, {modelo_venda}) atualizados"
    }


def test_fetch_trechos_by_company_id(rf):
    company_internal_id = 123
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    request = rf.get("/v1/atualizar_checkpoints")
    with mock.patch(
        "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trechos_by_company_id"
    ) as mock_fetch_trechos_by_company_id:
        response = views.fetch_trechos_by_company_id(request, company_internal_id=company_internal_id)
    mock_fetch_trechos_by_company_id.assert_called_once_with(company_internal_id, modelo_venda)
    assert response.status_code == 200

    assert json.loads(response.content) == {
        "mensagem": (
            "Fetch de trechos vendidos de todas as rotas ativas foi iniciado para a empresa"
            f" {company_internal_id} {modelo_venda=}"
        )
    }


def test_list_passagens(rf):
    body = {
        "travel_ids": [1, 2, 3],
        "buseiro_ids": [10, 20, 30],
        "status": "confirmada",
        "with_preco_rodoviaria": True,
    }
    request = rf.post("/v1/passagens/list", body, content_type="application/json")

    with mock.patch(
        "rodoviaria.service.get_passagens_svc.get_passagens", return_value=[{}, {}, {}]
    ) as mock_get_passagens:
        resp = views.list_passagens(request)

    assert resp.status_code == 200
    mock_get_passagens.assert_called_once_with(
        travel_ids=body["travel_ids"],
        buseiro_ids=body["buseiro_ids"],
        status=body["status"],
        with_preco_rodoviaria=body["with_preco_rodoviaria"],
    )


def test_get_passagem_info(rf):
    body = {"travel_id": 1, "buseiro_id": 10}
    request = rf.post("/v1/passagens/get", body, content_type="application/json")

    with mock.patch(
        "rodoviaria.service.get_passagens_svc.get_passagem_info",
        return_value=[{}, {}, {}],
    ) as mock_get_passagem_info:
        resp = views.get_passagem_info(request)

    assert resp.status_code == 200
    mock_get_passagem_info.assert_called_once_with(travel_id=body["travel_id"], buseiro_id=body["buseiro_id"])


def test_hard_stop_empresa(rf):
    params = {
        "company_internal_id": 123,
        "modelo_venda": "marketplace",
    }
    request = rf.post(
        "/v1/hard_stop_empresa",
        data=json.dumps(params),
        content_type="application/json",
    )
    with mock.patch("rodoviaria.service.company_svc.hard_stop_empresa") as mock_method:
        mock_method.return_value = {"success": True}
        response = views.hard_stop_empresa(request)
    mock_method.assert_called_once_with(params["company_internal_id"], params["modelo_venda"])
    assert response.status_code == 200
    assert json.loads(response.content) == {"success": True}


def test_hard_stop_empresa_erro_company_not_found(rf):
    params = {
        "company_internal_id": 123,
        "modelo_venda": "marketplace",
    }
    request = rf.post(
        "/v1/hard_stop_empresa",
        data=json.dumps(params),
        content_type="application/json",
    )
    with mock.patch("rodoviaria.service.company_svc.hard_stop_empresa") as mock_method:
        mock_method.side_effect = RodoviariaCompanyNotFoundException("teste")
        response = views.hard_stop_empresa(request)
    mock_method.assert_called_once_with(params["company_internal_id"], params["modelo_venda"])
    assert response.status_code == 400
    assert json.loads(response.content) == {"error": "teste"}


def test_fetch_data_limite_rotas(rf):
    params = {
        "company_internal_id": 123,
        "modelo_venda": "marketplace",
    }
    request = rf.get(
        "/v1/fetch_data_limite_rotas",
    )
    with mock.patch("rodoviaria.service.fetch_data_limite_rotas_svc.fetch_data_limite_rotas") as mock_method:
        mock_method.return_value = {}
        response = views.fetch_data_limite_rotas(request, 123, "marketplace")
    mock_method.assert_called_once_with(
        company_internal_id=params["company_internal_id"],
        modelo_venda=params["modelo_venda"],
    )
    assert response.status_code == 200
    assert json.loads(response.content) == {}


def test_bulk_grupo_tem_passagem_emitida(rf):
    data = {
        "grupos_passenger_map": {
            "4232": [
                {"travel_id": 555, "buseiro_id": 555},
                {"travel_id": 123, "buseiro_id": 456},
            ]
        }
    }
    request = rf.post(
        "/v1/bulk-grupo-tem-passagem-emitida",
        json.dumps(data),
        content_type="application/json",
    )

    with mock.patch("rodoviaria.service.get_passagens_svc.grupos_com_emissao") as mock_method:
        mock_method.return_value = {4232: True}
        response = views.bulk_grupo_tem_passagem_emitida(request)

    mock_method.assert_called_once()
    assert response.status_code == 200
    assert json.loads(response.content) == {"4232": True}


def test_trechos_empresa_com_maiores_divergencias_de_preco(rf):
    request = rf.get("/v1/list-trechos-maiores-divergencias-preco")
    with mock.patch(
        "rodoviaria.service.divergencia_precos_svc.trechos_empresa_com_maiores_divergencias_de_preco"
    ) as mock_method:
        mock_method.return_value = [{"order": 1, "origem_id": 10, "destino_id": 20, "divergencia": 120}]
        response = views.trechos_empresa_com_maiores_divergencias_de_preco(request, days_behind=7)

    assert response.status_code == 200
    assert json.loads(response.content) == mock_method.return_value
    mock_method.assert_called_once_with(7, 20)


def test_view_integracoes(rf):
    with mock.patch(
        "rodoviaria.service.sistema_integracao_empresa_svc.get_integracoes_empresa"
    ) as mock_sistema_integracao_empresa:
        mock_sistema_integracao_empresa.return_value = [{"name": "totalbus", "id": "1"}]
        request = rf.get("/v1/integracoes")
        response = views.integracoes(request)
        data = json.loads(response.content)
        assert data == {"integracoes": [{"name": "totalbus", "id": "1"}]}


def test_remanejamentos_pendentes_ou_completos_recentes(rf):
    with mock.patch(
        "rodoviaria.service.remanejamento_svc.remanejamentos_pendentes_ou_completos_recentes",
        return_value={"remanejamentos": []},
    ) as mock_svc:
        request = rf.get("/v1/remanejamentos_recentes")
        response = views.remanejamentos_pendentes_ou_completos_recentes(request, grupo_id=8391)
    assert json.loads(response.content) == {"remanejamentos": []}
    mock_svc.assert_called_once_with(8391)


def test_criacao_integracao_create_company(rf):
    name = "A gloriosa empresa da Clara"
    req_body = {
        "name": name,
        "company_internal_id": 24,
        "modelo_venda": "marketplace",
        "features": [],
        "login": None,
        "max_percentual_divergencia": None,
        "integracao": "integracao do Juam",
    }
    request = rf.post("/create_company", data=req_body, content_type="application/json")
    views.create_company(request)
    integracao = Integracao.objects.get(name="integracao_do_juam")
    company = Company.objects.get(company_internal_id=24)
    assert name == company.name
    assert integracao.id == company.integracao.pk
    assert integracao.name == company.integracao.name
    assert company.features == []


def test_edicao_integracao_update_company(rf):
    baker.make(
        "rodoviaria.Company",
        name="a gloriosa empresa da clara",
        company_internal_id=24,
        features=[],
        integracao=baker.make("rodoviaria.integracao", name="integracao_antiga"),
    )
    req_body = {
        "name": "a gloriosa empresa da clara",
        "company_internal_id": 24,
        "modelo_venda": "marketplace",
        "features": [],
        "login": None,
        "max_percentual_divergencia": None,
        "integracao": "  nóme da integração nôva editada  ",
    }
    request = rf.post("/update_company", data=req_body, content_type="application/json")
    views.update_company(request)
    integracao_antiga_registrada = Integracao.objects.get(name="integracao_antiga")
    integracao_update = Integracao.objects.get(name="nome_da_integracao_nova_editada")
    company = Company.objects.get(company_internal_id=24)

    assert integracao_antiga_registrada.name == "integracao_antiga"
    assert integracao_update.name == "nome_da_integracao_nova_editada"
    assert integracao_update.id == company.integracao.pk
    assert integracao_update.name == company.integracao.name
    assert company.features == []


def test_get_rota(rf):
    request = rf.get("/get_rota")
    with mock.patch(
        "rodoviaria.service.auto_integra_operacao.auto_integra_operacao_svc.get_detalhes_para_integrar_uma_rota",
        return_value="retorno",
    ):
        retorno = views.get_rota(request, rodoviaria_rota_id=1)
        assert json.loads(retorno.content) == "retorno"

    with mock.patch(
        "rodoviaria.service.auto_integra_operacao.auto_integra_operacao_svc.get_detalhes_para_integrar_uma_rota",
        side_effect=RodoviariaException("Erro"),
    ):
        retorno = views.get_rota(request, rodoviaria_rota_id=1)
        assert json.loads(retorno.content) == {"message": "Erro"}


def test_get_atualizacao_passagem_api_parceiro(rf, vexado_company):
    buseiro_id = 12
    modelo_venda = "marketplace"
    travel_id = 1
    response_api = {"integracao": "vexado"}
    data = [RetornoConsultarBilheteForm.parse_obj(response_api).dict()]
    request = rf.get("/v1/get-atualizacao-passagem-api-parceiro")
    with mock.patch("rodoviaria.service.get_passagens_svc.get_infos_atualizacao_passagem") as mock_method:
        mock_method.return_value = data
        response = views.get_atualizacao_passagem_api_parceiro(request, buseiro_id, modelo_venda, travel_id)
    assert response.status_code == 200
    assert json.loads(response.content) == data
    mock_method.assert_called_once_with(buseiro_id, modelo_venda, travel_id)


def test_get_atualizacao_passagem_api_parceiro_sem_passagem_no_banco(rf, vexado_company):
    buseiro_id = 12
    modelo_venda = "marketplace"
    travel_id = 1
    data = []
    request = rf.get("/v1/get-atualizacao-passagem-api-parceiro")
    with mock.patch("rodoviaria.service.get_passagens_svc.get_infos_atualizacao_passagem") as mock_method:
        mock_method.return_value = data
        response = views.get_atualizacao_passagem_api_parceiro(request, buseiro_id, modelo_venda, travel_id)
    assert response.status_code == 200
    assert json.loads(response.content) == data
    mock_method.assert_called_once_with(buseiro_id, modelo_venda, travel_id)


def test_verifica_reverthardstop_empresa(rf):
    agora = datetime.now()
    baker.make(
        Company,
        company_internal_id=1,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        previous_features=["active"],
        previous_features_updated_at=agora,
        features=[],
    )
    request = rf.get("v1/raise_if_unable_hard_stop")
    response = views.verifica_elegibilidade_revert_hard_stop(request, 1)
    assert response.status_code == 200
    assert json.loads(response.content) == {"sucesso": True}


def test_raise_if_unable_hard_stop_nao_integrada(rf):
    agora = datetime.now()
    baker.make(
        Company,
        name="Clarita",
        company_internal_id=1,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        previous_features_updated_at=agora,
        features=[Company.Feature.ATUALIZAR_PRECO_CHECKOUT],
    )
    request = rf.get("v1/raise_if_unable_hard_stop")
    response = views.verifica_elegibilidade_revert_hard_stop(request, 1)
    assert response.status_code == 422
    assert json.loads(response.content) == {"error": "A empresa Clarita não está integrada"}


def test_linkar_tipo_assento(rf):
    params = RodoviariaLinkarTiposAssentosParams(
        id_assento=1, tipo_assento_buser_preferencial="leito", tipos_assentos_buser_match=["leito", "convencional"]
    )
    request = rf.get("v1/tipo_assento/link")
    with mock.patch("rodoviaria.service.class_match_svc.linkar_tipo_assento"):
        response = views.linkar_tipo_assento(request, params)
    assert response.status_code == 200
    assert json.loads(response.content) == {}


def test_linkar_tipo_assento_tipo_assento_invalido():
    with pytest.raises(ValidationError):
        RodoviariaLinkarTiposAssentosParams(
            id_assento=1,
            tipo_assento_buser_preferencial="Querem o meu tesouro?",
            tipos_assentos_buser_match=["Procurem!", "Nele está tudo que esse", "mundo pode dar a vocês"],
        )


def test_view_bloquear_poltronas_orchestrator(rf, totalbus_api, totalbus_trechoclasses):
    request = rf.post("/v1/bloquear-poltronas")
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    poltronas = [18]
    params = BloquearPoltronasParams(trecho_classe_id=trecho_classe_id, poltronas=poltronas)

    with mock.patch.object(CompraRodoviariaSVC, "bloquear_poltronas") as mock_bloquear_poltronas:
        mock_bloquear_poltronas.return_value = {}
        response = views.bloquear_poltronas(request, params)

    assert response.status_code == 200
    assert json.loads(response.content) == {}
    expected_form = BloquearPoltronasForm(trechoclasse_id=trecho_classe_id, poltronas=poltronas)
    mock_bloquear_poltronas.assert_called_once_with(expected_form)


class MockBaseModel(BaseModel):
    id: int


def test_view_bloquear_poltronas_orchestrator_base_model_returned(rf, totalbus_api, totalbus_trechoclasses, mocker):
    request = rf.post("/v1/bloquear-poltronas")
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    poltronas = [18]
    params = BloquearPoltronasParams(trecho_classe_id=trecho_classe_id, poltronas=poltronas)

    with mock.patch.object(CompraRodoviariaSVC, "bloquear_poltronas") as mock_bloquear_poltronas:
        mock_bloquear_poltronas.return_value = MockBaseModel(id=12)
        response = views.bloquear_poltronas(request, params)

    assert response.status_code == 200
    assert json.loads(response.content) == {"id": 12}
    expected_form = BloquearPoltronasForm(trechoclasse_id=trecho_classe_id, poltronas=poltronas)
    mock_bloquear_poltronas.assert_called_once_with(expected_form)


def test_view_bloquear_poltronas_v2_orchestrator(rf, totalbus_api, totalbus_trechoclasses):
    request = rf.post("/v2/bloquear-poltronas")
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    poltrona = 18
    params = BloquearPoltronasParamsV2(trecho_classe_id=trecho_classe_id, poltrona=poltrona)

    expected = BloquearPoltronasResponse(seat=1, best_before=datetime.min, external_payload=[])
    with mock.patch.object(CompraRodoviariaSVC, "bloquear_poltronas_v2") as mock_bloquear_poltronas:
        mock_bloquear_poltronas.return_value = expected
        response = views.bloquear_poltronas_v2(request, params)

    assert response.status_code == 200
    assert BloquearPoltronasResponse.parse_obj(json.loads(response.content)).json() == expected.json()
    expected_form = BloquearPoltronasFormV2(trechoclasse_id=trecho_classe_id, poltrona=poltrona)
    mock_bloquear_poltronas.assert_called_once_with(expected_form)


def test_view_desenho_mapa_poltronas(rf, totalbus_api, totalbus_trechoclasses):
    request = rf.get("/v1/desenho-mapa-poltronas")
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    with mock.patch.object(TotalbusAPI, "get_desenho_mapa_poltronas") as mock_get_desenho_mapa_poltronas:
        mock_get_desenho_mapa_poltronas.return_value = Onibus(
            layout=[
                Deck(
                    andar=0,
                    assentos=[
                        Assento(
                            numero=1,
                            livre=False,
                            x=1,
                            y=1,
                            tipo_assento="executivo",
                            categoria_especial="normal",
                            preco=D("123.45"),
                        )
                    ],
                )
            ]
        )
        response = views.get_desenho_mapa_poltronas(request, trecho_classe_id)
    assert response.status_code == 200
    assert json.loads(response.content) == {
        "layout": [
            {
                "andar": 0,
                "assentos": [
                    {
                        "numero": 1,
                        "x": 1,
                        "y": 1,
                        "livre": False,
                        "tipo_assento": "executivo",
                        "categoria_especial": "normal",
                        "preco": "123.45",
                    }
                ],
            }
        ]
    }
    mock_get_desenho_mapa_poltronas.assert_called_once_with(trecho_classe_id)


def test_view_desenho_mapa_poltronas_nao_implementado(rf, totalbus_api, totalbus_trechoclasses):
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    request = rf.get("/rodoviaria/v1/desenho-mapa-poltronas", data={"trecho_classe_id": trecho_classe_id})
    with mock.patch.object(TotalbusAPI, "get_desenho_mapa_poltronas") as mock_get_desenho_mapa_poltronas:
        mock_get_desenho_mapa_poltronas.side_effect = NotImplementedError(
            "Método get_desenho_mapa_poltronas não implementado para API totalbus"
        )
        response = request_with_middleware(request)

    assert response.status_code == 501
    assert json.loads(response.content) == {
        "error": "Método get_desenho_mapa_poltronas não implementado para API totalbus",
        "error_type": "not_implemented",
    }


@responses.activate
def test_view_get_vagas_por_categoria_especial(totalbus_login, totalbus_trechoclasses):
    # dada uma empresa com categoria_especial linkada no bd
    baker.make(
        "rodoviaria.CompanyCategoriaEspecial",
        company=totalbus_login.company,
        categoria_id_external=1,
        descricao_external="Assento Anormal",
        categoria_especial=Passagem.CategoriaEspecial.NORMAL,
    )
    baker.make(
        "rodoviaria.CompanyCategoriaEspecial",
        company=totalbus_login.company,
        categoria_id_external=33,
        descricao_external="Benefício PCD",
        categoria_especial=Passagem.CategoriaEspecial.PCD,
    )
    baker.make(
        "rodoviaria.CompanyCategoriaEspecial",
        company=totalbus_login.company,
        categoria_id_external=30,
        descricao_external="Espaço Mulher",
        categoria_especial=Passagem.CategoriaEspecial.ESPACO_MULHER,
    )
    responses.add(
        responses.POST,
        totalbus_endpoints.RetornaPoltronasConfig(totalbus_login).url,
        json=totalbus_mocker.GetPoltronasLivres.response(),
    )
    responses.add(
        responses.POST,
        totalbus_endpoints.ConsultarCategoriaCorridaRequestConfig(totalbus_login).url,
        json=totalbus_mocker.ConsultarCategoriaCorrida.response(),
    )
    trecho_classe_id = totalbus_trechoclasses.ida.trechoclasse_internal_id
    form = DefaultForm(trechoclasse_id=trecho_classe_id, force_renew_link=False)

    resp = CompraRodoviariaSVC(form).vagas_por_categoria_especial(form)

    assert sorted(resp["categorias_especiais"], key=lambda a: a["id"]) == sorted(
        [
            {"id": "idoso_100", "nome": "Idoso 100%", "vagas": 0},
            {"id": "idoso_50", "nome": "Idoso 50%", "vagas": 0},
            {"id": "jovem_100", "nome": "Jovem 100%", "vagas": 0},
            {"id": "jovem_50", "nome": "Jovem 50%", "vagas": 0},
            {"id": "crianca", "nome": "Criança (sem poltrona extra)", "vagas": 0},
            {"id": "pcd", "nome": "PCD", "vagas": 4},
            {"id": "espaco_mulher", "nome": "Espaço Mulher", "vagas": 8},
            {"id": "normal", "nome": "Normal", "vagas": 38},
            {"id": "ignorado", "nome": "Ignorado", "vagas": 0},
            {"id": "espaco_pet", "nome": "Espaço Pet", "vagas": 0},
        ],
        key=lambda a: a["id"],
    )


@responses.activate
def test_view_get_vagas_por_categoria_especial_praxio(praxio_login, praxio_trechoclasses):
    responses.add(
        responses.POST,
        praxio_endpoints.EfetuaLoginConfig(praxio_login, with_auth=False).url,
        json=praxio_mocker.MockLogin.response(),
    )
    responses.add(
        responses.POST,
        praxio_endpoints.RetornaPoltronasConfig(praxio_login, with_auth=False).url,
        json=praxio_mocker.MockGetPoltronasLivres.response_categorias_especiais(),
    )
    trecho_classe_id = praxio_trechoclasses.ida.trechoclasse_internal_id
    form = DefaultForm(trechoclasse_id=trecho_classe_id, force_renew_link=False)

    resp = CompraRodoviariaSVC(form).vagas_por_categoria_especial(form)

    assert sorted(resp["categorias_especiais"], key=lambda a: a["id"]) == sorted(
        [
            {"id": "crianca", "nome": "Criança (sem poltrona extra)", "vagas": 0},
            {"id": "espaco_mulher", "nome": "Espaço Mulher", "vagas": 0},
            {"id": "espaco_pet", "nome": "Espaço Pet", "vagas": 0},
            {"id": "idoso_100", "nome": "Idoso 100%", "vagas": 1},
            {"id": "idoso_50", "nome": "Idoso 50%", "vagas": 1},
            {"id": "ignorado", "nome": "Ignorado", "vagas": 0},
            {"id": "jovem_100", "nome": "Jovem 100%", "vagas": 1},
            {"id": "jovem_50", "nome": "Jovem 50%", "vagas": 0},
            {"id": "normal", "nome": "Normal", "vagas": 0},
            {"id": "pcd", "nome": "PCD", "vagas": 2},
        ],
        key=lambda a: a["id"],
    )


@pytest.mark.parametrize(
    "features,modelo_venda,status_task,expected_result",
    [
        pytest.param(
            [Company.Feature.AUTO_INTEGRA_OPERACAO],
            Company.ModeloVenda.MARKETPLACE,
            TaskStatus.Status.SUCCESS,
            True,
            id="elegivel",
        ),
        pytest.param(
            [Company.Feature.AUTO_INTEGRA_OPERACAO],
            Company.ModeloVenda.MARKETPLACE,
            TaskStatus.Status.FAILURE,
            True,
            id="elegivel_task_failure",
        ),
        pytest.param(
            [Company.Feature.AUTO_INTEGRA_OPERACAO],
            Company.ModeloVenda.HIBRIDO,
            TaskStatus.Status.SUCCESS,
            {"error": "Empresa não possui a feature flag necessária."},
            id="nao_roda_hibrido",
        ),
        pytest.param(
            [Company.Feature.AUTO_INTEGRA_OPERACAO],
            Company.ModeloVenda.MARKETPLACE,
            TaskStatus.Status.PENDING,
            {"error": "Existe uma tarefa em execução para essa empresa. Aguarde o término."},
            id="nao_roda_com_task_pendente",
        ),
        pytest.param(
            [Company.Feature.BUSCAR_SERVICO],
            Company.ModeloVenda.MARKETPLACE,
            TaskStatus.Status.PENDING,
            {"error": "Empresa não possui a feature flag necessária."},
            id="nao_roda_sem_feature",
        ),
    ],
)
def test_verificar_elegibilidade_auto_integra_operacao(features, modelo_venda, status_task, expected_result):
    company = baker.make(
        Company,
        company_internal_id=310,
        modelo_venda=modelo_venda,
        features=features,
    )
    baker.make(
        TaskStatus,
        company=company,
        status=status_task,
    )
    response = ninja_client.get(
        f"/v1/auto_integra_operacao/verificar_elegibilidade?company_internal_id={company.company_internal_id}"
    )
    assert response.json() == expected_result


def test_auto_integra_operacao_min_rotas_para_integrar():
    company = baker.make(
        Company,
        company_internal_id=310,
        auto_integra_rotas_min=53,
    )
    response = ninja_client.get(
        f"/v1/auto_integra_operacao/get_min_rotas_para_integrar?company_internal_id={company.company_internal_id}"
    )

    assert response.json() == 53


def test_auto_integra_operacao_save_qtd_rotas_para_integrar():
    company = baker.make(
        Company,
        company_internal_id=310,
        auto_integra_rotas_min=53,
    )
    response = ninja_client.post(
        f"/v1/auto_integra_operacao/set_min_rotas_para_integrar?company_internal_id={company.company_internal_id}&qtd_rotas=99"
    )
    assert response.json() == "99"


def test_auto_integra_operacao_save_qtd_rotas_para_integrar_erro_company_does_not_exists():
    response = ninja_client.post(
        "/v1/auto_integra_operacao/set_min_rotas_para_integrar?company_internal_id=1&qtd_rotas=99"
    )
    assert response.status_code == 400

    assert response.json() == {"error": "Company_id 1 não existe."}

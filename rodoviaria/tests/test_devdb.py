import os

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.contrib.auth.models import User

from rodoviaria.management.commands.devdb import command


def test_db_host_invalid(mocker):
    mocker.patch.dict(os.environ, {"DB_HOST": "DBDEPROD"})

    runner = CliRunner()
    result = runner.invoke(command)

    assert result.exit_code == 1
    assert "Só pode rodar devdb em banco de teste" in result.output


def test_model_sem_pk(mocker):
    user_sem_pk = User()
    mocker.patch("rodoviaria.management.commands.devdb.ALL_DATA", [[user_sem_pk]])

    runner = CliRunner()
    result = runner.invoke(command)

    assert result.exit_code == 1
    assert "Todas instâncias devem ter pk definida" in result.output

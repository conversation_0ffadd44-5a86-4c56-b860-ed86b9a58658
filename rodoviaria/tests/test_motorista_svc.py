from datetime import date
from unittest import mock

import pytest
from model_bakery import baker

from rodoviaria.forms.motorista_forms import Motorista as MotoristaForm
from rodoviaria.models.core import Company, Motorista
from rodoviaria.service.exceptions import RodoviariaCompanyNotFoundException
from rodoviaria.service.motorista_svc import escala_motorista_task


@pytest.fixture
def dados_motorista():
    return MotoristaForm.parse_obj(
        {
            "user_id": 5678,
            "nome": "Fulano de Tal",
            "email": "<EMAIL>",
            "telefone": "(11) 91234-5678",
            "cpf": "111.111.111-11",
            "cnh": {
                "numero": "12345678901",
                "validade": "2030-10-20",
                "categoria": "C",
                "orgao_emissor": "SSP",
                "uf": "SP",
            },
            "registro_antt": {"numero": "12345678901234", "validade": "2040-12-22"},
        }
    )


@pytest.fixture
def dados_motorista_alterado():
    return MotoristaForm.parse_obj(
        {
            "user_id": 5678,
            "nome": "Outro Nome",
            "email": "<EMAIL>",
            "telefone": "(99) 99999-9999",
            "cpf": "999.999.999-99",
            "cnh": {
                "numero": "99999999999",
                "validade": "2122-01-01",
                "categoria": "E",
                "orgao_emissor": "Outro orgão",
                "uf": "RJ",
            },
            "registro_antt": {"numero": "99999999999999", "validade": "2133-01-01"},
        }
    )


@pytest.fixture
def dados_motorista_sem_docs():
    return MotoristaForm.parse_obj(
        {
            "user_id": 5678,
            "nome": "Fulano de Tal",
            "email": "<EMAIL>",
            "telefone": "(11) 91234-5678",
            "cpf": "111.111.111-11",
        }
    )


@pytest.fixture
def dados_motorista_alterado_sem_docs():
    return MotoristaForm.parse_obj(
        {
            "user_id": 5678,
            "nome": "Outro Nome",
            "email": "<EMAIL>",
            "telefone": "(99) 99999-9999",
            "cpf": "999.999.999-99",
        }
    )


@pytest.fixture
def motorista_model(vexado_company):
    record = baker.make(
        "rodoviaria.Motorista",
        # relações
        company=vexado_company,
        internal_id=5678,
        external_id_usuario=111,
        external_id_usuario_empresa=222,
        external_id_motorista=333,
        # dados básicos
        nome="Fulano de Tal",
        email="<EMAIL>",
        telefone="11912345678",
        cpf="11111111111",
        # documentos
        cnh_numero="12345678901",
        cnh_validade=date(2030, 10, 20),
        cnh_categoria="C",
        cnh_orgao_emissor="SSP",
        cnh_uf="SP",
        antt_numero="12345678901234",
        antt_validade=date(2040, 12, 22),
    )
    yield record
    record.delete()


@pytest.fixture
def motorista_model_sem_docs(vexado_company):
    record = baker.make(
        "rodoviaria.Motorista",
        # relações
        company=vexado_company,
        internal_id=5678,
        external_id_usuario=111,
        external_id_usuario_empresa=222,
        # dados básicos
        nome="Fulano de Tal",
        email="<EMAIL>",
        telefone="11912345678",
        cpf="11111111111",
    )
    yield record
    record.delete()


@pytest.fixture
def grupo(vexado_company):
    record = baker.make("rodoviaria.Grupo", grupo_internal_id=1234, company_integracao=vexado_company)
    yield record
    record.delete()


@pytest.fixture
def trecho_classe(grupo):
    record = baker.make("rodoviaria.TrechoClasse", grupo=grupo, external_id="6789")
    yield record
    record.delete()


def test_escala_motorista_novo(dados_motorista, grupo, trecho_classe):
    with (
        mock.patch("rodoviaria.api.orchestrator.OrchestrateRodoviaria.cria_motorista") as cria_motorista_mock,
        mock.patch("rodoviaria.api.orchestrator.OrchestrateRodoviaria.escala_motorista") as escala_motorista_mock,
    ):
        cria_motorista_mock.return_value = {
            "id_usuario": 111,
            "id_usuario_empresa": 222,
            "id_motorista": 333,
        }

        escala_motorista_task(dados_motorista.json(), grupo.grupo_internal_id)

        cria_motorista_mock.assert_called_with(dados_motorista)
        escala_motorista_mock.assert_called_with(222, trecho_classe.external_id)

        obj = Motorista.objects.get(
            company=grupo.company_integracao,
            internal_id=5678,
        )
        assert obj.external_id_usuario == 111
        assert obj.external_id_usuario_empresa == 222
        assert obj.external_id_motorista == 333
        assert obj.nome == "Fulano de Tal"
        assert obj.email == "<EMAIL>"
        assert obj.telefone == "11912345678"
        assert obj.cpf == "11111111111"
        assert obj.cnh_numero == "12345678901"
        assert obj.cnh_validade == date(2030, 10, 20)
        assert obj.cnh_categoria == "C"
        assert obj.cnh_orgao_emissor == "SSP"
        assert obj.cnh_uf == "SP"
        assert obj.antt_numero == "12345678901234"
        assert obj.antt_validade == date(2040, 12, 22)


def test_escala_motorista_dados_alterados(dados_motorista_alterado, motorista_model, grupo, trecho_classe):
    with (
        mock.patch(
            "rodoviaria.api.orchestrator.OrchestrateRodoviaria.edita_dados_motorista"
        ) as edita_dados_motorista_mock,
        mock.patch(
            "rodoviaria.api.orchestrator.OrchestrateRodoviaria.edita_documentos_motorista"
        ) as edita_documentos_motorista_mock,
        mock.patch("rodoviaria.api.orchestrator.OrchestrateRodoviaria.escala_motorista") as escala_motorista_mock,
    ):
        escala_motorista_task(dados_motorista_alterado.json(), grupo.grupo_internal_id)

        edita_dados_motorista_mock.assert_called_with(motorista_model.external_id_usuario, dados_motorista_alterado)
        edita_documentos_motorista_mock.assert_called_with(
            motorista_model.external_id_motorista,
            motorista_model.external_id_usuario_empresa,
            dados_motorista_alterado,
        )
        escala_motorista_mock.assert_called_with(motorista_model.external_id_usuario_empresa, trecho_classe.external_id)

        obj = Motorista.objects.get(
            company=grupo.company_integracao,
            internal_id=5678,
        )
        assert obj.external_id_usuario == 111
        assert obj.external_id_usuario_empresa == 222
        assert obj.external_id_motorista == 333
        assert obj.nome == "Outro Nome"
        assert obj.email == "<EMAIL>"
        assert obj.telefone == "99999999999"
        assert obj.cpf == "99999999999"
        assert obj.cnh_numero == "99999999999"
        assert obj.cnh_validade == date(2122, 1, 1)
        assert obj.cnh_categoria == "E"
        assert obj.cnh_orgao_emissor == "Outro orgão"
        assert obj.cnh_uf == "RJ"
        assert obj.antt_numero == "99999999999999"
        assert obj.antt_validade == date(2133, 1, 1)


def test_escala_motorista_marketplace(dados_motorista, motorista_model, grupo, trecho_classe):
    grupo.company_integracao.modelo_venda = Company.ModeloVenda.MARKETPLACE
    grupo.company_integracao.save()
    with pytest.raises(RodoviariaCompanyNotFoundException):
        escala_motorista_task(dados_motorista.json(), grupo.grupo_internal_id)


def test_escala_motorista_novo_sem_docs(dados_motorista_sem_docs, grupo, trecho_classe):
    with (
        mock.patch("rodoviaria.api.orchestrator.OrchestrateRodoviaria.cria_motorista") as cria_motorista_mock,
        mock.patch("rodoviaria.api.orchestrator.OrchestrateRodoviaria.escala_motorista") as escala_motorista_mock,
    ):
        cria_motorista_mock.return_value = {
            "id_usuario": 111,
            "id_usuario_empresa": 222,
            "id_motorista": None,
        }

        escala_motorista_task(dados_motorista_sem_docs.json(), grupo.grupo_internal_id)

        cria_motorista_mock.assert_called_with(dados_motorista_sem_docs)
        escala_motorista_mock.assert_called_with(222, trecho_classe.external_id)

        obj = Motorista.objects.get(
            company=grupo.company_integracao,
            internal_id=5678,
        )
        assert obj.external_id_usuario == 111
        assert obj.external_id_usuario_empresa == 222
        assert obj.external_id_motorista is None
        assert obj.nome == "Fulano de Tal"
        assert obj.email == "<EMAIL>"
        assert obj.telefone == "11912345678"
        assert obj.cpf == "11111111111"
        assert obj.cnh_numero == ""
        assert obj.cnh_validade is None
        assert obj.cnh_categoria == ""
        assert obj.cnh_orgao_emissor == ""
        assert obj.cnh_uf == ""
        assert obj.antt_numero == ""
        assert obj.antt_validade is None


def test_escala_motorista_dados_alterados_sem_docs(
    dados_motorista_alterado_sem_docs, motorista_model_sem_docs, grupo, trecho_classe
):
    with (
        mock.patch(
            "rodoviaria.api.orchestrator.OrchestrateRodoviaria.edita_dados_motorista"
        ) as edita_dados_motorista_mock,
        mock.patch("rodoviaria.api.orchestrator.OrchestrateRodoviaria.escala_motorista") as escala_motorista_mock,
    ):
        escala_motorista_task(dados_motorista_alterado_sem_docs.json(), grupo.grupo_internal_id)

        edita_dados_motorista_mock.assert_called_with(
            motorista_model_sem_docs.external_id_usuario,
            dados_motorista_alterado_sem_docs,
        )
        escala_motorista_mock.assert_called_with(
            motorista_model_sem_docs.external_id_usuario_empresa,
            trecho_classe.external_id,
        )

        obj = Motorista.objects.get(
            company=grupo.company_integracao,
            internal_id=5678,
        )
        assert obj.external_id_usuario == 111
        assert obj.external_id_usuario_empresa == 222
        assert obj.external_id_motorista is None
        assert obj.nome == "Outro Nome"
        assert obj.email == "<EMAIL>"
        assert obj.telefone == "99999999999"
        assert obj.cpf == "99999999999"
        assert obj.cnh_numero == ""
        assert obj.cnh_validade is None
        assert obj.cnh_categoria == ""
        assert obj.cnh_orgao_emissor == ""
        assert obj.cnh_uf == ""
        assert obj.antt_numero == ""
        assert obj.antt_validade is None


def test_escala_motorista_ja_existe_sem_docs(dados_motorista_sem_docs, motorista_model_sem_docs, grupo, trecho_classe):
    with mock.patch("rodoviaria.api.orchestrator.OrchestrateRodoviaria.escala_motorista") as escala_motorista_mock:
        escala_motorista_task(dados_motorista_sem_docs.json(), grupo.grupo_internal_id)
        escala_motorista_mock.assert_called_with(
            motorista_model_sem_docs.external_id_usuario_empresa,
            trecho_classe.external_id,
        )

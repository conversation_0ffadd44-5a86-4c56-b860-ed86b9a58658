import json
from unittest import mock

import pytest
from model_bakery import baker

from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.forms.compra_rodoviaria_forms import DefaultForm, DesbloquearPoltronasForm
from rodoviaria.forms.staff_forms import CheckPaxForm
from rodoviaria.models.core import Grupo, Passagem, TrechoClasse
from rodoviaria.service import reserva_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import RodoviariaTrechoclasseFactoryException


def test_get_map_poltronas_invalido(totalbus_grupos_mockado, totalbus_company, totalbus_login):
    # neste fluxo é utilizado o grupo_internal.company, que é o registro de Company do buser_django.
    # Assim, é preciso que o id seja o company_internal_id pro teste não falhar.
    trecho_classe_buser_django_infos = totalbus_grupos_mockado.ida.trecho_classe_infos
    with mock.patch.object(
        CompraRodoviariaSVC,
        "_get_internal_grupo",
        return_value=totalbus_grupos_mockado.ida.grupo,
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
        return_value=trecho_classe_buser_django_infos,
    ):
        with pytest.raises(RodoviariaTrechoclasseFactoryException):
            form = DefaultForm(trechoclasse_id=-999, force_renew_link=True)
            CompraRodoviariaSVC(form).get_map_poltronas(form)


def test_add_pax_na_lista_task():
    passageiro = CheckPaxForm.parse_obj(
        {
            "id_destino": 514,
            "id_origem": 638,
            "passenger": {
                "buseiro_id": 341475,
                "cpf": "11975923774",
                "name": "Nome Teste",
                "phone": "21969200000",
                "rg_number": "8885000",
            },
            "travel_id": 5409394,
            "trechoclasse_id": 13984203,
            "valor_por_buseiro": 85.0,
        }
    )
    with mock.patch("rodoviaria.service.reserva_svc.add_pax_na_lista") as mock_add:
        reserva_svc.add_pax_na_lista_grupo_task([passageiro.json()])
        mock_add.assert_called_once_with(passageiro)


def test_efetua_cancelamento_cancela_travel():
    travel_internal_id = 5932
    buseiro_internal_id = None
    trecho_classe_internal_id = 83492
    trecho_classe = baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=trecho_classe_internal_id)
    baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=travel_internal_id,
        trechoclasse_integracao=trecho_classe,
        status=Passagem.Status.CONFIRMADA,
    )
    with mock.patch("rodoviaria.service.reserva_svc.efetua_cancelamento") as mock_efetua_cancelamento:
        response = reserva_svc.efetua_cancelamento(travel_internal_id, buseiro_internal_id)
    assert response == mock_efetua_cancelamento.return_value
    mock_efetua_cancelamento.assert_called_once_with(travel_internal_id, buseiro_internal_id)


def test_efetua_cancelamento_travel_nao_encontrada():
    travel_internal_id = 5932
    buseiro_internal_id = 8492
    trecho_classe_internal_id = 83492
    baker.make("rodoviaria.TrechoClasse", trechoclasse_internal_id=trecho_classe_internal_id)
    response = reserva_svc.efetua_cancelamento(travel_internal_id, buseiro_internal_id)
    assert response.status_code == 200
    assert json.loads(response.content) == {
        "message": f"Travel {travel_internal_id} não encontrada no banco do rodoviaria"
    }


def test_desbloquear_poltronas(eulabs_company):
    trecho_classe_id = 4231
    poltronas = [12, 13]
    with mock.patch.object(CompraRodoviariaSVC, "_get_grupo"), mock.patch.object(
        CompraRodoviariaSVC,
        "_get_company_id",
        return_value=eulabs_company.company_internal_id,
    ), mock.patch.object(OrchestrateRodoviaria, "desbloquear_poltronas") as mock_desbloquear_poltronas:
        mock_desbloquear_poltronas.return_value = {}
        form = DesbloquearPoltronasForm(trechoclasse_id=trecho_classe_id, poltronas=poltronas)
        CompraRodoviariaSVC(form).desbloquear_poltronas(form)
    mock_desbloquear_poltronas.assert_called_once_with(trecho_classe_id, poltronas)


def test_get_map_poltronas(eulabs_company, mocker):
    trecho_classe_id = 4231
    baker.make(
        TrechoClasse,
        grupo=baker.make(Grupo, company_integracao=eulabs_company),
        trechoclasse_internal_id=trecho_classe_id,
    )
    mocker.patch.object(CompraRodoviariaSVC, "_get_grupo")
    mocker.patch.object(
        CompraRodoviariaSVC,
        "_get_company_id",
        return_value=eulabs_company.company_internal_id,
    )
    mock_get_map_poltronas = mocker.patch.object(OrchestrateRodoviaria, "get_map_poltronas")
    mock_get_map_poltronas.return_value = {}
    form = DefaultForm(trechoclasse_id=trecho_classe_id, force_renew_link=True)

    CompraRodoviariaSVC(form).get_map_poltronas(form)

    mock_get_map_poltronas.assert_called_once_with(trecho_classe_id)


def test_efetua_cancelamento_empresa_inativa(
    totalbus_trechoclasses, mock_totalbus_cancelar_login_inativo, totalbus_login
):
    travel_internal_id = 5932
    buseiro_internal_id = None
    passagem = baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=travel_internal_id,
        trechoclasse_integracao=totalbus_trechoclasses.ida,
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id="asdfasd",
        company_integracao=totalbus_login.company,
    )
    reserva_svc.efetua_cancelamento(travel_internal_id, buseiro_internal_id)
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CANCELADA
    assert Passagem.objects.filter(tags__name="cancelada_login_inativo").count() == 1


def test_efetua_cancelamento_empresa_inativa_missing_config(
    totalbus_trechoclasses, mock_totalbus_cancelar_missing_config, totalbus_login
):
    travel_internal_id = 5932
    buseiro_internal_id = None
    passagem = baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=travel_internal_id,
        trechoclasse_integracao=totalbus_trechoclasses.ida,
        status=Passagem.Status.CONFIRMADA,
        pedido_external_id="asdfasd",
        company_integracao=totalbus_login.company,
    )
    reserva_svc.efetua_cancelamento(travel_internal_id, buseiro_internal_id)
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CANCELADA
    assert Passagem.objects.filter(tags__name="cancelada_login_inativo").count() == 1

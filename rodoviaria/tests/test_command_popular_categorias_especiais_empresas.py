from django.core.management import call_command

from rodoviaria.models.core import CompanyCategoriaEspecial
from rodoviaria.tests.totalbus.conftest import mock_consultar_categoria_empresa

totalbus_mock_consultar_categoria_empresa = mock_consultar_categoria_empresa


def test_command_popula_categorias_especiais(totalbus_login, totalbus_mock_consultar_categoria_empresa):
    call_command("popular_categorias_especiais_empresas")
    qtd_categorias = CompanyCategoriaEspecial.objects.all().count()

    assert qtd_categorias == 17


def test_command_nao_duplica_registros(totalbus_login, totalbus_mock_consultar_categoria_empresa):
    call_command("popular_categorias_especiais_empresas")
    call_command("popular_categorias_especiais_empresas")
    qtd_categorias = CompanyCategoriaEspecial.objects.all().count()
    assert qtd_categorias == 17

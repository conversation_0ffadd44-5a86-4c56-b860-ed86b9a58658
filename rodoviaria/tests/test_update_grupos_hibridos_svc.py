from datetime import datetime, timed<PERSON><PERSON>
from unittest import mock

import pytest
from django.db.models import F
from model_bakery import baker

from commons.dateutils import to_default_tz
from rodoviaria.api.vexado import models as vexado_models
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.models.core import Company, Grupo
from rodoviaria.models.vexado import VexadoGrupoClasse
from rodoviaria.service import update_grupos_hibridos_svc


def test_link_vexado_grupo_classe_encontra_pelo_external_id_e_deleta_grupo_classe_antigo():
    veiculo_id = 5423
    veiculo_andar = 1
    rota_external_id = 1427
    grupo_classe_external_id = 89239
    company = baker.make("rodoviaria.Company")
    veiculo = baker.make("rodoviaria.Veiculo", id_external=veiculo_id, company=company)
    grupo = baker.make("rodoviaria.Grupo", company_integracao=company)
    grupo_classe_1 = baker.make("rodoviaria.GrupoClasse", grupo=grupo)
    grupo_classe_2 = baker.make("rodoviaria.GrupoClasse", grupo=grupo)
    grupo_classe_vexado = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_1,
        grupo_classe_external_id=grupo_classe_external_id,
    )
    response = update_grupos_hibridos_svc.link_vexado_grupo_classe(
        grupo_classe_2,
        company,
        veiculo_id,
        veiculo_andar,
        grupo_classe_external_id,
        rota_external_id,
    )
    assert response.id == grupo_classe_vexado.id
    assert response.grupo_classe_external_id == grupo_classe_vexado.grupo_classe_external_id
    assert response.rota_external_id == rota_external_id
    assert response.veiculo == veiculo
    assert response.andar == veiculo_andar
    assert response.grupo_classe == grupo_classe_2
    assert response.status == "created"


def test_link_vexado_grupo_classe_encontra_pelo_grupo_classe():
    veiculo_id = 5423
    veiculo_andar = 1
    rota_external_id = 1427
    grupo_classe_external_id = 89239
    company = baker.make("rodoviaria.Company")
    veiculo = baker.make("rodoviaria.Veiculo", id_external=veiculo_id, company=company)
    grupo_classe = baker.make("rodoviaria.GrupoClasse")
    grupo_classe_vexado = baker.make("rodoviaria.VexadoGrupoClasse", grupo_classe=grupo_classe)
    response = update_grupos_hibridos_svc.link_vexado_grupo_classe(
        grupo_classe,
        company,
        veiculo_id,
        veiculo_andar,
        grupo_classe_external_id,
        rota_external_id,
    )
    assert response.id == grupo_classe_vexado.id
    assert response.grupo_classe_external_id == grupo_classe_external_id
    assert response.rota_external_id == rota_external_id
    assert response.veiculo == veiculo
    assert response.andar == veiculo_andar
    assert response.grupo_classe == grupo_classe
    assert response.status == "created"


def test_link_vexado_grupo_classe_create():
    veiculo_id = 5423
    veiculo_andar = 1
    rota_external_id = 1427
    grupo_classe_external_id = 89239
    company = baker.make("rodoviaria.Company")
    veiculo = baker.make("rodoviaria.Veiculo", id_external=veiculo_id, company=company)
    grupo_classe = baker.make("rodoviaria.GrupoClasse")
    response = update_grupos_hibridos_svc.link_vexado_grupo_classe(
        grupo_classe,
        company,
        veiculo_id,
        veiculo_andar,
        grupo_classe_external_id,
        rota_external_id,
    )
    assert response.veiculo == veiculo
    assert response.grupo_classe == grupo_classe
    assert response.andar == veiculo_andar
    assert response.grupo_classe_external_id == grupo_classe_external_id
    assert response.rota_external_id == rota_external_id
    assert response.status == "created"


def test_link_vexado_grupo_classe_create_nao_encontra_veiculo():
    veiculo_id = 3245
    veiculo_andar = 1
    rota_external_id = 1427
    grupo_classe_external_id = 89239
    company = baker.make("rodoviaria.Company")
    baker.make("rodoviaria.Veiculo", id_external=5423, company=company)
    grupo_classe = baker.make("rodoviaria.GrupoClasse")
    response = update_grupos_hibridos_svc.link_vexado_grupo_classe(
        grupo_classe,
        company,
        veiculo_id,
        veiculo_andar,
        grupo_classe_external_id,
        rota_external_id,
    )
    assert response.veiculo is None
    assert response.grupo_classe == grupo_classe
    assert response.andar == veiculo_andar
    assert response.grupo_classe_external_id == grupo_classe_external_id
    assert response.rota_external_id == rota_external_id
    assert response.status == "created"


def test_update_grupos_hibridos_criados():
    datetime_now = to_default_tz(datetime(2022, 1, 1, 15, 30))
    company = baker.make("rodoviaria.Company", company_internal_id=3024)
    grupo = baker.make(
        "rodoviaria.Grupo",
        company_integracao=company,
        datetime_ida=datetime_now + timedelta(days=2),
    )
    grupo_classe = baker.make(
        "rodoviaria.GrupoClasse",
        grupo=grupo,
        tipo_assento_internal="semi leito",
    )
    vexado_grupo_classe = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        rota_external_id=593,
        grupo_classe=grupo_classe,
        status="pending",
    )
    with mock.patch("rodoviaria.service.update_grupos_hibridos_svc.timezone.now") as mock_now, mock.patch(
        "rodoviaria.service.update_grupos_hibridos_svc._update_grupos_pendentes"
    ) as mock_update_grupos_pendentes:
        mock_now.return_value = datetime_now
        update_grupos_hibridos_svc.update_grupos_hibridos_criados(company.company_internal_id)
    mock_update_grupos_pendentes.assert_called_once_with(company.company_internal_id, {593: [vexado_grupo_classe]})


def test_update_grupos_pendentes(vexado_company, vexado_login):
    datetime_now = to_default_tz(datetime(2022, 1, 1, 15, 30))
    company = vexado_company
    grupo = baker.make(
        "rodoviaria.Grupo",
        company_integracao=company,
        datetime_ida=datetime_now + timedelta(days=2),
    )
    grupo = baker.make(
        "rodoviaria.Grupo",
        company_integracao=company,
        datetime_ida=datetime_now + timedelta(days=2),
    )
    grupo_classe = baker.make("rodoviaria.GrupoClasse", grupo=grupo, tipo_assento_internal="semi leito")
    grupo_classe_2 = baker.make("rodoviaria.GrupoClasse", grupo=grupo, tipo_assento_internal="leito")
    vexado_grupo_classe = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        rota_external_id=50233,
        grupo_classe=grupo_classe,
        status="pending",
    )
    vexado_grupo_classe_2 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        rota_external_id=50233,
        grupo_classe=grupo_classe_2,
        status="pending",
    )
    with mock.patch.object(VexadoAPI, "viagens_por_rota") as mock_viagens_por_rota:
        mock_viagens_por_rota.return_value = [
            vexado_models.SimplifiedViagensForm(
                id=3955,
                dataHoraPartida="31/01/2022 18:30",
                rotaDto={"id": 594213},
                tipoPreco={"tipoPreco": "LEITO"},
                veiculoDto={"id": 94231},
                andar=1,
            ),
            vexado_models.SimplifiedViagensForm(
                id=2954,
                dataHoraPartida="03/01/2022 15:30",
                rotaDto={"id": 50233},
                tipoPreco={"tipoPreco": "SEMI_LEITO"},
                veiculoDto={"id": 8549},
                andar=1,
            ),
        ]
        vexado_grupos_classe_annotate = (
            VexadoGrupoClasse.objects.annotate(datetime_ida=F("grupo_classe__grupo__datetime_ida"))
            .annotate(tipo_assento=F("grupo_classe__tipo_assento_internal"))
            .filter(id__in=[vexado_grupo_classe.id, vexado_grupo_classe_2.id])
        )
        response = update_grupos_hibridos_svc._update_grupos_pendentes(
            company.company_internal_id, {50233: vexado_grupos_classe_annotate}
        )
    vexado_grupo_classe.refresh_from_db()
    assert vexado_grupo_classe.grupo_classe_external_id == 2954
    assert response == {
        "('2022-01-03 15:30', 50233, 'semi leito')": "ok",
        "('2022-01-03 15:30', 50233, 'leito')": "nao encontrado",
    }


def test_fill_grupo_classe_external_id():
    grupo_classe_1 = baker.make("rodoviaria.GrupoClasse")
    grupo_classe_2 = baker.make("rodoviaria.GrupoClasse")
    grupo_classe_3 = baker.make("rodoviaria.GrupoClasse")
    baker.make("rodoviaria.TrechoClasse", grupo_classe=grupo_classe_1, external_id=1111)
    baker.make("rodoviaria.TrechoClasse", grupo_classe=grupo_classe_2, external_id=2222)
    vexado_grupo_classe_1 = baker.make("rodoviaria.VexadoGrupoClasse", grupo_classe=grupo_classe_1)
    vexado_grupo_classe_2 = baker.make("rodoviaria.VexadoGrupoClasse", grupo_classe=grupo_classe_2)
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_3,
        grupo_classe_external_id=3333,
    )
    response = update_grupos_hibridos_svc.fill_grupo_classe_external_id()
    vexado_grupo_classe_1.refresh_from_db()
    vexado_grupo_classe_2.refresh_from_db()
    assert vexado_grupo_classe_1.grupo_classe_external_id == 1111
    assert vexado_grupo_classe_2.grupo_classe_external_id == 2222
    assert response == {"grupos_classes_atualizados": 2}


def test_fetch_grupos_criados_anteriormente(vexado_company, vexado_login):
    datetime_now = to_default_tz(datetime(2022, 2, 10, 15, 0))  # '10/02/2022 15:00'
    # vexado_grupo_classe já criado
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe_external_id=5032,
        rota_external_id=998877,
        grupo_classe=baker.make(
            "rodoviaria.GrupoClasse",
            tipo_assento_internal="semi leito",
            grupo=baker.make(
                "rodoviaria.Grupo",
                datetime_ida=datetime_now + timedelta(days=1),
                company_integracao=vexado_company,
            ),
        ),
    )
    grupos_count = Grupo.objects.count()
    with mock.patch.object(VexadoAPI, "viagens_por_rota") as mock_viagens_por_rota, mock.patch.object(
        VexadoAPI, "buscar_rotas"
    ) as mock_buscar_rotas, mock.patch("rodoviaria.service.update_grupos_hibridos_svc.timezone.now") as mock_now:
        mock_now.return_value = datetime_now
        mock_buscar_rotas.return_value = [{"id": 998877}]
        mock_viagens_por_rota.return_value = [
            vexado_models.SimplifiedViagensForm(
                id=3955,
                dataHoraPartida="31/01/2022 18:30",
                rotaDto={"id": 998877},
                tipoPreco={"tipoPreco": "LEITO"},
                veiculoDto={"id": 94231},
                andar=1,
            ),
            vexado_models.SimplifiedViagensForm(
                id=2954,
                dataHoraPartida="15/02/2022 15:30",
                rotaDto={"id": 998877},
                tipoPreco={"tipoPreco": "SEMI_LEITO"},
                veiculoDto={"id": 8549},
                andar=1,
            ),
            vexado_models.SimplifiedViagensForm(
                id=2955,
                dataHoraPartida="15/02/2022 15:30",
                rotaDto={"id": 998877},
                tipoPreco={"tipoPreco": "LEITO"},
                veiculoDto={"id": 8549},
                andar=1,
            ),
            vexado_models.SimplifiedViagensForm(
                id=5032,
                dataHoraPartida="11/02/2022 15:00",
                rotaDto={"id": 998877},
                tipoPreco={"tipoPreco": "SEMI_LEITO"},
                veiculoDto={"id": 8549},
                andar=1,
            ),
        ]
        assert VexadoGrupoClasse.objects.filter(rota_external_id=998877).count() == 1
        response = update_grupos_hibridos_svc.fetch_grupos_criados_anteriormente(vexado_company.company_internal_id)
        assert VexadoGrupoClasse.objects.filter(rota_external_id=998877).count() == 3
        assert response == {"grupos_classes_criados": 2}
        assert Grupo.objects.count() == grupos_count + 1


def test_fetch_grupos_criados_anteriormente_company_marketplace(vexado_company, vexado_login):
    vexado_company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_company.save()
    with pytest.raises(Company.DoesNotExist):
        update_grupos_hibridos_svc.fetch_grupos_criados_anteriormente(vexado_company.company_internal_id)


def test_update_grupos_hibridos_criados_todas_empresas():
    company_1 = baker.make("rodoviaria.Company", company_internal_id=1234)
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=baker.make(
            "rodoviaria.GrupoClasse",
            grupo=baker.make("rodoviaria.Grupo", company_integracao=company_1),
        ),
        status="created",
    )
    company_2 = baker.make("rodoviaria.Company", company_internal_id=5678)
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=baker.make(
            "rodoviaria.GrupoClasse",
            grupo=baker.make("rodoviaria.Grupo", company_integracao=company_2),
        ),
        status="pending",
    )
    with mock.patch(
        "rodoviaria.service.update_grupos_hibridos_svc.update_grupos_hibridos_criados"
    ) as mock_update_grupos_hibridos_criados:
        update_grupos_hibridos_svc.update_grupos_hibridos_criados_todas_empresas()
    mock_update_grupos_hibridos_criados.assert_called_once_with(5678)

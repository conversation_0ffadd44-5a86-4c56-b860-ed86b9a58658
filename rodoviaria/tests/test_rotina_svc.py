from datetime import datetime, timed<PERSON><PERSON>
from unittest import mock

import pytest
from django.core.management import call_command
from model_bakery import baker
from simple_token_bucket import SimpleTokenBucket

from commons.dateutils import to_default_tz, to_tz, today_midnight
from rodoviaria.models.core import Company, Rotina
from rodoviaria.service import rotina_svc
from rodoviaria.service.exceptions import RodoviariaCompanyNotFoundException, RodoviariaRotaNotFoundException


@pytest.fixture(autouse=True)
def infinite_token_bucket():
    # TODO: tech-debt testes deveriam usar o NullTokenBucket
    with mock.patch.object(SimpleTokenBucket, "try_get_token", return_value=True):
        yield


def test_fetch_rotina(
    rota_totalbus,
    totalbus_mock_buscar_itinerario,
    mock_buscar_todos_servicos_totalbus_response,
):
    next_days = 15
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotina = rotina_svc.fetch_rotina(rodoviaria_rota_id=rota_totalbus.id, next_days=next_days)
        assert rotina["empresa"] == "Totalbus Transporte"
        assert rotina["empresa_internal_id"] == rota_totalbus.company.company_internal_id
        assert rotina["id_hash"] == rota_totalbus.id_hash
        assert rotina["id_internal"] == rota_totalbus.id_internal
        assert rotina["id_external"] == rota_totalbus.id_external
        assert "05:30" in rotina["rotina"]
        assert rotina["rotina"]["05:30"][0]["dia"] == "ter"
        assert "2020-05-26" in rotina["rotina"]["05:30"][0]["datas"]
        last_day = (
            datetime.strptime(rotina["intervalo_busca"]["primeiro"], "%Y-%m-%d") + timedelta(days=next_days - 1)
        ).strftime("%Y-%m-%d")
        assert last_day == rotina["intervalo_busca"]["ultimo"]
        assert rotina == rotina_svc._fetch_rotina(
            rota=rota_totalbus,
            next_days=next_days,
            first_day=to_default_tz(datetime.now()),
        )


def test_fetch_rotina_nao_localizada(
    rota_totalbus,
    totalbus_mock_buscar_itinerario_nao_localizado,
    mock_buscar_todos_servicos_totalbus_response,
):
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        first_day = to_default_tz(datetime.now()) - timedelta(days=2)
        rotina = rotina_svc.fetch_rotina(rodoviaria_rota_id=rota_totalbus.id, first_day=first_day)
        assert rotina["empresa"] == "Totalbus Transporte"
        assert rotina["empresa_internal_id"] == rota_totalbus.company.company_internal_id
        assert rotina["id_hash"] == rota_totalbus.id_hash
        assert rotina["id_internal"] == rota_totalbus.id_internal
        assert rotina["id_external"] == rota_totalbus.id_external
        assert rotina["rotina"] == {}
        last_day = (datetime.strptime(rotina["intervalo_busca"]["primeiro"], "%Y-%m-%d") + timedelta(days=13)).strftime(
            "%Y-%m-%d"
        )
        assert last_day == rotina["intervalo_busca"]["ultimo"]
        assert today_midnight().strftime("%Y-%m-%d") == rotina["intervalo_busca"]["primeiro"]


def test_fetch_rotina_rota_nao_existente():
    with pytest.raises(RodoviariaRotaNotFoundException):
        rotina_svc.fetch_rotina(rodoviaria_rota_id=1)


def test_fetch_rotina_no_local_checkpoint_error(
    rota_totalbus,
    totalbus_mock_buscar_itinerario_nao_localizado,
    mock_buscar_todos_servicos_totalbus_response,
    caplog,
):
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        first_checkpoint = rota_totalbus.first_checkpoint()
        first_checkpoint.local = None
        first_checkpoint.save()
        rotina_svc.fetch_rotina(rodoviaria_rota_id=rota_totalbus.id)
        assert f"first_checkpoint_timezone não encontrado para rota {rota_totalbus.id}" in caplog.messages[0]


def test_fetch_rotina_no_timezone_checkpoint_error(
    rota_totalbus,
    totalbus_mock_buscar_itinerario_nao_localizado,
    mock_buscar_todos_servicos_totalbus_response,
    caplog,
):
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        first_checkpoint_cidade = rota_totalbus.first_checkpoint().local.cidade
        first_checkpoint_cidade.timezone = None
        first_checkpoint_cidade.save()
        rotina_svc.fetch_rotina(rodoviaria_rota_id=rota_totalbus.id)
        assert f"first_checkpoint_timezone não encontrado para rota {rota_totalbus.id}" in caplog.messages[0]


def test_fetch_rotina_by_internal_id_rota_nao_existente():
    with pytest.raises(RodoviariaRotaNotFoundException):
        rotina_svc.fetch_rotina_by_internal_id(rota_id=1)


def test_fetch_rotina_by_internal_id(
    rota_totalbus,
    totalbus_mock_buscar_itinerario,
    mock_buscar_todos_servicos_totalbus_response,
):
    next_days = 1
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        first_day = to_default_tz(datetime.now()) + timedelta(days=2)
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotina = rotina_svc.fetch_rotina_by_internal_id(
            rota_id=rota_totalbus.id_internal, next_days=next_days, first_day=first_day
        )
        assert rotina["empresa"] == "Totalbus Transporte"
        assert rotina["empresa_internal_id"] == rota_totalbus.company.company_internal_id
        assert rotina["id_hash"] == rota_totalbus.id_hash
        assert rotina["id_internal"] == rota_totalbus.id_internal
        assert rotina["id_external"] == rota_totalbus.id_external
        assert "05:30" in rotina["rotina"]
        assert rotina["rotina"]["05:30"][0]["dia"] == "ter"
        assert "2020-05-26" in rotina["rotina"]["05:30"][0]["datas"]
        last_day = (
            datetime.strptime(rotina["intervalo_busca"]["primeiro"], "%Y-%m-%d") + timedelta(days=next_days - 1)
        ).strftime("%Y-%m-%d")
        assert last_day == rotina["intervalo_busca"]["ultimo"]


def test_fetch_rotina_by_internal_id_nao_localizada(
    rota_totalbus,
    totalbus_mock_buscar_itinerario_nao_localizado,
    mock_buscar_todos_servicos_totalbus_response,
):
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        first_day = to_default_tz(datetime.now()) - timedelta(days=2)
        rotina = rotina_svc.fetch_rotina_by_internal_id(rota_id=rota_totalbus.id_internal, first_day=first_day)
        assert rotina["empresa"] == "Totalbus Transporte"
        assert rotina["empresa_internal_id"] == rota_totalbus.company.company_internal_id
        assert rotina["id_hash"] == rota_totalbus.id_hash
        assert rotina["id_internal"] == rota_totalbus.id_internal
        assert rotina["id_external"] == rota_totalbus.id_external
        assert rotina["rotina"] == {}
        last_day = (datetime.strptime(rotina["intervalo_busca"]["primeiro"], "%Y-%m-%d") + timedelta(days=13)).strftime(
            "%Y-%m-%d"
        )
        assert last_day == rotina["intervalo_busca"]["ultimo"]


def test_get_rotina(rota_totalbus):
    dt = datetime.now().replace(hour=3, minute=30)
    baker.make(
        "rodoviaria.Rotina",
        rota=rota_totalbus,
        datetime_ida=to_default_tz(dt + timedelta(days=25)),
        ativo=False,
    )
    rotina = rotina_svc.get_rotina(rodoviaria_rota_id=rota_totalbus.id)
    assert rotina["empresa"] == "Totalbus Transporte"
    assert rotina["empresa_internal_id"] == rota_totalbus.company.company_internal_id
    assert rotina["id_hash"] == rota_totalbus.id_hash
    assert rotina["id_internal"] == rota_totalbus.id_internal
    assert rotina["id_external"] == rota_totalbus.id_external
    assert any(rotina["rotina"].values())
    first_day = (datetime.strptime(rotina["intervalo_busca"]["ultimo"], "%Y-%m-%d") - timedelta(days=29)).strftime(
        "%Y-%m-%d"
    )
    assert first_day == rotina["intervalo_busca"]["primeiro"]
    assert dt.strftime("%H:%M") not in rotina["rotina"]
    assert rotina == rotina_svc._get_rotina(rota=rota_totalbus, next_days=30)


def test_get_rotina_by_internal_id(rota_totalbus):
    next_days = 7
    rotina = rotina_svc.get_rotina_by_internal_id(rota_id=rota_totalbus.id_internal, next_days=next_days)
    assert rotina["empresa_internal_id"] == rota_totalbus.company.company_internal_id
    assert rotina["id_internal"] == rota_totalbus.id_internal
    assert any(rotina["rotina"].values())
    first_day = (
        datetime.strptime(rotina["intervalo_busca"]["ultimo"], "%Y-%m-%d") - timedelta(days=next_days - 1)
    ).strftime("%Y-%m-%d")
    assert first_day == rotina["intervalo_busca"]["primeiro"]


def test_get_rotina_rota_nao_existente():
    with pytest.raises(RodoviariaRotaNotFoundException):
        rotina_svc.get_rotina(rodoviaria_rota_id=1)


def test_get_rotina_by_internal_id_rota_nao_existente():
    with pytest.raises(RodoviariaRotaNotFoundException):
        rotina_svc.get_rotina_by_internal_id(rota_id=1)


def test__get_rotina_response(rota_totalbus):
    datetime_idas_tz = [to_default_tz(datetime.now())]
    rotina_response = rotina_svc._get_rotina_response(rota=rota_totalbus, datetime_idas_tz=datetime_idas_tz)
    day = rotina_svc.DIAS_SEMANA[datetime_idas_tz[0].weekday()]
    horario = datetime_idas_tz[0].strftime("%H:%M")
    data = datetime_idas_tz[0].strftime("%Y-%m-%d")
    assert day == rotina_response["rotina"][horario][0]["dia"]
    assert data in rotina_response["rotina"][horario][0]["datas"]


def test__parse_hits(rota_totalbus):
    datetime_idas_tz = [(to_default_tz(datetime.now()) + timedelta(days=d, hours=d)) for d in range(3, 0, -1)]

    hits = rotina_svc._parse_hits(datetime_idas_tz)
    day = rotina_svc.DIAS_SEMANA[datetime_idas_tz[0].weekday()]
    horario = datetime_idas_tz[0].strftime("%H:%M")
    data = datetime_idas_tz[0].strftime("%Y-%m-%d")
    assert day == hits[horario][0]["dia"]
    assert data in hits[horario][0]["datas"]


def test_command_fetch_rotinas(totalbus_company):
    with mock.patch("rodoviaria.service.rotina_svc.fetch_rotinas_empresa") as mock_fetch_rotinas_empresa:
        company_id = totalbus_company.company_internal_id
        next_days = 7
        kwargs = {"company_id": company_id, "next_days": next_days}
        call_command("fetch_rotinas", **kwargs)
        mock_fetch_rotinas_empresa.assert_called_once_with(company_internal_id=company_id, next_days=next_days)


def test_fetch_rotinas_empresa_subtask(
    totalbus_login,
    rota_totalbus,
    mock_buscar_todos_servicos_totalbus_response,
    totalbus_mock_buscar_itinerario_variable,
):
    company_internal_id = totalbus_login.company.company_internal_id
    next_days = 14
    first_day = to_default_tz(datetime.now())
    rota_totalbus.company = totalbus_login.company
    rota_totalbus.save()

    num_rotinas = Rotina.objects.count()

    with mock.patch(
        "rodoviaria.service.rotina_totalbus_async_svc._fetch_todos_servicos",
        return_value=mock_buscar_todos_servicos_totalbus_response.cleaned,
    ):
        rotina_svc.fetch_rotinas_empresa(company_internal_id, first_day, next_days)
        assert Rotina.objects.count() == num_rotinas + 2


def test_fetch_rotinas_empresa(totalbus_login):
    company_internal_id = totalbus_login.company.company_internal_id
    first_day = to_default_tz(datetime.now())
    next_days = 14
    totalbus_login.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_login.company.save()
    with pytest.raises(RodoviariaCompanyNotFoundException):
        rotina_svc.fetch_rotinas_empresa(company_internal_id, first_day, next_days)


def test_atualiza_rotinas_com_base_nas_novas_inativa_antigas(rota_mock, mock_rotinas):
    novas_rotinas_utc = [
        to_tz(today_midnight(), "UTC"),
        to_tz(today_midnight() + timedelta(days=4), "UTC"),
    ]
    next_days = 30
    (r1, r2, r3) = (
        mock_rotinas.rotinas[0],
        mock_rotinas.rotinas[1],
        mock_rotinas.rotinas[2],
    )
    r1.datetime_ida = to_tz(today_midnight() + timedelta(days=1), "UTC")
    r2.datetime_ida = to_tz(today_midnight() + timedelta(days=2), "UTC")
    r3.datetime_ida = to_tz(today_midnight() + timedelta(days=3), "UTC")
    r1.save()
    r2.save()
    r3.save()

    rotinas = Rotina.objects.filter(rota_id=rota_mock, ativo=True)
    assert len(rotinas) == len(mock_rotinas.rotinas)

    rota_mock.atualiza_rotinas_com_base_nas_novas(novas_rotinas_utc, next_days)

    rotinas = Rotina.objects.filter(rota_id=rota_mock, ativo=True)
    assert len(rotinas) == 0  # Todas inativadas


def test_atualiza_rotinas_com_base_nas_novas_reativa_antigas(rota_mock, mock_rotinas):
    dt1 = to_tz(today_midnight() + timedelta(days=1), "UTC")
    dt2 = to_tz(today_midnight() + timedelta(days=2), "UTC")
    dt3 = to_tz(today_midnight() + timedelta(days=3), "UTC")
    novas_rotinas_utc = [
        to_tz(today_midnight(), "UTC"),
        dt1,
        dt2,
        dt3,
        to_tz(today_midnight() + timedelta(days=4), "UTC"),
    ]
    next_days = 30
    (r1, r2, r3) = (
        mock_rotinas.rotinas[0],
        mock_rotinas.rotinas[1],
        mock_rotinas.rotinas[2],
    )
    r1.datetime_ida = dt1
    r2.datetime_ida = dt2
    r3.datetime_ida = dt3
    r1.ativo = False
    r2.ativo = False
    r3.ativo = False
    r1.save()
    r2.save()
    r3.save()

    rotinas = Rotina.objects.filter(rota_id=rota_mock, ativo=True)
    assert len(rotinas) == 0

    rota_mock.atualiza_rotinas_com_base_nas_novas(novas_rotinas_utc, next_days)

    rotinas = Rotina.objects.filter(rota_id=rota_mock, ativo=True)
    assert len(rotinas) == len(mock_rotinas.rotinas)  # Todas reativadas


def test_atualiza_rotinas_com_base_nas_novas_reativa_uma_rotina_apenas(rota_mock):
    dt1 = to_tz(today_midnight() + timedelta(days=1, hours=12), "UTC")
    novas_rotinas_utc = [dt1]
    next_days = 30
    r1 = baker.make(Rotina, datetime_ida=dt1, rota=rota_mock, ativo=False)

    rotinas = Rotina.objects.filter(rota_id=rota_mock, ativo=True)
    assert len(rotinas) == 0

    rota_mock.atualiza_rotinas_com_base_nas_novas(novas_rotinas_utc, next_days)

    r1.refresh_from_db()
    assert r1.ativo is True


def cria_rotinas(rota_totalbus, base_date, classes, qtd_rotinas, qtd_trechos_por_rotina, asc=True):
    rotinas = []
    r = range(qtd_rotinas)
    if not asc:
        r = range(qtd_rotinas, 0, -1)
    for x in r:
        try:
            rotina = Rotina.objects.get(
                rota=rota_totalbus,
                datetime_ida=to_default_tz(base_date + timedelta(days=(7 * x))),
            )
        except Exception:
            rotina = baker.make(
                "rodoviaria.Rotina",
                rota=rota_totalbus,
                datetime_ida=to_default_tz(base_date + timedelta(days=(7 * x))),
                ativo=True,
            )
        trechos = []
        dt_tc = rotina.datetime_ida
        for y in range(qtd_trechos_por_rotina):
            for c in classes:
                trecho = baker.make(
                    "rodoviaria.RotinaTrechoVendido",
                    rotina=rotina,
                    datetime_ida_trecho_vendido=dt_tc,
                    trecho_vendido=baker.make(
                        "rodoviaria.TrechoVendido",
                        rota=rota_totalbus,
                        id_internal=y,
                        classe=c,
                        capacidade_classe=12,
                        preco=60.00,
                        origem=baker.make(
                            "rodoviaria.LocalEmbarque",
                            local_embarque_internal_id=12,
                            cidade=baker.make("rodoviaria.Cidade", timezone="America/Sao_Paulo"),
                        ),
                        destino=baker.make(
                            "rodoviaria.LocalEmbarque",
                            local_embarque_internal_id=13,
                            cidade=baker.make("rodoviaria.Cidade", timezone="America/Sao_Paulo"),
                        ),
                    ),
                )
                trechos.append(trecho)
            dt_tc = dt_tc + timedelta(hours=1, minutes=30)
        rotinas.append((rotina, trechos))

    dia = rotina_svc.DIAS_SEMANA[base_date.weekday()]
    hora = base_date.strftime("%H:%M")
    return dia, hora, rotinas


def test_get_rotinas_inativas_por_rota_integrada_ativa():
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=123944,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    rota = baker.make("rodoviaria.Rota", id_internal=89741, ativo=True, company=company)
    r = baker.make(
        "rodoviaria.Rotina",
        datetime_ida=to_default_tz(datetime(2022, 1, 1, 19)),
        ativo=False,
        rota=rota,
    )
    r2 = baker.make(
        "rodoviaria.Rotina",
        datetime_ida=to_default_tz(datetime(2022, 1, 2, 19)),
        ativo=False,
        rota=rota,
    )

    result = rotina_svc.get_rotinas_inativas_por_rota_integrada_ativa([company.company_internal_id])
    assert len(result) == 1
    assert len(result[rota.id_internal]) == 2
    assert result[rota.id_internal][0] == to_tz(r.datetime_ida, "UTC")
    assert result[rota.id_internal][1] == to_tz(r2.datetime_ida, "UTC")


def test_get_rotinas_inativas_por_rota_integrada_ativa_primeiro_chk_rota_nao_integrado():
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=123942,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    rota = baker.make("rodoviaria.Rota", id_internal=472895, ativo=True, company=company)
    baker.make(
        "rodoviaria.Checkpoint",
        rota=rota,
        idx=0,
        duracao=timedelta(hours=0),
        tempo_embarque=timedelta(hours=0),
        local=None,
    )
    baker.make(
        "rodoviaria.Checkpoint",
        rota=rota,
        idx=1,
        duracao=timedelta(hours=10),
        tempo_embarque=timedelta(hours=10),
        local=baker.make("rodoviaria.LocalEmbarque", local_embarque_internal_id=1),
    )
    r = baker.make(
        "rodoviaria.Rotina",
        datetime_ida=to_default_tz(datetime(2022, 1, 1, 19)),
        ativo=False,
        rota=rota,
    )
    r2 = baker.make(
        "rodoviaria.Rotina",
        datetime_ida=to_default_tz(datetime(2022, 1, 2, 19)),
        ativo=False,
        rota=rota,
    )
    print("[teste]")
    result = rotina_svc.get_rotinas_inativas_por_rota_integrada_ativa([company.company_internal_id])
    assert len(result) == 1
    assert len(result[rota.id_internal]) == 2
    dt1 = to_tz(r.datetime_ida + timedelta(hours=20), "UTC")
    dt2 = to_tz(r2.datetime_ida + timedelta(hours=20), "UTC")
    assert result[rota.id_internal][0] == dt1
    assert result[rota.id_internal][1] == dt2

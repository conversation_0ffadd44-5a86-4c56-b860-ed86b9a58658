import pytest
from model_bakery import baker

from commons.django_utils import paginator_sort_by
from rodoviaria.models.core import TrechoVendido


@pytest.fixture
def items_para_paginacao():
    return baker.make(TrechoVendido, 10)


def test_paginator_sort_by_order_by(items_para_paginacao):
    queryset = TrechoVendido.objects.all()
    queryset_ordered = queryset.order_by("capacidade_classe")
    queryset, count, num_pages = paginator_sort_by(queryset, "capacidade_classe", 10, 1, False)
    assert list(queryset.values("id")) == list(queryset_ordered.values("id"))
    assert count == 10
    assert num_pages == 1


def test_paginator_sort_by_paginacao(items_para_paginacao):
    queryset = TrechoVendido.objects.all()
    queryset, count, num_pages = paginator_sort_by(queryset, None, 5, 1, False)
    assert len(queryset) == 5
    assert count == 10
    assert num_pages == 2


def test_paginator_sort_by_order_by_descending(items_para_paginacao):
    queryset = TrechoVendido.objects.all()
    queryset_ordered = queryset.order_by("-capacidade_classe")

    queryset, count, num_pages = paginator_sort_by(queryset, "capacidade_classe", 10, 1, True)

    paginator_descending_list = list(queryset.values_list("id", flat=True))
    descending_list = list(queryset_ordered.values_list("id", flat=True))
    assert paginator_descending_list == descending_list
    assert count == 10
    assert num_pages == 1

from datetime import datetime
from types import SimpleNamespace
from unittest import mock

import pytest
from model_bakery import baker

from commons.dateutils import to_default_tz
from rodoviaria.api.eulabs.api import EulabsAPI
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.forms.compra_rodoviaria_forms import VerificarPoltronaForm
from rodoviaria.models.core import Cidade, Company, Grupo, LocalEmbarque, Passagem, TrechoClasse
from rodoviaria.service import reserva_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.tests.utils_testes import _comprar_params


@pytest.fixture
def companies_multimodelo(eulabs_company, eulabs_login, vexado_company, vexado_login):
    company_internal_id = 876
    eulabs_company.company_internal_id = company_internal_id
    eulabs_company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_company.company_internal_id = company_internal_id
    vexado_company.modelo_venda = Company.ModeloVenda.HIBRIDO
    eulabs_company.save()
    vexado_company.save()
    yield SimpleNamespace(
        marketplace=eulabs_company,
        hibrido=vexado_company,
        company_internal_id=company_internal_id,
        hibrido_api=VexadoAPI,
        marketplace_api=EulabsAPI,
    )


def mock_trecho_api():
    servico = ServicoForm(
        tipo_veiculo=2,
        external_id="83123",
        external_datetime_ida=to_default_tz(datetime(2022, 6, 10, 14, 30)),
        preco=120,
        vagas=21,
        provider_data={"id": "9323"},
        linha="SÃO PAULO X RIO DE JANEIRO",
        veiculo_andar="1",
        veiculo_id=43213,
        rota_external_id="3123",
    )
    buscar_servico_form = BuscarServicoForm(found=True, servicos=[servico])
    return buscar_servico_form


def _create_locais_embarque(company, timezone, origem_id, destino_id):
    cidade_origem, cidade_destino = baker.make(Cidade, company=company, _quantity=2, timezone=timezone)
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=origem_id,
        cidade=cidade_origem,
        id_external=2312,
    )
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=destino_id,
        cidade=cidade_destino,
        id_external=93282,
    )


def _create_trecho_classe(trecho_classe_id, company):
    origem, destino = baker.make(LocalEmbarque, _quantity=2)
    grupo = baker.make(Grupo, company_integracao=company)
    return baker.make(
        TrechoClasse,
        grupo=grupo,
        trechoclasse_internal_id=trecho_classe_id,
        origem=origem,
        destino=destino,
    )


def test_verifica_poltronas_multimodelo_caso_hibrido(companies_multimodelo, vexado_grupos_mockado):
    grupo_buser_django = vexado_grupos_mockado.ida.grupo
    grupo_buser_django.company_id = companies_multimodelo.company_internal_id
    trecho_classe_buser_django_infos = vexado_grupos_mockado.ida.trecho_classe_infos
    timezone = trecho_classe_buser_django_infos.cidade_origem.timezone
    _create_locais_embarque(
        companies_multimodelo.hibrido,
        timezone,
        trecho_classe_buser_django_infos.localembarque_origem_id,
        trecho_classe_buser_django_infos.localembarque_destino_id,
    )
    grupo_buser_django.modelo_venda = Company.ModeloVenda.HIBRIDO
    trecho_classe_id = 731312
    with mock.patch.object(CompraRodoviariaSVC, "_get_internal_grupo", return_value=grupo_buser_django), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
        return_value=trecho_classe_buser_django_infos,
    ), mock.patch.object(
        companies_multimodelo.hibrido_api,
        "buscar_corridas",
        return_value=mock_trecho_api(),
    ) as mock_hibrido_buscar_servico, mock.patch.object(
        companies_multimodelo.hibrido_api, "verifica_poltrona", return_value=[13, 14]
    ) as mock_hibrido_verifica_poltrona:
        params = VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)
        poltronas = CompraRodoviariaSVC(params).verifica_poltrona(params)
    assert poltronas == [13, 14]
    mock_hibrido_buscar_servico.assert_called_once()
    mock_hibrido_verifica_poltrona.assert_called_once()


def test_verifica_poltronas_multimodelo_caso_marketplace(companies_multimodelo, eulabs_grupos_mockado):
    grupo_buser_django = eulabs_grupos_mockado.ida.grupo
    grupo_buser_django.company_id = companies_multimodelo.company_internal_id
    trecho_classe_buser_django_infos = eulabs_grupos_mockado.ida.trecho_classe_infos
    timezone = trecho_classe_buser_django_infos.cidade_origem.timezone
    _create_locais_embarque(
        companies_multimodelo.marketplace,
        timezone,
        trecho_classe_buser_django_infos.localembarque_origem_id,
        trecho_classe_buser_django_infos.localembarque_destino_id,
    )
    grupo_buser_django.modelo_venda = Company.ModeloVenda.MARKETPLACE
    trecho_classe_id = 482739
    with mock.patch.object(CompraRodoviariaSVC, "_get_internal_grupo", return_value=grupo_buser_django), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
        return_value=trecho_classe_buser_django_infos,
    ), mock.patch.object(
        companies_multimodelo.marketplace_api,
        "buscar_corridas",
        return_value=mock_trecho_api(),
    ) as mock_mktplace_buscar_servico, mock.patch.object(
        companies_multimodelo.marketplace_api,
        "verifica_poltrona",
        return_value=[24, 25],
    ) as mock_mktplace_verifica_poltrona:
        params = VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)
        poltronas = CompraRodoviariaSVC(params).verifica_poltrona(params)
    assert poltronas == [24, 25]
    mock_mktplace_buscar_servico.assert_called_once()
    mock_mktplace_verifica_poltrona.assert_called_once()


def test_efetua_compra_multimodelo_caso_marketplace(
    companies_multimodelo, eulabs_grupos_mockado, mock_dispara_atualizacao_trecho
):
    trecho_classe_id = 482739
    comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    _create_trecho_classe(trecho_classe_id, companies_multimodelo.marketplace)
    with mock.patch.object(companies_multimodelo.marketplace_api, "comprar") as mock_comprar_marketplace:
        CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    mock_comprar_marketplace.assert_called_once()


def test_efetua_compra_multimodelo_caso_hibrido(
    companies_multimodelo, vexado_grupos_mockado, mock_dispara_atualizacao_trecho
):
    trecho_classe_id = 423423
    comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    _create_trecho_classe(trecho_classe_id, companies_multimodelo.hibrido)
    with mock.patch.object(companies_multimodelo.hibrido_api, "comprar") as mock_comprar_hibrido:
        CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    mock_comprar_hibrido.assert_called_once()


def test_efetua_cancelamento_multimodelo_caso_marketplace(companies_multimodelo):
    buseiro_id = 45123
    travel_id_marketplace = 93123
    baker.make(  # passagem marketplace
        Passagem,
        company_integracao=companies_multimodelo.marketplace,
        travel_internal_id=travel_id_marketplace,
        buseiro_internal_id=buseiro_id,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=baker.make(TrechoClasse, trechoclasse_internal_id=31234),
    )
    with mock.patch.object(companies_multimodelo.marketplace_api, "cancela_venda") as mock_cancela_venda_mktplace:
        reserva_svc.efetua_cancelamento(travel_id=travel_id_marketplace)
    mock_cancela_venda_mktplace.assert_called_once()


def test_efetua_cancelamento_multimodelo_caso_hibrido(companies_multimodelo):
    buseiro_id = 45123
    travel_id_hibrido = 88931
    baker.make(
        Passagem,
        company_integracao=companies_multimodelo.hibrido,
        travel_internal_id=travel_id_hibrido,
        buseiro_internal_id=buseiro_id,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao=baker.make(TrechoClasse, trechoclasse_internal_id=83712),
    )
    with mock.patch.object(companies_multimodelo.hibrido_api, "cancela_venda") as mock_cancela_venda_hibrido:
        reserva_svc.efetua_cancelamento(travel_id=travel_id_hibrido)
    mock_cancela_venda_hibrido.assert_called_once()

from unittest import mock
from unittest.mock import Mock

import pytest
from django.db import connections
from model_bakery import baker

from rodoviaria.api.forms import BuscarServicoForm
from rodoviaria.api.guichepass.api import GuichepassAPI
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.forms.staff_forms import CheckPaxMultipleForm
from rodoviaria.models.core import Company
from rodoviaria.service.exceptions import (
    RodoviariaCompanyNotFoundException,
    RodoviariaIntegracaoNotFoundException,
    RodoviariaUnsupportedFeatureException,
)


def test_orchestrator_praxio(mocker, praxio_login):
    _test_api_methods(mocker, PraxioAPI, praxio_login.company)


def test_orchestrator_totalbus(mocker, totalbus_login):
    _test_api_methods(mocker, TotalbusAPI, totalbus_login.company)


def test_aceita_apenas_providers_validos(praxio_company):
    praxio_company.integracao.name = "inexistente"
    praxio_company.integracao.save()
    orchestrator = OrchestrateRodoviaria(praxio_company.company_internal_id, praxio_company.modelo_venda)
    with pytest.raises(RodoviariaIntegracaoNotFoundException, match=r"Integracao inexistente não encontrada"):
        orchestrator.provider  # noqa:B018


def test_orchestrator_guichepass(mocker, guiche_login):
    _test_api_methods(mocker, GuichepassAPI, guiche_login.company)


def _test_api_methods(mocker, api, company):
    mock_atualiza_origens = mocker.patch.object(api, "atualiza_origens")
    mock_cancela_venda = mocker.patch.object(api, "cancela_venda")
    mock_comprar = mocker.patch.object(api, "comprar")
    mock_buscar_itinerario = mocker.patch.object(api, "buscar_itinerario")

    api_obj = OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda)
    api_obj.atualiza_origens()
    api_obj.cancela_venda({})
    api_obj.comprar(mock.MagicMock())
    api_obj.buscar_itinerario({})

    mock_atualiza_origens.assert_called_with()
    mock_cancela_venda.assert_called_with({})
    mock_comprar.assert_called_with(mock.ANY)
    mock_buscar_itinerario.assert_called_with({})


def test_metodos_vexado(mocker, vexado_login, vexado_company):
    mock_listar_empresas = mocker.patch.object(VexadoAPI, "listar_empresas")
    mock_editar_rota = mocker.patch.object(VexadoAPI, "editar_rota")
    api_obj = OrchestrateRodoviaria(vexado_company.company_internal_id, vexado_company.modelo_venda)
    api_obj.listar_empresas()
    api_obj.editar_rota(rota_external_id=1, delimitacao=2, origem_id=3, destino_id=4, prefixo="5")
    mock_listar_empresas.assert_called_with()
    mock_editar_rota.assert_called_with(1, 2, 3, 4, "5")


@pytest.fixture
def company_unbounded():
    company = Company()
    return company


@pytest.fixture
def orchestrator_mock(company_unbounded):
    orchestrator = OrchestrateRodoviaria(company_unbounded.company_internal_id, company_unbounded.modelo_venda)
    orchestrator._provider = Mock(company=company_unbounded)
    return orchestrator


def test_company_inactive(orchestrator_mock):
    with pytest.raises(RodoviariaUnsupportedFeatureException):
        orchestrator_mock.comprar({})
    orchestrator_mock.provider.comprar.assert_not_called()


def test_company_active(orchestrator_mock, company_unbounded):
    company_unbounded.features = ["active"]
    magic_mock = mock.MagicMock()
    orchestrator_mock.comprar(magic_mock)
    orchestrator_mock.provider.comprar.assert_called_with(magic_mock)


def test_company_supported_feature(orchestrator_mock, company_unbounded):
    company_unbounded.features = ["itinerario"]
    # Executa a função mas não tem grupo -1.
    assert orchestrator_mock.buscar_todos_servicos(-1) is not None


def test_company_unsupported_feature(orchestrator_mock, company_unbounded):
    with pytest.raises(RodoviariaUnsupportedFeatureException):
        orchestrator_mock.buscar_todos_servicos(1)


def test_add_multiple_pax_na_lista_passageiros_viagem(
    orchestrator_mock,
    company_unbounded,
    mock_add_multiple_pax,
    django_assert_num_queries,
):
    company_unbounded.features = ["add_pax_staff"]
    add_multiple_pax_form = CheckPaxMultipleForm.parse_obj(mock_add_multiple_pax)
    baker.make(
        "rodoviaria.Passagem",
        travel_internal_id=add_multiple_pax_form.travels[0].travel_id,
        buseiro_internal_id=add_multiple_pax_form.travels[0].buseiros[0].id,
        status="confirmada",
    )
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        orchestrator_mock.add_multiple_pax_na_lista_passageiros_viagem(add_multiple_pax_form)
    add_multiple_pax_form.travels[0].buseiros.pop(0)
    orchestrator_mock.provider.add_multiple_pax_na_lista_passageiros_viagem.assert_called_with(add_multiple_pax_form)


def test_provider_multimodelo(vexado_login, totalbus_login):
    company_internal_id = 94231
    vexado_login.company.company_internal_id = company_internal_id
    vexado_login.company.save()
    with pytest.raises(RodoviariaCompanyNotFoundException):  # pra hibrido tem que passar modelo_venda
        OrchestrateRodoviaria(company_internal_id).provider  # noqa:B018
    totalbus_login.company.company_internal_id = company_internal_id
    totalbus_login.company.save()
    assert OrchestrateRodoviaria(company_internal_id).provider.company == totalbus_login.company
    assert (
        OrchestrateRodoviaria(company_internal_id, Company.ModeloVenda.HIBRIDO).provider.company == vexado_login.company
    )
    assert (
        OrchestrateRodoviaria(company_internal_id, Company.ModeloVenda.MARKETPLACE).provider.company
        == totalbus_login.company
    )


def test_find_trecho_vendido_nao_chama_api_caso_request_params_sejam_o_mesmo(orchestrator_mock):
    with mock.patch.object(OrchestrateRodoviaria, "buscar_corridas") as mock_buscar_corridas:
        mock_buscar_corridas.return_value = BuscarServicoForm(found=False, servicos=[])
        # bate a primeira vez no mock_buscar_corridas e gera um cache no cached_buscar_corridas
        orchestrator_mock.find_trecho_vendido(
            1, 2, "2022-01-01", "America/Sao_Paulo", "America/Sao_Paulo", 1, [], mock.Mock()
        )
        # chama novamente com os mesmo parametros de origem, destino e data, mas o resto diferente
        orchestrator_mock.find_trecho_vendido(
            1, 2, "2022-01-01", "America/Cuaiaba", "America/Cuaiaba", 2, [1], mock.Mock()
        )
    # como o cache foi criado, só deve chamar 1 vez
    mock_buscar_corridas.assert_called_once()


def test_orchestrator_flyweight():
    company_id = 1
    marketplace = Company.ModeloVenda.MARKETPLACE
    hibrido = Company.ModeloVenda.HIBRIDO
    instance_1 = OrchestrateRodoviaria(company_id, marketplace)
    instance_2 = OrchestrateRodoviaria(company_id, marketplace)
    instance_3 = OrchestrateRodoviaria(company_id, hibrido)

    assert instance_1 is instance_2
    assert instance_1 is not instance_3

import datetime

import pytest
from pydantic import ValidationError

from rodoviaria.api.totalbus import models


def teste_bloquear_poltrona_form():
    entrada = {
        "origem": 1,
        "destino": 2,
        "data": "2020-01-01",
        "servico": 1,
    }

    # ok
    form_dict = models.ServicoTrecho(**entrada).dict(by_alias=True)
    assert form_dict["origem"] == 1
    assert form_dict["destino"] == 2
    assert form_dict["data"] == datetime.date(2020, 1, 1)
    assert form_dict["servico"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("origem")
        models.ServicoTrecho(**entrada)


def test_localidade_form():
    entrada = {"cidade": "Sao Paulo - SP", "id": "321", "uf": "SP"}
    localidade = models.Localidade.parse_obj(entrada)
    assert localidade.descricao == "SAO PAULO - SP"
    assert localidade.external_local_id == "321"
    assert localidade.external_cidade_id == "321"
    assert localidade.uf == "SP"
    assert localidade.nome_cidade == "SAO PAULO"
    assert localidade.id_cidade_ibge is None


def teste_confirmar_venda_form():
    entrada = {
        "nomePassageiro": "str",
        "transacao": "str",
        "documentoPassageiro": "34.123.431-1",
        "tipoDocumentoPassageiro": "CPF",
        "telefone": "12991817263",
        "idFormaPagamento": 23,
        "formaPagamento": "BUSER",
    }
    form_dict = models.ConfirmarVendaForm(**entrada).dict(by_alias=True)
    assert form_dict["transacao"] == "str"
    assert form_dict["nomePassageiro"] == "str"
    assert form_dict["documentoPassageiro"] == "34.123.431-1"
    assert form_dict["tipoDocumentoPassageiro"] == "CPF"
    assert form_dict["telefone"] == "12991817263"
    assert form_dict["idFormaPagamento"] == 23
    assert form_dict["formaPagamento"] == "BUSER"

    entrada["telefone"] = ""
    form_dict = models.ConfirmarVendaForm(**entrada).dict(by_alias=True)
    assert form_dict["telefone"] == "11999999999"


def teste_cancelar_venda_form():
    entrada = {"transacao": "str", "validarMulta": False}
    form_dict = models.CancelarVendaForm(**entrada).dict(by_alias=True)
    assert form_dict["transacao"] == "str"
    assert form_dict["validarMulta"] is False


def teste_buscar_itinerario_corrida_form():
    entrada = {"data": "2020-01-01", "servico": 1}
    form_dict = models.BuscarItinerarioCorridaForm(**entrada).dict(by_alias=True)
    assert form_dict["servico"] == 1
    assert form_dict["data"] == datetime.date(2020, 1, 1)


def teste_retorna_poltronas_form():
    entrada = {"origem": 1, "destino": 2, "data": "2020-01-01", "servico": 1}

    # ok
    form_dict = models.RetornaPoltronasModel(**entrada).dict(by_alias=True)
    assert form_dict["origem"] == 1
    assert form_dict["destino"] == 2
    assert form_dict["data"] == datetime.date(2020, 1, 1)
    assert form_dict["servico"] == 1

    # dados faltando
    with pytest.raises(ValidationError, match=r"field required"):
        entrada.pop("servico")
        models.RetornaPoltronasModel(**entrada)

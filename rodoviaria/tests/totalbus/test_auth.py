from model_bakery import baker

from rodoviaria.api.totalbus.auth import TotalbusAuth


def test_get_login_from_company():
    company = baker.make("rodoviaria.Company")
    totalbus_login = baker.make("rodoviaria.TotalbusLogin", company=company)
    auth_obj = TotalbusAuth.from_company(company)
    assert auth_obj.tenant_id == totalbus_login.tenant_id
    assert auth_obj.username == totalbus_login.user
    assert auth_obj.password == totalbus_login.password


def test_get_login_from_client():
    company = baker.make("rodoviaria.Company")
    totalbus_login = baker.make("rodoviaria.TotalbusLogin", company=company)
    auth_obj = TotalbusAuth.from_client(totalbus_login)
    assert auth_obj.tenant_id == totalbus_login.tenant_id
    assert auth_obj.username == totalbus_login.user
    assert auth_obj.password == totalbus_login.password

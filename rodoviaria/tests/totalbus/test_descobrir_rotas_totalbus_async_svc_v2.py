from datetime import datetime, <PERSON><PERSON><PERSON>
from unittest import mock

import pytest
import time_machine
from celery.exceptions import Retry
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.forms import Localidade
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.totalbus.models import Itinerario
from rodoviaria.models.core import Checkpoint, Company, Rota, Rotina, TaskStatus
from rodoviaria.service import descobrir_rotas_totalbus_async_svc_v2
from rodoviaria.tests.totalbus.mocker import BuscarItinerarioCorrida, BuscarServicosDetalhado


def test_descobrir_rotas_com_company_external_id(mocker, totalbus_login):
    mock_chain = mocker.patch("rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.chain")
    totalbus_login.company.company_external_id = 11
    resp = descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas(
        client=totalbus_login,
        company_internal_id=totalbus_login.company.company_internal_id,
        modelo_venda=totalbus_login.company.modelo_venda,
        next_days=21,
        shift_days=2,
    )
    mock_chain.return_value()
    assert resp == mock_chain.return_value.on_error.return_value
    assert len(mock_chain.call_args[0][0]) == 22
    assert (
        mock_chain.call_args[0][0].tasks[0]["task"]
        == "rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas_do_dia"
    )
    assert (
        mock_chain.call_args[0][1].task
        == "rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.finisher_descobrir_rotas_totalbus"
    )


def test_descobrir_rotas_sem_company_external_id(mocker, totalbus_login, mock_consultar_empresas_usando_base_url_obj):
    mock_chain = mocker.patch("rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.chain")
    totalbus_login.company.company_external_id = None
    resp = descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas(
        client=totalbus_login,
        company_internal_id=totalbus_login.company.company_internal_id,
        modelo_venda=totalbus_login.company.modelo_venda,
        next_days=21,
        shift_days=2,
    )
    mock_chain.return_value()
    assert resp == mock_chain.return_value.on_error.return_value
    assert len(mock_chain.call_args[0][0].tasks) == 66
    assert (
        mock_chain.call_args[0][0].tasks[0]["task"]
        == "rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas_do_dia"
    )
    assert (
        mock_chain.call_args[0][1].task
        == "rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.finisher_descobrir_rotas_totalbus"
    )


def test_descobrir_rotas_return_object(mocker, totalbus_login):
    mock_chain = mocker.patch("rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.chain")
    totalbus_login.company.company_external_id = 11
    resp = descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas(
        client=totalbus_login,
        company_internal_id=totalbus_login.company.company_internal_id,
        modelo_venda=totalbus_login.company.modelo_venda,
        next_days=21,
        shift_days=2,
        return_task_object=True,
    )
    mock_chain.return_value.apply_async.assert_not_called()
    assert resp == mock_chain.return_value.on_error.return_value
    assert len(mock_chain.call_args[0][0]) == 22
    assert (
        mock_chain.call_args[0][0].tasks[0]["task"]
        == "rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas_do_dia"
    )
    assert (
        mock_chain.call_args[0][1].task
        == "rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2.finisher_descobrir_rotas_totalbus"
    )


def test_descobrir_rotas_not_enough_tokens(mocker, totalbus_login):
    mock_buscar_viagens_api = mocker.patch(
        "rodoviaria.service.descobrir_rotas_totalbus_async_svc_v2._buscar_viagens_api"
    )
    mock_buscar_viagens_api.side_effect = NotEnoughTokens(remaining_seconds=10)
    with pytest.raises(Retry):
        descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas_do_dia(
            company_internal_id=totalbus_login.company.company_internal_id,
            modelo_venda=totalbus_login.company.modelo_venda,
            company_external_id=totalbus_login.company.company_external_id,
            day=datetime(2024, 1, 30, 0, 0).isoformat(),
        )


def test_descobrir_rotas_do_dia_cria_rota(totalbus_login, mock_buscar_servicos_detalhado):
    assert Rota.objects.filter(company_id=totalbus_login.company_id).count() == 0
    totalbus_login.company.company_external_id = 11
    totalbus_login.company.save()
    day = datetime.strptime(BuscarServicosDetalhado.response()[0]["dataHoraServico"], "%Y-%m-%d %H:%M:%S")
    descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas_do_dia(
        company_internal_id=totalbus_login.company.company_internal_id,
        modelo_venda=totalbus_login.company.modelo_venda,
        company_external_id=totalbus_login.company.company_external_id,
        day=day.isoformat(),
    )
    rotas = list(Rota.objects.filter(company_id=totalbus_login.company_id))
    expected_rotinas = {
        "22181783ba9d6a830c58818dde28c7ba32051c": [
            to_tz(datetime(2024, 1, 20, 9, 0), "UTC"),
            to_tz(datetime(2024, 1, 20, 22, 0), "UTC"),
        ],
        "222186289bbfd5ac755b04a1b51bd7d473c8679": [to_tz(datetime(2024, 1, 20, 22, 10), "UTC")],
    }
    for rota in rotas:
        assert list(rota.rotina_set.values_list("datetime_ida", flat=True)) == expected_rotinas[rota.id_hash]
        assert rota.itinerario.count() == 3


def test_descobrir_rotas_do_dia_nao_inativa_rotina_de_rota_nao_encontrada(
    totalbus_login, mock_buscar_servicos_detalhado
):
    day = datetime.strptime(BuscarServicosDetalhado.response()[0]["dataHoraServico"], "%Y-%m-%d %H:%M:%S")
    rota = baker.make(Rota, id_hash="12344321jbajsdjoasndknas", company=totalbus_login.company)
    rotina_para_inativar = baker.make(Rotina, rota=rota, datetime_ida=to_tz(day.replace(hour=10), "UTC"))
    totalbus_login.company.company_external_id = 11
    totalbus_login.company.save()
    descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas_do_dia(
        company_internal_id=totalbus_login.company.company_internal_id,
        modelo_venda=totalbus_login.company.modelo_venda,
        company_external_id=totalbus_login.company.company_external_id,
        day=day.isoformat(),
    )
    rotina_para_inativar.refresh_from_db()
    assert rotina_para_inativar.ativo is True


def test_descobrir_rotas_do_dia_rota_ja_existe_e_atualiza_rotinas(totalbus_login, mock_buscar_servicos_detalhado):
    rota = baker.make(Rota, id_hash="22181783ba9d6a830c58818dde28c7ba32051c", company=totalbus_login.company)
    rotina_para_atualizar = baker.make(
        Rotina, rota=rota, datetime_ida=to_tz(datetime(2024, 1, 20, 9, 0), "UTC"), ativo=False
    )
    data_rotina_para_criar = to_tz(datetime(2024, 1, 20, 22, 0), "UTC")
    rotina_nao_encontrada = baker.make(
        Rotina, rota=rota, datetime_ida=to_tz(datetime(2024, 1, 20, 15, 0), "UTC"), ativo=False
    )
    rotina_para_nao_alterar = baker.make(Rotina, rota=rota, datetime_ida=to_tz(datetime(2024, 1, 21, 13, 0), "UTC"))
    totalbus_login.company.company_external_id = 11
    totalbus_login.company.save()
    day = datetime.strptime(BuscarServicosDetalhado.response()[0]["dataHoraServico"], "%Y-%m-%d %H:%M:%S")
    assert Rota.objects.filter(company_id=totalbus_login.company_id).count() == 1
    assert not Rotina.objects.filter(rota=rota, datetime_ida=data_rotina_para_criar).exists()
    descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas_do_dia(
        company_internal_id=totalbus_login.company.company_internal_id,
        modelo_venda=totalbus_login.company.modelo_venda,
        company_external_id=totalbus_login.company.company_external_id,
        day=day.isoformat(),
    )
    rotina_para_atualizar.refresh_from_db()
    assert rotina_para_atualizar.ativo is True
    # nao inativa rotina não encontrada pois ela vai ser inativada no callback
    rotina_nao_encontrada.refresh_from_db()
    assert rotina_nao_encontrada.ativo is False
    rotina_para_nao_alterar.refresh_from_db()
    assert rotina_para_nao_alterar.ativo is True
    assert Rotina.objects.filter(rota=rota, datetime_ida=data_rotina_para_criar).exists()


def test_create_rotas_nao_existentes_ignora_conflito_com_rota_que_ja_existe(totalbus_login):
    cleaned_itinerario = BuscarItinerarioCorrida.response()["lsParadas"]
    parsed_itinerario = Itinerario.parse_obj(cleaned_itinerario)
    map_rotas_api = {
        parsed_itinerario.hash: {
            "rotinas": {},
            "parsed": parsed_itinerario,
            "cleaned": cleaned_itinerario,
            "hash": parsed_itinerario.hash,
            "id_external": 7010,
        }
    }
    rota = baker.make(Rota, id_hash=parsed_itinerario.hash, company=totalbus_login.company)
    baker.make(Checkpoint, rota=rota, idx=0)
    assert Checkpoint.objects.filter(rota=rota).count() == 1
    assert Rota.objects.filter(company=totalbus_login.company).count() == 1
    descobrir_rotas_totalbus_async_svc_v2._create_rotas_nao_existentes(
        totalbus_login.company.id, map_rotas_api=map_rotas_api, map_rotas_buser={}
    )
    assert Rota.objects.filter(company=totalbus_login.company).count() == 1
    assert Checkpoint.objects.filter(rota=rota).count() == 1


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_descobrir_rotas_finisher():
    now_mock = datetime(2022, 10, 19, 16, 43, 34)
    company = baker.make(Company, company_internal_id=10, modelo_venda=Company.ModeloVenda.MARKETPLACE)
    rota_com_rotina_ativa = baker.make(Rota, company=company)
    baker.make(Rotina, rota=rota_com_rotina_ativa, datetime_ida=(now_mock + timedelta(hours=1)))
    rota_sem_rotina_ativa = baker.make(Rota, company=company)
    baker.make(Rotina, rota=rota_sem_rotina_ativa, datetime_ida=(now_mock + timedelta(hours=1)), ativo=False)
    descobrir_rotas_totalbus_async_svc_v2.inativa_rotas_sem_rotinas(company.id)
    rota_com_rotina_ativa.refresh_from_db()
    assert rota_com_rotina_ativa.ativo is True
    rota_sem_rotina_ativa.refresh_from_db()
    assert rota_sem_rotina_ativa.ativo is False


@time_machine.travel(datetime(2024, 1, 19, 10, 0, 0, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_descobrir_rotas(totalbus_login, mock_buscar_servicos_detalhado):
    now_mock = datetime(2024, 1, 19, 10, 0, 0)
    totalbus_login.company.company_external_id = 11
    data_rotina_para_criar = to_tz(datetime(2024, 1, 20, 22, 0), "UTC")
    rota_para_ativar = baker.make(
        Rota, id_hash="22181783ba9d6a830c58818dde28c7ba32051c", company=totalbus_login.company, ativo=False
    )
    rota_para_inativar = baker.make(Rota, id_hash="12344321jbajsdjoasndknas", company=totalbus_login.company)
    rotina_para_inativar = baker.make(
        Rotina, rota=rota_para_inativar, datetime_ida=to_tz(now_mock + timedelta(days=2), "UTC")
    )  # rotina para inativar
    Rotina.objects.filter(id=rotina_para_inativar.id).update(updated_at=now_mock - timedelta(days=2))
    descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas(
        client=totalbus_login,
        company_internal_id=totalbus_login.company.company_internal_id,
        modelo_venda=totalbus_login.company.modelo_venda,
        next_days=4,
        shift_days=0,
    )
    rota_para_inativar.refresh_from_db()
    rotina_para_inativar.refresh_from_db()
    rota_para_ativar.refresh_from_db()
    assert rotina_para_inativar.ativo is False
    assert rota_para_inativar.ativo is False
    assert Rotina.objects.filter(rota=rota_para_ativar, datetime_ida=data_rotina_para_criar).exists()
    assert rota_para_ativar.ativo is True


@time_machine.travel(datetime(2024, 1, 19, 10, 0, 0, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_descobrir_rotas_duas_empresas_na_api(
    totalbus_login,
    mock_buscar_servicos_detalhado_sem_viagens_na_segunda_request,
    mock_consultar_empresas_usando_base_url_obj,
):
    # teste para validar o problema que dava anteriormente:
    # quando tinha duas empresas no mesmo login da API ele buscava a rotina para uma empresa
    # e quando não encontrava a mesma rotina na outra, inativava a que tinha acabado de criar
    data_rotina_para_criar = to_tz(datetime(2024, 1, 20, 22, 0), "UTC")
    rota_para_ativar = baker.make(
        Rota, id_hash="22181783ba9d6a830c58818dde28c7ba32051c", company=totalbus_login.company, ativo=False
    )
    descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas(
        client=totalbus_login,
        company_internal_id=totalbus_login.company.company_internal_id,
        modelo_venda=totalbus_login.company.modelo_venda,
        next_days=0,
        shift_days=1,
    )
    rota_para_ativar.refresh_from_db()
    rotina = Rotina.objects.get(rota=rota_para_ativar, datetime_ida=data_rotina_para_criar)
    assert rotina.ativo is True
    assert rota_para_ativar.ativo is True


@time_machine.travel(datetime(2024, 1, 19, 10, 0, 0, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_finisher_descobrir_rotas_totalbus():
    now_mock = datetime(2024, 1, 19, 10, 0, 0)
    init = datetime(2024, 5, 10, 0, 0, 0)
    end = datetime(2024, 5, 15, 0, 0, 0)
    rota_com_rotina_que_sera_inativada = baker.make(Rota, ativo=True)
    rotina_nao_atualizada = baker.make(
        Rotina,
        rota=rota_com_rotina_que_sera_inativada,
        datetime_ida=to_default_tz(datetime(2024, 5, 13, 12, 0, 0)),
        ativo=True,
    )
    Rotina.objects.filter(id=rotina_nao_atualizada.id).update(updated_at=now_mock - timedelta(days=2))
    descobrir_rotas_totalbus_async_svc_v2.finisher_descobrir_rotas_totalbus(
        rota_com_rotina_que_sera_inativada.company_id, init.isoformat(), end.isoformat()
    )
    rota_com_rotina_que_sera_inativada.refresh_from_db()
    rotina_nao_atualizada.refresh_from_db()
    assert rota_com_rotina_que_sera_inativada.ativo is False
    assert rotina_nao_atualizada.ativo is False
    assert (
        TaskStatus.objects.get(
            company=rota_com_rotina_que_sera_inativada.company, task_name=TaskStatus.Name.DESCOBRIR_ROTAS
        ).status
        == TaskStatus.Status.SUCCESS
    )


def test_finisher_descobrir_rotas_totalbus_on_error():
    rota_sem_rotina = baker.make(Rota)
    descobrir_rotas_totalbus_async_svc_v2.finisher_descobrir_rotas_totalbus_on_error(
        None, None, None, rota_sem_rotina.company_id
    )
    rota_sem_rotina.refresh_from_db()
    assert rota_sem_rotina.ativo is False
    assert (
        TaskStatus.objects.get(company=rota_sem_rotina.company, task_name=TaskStatus.Name.DESCOBRIR_ROTAS).status
        == TaskStatus.Status.FAILURE
    )


@time_machine.travel(datetime(2024, 1, 19, 10, 0, 0, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_descobrir_rotas_localidade_nao_encontrada_get_missing(
    totalbus_login, mock_buscar_servicos_detalhado_sem_desc_origem
):
    totalbus_login.company.company_external_id = 11
    data_rotina_para_criar = to_tz(datetime(2024, 1, 20, 9, 0), "UTC")
    rota_para_ativar = baker.make(
        Rota, id_hash="22181783ba9d6a830c58818dde28c7ba32051c", company=totalbus_login.company, ativo=False
    )
    with mock.patch.object(
        OrchestrateRodoviaria,
        "atualiza_origens",
        return_value=[
            Localidade(
                nome_cidade="BOCA DO ACRE",
                external_cidade_id="21867",
                uf="",
                external_local_id="21867",
                complemento=None,
            )
        ],
    ):
        descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas(
            client=totalbus_login,
            company_internal_id=totalbus_login.company.company_internal_id,
            modelo_venda=totalbus_login.company.modelo_venda,
            next_days=4,
            shift_days=0,
        )
    rota_para_ativar.refresh_from_db()
    assert rota_para_ativar.ativo is True
    assert Rotina.objects.filter(rota=rota_para_ativar, datetime_ida=data_rotina_para_criar).exists()


@time_machine.travel(datetime(2024, 1, 19, 10, 0, 0, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_descobrir_rotas_localidade_nao_encontrada_ignora_rota(
    totalbus_login, mock_buscar_servicos_detalhado_sem_desc_origem
):
    totalbus_login.company.company_external_id = 11
    data_rotina_para_criar = to_tz(datetime(2024, 1, 20, 22, 0), "UTC")
    rota_para_ativar = baker.make(
        Rota, id_hash="22181783ba9d6a830c58818dde28c7ba32051c", company=totalbus_login.company, ativo=False
    )
    with mock.patch.object(OrchestrateRodoviaria, "atualiza_origens", return_value=[]):
        descobrir_rotas_totalbus_async_svc_v2.descobrir_rotas(
            client=totalbus_login,
            company_internal_id=totalbus_login.company.company_internal_id,
            modelo_venda=totalbus_login.company.modelo_venda,
            next_days=4,
            shift_days=0,
        )
    rota_para_ativar.refresh_from_db()
    assert rota_para_ativar.ativo is False
    assert not Rotina.objects.filter(rota=rota_para_ativar, datetime_ida=data_rotina_para_criar).exists()

import json
import os
from itertools import cycle

import pytest
import responses
from django.conf import settings
from model_bakery import baker
from responses.matchers import json_params_matcher

import rodoviaria.api.totalbus.endpoints as endpoints
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.forms.staff_forms import TotalbusUndefinedCompanyClient
from rodoviaria.tests.totalbus import mocker


@pytest.fixture
def totalbus_api(totalbus_login, cache_mock):
    return TotalbusAPI(totalbus_login.company)


@pytest.fixture
def totalbus_api_validar_multa(totalbus_login):
    totalbus_login.validar_multa = True
    totalbus_login.save()
    return TotalbusAPI(totalbus_login.company)


@pytest.fixture
def mock_login(totalbus_api, requests_mock):
    yield requests_mock.add(
        responses.POST,
        f"{totalbus_api.base_url}/{endpoints.TotalbusUrlPath.LOGIN}",
        json=mocker.Login.response(),
    )
    requests_mock.remove(responses.POST, f"{totalbus_api.base_url}/{endpoints.TotalbusUrlPath.LOGIN}")


@pytest.fixture
def mock_unauthorized_login(totalbus_login, requests_mock):
    requests_mock.add(
        responses.GET,
        endpoints.BuscaOrigensConfig(totalbus_login).url,
        json=mocker.BuscaOrigem.response_unauthorized_login(),
        status=401,
    )


@pytest.fixture
def mock_bloquear_poltrona_venda_normal(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaVendaNormalConfig(totalbus_login).url,
        json=mocker.BloquearPoltrona.response(),
    )
    yield http_mock


@pytest.fixture
def mock_bloquear_poltrona_twice_sucesso_and_error(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaVendaNormalConfig(totalbus_login).url,
        json=mocker.BloquearPoltrona.response(),
    )
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaVendaNormalConfig(totalbus_login).url,
        json=mocker.BloquearPoltrona.response_erro_poltrona_selecionada(),
        status=400,
    )


@pytest.fixture
def mock_desbloquear_poltrona(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.DesbloquearPoltronasConfig(totalbus_login).url,
        json=mocker.DesbloquearPoltrona.response(),
    )


@pytest.fixture
def mock_bloquear_poltrona_erro_venda_bloqueada(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaVendaNormalConfig(totalbus_login).url,
        json=mocker.BloquearPoltrona.response_erro_venda_bloqueada(),
        status=400,
    )


@pytest.fixture
def mock_bloquear_poltrona_erro_venda_bloqueada_categoria_indisponivel(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaVendaNormalConfig(totalbus_login).url,
        json=mocker.BloquearPoltrona.response_erro_venda_bloqueada_categoria_indisponivel(),
        status=400,
    )


@pytest.fixture
def mock_bloquear_poltrona_erro_venda_bloqueada_tipo_passagem_sem_disponibilidade(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaVendaNormalConfig(totalbus_login).url,
        json=mocker.BloquearPoltrona.response_erro_venda_bloqueada_tipo_passagem_sem_disponibilidade(),
        status=400,
    )


@pytest.fixture
def mock_bloquear_poltrona_erro_poltrona_selecionada(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaVendaNormalConfig(totalbus_login).url,
        json=mocker.BloquearPoltrona.response_erro_poltrona_selecionada(),
        status=400,
    )


@pytest.fixture
def mock_comprar(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaRequestConfig(totalbus_login).url,
        json=mocker.ConfirmarVenda.response(),
    )


@pytest.fixture
def mock_comprar_com_erro(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaRequestConfig(totalbus_login).url,
        json=mocker.ConfirmarVenda.response_boleto_nao_encontrado(),
        status=400,
    )


@pytest.fixture
def mock_comprar_duas_vezes_com_erro_na_segunda(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaRequestConfig(totalbus_login).url,
        json=mocker.ConfirmarVenda.response(),
    )
    http_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaRequestConfig(totalbus_login).url,
        json=mocker.ConfirmarVenda.response_boleto_nao_encontrado(),
        status=400,
    )


@pytest.fixture
def mock_comprar_categoria_indisponivel(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaRequestConfig(totalbus_login).url,
        json=mocker.ConfirmarVenda.response_categoria_indisponivel(),
        status=400,
    )


@pytest.fixture
def mock_comprar_data_nascimento_obrigatorio(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaRequestConfig(totalbus_login).url,
        json=mocker.ConfirmarVenda.response_data_nascimento_obrigatorio(),
        status=400,
    )


@pytest.fixture
def mock_comprar_asiento(totalbus_login, http_mock):
    http_mock.add(
        responses.POST,
        endpoints.ConfirmarVendaRequestConfig(totalbus_login).url,
        json=mocker.ConfirmarVenda.response_asiento(),
        status=400,
    )


@pytest.fixture
def mock_cancelar_venda_por_bilhete(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaPorBilheteRequestConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response(),
    )


@pytest.fixture
def mock_cancelar_venda_por_bilhete_erro_generico(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaPorBilheteRequestConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_erro_generico(),
        status=400,
    )


@pytest.fixture
def mock_cancelar_venda_por_bilhete_com_devolucao(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaPorBilheteRequestConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_tempo_excedido(),
        status=400,
    )
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaPorBilheteRequestConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_com_multa(),
    )


@pytest.fixture
def mock_cancela_venda(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response(),
    )


@pytest.fixture
def mock_cancela_venda_bilhete_cancelado(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_bilhete_cancelado(),
        status=400,
    )


@pytest.fixture
def mock_cancela_venda_bilhete_troca_de_poltrona(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_erro_troca_de_poltrona(),
        status=400,
    )


@pytest.fixture
def mock_cancela_bilhete_raise_poltronatrocada(totalbus_login, requests_mock):
    # chama api cancelar venda. Em caso de falha
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_erro_troca_de_poltrona(),
        status=400,
    )
    # busca o bilhete na api do parceiro para atualizar dados
    requests_mock.add(
        responses.POST,
        endpoints.BuscarBilheteRequestConfig(totalbus_login).url,
        json=mocker.BuscarBilhetes.response(),
    )
    # só então bate na api cancelar_venda_por_bilhete
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaPorBilheteRequestConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_erro_troca_de_poltrona(),
        status=400,
    )


@pytest.fixture
def mock_cancela_venda_considerado_embarcado(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_considerado_embarcado(),
        status=400,
    )


@pytest.fixture
def mock_cancela_venda_bilhete_nao_encontrado(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_bilhete_nao_encontrado(),
        status=400,
    )


@pytest.fixture
def mock_cancela_venda_tempo_excedido(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_tempo_excedido(),
        status=400,
    )


@pytest.fixture
def mock_cancela_venda_erro_generico(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_erro_generico(),
        status=400,
    )


@pytest.fixture
def mock_cancela_venda_login_inativo(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_login_inativo(),
        status=401,
    )


@pytest.fixture
def mock_cancela_venda_missing_config(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_missing_config(),
        status=400,
    )


@pytest.fixture
def mock_cancela_venda_com_devolucao(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_tempo_excedido(),
        status=400,
    )
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_com_multa(),
    )


@pytest.fixture
def mock_cancela_venda_com_devolucao_2(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_tempo_excedido_2(),
        status=400,
    )
    requests_mock.add(
        responses.POST,
        endpoints.CancelarVendaConfig(totalbus_login).url,
        json=mocker.CancelarVenda.response_com_multa(),
    )


@pytest.fixture
def mock_atualiza_origens(totalbus_login, requests_mock):
    requests_mock.add(
        responses.GET,
        endpoints.BuscaOrigensConfig(totalbus_login).url,
        json=mocker.BuscaOrigem.response(),
    )


@pytest.fixture
def mock_buscar_destinos(request, get_request_params, totalbus_login, requests_mock):
    origem_external_id = get_request_params(request, "origem_external_id")
    return requests_mock.add(
        responses.GET,
        endpoints.BuscarDestinosConfig(totalbus_login, origem_external_id).url,
        json=mocker.BuscaDestinos.response(),
    )


@pytest.fixture
def mock_buscar_origens_destinos(request, get_request_params, totalbus_login, requests_mock):
    company_external_id = get_request_params(request, "company_external_id")
    return requests_mock.add(
        responses.GET,
        endpoints.BuscarOrigensDestinosConfig(totalbus_login, company_external_id).url,
        json=mocker.BuscaOrigensDestinos.response(),
    )


@pytest.fixture
def mock_buscar_origens_destinos_todas_empresas(totalbus_login, get_request_params, totalbus_api, requests_mock):
    company_external_ids = [e["id"] for e in mocker.ConsultarEmpresas.response()]
    for company_external_id in company_external_ids:
        requests_mock.add(
            responses.GET,
            endpoints.BuscarOrigensDestinosConfig(totalbus_login, company_external_id).url,
            json=mocker.BuscaOrigensDestinos.response(),
        )


@pytest.fixture
def mock_busca_origem_processando_cache(totalbus_login, requests_mock):
    requests_mock.add(
        responses.GET,
        endpoints.BuscaOrigensConfig(totalbus_login).url,
        json=mocker.BuscaOrigem.response_processando_cache(),
        status=423,
    )


@pytest.fixture
def mock_buscar_itinerario(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarItinerarioCorridaRequestConfig(totalbus_login).url,
        json=mocker.BuscarItinerarioCorrida.response(),
    )


@pytest.fixture
def mock_buscar_itinerario_variable(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarItinerarioCorridaRequestConfig(totalbus_login).url,
        json=mocker.BuscarItinerarioCorrida.response(),
    )
    requests_mock.add(
        responses.POST,
        endpoints.BuscarItinerarioCorridaRequestConfig(totalbus_login).url,
        json=mocker.BuscarItinerarioCorrida.response_2(),
    )


@pytest.fixture
def mock_buscar_itinerario_nao_localizado(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarItinerarioCorridaRequestConfig(totalbus_login).url,
        json=mocker.BuscarItinerarioCorrida.response_itinerario_nao_localizado(),
        status=400,
    )


@pytest.fixture
def mock_buscar_bilhetes_por_numero_sistema(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarBilheteRequestConfig(totalbus_login).url,
        json=mocker.BuscarBilhetes.response(),
    )


@pytest.fixture
def mock_cancela_bilhete_raise_timeout(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarBilheteRequestConfig(totalbus_login).url,
        json={},
        status=408,
    )


@pytest.fixture
def mock_buscar_bilhetes_por_numero_sistema_conferir_params(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarBilheteRequestConfig(totalbus_login).url,
        json=mocker.BuscarBilhetes.response(),
        match=[json_params_matcher(mocker.BuscarBilhetes.request_by_numero_sistema())],
    )


@pytest.fixture
def mock_buscar_bilhetes_por_numero_sistema_nenhum_bilhete_encontrado(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarBilheteRequestConfig(totalbus_login).url,
        json=mocker.BuscarBilhetes.response_no_tickets_found(),
        status=400,
    )


@pytest.fixture
def mock_buscar_bilhetes_por_numero_sistema_todos_cancelados(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarBilheteRequestConfig(totalbus_login).url,
        json=mocker.BuscarBilhetes.response_with_all_tickets_canceled(),
        status=200,
    )


@pytest.fixture
def mock_get_poltronas_livres(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(totalbus_login).url,
        json=mocker.GetPoltronasLivres.response(),
    )


@pytest.fixture
def mock_get_poltronas_livres_bordas(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(totalbus_login).url,
        json=mocker.GetPoltronasLivres.response_bordas(),
    )


@pytest.fixture
def mock_poltronas_insuficientes(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(totalbus_login).url,
        json=mocker.GetPoltronasLivres.response_poltronas_insuficientes(),
    )


@pytest.fixture
def mock_poltronas_error(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(totalbus_login).url,
        json=mocker.GetPoltronasLivres.response_error(),
        status=400,
    )


@pytest.fixture
def mock_buscar_todos_servicos(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarTodosServicosDisponiveisConfig(totalbus_login).url,
        json=mocker.BuscarTodosServicosDisponiveis.response(),
    )


@pytest.fixture
def mock_buscar_todos_servicos_vazio(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarTodosServicosDisponiveisConfig(totalbus_login).url,
        json={},
    )


@pytest.fixture
def mock_buscar_todos_servicos_erro(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarTodosServicosDisponiveisConfig(totalbus_login).url,
        json={},
        status=400,
    )


@pytest.fixture
def mock_buscar_servico(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker.BuscarServico.response(),
    )


@pytest.fixture
def mock_buscar_servico_com_hibrido(totalbus_login, requests_mock):
    servico_hibrido = "999911"
    mock = mocker.BuscarServico.response()
    mock["lsServicos"][1]["servico"] = servico_hibrido
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mock,
    )


@pytest.fixture
def mock_buscar_servico_com_hibrido_mismatch(totalbus_login, requests_mock):
    servico_hibrido = "999911"
    mock = mocker.BuscarServico.response()
    mock["lsServicos"][2]["servico"] = servico_hibrido
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mock,
    )


@pytest.fixture
def mock_buscar_servico_conexao(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker.BuscarServico.response_conexao(),
    )


@pytest.fixture
def mock_buscar_servico_conexao_com_tempo_espera(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker.BuscarServico.response_conexao_com_tempo_espera(),
    )


@pytest.fixture
def mock_buscar_servico_nao_localizado(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker.BuscarServico.response_trecho_nao_localizado(),
        status=400,
    )


@pytest.fixture
def mock_buscar_servico_nao_disponivel(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker.BuscarServico.response_trecho_nao_disponivel(),
        status=400,
    )


@pytest.fixture
def mock_buscar_corridas_to_many_requests(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker.BuscarServico.response_to_many_requests(),
        status=429,
    )


@pytest.fixture
def mock_buscar_servico_unico(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker.BuscarServico.response_for_only_one_service(),
    )


@pytest.fixture
@responses.activate
def mock_connection_error(totalbus_api, requests_mock):
    pass


@pytest.fixture
def mock_buscar_itinerario_corrida(totalbus_login, requests_mock):
    filename = os.path.join(settings.BASE_DIR, "rodoviaria/tests/totalbus/data/buscaritinerariocorrida.json")
    with open(filename) as f:
        yield requests_mock.add(
            responses.POST,
            endpoints.BuscarItinerarioCorridaRequestConfig(totalbus_login).url,
            json=json.load(f),
        )


@pytest.fixture
def mock_consultar_empresas(requests_mock):
    login_obj = TotalbusUndefinedCompanyClient.parse_obj(
        {"user": "user", "password": "password", "tenant_id": "tenant_id"}
    )
    requests_mock.add(
        responses.GET,
        endpoints.ConsultarEmpresasConfig(login_obj).url,
        json=mocker.ConsultarEmpresas.response(),
    )


@pytest.fixture
def mock_consultar_empresas_usando_base_url_obj(totalbus_login, requests_mock):
    requests_mock.add(
        responses.GET,
        endpoints.ConsultarEmpresasConfig(totalbus_login).url,
        json=mocker.ConsultarEmpresas.response(),
    )


@pytest.fixture
def mock_buscar_formas_pagamento(requests_mock):
    login_obj = TotalbusUndefinedCompanyClient.parse_obj(
        {"user": "user", "password": "password", "tenant_id": "tenant_id"}
    )
    requests_mock.add(
        responses.GET,
        endpoints.BuscarFormasPagamentoConfig(login_obj).url,
        json=mocker.BuscarFormasPagamento.response(),
    )


@pytest.fixture
def mock_padrao_buscar_servico_data_input_invalida(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=mocker.BuscarServicosDetalhado.response_data_invalida(),
        status=400,
    )


@pytest.fixture
def mock_buscar_servicos_detalhado(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=mocker.BuscarServicosDetalhado.response(),
    )


@pytest.fixture
def mock_buscar_servicos_detalhado_sem_desc_origem(totalbus_login, requests_mock):
    response = mocker.BuscarServicosDetalhado.response()
    response = [response[0]]
    del response[0]["trechos"][0]["descDestino"]
    del response[0]["trechos"][1]["descOrigem"]
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=response,
    )


@pytest.fixture
def mock_buscar_servicos_detalhado_jdbc_connection_error(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=mocker.BuscarServicosDetalhado.response_jdbc_connection_error(),
        status=400,
    )


@pytest.fixture
def mock_buscar_servicos_detalhado_sem_viagens_na_segunda_request(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=mocker.BuscarServicosDetalhado.response(),
    )
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=[],
    )


@pytest.fixture
def mock_buscar_servicos_detalhado_varias_datas_mesmo_servico(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=mocker.BuscarServicosDetalhado.response_mesmo_servico(),
    )


@pytest.fixture
def mock_padrao_buscar_servicos_bad_request_no_response_with_retry(totalbus_login, requests_mock):
    requests_mock.add(responses.POST, endpoints.BuscarServicosPeriodoConfig(totalbus_login).url, json=None, status=400)
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=mocker.BuscarServicosDetalhado.response(),
    )


@pytest.fixture
def mock_buscar_servicos_detalhado_sem_servicos(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarServicosPeriodoConfig(totalbus_login).url,
        json=mocker.BuscarServicosDetalhado.response_sem_servico(),
    )


@pytest.fixture
def mock_buscar_servicos_ignora_classe_mais_de_um_match(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker.BuscarServico.response_mais_de_um_match_horario(),
    )


@pytest.fixture
def mock_consultar_categoria_servico(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.ConsultarCategoriaCorridaRequestConfig(totalbus_login).url,
        json=mocker.ConsultarCategoriaCorrida.response(),
    )


@pytest.fixture
def mock_consultar_categoria_servico_with_retry(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.ConsultarCategoriaCorridaRequestConfig(totalbus_login).url,
        json=mocker.ConsultarCategoriaCorrida.response_connection_error(),
        status=500,
    )
    requests_mock.add(
        responses.POST,
        endpoints.ConsultarCategoriaCorridaRequestConfig(totalbus_login).url,
        json=mocker.ConsultarCategoriaCorrida.response(),
    )


@pytest.fixture
def mock_consultar_categoria_servico_buser(totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints.ConsultarCategoriaCorridaRequestConfig(totalbus_login).url,
        json=mocker.ConsultarCategoriaCorrida.response_com_categoria_buser(),
    )


@pytest.fixture
def mock_consultar_categoria_empresa(totalbus_login, requests_mock):
    requests_mock.add(
        responses.GET,
        endpoints.ConsultarCategoriaRequestConfig(totalbus_login).url,
        json=mocker.ConsultarCategoriaEmpresa.response(),
    )


@pytest.fixture
def cidades_buscar_servico_padrao_com_tz():
    def factory(company_id):
        locais_ids = [2, 3, 22, 21817, 21862, 21863, 21867, 21885, 21886]
        return baker.make(
            "rodoviaria.cidade",
            company_id=company_id,
            id_external=cycle(locais_ids),
            timezone="America/Cuiaba",
            _quantity=len(locais_ids),
        )

    return factory

from faker import Faker

fake = Faker("pt_BR")


class Login:
    @staticmethod
    def response():
        return {
            "status": "SUCCESS",
            "usuarioId": 1482,
            "puntoventaId": 3062,
            "estacionId": 1764,
            "nombusuario": "BUSER BRASIL TECNOLOGIA LTDA",
            "tipoventaId": 12,
            "empresas": [45, 42, 43],
            "retornarTodasLocalidades": False,
            "isSeguroOpcionalEmpresasPuntoVenda": {
                "42": True,
                "43": True,
                "45": True,
                "46": True,
            },
            "perfil": [],
        }


class DesbloquearPoltrona:
    @staticmethod
    def request():
        return {"transacao": "TRANSACAO_TOTALBUS"}

    @staticmethod
    def response():
        return {"status": True}


class BloquearPoltrona:
    @staticmethod
    def request():
        return {
            "origem": 2063,  # id da origem
            "destino": 2201,  # “localidadeConexaoId”
            "data": "2020-01-01",  # dataCorrida
            "servico": 1,  # servico
            "poltrona": "1",
        }

    @staticmethod
    def response():
        return {
            "origem": {
                "id": 2063,
                "cidade": "FORTALEZA - CE",
                "sigla": "FOR",
                "uf": "CE",
            },
            "destino": {
                "id": 2201,
                "cidade": "SOBRAL - CE",
                "sigla": "SOL",
                "uf": "CE",
            },
            "data": "2020-11-17",
            "servico": "1010485",
            "assento": "27",
            "duracao": 10,
            "transacao": "TRANSACAO_TOTALBUS_BLOQUEAR_POLTRONA",
            "preco": {
                "tarifa": "560.84",
                "outros": "0.00",
                "pedagio": "20.86",
                "seguro": "0.00",
                "preco": "560.84",
                "tarifaComPricing": "560.84",
                "taxaEmbarque": "7.60",
                "seguroW2I": "0.00",
            },
            "rutaid": "1445",
            "seguroOpcional": {"id": 41, "km": 225, "valor": 10.00},
            "numOperacion": "010023561934",
            "localizador": "010023561934",
            "boletoId": 10000017060472,
            "empresaCorridaId": 2,
            "dataSaida": "2023-12-28 22:45",
            "dataChegada": "2023-12-29 00:45",
            "dataCorrida": "2023-12-28",
            "classeServicoId": 2,
        }

    @staticmethod
    def response_erro_venda_bloqueada():
        return {
            "timestamp": "2021-09-07T18:57:13.693",
            "status": 400,
            "message": "Bloqueio não concluído. Verifique as restrições.",
            "messagem": "Bloqueio não concluído. Verifique as restrições.",
            "error": "Bloqueio não concluído. Verifique as restrições.",
            "id": "4eanjn323oknnsa",
        }

    @staticmethod
    def response_erro_venda_bloqueada_tipo_passagem_sem_disponibilidade():
        return {
            "timestamp": "2021-09-07T18:57:13.693",
            "status": 400,
            "message": "RE-04|O tipo de passagem selecionado já não tem disponibilidade.",
            "messagem": "RE-04|O tipo de passagem selecionado já não tem disponibilidade.",
            "error": "RE-04|O tipo de passagem selecionado já não tem disponibilidade.",
            "id": "4eanjn323oknnsa",
        }

    @staticmethod
    def response_erro_venda_bloqueada_categoria_indisponivel():
        return {
            "timestamp": "2021-09-07T18:57:13.693",
            "status": 400,
            "message": "RE-04|Categoria indisponível para esse serviço.",
            "messagem": "RE-04|Categoria indisponível para esse serviço.",
            "error": "RE-04|Categoria indisponível para esse serviço.",
            "id": "4eanjn323oknnsa",
        }

    @staticmethod
    def response_erro_poltrona_selecionada():
        return {
            "timestamp": "2021-09-07T18:57:13.693",
            "status": 400,
            "message": "A poltrona já foi selecionada",
            "messagem": "A poltrona já foi selecionada",
            "error": "A poltrona já foi selecionada",
            "id": "4eanjn323oknnsa",
        }


class ConfirmarVenda:
    @staticmethod
    def response():
        return {
            "poltrona": "40",
            "servico": "6631221",
            "transacao": "TRANSACAO_TOTALBUS_CONFIRMAR_VENDA",
            "localizador": "QUQIEH",
            "nome": "RJ Consultores",
            "documento": "123456789",
            "seguroW2i": "0",
            "numeroBilhete": "10000003772511",
            "certificado": "",
            "xmlBPE": (
                '<BPe xmlns="http://www.portalfiscal.inf.br/bpe"><infBPe'
                ' Id="BPe31200641550112006304630550000132511509753679"'
                ' versao="1.00"><ide><cUF>31</cUF><tpAmb>2</tpAmb><mod>63</mod><serie>55</serie><nBP>13251</nBP><cBP>50975367</cBP><cDV>9</cDV><modal>1</modal><dhEmi>2020-06-17T17:54:30-03:00</dhEmi><tpEmis>1</tpEmis><verProc>100</verProc><tpBPe>0</tpBPe><indPres>1</indPres><UFIni>MG</UFIni><cMunIni>3103405</cMunIni><UFFim>MG</UFFim><cMunFim>3106200</cMunFim></ide><emit><CNPJ>41550112006304</CNPJ><IE>0030308870018</IE><xNome>EXPRESSO'
                " GUANABARA LTDA</xNome><xFant>EXPRESSO GUANABARA"
                " LTDA</xFant><IM>3170206</IM><CNAE>1234567</CNAE><CRT>3</CRT><enderEmit><xLgr>PRACA DA"
                " BIBLIA</xLgr><nro>200</nro><xCpl>GUICHE"
                " 11</xCpl><xBairro>MARTINS</xBairro><cMun>3170206</cMun><xMun>UBERLANDIA</xMun><CEP>38400476</CEP><UF>MG</UF><fone>0800999999</fone><email><EMAIL></email></enderEmit><TAR>12345</TAR></emit><infPassagem><cLocOrig>3103405</cLocOrig><xLocOrig>ARACUAI</xLocOrig><cLocDest>3106200</cLocDest><xLocDest>BELO"
                " HORIZONTE</xLocDest><dhEmb>2020-06-18T06:00:00-03:00</dhEmb><dhValidade>2021-06-17T17:54:30-03:00</dhValidade><infPassageiro><xNome>RJ"
                " Consultores</xNome><tpDoc>1</tpDoc><nDoc>123456789</nDoc><dNasc>2020-06-17</dNasc></infPassageiro></infPassagem><infViagem><cPercurso>2090</cPercurso><xPercurso>BELO"
                " HORIZONTE/ARAÇUAÍ</xPercurso><tpViagem>00</tpViagem><tpServ>1</tpServ><tpAcomodacao>1</tpAcomodacao><tpTrecho>1</tpTrecho><dhViagem>2020-06-18T06:00:00-03:00</dhViagem><prefixo>MARINHO</prefixo><poltrona>40</poltrona></infViagem><infValorBPe><vBP>136.66</vBP><vDesconto>0.01</vDesconto><vPgto>136.65</vPgto><vTroco>0.00</vTroco><tpDesconto>01</tpDesconto><xDesconto>Tarifa"
                " promocional</xDesconto><Comp><tpComp>01</tpComp><vComp>119.17</vComp></Comp><Comp><tpComp>02</tpComp><vComp>2.49</vComp></Comp><Comp><tpComp>03</tpComp><vComp>10.00</vComp></Comp><Comp><tpComp>04</tpComp><vComp>5.00</vComp></Comp></infValorBPe><imp><ICMS><ICMS20><CST>20</CST><pRedBC>10.00</pRedBC><vBC>122.99</vBC><pICMS>10.00</pICMS><vICMS>12.30</vICMS></ICMS20></ICMS></imp><pag><tPag>03</tPag><vPag>136.65</vPag><card><tpIntegra>1</tpIntegra><CNPJ>**************</CNPJ><tBand>02</tBand><cAut>999111</cAut></card></pag><infAdic><infCpl>ICMS:12,30"
                " (10,00%) OUTROS TRIB:13,53"
                " (11,00%)</infCpl></infAdic></infBPe><infBPeSupl><qrCodBPe>https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31200641550112006304630550000132511509753679&amp;tpAmb=2</qrCodBPe><boardPassBPe>312006415501120063046305500001325115097536790000132510MARINHO202006180600000100000119170000000000000000002565000072</boardPassBPe></infBPeSupl><Signature"
                ' xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod'
                ' Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/><SignatureMethod'
                ' Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/><Reference'
                ' URI="#BPe31200641550112006304630550000132511509753679"><Transforms><Transform'
                ' Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/><Transform'
                ' Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/></Transforms><DigestMethod'
                ' Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/><DigestValue>XXQifNp17XtDkkZ1LpgcRhgoUIc=</DigestValue></Reference></SignedInfo><SignatureValue>UOfFVYEFAU3mb7Cf7tYDW3cyMxnxwzcSIPCHQck/nIbNwUUoCLIvyC2F9KijLTUBqz36fjFTizBag2gvqooL4G9+q8/ENTkWmsJZi1+s1xnUoQjX83J7mMi5xvkSSleF9aDvOTkzPCZCpsz4s/cbeUvh1yhX7DbY4/JY0Gra3h4+Jm7hMh24ebx/jsCiZ5DRb1u0bKRK27u6YybuMH62pDhKO8PW+yEXoRjaDbcmVNQyVSd6KyRAYEsP+ZggZbLWwsABJ6XUEoAhRPOIyeq5zF6i+Lf8Q3Tc6bgfzE2iuoEBP56cL4oGnXmrwEXD0ZsukEYB7edagjZmPVpz53TBrw==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></BPe>'
            ),
            "bpe": {
                "qrcodeBpe": "https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31200641550112006304630550000132511509753679&tpAmb=2",
                "qrcode": "https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31200641550112006304630550000132511509753679&tpAmb=2",
                "plataforma": "21",
                "prefixo": "MARINHO",
                "linha": "BELO HORIZONTE/ARAÇUAÍ",
                "valorTotal": "136.66",
                "desconto": "0.01",
                "valorPagar": "131.65",
                "chaveBpe": "31200641550112006304630550000132511509753679",
                "formaPagamento": "CREDITO",
                "valorFormaPagamento": "131.65",
                "troco": "0",
                "tipoDesconto": "NORMAL",
                "numeroBpe": "13251",
                "serie": "055",
                "protocoloAutorizacao": "131200*********",
                "numeroSistema": "171007",
                "outrosTributos": "ICMS:12,30 (10,00%) OUTROS TRIB:13,53 (11,00%)",
                "contingencia": "False",
                "codigoAnttOrigem": "2565",
                "codigoAnttDestino": "72",
                "codigoMonitriipBPe": "312006415501120063046305500001325115097536790000132510MARINHO202006180600000100000119170000000000000000002565000072",
                "tarifa": "119.16",
                "pedagio": "2.49",
                "taxaEmbarque": "10",
                "seguro": "0.00",
                "outros": "0",
                "tipoServico": "HORARIO ORDINARIO",
                "classe": "CONVENCIONAL TESTE12",
                "cabecalhoAgencia": {
                    "bairro": "CENTRO",
                    "cidade": "BELO HORIZONTE",
                    "cnpj": "33.337.007/0023-68",
                    "endereco": "BARÃO HOMEM DE MELO",
                    "numero": "N° 100",
                    "razaoSocial": "UNIAO TRANSPORTE INTERESTADUAL DE LUXO S",
                    "uf": "MG",
                },
                "cabecalhoEmitente": {
                    "bairro": "MARTINS",
                    "cep": "38400476",
                    "cidade": "UBERLANDIA",
                    "cnpj": "41550112006304",
                    "endereco": "PRACA DA BIBLIA",
                    "inscricaoEstadual": "0030308870018",
                    "numero": "200",
                    "razaoSocial": "EXPRESSO GUANABARA LTDA",
                },
                "telefoneEmpresa": "SAC 0800999999",
            },
            "numeroSistema": "171007",
            "agencia": "BELO HORIZONTE -- BH",
        }

    @staticmethod
    def response_boleto_nao_encontrado():
        return {
            "timestamp": "2021-09-07T18:57:13.693",
            "status": 400,
            "message": "Boleto não encontrado",
            "messagem": "Boleto não encontrado",
            "error": "Boleto não encontrado",
            "id": "4eanjn323oknnsa",
        }

    @staticmethod
    def response_categoria_indisponivel():
        return {
            "status": 400,
            "message": "RE-04|Categoria indisponível para esse serviço.",
            "messagem": "Categoria indisponível para esse serviço.",
        }

    @staticmethod
    def response_asiento():
        return {
            "status": 400,
            "message": "Error.Bloqueo.Asiento.Ya.Cedido",
            "messagem": "Error.Bloqueo.Asiento.Ya.Cedido",
        }

    @staticmethod
    def response_data_nascimento_obrigatorio():
        return {
            "timestamp": "2024-08-02T22:27:03.313",
            "status": 400,
            "message": "Data nascimento do passageiro é obrigatório",
            "mensagem": "Data nascimento do passageiro é obrigatório",
            "error": "Data nascimento do passageiro é obrigatório",
            "id": "f602715129c47e81",
        }


class CancelarVenda:
    @staticmethod
    def response():
        return {"multa": "0", "status": True}

    @staticmethod
    def response_com_multa():
        return {"multa": "9.33", "status": True}

    @staticmethod
    def response_tempo_excedido():
        return {
            "timestamp": "2021-09-07T14:54:22.46",
            "status": 400,
            "message": "O tempo permitido para cancelamento de bilhetes desta agência já foi ultrapassado.",
            "mensagem": "O tempo permitido para cancelamento de bilhetes desta agência já foi ultrapassado.",
            "error": "O tempo permitido para cancelamento de bilhetes desta agência já foi ultrapassado.",
            "id": "1b7ae079a152359c",
        }

    @staticmethod
    def response_tempo_excedido_2():
        return {
            "timestamp": "2021-09-07T14:54:22.46",
            "status": 400,
            "message": "Cancelamento não permitido para pois o Limite para Cancelamento excedeu",
            "mensagem": "Cancelamento não permitido para pois o Limite para Cancelamento excedeu",
            "error": "Cancelamento não permitido para pois o Limite para Cancelamento excedeu",
            "id": "1b7ae079a152359c",
        }

    @staticmethod
    def response_erro_generico():
        return {
            "timestamp": "2021-09-07T14:54:22.46",
            "status": 400,
            "message": "Um erro genérico.",
            "mensagem": "Um erro genérico.",
            "error": "Um erro genérico.",
            "id": "1b7ae079a152359c",
        }

    @staticmethod
    def response_login_inativo():
        return {
            "timestamp": "2021-09-07T14:54:22.46",
            "status": 400,
            "message": "Usuário/Senha inválido",
            "mensagem": "Usuário/Senha inválido",
            "error": "Usuário/Senha inválido",
            "id": "1b7ae079a152359c",
        }

    @staticmethod
    def response_missing_config():
        return {
            "timestamp": "2021-09-07T14:54:22.46",
            "status": 400,
            "message": "Ponto de Venda/Usuário/Estação devem ser configurados",
            "mensagem": "Ponto de Venda/Usuário/Estação devem ser configurados",
            "error": "Ponto de Venda/Usuário/Estação devem ser configurados",
            "id": "1b7ae079a152359c",
        }

    @staticmethod
    def response_bilhete_cancelado():
        return {
            "timestamp": "2021-10-03T22:33:42.18",
            "status": 400,
            "message": "O bilhete já foi cancelado.",
            "mensagem": "O bilhete já foi cancelado.",
            "error": "O bilhete já foi cancelado.",
            "id": "74bb7a11f0f78983",
        }

    @staticmethod
    def response_bilhete_nao_encontrado():
        return {
            "timestamp": "2021-10-03T22:33:42.18",
            "status": 400,
            "message": "Bilhete não encontrado (E1)",
            "mensagem": "Bilhete não encontrado (E1)",
            "error": "Bilhete não encontrado (E1)",
            "id": "74bb7a11f0f78983",
        }

    @staticmethod
    def response_erro_troca_de_poltrona():
        return {
            "timestamp": "2022-09-22T00:57:55.612",
            "status": 400,
            "message": "Não é possível cancelar/devolver um bilhete que possui troca de poltrona",
            "mensagem": "Não é possível cancelar/devolver um bilhete que possui troca de poltrona",
            "error": "Não é possível cancelar/devolver um bilhete que possui troca de poltrona",
            "id": "db75969f10b0059a",
        }

    @staticmethod
    def response_considerado_embarcado():
        return {
            "timestamp": "2023-06-14T20:39:57.768",
            "status": 400,
            "message": "Cancelamento não permitido. Considerado embarcado.",
            "mensagem": "Cancelamento não permitido. Considerado embarcado.",
            "error": "Cancelamento não permitido. Considerado embarcado.",
            "id": "1236a6803b574cd4",
        }


class BuscaOrigem:
    @staticmethod
    def response():
        return [
            {"id": 5650, "cidade": "INHUMAS - GO", "sigla": "INH", "uf": "GO"},
            {"id": 5410, "cidade": "BELO HORIZONTE - MG", "sigla": "BHZ", "uf": "MG"},
        ]

    @staticmethod
    def response_unauthorized_login():
        return {
            "timestamp": "2022-06-07T19:03:34.8Z",
            "message": "Usuário/Senha inválido",
            "messagem": "Usuário/Senha inválido",
            "error": "Usuário/Senha inválido",
            "id": "2bf3206c4e5664c3",
            "status": 401,
        }

    @staticmethod
    def response_processando_cache():
        return {
            "timestamp": "2022-06-01T14:46:11.703",
            "status": 423,
            "message": "Processando Cache. Tente novamente.",
            "mensagem": "Processando Cache. Tente novamente.",
            "error": "Processando Cache. Tente novamente.",
            "id": "c85a5302d0a75cf3",
        }


class BuscaDestinos:
    @staticmethod
    def response():
        return [
            {"id": "19052", "cidade": "ARARAQUARA - SP", "sigla": "ARQ", "uf": "SP", "empresas": "1"},
            {"id": "19053", "cidade": "ARAÇATUBA - SP", "sigla": "ARA", "uf": "SP", "empresas": "1"},
            {"id": "19054", "cidade": "TAUBATÉ - SP", "sigla": "TAU", "uf": "SP", "empresas": "1"},
        ]

    @staticmethod
    def response_unauthorized_login():
        return {
            "timestamp": "2022-06-07T19:03:34.8Z",
            "message": "Usuário/Senha inválido",
            "messagem": "Usuário/Senha inválido",
            "error": "Usuário/Senha inválido",
            "id": "2bf3206c4e5664c3",
            "status": 401,
        }


class BuscaOrigensDestinos:
    @staticmethod
    def response():
        return [
            {
                "origem": {"id": 5557, "cidade": "FRUTAL - MG", "sigla": "FRU", "uf": "MG", "empresas": "42"},
                "destinos": [
                    {"id": 19118, "cidade": "BARRETOS - SP", "sigla": "BRS", "uf": "SP", "empresas": "42"},
                    {"id": 19123, "cidade": "COLOMBIA - SP", "sigla": "CLM", "uf": "SP", "empresas": "42"},
                    {"id": 7550, "cidade": "PLANURA - MG", "sigla": "PLU", "uf": "MG", "empresas": "42"},
                ],
            },
            {
                "origem": {"id": 7550, "cidade": "PLANURA - MG", "sigla": "PLU", "uf": "MG", "empresas": "42"},
                "destinos": [
                    {"id": 19118, "cidade": "BARRETOS - SP", "sigla": "BRS", "uf": "SP", "empresas": "42"},
                    {"id": 19123, "cidade": "COLOMBIA - SP", "sigla": "CLM", "uf": "SP", "empresas": "42"},
                    {"id": 5557, "cidade": "FRUTAL - MG", "sigla": "FRU", "uf": "MG", "empresas": "42"},
                ],
            },
        ]

    @staticmethod
    def response_unauthorized_login():
        return {
            "timestamp": "2022-06-07T19:03:34.8Z",
            "message": "Usuário/Senha inválido",
            "messagem": "Usuário/Senha inválido",
            "error": "Usuário/Senha inválido",
            "id": "2bf3206c4e5664c3",
            "status": 401,
        }


class BuscarItinerarioCorrida:
    @staticmethod
    def request():
        return {"data": "2020-01-01", "servico": 1}

    @staticmethod
    def response():
        res = {
            "servico": "1010665",
            "data": "2020-05-26",
            "lsParadas": [
                {
                    "localidade": {"id": 2063, "cidade": "FORTALEZA - CE", "uf": "CE"},
                    "distancia": "69.7",
                    "permanencia": "00:00",
                    "data": "2020-05-26",
                    "hora": "05:30",
                }
            ],
        }
        return res

    @staticmethod
    def response_2():
        res = {
            "servico": "1010665",
            "data": "2020-05-30",
            "lsParadas": [
                {
                    "localidade": {"id": 2063, "cidade": "FORTALEZA - CE", "uf": "CE"},
                    "distancia": "69.7",
                    "permanencia": "00:00",
                    "data": "2020-05-30",
                    "hora": "05:30",
                }
            ],
        }
        return res

    @staticmethod
    def response_itinerario_nao_localizado():
        res = {
            "timestamp": "2021-10-26T19:54:35.434",
            "status": 400,
            "message": "CI-02|ITINERARIO NAO LOCALIZADO",
            "mensagem": "CI-02|ITINERARIO NAO LOCALIZADO",
            "error": "CI-02|ITINERARIO NAO LOCALIZADO",
            "id": "001061d114874ce6",
        }
        return res


class GetPoltronasLivres:
    @staticmethod
    def request():
        return {
            "origem": 2063,
            "destino": 2201,
            "data": "2020-01-18",
            "servico": 1020,
            "trechoclasse_internal_id": 555,
        }

    @staticmethod
    def request_for_n_poltronas(n):
        return {
            "origem": 2063,
            "destino": 2201,
            "data": "2020-01-18",
            "servico": 1020,
            "trechoclasse_internal_id": 555,
            "NumeroPoltronas": n,
        }

    @staticmethod
    def response_poltronas_insuficientes():
        return {
            "origem": {
                "id": 2063,
                "cidade": "FORTALEZA - CE",
                "sigla": "FOR",
                "uf": "CE",
            },
            "destino": {
                "id": 2201,
                "cidade": "SOBRAL - CE",
                "sigla": "SOL",
                "uf": "CE",
            },
            "data": "2020-11-17",
            "servico": "1020",
            "mapaPoltrona": [
                {"x": "12", "y": "4", "disponivel": False, "numero": "01"},
                {"x": "1", "y": "3", "disponivel": True, "numero": "02"},
                {"x": "8", "y": "3", "disponivel": False, "numero": "03"},
            ],
        }

    @staticmethod
    def response_bordas():
        return {
            "origem": {
                "id": 2063,
                "cidade": "FORTALEZA - CE",
                "sigla": "FOR",
                "uf": "CE",
            },
            "destino": {
                "id": 2201,
                "cidade": "SOBRAL - CE",
                "sigla": "SOL",
                "uf": "CE",
            },
            "data": "2020-11-17",
            "servico": "1020",
            "mapaPoltrona": [
                {"x": "12", "y": "4", "disponivel": True, "numero": "01"},
                {"x": "1", "y": "3", "disponivel": False, "numero": "02"},
                {"x": "8", "y": "3", "disponivel": False, "numero": "03"},
                {"x": "8", "y": "4", "disponivel": True, "numero": "04"},
            ],
        }

    @staticmethod
    def response_error():
        return {"message": "BO-03|SERVICO NAO LOCALIZADO"}

    @staticmethod
    def response():
        return {
            "origem": {
                "id": 2063,
                "cidade": "FORTALEZA - CE",
                "sigla": "FOR",
                "uf": "CE",
            },
            "destino": {
                "id": 2201,
                "cidade": "SOBRAL - CE",
                "sigla": "SOL",
                "uf": "CE",
            },
            "data": "2020-11-17",
            "servico": "1010485",
            "mapaPoltrona": [
                {"x": "12", "y": "4", "disponivel": False, "numero": "WC", "categoriaReservadaId": 1},
                {"x": "1", "y": "3", "disponivel": False, "numero": "04", "categoriaReservadaId": 30},
                {"x": "8", "y": "3", "disponivel": True, "numero": "32", "categoriaReservadaId": 33},
                {"x": "8", "y": "4", "disponivel": True, "numero": "31", "categoriaReservadaId": -1},
                {"x": "8", "y": "1", "disponivel": True, "numero": "30", "categoriaReservadaId": -1},
                {"x": "10", "y": "3", "disponivel": True, "numero": "40", "categoriaReservadaId": -1},
                {"x": "11", "y": "3", "disponivel": True, "numero": "44", "categoriaReservadaId": -1},
                {"x": "11", "y": "4", "disponivel": True, "numero": "43", "categoriaReservadaId": -1},
                {"x": "11", "y": "1", "disponivel": True, "numero": "42", "categoriaReservadaId": -1},
                {"x": "11", "y": "0", "disponivel": True, "numero": "41", "categoriaReservadaId": -1},
                {"x": "12", "y": "3", "disponivel": False, "numero": "CF", "categoriaReservadaId": -1},
                {"x": "12", "y": "1", "disponivel": True, "numero": "46", "categoriaReservadaId": -1},
                {"x": "12", "y": "0", "disponivel": True, "numero": "45", "categoriaReservadaId": -1},
                {"x": "9", "y": "3", "disponivel": True, "numero": "36", "categoriaReservadaId": -1},
            ],
            "lsLocalidadeEmbarque": [],
            "lsLocalidadeDesembarque": [],
            "pricingSequencia": [],
            "pricingPoltrona": [
                {
                    "numero": "25",
                    "precoNumero": "18.17",
                    "nome": "ALIAS PRICING",
                    "pricingId": 6152,
                    "porcentagem": -50,
                },
                {
                    "numero": "26",
                    "precoNumero": "18.17",
                    "nome": "ALIAS PRICING",
                    "pricingId": 6152,
                    "porcentagem": -50,
                },
                {
                    "numero": "27",
                    "precoNumero": "18.17",
                    "nome": "ALIAS PRICING",
                    "pricingId": 6152,
                    "porcentagem": -50,
                },
                {
                    "numero": "28",
                    "precoNumero": "18.17",
                    "nome": "ALIAS PRICING",
                    "pricingId": 6152,
                    "porcentagem": -50,
                },
            ],
        }


class BuscarBilhetes:
    @staticmethod
    def request_by_numero_sistema():
        return {"data": "2021-09-22", "numeroSistema": "010000930269"}

    @staticmethod
    def response():
        return [
            {
                "numeroBilhete": "10000000706966",
                "localizador": "010000930269",
                "poltrona": "22",
                "servico": "93084",
                "nome": fake.name(),
                "documento": fake.rg(),
                "dataViagem": "22/09/2021 23:50",
                "bpe": {
                    "cabecalhoAgencia": {
                        "bairro": "ITAIM PAULISTA",
                        "cidade": "SAO PAULO",
                        "cnpj": "29365880000181",
                        "endereco": "DR GUILHERME BANNITZ",
                        "numero": "126",
                        "razaoSocial": "BUSER BRASIL TECNOLOGIA LTDA",
                        "uf": "SP",
                    },
                    "cabecalhoEmitente": {
                        "bairro": "METROPOLE",
                        "cep": "17900000",
                        "cidade": "DRACENA",
                        "cnpj": "43004159000197",
                        "endereco": "RUA DOS MANGUEIRAIS",
                        "inscricaoEstadual": "292090458114",
                        "numero": "101",
                        "razaoSocial": "EXPRESSO ADAMANTINA LTDA",
                        "uf": "SP",
                    },
                    "qrCode": "",
                    "qrcodeBpe": "",
                    "chaveBpe": "",
                    "serieBPe": "",
                    "numeroBPe": "",
                    "codigoANTTDestino": 71,
                    "codigoMonitriipBPe": (
                        "00000008857008028741202110092350000200000092640258600011999999999000070000071"
                    ),
                    "contingencia": False,
                    "dataAutorizacao": "",
                    "formaPagamento": "BUSER",
                    "linha": "SAO PAULO(SP) X RIO DE JANEIRO(RJ)",
                    "numeroSistema": "88570",
                    "tipoServico": "00",
                    "tarifa": 92.64,
                    "seguro": 0.00,
                    "embarque": 7.26,
                    "pedagio": 9.06,
                    "outros": 0.00,
                    "desconto": 23.96,
                    "troco": 0,
                    "valorFormaPagamento": 85.00,
                    "valorPagar": 85.00,
                    "valorTotal": 108.96,
                    "outrosTributos": "",
                    "classe": "DD - CONVENCIONAL - NON STOP",
                    "plataforma": "53",
                    "prefixo": "08028741",
                    "protocoloAutorizacao": "",
                    "cnpjEmpresa": "43004159000197",
                    "telefoneEmpresa": "0800 8801 980",
                    "telefoneEmpresaSac": "0800 8801 980",
                    "tipoDesconto": "Tarifa promocional",
                    "orgaoId": 3,
                    "canalVenda": "BUSER",
                    "codigoAgencia": 3062,
                    "descricaoAgencia": "BUSER",
                    "tipoBpe": "Normal",
                },
                "origem": {"id": 22115, "cidade": "SAO PAULO (TIETE) - SP", "uf": "SP"},
                "destino": {"id": 22208, "cidade": "RIO DE JANEIRO - RJ", "uf": "RJ"},
                "status": "V",
                "cancelado": False,
                "motivoCancelamento": "17",
                "dadosGatewayPagamento": [],
            },
            {
                "numeroBilhete": "10000000718099",
                "localizador": "010000930269",
                "poltrona": "22",
                "servico": "93084",
                "nome": fake.name(),
                "documento": fake.rg(),
                "dataViagem": "22/09/2021 23:50",
                "bpe": {
                    "cabecalhoAgencia": {
                        "bairro": "ITAIM PAULISTA",
                        "cidade": "SAO PAULO",
                        "cnpj": "29365880000181",
                        "endereco": "DR GUILHERME BANNITZ",
                        "numero": "126",
                        "razaoSocial": "BUSER BRASIL TECNOLOGIA LTDA",
                        "uf": "SP",
                    },
                    "cabecalhoEmitente": {
                        "bairro": "METROPOLE",
                        "cep": "17900000",
                        "cidade": "DRACENA",
                        "cnpj": "43004159000197",
                        "endereco": "RUA DOS MANGUEIRAIS",
                        "inscricaoEstadual": "292090458114",
                        "numero": "101",
                        "razaoSocial": "EXPRESSO ADAMANTINA LTDA",
                        "uf": "SP",
                    },
                    "qrCode": "",
                    "qrcodeBpe": "",
                    "chaveBpe": "",
                    "serieBPe": "",
                    "numeroBPe": "",
                    "codigoANTTDestino": 71,
                    "codigoMonitriipBPe": (
                        "00000008857008028741202110092350000200000092640258600011999999999000070000071"
                    ),
                    "contingencia": False,
                    "dataAutorizacao": "",
                    "formaPagamento": "BUSER",
                    "linha": "SAO PAULO(SP) X RIO DE JANEIRO(RJ)",
                    "numeroSistema": "88570",
                    "tipoServico": "00",
                    "tarifa": 92.64,
                    "seguro": 0.00,
                    "embarque": 7.26,
                    "pedagio": 9.06,
                    "outros": 0.00,
                    "desconto": 23.96,
                    "troco": 0,
                    "valorFormaPagamento": 85.00,
                    "valorPagar": 85.00,
                    "valorTotal": 108.96,
                    "outrosTributos": "",
                    "classe": "DD - CONVENCIONAL - NON STOP",
                    "plataforma": "53",
                    "prefixo": "08028741",
                    "protocoloAutorizacao": "",
                    "cnpjEmpresa": "43004159000197",
                    "telefoneEmpresa": "0800 8801 980",
                    "telefoneEmpresaSac": "0800 8801 980",
                    "tipoDesconto": "Tarifa promocional",
                    "orgaoId": 3,
                    "canalVenda": "BUSER",
                    "codigoAgencia": 3062,
                    "descricaoAgencia": "BUSER",
                    "tipoBpe": "Normal",
                },
                "origem": {"id": 22115, "cidade": "SAO PAULO (TIETE) - SP", "uf": "SP"},
                "destino": {"id": 22208, "cidade": "RIO DE JANEIRO - RJ", "uf": "RJ"},
                "status": "C",
                "cancelado": True,
                "motivoCancelamento": "17",
                "dadosGatewayPagamento": [],
            },
        ]

    @staticmethod
    def response_with_all_tickets_canceled():
        return [
            {
                "numeroBilhete": "10000000706966",
                "localizador": "010000930269",
                "poltrona": "22",
                "servico": "93084",
                "nome": fake.name(),
                "documento": fake.rg(),
                "dataViagem": "09/10/2021 23:50",
                "bpe": {
                    "cabecalhoAgencia": {
                        "bairro": "ITAIM PAULISTA",
                        "cidade": "SAO PAULO",
                        "cnpj": "29365880000181",
                        "endereco": "DR GUILHERME BANNITZ",
                        "numero": "126",
                        "razaoSocial": "BUSER BRASIL TECNOLOGIA LTDA",
                        "uf": "SP",
                    },
                    "cabecalhoEmitente": {
                        "bairro": "METROPOLE",
                        "cep": "17900000",
                        "cidade": "DRACENA",
                        "cnpj": "43004159000197",
                        "endereco": "RUA DOS MANGUEIRAIS",
                        "inscricaoEstadual": "292090458114",
                        "numero": "101",
                        "razaoSocial": "EXPRESSO ADAMANTINA LTDA",
                        "uf": "SP",
                    },
                    "qrCode": "",
                    "qrcodeBpe": "",
                    "chaveBpe": "",
                    "serieBPe": "",
                    "numeroBPe": "",
                    "codigoANTTDestino": 71,
                    "codigoMonitriipBPe": (
                        "00000008857008028741202110092350000200000092640258600011999999999000070000071"
                    ),
                    "contingencia": False,
                    "dataAutorizacao": "",
                    "formaPagamento": "BUSER",
                    "linha": "SAO PAULO(SP) X RIO DE JANEIRO(RJ)",
                    "numeroSistema": "88570",
                    "tipoServico": "00",
                    "tarifa": 92.64,
                    "seguro": 0.00,
                    "embarque": 7.26,
                    "pedagio": 9.06,
                    "outros": 0.00,
                    "desconto": 23.96,
                    "troco": 0,
                    "valorFormaPagamento": 85.00,
                    "valorPagar": 85.00,
                    "valorTotal": 108.96,
                    "outrosTributos": "",
                    "classe": "DD - CONVENCIONAL - NON STOP",
                    "plataforma": "53",
                    "prefixo": "08028741",
                    "protocoloAutorizacao": "",
                    "cnpjEmpresa": "43004159000197",
                    "telefoneEmpresa": "0800 8801 980",
                    "telefoneEmpresaSac": "0800 8801 980",
                    "tipoDesconto": "Tarifa promocional",
                    "orgaoId": 3,
                    "canalVenda": "BUSER",
                    "codigoAgencia": 3062,
                    "descricaoAgencia": "BUSER",
                    "tipoBpe": "Normal",
                },
                "origem": {"id": 22115, "cidade": "SAO PAULO (TIETE) - SP", "uf": "SP"},
                "destino": {"id": 22208, "cidade": "RIO DE JANEIRO - RJ", "uf": "RJ"},
                "status": "V",
                "cancelado": True,
                "motivoCancelamento": "17",
                "dadosGatewayPagamento": [],
            },
            {
                "numeroBilhete": "10000000718099",
                "localizador": "010000930269",
                "poltrona": "22",
                "servico": "93084",
                "nome": fake.name(),
                "documento": fake.rg(),
                "dataViagem": "09/10/2021 23:50",
                "bpe": {
                    "cabecalhoAgencia": {
                        "bairro": "ITAIM PAULISTA",
                        "cidade": "SAO PAULO",
                        "cnpj": "29365880000181",
                        "endereco": "DR GUILHERME BANNITZ",
                        "numero": "126",
                        "razaoSocial": "BUSER BRASIL TECNOLOGIA LTDA",
                        "uf": "SP",
                    },
                    "cabecalhoEmitente": {
                        "bairro": "METROPOLE",
                        "cep": "17900000",
                        "cidade": "DRACENA",
                        "cnpj": "43004159000197",
                        "endereco": "RUA DOS MANGUEIRAIS",
                        "inscricaoEstadual": "292090458114",
                        "numero": "101",
                        "razaoSocial": "EXPRESSO ADAMANTINA LTDA",
                        "uf": "SP",
                    },
                    "qrCode": "",
                    "qrcodeBpe": "",
                    "chaveBpe": "",
                    "serieBPe": "",
                    "numeroBPe": "",
                    "codigoANTTDestino": 71,
                    "codigoMonitriipBPe": (
                        "00000008857008028741202110092350000200000092640258600011999999999000070000071"
                    ),
                    "contingencia": False,
                    "dataAutorizacao": "",
                    "formaPagamento": "BUSER",
                    "linha": "SAO PAULO(SP) X RIO DE JANEIRO(RJ)",
                    "numeroSistema": "88570",
                    "tipoServico": "00",
                    "tarifa": 92.64,
                    "seguro": 0.00,
                    "embarque": 7.26,
                    "pedagio": 9.06,
                    "outros": 0.00,
                    "desconto": 23.96,
                    "troco": 0,
                    "valorFormaPagamento": 85.00,
                    "valorPagar": 85.00,
                    "valorTotal": 108.96,
                    "outrosTributos": "",
                    "classe": "DD - CONVENCIONAL - NON STOP",
                    "plataforma": "53",
                    "prefixo": "08028741",
                    "protocoloAutorizacao": "",
                    "cnpjEmpresa": "43004159000197",
                    "telefoneEmpresa": "0800 8801 980",
                    "telefoneEmpresaSac": "0800 8801 980",
                    "tipoDesconto": "Tarifa promocional",
                    "orgaoId": 3,
                    "canalVenda": "BUSER",
                    "codigoAgencia": 3062,
                    "descricaoAgencia": "BUSER",
                    "tipoBpe": "Normal",
                },
                "origem": {"id": 22115, "cidade": "SAO PAULO (TIETE) - SP", "uf": "SP"},
                "destino": {"id": 22208, "cidade": "RIO DE JANEIRO - RJ", "uf": "RJ"},
                "status": "C",
                "cancelado": True,
                "motivoCancelamento": "17",
                "dadosGatewayPagamento": [],
            },
        ]

    @staticmethod
    def response_no_tickets_found():
        return {
            "timestamp": "2021-10-18T20:34:26.682",
            "status": 400,
            "message": "Não há bilhete para a consulta enviada",
            "mensagem": "Não há bilhete para a consulta enviada",
            "error": "Não há bilhete para a consulta enviada",
            "id": "18ba7937b1a4a241",
        }


class BuscarServico:
    @staticmethod
    def request():
        return {
            "origem": 21847,
            "destino": 19068,
            "data": "2020-05-14",
        }

    @staticmethod
    def response(saida=None):
        if not saida:
            saida = "2021-05-14 20:05"
        res = {
            "origem": {
                "id": 21847,
                "cidade": "SAO PAULO - VIP",
                "sigla": "2",
                "uf": "SP",
            },
            "destino": {
                "id": 19068,
                "cidade": "RIBEIRAO PRETO - SP",
                "sigla": "118",
                "uf": "SP",
            },
            "data": "2021-05-14",
            "lsServicos": [
                {
                    "servico": "712",
                    "rutaId": 202,
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21847,
                    "grupoDestinoId": 19068,
                    "saida": saida,
                    "chegada": "2021-05-15 00:25",
                    "dataCorrida": "2021-05-14",
                    "dataSaida": "2021-05-14",
                    "poltronasLivres": 6,
                    "poltronasTotal": 20,
                    "preco": 142.41,
                    "precoOriginal": 159.00,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 352.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "800",
                    "rutaId": 202,
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21847,
                    "grupoDestinoId": 19068,
                    "saida": "2021-05-14 20:00",
                    "chegada": "2021-05-15 00:25",
                    "dataCorrida": "2021-05-13",
                    "dataSaida": "2021-05-14",
                    "poltronasLivres": 6,
                    "poltronasTotal": 12,
                    "preco": 145.41,
                    "precoOriginal": 159.00,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 352.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "715",
                    "rutaId": 262,
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21847,
                    "grupoDestinoId": 19068,
                    "saida": "2021-05-14 20:05",
                    "chegada": "2021-05-15 00:25",
                    "dataCorrida": "2021-05-14",
                    "dataSaida": "2021-05-14",
                    "poltronasLivres": 5,
                    "poltronasTotal": 6,
                    "preco": 142.41,
                    "precoOriginal": 159.00,
                    "classe": "LEITO-CAMA MISTO",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 352.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "718",
                    "rutaId": 304,
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21847,
                    "grupoDestinoId": 19068,
                    "saida": "2021-05-14 23:00",
                    "chegada": "2021-05-15 04:00",
                    "dataCorrida": "2021-05-14",
                    "dataSaida": "2021-05-14",
                    "poltronasLivres": 9,
                    "poltronasTotal": 20,
                    "preco": 152.41,
                    "precoOriginal": 159.00,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 317.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "719",
                    "rutaId": 305,
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21847,
                    "grupoDestinoId": 19068,
                    "saida": "2021-05-14 23:00",
                    "chegada": "2021-05-15 04:00",
                    "dataCorrida": "2021-05-14",
                    "dataSaida": "2021-05-14",
                    "poltronasLivres": 2,
                    "poltronasTotal": 6,
                    "preco": 142.41,
                    "precoOriginal": 159.00,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 317.0,
                    "cnpj": "26760933000170",
                },
            ],
        }
        return res

    @staticmethod
    def response_sem_servico():
        return {
            "origem": {
                "id": 21847,
                "cidade": "SAO PAULO - VIP",
                "sigla": "2",
                "uf": "SP",
            },
            "destino": {
                "id": 19068,
                "cidade": "RIBEIRAO PRETO - SP",
                "sigla": "118",
                "uf": "SP",
            },
            "data": "2021-05-14",
            "lsServicos": [],
        }

    @staticmethod
    def response_for_only_one_service():
        res = {
            "origem": {
                "id": 21847,
                "cidade": "SAO PAULO - VIP",
                "sigla": "2",
                "uf": "SP",
            },
            "destino": {
                "id": 19068,
                "cidade": "RIBEIRAO PRETO - SP",
                "sigla": "118",
                "uf": "SP",
            },
            "data": "2021-05-14",
            "lsServicos": [
                {
                    "servico": "712",
                    "rutaId": 202,
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21847,
                    "grupoDestinoId": 19068,
                    "saida": "2021-05-14 20:05",
                    "chegada": "2021-05-15 00:25",
                    "dataCorrida": "2021-05-14",
                    "dataSaida": "2021-05-14",
                    "poltronasLivres": 6,
                    "poltronasTotal": 20,
                    "preco": 142.41,
                    "precoOriginal": 159.00,
                    "classe": "EXECUTIVO",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 352.0,
                    "cnpj": "26760933000170",
                }
            ],
        }
        return res

    @staticmethod
    def response_to_many_requests():
        res = {
            "timestamp": "2022-01-25T16:12:28.658",
            "status": 429,
            "error": "429 TOO_MANY_REQUESTS",
            "message": (
                "Empresa '1be788c3-b594-4760-ae4c-3f746f6bb0c5' extrapolou a quantidade de requisições por minuto."
            ),
        }
        return res

    @staticmethod
    def response_trecho_nao_disponivel():
        res = {
            "timestamp": "2021-10-26T19:54:35.434",
            "status": 400,
            "message": "Trecho não disponível",
            "mensagem": "Trecho não disponível",
            "error": "Trecho não disponível",
            "id": "001061d114874ce6",
        }
        return res

    @staticmethod
    def response_trecho_nao_localizado():
        res = {
            "timestamp": "2021-10-26T19:54:35.434",
            "status": 400,
            "message": "BO-02|TRECHO NAO LOCALIZADO",
            "mensagem": "BO-02|TRECHO NAO LOCALIZADO",
            "error": "BO-02|TRECHO NAO LOCALIZADO",
            "id": "001061d114874ce6",
        }
        return res

    @staticmethod
    def response_mais_de_um_match_horario():
        return {
            "origem": {
                "id": 21887,
                "cidade": "UBERLANDIA VIP",
                "sigla": "119",
                "uf": "MG",
                "empresas": "42",
            },
            "destino": {
                "id": 21847,
                "cidade": "SAO PAULO - VIP",
                "sigla": "2",
                "uf": "SP",
                "empresas": "42",
            },
            "data": "2023-01-30",
            "lsServicos": [
                {
                    "servico": "865",
                    "rutaId": 906,
                    "prefixoLinha": "06043451",
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21887,
                    "grupoDestinoId": 21847,
                    "saida": "2023-01-30 10:45",
                    "chegada": "2023-01-30 20:00",
                    "dataCorrida": "2023-01-30",
                    "dataSaida": "2023-01-30",
                    "poltronasLivres": 0,
                    "poltronasTotal": 6,
                    "preco": 269.90,
                    "precoOriginal": 317.57,
                    "classe": "CAMA INDIVIDUAL SUP.",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 470.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "866",
                    "rutaId": 908,
                    "prefixoLinha": "06043451",
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21887,
                    "grupoDestinoId": 21847,
                    "saida": "2023-01-30 10:45",
                    "chegada": "2023-01-30 20:00",
                    "dataCorrida": "2023-01-30",
                    "dataSaida": "2023-01-30",
                    "poltronasLivres": 0,
                    "poltronasTotal": 2,
                    "preco": 269.90,
                    "precoOriginal": 317.57,
                    "classe": "CAMA INDIVIDUAL INF.",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 470.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "905",
                    "rutaId": 962,
                    "prefixoLinha": "06043451",
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21887,
                    "grupoDestinoId": 21847,
                    "saida": "2023-01-30 10:45",
                    "chegada": "2023-01-30 19:30",
                    "dataCorrida": "2023-01-30",
                    "dataSaida": "2023-01-30",
                    "poltronasLivres": 10,
                    "poltronasTotal": 14,
                    "preco": 249.90,
                    "precoOriginal": 266.35,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 615.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "906",
                    "rutaId": 963,
                    "prefixoLinha": "06043451",
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21887,
                    "grupoDestinoId": 21847,
                    "saida": "2023-01-30 10:45",
                    "chegada": "2023-01-30 19:30",
                    "dataCorrida": "2023-01-30",
                    "dataSaida": "2023-01-30",
                    "poltronasLivres": 2,
                    "poltronasTotal": 4,
                    "preco": 249.90,
                    "precoOriginal": 266.35,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 615.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "902",
                    "rutaId": 963,
                    "prefixoLinha": "06043451",
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21887,
                    "grupoDestinoId": 21847,
                    "saida": "2023-01-30 21:45",
                    "chegada": "2023-01-31 06:15",
                    "dataCorrida": "2023-01-30",
                    "dataSaida": "2023-01-30",
                    "poltronasLivres": 1,
                    "poltronasTotal": 4,
                    "preco": 249.90,
                    "precoOriginal": 266.35,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 615.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "901",
                    "rutaId": 962,
                    "prefixoLinha": "06043451",
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21887,
                    "grupoDestinoId": 21847,
                    "saida": "2023-01-30 21:45",
                    "chegada": "2023-01-31 06:15",
                    "dataCorrida": "2023-01-30",
                    "dataSaida": "2023-01-30",
                    "poltronasLivres": 3,
                    "poltronasTotal": 14,
                    "preco": 249.90,
                    "precoOriginal": 266.35,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 615.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "903",
                    "rutaId": 964,
                    "prefixoLinha": "06043451",
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21887,
                    "grupoDestinoId": 21847,
                    "saida": "2023-01-30 21:45",
                    "chegada": "2023-01-31 06:15",
                    "dataCorrida": "2023-01-30",
                    "dataSaida": "2023-01-30",
                    "poltronasLivres": 0,
                    "poltronasTotal": 6,
                    "preco": 269.90,
                    "precoOriginal": 266.35,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 615.0,
                    "cnpj": "26760933000170",
                },
                {
                    "servico": "904",
                    "rutaId": 965,
                    "prefixoLinha": "06043451",
                    "marcaId": 62,
                    "grupo": "VIACA",
                    "grupoOrigemId": 21887,
                    "grupoDestinoId": 21847,
                    "saida": "2023-01-30 21:45",
                    "chegada": "2023-01-31 06:15",
                    "dataCorrida": "2023-01-30",
                    "dataSaida": "2023-01-30",
                    "poltronasLivres": 0,
                    "poltronasTotal": 2,
                    "preco": 269.90,
                    "precoOriginal": 266.35,
                    "classe": "LEITO CAMA",
                    "empresa": "VIACAO LUXOR LTDA",
                    "empresaId": 42,
                    "vende": True,
                    "bpe": False,
                    "km": 615.0,
                    "cnpj": "26760933000170",
                },
            ],
        }

    @staticmethod
    def response_conexao():
        return {
            "origem": {"id": 2772, "cidade": "BRASILIA - DF", "sigla": "BSB", "uf": "DF", "empresas": "-1"},
            "destino": {"id": 18697, "cidade": "SAO PAULO (TIETE) - SP", "sigla": "SAO", "uf": "SP", "empresas": "-1"},
            "data": "2023-12-16",
            "lsServicos": [
                {
                    "servico": "888810",
                    "rutaId": 3628,
                    "prefixoLinha": "12048761",
                    "marcaId": 22,
                    "grupo": "RODER",
                    "grupoOrigemId": 2772,
                    "grupoDestinoId": 18697,
                    "saida": "2023-12-16 15:30",
                    "chegada": "2023-12-17 09:15",
                    "dataCorrida": "2023-12-16",
                    "dataSaida": "2023-12-16",
                    "poltronasLivres": 21,
                    "poltronasTotal": 42,
                    "preco": 352.78,
                    "precoOriginal": 486.24,
                    "classe": "EXECUTIVO",
                    "empresa": "RODE ROTAS",
                    "empresaId": 2,
                    "conexao": {
                        "servicoConexao": "888811",
                        "localidadeConexao": "CALDAS NOVAS - GO",
                        "localidadeConexaoId": 3649,
                        "empresa": "RODE ROTAS",
                        "empresaId": 2,
                        "rutaId": 842,
                        "marcaId": 22,
                        "dataCorridaConexao": "2023-12-16",
                        "dataSaidaConexao": "2023-12-16",
                        "primeiroTrechoPoltronasLivres": 21,
                        "primeiroTrechoPoltronasTotal": 42,
                        "primeiroTrechoClasse": "EXECUTIVO",
                        "primeiroTrechoDataCorrida": "16/12/2023",
                        "primeiroTrechoDataSaida": "16/12/2023",
                        "primeiroTrechoDataChegada": "16/12/2023",
                        "primeiroTrechoHoraSaida": "15:30",
                        "primeiroTrechoHoraChegada": "19:59",
                        "primeiroTrechoPreco": "111.75",
                        "primeiroTrechoPrecoOriginal": "154.31",
                        "primeiroTrechoServico": "888810",
                        "primeiroTrechoLinha": 3628,
                        "primeiroTrechoEmpresa": "RODE ROTAS",
                        "primeiroTrechoEmpresaId": 2,
                        "primeiroTrechoMarca": 22,
                        "primeiroTrechoOrigem": 2772,
                        "primeiroTrechoOrigemDescricao": "BRASILIA - DF",
                        "primeiroTrechoDestino": 3649,
                        "primeiroTrechoDestinoDescricao": "CALDAS NOVAS - GO",
                        "primeiroTrechoVende": True,
                        "primeiroTrechoIsBpe": True,
                        "primeiroTrechoSequencia": 1,
                        "segundoTrechoPoltronasLivres": 21,
                        "segundoTrechoPoltronasTotal": 42,
                        "segundoTrechoClasse": "EXECUTIVO",
                        "segundoTrechoDataCorrida": "15/12/2023",
                        "segundoTrechoDataSaida": "16/12/2023",
                        "segundoTrechoDataChegada": "17/12/2023",
                        "segundoTrechoHoraSaida": "20:00",
                        "segundoTrechoHoraChegada": "09:15",
                        "segundoTrechoPreco": "241.03",
                        "segundoTrechoPrecoOriginal": "331.93",
                        "segundoTrechoServico": "888811",
                        "segundoTrechoLinha": 842,
                        "segundoTrechoEmpresa": "RODE ROTAS",
                        "segundoTrechoEmpresaId": 2,
                        "segundoTrechoMarca": 22,
                        "segundoTrechoOrigem": 3649,
                        "segundoTrechoOrigemDescricao": "CALDAS NOVAS - GO",
                        "segundoTrechoDestino": 18697,
                        "segundoTrechoDestinoDescricao": "SAO PAULO (TIETE) - SP",
                        "segundoTrechoVende": True,
                        "segundoTrechoIsBpe": True,
                        "segundoTrechoSequencia": 2,
                        "terceiroTrechoVende": False,
                        "terceiroTrechoIsBpe": False,
                        "vende": True,
                        "km": 0.0,
                        "cnpj": "18449504000159",
                        "conexionCtrlId": 54912,
                        "conexionGrupo": 1107393,
                        "bpe": True,
                    },
                    "vende": True,
                    "bpe": True,
                    "km": 0.0,
                    "cnpj": "18449504000159",
                    "modalidadeBpe": "",
                }
            ],
        }

    @staticmethod
    def response_conexao_com_tempo_espera():
        return {
            "origem": {"id": 2772, "cidade": "BRASILIA - DF", "sigla": "BSB", "uf": "DF", "empresas": "-1"},
            "destino": {"id": 18697, "cidade": "SAO PAULO (TIETE) - SP", "sigla": "SAO", "uf": "SP", "empresas": "-1"},
            "data": "2023-12-16",
            "lsServicos": [
                {
                    "servico": "888810",
                    "rutaId": 3628,
                    "prefixoLinha": "12048761",
                    "marcaId": 22,
                    "grupo": "RODER",
                    "grupoOrigemId": 2772,
                    "grupoDestinoId": 18697,
                    "saida": "2023-12-16 15:30",
                    "chegada": "2023-12-17 09:15",
                    "dataCorrida": "2023-12-16",
                    "dataSaida": "2023-12-16",
                    "poltronasLivres": 21,
                    "poltronasTotal": 42,
                    "preco": 352.78,
                    "precoOriginal": 486.24,
                    "classe": "EXECUTIVO",
                    "empresa": "RODE ROTAS",
                    "empresaId": 2,
                    "conexao": {
                        "servicoConexao": "888811",
                        "localidadeConexao": "CALDAS NOVAS - GO",
                        "localidadeConexaoId": 3649,
                        "empresa": "RODE ROTAS",
                        "empresaId": 2,
                        "rutaId": 842,
                        "marcaId": 22,
                        "dataCorridaConexao": "2023-12-16",
                        "dataSaidaConexao": "2023-12-16",
                        "primeiroTrechoPoltronasLivres": 21,
                        "primeiroTrechoPoltronasTotal": 42,
                        "primeiroTrechoClasse": "EXECUTIVO",
                        "primeiroTrechoDataCorrida": "16/12/2023",
                        "primeiroTrechoDataSaida": "16/12/2023",
                        "primeiroTrechoDataChegada": "16/12/2023",
                        "primeiroTrechoHoraSaida": "15:30",
                        "primeiroTrechoHoraChegada": "19:59",
                        "primeiroTrechoPreco": "111.75",
                        "primeiroTrechoPrecoOriginal": "154.31",
                        "primeiroTrechoServico": "888810",
                        "primeiroTrechoLinha": 3628,
                        "primeiroTrechoEmpresa": "RODE ROTAS",
                        "primeiroTrechoEmpresaId": 2,
                        "primeiroTrechoMarca": 22,
                        "primeiroTrechoOrigem": 2772,
                        "primeiroTrechoOrigemDescricao": "BRASILIA - DF",
                        "primeiroTrechoDestino": 3649,
                        "primeiroTrechoDestinoDescricao": "CALDAS NOVAS - GO",
                        "primeiroTrechoVende": True,
                        "primeiroTrechoIsBpe": True,
                        "primeiroTrechoSequencia": 1,
                        "segundoTrechoPoltronasLivres": 21,
                        "segundoTrechoPoltronasTotal": 42,
                        "segundoTrechoClasse": "EXECUTIVO",
                        "segundoTrechoDataCorrida": "16/12/2023",
                        "segundoTrechoDataSaida": "16/12/2023",
                        "segundoTrechoDataChegada": "17/12/2023",
                        "segundoTrechoHoraSaida": "21:00",
                        "segundoTrechoHoraChegada": "09:15",
                        "segundoTrechoPreco": "241.03",
                        "segundoTrechoPrecoOriginal": "331.93",
                        "segundoTrechoServico": "888811",
                        "segundoTrechoLinha": 842,
                        "segundoTrechoEmpresa": "RODE ROTAS",
                        "segundoTrechoEmpresaId": 2,
                        "segundoTrechoMarca": 22,
                        "segundoTrechoOrigem": 3649,
                        "segundoTrechoOrigemDescricao": "CALDAS NOVAS - GO",
                        "segundoTrechoDestino": 18697,
                        "segundoTrechoDestinoDescricao": "SAO PAULO (TIETE) - SP",
                        "segundoTrechoVende": True,
                        "segundoTrechoIsBpe": True,
                        "segundoTrechoSequencia": 2,
                        "terceiroTrechoVende": False,
                        "terceiroTrechoIsBpe": False,
                        "vende": True,
                        "km": 0.0,
                        "cnpj": "18449504000159",
                        "conexionCtrlId": 54912,
                        "conexionGrupo": 1107393,
                        "bpe": True,
                    },
                    "vende": True,
                    "bpe": True,
                    "km": 0.0,
                    "cnpj": "18449504000159",
                    "modalidadeBpe": "",
                }
            ],
        }


class BuscarTodosServicosDisponiveis:
    @staticmethod
    def response():
        return {
            "servicos": [
                {
                    "numservico": 1,
                    "horaServico": "06:10:00",
                    "origem": {"id": 21918, "descripcion": "DRACENA - SP"},
                    "destino": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                },
                {
                    "numservico": 2,
                    "horaServico": "07:15:00",
                    "origem": {"id": 21918, "descripcion": "DRACENA - SP"},
                    "destino": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                },
                {
                    "numservico": 4,
                    "horaServico": "12:20:00",
                    "origem": {"id": 21918, "descripcion": "DRACENA - SP"},
                    "destino": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                },
                {
                    "numservico": 5,
                    "horaServico": "13:50:00",
                    "origem": {"id": 21918, "descripcion": "DRACENA - SP"},
                    "destino": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                },
                {
                    "numservico": 6,
                    "horaServico": "13:15:00",
                    "origem": {"id": 21918, "descripcion": "DRACENA - SP"},
                    "destino": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                },
                {
                    "numservico": 7,
                    "horaServico": "16:00:00",
                    "origem": {"id": 21918, "descripcion": "DRACENA - SP"},
                    "destino": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                },
                {
                    "numservico": 8,
                    "horaServico": "17:40:00",
                    "origem": {"id": 21918, "descripcion": "DRACENA - SP"},
                    "destino": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                },
                {
                    "numservico": 21,
                    "horaServico": "06:40:00",
                    "origem": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                    "destino": {"id": 21918, "descripcion": "DRACENA - SP"},
                },
                {
                    "numservico": 22,
                    "horaServico": "07:50:00",
                    "origem": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                    "destino": {"id": 21918, "descripcion": "DRACENA - SP"},
                },
                {
                    "numservico": 23,
                    "horaServico": "09:50:00",
                    "origem": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                    "destino": {"id": 21918, "descripcion": "DRACENA - SP"},
                },
                {
                    "numservico": 24,
                    "horaServico": "12:20:00",
                    "origem": {"id": 21915, "descripcion": "PACAEMBU - SP"},
                    "destino": {"id": 21918, "descripcion": "DRACENA - SP"},
                },
            ],
            "totalPaginacao": None,
        }


class ConsultarEmpresas:
    @staticmethod
    def response():
        return [
            {"id": 1, "nome": "LEVARE TRANSPORTES LTDA", "cnpj": "09399877000171"},
            {"id": 42, "nome": "VIACAO LUXOR LTDA.", "cnpj": "26760933000170"},
            {
                "id": 62,
                "nome": "TRANSRAPIDO SÃO FRANCISCO LTDA",
                "cnpj": "72951635000185",
            },
        ]


class ConsultarCategoriaCorrida:
    @staticmethod
    def response():
        return [
            {
                "categoriaId": 1,
                "desccategoria": "NORMAL",
                "disponibilidadeCota": 38,
                "gratuidadeCrianca": False,
                "estudante": False,
                "idoso": False,
                "orgaoConcedente": [
                    {"orgaoConcedenteId": 3, "descOrgao": "ANTT", "idadeMinimaIdoso": 60, "idadeMaximaCrianca": 5}
                ],
                "desconto": {
                    "importeTarifa": 200.23,
                    "importePedagio": 19.73,
                    "importeSeguro": 0.00,
                    "importeTaxaEmbarque": 6.03,
                    "importeOutros": 0.00,
                    "importeTarifaSeguro": 219.96,
                    "importeTarifaTaxa": 225.99,
                    "importeTarifaPedagio": 219.96,
                    "importeTarifaOutros": 219.96,
                    "importeTarifaTotal": 225.99,
                    "importeDesconto": 0.00,
                    "pricingAplicado": "",
                    "cotaObrigatoria": False,
                    "quantidadeCota": 200,
                    "assentosReservados": "",
                    "exigirNome": True,
                    "exigirDocumento": True,
                    "exigirTelefone": True,
                    "exigirDataNascimento": False,
                    "exigirEndereco": False,
                    "exigirEmail": False,
                    "clientePcd": False,
                    "naoPermiteVendaMesmoDocViagem": False,
                    "naoPermiteVendaDuasGratuidades": False,
                    "naoAplicaTarifaMinima": False,
                },
                "vendeApi": True,
                "seguroOpcionalKm": 0,
                "seguroOpcionalValor": 0,
            },
            {
                "categoriaId": 30,
                "desccategoria": "ESPAÇO MULHER",
                "disponibilidadeCota": 8,
                "gratuidadeCrianca": False,
                "estudante": False,
                "idoso": False,
                "orgaoConcedente": [
                    {"orgaoConcedenteId": 3, "descOrgao": "ANTT", "idadeMinimaIdoso": 60, "idadeMaximaCrianca": 5}
                ],
                "desconto": {
                    "importeTarifa": 200.23,
                    "importePedagio": 19.73,
                    "importeSeguro": 0.00,
                    "importeTaxaEmbarque": 6.03,
                    "importeOutros": 0.00,
                    "importeTarifaSeguro": 219.96,
                    "importeTarifaTaxa": 225.99,
                    "importeTarifaPedagio": 219.96,
                    "importeTarifaOutros": 219.96,
                    "importeTarifaTotal": 225.99,
                    "importeDesconto": 0.00,
                    "pricingAplicado": "",
                    "cotaObrigatoria": True,
                    "quantidadeCota": 8,
                    "assentosReservados": "13;14;9;10",
                    "exigirNome": True,
                    "exigirDocumento": True,
                    "exigirTelefone": True,
                    "exigirDataNascimento": False,
                    "exigirEndereco": False,
                    "exigirEmail": False,
                    "clientePcd": False,
                    "naoPermiteVendaMesmoDocViagem": False,
                    "naoPermiteVendaDuasGratuidades": False,
                    "naoAplicaTarifaMinima": False,
                },
                "vendeApi": True,
                "seguroOpcionalKm": 0,
                "seguroOpcionalValor": 0,
            },
            {
                "categoriaId": 33,
                "desccategoria": "ESPAÇO PET",
                "disponibilidadeCota": 4,
                "gratuidadeCrianca": False,
                "estudante": False,
                "idoso": False,
                "orgaoConcedente": [
                    {"orgaoConcedenteId": 3, "descOrgao": "ANTT", "idadeMinimaIdoso": 60, "idadeMaximaCrianca": 5}
                ],
                "desconto": {
                    "importeTarifa": 200.23,
                    "importePedagio": 19.73,
                    "importeSeguro": 0.00,
                    "importeTaxaEmbarque": 6.03,
                    "importeOutros": 0.00,
                    "importeTarifaSeguro": 219.96,
                    "importeTarifaTaxa": 225.99,
                    "importeTarifaPedagio": 219.96,
                    "importeTarifaOutros": 219.96,
                    "importeTarifaTotal": 225.99,
                    "importeDesconto": 0.00,
                    "pricingAplicado": "",
                    "cotaObrigatoria": False,
                    "quantidadeCota": 4,
                    "assentosReservados": "",
                    "exigirNome": True,
                    "exigirDocumento": True,
                    "exigirTelefone": True,
                    "exigirDataNascimento": False,
                    "exigirEndereco": False,
                    "exigirEmail": False,
                    "clientePcd": False,
                    "naoPermiteVendaMesmoDocViagem": False,
                    "naoPermiteVendaDuasGratuidades": False,
                    "naoAplicaTarifaMinima": False,
                },
                "vendeApi": True,
                "seguroOpcionalKm": 0,
                "seguroOpcionalValor": 0,
            },
        ]

    @staticmethod
    def response_connection_error():
        return {
            "timestamp": "2024-10-21T13:35:30.449",
            "status": 500,
            "message": "Unable to acquire JDBC Connection; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection",
            "mensagem": "Unable to acquire JDBC Connection; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection",
            "error": "Unable to acquire JDBC Connection; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection",
            "id": "6ce45fa1c1384cbe",
        }

    @staticmethod
    def response_com_categoria_buser():
        return [
            {
                "categoriaId": 1,
                "desccategoria": "NORMAL",
                "disponibilidadeCota": 38,
                "gratuidadeCrianca": False,
                "estudante": False,
                "idoso": False,
                "orgaoConcedente": [
                    {"orgaoConcedenteId": 3, "descOrgao": "ANTT", "idadeMinimaIdoso": 60, "idadeMaximaCrianca": 5}
                ],
                "desconto": {
                    "importeTarifa": 200.23,
                    "importePedagio": 19.73,
                    "importeSeguro": 0.00,
                    "importeTaxaEmbarque": 6.03,
                    "importeOutros": 0.00,
                    "importeTarifaSeguro": 219.96,
                    "importeTarifaTaxa": 225.99,
                    "importeTarifaPedagio": 219.96,
                    "importeTarifaOutros": 219.96,
                    "importeTarifaTotal": 225.99,
                    "importeDesconto": 0.00,
                    "pricingAplicado": "",
                    "cotaObrigatoria": False,
                    "quantidadeCota": 200,
                    "assentosReservados": "",
                    "exigirNome": True,
                    "exigirDocumento": True,
                    "exigirTelefone": True,
                    "exigirDataNascimento": False,
                    "exigirEndereco": False,
                    "exigirEmail": False,
                    "clientePcd": False,
                    "naoPermiteVendaMesmoDocViagem": False,
                    "naoPermiteVendaDuasGratuidades": False,
                    "naoAplicaTarifaMinima": False,
                },
                "vendeApi": True,
                "seguroOpcionalKm": 0,
                "seguroOpcionalValor": 0,
            },
            {
                "categoriaId": 30,
                "desccategoria": "BUSER",
                "disponibilidadeCota": 3,
                "gratuidadeCrianca": False,
                "estudante": False,
                "idoso": False,
                "orgaoConcedente": [
                    {"orgaoConcedenteId": 3, "descOrgao": "ANTT", "idadeMinimaIdoso": 60, "idadeMaximaCrianca": 5}
                ],
                "desconto": {
                    "importeTarifa": 200.23,
                    "importePedagio": 19.73,
                    "importeSeguro": 0.00,
                    "importeTaxaEmbarque": 6.03,
                    "importeOutros": 0.00,
                    "importeTarifaSeguro": 219.96,
                    "importeTarifaTaxa": 225.99,
                    "importeTarifaPedagio": 219.96,
                    "importeTarifaOutros": 219.96,
                    "importeTarifaTotal": 225.99,
                    "importeDesconto": 0.00,
                    "pricingAplicado": "",
                    "cotaObrigatoria": True,
                    "quantidadeCota": 8,
                    "assentosReservados": "13;14;9;10",
                    "exigirNome": True,
                    "exigirDocumento": True,
                    "exigirTelefone": True,
                    "exigirDataNascimento": False,
                    "exigirEndereco": False,
                    "exigirEmail": False,
                    "clientePcd": False,
                    "naoPermiteVendaMesmoDocViagem": False,
                    "naoPermiteVendaDuasGratuidades": False,
                    "naoAplicaTarifaMinima": False,
                },
                "vendeApi": True,
                "seguroOpcionalKm": 0,
                "seguroOpcionalValor": 0,
            },
        ]


class ConsultarCategoriaEmpresa:
    @staticmethod
    def response():
        return [
            {"categoriaId": 17, "desccategoria": "ANIMAL DE ESTIMAÇÃO", "vendeApi": False},
            {"categoriaId": 12, "desccategoria": "CORTESIA EMPRESA", "vendeApi": False},
            {"categoriaId": 5, "desccategoria": "DEFICIENTE 100%", "vendeApi": False},
            {"categoriaId": 9, "desccategoria": "DEFICIENTE PR", "vendeApi": False},
            {"categoriaId": 14, "desccategoria": "DESLOCAMENTO FUNCIONARIO", "vendeApi": False},
            {"categoriaId": 15, "desccategoria": "DESLOCAMENTO MOTORISTA", "vendeApi": False},
            {"categoriaId": 3, "desccategoria": "FIDELIDADE", "vendeApi": False},
            {"categoriaId": 10, "desccategoria": "FISCAL DO TRABALHO", "vendeApi": False},
            {"categoriaId": 11, "desccategoria": "GRATUIDADE CRIANCA", "vendeApi": False},
            {"categoriaId": 4, "desccategoria": "IDOSO 100%", "vendeApi": False},
            {"categoriaId": 20, "desccategoria": "IDOSO 50%", "vendeApi": False},
            {"categoriaId": 7, "desccategoria": "JOVEM 100%", "vendeApi": False},
            {"categoriaId": 8, "desccategoria": "JOVEM 50%", "vendeApi": False},
            {"categoriaId": 1, "desccategoria": "NORMAL", "vendeApi": False},
            {"categoriaId": 16, "desccategoria": "PASSE LIVRE EMPRESA", "vendeApi": False},
            {"categoriaId": 13, "desccategoria": "POLICIAL RODOVIARIO", "vendeApi": False},
            {"categoriaId": 18, "desccategoria": "TROCAS/ABERTOS SRVP", "vendeApi": False},
        ]


class BuscarFormasPagamento:
    @staticmethod
    def response():
        return [
            {"id": 2, "descricao": "CRÉDITO", "tipoPago": 2},
            {"id": 1, "descricao": "DINHEIRO"},
            {"id": 3, "descricao": "DÉBITO", "tipoPago": 1},
        ]


class BuscarServicosDetalhado:
    @staticmethod
    def response_sem_servico():
        return []

    @staticmethod
    def response_data_invalida():
        """Ocorre quando a data informada não está no formado YYYY-MM-DD HH:mm"""
        return {
            "timestamp": "2024-01-25T11:10:06.413",
            "status": 400,
            "message": "Data Inicial inválida",
            "mensagem": "Data Inicial inválida",
            "error": "Data Inicial inválida",
            "id": "70c493dd27fd7e48",
        }

    @staticmethod
    def response_jdbc_connection_error():
        """Ocorre um erro de conexão interno do parceiro com o banco de dados"""
        return {
            "timestamp": "2024-10-16T21:40:44.438",
            "status": 400,
            "message": "Unable to acquire JDBC Connection; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection",
            "mensagem": "Unable to acquire JDBC Connection; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection",
            "error": "Unable to acquire JDBC Connection; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection",
            "id": "b6125fa0c3de81d0",
        }

    @staticmethod
    def response():
        return [
            {
                "servicoId": 1022,
                "servico": "172",
                "esquemaId": 62,
                "empresaId": 42,
                "via": 82,
                "vigencia": "20501231",
                "vigenciaInicial": "2022-06-13 00:00:00",
                "vigenciaFinal": "2050-12-31 00:00:00",
                "feriados": True,
                "horaInicio": "06:00",
                "servicoExtra": False,
                "frequencia": "|X|X|X|X|X|X|X|",
                "origem": 2,
                "destino": 21867,
                "linhaId": 182,
                "dataHoraServico": "2024-01-20 06:00:00",
                "dataHoraOriginalServico": "2024-01-20 06:00:00",
                "dataSaida": "2024-01-20 06:00:00",
                "horaSaida": "06:00",
                "dataChegada": "2024-01-20 06:15:00",
                "horaChegada": "06:15",
                "dataHoraModificacao": "2023-10-23 03:01:34",
                "descTipoServico": "NORMAL",
                "tipoCarroId": 63,
                "status": 1,
                "descricaoStatus": "Ativo",
                "descricaoStatusCorrida": "Fora de range",
                "classeId": 2,
                "sentidoIda": 1,
                "motoristas": [{"nome": "FRANCIVALDO BARBOSA DE LIMA", "cve": "79"}],
                "veiculo": "QWP0848",
                "linha": "RIO BRANCO X BOCA DO ACRE",
                "trechos": [
                    {
                        "trechoId": 4902,
                        "dataInicio": "2024-01-20 06:00:00",
                        "dataFinal": "2024-01-20 06:15:00",
                        "descOrigem": "RIO BRANCO - BR",
                        "descDestino": "BOCA DO ACRE",
                        "origemIBGE": 1200401,
                        "destinoIBGE": 1300706,
                        "origem": 2,
                        "destino": 21867,
                        "horaInicio": "06:00",
                        "horaFinal": "06:15",
                        "vendaId": 1,
                        "kmReal": "10",
                        "sequencia": 1,
                        "tempoExcecao": "",
                        "tempoTrecho": "",
                        "via": "82",
                    },
                    {
                        "trechoId": 1637,
                        "dataInicio": "2024-01-20 06:15:00",
                        "dataFinal": "2024-01-20 06:35:00",
                        "descOrigem": "BOCA DO ACRE",
                        "descDestino": "KM 40",
                        "origemIBGE": 1300706,
                        "destinoIBGE": 1200138,
                        "origem": 21867,
                        "destino": 21817,
                        "horaInicio": "06:15",
                        "horaFinal": "06:35",
                        "vendaId": 1,
                        "kmReal": "16",
                        "sequencia": 2,
                        "tempoExcecao": "",
                        "tempoTrecho": "00:20",
                        "via": "45",
                    },
                ],
            },
            {
                "servicoId": 8012,
                "servico": "16",
                "esquemaId": 25,
                "empresaId": 42,
                "via": 45,
                "vigencia": "20501231",
                "vigenciaInicial": "2022-04-11 00:00:00",
                "vigenciaFinal": "2050-12-31 00:00:00",
                "feriados": True,
                "horaInicio": "19:10",
                "servicoExtra": False,
                "frequencia": "|X|X|X|X|X|X|X|",
                "origem": 22,
                "destino": 21863,
                "linhaId": 143,
                "dataHoraServico": "2024-01-20 19:10:00",
                "dataHoraOriginalServico": "2024-01-20 19:10:00",
                "dataSaida": "2024-01-20 19:10:00",
                "horaSaida": "19:10",
                "dataChegada": "2024-01-20 19:25:00",
                "horaChegada": "19:25",
                "dataHoraModificacao": "2024-01-20 16:02:29",
                "descTipoServico": "EXTRA",
                "tipoCarroId": 222,
                "status": 1,
                "descricaoStatus": "Ativo",
                "descricaoStatusCorrida": "Fora de range",
                "classeId": 1,
                "sentidoIda": 0,
                "motoristas": [],
                "linha": "LAGOINHA X CRUZEIRO DO SUL",
                "trechos": [
                    {
                        "trechoId": 3733,
                        "dataInicio": "2024-01-20 19:10:00",
                        "dataFinal": "2024-01-20 19:25:00",
                        "descOrigem": "CRUZEIRO DO SUL - AC",
                        "descDestino": "VAR.ROD.ALVES",
                        "origemIBGE": 1200203,
                        "destinoIBGE": 1200203,
                        "origem": 22,
                        "destino": 21863,
                        "horaInicio": "19:10",
                        "horaFinal": "19:25",
                        "vendaId": 1,
                        "kmReal": "38",
                        "sequencia": 1,
                        "tempoExcecao": "",
                        "tempoTrecho": "00:15",
                        "via": "45",
                    },
                    {
                        "trechoId": 3735,
                        "dataInicio": "2024-01-20 19:25:00",
                        "dataFinal": "2024-01-20 19:40:00",
                        "descOrigem": "VAR.ROD.ALVES",
                        "descDestino": "LAGOINHA",
                        "origemIBGE": 1300706,
                        "destinoIBGE": 1200203,
                        "origem": 21863,
                        "destino": 21862,
                        "horaInicio": "19:25",
                        "horaFinal": "19:40",
                        "vendaId": 1,
                        "kmReal": "32",
                        "sequencia": 2,
                        "tempoExcecao": "",
                        "tempoTrecho": "00:15",
                        "via": "45",
                    },
                ],
            },
            {
                "servicoId": 1000,
                "servico": "16",
                "esquemaId": 22,
                "empresaId": 42,
                "via": 45,
                "vigencia": "20501231",
                "vigenciaInicial": "2022-04-11 00:00:00",
                "vigenciaFinal": "2050-12-31 00:00:00",
                "feriados": True,
                "horaInicio": "19:00",
                "servicoExtra": False,
                "frequencia": "|X|X|X|X|X|X|X|",
                "origem": 2,
                "destino": 3,
                "linhaId": 142,
                "dataHoraServico": "2024-01-20 19:00:00",
                "dataHoraOriginalServico": "2024-01-20 19:00:00",
                "dataSaida": "2024-01-20 19:00:00",
                "horaSaida": "19:00",
                "dataChegada": "2024-01-20 19:15:00",
                "horaChegada": "19:15",
                "dataHoraModificacao": "2023-10-27 16:43:16",
                "descTipoServico": "NORMAL",
                "tipoCarroId": 22,
                "status": 1,
                "descricaoStatus": "Ativo",
                "descricaoStatusCorrida": "Fora de range",
                "classeId": 1,
                "sentidoIda": 1,
                "motoristas": [],
                "linha": "RIO BRANCO X BUJARI - AC",
                "trechos": [
                    {
                        "trechoId": 1602,
                        "dataInicio": "2024-01-20 19:00:00",
                        "dataFinal": "2024-01-20 19:15:00",
                        "descOrigem": "RIO BRANCO - BR",
                        "descDestino": "BOCA DO ACRE",
                        "origemIBGE": 1200401,
                        "destinoIBGE": 1300706,
                        "origem": 2,
                        "destino": 21867,
                        "horaInicio": "19:00",
                        "horaFinal": "19:15",
                        "vendaId": 1,
                        "kmReal": "24",
                        "sequencia": 1,
                        "tempoExcecao": "",
                        "tempoTrecho": "00:15",
                        "via": "45",
                    },
                    {
                        "trechoId": 1637,
                        "dataInicio": "2024-01-20 19:15:00",
                        "dataFinal": "2024-01-20 19:35:00",
                        "descOrigem": "BOCA DO ACRE",
                        "descDestino": "KM 40",
                        "origemIBGE": 1200138,
                        "destinoIBGE": 1200138,
                        "origem": 21867,
                        "destino": 21817,
                        "horaInicio": "19:15",
                        "horaFinal": "19:35",
                        "vendaId": 1,
                        "kmReal": "16",
                        "sequencia": 2,
                        "tempoExcecao": "",
                        "tempoTrecho": "00:20",
                        "via": "45",
                    },
                ],
            },
        ]

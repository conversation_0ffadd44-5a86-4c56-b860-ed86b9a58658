from django.db import connections
from model_bakery import baker

from rodoviaria.api.totalbus.auth import TotalbusAuth


def test_auth_grava_header_request_from_company(mocker, django_assert_num_queries):
    company = baker.make("rodoviaria.Company")
    login = baker.make(
        "rodoviaria.TotalbusLogin", company=company, password="buser", user="totalbus", tenant_id="server-1"
    )
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        auth = TotalbusAuth.from_company(company)

    request = mocker.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["Authorization"] == "Basic dG90YWxidXM6YnVzZXI="
    assert request.headers["X-tenant-id"] == login.tenant_id


def test_auth_grava_header_request_from_client(mocker, django_assert_num_queries):
    """Auth a partir do client não executa query no bd."""
    company = baker.make("rodoviaria.Company")
    client_login = baker.make(
        "rodoviaria.TotalbusLogin", company=company, password="buser", user="totalbus", tenant_id="server-1"
    )
    with django_assert_num_queries(0, connection=connections["rodoviaria"]):
        auth = TotalbusAuth.from_client(client_login)
    request = mocker.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["Authorization"] == "Basic dG90YWxidXM6YnVzZXI="
    assert request.headers["X-tenant-id"] == client_login.tenant_id

import json
from unittest import mock

import responses
from model_bakery import baker

from rodoviaria import views
from rodoviaria.api.totalbus import endpoints
from rodoviaria.models.core import CompanyCategoriaEspecial, Passagem, TrechoClasse, TrechoClasseError
from rodoviaria.service import novos_modelos_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.tests.middleware import request_with_middleware


def test_get_poltronas(
    rf,
    totalbus_trechoclasses,
    mock_totalbus_get_poltronas,
    totalbus_grupos_mockado,
):
    old_tc = totalbus_trechoclasses.ida
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = old_tc.grupo
        request = rf.get("/rodoviaria/get_poltronas")
        response = views.get_poltronas(
            request,
            trecho_classe_id=old_tc.trechoclasse_internal_id,
            num_passageiros=2,
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        poltronas_livres = [32, 31, 30, 40, 44, 43, 42, 41, 46, 45, 36]
        assert data[0] in poltronas_livres and data[1] in poltronas_livres and data[0] != data[1]


def test_api_totalbus_get_poltronas_busca_normais(totalbus_api, totalbus_login, requests_mock):
    mocked_response = {
        "mapaPoltrona": [
            {"x": "1", "y": "0", "disponivel": True, "numero": "01", "categoriaReservadaId": -1},
            {"x": "1", "y": "1", "disponivel": False, "numero": "02", "categoriaReservadaId": -1},
            {"x": "1", "y": "3", "disponivel": False, "numero": "03", "categoriaReservadaId": -1},
            {"x": "3", "y": "0", "disponivel": True, "numero": "04", "categoriaReservadaId": 30},
            {"x": "3", "y": "1", "disponivel": True, "numero": "05", "categoriaReservadaId": 30},
        ],
    }

    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(totalbus_login).url,
        json=mocked_response,
    )

    params = {"origem": 3603, "destino": 12722, "data": "2024-07-26", "servico": 111123}
    # ao consultar as poltronas livres para um determinado servico
    poltronas = totalbus_api._map_poltronas_from_api(
        params=params, categoria_especial=Passagem.CategoriaEspecial.NORMAL
    )

    # somente poltronas normais
    assert poltronas == {
        "01": "livre",
        "02": "ocupada",
        "03": "ocupada",
    }


def test_api_totalbus_get_poltronas_nao_pega_normais_novo_modelo_compra_antecipada(
    totalbus_api, totalbus_login, requests_mock
):
    totalbus_api.company.company_internal_id = novos_modelos_svc.VIACAO_ADAMANTINA_INTERNAL_ID
    mocked_response = {
        "mapaPoltrona": [
            {"x": "1", "y": "0", "disponivel": True, "numero": "01", "categoriaReservadaId": -1},
            {"x": "1", "y": "1", "disponivel": False, "numero": "02", "categoriaReservadaId": -1},
            {"x": "1", "y": "3", "disponivel": False, "numero": "03", "categoriaReservadaId": -1},
            {"x": "3", "y": "0", "disponivel": False, "numero": "04", "categoriaReservadaId": 30},
            {"x": "3", "y": "1", "disponivel": True, "numero": "05", "categoriaReservadaId": 30},
        ],
    }

    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(totalbus_login).url,
        json=mocked_response,
    )
    baker.make(
        CompanyCategoriaEspecial,
        company=totalbus_api.company,
        categoria_especial=Passagem.CategoriaEspecial.NORMAL,
        categoria_id_external=30,
        descricao_external="BUSER",
    )

    params = {"origem": 3603, "destino": 12722, "data": "2024-07-26", "servico": 111123}
    # ao consultar as poltronas livres para um determinado servico
    poltronas = totalbus_api._map_poltronas_from_api(
        params=params, categoria_especial=Passagem.CategoriaEspecial.NORMAL
    )

    # somente poltronas normais
    assert poltronas == {
        "04": "ocupada",
        "05": "livre",
    }


def test_api_totalbus_get_poltronas_busca_especial(totalbus_api, totalbus_login, requests_mock):
    # dada uma consulta do mapa de poltronas de uma determinada
    # empresa onde a categoriaReservadaId -1 (Normal) e 30 (Idoso)
    mocked_response = {
        "mapaPoltrona": [
            {"x": "1", "y": "0", "disponivel": True, "numero": "01", "categoriaReservadaId": -1},
            {"x": "1", "y": "1", "disponivel": False, "numero": "02", "categoriaReservadaId": -1},
            {"x": "1", "y": "3", "disponivel": False, "numero": "03", "categoriaReservadaId": -1},
            {"x": "3", "y": "0", "disponivel": False, "numero": "04", "categoriaReservadaId": 30},
            {"x": "3", "y": "1", "disponivel": True, "numero": "05", "categoriaReservadaId": 30},
        ],
    }

    requests_mock.add(
        responses.POST,
        endpoints.RetornaPoltronasConfig(totalbus_login).url,
        json=mocked_response,
    )
    baker.make(
        CompanyCategoriaEspecial,
        company=totalbus_api.company,
        categoria_especial=Passagem.CategoriaEspecial.IDOSO_100,
        categoria_id_external=30,
        descricao_external="IDOSO 100%",
    )
    params = {"origem": 3603, "destino": 12722, "data": "2024-07-26", "servico": 111123}
    # ao consultar as poltronas livres para um determinado servico
    # filtrando pela categoria idoso.
    poltronas = totalbus_api._map_poltronas_from_api(
        params=params, categoria_especial=Passagem.CategoriaEspecial.IDOSO_100
    )

    # espero que todas as poltronas sejam retornadas.
    assert poltronas == {
        "01": "livre",
        "02": "ocupada",
        "03": "ocupada",
        "04": "ocupada",
        "05": "livre",
    }


def test_get_poltronas_overbooking(rf, totalbus_trechoclasses, mock_totalbus_poltronas_insuficientes):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = totalbus_trechoclasses.ida.grupo
        request = rf.get(
            "/rodoviaria/v1/get_poltronas",
            data={"trecho_classe_id": totalbus_trechoclasses.ida.trechoclasse_internal_id, "num_passageiros": 2},
        )

        response = request_with_middleware(request)

        assert response.status_code == 444
        data = json.loads(response.content)
        assert data == {
            "error": "Apenas 1 poltrona disponível para esta viagem",
            "error_type": "overbooking",
            "vagas_disponiveis": 1,
        }


def test_get_poltronas_connection_error(rf, totalbus_trechoclasses, mock_totalbus_connection_error_antes_pagamento):
    with mock.patch.object(
        CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria"
    ) as mock_tenta_criar_grupo_rodoviaria, mock.patch.object(TrechoClasse, "save"):
        mock_tenta_criar_grupo_rodoviaria.return_value = totalbus_trechoclasses.ida.grupo
        request = rf.get(
            "/rodoviaria/v1/get_poltronas",
            data={"trecho_classe_id": totalbus_trechoclasses.ida.trechoclasse_internal_id, "num_passageiros": 2},
        )

        response = request_with_middleware(request)

        assert response.status_code == 504
        data = json.loads(response.content)
        assert data == {
            "error": "totalbus http://totalbus.base/consultaonibus/buscaOnibus connection error",
            "error_type": "connection_error",
        }


def test_service_not_found(
    rf,
    buser_grupos,
    totalbus_company,
    totalbus_locais,
    mock_buscar_servico_totalbus_sem_servico,
    mock_totalbus_login,
    buser_company_expresso,
):
    # adaptação enquanto a factory ainda depende do buser_django
    totalbus_company.company_internal_id = buser_company_expresso.id
    totalbus_company.save()
    request = rf.get(
        "/rodoviaria/v1/get_poltronas",
        data={"trecho_classe_id": buser_grupos.unlinked_ida.trechoclasse.id, "num_passageiros": 2},
    )

    response = request_with_middleware(request)

    assert response.status_code == 404
    data = json.loads(response.content)
    assert data == {
        "error": f"Serviço não encontrado na API: [{TrechoClasseError.Motivo.SEM_SERVICO}]",
        "error_type": "service_not_found",
    }

from datetime import UTC, date, datetime
from decimal import Decimal
from unittest import mock

import pytest
from celery import shared_task
from celery.exceptions import Retry
from django.db import connections, reset_queries
from django.test import override_settings

from bp.settings_celery import _orjson_default, _orjson_loads
from commons import celery_utils
from commons.circuit_breaker import MyCircuitBreakerError
from commons.token_bucket import NotEnoughTokens
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.models import TaskStatus

QUEUE_NAMES = [
    "bp_cron",
    "bp_descobrir_rotas",
    "bp_trechos_vendidos",
    "bp_rotinas",
    "bp_expresso_adamantina",
]


@shared_task(queue="bp_ping", name="tasks_counter")
def dummy_task_counter():
    return TaskStatus.objects.all().count()


@pytest.mark.parametrize("q", QUEUE_NAMES)
def test_is_deployed_queue(q):
    assert celery_utils.is_deployed_queue(q)


def test_non_deployed_queue():
    assert not celery_utils.is_deployed_queue("bp_fila_inexixtente")


def test_deployed_queue_name(totalbus_login):
    with mock.patch("commons.celery_utils.is_deployed_queue") as mock_is_deployed_queue:
        mock_is_deployed_queue.return_value = True
        totalbus_api = TotalbusAPI(totalbus_login.company)
        assert totalbus_api.queue_name == "bp_totalbus_transporte"


def test_deployed_queue_name_com_xa0_no_nome(totalbus_login):
    totalbus_login.company.name = "Totalbus Transporte\xa0LTDA"
    totalbus_login.company.save()
    with mock.patch("commons.celery_utils.is_deployed_queue") as mock_is_deployed_queue:
        mock_is_deployed_queue.return_value = True
        totalbus_api = TotalbusAPI(totalbus_login.company)
        assert totalbus_api.queue_name == "bp_totalbus_transporte_ltda"


def test_non_deployed_queue_name(totalbus_login):
    with mock.patch("commons.celery_utils.is_deployed_queue") as mock_is_deployed_queue:
        mock_is_deployed_queue.return_value = False
        totalbus_api = TotalbusAPI(totalbus_login.company)
        assert totalbus_api.queue_name is None


@shared_task
@celery_utils.retry((MyCircuitBreakerError, NotEnoughTokens, TypeError), max_retries=1)
def task():
    _inner()


def _inner():
    pass


@pytest.mark.parametrize(
    "exp, offset",
    [
        (NotEnoughTokens(remaining_seconds=-1), 20),
        (MyCircuitBreakerError("deu ruim", remaining_seconds=5), 5),
        (MyCircuitBreakerError("deu ruim quebrado", remaining_seconds=5.1), 6),
        (NotEnoughTokens(remaining_seconds=5), 5),
        (TypeError(), 20),
    ],
)
def test_retry_respeita_remaining_seconds(mocker, exp, offset):
    mocker.patch("rodoviaria.tests.test_celery_utils._inner", side_effect=[exp, None])
    mock_exponencial_backoff_interval = mocker.patch("commons.celery_utils.get_exponential_backoff_interval")
    with pytest.raises(Retry):
        task.apply().get()
    mock_exponencial_backoff_interval.assert_called_once_with(
        factor=15, retries=0, maximum=30 * 60, full_jitter=True, offset=offset
    )


def test_get_exponential_backoff_interval_offset():
    countdown = celery_utils.get_exponential_backoff_interval(20, 0, maximum=15 * 60, full_jitter=True, offset=5)
    assert countdown >= 5


@override_settings(DEBUG=True)
def test_create_task():
    rodov = connections["rodoviaria"]
    reset_queries()
    dummy_task_counter.delay()
    query_sql = rodov.queries[0]["sql"]
    task_prefix = query_sql.split("\n")[0]
    assert task_prefix == "/* task=bp.celery.tasks_counter */"


@pytest.mark.parametrize(
    "input,expected",
    [
        (
            datetime(2024, 1, 14, 15),
            {"__type__": "datetime", "__value__": "2024-01-14T15:00:00"},
        ),
        (
            datetime(2024, 1, 14, 15, tzinfo=UTC),
            {"__type__": "datetime", "__value__": "2024-01-14T15:00:00+00:00"},
        ),
        (
            date(2024, 1, 14),
            {"__type__": "date", "__value__": "2024-01-14"},
        ),
        (
            Decimal("123.456789"),
            {"__type__": "decimal", "__value__": "123.456789"},
        ),
    ],
)
def test_orjson_dumps(input, expected):
    assert _orjson_default(input) == expected


@pytest.mark.parametrize(
    "input,expected",
    [
        (
            '{"__type__": "datetime", "__value__": "2024-01-14T15:00:00"}',
            datetime(2024, 1, 14, 15),
        ),
        (
            '{"__type__": "datetime", "__value__": "2024-01-14T15:00:00+00:00"}',
            datetime(2024, 1, 14, 15, tzinfo=UTC),
        ),
        (
            '{"__type__": "date", "__value__": "2024-01-14"}',
            date(2024, 1, 14),
        ),
        (
            '{"__type__": "decimal", "__value__": "123.456789"}',
            Decimal("123.456789"),
        ),
    ],
)
def test_orjson_loads(input, expected):
    assert _orjson_loads(input) == expected

import logging
from datetime import datetime, timedelta
from decimal import Decimal
from types import SimpleNamespace
from unittest import mock

import pytest
import time_machine
from celery.exceptions import Retry
from django.db import connections
from django.utils import timezone
from model_bakery import baker
from redis.exceptions import LockError
from zoneinfo import ZoneInfo

from commons.dateutils import to_tz, today_midnight
from rodoviaria.api.forms import ServicoForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import (
    Cidade,
    Company,
    LocalEmbarque,
    TrechoClasse,
    TrechoClasseError,
    TrechoVendido,
)
from rodoviaria.service.exceptions import RodoviariaTooManyRequestsError
from rodoviaria.service.link_trechoclasse_async_svc import (
    DEFAULT_QUEUE_NAME,
    TAG_TO_BE_CLOSED,
    TAG_TO_BE_UPDATED,
    UPDATE_PRICE_QUEUE_NAME,
    _generate_tasks,
    _get_locais_map,
    add_tag_on_trechos_error_in_bulk,
    add_tag_on_trechos_in_bulk,
    buscar_dados_api_default_rate_limit,
    get_tagged_trechos_classes,
    link_trechoclasse_async,
    remover_tags_trechos_atualizados,
    salvar_trecho_classe_error,
    upsert_trecho_classe,
)
from rodoviaria.views_schemas import LinkTrechosClassesAsyncParams

TRECHOCLASSE_DATETIME_IDA = today_midnight() + timedelta(days=2)
TAG = TAG_TO_BE_UPDATED


@pytest.fixture
def locais(totalbus_login):
    cidade_goiania = baker.make(
        "rodoviaria.Cidade",
        id_external="21847",
        cidade_internal_id=2,
        company=totalbus_login.company,
        name="Goiania",
    )
    cidade_uberlandia = baker.make(
        "rodoviaria.Cidade",
        id_external="19068",
        cidade_internal_id=2,
        company=totalbus_login.company,
        name="Uberlandia",
    )
    local_origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="21847",
        cidade=cidade_goiania,
        local_embarque_internal_id=1,
        nickname="Goiania",
    )
    local_destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="19068",
        cidade=cidade_uberlandia,
        local_embarque_internal_id=2,
        nickname="Uberlandia",
    )
    return SimpleNamespace(origem=local_origem, destino=local_destino)


@pytest.fixture
def trechos_a_verificar(totalbus_login, locais):
    return LinkTrechosClassesAsyncParams.parse_obj(
        {
            "trechos": [
                {
                    "company_internal_id": totalbus_login.company.company_internal_id,
                    "modelo_venda": totalbus_login.company.modelo_venda,
                    "origem_internal_id": locais.origem.local_embarque_internal_id,
                    "destino_internal_id": locais.destino.local_embarque_internal_id,
                    "datetime_ida": TRECHOCLASSE_DATETIME_IDA.strftime("%Y-%m-%dT%H:%M:%S"),
                    "origem_timezone": "America/Sao_Paulo",
                    "trechoclasse_internal_id": 1,
                    "grupo_internal_id": 1,
                    "grupo_datetime_ida": TRECHOCLASSE_DATETIME_IDA.strftime("%Y-%m-%dT%H:%M:%S"),
                    "grupoclasse_internal_id": 1,
                    "tipo_assento": "leito cama",
                },
                {
                    "company_internal_id": totalbus_login.company.company_internal_id,
                    "modelo_venda": totalbus_login.company.modelo_venda,
                    "origem_internal_id": locais.origem.local_embarque_internal_id,
                    "destino_internal_id": locais.destino.local_embarque_internal_id,
                    "datetime_ida": TRECHOCLASSE_DATETIME_IDA.strftime("%Y-%m-%dT%H:%M:%S"),
                    "origem_timezone": "America/Sao_Paulo",
                    "trechoclasse_internal_id": 2,
                    "grupo_internal_id": 2,
                    "grupo_datetime_ida": TRECHOCLASSE_DATETIME_IDA.strftime("%Y-%m-%dT%H:%M:%S"),
                    "grupoclasse_internal_id": 2,
                    "tipo_assento": "leito cama",
                },
                {
                    "company_internal_id": totalbus_login.company.company_internal_id,
                    "modelo_venda": totalbus_login.company.modelo_venda,
                    "origem_internal_id": locais.origem.local_embarque_internal_id,
                    "destino_internal_id": locais.destino.local_embarque_internal_id,
                    "datetime_ida": TRECHOCLASSE_DATETIME_IDA.strftime("%Y-%m-%dT%H:%M:%S"),
                    "origem_timezone": "America/Sao_Paulo",
                    "trechoclasse_internal_id": 3,
                    "grupo_internal_id": 3,
                    "grupo_datetime_ida": TRECHOCLASSE_DATETIME_IDA.strftime("%Y-%m-%dT%H:%M:%S"),
                    "grupoclasse_internal_id": 3,
                    "tipo_assento": "leito cama",
                },
            ]
        }
    ).trechos


@pytest.fixture
def mock_atualiza_trecho_task():
    with mock.patch("bp.buserdjango_celery.atualiza_trecho_unico.delay") as mock_atualiza_trecho:
        yield mock_atualiza_trecho


@pytest.mark.parametrize("mock_buscar_servico_totalbus", [TRECHOCLASSE_DATETIME_IDA], indirect=True)
def test_link_trechoclasse_async_create(
    totalbus_login, mock_buscar_servico_totalbus, trechos_a_verificar, mock_atualiza_trecho_task
):
    link_trechoclasse_async(trechos_a_verificar)
    tcs_error = TrechoClasseError.objects.filter(trechoclasse_internal_id__in=(1, 2, 3))
    tcs = TrechoClasse.objects.filter(trechoclasse_internal_id__in=(1, 2, 3))

    assert not tcs_error
    assert len(tcs) == 3
    assert mock_atualiza_trecho_task.call_count == 3


def test_link_trechoclasse_async_task_default_queue(totalbus_login, trechos_a_verificar):
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc.buscar_dados_api_default_rate_limit") as mock_calls:
        link_trechoclasse_async(trechos_a_verificar)

    for args in mock_calls.s.return_value.set.call_args_list:
        assert args[1]["queue"] == DEFAULT_QUEUE_NAME


def test_link_trechoclasse_async_task_update_price_queue(totalbus_login, trechos_a_verificar):
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc.buscar_dados_api_default_rate_limit") as mock_calls:
        link_trechoclasse_async(trechos_a_verificar, True)

    for args in mock_calls.s.return_value.set.call_args_list:
        assert args[1]["queue"] == UPDATE_PRICE_QUEUE_NAME


def test_link_trechoclasse_async_task_high_rate_limit(totalbus_login, trechos_a_verificar):
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc.buscar_dados_api_high_rate_limit") as mock_calls:
        link_trechoclasse_async(trechos_a_verificar, use_hot_update_price_queue=True)

    for args in mock_calls.s.return_value.set.call_args_list:
        assert args[1]["queue"] == "bp_link_trechos_classes_atualiza_preco_hot"


def test_link_trechoclasse_async_task_use_update_top_divergencias(totalbus_login, trechos_a_verificar):
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc.buscar_dados_api_default_rate_limit") as mock_calls:
        link_trechoclasse_async(trechos_a_verificar, use_update_top_divergencias=True)

    for args in mock_calls.s.return_value.set.call_args_list:
        assert args[1]["queue"] == "bp_link_trechos_classes_atualiza_top_divergencias"


def test_link_trechoclasse_async_task_default_rate_limit(totalbus_login, trechos_a_verificar):
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc.buscar_dados_api_default_rate_limit") as mock_calls:
        link_trechoclasse_async(trechos_a_verificar)

    for args in mock_calls.s.return_value.set.call_args_list:
        assert args[1]["queue"] == "bp_link_trechos_classes"


def test_link_trechoclasse_async_task_low_rate_limit(totalbus_login, trechos_a_verificar):
    totalbus_login.company.integracao.use_low_rate_limit = True
    totalbus_login.company.integracao.save()
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc.buscar_dados_api_low_rate_limit") as mock_calls:
        link_trechoclasse_async(trechos_a_verificar)

    for args in mock_calls.s.return_value.set.call_args_list:
        assert args[1]["queue"] == DEFAULT_QUEUE_NAME


def test_get_locais_map_apenas_markeptlace(totalbus_company, vexado_company, django_assert_num_queries):
    local_embarque_internal_id = 7832
    company_internal_id = totalbus_company.company_internal_id
    local_marketplace = baker.make(
        LocalEmbarque,
        local_embarque_internal_id=local_embarque_internal_id,
        id_external=3123,
        cidade=baker.make(Cidade, company=totalbus_company),
    )
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=local_embarque_internal_id,
        id_external=53123,
        cidade=baker.make(Cidade, company=vexado_company),
    )

    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        locais = _get_locais_map([local_embarque_internal_id], [totalbus_company.company_internal_id])
    assert locais == {
        (company_internal_id, local_embarque_internal_id): (
            local_marketplace.id,
            str(local_marketplace.id_external),
        )
    }


def test_link_trechoclasse_async_erro_inesperado_buscar_servico(totalbus_login, trechos_a_verificar):
    with mock.patch.object(OrchestrateRodoviaria, "buscar_corridas") as mock_buscar_servico:
        mock_buscar_servico.side_effect = Exception("Erro")
        link_trechoclasse_async(trechos_a_verificar)
    tc_error = TrechoClasseError.objects.filter(trechoclasse_internal_id=1)
    tc = TrechoClasse.objects.filter(trechoclasse_internal_id=1)
    assert len(tc_error) == 0
    assert len(tc) == 0


def test_link_trechoclasse_async_erro_too_many_requests(totalbus_login, trechos_a_verificar):
    with mock.patch.object(OrchestrateRodoviaria, "buscar_corridas") as mock_buscar_servico:
        mock_buscar_servico.side_effect = RodoviariaTooManyRequestsError("Erro")
        with pytest.raises(Retry):
            link_trechoclasse_async(trechos_a_verificar)
        mock_buscar_servico.assert_called_once_with(
            {
                "origem": trechos_a_verificar[0].origem_id_external,
                "destino": trechos_a_verificar[0].destino_id_external,
                "data": TRECHOCLASSE_DATETIME_IDA.strftime("%Y-%m-%d"),
            },
            {
                "datetime_ida": TRECHOCLASSE_DATETIME_IDA,
                "timezone": trechos_a_verificar[0].origem_timezone,
                "tipo_assento": trechos_a_verificar[0].tipo_assento,
            },
        )


def test__generate_tasks_register_loginfo_when_duplicated(trechos_a_verificar, caplog):
    # dada uma lista de trechoclasse para ser atualizado, com duplicacoes
    trecho = trechos_a_verificar[:1]
    with mock.patch(
        "rodoviaria.service.link_trechoclasse_async_svc._deduplicate_tasks", side_effect=LockError
    ), caplog.at_level(logging.INFO):
        # ao adicionar novas tarefas de link_trechoclasse, espero que as duplicações sejam removidas
        _generate_tasks(trecho, "minha_fila_de_teste", buscar_dados_api_default_rate_limit)
    # e que no caso de duplicação, que o log seja salvo.
    assert caplog.records[0].message == "link_trechoclasse_async_lockerror"


@pytest.mark.parametrize("mock_buscar_servico_totalbus", [TRECHOCLASSE_DATETIME_IDA], indirect=True)
def test_link_trechoclasse_async_update_nao_atualiza_recem_atualizados(
    totalbus_login, mock_buscar_servico_totalbus, trechos_a_verificar
):
    external_id = 50505
    tc = baker.make(
        "rodoviaria.TrechoClasse",
        grupo=baker.make("rodoviaria.Grupo"),
        trechoclasse_internal_id=1,
        external_id=external_id,
        datetime_ida=TRECHOCLASSE_DATETIME_IDA,
    )
    link_trechoclasse_async(trechos_a_verificar)
    tc_error = TrechoClasseError.objects.filter(trechoclasse_internal_id=1)
    tc.refresh_from_db()
    assert not tc_error
    assert tc.external_id == str(external_id)
    assert TAG_TO_BE_UPDATED not in tc.tags_set()


@pytest.mark.parametrize("mock_buscar_servico_totalbus", [TRECHOCLASSE_DATETIME_IDA], indirect=True)
def test_link_trechoclasse_async_update(
    totalbus_login, mock_buscar_servico_totalbus, trechos_a_verificar, mock_atualiza_trecho_task
):
    external_id = 50505
    tc = baker.make(
        "rodoviaria.TrechoClasse",
        grupo=baker.make("rodoviaria.Grupo"),
        trechoclasse_internal_id=1,
        external_id=external_id,
        datetime_ida=TRECHOCLASSE_DATETIME_IDA,
    )
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc._is_valid", return_value=True):
        link_trechoclasse_async(trechos_a_verificar[:1])
    tc_error = TrechoClasseError.objects.filter(trechoclasse_internal_id=1)
    tc.refresh_from_db()
    assert not tc_error
    assert tc.external_id != str(external_id)
    mock_atualiza_trecho_task.assert_called_once_with(trecho_classe_id=1, to_close=False, preco="142.41", vagas=6)


def test_link_trechoclasse_async_update_nao_atualiza_se_ja_tiver_tag(
    totalbus_login, trechos_a_verificar, mock_atualiza_trecho_task
):
    tc = baker.make(
        "rodoviaria.TrechoClasse",
        grupo=baker.make("rodoviaria.Grupo"),
        trechoclasse_internal_id=1,
        external_id=999,
        datetime_ida=TRECHOCLASSE_DATETIME_IDA,
    )
    tc.tags.add(TAG_TO_BE_UPDATED)
    with mock.patch("rodoviaria.service.link_trechoclasse_async_svc._is_valid", return_value=True):
        link_trechoclasse_async(trechos_a_verificar[:1])
    tc_error = TrechoClasseError.objects.filter(trechoclasse_internal_id=1)
    tc.refresh_from_db()
    assert not tc_error
    assert tc.external_id == "999"
    assert TAG_TO_BE_UPDATED in tc.tags_set()
    mock_atualiza_trecho_task.assert_not_called()


def test_link_trechoclasse_async_servico_nao_encontrado(
    mocker, totalbus_login, mock_buscar_servico_totalbus, trechos_a_verificar, mock_atualiza_trecho_task
):
    mocker.patch(
        "rodoviaria.service.link_trechoclasse_async_svc.motivo_servico_nao_encontrado",
        return_value=(TrechoClasseError.Motivo.MISMATCH_DE_HORARIO, [["15:30", "semi leito"], ["15:45", "leito"]]),
    )
    link_trechoclasse_async(trechos_a_verificar[:1])
    tc_error = TrechoClasseError.objects.get(trechoclasse_internal_id=1)
    tc = TrechoClasse.objects.filter(trechoclasse_internal_id=1)
    assert tc_error
    assert not tc
    assert tc_error.servicos_proximos_parseados == [
        ["15:30", "semi leito"],
        ["15:45", "leito"],
    ]
    assert tc_error.motivo == TrechoClasseError.Motivo.MISMATCH_DE_HORARIO
    assert (
        tc_error.motivo_fechamento
        == f"[{TrechoClasseError.Motivo.MISMATCH_DE_HORARIO}] {tc_error.servicos_proximos_parseados}"
    )
    mock_atualiza_trecho_task.assert_called_once_with(
        trecho_classe_id=1, to_close=True, motivo_unmatch=tc_error.motivo_fechamento
    )


@pytest.fixture
def tagged_trechos():
    tc = baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=999,
        preco_rodoviaria=12.12,
        vagas=6,
    )
    tc.tags.add(TAG_TO_BE_UPDATED)
    tce = baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=666,
        servicos_proximos_parseados=[("16:00", "semi leito")],
        motivo=TrechoClasseError.Motivo.MISMATCH_DE_HORARIO,
    )
    tce.tags.add(TAG_TO_BE_CLOSED)
    return tc, tce


def test_get_tagged_trechos_classes(tagged_trechos):
    result = get_tagged_trechos_classes()
    assert len(result[TAG_TO_BE_CLOSED]) == 1
    assert result[TAG_TO_BE_CLOSED] == {
        tagged_trechos[
            1
        ].trechoclasse_internal_id: f"[{TrechoClasseError.Motivo.MISMATCH_DE_HORARIO}] [['16:00', 'semi leito']]"
    }
    assert len(result[TAG_TO_BE_UPDATED]) == 1
    assert result[TAG_TO_BE_UPDATED][tagged_trechos[0].trechoclasse_internal_id]["preco"] == Decimal("12.12")
    assert result[TAG_TO_BE_UPDATED][tagged_trechos[0].trechoclasse_internal_id]["vagas"] == 6


def test_remover_tags_trechos_atualizados(tagged_trechos):
    trechos_por_tag = {
        TAG_TO_BE_UPDATED: [tagged_trechos[0].trechoclasse_internal_id],
        TAG_TO_BE_CLOSED: [tagged_trechos[1].trechoclasse_internal_id],
    }
    remover_tags_trechos_atualizados(trechos_por_tag)
    tagged_trechos[0].refresh_from_db()
    tagged_trechos[1].refresh_from_db()
    assert len(tagged_trechos[0].tags_set()) == 0
    assert len(tagged_trechos[1].tags_set()) == 0


def test_add_tag_on_trechos_in_bulk_nao_repete_tag():
    tag_name = "tag_teste"
    trechos = [
        baker.make("rodoviaria.TrechoClasse"),
        baker.make("rodoviaria.TrechoClasse"),
    ]
    trechos[0].tags.add(tag_name)
    trechos[1].tags.add(tag_name)
    add_tag_on_trechos_in_bulk(trechos, tag_name)
    trechos[1].refresh_from_db()
    assert len(trechos[1].tags_set()) == 1


def test_add_tag_on_trechos_error_in_bulk_nao_repete_tag():
    tag_name = "tag_teste"
    trechos = [
        baker.make("rodoviaria.TrechoClasseError"),
        baker.make("rodoviaria.TrechoClasseError"),
    ]
    trechos[0].tags.add(tag_name)
    trechos[1].tags.add(tag_name)
    add_tag_on_trechos_error_in_bulk(trechos, tag_name)
    assert len(trechos[1].tags_set()) == 1


@time_machine.travel(datetime(2022, 7, 12, 14, 32, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_salvar_trecho_classe_error_update(trechos_a_verificar):
    company = baker.make(Company)
    origem, destino = baker.make(LocalEmbarque, _quantity=2)
    trechos_a_verificar[0].rodoviaria_origem_id = origem.id
    trechos_a_verificar[0].rodoviaria_destino_id = destino.id
    datetime_now = timezone.now()
    tc_error = baker.make(
        TrechoClasseError,
        trechoclasse_internal_id=trechos_a_verificar[0].trechoclasse_internal_id,
    )
    TrechoClasseError.objects.filter(id=tc_error.id).update(updated_at=datetime_now - timedelta(days=1))
    tc_error.refresh_from_db()
    assert tc_error.updated_at == timezone.now() - timedelta(days=1)
    salvar_trecho_classe_error(company, trechos_a_verificar[0], [])
    tc_error.refresh_from_db()
    assert tc_error.updated_at == timezone.now()
    assert tc_error.motivo_fechamento == f"[{TrechoClasseError.Motivo.SEM_SERVICO}]"


@time_machine.travel(datetime(2022, 7, 12, 14, 32, 30, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_salvar_trecho_classe_update(trechos_a_verificar):
    company = baker.make(Company, company_internal_id=123)
    origem, destino = baker.make(LocalEmbarque, _quantity=2)
    trechos_a_verificar[0].rodoviaria_origem_id = origem.id
    trechos_a_verificar[0].rodoviaria_destino_id = destino.id
    trechos_a_verificar[0].company_internal_id = company.id
    dados_api = ServicoForm(
        external_id="123", preco=Decimal("123.45"), external_datetime_ida=datetime(2023, 5, 25, 12, 45), vagas=13
    )
    datetime_now = timezone.now()
    tc = baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trechos_a_verificar[0].trechoclasse_internal_id,
    )
    TrechoClasse.objects.filter(id=tc.id).update(updated_at=datetime_now - timedelta(days=1))
    tc.refresh_from_db()
    assert tc.updated_at == timezone.now() - timedelta(days=1)
    upsert_trecho_classe(dados_api, trechos_a_verificar[0])
    tc.refresh_from_db()
    assert tc.updated_at == timezone.now()


time_machine.travel(datetime(2022, 7, 12, 14, 32, 30, tzinfo=ZoneInfo("America/Sao_Paulo")))


def test_salvar_trecho_classe_create(trechos_a_verificar):
    company = baker.make(Company)
    origem, destino = baker.make(LocalEmbarque, _quantity=2)
    trechos_a_verificar[0].rodoviaria_origem_id = origem.id
    trechos_a_verificar[0].rodoviaria_destino_id = destino.id
    trechos_a_verificar[0].company_internal_id = company.id
    dados_api = ServicoForm(
        external_id="123", preco=Decimal("123.45"), external_datetime_ida=datetime(2023, 5, 25, 12, 45), vagas=13
    )
    assert not TrechoClasse.objects.filter(id=trechos_a_verificar[0].trechoclasse_internal_id).exists()
    upsert_trecho_classe(dados_api, trechos_a_verificar[0])
    tc = TrechoClasse.objects.filter(trechoclasse_internal_id=trechos_a_verificar[0].trechoclasse_internal_id).first()
    assert tc.external_datetime_ida == to_tz(datetime(2023, 5, 25, 12, 45), trechos_a_verificar[0].origem_timezone)


@time_machine.travel(datetime(2022, 7, 12, 14, 32, 30, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_salvar_trecho_classe_update_trecho_vendido(trechos_a_verificar):
    trecho = trechos_a_verificar[0]
    company = baker.make(Company, company_internal_id=123)
    origem, destino = baker.make(LocalEmbarque, _quantity=2)
    trecho.rodoviaria_origem_id = origem.id
    trecho.rodoviaria_destino_id = destino.id
    trecho.company_internal_id = company.id
    trecho.datetime_ida = datetime(2022, 7, 12, 14, 32, 30, tzinfo=ZoneInfo("America/Sao_Paulo"))
    dados_api = ServicoForm(
        external_id="123", preco=Decimal("123.45"), external_datetime_ida=datetime(2023, 5, 25, 12, 45), vagas=13
    )

    tv = baker.make(
        TrechoVendido,
        origem=origem,
        destino=destino,
        classe=dados_api.classe,
        status_preco=TrechoVendido.StatusPreco.OK,
        preco=0,
        rota__company=company,
    )

    upsert_trecho_classe(dados_api, trechos_a_verificar[0])

    tv.refresh_from_db()
    assert tv.preco == dados_api.preco
    assert tv.datetimeida_preco == trecho.datetime_ida

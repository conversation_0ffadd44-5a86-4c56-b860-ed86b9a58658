import copy
from datetime import date, datetime
from decimal import Decimal
from http import <PERSON><PERSON>PMethod, HTTPStatus

import pytest
from pydantic import parse_obj_as

from rodoviaria.api.eulabs import endpoints as eulabs_endpoints
from rodoviaria.api.eulabs import models as eulabs_models
from rodoviaria.api.eulabs.api import _get_flat_list_poltronas, order_itinerario_by_datetime
from rodoviaria.api.eulabs.exceptions import (
    EulabsNaoPodeCancelar,
    EulabsPoltronaIndisponivel,
)
from rodoviaria.api.executors.impl import get_http_executor
from rodoviaria.service.exceptions import RodoviariaConnectionError
from rodoviaria.tests.eulabs.mock_data_response import (
    mock_cancel_conditions,
    mock_cancel_sale,
    mock_error,
    mock_findsale,
    mock_summary,
    mock_summary_list,
    mock_travels,
    mock_travels_detail,
)


def test_repr(eulabs_api):
    assert (
        str(eulabs_endpoints.BuscarOrigensConfig(eulabs_api.login))
        == f"EULABS_BuscarOrigensConfig_{eulabs_api.company.id}_{eulabs_api.company.modelo_venda}"
    )


def test_buscar_origens(eulabs_api, mock_buscar_origens):
    executor = get_http_executor()
    request_config = eulabs_endpoints.BuscarOrigensConfig(eulabs_api.login)
    response = request_config.invoke(
        executor,
        params={"is_road_station": True},
    )
    response_json = response.json()
    parsed_response = parse_obj_as(list[eulabs_models.OrigemResponseForm], response_json)

    assert parsed_response[1].external_cidade_id == "31"
    assert parsed_response[1].external_local_id == "31"
    assert parsed_response[1].nome_cidade == "ALTA FLORESTA D'OESTE"
    assert parsed_response[1].complemento is None
    assert parsed_response[1].uf == "RO"


def test_buscar_corridas(eulabs_api, mock_buscar_corridas_duas_corridas_empresas_diferentes):
    executor = get_http_executor()
    request_params = {"origem": 1, "destino": 2, "data": "2022-05-31"}
    params = eulabs_models.BuscarCorridasRequestForm.parse_obj(request_params).dict()
    request_config = eulabs_endpoints.BuscarCorridasConfig(eulabs_api.login)
    response = request_config.invoke(executor, params=params).json()
    assert response == mock_travels.duas_corridas_empresas_diferentes
    parsed_response = parse_obj_as(eulabs_models.CorridasForm, response)
    assert isinstance(parsed_response, eulabs_models.CorridasForm)
    assert len(parsed_response) == 2
    item = mock_travels.duas_corridas_empresas_diferentes[0]["items"][0]
    classe = item["tariffs"][0]
    assert parsed_response[0].external_id == f'{item["id"]}/{classe["category"]["vehicle_type_id"]}'
    assert parsed_response[0].rota_external_id == mock_travels.duas_corridas_empresas_diferentes[0]["id"]
    parsed_response.filter(
        rota_external_id=mock_travels.duas_corridas_empresas_diferentes[0]["id"],
        company_external_id=mock_travels.duas_corridas_empresas_diferentes[1]["items"][0]["company"]["id"],
    )
    assert len(parsed_response) == 1


def test_buscar_corridas_nova_empresa(eulabs_api, mock_buscar_corridas_duas_corridas_empresas_diferentes):
    executor = get_http_executor()
    request_params = {"origem": 1, "destino": 2, "data": "2022-05-31"}
    params = eulabs_models.BuscarCorridasRequestForm.parse_obj(request_params).dict()
    request_config = eulabs_endpoints.BuscarCorridasConfig(eulabs_api.login)
    response = request_config.invoke(executor, params=params).json()
    assert response == mock_travels.duas_corridas_empresas_diferentes
    parsed_response = parse_obj_as(eulabs_models.CorridasForm, response)
    assert isinstance(parsed_response, eulabs_models.CorridasForm)
    parsed_response.filter(company_external_id=88126)
    assert len(parsed_response) == 2
    assert parsed_response[0].company_external_id == 88126
    assert parsed_response[1].company_external_id == 118


def test_buscar_corridas_sem_preco(eulabs_api, mock_buscar_corridas_sem_preco):
    executor = get_http_executor()
    request_params = {"origem": 1, "destino": 2, "data": "2022-05-31"}
    params = eulabs_models.BuscarCorridasRequestForm.parse_obj(request_params).dict()
    request_config = eulabs_endpoints.BuscarCorridasConfig(eulabs_api.login)
    response = request_config.invoke(executor, params=params).json()
    assert response == mock_travels.corrida_sem_preco
    parsed_response = parse_obj_as(eulabs_models.CorridasForm, response)
    assert isinstance(parsed_response, eulabs_models.CorridasForm)
    assert len(parsed_response) == 0


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": "key"}], indirect=True)
def test_retorna_poltronas(eulabs_api, mock_retorna_poltronas):
    executor = get_http_executor()
    request_config = eulabs_endpoints.RetornaPoltronasConfig(eulabs_api.login, travel_key="key")
    response = request_config.invoke(executor)
    response_json = response.json()
    poltronas = _get_flat_list_poltronas(response_json)
    parsed_poltronas = parse_obj_as(eulabs_models.PoltronasForm, poltronas)
    assert len(parsed_poltronas) == 60
    parsed_poltronas.filter()
    assert len(parsed_poltronas) == 58
    parsed_poltronas.filter(classe="SEMI")
    assert len(parsed_poltronas) == 46
    parsed_poltronas.filter(preco_lte=65)
    assert len(parsed_poltronas) == 42
    parsed_poltronas.filter(ocupada=False)
    assert len(parsed_poltronas) == 10
    assert parsed_poltronas.select(quantidade=2) == [24, 25]
    assert isinstance(parsed_poltronas[0], eulabs_models.PoltronaForm)
    map_poltronas = parsed_poltronas.map()
    for p in parsed_poltronas:
        assert not p.ocupada
        assert map_poltronas[str(p.numero).zfill(2)] == "livre"


@pytest.mark.parametrize("mock_retorna_poltronas", [{"travel_key": "key"}], indirect=True)
def test_retorna_poltronas_ordena_por_proximidade_de_preco(eulabs_api, mock_retorna_poltronas):
    executor = get_http_executor()
    request_config = eulabs_endpoints.RetornaPoltronasConfig(eulabs_api.login, travel_key="key")
    response = request_config.invoke(executor)
    response_json = response.json()
    poltronas = _get_flat_list_poltronas(response_json)
    parsed_poltronas = parse_obj_as(eulabs_models.PoltronasForm, poltronas)
    parsed_poltronas.filter(classe="LEITO", preco_base_ordenacao=Decimal("252.71"), ocupada=False)
    assert [p.preco for p in parsed_poltronas] == [Decimal("252.71"), Decimal("242.71"), Decimal("292.71")]


@pytest.mark.parametrize("mock_bloquear_poltrona", [{"travel_key": "key"}], indirect=True)
def test_bloquear_poltrona(eulabs_api, mock_bloquear_poltrona):
    executor = get_http_executor()
    json = eulabs_models.BloquearPoltronaRequestForm(seat_id=21, tipo="deficient").request_body
    assert json == {"items": [{"seat": 21, "type": "deficient", "required_companion": False}]}
    request_config = eulabs_endpoints.BloquearPoltronaConfig(eulabs_api.login, travel_key="key")
    response = request_config.invoke(executor, json=json)
    response_json = response.json()
    seat_key = eulabs_models.BloquearPoltrona(**response_json).selected_seat_key
    assert response_json == {"selected_seat_Key": "seat_key"}
    assert seat_key == "seat_key"


@pytest.mark.parametrize("mock_bloquear_poltrona_error", [{"travel_key": "key"}], indirect=True)
def test_bloquear_poltrona_ja_bloqueada(eulabs_api, mock_bloquear_poltrona_error):
    with pytest.raises(EulabsPoltronaIndisponivel):
        executor = get_http_executor()
        json = eulabs_models.BloquearPoltronaRequestForm(seat_id=21).request_body
        request_config = eulabs_endpoints.BloquearPoltronaConfig(eulabs_api.login, travel_key="key")
        request_config.invoke(executor, json=json)


@pytest.mark.parametrize(
    "mock_desbloquear_poltrona", [{"travel_key": "key", "selected_seat_keys": ["keyseat"]}], indirect=True
)
def test_desbloquear_poltrona(eulabs_api, mock_desbloquear_poltrona):
    executor = get_http_executor()
    request_config = eulabs_endpoints.DesbloquearPoltronaConfig(
        eulabs_api.login, travel_key="key", selected_seat_key="keyseat"
    )
    response = request_config.invoke(executor)
    assert response.status_code == 204
    assert response.json() is None


@pytest.mark.parametrize(
    "mock_desbloquear_poltrona", [{"travel_key": "key", "selected_seat_keys": ["keyseat"]}], indirect=True
)
def test_desbloquear_poltrona_config(eulabs_api, mock_desbloquear_poltrona):
    executor = get_http_executor()
    request_config = eulabs_endpoints.DesbloquearPoltronaConfig(
        eulabs_api.login, travel_key="key", selected_seat_key="keyseat"
    )
    response = request_config.invoke(executor)
    assert response.status_code == 204
    assert response.json() is None


@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [24, 25]}], indirect=True)
def test_efetuar_reserva(eulabs_api, mock_efetuar_reserva):
    executor = get_http_executor()
    request_config = eulabs_endpoints.EfetuarReservaConfig(eulabs_api.login)
    response = request_config.invoke(
        executor,
        params={"device_type": "desktop"},
        json={"items": {}},
    )
    response_json = response.json()
    response_parsed = parse_obj_as(eulabs_models.CompraForm, response_json)
    assert len(response_parsed.passagens) == 2
    assert response_parsed.passagens[0].poltrona == 24
    assert response_parsed.passagens[1].poltrona == 25


@pytest.mark.parametrize("mock_efetuar_reserva", [{"poltronas": [24, 25]}], indirect=True)
def test_efetuar_reserva_config(eulabs_api, mock_efetuar_reserva):
    executor = get_http_executor()
    request_config = eulabs_endpoints.EfetuarReservaConfig(eulabs_api.login)
    response = request_config.invoke(
        executor,
        params={"device_type": "desktop"},
        json={},
    )
    response_json = response.json()
    comprar_response = parse_obj_as(eulabs_models.CompraForm, response_json)
    assert response.json() == mock_efetuar_reserva
    assert len(comprar_response.passagens) == 2
    assert comprar_response.passagens[0].poltrona == 24
    assert comprar_response.passagens[1].poltrona == 25


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [34, 42]}], indirect=True)
def test_consulta_reserva(eulabs_api, mock_consultar_reserva):
    response = eulabs_api._consultar_reserva_request(pedido_external_id=123)
    expected_raw = copy.deepcopy(mock_findsale.sale)
    expected_raw["items"][0]["road"]["seat_number"] = 34
    expected_raw["items"][0]["key"] = "VIB-17416588"
    expected_raw["items"][1]["road"]["seat_number"] = 42
    expected_raw["items"][1]["key"] = "VIB-17416589"
    assert response == parse_obj_as(list[eulabs_models.ReservaForm], expected_raw["items"])


@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [34, 42]}], indirect=True)
def test_consulta_reserva_config(eulabs_api, mock_consultar_reserva):
    executor = get_http_executor()
    request_config = eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, sale_id=123)
    response = request_config.invoke(executor)
    response_json = response.json()

    expected_raw = copy.deepcopy(mock_findsale.sale)
    expected_raw["items"][0]["road"]["seat_number"] = 34
    expected_raw["items"][0]["key"] = "VIB-17416588"
    expected_raw["items"][1]["road"]["seat_number"] = 42
    expected_raw["items"][1]["key"] = "VIB-17416589"
    parsed = parse_obj_as(eulabs_models.ReservasForm, response_json["items"])
    assert response.json() == expected_raw
    assert response.status_code == 200
    assert len(parsed) == 2


def test_consulta_reserva_retry(eulabs_api, requests_mock):
    expected_raw = copy.deepcopy(mock_findsale.sale)
    expected_raw["items"][0]["road"]["seat_number"] = 34
    expected_raw["items"][1]["road"]["seat_number"] = 42
    pedido_external_id = 102030
    requests_mock.add(
        HTTPMethod.GET,
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, sale_id=pedido_external_id).url,
        json={"message": "venda não encontrada"},
        status=400,
    )
    requests_mock.add(
        HTTPMethod.GET,
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, sale_id=pedido_external_id).url,
        json={"message": "venda não encontrada"},
        status=400,
    )
    requests_mock.add(
        HTTPMethod.GET,
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, sale_id=pedido_external_id).url,
        json=expected_raw,
    )
    response = eulabs_api._consultar_reserva_request(pedido_external_id=pedido_external_id)
    assert requests_mock.assert_call_count(
        eulabs_endpoints.ConsultaReservaConfig(eulabs_api.login, sale_id=pedido_external_id).url, 3
    )
    assert response == parse_obj_as(list[eulabs_models.ReservaForm], expected_raw["items"])


@pytest.mark.parametrize("mock_condicoes_cancelamento_cancelar", [{"item_keys": ["item_key"]}], indirect=True)
def test_condicoes_cancelamento(eulabs_api, mock_condicoes_cancelamento_cancelar):
    executor = get_http_executor()
    request_config = eulabs_endpoints.CondicoesCancelamentoConfig(eulabs_api.login, "item_key")
    response = request_config.invoke(
        executor,
    )
    response_json = response.json()
    assert response_json == mock_cancel_conditions.cancelar


@pytest.mark.parametrize("mock_condicoes_cancelamento_nao_autorizado", [{"item_keys": ["item_key"]}], indirect=True)
def test_condicoes_cancelamento_nao_autorizado(eulabs_api, mock_condicoes_cancelamento_nao_autorizado):
    executor = get_http_executor()
    request_config = eulabs_endpoints.CondicoesCancelamentoConfig(eulabs_api.login, "item_key")
    response = request_config.invoke(
        executor,
    )
    response_json = response.json()
    assert response_json == mock_cancel_conditions.nao_autorizado


@pytest.mark.parametrize("mock_condicoes_cancelamento_erro_nao_permitido", [{"item_keys": ["item_key"]}], indirect=True)
def test_condicoes_cancelamento_erro_nao_permitido(eulabs_api, mock_condicoes_cancelamento_erro_nao_permitido):
    with pytest.raises(EulabsNaoPodeCancelar):
        executor = get_http_executor()
        request_config = eulabs_endpoints.CondicoesCancelamentoConfig(eulabs_api.login, "item_key")
        request_config.invoke(
            executor,
        )


@pytest.mark.parametrize("mock_condicoes_cancelamento_connection_error", [{"item_keys": ["item_key"]}], indirect=True)
def test_condicoes_cancelamento_connection_error(eulabs_api, mock_condicoes_cancelamento_connection_error):
    with pytest.raises(EulabsNaoPodeCancelar, match="connection error"):
        executor = get_http_executor()
        request_config = eulabs_endpoints.CondicoesCancelamentoConfig(eulabs_api.login, "item_key")
        request_config.invoke(
            executor,
        )


@pytest.mark.parametrize("mock_cancelar_venda", [{"cancel_key": "cancel_key", "cancel_type": "Cancel"}], indirect=True)
def test_cancela_venda(eulabs_api, mock_cancelar_venda):
    executor = get_http_executor()
    request_config = eulabs_endpoints.CancelaVendaConfig(eulabs_api.login, "cancel_key", "Cancel")
    response = request_config.invoke(
        executor,
    )
    assert response.json() == mock_cancel_sale.sucesso


@pytest.mark.parametrize("mock_buscar_itinerario", [{"item_id": "123456"}], indirect=True)
def test_buscar_itinerario(eulabs_api, mock_buscar_itinerario):
    executor = get_http_executor()
    request_config = eulabs_endpoints.BuscarItinerarioViagemConfig(
        eulabs_api.login, item_id="123456", rota_external_id=99999
    )
    response = request_config.invoke(executor)
    response_raw = response.json()
    response_cleaned = order_itinerario_by_datetime(response_raw)
    response_parsed = parse_obj_as(eulabs_models.Itinerario, response_cleaned)
    assert response_raw == mock_summary.itinerario
    assert response_parsed[0].duracao == 0
    for checkpoint in response_parsed[1:]:
        assert checkpoint.duracao > 0
    assert len(response_parsed) == 5
    assert response_parsed.hash == "3541579146deb2da094619d8ff1d36f6a3"


def test_buscar_itinerario_erro_conexao(eulabs_api, mock_buscar_itinerario_erro_conexao):
    executor = get_http_executor()
    request_config = eulabs_endpoints.BuscarItinerarioViagemConfig(
        eulabs_api.login, item_id="123456", rota_external_id=99999
    )
    with pytest.raises(RodoviariaConnectionError):
        request_config.invoke(executor)


def test_buscar_trechos_vendidos(eulabs_api, mock_buscar_trechos_vendidos):
    executor = get_http_executor()
    request_config = eulabs_endpoints.BuscarTrechosVendidosViagemConfig(eulabs_api.login, item_id="123456")
    response = request_config.invoke(executor)
    response_raw = response.json()
    response_parsed = parse_obj_as(list[eulabs_models.TrechoVendidoModel], response_raw)
    assert response_raw == mock_travels_detail.trechos_vendidos
    assert len(response_parsed) == 7


def test_buscar_viagens_por_periodo_none(eulabs_api, mock_viagens_por_periodo_none):
    response_parsed = eulabs_api._buscar_viagens_por_periodo(
        data_inicio=datetime(2022, 5, 10), data_fim=datetime(2022, 5, 24)
    )

    assert response_parsed is None


def test_buscar_viagens_por_periodo(eulabs_api, mock_viagens_por_periodo):
    response_parsed = eulabs_api._buscar_viagens_por_periodo(
        data_inicio=datetime(2022, 5, 10), data_fim=datetime(2022, 5, 24)
    )

    assert response_parsed == parse_obj_as(eulabs_models.Viagens, mock_summary_list.viagens)
    assert isinstance(response_parsed, eulabs_models.Viagens)
    expected_datetime_ida = (
        f"{mock_summary_list.viagens[0]['departure_date']} {mock_summary_list.viagens[0]['departure_time']}"
    )
    assert response_parsed[0].datetime_ida.strftime("%Y-%m-%d %H:%M:%S") == expected_datetime_ida
    response_parsed[0].company_external_id = 53213
    response_parsed.filter(company_external_id=1)
    assert len(response_parsed) == 1
    response_parsed.filter(rotas_external_ids=[83952])
    assert len(response_parsed) == 0


def test_buscar_viagens_por_periodo_empresa_nova(eulabs_api, mock_viagens_por_periodo):
    response_parsed = eulabs_api._buscar_viagens_por_periodo(
        data_inicio=datetime(2022, 5, 10), data_fim=datetime(2022, 5, 24)
    )

    assert response_parsed == parse_obj_as(eulabs_models.Viagens, mock_summary_list.viagens)
    response_parsed.filter(company_external_id=88126)
    assert len(response_parsed) == 2
    assert response_parsed[0].company_external_id == 88126
    assert response_parsed[1].company_external_id == 118


def test_make_viagens_por_periodo_request(eulabs_api, requests_mock):
    request_config = eulabs_endpoints.BuscarViagensPorPeriodoConfig(eulabs_api.login)
    requests_mock.add(
        request_config.method,
        request_config.url,
        json=mock_error.error_reading_from_server,
        status=HTTPStatus.BAD_REQUEST,
    )
    requests_mock.add(
        request_config.method,
        request_config.url,
        json=mock_error.error_reading_from_server,
        status=HTTPStatus.BAD_GATEWAY,
    )
    requests_mock.add(
        request_config.method,
        request_config.url,
        json=mock_summary_list.viagens,
        status=HTTPStatus.OK,
    )
    viagens_por_periodo = eulabs_endpoints.buscar_viagens_por_periodo_request(
        eulabs_api.login, date(2024, 11, 1), date(2024, 11, 30)
    )
    # espera-se usar a politica de retry para obter a resposta saudável na segunda tentativa
    assert viagens_por_periodo == parse_obj_as(eulabs_models.Viagens, mock_summary_list.viagens)

locais = [
    {"id": 428, "code": "0778", "description": "ALTA FLORESTA - RODOVIARIA", "uf_acronym": "MT"},
    {"id": 31, "code": "0416", "description": "ALTA FLORESTA D'OESTE", "uf_acronym": "RO"},
    {"id": 20, "code": "0240", "description": "ALVORADA DO OESTE", "uf_acronym": "RO"},
    {"id": 226, "code": "0799", "description": "AMAMBAI", "uf_acronym": "MS"},
    {"id": 701, "code": "1358", "description": "AMERICANA", "uf_acronym": "SP"},
    {"id": 1828, "code": "0501", "description": "APARECIDA", "uf_acronym": "SP"},
    {"id": 73, "code": "0033", "description": "ARARANGUÁ", "uf_acronym": "SC"},
    {"id": 80, "code": "0155", "description": "ARAÇATUBA", "uf_acronym": "SC"},
    {"id": 370, "code": "1560", "description": "ARIPUANÃ", "uf_acronym": "MT"},
    {"id": 10, "code": "0233", "description": "ARIQUEMES", "uf_acronym": "RO"},
    {"id": 84, "code": "0242", "description": "BALNEÁRIO CAMBORIÚ", "uf_acronym": "SC"},
    {"id": 893, "code": "4129", "description": "BARRA VELHA - RODOVIÁRIA", "uf_acronym": "SC"},
    {"id": 250, "code": "0030", "description": "BARRACÃO", "uf_acronym": "PR"},
    {"id": 360, "code": "0061", "description": "BELO HORIZONTE", "uf_acronym": "MG"},
    {"id": 645, "code": "0042", "description": "BLUMENAU", "uf_acronym": "SC"},
    {"id": 46, "code": "0795", "description": "BOA VISTA", "uf_acronym": "RR"},
    {"id": 353, "code": "0613", "description": "BRASÍLIA", "uf_acronym": "DF"},
    {"id": 27, "code": "0822", "description": "CACOAL", "uf_acronym": "RO"},
    {"id": 1351, "code": "0438", "description": "CAMPINAS", "uf_acronym": "SP"},
    {"id": 68, "code": "0771", "description": "CAMPO GRANDE", "uf_acronym": "MS"},
    {"id": 355, "code": "0213", "description": "CAMPO MOURÃO", "uf_acronym": "PR"},
    {"id": 1083, "code": "6884", "description": "CAMPO NOVO DO PARECIS", "uf_acronym": "MT"},
    {"id": 729, "code": "4214", "description": "CAMPO VERDE", "uf_acronym": "MT"},
    {"id": 254, "code": "4083", "description": "CAPANEMA", "uf_acronym": "PR"},
    {"id": 256, "code": "0032", "description": "CAPITÃO L. MARQUES", "uf_acronym": "PR"},
    {"id": 50, "code": "0793", "description": "CARACARAÍ", "uf_acronym": "RR"},
    {"id": 240, "code": "0085", "description": "CARAZINHO", "uf_acronym": "RS"},
    {"id": 218, "code": "0208", "description": "CASCAVEL", "uf_acronym": "PR"},
    {"id": 37, "code": "1166", "description": "CEREJEIRAS", "uf_acronym": "RO"},
    {"id": 262, "code": "0413", "description": "CHAPECÓ", "uf_acronym": "SC"},
    {"id": 358, "code": "0400", "description": "COLATINA", "uf_acronym": "ES"},
    {"id": 35, "code": "0172", "description": "COLORADO DO OESTE", "uf_acronym": "RO"},
    {"id": 508, "code": "0811", "description": "COLÍDER", "uf_acronym": "MT"},
    {"id": 215, "code": "0474", "description": "COMODORO", "uf_acronym": "MT"},
    {"id": 260, "code": "0414", "description": "CONCÓRDIA", "uf_acronym": "SC"},
    {"id": 26, "code": "0354", "description": "COSTA MARQUES", "uf_acronym": "RO"},
    {"id": 112, "code": "0767", "description": "COXIM", "uf_acronym": "MS"},
    {"id": 74, "code": "1000", "description": "CRICIÚMA", "uf_acronym": "SC"},
    {"id": 118, "code": "0346", "description": "CUIABÁ", "uf_acronym": "MT"},
    {"id": 90, "code": "0209", "description": "CURITIBA", "uf_acronym": "PR"},
    {"id": 62, "code": "0349", "description": "CÁCERES", "uf_acronym": "MT"},
    {"id": 229, "code": "0726", "description": "DOURADOS", "uf_acronym": "MS"},
    {"id": 231, "code": "0150", "description": "ERECHIM", "uf_acronym": "RS"},
    {"id": 33, "code": "5037", "description": "ESPIGÃO DO OESTE", "uf_acronym": "RO"},
    {"id": 236, "code": "0884", "description": "ESTRELA", "uf_acronym": "RS"},
    {"id": 1213, "code": "1212", "description": "EXTREMA", "uf_acronym": "RO"},
    {"id": 82, "code": "0241", "description": "FLORIANÓPOLIS", "uf_acronym": "SC"},
    {"id": 530, "code": "1160", "description": "FOZ DO IGUAÇU", "uf_acronym": "PR"},
    {"id": 273, "code": "0408", "description": "FRANCISCO BELTRÃO", "uf_acronym": "PR"},
    {"id": 242, "code": "0075", "description": "FREDERICO WESTPHALEN", "uf_acronym": "RS"},
    {"id": 350, "code": "0190", "description": "GOIÂNIA", "uf_acronym": "GO"},
    {"id": 222, "code": "0314", "description": "GUAÍRA", "uf_acronym": "PR"},
    {"id": 309, "code": "0807", "description": "HUMAITÁ", "uf_acronym": "AM"},
    {"id": 12, "code": "0503", "description": "IBATIBA", "uf_acronym": "ES"},
    {"id": 79, "code": "0168", "description": "IMBITUBA", "uf_acronym": "SC"},
    {"id": 60, "code": "0460", "description": "ITACOATIARA", "uf_acronym": "AM"},
    {"id": 86, "code": "0243", "description": "ITAJAÍ", "uf_acronym": "SC"},
    {"id": 9, "code": "0402", "description": "ITAPUÃ DO OESTE", "uf_acronym": "RO"},
    {"id": 5455, "code": "0594", "description": "ITUMBIARA", "uf_acronym": "GO"},
    {"id": 116, "code": "0461", "description": "JACIARA", "uf_acronym": "MT"},
    {"id": 14, "code": "0824", "description": "JARÚ", "uf_acronym": "RO"},
    {"id": 342, "code": "0605", "description": "JATAÍ", "uf_acronym": "GO"},
    {"id": 3, "code": "0962", "description": "JI-PARANÁ", "uf_acronym": "RO"},
    {"id": 643, "code": "0041", "description": "JOAÇABA", "uf_acronym": "SC"},
    {"id": 88, "code": "0244", "description": "JOINVILLE", "uf_acronym": "SC"},
    {"id": 78, "code": "0165", "description": "LAGUNA", "uf_acronym": "SC"},
    {"id": 237, "code": "0885", "description": "LAJEADO", "uf_acronym": "RS"},
    {"id": 100, "code": "0210", "description": "LONDRINA", "uf_acronym": "PR"},
    {"id": 494, "code": "0813", "description": "LUCAS DO RIO VERDE", "uf_acronym": "MT"},
    {"id": 7, "code": "0310", "description": "MACHADINHO DO OESTE", "uf_acronym": "RO"},
    {"id": 58, "code": "0788", "description": "MANAUS", "uf_acronym": "AM"},
    {"id": 245, "code": "0055", "description": "MARAVILHA", "uf_acronym": "SC"},
    {"id": 221, "code": "4041", "description": "MARECHAL C RONDON", "uf_acronym": "PR"},
    {"id": 357, "code": "0420", "description": "MARINGÁ", "uf_acronym": "PR"},
    {"id": 533, "code": "6155", "description": "MEDIANEIRA", "uf_acronym": "PR"},
    {"id": 17, "code": "0019", "description": "MIRANTE DA SERRA", "uf_acronym": "RO"},
    {"id": 48, "code": "0514", "description": "MUCAJAÍ", "uf_acronym": "RR"},
    {"id": 223, "code": "0773", "description": "MUNDO NOVO", "uf_acronym": "MS"},
    {"id": 228, "code": "0770", "description": "NAVIRAÍ", "uf_acronym": "MS"},
    {"id": 540, "code": "0808", "description": "NOBRES", "uf_acronym": "MT"},
    {"id": 235, "code": "0783", "description": "NOVA ALVORADA DO SUL", "uf_acronym": "MS"},
    {"id": 28, "code": "0475", "description": "NOVA BRASILÂNDIA", "uf_acronym": "RO"},
    {"id": 492, "code": "0814", "description": "NOVA MUTUM", "uf_acronym": "MT"},
    {"id": 505, "code": "0928", "description": "NOVA SANTA HELENA", "uf_acronym": "MT"},
    {"id": 359, "code": "2525", "description": "NOVA SERRANA", "uf_acronym": "MG"},
    {"id": 43, "code": "0063", "description": "NOVO HORIZONTE", "uf_acronym": "RO"},
    {"id": 1732, "code": "2072", "description": "OSASCO", "uf_acronym": "SP"},
    {"id": 16, "code": "5549", "description": "OURO PRETO", "uf_acronym": "RO"},
    {"id": 15, "code": "0235", "description": "OURO PRETO RODOVIÁRIA", "uf_acronym": "RO"},
    {"id": 47, "code": "0520", "description": "PACARAIMA", "uf_acronym": "RR"},
    {"id": 220, "code": "0216", "description": "PALOTINA", "uf_acronym": "PR"},
    {"id": 32, "code": "0821", "description": "PIMENTA BUENO", "uf_acronym": "RO"},
    {"id": 92, "code": "0211", "description": "PONTA GROSSA", "uf_acronym": "PR"},
    {"id": 227, "code": "0728", "description": "PONTA PORÃ", "uf_acronym": "MS"},
    {"id": 121, "code": "0348", "description": "PONTES E LACERDA", "uf_acronym": "MT"},
    {"id": 70, "code": "1050", "description": "PORTO ALEGRE", "uf_acronym": "RS"},
    {"id": 5, "code": "0234", "description": "PORTO VELHO", "uf_acronym": "RO"},
    {"id": 6, "code": "0823", "description": "PRESIDENTE MÉDICI ROD", "uf_acronym": "RO"},
    {"id": 103, "code": "0991", "description": "PRESIDENTE PRUDENTE", "uf_acronym": "SP"},
    {"id": 727, "code": "0542", "description": "PRIMAVERA DO LESTE", "uf_acronym": "MT"},
    {"id": 255, "code": "0031", "description": "REALEZA", "uf_acronym": "PR"},
    {"id": 4311, "code": "9818", "description": "REALIDADE", "uf_acronym": "AM"},
    {"id": 451, "code": "0905", "description": "RIBEIRÃO PRETO", "uf_acronym": "SP"},
    {"id": 63, "code": "0672", "description": "RIO BRANCO", "uf_acronym": "AC"},
    {"id": 638, "code": "0039", "description": "RIO DO SUL", "uf_acronym": "SC"},
    {"id": 346, "code": "0781", "description": "RIO VERDE", "uf_acronym": "GO"},
    {"id": 29, "code": "5034", "description": "ROLIM DE MOURA", "uf_acronym": "RO"},
    {"id": 114, "code": "0350", "description": "RONDONÓPOLIS", "uf_acronym": "MT"},
    {"id": 56, "code": "0549", "description": "RORAINÓPOLIS", "uf_acronym": "RR"},
    {"id": 626, "code": "1138", "description": "SANTA ELENA DE UAIREM (VE)", "uf_acronym": "EX"},
    {"id": 30, "code": "0003", "description": "SANTA LUZIA", "uf_acronym": "RO"},
    {"id": 338, "code": "0607", "description": "SANTA RITA DO ARAGUAIA", "uf_acronym": "GO"},
    {"id": 216, "code": "0036", "description": "SANTA ROSA DO SUL", "uf_acronym": "SC"},
    {"id": 251, "code": "0555", "description": "SANTO ANTÔNIO SUDOESTE", "uf_acronym": "PR"},
    {"id": 261, "code": "0560", "description": "SEARA", "uf_acronym": "SC"},
    {"id": 25, "code": "0131", "description": "SERINGUEIRAS", "uf_acronym": "RO"},
    {"id": 500, "code": "0816", "description": "SINOP", "uf_acronym": "MT"},
    {"id": 239, "code": "0886", "description": "SOLEDADE", "uf_acronym": "RS"},
    {"id": 72, "code": "0034", "description": "SOMBRIO", "uf_acronym": "SC"},
    {"id": 113, "code": "0780", "description": "SONORA", "uf_acronym": "MS"},
    {"id": 497, "code": "0810", "description": "SORRISO", "uf_acronym": "MT"},
    {"id": 24, "code": "1140", "description": "SÃO FRANCISCO", "uf_acronym": "RO"},
    {"id": 107, "code": "0772", "description": "SÃO GABRIEL DO OESTE", "uf_acronym": "MS"},
    {"id": 247, "code": "0045", "description": "SÃO JOSÉ DO CEDRO", "uf_acronym": "SC"},
    {"id": 23, "code": "0403", "description": "SÃO MIGUEL DO GUAPORÉ", "uf_acronym": "RO"},
    {"id": 246, "code": "0100", "description": "SÃO MIGUEL DO OESTE", "uf_acronym": "SC"},
    {"id": 446, "code": "0500", "description": "SÃO PAULO - BARRA FUNDA", "uf_acronym": "SP"},
    {"id": 1020, "code": "6451", "description": "SÃO PAULO - TIETÊ", "uf_acronym": "SP"},
    {"id": 232, "code": "0214", "description": "TERRA ROXA", "uf_acronym": "PR"},
    {"id": 42, "code": "0304", "description": "THEOBROMA", "uf_acronym": "RO"},
    {"id": 5053, "code": "1059", "description": "TIJUCAS", "uf_acronym": "SC"},
    {"id": 219, "code": "0218", "description": "TOLEDO", "uf_acronym": "PR"},
    {"id": 217, "code": "0037", "description": "TORRES - (VILA SAO JOAO)", "uf_acronym": "RS"},
    {"id": 76, "code": "0170", "description": "TUBARÃO", "uf_acronym": "SC"},
    {"id": 452, "code": "1041", "description": "UBERABA", "uf_acronym": "MG"},
    {"id": 453, "code": "0571", "description": "UBERLÂNDIA", "uf_acronym": "MG"},
    {"id": 354, "code": "0980", "description": "UBIRATÃ", "uf_acronym": "PR"},
    {"id": 18, "code": "0069", "description": "URUPÁ", "uf_acronym": "RO"},
    {"id": 39, "code": "1016", "description": "VALE DO ANARI", "uf_acronym": "RO"},
    {"id": 49, "code": "0575", "description": "VILA IRACEMA", "uf_acronym": "RR"},
    {"id": 34, "code": "0820", "description": "VILHENA", "uf_acronym": "RO"},
    {"id": 363, "code": "0502", "description": "VITÓRIA", "uf_acronym": "ES"},
    {"id": 1080, "code": "1445", "description": "VÁRZEA GRANDE", "uf_acronym": "MT"},
    {"id": 263, "code": "0417", "description": "XANXERÊ", "uf_acronym": "SC"},
]

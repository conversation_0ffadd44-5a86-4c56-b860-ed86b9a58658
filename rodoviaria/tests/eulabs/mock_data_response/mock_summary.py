itinerario = [
    {
        "seccional_id": 15,
        "seccional_code": "0235",
        "seccional_name": "OURO PRETO RODOVIÁRIA",
        "total_time": "01:10",
        "arrival_zone": "-01:00",
        "local_arrival_date_time": "2022-08-24T08:25:00Z",
        "stop_time": "00:10",
        "departure_time_zone": "-01:00",
        "local_exit": "2022-08-24T08:35:00Z",
        "total_km": 432,
        "uf_acronym": "RO",
    },
    {
        "seccional_id": 14,
        "seccional_code": "0824",
        "seccional_name": "JARÚ",
        "total_time": "02:10",
        "arrival_zone": "-01:00",
        "local_arrival_date_time": "2022-08-24T09:25:00Z",
        "stop_time": "00:10",
        "departure_time_zone": "-01:00",
        "local_exit": "2022-08-24T09:35:00Z",
        "total_km": 231,
        "uf_acronym": "RO",
    },
    {
        "seccional_id": 3,
        "seccional_code": "0962",
        "seccional_name": "JI-PARANÁ",
        "total_time": "00:00",
        "arrival_zone": "-01:00",
        "local_arrival_date_time": "2022-08-24T07:15:00Z",
        "stop_time": "00:15",
        "departure_time_zone": "-01:00",
        "local_exit": "2022-08-24T07:30:00Z",
        "total_km": 1,
        "uf_acronym": "RO",
    },
    {
        "seccional_id": 10,
        "seccional_code": "0233",
        "seccional_name": "ARIQUEMES",
        "total_time": "03:45",
        "arrival_zone": "-01:00",
        "local_arrival_date_time": "2022-08-24T11:00:00Z",
        "stop_time": "00:30",
        "departure_time_zone": "-01:00",
        "local_exit": "2022-08-24T11:30:00Z",
        "total_km": 444,
        "uf_acronym": "RO",
    },
    {
        "seccional_id": 5,
        "seccional_code": "0234",
        "seccional_name": "PORTO VELHO",
        "total_time": "07:25",
        "arrival_zone": "-01:00",
        "local_arrival_date_time": "2022-08-24T14:40:00Z",
        "stop_time": "00:10",
        "departure_time_zone": "-01:00",
        "local_exit": "2022-08-24T14:50:00Z",
        "total_km": 510,
        "uf_acronym": "RO",
    },
]


erro_conexao = {
    "message": (
        'last connection error: connection error: desc = "transport: Error while dialing: '
        'dial tcp 192.168.1.33:50052: connect: connection timed out"'
    )
}

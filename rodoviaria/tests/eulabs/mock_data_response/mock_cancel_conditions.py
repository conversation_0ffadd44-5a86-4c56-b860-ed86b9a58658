cancel_key_hash = "cancel_key_hash"

cancelar = {
    "fine_amount": 0,
    "refund_amount": 62.41,
    "status": "authorized",
    "type": ["Cancel"],
    "key": cancel_key_hash,
    "allow_refund": False,
}

devolver = {
    "fine_amount": 0,
    "refund_amount": 62.41,
    "status": "authorized",
    "type": ["Devolution"],
    "key": cancel_key_hash,
    "allow_refund": False,
}

nao_autorizado = {
    "fine_amount": 0,
    "refund_amount": 62.41,
    "status": "not authorized",
    "type": None,
    "key": cancel_key_hash,
    "allow_refund": False,
}

sem_opcoes = {
    "fine_amount": 0,
    "refund_amount": 62.41,
    "status": "authorized",
    "type": [],
    "key": cancel_key_hash,
    "allow_refund": False,
}

erro_nao_permitido = {
    "erro": "Não é possível cancelar ou devolver uma passagem que esteja com situação diferente de Normal (N) ou BPE"
}

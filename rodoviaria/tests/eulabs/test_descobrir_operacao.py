from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from unittest import mock

import pytest
from django.db import connections
from model_bakery import baker

from commons.dateutils import timezone, to_default_tz
from rodoviaria.api.eulabs import descobrir_operacao, models
from rodoviaria.api.eulabs.api import EulabsAPI
from rodoviaria.api.eulabs.endpoints import BuscarViagensPorPeriodoConfig
from rodoviaria.models.core import LocalEmbarque, Rota, Rotina, RotinaTrechoVendido, TrechoVendido
from rodoviaria.tests.eulabs.mock_data_response import mock_summary, mock_travels_detail


@pytest.fixture
def sorted_itinerario():
    return sorted(
        mock_summary.itinerario,
        key=lambda d: datetime.strptime(d["local_exit"], "%Y-%m-%dT%H:%M:%SZ"),
    )


def test_descobrir_operacao(mocker, eulabs_api, mock_viagens_por_periodo):
    mock_group = mocker.patch("rodoviaria.api.eulabs.descobrir_operacao.chain")
    task = descobrir_operacao.descobrir_operacao(eulabs_api.login)
    mock_group.return_value.on_error.return_value.assert_called_once()
    assert task == mock_group.return_value.on_error.return_value
    assert mock_group.call_args[0][0].tasks[0].task == "rodoviaria.api.eulabs.descobrir_operacao._buscar_rota_servico"


def test_descobrir_operacao_return_task_object(mocker, eulabs_api, mock_viagens_por_periodo):
    mock_group = mocker.patch("rodoviaria.api.eulabs.descobrir_operacao.chain")
    task = descobrir_operacao.descobrir_operacao(eulabs_api.login, return_task_object=True)
    mock_group.return_value.on_error.return_value.assert_not_called()
    assert task == mock_group.return_value.on_error.return_value
    assert mock_group.call_args[0][0].tasks[0].task == "rodoviaria.api.eulabs.descobrir_operacao._buscar_rota_servico"


def test_descobrir_operacao_inativa_rotas_e_rotinas_empresa(mocker, eulabs_api, mock_viagens_por_periodo):
    mocker.patch("rodoviaria.api.eulabs.descobrir_operacao.group")
    rota = baker.make(Rota, company=eulabs_api.company, ativo=True)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=timezone.now() + timedelta(days=3), ativo=True)
    trecho_vendido = baker.make(TrechoVendido, rota=rota, ativo=True)
    descobrir_operacao.descobrir_operacao(eulabs_api.login)
    rota.refresh_from_db()
    assert rota.ativo is False
    rotina.refresh_from_db()
    assert rotina.ativo is False
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is False


@pytest.fixture
def locais_embarque_itinerario(eulabs_company, sorted_itinerario):
    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    locais = {}
    for n, cp in enumerate(itinerario):
        locais[cp.local.external_local_id] = baker.make(
            LocalEmbarque,
            local_embarque_internal_id=n,
            id_external=cp.local.external_local_id,
            nickname=cp.local.descricao,
            cidade__company=eulabs_company,
            cidade__name=cp.local.nome_cidade,
        )
    return locais


def test_buscar_rota_servico_cria_rota_rotina_trecho_vendido(
    django_assert_num_queries,
    eulabs_api,
    mock_buscar_trechos_vendidos,
    mock_buscar_itinerario,
    locais_embarque_itinerario,
    sorted_itinerario,
):
    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    expected_hash = itinerario.hash
    expected_datetime_ida = to_default_tz(itinerario[0].datetime_ida)
    expected_trecho_vendido = mock_travels_detail.trechos_vendidos[1]
    with django_assert_num_queries(27, connection=connections["rodoviaria"]):
        descobrir_operacao._buscar_rota_servico(eulabs_api.company.id, id_viagem=123123)
    rota = Rota.objects.get(company=eulabs_api.company, id_hash=expected_hash)
    rotina = Rotina.objects.get(rota=rota, datetime_ida=expected_datetime_ida)
    trecho_vendido = TrechoVendido.objects.get(
        rota=rota,
        origem__id_external=expected_trecho_vendido["origin_id"],
        destino__id_external=expected_trecho_vendido["destination_id"],
        classe=expected_trecho_vendido["class_description"],
    )
    assert trecho_vendido.ativo is False
    assert trecho_vendido.preco is None
    assert trecho_vendido.tipo_assento.tipo_assento_parceiro == expected_trecho_vendido["class_description"]
    assert trecho_vendido.capacidade_classe == expected_trecho_vendido["capacity"]
    assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=trecho_vendido)


def test_buscar_rota_servico_cria_checkpoints(
    eulabs_api, mock_buscar_trechos_vendidos, mock_buscar_itinerario, locais_embarque_itinerario, sorted_itinerario
):
    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    expected_hash = itinerario.hash
    expected_checkpoints_len = len(itinerario)
    descobrir_operacao._buscar_rota_servico(eulabs_api.company.id, id_viagem=123123)
    rota = Rota.objects.get(company=eulabs_api.company, id_hash=expected_hash)
    assert rota.itinerario.count() == expected_checkpoints_len


def test_buscar_rota_servico_atualiza_rota_rotina(
    django_assert_num_queries,
    eulabs_api,
    mock_buscar_trechos_vendidos,
    mock_buscar_itinerario,
    locais_embarque_itinerario,
    sorted_itinerario,
):
    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    rota = baker.make(Rota, company=eulabs_api.company, id_hash=itinerario.hash, ativo=False)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=to_default_tz(itinerario[0].datetime_ida), ativo=False)
    expected_trecho_vendido = mock_travels_detail.trechos_vendidos[1]
    trecho_vendido = baker.make(
        TrechoVendido,
        rota=rota,
        origem__id_external=expected_trecho_vendido["origin_id"],
        destino__id_external=expected_trecho_vendido["destination_id"],
        classe=expected_trecho_vendido["class_description"],
        capacidade_classe=expected_trecho_vendido["capacity"] - 3,
        ativo=False,
        origem=locais_embarque_itinerario[str(expected_trecho_vendido["origin_id"])],
        destino=locais_embarque_itinerario[str(expected_trecho_vendido["destination_id"])],
    )
    with (
        django_assert_num_queries(22, connection=connections["rodoviaria"]),
        mock.patch("rodoviaria.service.atualiza_operacao_utils.atualiza_trecho_batch") as mock_atualiza_trechos,
    ):
        descobrir_operacao._buscar_rota_servico(eulabs_api.company.id, id_viagem=123123)
    rota.refresh_from_db()
    assert rota.ativo is True
    rotina.refresh_from_db()
    assert rotina.ativo is True
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is False
    assert trecho_vendido.tipo_assento.tipo_assento_parceiro == expected_trecho_vendido["class_description"]
    assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=trecho_vendido)
    mock_atualiza_trechos.delay.assert_not_called()


def test_buscar_rota_servico_atualiza_rota_rotina_mantem_trecho_vendido_ativo(
    eulabs_api,
    mock_buscar_trechos_vendidos,
    mock_buscar_itinerario,
    locais_embarque_itinerario,
    sorted_itinerario,
):
    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    rota = baker.make(Rota, company=eulabs_api.company, id_hash=itinerario.hash, ativo=False)
    expected_trecho_vendido = mock_travels_detail.trechos_vendidos[1]
    trecho_vendido = baker.make(
        TrechoVendido,
        rota=rota,
        origem__id_external=expected_trecho_vendido["origin_id"],
        destino__id_external=expected_trecho_vendido["destination_id"],
        classe=expected_trecho_vendido["class_description"],
        capacidade_classe=expected_trecho_vendido["capacity"] - 3,
        ativo=True,
        origem=locais_embarque_itinerario[str(expected_trecho_vendido["origin_id"])],
        destino=locais_embarque_itinerario[str(expected_trecho_vendido["destination_id"])],
        preco=Decimal("120"),
        status_preco=TrechoVendido.StatusPreco.OK,
    )
    descobrir_operacao._buscar_rota_servico(eulabs_api.company.id, id_viagem=123123)
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is True
    assert trecho_vendido.status_preco == TrechoVendido.StatusPreco.OK
    assert trecho_vendido.preco == Decimal("120")
    assert trecho_vendido.capacidade_classe == expected_trecho_vendido["capacity"]


def test_buscar_rota_servico_atualiza_rota_rotina_ativa_trecho_vendido_e_coloca_status_ok(
    eulabs_api,
    mock_buscar_trechos_vendidos,
    mock_buscar_itinerario,
    locais_embarque_itinerario,
    sorted_itinerario,
):
    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    rota = baker.make(Rota, company=eulabs_api.company, id_hash=itinerario.hash, ativo=False)
    expected_trecho_vendido = mock_travels_detail.trechos_vendidos[1]
    trecho_vendido = baker.make(
        TrechoVendido,
        rota=rota,
        origem__id_external=expected_trecho_vendido["origin_id"],
        destino__id_external=expected_trecho_vendido["destination_id"],
        classe=expected_trecho_vendido["class_description"],
        capacidade_classe=expected_trecho_vendido["capacity"] - 3,
        ativo=False,
        origem=locais_embarque_itinerario[str(expected_trecho_vendido["origin_id"])],
        destino=locais_embarque_itinerario[str(expected_trecho_vendido["destination_id"])],
        preco=Decimal("120"),
        status_preco=TrechoVendido.StatusPreco.OK,
    )
    descobrir_operacao._buscar_rota_servico(eulabs_api.company.id, id_viagem=123123)
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is True
    assert trecho_vendido.status_preco == TrechoVendido.StatusPreco.OK
    assert trecho_vendido.preco == Decimal("120")
    assert trecho_vendido.capacidade_classe == expected_trecho_vendido["capacity"]


def test_buscar_rota_servico_atualiza_rota_rotina_coloca_status_ok_e_mantem_inativo_por_preco_absurdo(
    eulabs_api,
    mock_buscar_trechos_vendidos,
    mock_buscar_itinerario,
    locais_embarque_itinerario,
    sorted_itinerario,
):
    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    rota = baker.make(Rota, company=eulabs_api.company, id_hash=itinerario.hash, ativo=False)
    expected_trecho_vendido = mock_travels_detail.trechos_vendidos[0]
    trecho_vendido = baker.make(
        TrechoVendido,
        rota=rota,
        origem__id_external=expected_trecho_vendido["origin_id"],
        destino__id_external=expected_trecho_vendido["destination_id"],
        classe=expected_trecho_vendido["class_description"],
        capacidade_classe=expected_trecho_vendido["capacity"],
        ativo=False,
        origem=locais_embarque_itinerario[str(expected_trecho_vendido["origin_id"])],
        destino=locais_embarque_itinerario[str(expected_trecho_vendido["destination_id"])],
        preco=Decimal("999999"),
        status_preco=TrechoVendido.StatusPreco.OK,
    )
    descobrir_operacao._buscar_rota_servico(eulabs_api.company.id, id_viagem=123123)
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.ativo is False
    assert trecho_vendido.status_preco == TrechoVendido.StatusPreco.OK
    assert trecho_vendido.preco == Decimal("999999")


def test_buscar_rota_servico_atualiza_rota_rotina_sem_trechos_vendidos(
    django_assert_num_queries, eulabs_api, mock_buscar_trechos_vendidos_vazio, mock_buscar_itinerario, sorted_itinerario
):
    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    rota = baker.make(Rota, company=eulabs_api.company, id_hash=itinerario.hash, ativo=False)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=to_default_tz(itinerario[0].datetime_ida), ativo=False)
    with django_assert_num_queries(11, connection=connections["rodoviaria"]):
        descobrir_operacao._buscar_rota_servico(eulabs_api.company.id, id_viagem=123123)
    rota.refresh_from_db()
    assert rota.ativo is False
    rotina.refresh_from_db()
    assert rotina.ativo is False
    assert rota.trechovendido_set.count() == 0


def test_buscar_rota_servico_nao_ativa_rota_e_rotina_sem_trecho_vendido(
    django_assert_num_queries, mocker, eulabs_api, mock_buscar_itinerario, locais_embarque_itinerario, sorted_itinerario
):
    # setup mock
    mocker.patch.object(EulabsAPI, "buscar_trechos_vendidos").return_value = []

    itinerario = models.Itinerario.parse_obj(sorted_itinerario)
    rota = baker.make(Rota, company=eulabs_api.company, id_hash=itinerario.hash, ativo=False)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=to_default_tz(itinerario[0].datetime_ida), ativo=False)
    with django_assert_num_queries(11, connection=connections["rodoviaria"]):
        descobrir_operacao._buscar_rota_servico(eulabs_api.company.id, id_viagem=123123)
    rota.refresh_from_db()
    assert rota.ativo is False
    rotina.refresh_from_db()
    assert rotina.ativo is False


def test_descobrir_rotas_para_mais_15_dias(eulabs_api):
    viagens_api = [
        {
            "id": 1988028,
            "line_code": "16126",
            "description": "SÃO PAULO - TIETÊ x TUBARÃO",
            "departure_time": "19:00:00",
            "departure_date": "2022-07-14",
            "schedule_id": 4231,
            "company_id": 88126,
        },
        {
            "id": 1988107,
            "line_code": "11160",
            "description": "SÃO PAULO - BARRA FUNDA x ARIPUANÃ",
            "departure_time": "17:00:00",
            "departure_date": "2022-07-14",
            "schedule_id": 6356,
            "company_id": 88126,
        },
    ]
    with mock.patch(
        "rodoviaria.api.eulabs.descobrir_operacao._buscar_e_salvar_rotas_dos_servicos"
    ) as mock_buscar_e_salvar_rotas_dos_servicos, mock.patch.object(
        BuscarViagensPorPeriodoConfig, "invoke"
    ) as mock_buscar_servicos:
        mock_buscar_servicos.return_value.json.side_effect = [[viagens_api[0]], [viagens_api[1]]]
        descobrir_operacao.descobrir_operacao(client=eulabs_api.login, next_days=25)
    expected_viagens = models.Viagens.parse_obj(viagens_api)
    mock_buscar_e_salvar_rotas_dos_servicos.assert_called_once_with(
        expected_viagens.__root__,
        eulabs_api.company,
        descobrir_operacao.DEFAULT_QUEUE_NAME,
        False,
    )
    assert mock_buscar_servicos.call_count == 2

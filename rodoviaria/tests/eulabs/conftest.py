import copy
import re
from datetime import datetime, timedelta

import pytest
import responses

import rodoviaria.api.eulabs.endpoints as endpoints
from rodoviaria.api.eulabs.api import EulabsAPI
from rodoviaria.tests.eulabs.mock_data_response import (
    mock_cancel_conditions,
    mock_cancel_sale,
    mock_chooseseat,
    mock_error,
    mock_findsale,
    mock_sales,
    mock_sales_items_request_bpe,
    mock_seatingmap,
    mock_sectionals,
    mock_summary,
    mock_summary_list,
    mock_travels,
    mock_travels_detail,
)


@pytest.fixture
def eulabs_api(eulabs_login, cache_mock):
    return EulabsAPI(eulabs_login.company)


@pytest.fixture
def mock_atualiza_origens_too_many_requests(eulabs_login, requests_mock):
    requests_mock.add(
        responses.GET,
        endpoints.BuscarOrigensConfig(eulabs_login).url,
        json={},
        status=429,
    )


@pytest.fixture
def mock_atualiza_origens_bad_request(eulabs_login, requests_mock):
    requests_mock.add(
        responses.GET,
        endpoints.BuscarOrigensConfig(eulabs_login).url,
        json={},
        status=400,
    )


@pytest.fixture
def mock_buscar_origens(eulabs_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        endpoints.BuscarOrigensConfig(eulabs_login).url,
        json=mock_sectionals.locais,
    )


@pytest.fixture
def mock_buscar_corridas(eulabs_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(eulabs_login).url,
        json=mock_travels.corridas,
    )


@pytest.fixture
def mock_buscar_corridas_com_beneficios_none(eulabs_login, requests_mock):
    response_json = copy.deepcopy(mock_travels.corridas)
    response_json[0]["free_seats_benefits"] = None
    yield requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(eulabs_login).url,
        json=response_json,
    )


@pytest.fixture
def mock_buscar_corridas_sem_preco(eulabs_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(eulabs_login).url,
        json=mock_travels.corrida_sem_preco,
    )


@pytest.fixture
def mock_buscar_corridas_duas_corridas(eulabs_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(eulabs_login).url,
        json=mock_travels.duas_corridas_mesma_empresa,
    )


@pytest.fixture
def mock_buscar_corridas_duas_corridas_vagas_diferentes(eulabs_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(eulabs_login).url,
        json=mock_travels.duas_corridas_vagas_diferentes,
    )


@pytest.fixture
def mock_buscar_corridas_duas_corridas_empresas_diferentes(eulabs_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(eulabs_login).url,
        json=mock_travels.duas_corridas_empresas_diferentes,
    )


@pytest.fixture
def mock_buscar_corridas_com_items_none(eulabs_login, requests_mock):
    yield requests_mock.add(
        responses.GET,
        endpoints.BuscarCorridasConfig(eulabs_login).url,
        json=mock_travels.items_none,
    )


@pytest.fixture
def mock_retorna_poltronas(request, eulabs_login, requests_mock, get_request_params):
    travel_key = get_request_params(request, "travel_key")
    yield requests_mock.add(
        responses.GET,
        endpoints.RetornaPoltronasConfig(eulabs_login, travel_key=travel_key).url,
        json=mock_seatingmap.poltronas,
    )


@pytest.fixture
def mock_retorna_poltronas_all_amount_zero(request, eulabs_login, requests_mock, get_request_params):
    travel_key = get_request_params(request, "travel_key")
    yield requests_mock.add(
        responses.GET,
        endpoints.RetornaPoltronasConfig(eulabs_login, travel_key=travel_key).url,
        json=mock_seatingmap.poltronas_all_amount_zero,
    )


@pytest.fixture
def mock_retorna_poltronas_non_busy_com_amount_zero(request, eulabs_login, requests_mock, get_request_params):
    travel_key = get_request_params(request, "travel_key")
    yield requests_mock.add(
        responses.GET,
        endpoints.RetornaPoltronasConfig(eulabs_login, travel_key=travel_key).url,
        json=mock_seatingmap.poltronas_non_busy_com_amount_zero,
    )


@pytest.fixture
def mock_retorna_poltronas_erro_400_marcacao_impossivel(eulabs_login, requests_mock, get_request_params):
    requests_mock.add(
        responses.GET,
        endpoints.RetornaPoltronasConfig(eulabs_login, travel_key=123).url,
        status=400,
        json={"message": "não foi possível realizar a marcação solicitada, tente novamente"},
    )
    requests_mock.add(
        responses.GET,
        endpoints.RetornaPoltronasConfig(eulabs_login, travel_key=123).url,
        json=mock_seatingmap.poltronas_non_busy_com_amount_zero,
    )


@pytest.fixture
def mock_bloquear_poltrona(request, eulabs_login, requests_mock, get_request_params):
    travel_key = get_request_params(request, "travel_key")
    yield requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(eulabs_login, travel_key).url,
        json=mock_chooseseat.sucesso,
    )


@pytest.fixture
def mock_bloquear_poltrona_error(request, eulabs_login, requests_mock, get_request_params):
    travel_key = get_request_params(request, "travel_key")
    yield requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(eulabs_login, travel_key).url,
        json=mock_chooseseat.selecionada,
        status=400,
    )


@pytest.fixture
def mock_bloquear_poltrona_tres_vezes(request, eulabs_login, requests_mock, get_request_params):
    travel_key = get_request_params(request, "travel_key")
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(eulabs_login, travel_key).url,
        json=mock_chooseseat.sucesso,
    )
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(eulabs_login, travel_key).url,
        json=mock_chooseseat.selecionada,
        status=400,
    )
    requests_mock.add(
        responses.POST,
        endpoints.BloquearPoltronaConfig(eulabs_login, travel_key).url,
        json=mock_chooseseat.sucesso,
    )


@pytest.fixture
def mock_desbloquear_poltrona(request, eulabs_login, requests_mock, get_request_params):
    travel_key = get_request_params(request, "travel_key")
    selected_seat_keys = get_request_params(request, "selected_seat_keys")
    for key in selected_seat_keys:
        requests_mock.add(
            responses.DELETE,
            endpoints.DesbloquearPoltronaConfig(eulabs_login, travel_key=travel_key, selected_seat_key=key).url,
            status=204,
        )
    yield requests_mock


@pytest.fixture
def mock_efetuar_reserva(request, eulabs_login, requests_mock, get_request_params):
    poltronas = get_request_params(request, "poltronas")
    base_pedido = copy.deepcopy(mock_sales.pedido)
    items = []
    for index, poltrona in enumerate(poltronas):
        item = copy.deepcopy(base_pedido["items"][0])
        item["key"] = f"VIB-{17416588 + index}"
        item["seat"] = poltrona
        items.append(item)
    base_pedido["items"] = items
    requests_mock.add(
        responses.POST,
        endpoints.EfetuarReservaConfig(eulabs_login).url,
        json=base_pedido,
    )
    yield base_pedido


@pytest.fixture
def mock_venda_falhou_documento_invalido(eulabs_login, requests_mock):
    endpoint = endpoints.EfetuarReservaConfig(eulabs_login).url
    return requests_mock.add(responses.POST, endpoint, json=mock_sales.doc_invalido, status=422)


@pytest.fixture
def mock_venda_falhou_400(eulabs_login, requests_mock):
    endpoint = endpoints.EfetuarReservaConfig(eulabs_login).url
    return requests_mock.add(responses.POST, endpoint, status=400)


@pytest.fixture
def mock_consultar_bpe(request, eulabs_login, requests_mock, get_request_params):
    poltronas = get_request_params(request, "poltronas")
    response = copy.deepcopy(mock_sales_items_request_bpe.request_bpe)
    response["items"] = response["items"][: len(poltronas)]
    for index, poltrona in enumerate(poltronas):
        response["items"][index]["road"]["seat_number"] = poltrona
        response["items"][index]["key"] = f"VIB-{17416588 + index}"
    requests_mock.add(
        responses.GET,
        re.compile(endpoints.ConsultaBpeConfig(eulabs_login, sale_id=r"\w+", item_key=r"\w+-\w+").url),
        json=response,
    )
    yield requests_mock


@pytest.fixture
def mock_consultar_reserva(request, eulabs_login, requests_mock, get_request_params):
    poltronas = get_request_params(request, "poltronas")
    response = copy.deepcopy(mock_findsale.sale)
    response["items"] = response["items"][: len(poltronas)]
    for index, poltrona in enumerate(poltronas):
        response["items"][index]["road"]["seat_number"] = poltrona
        response["items"][index]["key"] = f"VIB-{17416588 + index}"
    requests_mock.add(
        responses.GET,
        re.compile(endpoints.ConsultaReservaConfig(eulabs_login, sale_id=r"\w+").url),
        json=response,
    )
    yield requests_mock


@pytest.fixture
def mock_consultar_reserva_lista_vazia(request, eulabs_login, requests_mock, get_request_params):
    sale_id = get_request_params(request, "sale_id")
    response = copy.deepcopy(mock_findsale.sale)
    response["items"] = None
    requests_mock.add(
        responses.GET,
        endpoints.ConsultaReservaConfig(eulabs_login, sale_id=sale_id).url,
        json=response,
    )
    yield requests_mock


@pytest.fixture
def mock_consultar_reserva_components_ausente(request, eulabs_login, requests_mock, get_request_params):
    sale_id = get_request_params(request, "sale_id")
    response = copy.deepcopy(mock_findsale.sale)
    response["items"][0].pop("components")
    response["items"][1].pop("components")
    requests_mock.add(
        responses.GET,
        endpoints.ConsultaReservaConfig(eulabs_login, sale_id=sale_id).url,
        json=response,
    )
    yield requests_mock


@pytest.fixture
def mock_consultar_reserva_cancelada(request, eulabs_login, requests_mock, get_request_params):
    sale_ids = get_request_params(request, "sale_ids")
    response = copy.deepcopy(mock_findsale.sale)
    for r in response["items"]:
        r["road"]["status"] = "canceled"
    for sale_id in sale_ids:
        requests_mock.add(
            responses.GET,
            endpoints.ConsultaReservaConfig(eulabs_login, sale_id=sale_id).url,
            json=response,
        )
    yield requests_mock


@pytest.fixture
def mock_consultar_reserva_devolvida(request, eulabs_login, requests_mock, get_request_params):
    sale_ids = get_request_params(request, "sale_ids")
    response = copy.deepcopy(mock_findsale.sale)
    for r in response["items"]:
        r["road"]["status"] = "returned"
    for sale_id in sale_ids:
        requests_mock.add(
            responses.GET,
            endpoints.ConsultaReservaConfig(eulabs_login, sale_id=sale_id).url,
            json=response,
        )
    yield requests_mock


@pytest.fixture
def mock_consultar_reserva_nao_encontrada(request, eulabs_login, requests_mock, get_request_params):
    sale_id = get_request_params(request, "sale_id")
    response = mock_findsale.nao_encontrada
    yield requests_mock.add(
        responses.GET,
        endpoints.ConsultaReservaConfig(eulabs_login, sale_id=sale_id).url,
        json=response,
        status=400,
    )


@pytest.fixture
def mock_condicoes_cancelamento_cancelar(request, eulabs_login, requests_mock, get_request_params):
    item_keys = get_request_params(request, "item_keys")
    for item_key in item_keys:
        requests_mock.add(
            responses.GET,
            endpoints.CondicoesCancelamentoConfig(eulabs_login, item_key).url,
            json=mock_cancel_conditions.cancelar,
        )
    yield requests_mock


@pytest.fixture
def mock_condicoes_cancelamento_devolver(request, eulabs_login, requests_mock, get_request_params):
    item_keys = get_request_params(request, "item_keys")
    for item_key in item_keys:
        requests_mock.add(
            responses.GET,
            endpoints.CondicoesCancelamentoConfig(eulabs_login, item_key).url,
            json=mock_cancel_conditions.devolver,
        )
    yield requests_mock


@pytest.fixture
def mock_condicoes_cancelamento_nao_autorizado(request, eulabs_login, requests_mock, get_request_params):
    item_keys = get_request_params(request, "item_keys")
    for item_key in item_keys:
        requests_mock.add(
            responses.GET,
            endpoints.CondicoesCancelamentoConfig(eulabs_login, item_key).url,
            json=mock_cancel_conditions.nao_autorizado,
        )
    yield requests_mock


@pytest.fixture
def mock_condicoes_cancelamento_sem_opcoes(request, eulabs_login, requests_mock, get_request_params):
    item_keys = get_request_params(request, "item_keys")
    for item_key in item_keys:
        requests_mock.add(
            responses.GET,
            endpoints.CondicoesCancelamentoConfig(eulabs_login, item_key).url,
            json=mock_cancel_conditions.sem_opcoes,
        )
    yield requests_mock


@pytest.fixture
def mock_condicoes_cancelamento_erro_nao_permitido(request, eulabs_login, requests_mock, get_request_params):
    item_keys = get_request_params(request, "item_keys")
    for item_key in item_keys:
        requests_mock.add(
            responses.GET,
            endpoints.CondicoesCancelamentoConfig(eulabs_login, item_key).url,
            json=mock_cancel_conditions.erro_nao_permitido,
            status=422,
        )
    yield requests_mock


@pytest.fixture
def mock_condicoes_cancelamento_connection_error(request, eulabs_login, requests_mock, get_request_params):
    item_keys = get_request_params(request, "item_keys")
    for item_key in item_keys:
        requests_mock.add(
            responses.GET,
            endpoints.CondicoesCancelamentoConfig(eulabs_login, item_key).url,
            json=mock_error.connection_error,
            status=422,
        )
    yield requests_mock


@pytest.fixture
def mock_condicoes_cancelamento_502(request, eulabs_login, requests_mock, get_request_params):
    item_keys = get_request_params(request, "item_keys")
    for item_key in item_keys:
        requests_mock.add(
            responses.GET,
            endpoints.CondicoesCancelamentoConfig(eulabs_login, item_key).url,
            json=None,
            status=502,
        )
    yield requests_mock


@pytest.fixture
def mock_cancelar_venda(request, eulabs_login, requests_mock, get_request_params):
    cancel_type = get_request_params(request, "cancel_type")
    cancel_key = get_request_params(request, "cancel_key")
    endpoint = endpoints.CancelaVendaConfig(eulabs_login, cancel_key, cancel_type).url
    yield requests_mock.add(responses.POST, endpoint, json=mock_cancel_sale.sucesso)


@pytest.fixture
def mock_cancelar_venda_ja_cancelada(request, eulabs_login, requests_mock, get_request_params):
    cancel_type = get_request_params(request, "cancel_type")
    cancel_key = get_request_params(request, "cancel_key")
    endpoint = endpoints.CancelaVendaConfig(eulabs_login, cancel_key, cancel_type).url
    yield requests_mock.add(responses.POST, endpoint, json=mock_cancel_sale.ja_cancelada, status=422)


@pytest.fixture
def mock_cancelar_venda_timeout(request, eulabs_login, requests_mock, get_request_params):
    cancel_type = get_request_params(request, "cancel_type")
    cancel_key = get_request_params(request, "cancel_key")
    endpoint = endpoints.CancelaVendaConfig(eulabs_login, cancel_key, cancel_type).url
    yield requests_mock.add(responses.POST, endpoint, status=524)


@pytest.fixture
def mock_buscar_itinerario(request, eulabs_login, requests_mock):
    endpoint = endpoints.BuscarItinerarioViagemConfig(eulabs_login, item_id=r"\w+").url
    yield requests_mock.add(responses.GET, re.compile(endpoint), json=mock_summary.itinerario)


@pytest.fixture
def mock_buscar_itinerario_erro_conexao(request, eulabs_login, requests_mock):
    endpoint = endpoints.BuscarItinerarioViagemConfig(eulabs_login, item_id=r"\w+").url
    yield requests_mock.add(responses.GET, re.compile(endpoint), json=mock_summary.erro_conexao, status=400)


@pytest.fixture
def mock_buscar_trechos_vendidos(request, eulabs_login, requests_mock):
    endpoint = endpoints.BuscarTrechosVendidosViagemConfig(eulabs_login, item_id=r"\w+").url
    yield requests_mock.add(responses.GET, re.compile(endpoint), json=mock_travels_detail.trechos_vendidos)


@pytest.fixture
def mock_buscar_trechos_vendidos_vazio(request, eulabs_login, requests_mock):
    endpoint = endpoints.BuscarTrechosVendidosViagemConfig(eulabs_login, item_id=r"\w+").url
    yield requests_mock.add(responses.GET, re.compile(endpoint), json=[])


@pytest.fixture
def mock_buscar_itinerario_duas_vezes_hashs_diferentes(request, eulabs_login, requests_mock):
    itens_ids = [v["id"] for v in mock_summary_list.viagens_mesmo_external_id[:2]]
    itinerarios = [
        sorted(
            mock_summary.itinerario,
            key=lambda d: datetime.strptime(d["local_exit"], "%Y-%m-%dT%H:%M:%SZ"),
        ),
        sorted(
            mock_summary.itinerario[1:-1],
            key=lambda d: datetime.strptime(d["local_exit"], "%Y-%m-%dT%H:%M:%SZ"),
        ),
    ]
    for i, item_id in enumerate(itens_ids):
        endpoint = endpoint = endpoints.BuscarItinerarioViagemConfig(eulabs_login, item_id=item_id).url
        requests_mock.add(responses.GET, endpoint, json=itinerarios[i])
    yield itinerarios


@pytest.fixture
def mock_buscar_itinerario_duas_vezes_hashs_iguais(request, eulabs_login, requests_mock):
    itens_ids = [v["id"] for v in mock_summary_list.viagens[:2]]
    itinerario_2 = copy.deepcopy(mock_summary.itinerario)
    for c in itinerario_2:
        c["local_exit"] = (datetime.strptime(c["local_exit"], "%Y-%m-%dT%H:%M:%SZ") + timedelta(days=1)).strftime(
            "%Y-%m-%dT%H:%M:%SZ"
        )
    itinerarios = [
        sorted(
            mock_summary.itinerario,
            key=lambda d: datetime.strptime(d["local_exit"], "%Y-%m-%dT%H:%M:%SZ"),
        ),
        sorted(
            itinerario_2,
            key=lambda d: datetime.strptime(d["local_exit"], "%Y-%m-%dT%H:%M:%SZ"),
        ),
    ]
    for i, item_id in enumerate(itens_ids):
        endpoint = endpoints.BuscarItinerarioViagemConfig(eulabs_login, item_id=item_id).url
        requests_mock.add(responses.GET, endpoint, json=itinerarios[i])
    yield itinerarios


@pytest.fixture
def mock_viagens_por_periodo(request, eulabs_login, requests_mock):
    endpoint = endpoints.BuscarViagensPorPeriodoConfig(eulabs_login).url
    yield requests_mock.add(
        responses.GET,
        endpoint,
        json=mock_summary_list.viagens,
    )


@pytest.fixture
def mock_viagens_por_periodo_none(request, eulabs_login, requests_mock):
    endpoint = endpoints.BuscarViagensPorPeriodoConfig(eulabs_login).url
    yield requests_mock.add(
        responses.GET,
        endpoint,
        json=None,
    )


@pytest.fixture
def mock_tres_viagens_periodo_mesmo_external_id(request, eulabs_login, requests_mock):
    endpoint = endpoints.BuscarViagensPorPeriodoConfig(eulabs_login).url
    yield requests_mock.add(
        responses.GET,
        endpoint,
        json=mock_summary_list.viagens_mesmo_external_id,
    )

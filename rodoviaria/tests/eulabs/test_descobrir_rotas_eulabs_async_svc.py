from datetime import date, datetime, timedelta
from unittest import mock

import pytest
from celery.result import GroupResult
from model_bakery import baker

from commons.dateutils import to_default_tz, to_tz
from rodoviaria.api.eulabs.endpoints import BuscarViagensPorPeriodoConfig
from rodoviaria.api.eulabs.models import Itinerario, Viagens
from rodoviaria.models.core import Rota, Rotina, TaskStatus, TrechoVendido
from rodoviaria.service import descobrir_rotas_eulabs_async_svc
from rodoviaria.tests.eulabs.mock_data_response import mock_summary, mock_summary_list


def test_descobrir_rotas(eulabs_api, mock_viagens_por_periodo):
    with mock.patch(
        "rodoviaria.service.descobrir_rotas_eulabs_async_svc._buscar_e_salvar_rotas_dos_servicos"
    ) as mock_buscar_e_salvar_rotas_dos_servicos:
        descobrir_rotas_eulabs_async_svc.descobrir_rotas(eulabs_api.login, eulabs_api.company.company_internal_id)
    expected_viagens = Viagens.parse_obj(mock_summary_list.viagens)
    expected_viagens.filter(company_external_id=eulabs_api.company.company_external_id)
    mock_buscar_e_salvar_rotas_dos_servicos.assert_called_once_with(
        expected_viagens.__root__,
        eulabs_api.company.company_internal_id,
        descobrir_rotas_eulabs_async_svc.DEFAULT_QUEUE_NAME,
        False,
    )


def test_descobrir_rotas_para_mais_30_dias(eulabs_api):
    viagens_api = [
        {
            "id": 1988028,
            "line_code": "16126",
            "description": "SÃO PAULO - TIETÊ x TUBARÃO",
            "departure_time": "19:00:00",
            "departure_date": "2022-07-14",
            "schedule_id": 4231,
            "company_id": eulabs_api.company.company_external_id,
        },
        {
            "id": 1988107,
            "line_code": "11160",
            "description": "SÃO PAULO - BARRA FUNDA x ARIPUANÃ",
            "departure_time": "17:00:00",
            "departure_date": "2022-07-14",
            "schedule_id": 6356,
            "company_id": eulabs_api.company.company_external_id,
        },
    ]
    with mock.patch(
        "rodoviaria.service.descobrir_rotas_eulabs_async_svc._buscar_e_salvar_rotas_dos_servicos"
    ) as mock_buscar_e_salvar_rotas_dos_servicos, mock.patch.object(
        BuscarViagensPorPeriodoConfig, "invoke"
    ) as mock_buscar_servicos:
        mock_buscar_servicos.return_value.json.side_effect = [[viagens_api[0]], [viagens_api[1]]]
        descobrir_rotas_eulabs_async_svc.descobrir_rotas(eulabs_api.login, eulabs_api.company.company_internal_id, 40)
    expected_viagens = Viagens.parse_obj(viagens_api)
    mock_buscar_e_salvar_rotas_dos_servicos.assert_called_once_with(
        expected_viagens.__root__,
        eulabs_api.company.company_internal_id,
        descobrir_rotas_eulabs_async_svc.DEFAULT_QUEUE_NAME,
        False,
    )
    assert mock_buscar_servicos.call_count == 2


def test_buscar_e_salvar_rotas_dos_servicos_create_duas_rotas_mesmo_external_id(
    eulabs_api, mock_buscar_itinerario_duas_vezes_hashs_diferentes
):
    itinerario_1 = mock_buscar_itinerario_duas_vezes_hashs_diferentes[0]
    itinerario_2 = mock_buscar_itinerario_duas_vezes_hashs_diferentes[1]
    id_external_1 = mock_summary_list.viagens_mesmo_external_id[0]["id"]
    id_external_2 = mock_summary_list.viagens_mesmo_external_id[1]["id"]
    expected_hash_1 = Itinerario.parse_obj(itinerario_1).hash
    expected_hash_2 = Itinerario.parse_obj(itinerario_2).hash
    with mock.patch("celery.result.EagerResult.parent") as mock_chord_parent:
        mock_chord_parent.return_value = GroupResult("123")
        viagens = Viagens.parse_obj(mock_summary_list.viagens_mesmo_external_id[:2])
        descobrir_rotas_eulabs_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            eulabs_api.company.company_internal_id,
            descobrir_rotas_eulabs_async_svc.DEFAULT_QUEUE_NAME,
            False,
        )
    assert Rota.objects.get(id_hash=expected_hash_1).id_external == str(id_external_1)
    assert Rota.objects.get(id_hash=expected_hash_2).id_external == str(id_external_2)


def test_buscar_e_salvar_rotas_dos_servicos_create(eulabs_api, mock_buscar_itinerario_duas_vezes_hashs_iguais):
    itinerario_1 = mock_buscar_itinerario_duas_vezes_hashs_iguais[0]
    itinerario_2 = mock_buscar_itinerario_duas_vezes_hashs_iguais[1]
    expected_hash = Itinerario.parse_obj(itinerario_1).hash
    with mock.patch("celery.result.EagerResult.parent") as mock_chord_parent:
        mock_chord_parent.return_value = GroupResult("123")
        viagens = Viagens.parse_obj(mock_summary_list.viagens[:2])
        descobrir_rotas_eulabs_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            eulabs_api.company.company_internal_id,
            descobrir_rotas_eulabs_async_svc.DEFAULT_QUEUE_NAME,
            False,
        )
    datetime_ida_1 = to_tz(
        to_default_tz(datetime.strptime(itinerario_1[0]["local_exit"], "%Y-%m-%dT%H:%M:%SZ")),
        "UTC",
    )
    id_external_1 = mock_summary_list.viagens[0]["id"]
    datetime_ida_2 = to_tz(
        to_default_tz(datetime.strptime(itinerario_2[0]["local_exit"], "%Y-%m-%dT%H:%M:%SZ")),
        "UTC",
    )
    id_external_2 = mock_summary_list.viagens[1]["id"]
    rota = Rota.objects.get(id_hash=expected_hash)
    rotinas = list(Rotina.objects.filter(rota=rota).values_list("datetime_ida", "id_external"))
    assert rotinas == [(datetime_ida_1, str(id_external_1)), (datetime_ida_2, str(id_external_2))]


def test_descobrir_rotas_inativa_rota_e_rotina(eulabs_api, mock_viagens_por_periodo):
    rota = baker.make(Rota, company=eulabs_api.company)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=datetime.now() + timedelta(days=3))
    trecho_vendido = baker.make(TrechoVendido, rota=rota, ativo=True)
    with mock.patch("rodoviaria.service.descobrir_rotas_eulabs_async_svc._buscar_e_salvar_rotas_dos_servicos"):
        descobrir_rotas_eulabs_async_svc.descobrir_rotas(eulabs_api.login, eulabs_api.company.company_internal_id)
    rota.refresh_from_db()
    rotina.refresh_from_db()
    trecho_vendido.refresh_from_db()
    assert rota.ativo is False
    assert rotina.ativo is False
    assert trecho_vendido.ativo is True


@pytest.mark.parametrize("mock_buscar_itinerario", [{"item_id": mock_summary_list.viagens[0]["id"]}], indirect=True)
def test_buscar_e_salvar_rotas_dos_servicos_update(eulabs_api, mock_buscar_itinerario):
    sorted_itinerario = sorted(
        mock_summary.itinerario,
        key=lambda d: datetime.strptime(d["local_exit"], "%Y-%m-%dT%H:%M:%SZ"),
    )
    viagem = mock_summary_list.uma_viagem[0]
    expected_hash = Itinerario.parse_obj(sorted_itinerario).hash
    rota = baker.make(
        Rota, id_hash=expected_hash, id_external="vai_ser_trocado", company=eulabs_api.company, ativo=False
    )
    with mock.patch("celery.result.EagerResult.parent") as mock_chord_parent, mock.patch(
        "rodoviaria.service.salva_rotas_bulk_svc._is_rota_valid_to_update", return_value=True
    ):
        mock_chord_parent.return_value = GroupResult("123")
        viagens = Viagens.parse_obj(mock_summary_list.uma_viagem)
        descobrir_rotas_eulabs_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            eulabs_api.company.company_internal_id,
            descobrir_rotas_eulabs_async_svc.DEFAULT_QUEUE_NAME,
            False,
        )
    rota.refresh_from_db()
    assert rota.id_external == str(viagem["id"])
    assert rota.ativo is True
    assert (
        TaskStatus.objects.get(company=eulabs_api.company, task_name=TaskStatus.Name.DESCOBRIR_ROTAS).status
        == TaskStatus.Status.SUCCESS
    )


def test_buscar_e_salvar_rotas_dos_servicos_return_task(eulabs_api):
    with mock.patch("rodoviaria.service.descobrir_rotas_eulabs_async_svc.chain") as mock_chain:
        viagens = Viagens.parse_obj(mock_summary_list.uma_viagem)
        task = descobrir_rotas_eulabs_async_svc._buscar_e_salvar_rotas_dos_servicos(
            viagens,
            eulabs_api.company.company_internal_id,
            descobrir_rotas_eulabs_async_svc.DEFAULT_QUEUE_NAME,
            True,
        )
    assert task == mock_chain.return_value.on_error.return_value


def test_error_handler(eulabs_api):
    descobrir_rotas_eulabs_async_svc.error_handler(None, None, None, eulabs_api.company.id)
    assert (
        TaskStatus.objects.get(company=eulabs_api.company, task_name=TaskStatus.Name.DESCOBRIR_ROTAS).status
        == TaskStatus.Status.FAILURE
    )


@pytest.mark.parametrize(
    "data_inicial,data_final,result",
    [
        (date(2024, 1, 10), date(2024, 1, 20), [(date(2024, 1, 10), date(2024, 1, 20))]),
        (
            date(2024, 1, 10),
            date(2024, 3, 20),
            [
                (date(2024, 1, 10), date(2024, 2, 9)),
                (date(2024, 2, 10), date(2024, 3, 10)),
                (date(2024, 3, 11), date(2024, 3, 20)),
            ],
        ),
        (
            date(2024, 1, 10),
            date(2024, 3, 10),
            [
                (date(2024, 1, 10), date(2024, 2, 9)),
                (date(2024, 2, 10), date(2024, 3, 10)),
            ],
        ),
    ],
)
def test_get_range_buscas(data_inicial, data_final, result):
    assert descobrir_rotas_eulabs_async_svc._get_range_buscas(data_inicial, data_final) == result

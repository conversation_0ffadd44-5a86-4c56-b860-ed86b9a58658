import datetime
from decimal import Decimal

import zoneinfo

from rodoviaria.api.forms import ServicoForm


def test_servico_form_trunca_preco():
    form = ServicoForm(
        external_id="2140017/3",
        preco=Decimal("248.26999999999998"),
        external_datetime_ida=datetime.datetime(
            2024, 5, 15, 22, 45, tzinfo=zoneinfo.ZoneInfo(key="America/Porto_Velho")
        ),
        external_datetime_chegada=None,
        vagas=1,
        classe="LEITO",
        capacidade_classe=None,
        distancia=None,
        classe_reduzida=None,
        key=None,
        external_company_id=None,
    )
    assert form.preco == Decimal("248.27")

from datetime import timedelta

import pytest
from django.core.management import call_command
from django.utils import timezone
from model_bakery import baker

from rodoviaria.models.core import (
    Passagem,
)
from rodoviaria.tests.eulabs.mock_data_response import mock_findsale


@pytest.mark.parametrize("mock_consultar_bpe", [{"poltronas": [29]}], indirect=True)
@pytest.mark.parametrize("mock_consultar_reserva", [{"poltronas": [29]}], indirect=True)
def test_busca_dados_bpe_passagens(eulabs_company, eulabs_login, mock_consultar_bpe, mock_consultar_reserva):
    passagem_1 = baker.make(
        Passagem,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(days=1),
        company_integracao=eulabs_company,
        pedido_external_id=123,
        numero_passagem="VIB-17416588",
    )
    passagem_1.tags.add("dados_bpe_pendente")
    call_command("busca_dados_bpe_passagens")
    passagem_1.refresh_from_db()
    assert passagem_1.chave_bpe == "29250710771628000144630020000006151000026682"
    assert passagem_1.embarque_eletronico == (
        "eucaturmobile/boarding?type=manual"
        "&identification=4278"
        "&code=0014"
        "&line=50033"
        "&departure_travel=02/07/2025 08:00:00"
        "&departure=02/07/2025 20:00:00"
        "&direction=Ida"
        "&msg=GERAR BPE ATE O EMBARQUE, ANTES DO INICIO DA PRESTAÇAO DO SERVIÇO "
        "(SEÇAO V, ANEXO XIII, ART 23 $1º, RICMS/RO-DECR.22721/18). "
        "APÓS EMBARQUE VER BPE NO APP C/MOTORISTA"
    )
    assert (
        passagem_1.bpe_qrcode
        == "https://dfe-portal.svrs.rs.gov.br/BPE/qrcode?chBPe=29250710771628000144630020000006151000026682&tpAmb=1"
    )

    assert (
        passagem_1.numero_bilhete_embarque
        == (mock_findsale.sale["items"][0]["road"]["electronic_boarding"]["TicketGate"]["number"])
    )
    assert passagem_1.tipo_taxa_embarque == Passagem.TipoTaxaEmbarque.QRCODE
    assert passagem_1.tags_set() == set()


def test_busca_dados_bpe_passagens_erro_conexao(eulabs_company, eulabs_login, requests_mock):
    passagem_1 = baker.make(
        Passagem,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(days=1),
        company_integracao=eulabs_company,
        pedido_external_id=123,
        numero_passagem="VIB-17416588",
    )
    passagem_1.tags.add("dados_bpe_pendente")
    passagem_2 = baker.make(
        Passagem,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(days=1),
        company_integracao=eulabs_company,
        pedido_external_id=123,
        numero_passagem="VIB-17416589",
    )
    passagem_2.tags.add("dados_bpe_pendente")
    call_command("busca_dados_bpe_passagens")
    passagem_1.refresh_from_db()
    passagem_2.refresh_from_db()
    assert passagem_1.chave_bpe is None
    assert passagem_1.tags_set() == {"dados_bpe_pendente"}
    assert passagem_2.chave_bpe is None
    assert passagem_2.tags_set() == {"dados_bpe_pendente"}

from unittest import mock

import pytest

from rodoviaria.api.auth import Bearer<PERSON>allable<PERSON>uth


def test_bearer_callable_auth_static_key():
    auth = BearerCallableAuth(api_key="secret123")
    request = mock.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["X-Api-Key"] == "Bearer secret123"


def test_bearer_callable_auth_callable():
    auth = BearerCallableAuth(api_key=lambda: "secret123")
    request = mock.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["X-Api-Key"] == "Bearer secret123"


def test_bearer_callable_auth_different_header_key():
    auth = BearerCallableAuth(api_key=lambda: "secret123", header_name="I-Auth")
    request = mock.MagicMock()
    request.headers = {}
    auth(request)
    assert request.headers["I-Auth"] == "Bearer secret123"


@pytest.mark.parametrize(
    "api_key, expected_api_key",
    [
        ("chave_api_string", "chave_api_string"),
        (lambda: "chave_api_funcao", "chave_api_funcao"),
    ],
)
def test_api_key_assignment(api_key, expected_api_key):
    auth = BearerCallableAuth(api_key)
    assert auth.api_key == expected_api_key

import pytest

from rodoviaria.service import poltronas_svc
from rodoviaria.service.exceptions import RodoviariaOverbookingException


def test_seleciona_poltronas_juntas():
    poltronas_map = {13: "livre", 14: "ocupada", 15: "livre", 16: "livre", 17: "livre"}
    assert poltronas_svc.seleciona_poltrona(poltronas_map, 2) == [15, 16]


def test_seleciona_poltronas_separadas():
    poltronas_map = {
        13: "livre",
        14: "ocupada",
        15: "livre",
        16: "ocupada",
        17: "livre",
    }
    assert poltronas_svc.seleciona_poltrona(poltronas_map, 2) == [13, 17]


def test_seleciona_poltronas_duas_juntas_uma_separada():
    poltronas_map = {
        13: "ocupada",
        14: "livre",
        15: "livre",
        16: "ocupada",
        17: "livre",
    }
    assert poltronas_svc.seleciona_poltrona(poltronas_map, 3) == [14, 15, 17]


def test_seleciona_uma_poltronas():
    poltronas_map = {
        13: "ocupada",
        14: "ocupada",
        15: "livre",
        16: "ocupada",
        17: "livre",
    }
    assert poltronas_svc.seleciona_poltrona(poltronas_map, 1) == [15]


def test_seleciona_poltronas_excluindo_algumas():
    poltronas_map = {
        13: "livre",
        14: "ocupada",
        15: "livre",
        16: "ocupada",
        17: "livre",
    }
    assert poltronas_svc.seleciona_poltrona(poltronas_map, 2, poltronas_to_exclude=[15]) == [13, 17]


def test_selecao_impossivel():
    poltronas_map = {
        13: "livre",
        14: "ocupada",
        15: "livre",
        16: "ocupada",
        17: "livre",
    }
    with pytest.raises(RodoviariaOverbookingException):
        poltronas_svc.seleciona_poltrona(poltronas_map, 3, poltronas_to_exclude=[15])

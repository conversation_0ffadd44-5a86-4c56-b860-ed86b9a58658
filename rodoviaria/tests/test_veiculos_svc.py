import json
from types import SimpleNamespace
from unittest import mock

import pytest
from model_bakery import baker

from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.vexado import api as vexado_api
from rodoviaria.api.vexado import models as vexado_models
from rodoviaria.models.core import Company
from rodoviaria.models.vexado import MapaVeiculo, VexadoGrupoClasse
from rodoviaria.service import veiculos_svc

DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO


def test_fetch_mapas_veiculos_api_nenhuma_empresa_com_feature(
    vexado_company_multimodelo,
):
    vexado_company_multimodelo.features = []
    vexado_company_multimodelo.save()
    response = veiculos_svc.fetch_mapas_veiculos_api()
    assert response == {"message": "Nenhuma empresa com a feature de escalar veiculos"}


def test_fetch_mapas_veiculos_api(vexado_company_multimodelo):
    vexado_company_multimodelo.features.append(Company.Feature.ESCALAR_VEICULOS)
    vexado_company_multimodelo.modelo_venda = Company.ModeloVenda.HIBRIDO
    vexado_company_multimodelo.save()
    id_external_mapa_veiculo_existente = 9432
    mapa_veiculo_existente = baker.make("rodoviaria.MapaVeiculo", id_external=id_external_mapa_veiculo_existente)
    count_mapas_veiculos_old = MapaVeiculo.objects.count()
    assert mapa_veiculo_existente.provider_data is None
    with mock.patch.object(OrchestrateRodoviaria, "get_mapas_veiculos_api") as mock_get_mapas_veiculos_api:
        mock_get_mapas_veiculos_api.return_value = [
            vexado_models.MapaVeiculoForm(
                id_external=id_external_mapa_veiculo_existente,
                has_dois_andares=False,
                quantidade_poltronas_primeiro_andar=4,
                quantidade_poltronas_segundo_andar=0,
                provider_data={"id": id_external_mapa_veiculo_existente},
            ),
            vexado_models.MapaVeiculoForm(
                id_external=3231,
                has_dois_andares=True,
                quantidade_poltronas_primeiro_andar=2,
                quantidade_poltronas_segundo_andar=10,
                provider_data={"id": 3231},
            ),
        ]
        response = veiculos_svc.fetch_mapas_veiculos_api()
    count_mapas_veiculos_new = MapaVeiculo.objects.count()
    assert count_mapas_veiculos_new == count_mapas_veiculos_old + 1  # criou um mapa
    assert response == {"mapas_veiculos_count": count_mapas_veiculos_new}
    mapa_veiculo_existente.refresh_from_db()
    assert json.loads(mapa_veiculo_existente.provider_data) == {"id": id_external_mapa_veiculo_existente}  # atualizou


def test_fetch_veiculos_api_company_sem_feature(vexado_company_multimodelo, vexado_login):
    vexado_company_multimodelo.features = ["itinerario"]
    vexado_company_multimodelo.modelo_venda = Company.ModeloVenda.HIBRIDO
    vexado_company_multimodelo.save()
    response = veiculos_svc.fetch_veiculos_api(vexado_company_multimodelo.company_internal_id)
    assert response == {"error": "Empresa sem a feature escalar_onibus"}


def test_fetch_veiculos_api(vexado_company_multimodelo):
    vexado_company_multimodelo.features.append(Company.Feature.ESCALAR_VEICULOS)
    vexado_company_multimodelo.modelo_venda = Company.ModeloVenda.HIBRIDO
    vexado_company_multimodelo.save()
    mapa_veiculo_id = 903
    mapa_veiculo_existente = baker.make("rodoviaria.MapaVeiculo", id_external=mapa_veiculo_id)
    veiculo_existente = baker.make("rodoviaria.Veiculo", id_external=200, company=vexado_company_multimodelo)
    count_veiculos_old = vexado_company_multimodelo.veiculo_set.count()
    assert veiculo_existente.mapa_veiculo is None
    with mock.patch.object(OrchestrateRodoviaria, "get_lista_veiculos_api") as mock_get_lista_veiculos_api:
        mock_get_lista_veiculos_api.return_value = [
            vexado_models.VeiculoForm(
                descricao="ABC1234",
                external_mapa_veiculo_id=mapa_veiculo_id,
                id_external=veiculo_existente.id_external,
            ),
            vexado_models.VeiculoForm(descricao="DEF4321", external_mapa_veiculo_id=903, id_external=300),
            vexado_models.VeiculoForm(
                descricao="GHI1928",
                external_mapa_veiculo_id=mapa_veiculo_id,
                id_external=400,
            ),
        ]
        response = veiculos_svc.fetch_veiculos_api(vexado_company_multimodelo.company_internal_id)
    count_veiculos_new = vexado_company_multimodelo.veiculo_set.count()
    assert count_veiculos_new == count_veiculos_old + 2  # criou dois veiculos
    assert response == {"quantidade_veiculos_da_empresa": count_veiculos_new}
    veiculo_existente.refresh_from_db()
    assert veiculo_existente.mapa_veiculo == mapa_veiculo_existente


def test_get_link_or_create_veiculo_check_veiculo_link():
    id_internal = 3023
    company_id = 860
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    veiculo_existente = baker.make("rodoviaria.Veiculo", id_internal=id_internal, company=company)
    veiculo = SimpleNamespace(veiculo_id=id_internal, company_id=company_id)
    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo_link:
        veiculos_svc.get_link_or_create_veiculo(veiculo)
    mock_check_veiculo_link.assert_called_once_with(veiculo, [veiculo_existente])


def test_get_link_or_create_veiculo_link_veiculo():
    placa = "ABC0D01"
    company_id = 860
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    veiculo_existente = baker.make("rodoviaria.Veiculo", descricao=placa + "*", company=company)
    veiculo = SimpleNamespace(placa=placa, veiculo_id=7564, company_id=company_id)
    with mock.patch("rodoviaria.service.veiculos_svc._link_veiculo") as mock_link_veiculo:
        veiculos_svc.get_link_or_create_veiculo(veiculo)
    mock_link_veiculo.assert_called_once_with(veiculo, [veiculo_existente], company)


def test_get_link_or_create_veiculo_link_errado():
    placa = "ABC0D01"
    company_id = 860
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    baker.make("rodoviaria.Veiculo", descricao=placa + "*", id_internal=3123, company=company)
    veiculo = SimpleNamespace(placa=placa, veiculo_id=7564, company_id=company_id)
    with pytest.raises(
        veiculos_svc.GetOrCreateVeiculosError,
        match=f"veiculo com a placa {veiculo.placa} está com link divergente",
    ):
        veiculos_svc.get_link_or_create_veiculo(veiculo)


def test_get_link_or_create_veiculo_create_veiculo():
    company_id = 860
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    veiculo = SimpleNamespace(placa="PLAC4", veiculo_id=5434, company_id=company_id)
    with mock.patch("rodoviaria.service.veiculos_svc._create_veiculo") as mock_create_veiculo:
        veiculos_svc.get_link_or_create_veiculo(veiculo)
    mock_create_veiculo.assert_called_once_with(veiculo, company)


def test_get_link_or_create_veiculo_veiculo_existente_de_outra_empresa():
    placa = "ABC0D01"
    company_id = 860
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    veiculo = SimpleNamespace(placa="PLAC4", veiculo_id=5434, company_id=company_id)
    baker.make(
        "rodoviaria.Veiculo",
        descricao=placa,
        id_internal=5434,
        company=baker.make("rodoviaria.Company"),
    )
    with mock.patch("rodoviaria.service.veiculos_svc._create_veiculo") as mock_create_veiculo:
        veiculos_svc.get_link_or_create_veiculo(veiculo)
    mock_create_veiculo.assert_called_once_with(veiculo, company)


def test_get_link_or_create_veiculo_veiculo_placa_existente_em_outra_empresa():
    placa = "ABC0D01"
    company_id = 860
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    veiculo = SimpleNamespace(placa=placa, veiculo_id=5434, company_id=company_id)
    baker.make("rodoviaria.Veiculo", descricao=placa, company=baker.make("rodoviaria.Company"))
    with mock.patch("rodoviaria.service.veiculos_svc._create_veiculo") as mock_create_veiculo:
        veiculos_svc.get_link_or_create_veiculo(veiculo)
    mock_create_veiculo.assert_called_once_with(veiculo, company)


def test_check_veiculo_link_erro_placa_diferente():
    veiculo = SimpleNamespace(placa="ABCD345")
    veiculos_to_check = [baker.make("rodoviaria.Veiculo", descricao="EFGH678")]
    with pytest.raises(
        veiculos_svc.GetOrCreateVeiculosError,
        match=f"veiculo com id {veiculos_to_check[0].id} com placa diferente da informada",
    ):
        veiculos_svc._check_veiculo_link(veiculo, veiculos_to_check)


def test_check_veiculo_link_erro_classes_a_mais_na_api():
    veiculo = SimpleNamespace(placa="ABCD345", classes=[SimpleNamespace(capacidade=32, tipo="leito")])
    mapa_veiculo = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=32,
        quantidade_poltronas_segundo_andar=13,
    )
    veiculos_to_check = [baker.make("rodoviaria.Veiculo", descricao="ABCD345", mapa_veiculo=mapa_veiculo)]
    with pytest.raises(
        veiculos_svc.GetOrCreateVeiculosError,
        match="Quantidade de classes não bate com os veiculos da API",
    ):
        veiculos_svc._check_veiculo_link(veiculo, veiculos_to_check)


def test_check_veiculo_link_erro_quantidade_poltronas_diferente():
    veiculo = SimpleNamespace(placa="ABCD345", classes=[SimpleNamespace(capacidade=30, tipo="leito")])
    mapa_veiculo = baker.make("rodoviaria.MapaVeiculo", quantidade_poltronas_primeiro_andar=32)
    veiculos_to_check = [baker.make("rodoviaria.Veiculo", descricao="ABCD345", mapa_veiculo=mapa_veiculo)]
    with pytest.raises(
        veiculos_svc.GetOrCreateVeiculosError,
        match=f"Quantidade de poltronas da classe {veiculo.classes[0].tipo} não bate com os veiculos da API",
    ):
        veiculos_svc._check_veiculo_link(veiculo, veiculos_to_check)


def test_check_veiculo_link_sucesso():
    capacidades = [30, 15]
    veiculo = SimpleNamespace(
        placa="ABCD345",
        classes=[
            SimpleNamespace(capacidade=capacidades[0], tipo="semi leito"),
            SimpleNamespace(capacidade=capacidades[1], tipo="leito"),
        ],
    )
    mapa_veiculo = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=capacidades[0],
        quantidade_poltronas_segundo_andar=capacidades[1],
        has_dois_andares=True,
    )
    veiculos_to_check = [baker.make("rodoviaria.Veiculo", descricao="ABCD345", mapa_veiculo=mapa_veiculo)]
    response = veiculos_svc._check_veiculo_link(veiculo, veiculos_to_check)
    assert response == [veiculos_to_check[0]]


def test_check_veiculo_link_and_update_if_necessary_check_success():
    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo_link:
        mock_check_veiculo_link.return_value = ["veiculo"]
        veiculos_svc._check_veiculo_link_and_update_if_necessary("veiculo", ["veiculos"], "company")
    mock_check_veiculo_link.assert_called_once_with("veiculo", ["veiculos"])


def test_check_veiculo_link_and_update_if_necessary_update():
    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo_link, mock.patch(
        "rodoviaria.service.veiculos_svc._update_veiculo"
    ) as mock_update_veiculo:
        mock_check_veiculo_link.side_effect = veiculos_svc.GetOrCreateVeiculosError()
        mock_update_veiculo.return_value = ["veiculo"]
        veiculos_svc._check_veiculo_link_and_update_if_necessary("veiculo", ["veiculos"], "company")
    mock_check_veiculo_link.assert_called_once_with("veiculo", ["veiculos"])
    mock_update_veiculo.assert_called_once_with("veiculo", ["veiculos"], "company")


def test_link_veiculo_update():
    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo_link, mock.patch(
        "rodoviaria.service.veiculos_svc._update_veiculo"
    ) as mock_update_veiculo:
        mock_check_veiculo_link.side_effect = veiculos_svc.GetOrCreateVeiculosError(
            "veiculo com id 10 com placa diferente da informada"
        )
        veiculos_svc._link_veiculo(SimpleNamespace(veiculo_id="9493"), [])
    mock_update_veiculo.assert_called_once_with(SimpleNamespace(veiculo_id="9493"), [], None)


def test_link_veiculo_check_erro():
    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo_link, mock.patch(
        "rodoviaria.service.veiculos_svc._update_veiculo"
    ) as mock_update_veiculo, pytest.raises(veiculos_svc.GetOrCreateVeiculosError):
        mock_check_veiculo_link.side_effect = veiculos_svc.GetOrCreateVeiculosError(
            "veiculo com id 10 com placa diferente da informada"
        )
        mock_update_veiculo.side_effect = veiculos_svc.GetOrCreateVeiculosError(
            "Não foi encontrado um conjunto de mapas de veiculos para esse onibus"
        )
        veiculos_svc._link_veiculo(SimpleNamespace(veiculo_id="9493"), [])
    mock_update_veiculo.assert_called_once_with(SimpleNamespace(veiculo_id="9493"), [], None)


def test_link_veiculo_sucesso():
    veiculo_id = "9493"
    veiculos_nao_linkados = [baker.make("rodoviaria.Veiculo")]
    assert veiculos_nao_linkados[0].id_internal is None
    with mock.patch("rodoviaria.service.veiculos_svc._check_veiculo_link") as mock_check_veiculo_link:
        mock_check_veiculo_link.return_value = veiculos_nao_linkados
        response = veiculos_svc._link_veiculo(SimpleNamespace(veiculo_id=veiculo_id), veiculos_nao_linkados)
    veiculos_nao_linkados[0].refresh_from_db()
    assert veiculos_nao_linkados[0].id_internal == int(veiculo_id)
    assert response == [veiculos_nao_linkados[0]]


def test_create_veiculo_sucesso():
    company_id = 874
    veiculo_id = 3398
    capacidades = [30, 15]
    mapa_veiculo = list(baker.make("rodoviaria.MapaVeiculo", _quantity=2))
    veiculo = SimpleNamespace(
        placa="ABCD345",
        veiculo_id=veiculo_id,
        company_id=company_id,
        classes=[
            SimpleNamespace(capacidade=capacidades[0], tipo="semi leito"),
            SimpleNamespace(capacidade=capacidades[1], tipo="leito"),
        ],
    )
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    with mock.patch(
        "rodoviaria.service.veiculos_svc._find_mapas_veiculos"
    ) as mock_find_mapas_veiculos, mock.patch.object(OrchestrateRodoviaria, "create_veiculos_api"):
        mock_find_mapas_veiculos.return_value = mapa_veiculo
        response_1 = veiculos_svc._create_veiculo(veiculo)
        response_2 = veiculos_svc._create_veiculo(veiculo, company)
    assert response_1[0].id_internal == response_1[1].id_internal == veiculo_id
    assert response_1[0].descricao == "ABCD345"
    assert response_1[1].descricao == "ABCD345*"
    assert response_2[0].id_internal == response_2[1].id_internal == veiculo_id


def test_create_veiculo_version_2():
    company_id = 874
    veiculo_id = 3398
    capacidades = [30, 15]
    mapa_veiculo = list(baker.make("rodoviaria.MapaVeiculo", _quantity=2))
    baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    veiculo = SimpleNamespace(
        placa="XYZ9987",
        veiculo_id=veiculo_id,
        company_id=company_id,
        classes=[SimpleNamespace(capacidade=capacidades[0], tipo="semi leito")],
    )
    with mock.patch(
        "rodoviaria.service.veiculos_svc._find_mapas_veiculos"
    ) as mock_find_mapas_veiculos, mock.patch.object(OrchestrateRodoviaria, "create_veiculos_api"):
        mock_find_mapas_veiculos.return_value = mapa_veiculo
        response = veiculos_svc._create_veiculo(veiculo, veiculo_version=2)
    assert response[0].descricao == "XYZ9987 v.2"
    assert response[1].descricao == "XYZ9987* v.2"


def test_create_veiculo_sucesso_mapas_veiculos_nao_encontrados():
    company_id = 874
    capacidades = [30, 15]
    veiculo = SimpleNamespace(
        company_id=company_id,
        classes=[
            SimpleNamespace(capacidade=capacidades[0]),
            SimpleNamespace(capacidade=capacidades[1]),
        ],
    )
    baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    with mock.patch("rodoviaria.service.veiculos_svc._find_mapas_veiculos") as mock_find_mapas_veiculos, pytest.raises(
        veiculos_svc.GetOrCreateVeiculosError,
        match="Não foi encontrado um conjunto de mapas de veiculos para esse onibus",
    ):
        mock_find_mapas_veiculos.side_effect = veiculos_svc.MapasVeiculosNaoEncontrados
        veiculos_svc._create_veiculo(veiculo)


def test_find_mapas_veiculos_quantidade_par_AB():
    poltronas_classes = [42, 15]
    mapa_1 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=22,
        quantidade_poltronas_segundo_andar=6,
    )
    mapa_2 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=42,
        quantidade_poltronas_segundo_andar=15,
    )
    mapas_veiculos = [mapa_1, mapa_2]
    mapas_encontrados = veiculos_svc._find_mapas_veiculos(poltronas_classes, mapas_veiculos)
    assert mapas_encontrados == [mapa_2]


def test_find_mapas_veiculos_quantidade_impar():
    mapa_1 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=22,
        quantidade_poltronas_segundo_andar=6,
    )
    mapa_2 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=42,
        quantidade_poltronas_segundo_andar=15,
    )
    mapa_3 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=6,
        quantidade_poltronas_segundo_andar=0,
    )
    mapa_4 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=20,
        quantidade_poltronas_segundo_andar=0,
    )
    mapa_5 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=50,
        quantidade_poltronas_segundo_andar=0,
    )
    mapas_veiculos = [mapa_1, mapa_2, mapa_3, mapa_4, mapa_5]

    poltronas_classes = [42, 6, 15]
    mapas_encontrados = veiculos_svc._find_mapas_veiculos(poltronas_classes, mapas_veiculos)
    assert mapas_encontrados == [mapa_2, mapa_3]

    poltronas_classes = [42, 20, 15]
    mapas_encontrados = veiculos_svc._find_mapas_veiculos(poltronas_classes, mapas_veiculos)
    assert mapas_encontrados == [mapa_4, mapa_2]

    poltronas_classes = [42, 50, 15, 6, 22]
    mapas_encontrados = veiculos_svc._find_mapas_veiculos(poltronas_classes, mapas_veiculos)
    assert mapas_encontrados == [mapa_5, mapa_2, mapa_1]


def test_find_mapas_veiculos_quantidade_par_A_e_B():
    poltronas_classes = [42, 22]
    mapa_1 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=22,
        quantidade_poltronas_segundo_andar=0,
    )
    mapa_2 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=42,
        quantidade_poltronas_segundo_andar=0,
    )
    mapa_3 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=42,
        quantidade_poltronas_segundo_andar=10,
    )
    mapas_veiculos = [mapa_1, mapa_2, mapa_3]
    mapas_encontrados = veiculos_svc._find_mapas_veiculos(poltronas_classes, mapas_veiculos)
    assert mapas_encontrados == [mapa_2, mapa_1]


def test_find_mapas_veiculos_nao_encontra_mapas():
    poltronas_classes = [42, 55]
    mapa_1 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=22,
        quantidade_poltronas_segundo_andar=0,
    )
    mapa_2 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=42,
        quantidade_poltronas_segundo_andar=0,
    )
    mapa_3 = baker.make(
        "rodoviaria.MapaVeiculo",
        quantidade_poltronas_primeiro_andar=42,
        quantidade_poltronas_segundo_andar=10,
    )
    mapas_veiculos = [mapa_1, mapa_2, mapa_3]
    with pytest.raises(veiculos_svc.MapasVeiculosNaoEncontrados):
        veiculos_svc._find_mapas_veiculos(poltronas_classes, mapas_veiculos)


def test_update_veiculo_version_2():
    veiculos_to_check = [baker.make("rodoviaria.Veiculo", descricao="LLL1999", id_internal=3242)]
    with mock.patch("rodoviaria.service.veiculos_svc._create_veiculo") as mock_create_veiculo:
        mock_create_veiculo.return_value = ["veiculos"]
        response = veiculos_svc._update_veiculo("veiculo", veiculos_to_check, "company")
    assert response == ["veiculos"]
    veiculos_to_check[0].refresh_from_db()
    assert veiculos_to_check[0].descricao == "(OLD) LLL1999"
    assert veiculos_to_check[0].id_internal is None
    mock_create_veiculo.assert_called_once_with("veiculo", "company", veiculo_version=2)


def test_update_veiculo_version_3():
    veiculos_to_check = [baker.make("rodoviaria.Veiculo", descricao="LLL1999 v.2", id_internal=5423)]
    with mock.patch("rodoviaria.service.veiculos_svc._create_veiculo") as mock_create_veiculo:
        mock_create_veiculo.return_value = ["veiculos"]
        response = veiculos_svc._update_veiculo("veiculo", veiculos_to_check, "company")
    assert response == ["veiculos"]
    veiculos_to_check[0].refresh_from_db()
    assert veiculos_to_check[0].descricao == "(OLD) LLL1999 v.2"
    assert veiculos_to_check[0].id_internal is None
    mock_create_veiculo.assert_called_once_with("veiculo", "company", veiculo_version=3)


def test_trocar_onibus_viagem_task_create(vexado_company_multimodelo, vexado_login):
    veiculo = baker.make("rodoviaria.Veiculo")
    grupo_classe = baker.make("rodoviaria.GrupoClasse")
    assert not VexadoGrupoClasse.objects.filter(grupo_classe=grupo_classe, veiculo=veiculo).exists()
    with mock.patch.object(vexado_api.VexadoAPI, "altera_veiculo_viagem") as mock_escalar_veiculo_trecho:
        veiculos_svc.trocar_onibus_viagem_task(
            132,
            5423,
            veiculo.id,
            1,
            vexado_company_multimodelo.company_internal_id,
            grupo_classe.id,
        )
    mock_escalar_veiculo_trecho.assert_called_once_with(5423, 1, 132)
    assert VexadoGrupoClasse.objects.filter(grupo_classe=grupo_classe, veiculo=veiculo).exists()


def test_trocar_onibus_viagem_task_update(vexado_company_multimodelo, vexado_login):
    veiculo = baker.make("rodoviaria.Veiculo")
    grupo_classe = baker.make("rodoviaria.GrupoClasse")
    vexado_grupo_classe = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe_external_id=133,
        grupo_classe=grupo_classe,
    )
    assert vexado_grupo_classe.veiculo is None
    with mock.patch.object(vexado_api.VexadoAPI, "altera_veiculo_viagem") as mock_escalar_veiculo_trecho:
        veiculos_svc.trocar_onibus_viagem_task(
            133,
            5423,
            veiculo.id,
            2,
            vexado_company_multimodelo.company_internal_id,
            grupo_classe.id,
        )
    mock_escalar_veiculo_trecho.assert_called_once_with(5423, 2, 133)
    vexado_grupo_classe.refresh_from_db()
    assert vexado_grupo_classe.veiculo == veiculo
    assert vexado_grupo_classe.andar == 2

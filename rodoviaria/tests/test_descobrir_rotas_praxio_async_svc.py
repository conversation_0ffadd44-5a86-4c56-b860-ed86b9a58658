import json
from types import SimpleNamespace
from unittest import mock

from celery.result import GroupResult

from rodoviaria.api.praxio.models import ListaPartidasTFO
from rodoviaria.models.core import Rota
from rodoviaria.service.descobrir_rotas_praxio_async_svc import descobrir_rotas


def test_descobrir_rotas_create(praxio_api, praxio_mock_login):
    with mock.patch(
        "rodoviaria.service.descobrir_rotas_praxio_async_svc._busca_itinerario_viagem_circuit"
    ) as mock__busca_itinerario_viagem_circuit, mock.patch(
        "celery.result.EagerResult.parent"
    ) as mock_chord_parent, mock.patch(
        "rodoviaria.service.descobrir_rotas_praxio_async_svc._call_buscar_servicos"
    ) as mock_buscar_servicos:
        mock_buscar_servicos.return_value = [SimpleNamespace(external_id=3469)]
        mock__busca_itinerario_viagem_circuit.return_value = _mock_itinerario()
        mock_chord_parent.return_value = GroupResult("123")

        company_internal_id = praxio_api.company.company_internal_id
        descobrir_rotas(
            client=praxio_api.login,
            company_internal_id=company_internal_id,
        )

        rotas = Rota.objects.filter(company__company_internal_id=company_internal_id)
        assert len(rotas) == 1
        assert _mock_itinerario().parsed[0].local.descricao in rotas[0].first_checkpoint().name


def test_descobrir_rotas_update(praxio_api, praxio_mock_login):
    Rota.objects.create(
        id_hash="TERDLOd7db1321644768cb9e3898ead0424ade",
        company=praxio_api.company,
        ativo=False,
    )
    with mock.patch(
        "rodoviaria.service.descobrir_rotas_praxio_async_svc._busca_itinerario_viagem_circuit"
    ) as mock__busca_itinerario_viagem_circuit, mock.patch(
        "celery.result.EagerResult.parent"
    ) as mock_chord_parent, mock.patch(
        "rodoviaria.service.descobrir_rotas_praxio_async_svc._call_buscar_servicos"
    ) as mock_buscar_servicos, mock.patch(
        "rodoviaria.service.salva_rotas_bulk_svc._is_rota_valid_to_update", return_value=True
    ):
        mock_buscar_servicos.return_value = [SimpleNamespace(external_id=3469)]
        mock__busca_itinerario_viagem_circuit.return_value = _mock_itinerario()
        mock_chord_parent.return_value = GroupResult("123")

        company_internal_id = praxio_api.company.company_internal_id
        descobrir_rotas(
            client=praxio_api.login,
            company_internal_id=company_internal_id,
            next_days=15,
        )

        rotas = Rota.objects.filter(company__company_internal_id=company_internal_id)
        assert len(rotas) == 1
        assert rotas[0].ativo


def _mock_itinerario():
    cleaned = [
        {
            "DataBloqSemPassageiro": "0001-01-01T00:00:00",
            "HoraBloqSemPassageiro": None,
            "IDViagem": 3469,
            "DataPartida": "2022-07-03T20:00:00",
            "HoraPartida": "2000",
            "Sentido": 0,
            "Plataforma": "",
            "HoraChegada": None,
            "ControlaPoltrona": 0,
            "ControlaPassageiros": 0,
            "ControlaCliente": 0,
            "Localidade": {
                "ListEstabelecimentos": None,
                "BilheteEmbW2i": 0,
                "IDLocalidade": 2,
                "Descricao": "TERESINA (PI)",
                "Sigla": "TER",
                "IdRegiao": None,
                "Uf": "PI",
                "IdEstabelecimento": 0,
                "IdCidade": 2211001,
                "TxEmbIdoso50": 0,
                "TxEmbIdoso100": 0,
                "TxEmbPasseLivre": 0,
                "PedagioIdoso100": 0,
                "PedagioIdoso50": 0,
                "Codigo": 0,
                "AgenciasCargas": None,
                "LastUpdate": None,
                "TxPedagioPasseLivre": None,
                "TxEmbIdosoDef": 0,
                "CodigoSgltar": 0,
            },
            "DataChegada": "0001-01-01T00:00:00",
            "Obs": None,
            "HoraTolerancia": None,
            "DataExibirViagem": "0001-01-01T00:00:00",
            "HoraExibirViagem": None,
            "QtdPoltronaBloqueio": 0,
            "Comentario": "",
        },
        {
            "DataBloqSemPassageiro": "0001-01-01T00:00:00",
            "HoraBloqSemPassageiro": None,
            "IDViagem": 3469,
            "DataPartida": "2022-07-03T20:35:00",
            "HoraPartida": "2035",
            "Sentido": 0,
            "Plataforma": "",
            "HoraChegada": None,
            "ControlaPoltrona": 0,
            "ControlaPassageiros": 0,
            "ControlaCliente": 0,
            "Localidade": {
                "ListEstabelecimentos": None,
                "BilheteEmbW2i": 0,
                "IDLocalidade": 24,
                "Descricao": "DEMERVAL LOBAO (PI)",
                "Sigla": "DLO",
                "IdRegiao": None,
                "Uf": "PI",
                "IdEstabelecimento": 0,
                "IdCidade": 2203305,
                "TxEmbIdoso50": 0,
                "TxEmbIdoso100": 0,
                "TxEmbPasseLivre": 0,
                "PedagioIdoso100": 0,
                "PedagioIdoso50": 0,
                "Codigo": 0,
                "AgenciasCargas": None,
                "LastUpdate": None,
                "TxPedagioPasseLivre": None,
                "TxEmbIdosoDef": 0,
                "CodigoSgltar": 0,
            },
            "DataChegada": "0001-01-01T00:00:00",
            "Obs": None,
            "HoraTolerancia": None,
            "DataExibirViagem": "0001-01-01T00:00:00",
            "HoraExibirViagem": None,
            "QtdPoltronaBloqueio": 0,
            "Comentario": "",
        },
    ]
    itinerario = SimpleNamespace(
        parsed=ListaPartidasTFO.parse_raw(json.dumps(cleaned)),
        cleaned=cleaned,
    )
    return itinerario

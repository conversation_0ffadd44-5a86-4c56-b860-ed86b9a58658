import json
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

from requests import PreparedRequest


def json_params_keys_matcher(
    params: Optional[Union[Dict[str, Any], List[Any]]], *, strict_match: bool = True
) -> Callable[..., Any]:
    """Matches JSON encoded data of request body.

    Parameters
    ----------
    params : dict or list
        JSON object provided to 'json' arg of request or a part of it if used in
        conjunction with ``strict_match=False``.
    strict_match : bool, default=True
        Applied only when JSON object is a dictionary.
        If set to ``True``, validates that all keys of JSON object match.
        If set to ``False``, original request may contain additional keys.


    Returns
    -------
    Callable
        Matcher function.

    """

    def match(request: PreparedRequest) -> Tuple[bool, str]:
        reason = ""
        request_body = request.body
        json_params = (params or {}) if not isinstance(params, list) else params
        try:
            if isinstance(request_body, bytes):
                request_body = request_body.decode("utf-8")
            json_body = json.loads(request_body) if request_body else {}

            if isinstance(json_params, dict):
                json_params_keys = set(json_params.keys())
            else:
                json_params_keys = set(json_params)

            json_body_keys = set(json_body.keys())
            valid = json_params_keys.issubset(json_body_keys)
            if strict_match:
                valid = valid and json_body_keys.issubset(json_params_keys)
            if not valid:
                reason = f"request.body doesn't match: {json_body_keys} doesn't match {json_params_keys}"

        except json.JSONDecodeError:
            valid = False
            reason = "request.body doesn't match: JSONDecodeError: Cannot parse request.body"

        return valid, reason

    return match

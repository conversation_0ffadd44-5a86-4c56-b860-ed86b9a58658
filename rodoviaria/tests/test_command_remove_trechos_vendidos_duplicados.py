import pytest
from django.core.management import call_command
from model_bakery import baker

from rodoviaria.models.core import LocalEmbarque, Rota, Rotina, RotinaTrechoVendido, TipoAssento, TrechoVendido


def test_remove_trechos_vendidos_duplicados_comm_rotina_trechos_vendido_duplicada():
    # BUG
    # Quando existem 2 TVs duplicas de uma rotina diferentes da rotina do "tv_keeped", a lista
    # de rtvs_to_update era preenchida com rtvs duplicados.
    # cria trechos vendidos duplicados
    rota = baker.make(Rota)
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    tipo_assento = baker.make(TipoAssento)
    trecho_vendido_1 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=10
    )
    trecho_vendido_2 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=12
    )
    trecho_vendido_3 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=10
    )
    # Atualiza mesmo com RotinaTrechoVendido duplicada
    rotina_1 = baker.make(Rotina, rota=rota)
    rotina_2 = baker.make(Rotina, rota=rota)
    baker.make(RotinaTrechoVendido, rotina=rotina_1, trecho_vendido=trecho_vendido_1)
    baker.make(RotinaTrechoVendido, rotina=rotina_2, trecho_vendido=trecho_vendido_2)
    baker.make(RotinaTrechoVendido, rotina=rotina_2, trecho_vendido=trecho_vendido_3)

    # executa o comando
    call_command("remove_trechos_vendidos_duplicados")

    # mantem o trecho vendido mais antigo
    trecho_vendido_1.refresh_from_db()
    with pytest.raises(TrechoVendido.DoesNotExist):
        trecho_vendido_2.refresh_from_db()


def test_remove_trechos_vendidos_duplicados_sem_rotina_trechos_vendidos():
    # cria trechos vendidos duplicados
    rota = baker.make(Rota)
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    tipo_assento = baker.make(TipoAssento)
    trecho_vendido_1 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=12
    )
    trecho_vendido_2 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=10
    )

    # executa o comando
    call_command("remove_trechos_vendidos_duplicados")

    # mantem o trecho vendido mais antigo
    trecho_vendido_1.refresh_from_db()
    with pytest.raises(TrechoVendido.DoesNotExist):
        trecho_vendido_2.refresh_from_db()


def test_remove_trechos_vendidos_duplicados_considera_valores_None():
    # cria trechos vendidos duplicados com valores None
    rota = baker.make(Rota)
    trecho_vendido_1 = baker.make(TrechoVendido, rota=rota)
    trecho_vendido_2 = baker.make(TrechoVendido, rota=rota)

    # executa o comando
    call_command("remove_trechos_vendidos_duplicados")

    # mantem o trecho vendido mais antigo
    trecho_vendido_1.refresh_from_db()
    with pytest.raises(TrechoVendido.DoesNotExist):
        trecho_vendido_2.refresh_from_db()


def test_remove_trechos_vendidos_duplicados_atualiza_rotina_trechos_vendidos():
    # cria trechos vendidos duplicados e rotina trecho vendido para os dois apontando para a rotinas diferentes
    rota = baker.make(Rota)
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    tipo_assento = baker.make(TipoAssento)
    rotina_1 = baker.make(Rotina)
    rotina_2 = baker.make(Rotina)
    trecho_vendido_1 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=12
    )
    rtv_1 = baker.make(RotinaTrechoVendido, rotina=rotina_1, trecho_vendido=trecho_vendido_1)
    trecho_vendido_2 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=10
    )
    rtv_2 = baker.make(RotinaTrechoVendido, rotina=rotina_2, trecho_vendido=trecho_vendido_2)

    # executa o comando
    call_command("remove_trechos_vendidos_duplicados")

    # mantem o trecho vendido mais antigo
    # e atualiza o rotina trecho vendido que apontava para o outro para apontar para o trecho vendido mantido
    trecho_vendido_1.refresh_from_db()
    with pytest.raises(TrechoVendido.DoesNotExist):
        trecho_vendido_2.refresh_from_db()
    rtv_1.refresh_from_db()
    rtv_2.refresh_from_db()
    assert rtv_1.trecho_vendido_id == rtv_2.trecho_vendido_id == trecho_vendido_1.id


def test_remove_trechos_vendidos_duplicados_deleta_rotina_trechos_vendidos_duplicado():
    # cria trechos vendidos duplicados e rotina trecho vendido para os dois apontando para a mesma rotina
    rota = baker.make(Rota)
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    tipo_assento = baker.make(TipoAssento)
    rotina = baker.make(Rotina)
    trecho_vendido_1 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=12
    )
    rtv_1 = baker.make(RotinaTrechoVendido, rotina=rotina, trecho_vendido=trecho_vendido_1)
    trecho_vendido_2 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=10
    )
    rtv_2 = baker.make(RotinaTrechoVendido, rotina=rotina, trecho_vendido=trecho_vendido_2)

    # executa comando
    call_command("remove_trechos_vendidos_duplicados")

    # mantem o trecho vendido mais antigo e apaga rotina trecho vendido que já existe no trecho_vendido mantido
    trecho_vendido_1.refresh_from_db()
    with pytest.raises(TrechoVendido.DoesNotExist):
        trecho_vendido_2.refresh_from_db()
    rtv_1.refresh_from_db()
    with pytest.raises(RotinaTrechoVendido.DoesNotExist):
        rtv_2.refresh_from_db()


def test_remove_trechos_vendidos_duplicados_mantem_trecho_vendido_com_id_internal():
    # cria trechos vendidos duplicados. Um com id internal e outro sem
    rota = baker.make(Rota)
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    tipo_assento = baker.make(TipoAssento)
    rotina_1 = baker.make(Rotina)
    rotina_2 = baker.make(Rotina)
    trecho_vendido_1 = baker.make(
        TrechoVendido, rota=rota, origem=origem, destino=destino, tipo_assento=tipo_assento, capacidade_classe=12
    )
    rtv_1 = baker.make(RotinaTrechoVendido, rotina=rotina_1, trecho_vendido=trecho_vendido_1)
    trecho_vendido_2 = baker.make(
        TrechoVendido,
        rota=rota,
        origem=origem,
        destino=destino,
        tipo_assento=tipo_assento,
        capacidade_classe=10,
        id_internal=8888,
    )
    rtv_2 = baker.make(RotinaTrechoVendido, rotina=rotina_2, trecho_vendido=trecho_vendido_2)

    # executa comando
    call_command("remove_trechos_vendidos_duplicados")

    # mantem o trecho_vendido com id internal
    with pytest.raises(TrechoVendido.DoesNotExist):
        trecho_vendido_1.refresh_from_db()
    trecho_vendido_2.refresh_from_db()
    rtv_1.refresh_from_db()
    rtv_2.refresh_from_db()
    assert rtv_1.trecho_vendido_id == rtv_2.trecho_vendido_id == trecho_vendido_2.id


def test_remove_trechos_vendidos_duplicados_nao_mexe_em_trechos_vendidos_nao_duplicados():
    tipo_assento = baker.make(TipoAssento)
    rota = baker.make(Rota)

    # duplicado
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    baker.make(
        TrechoVendido,
        rota=rota,
        origem=origem,
        destino=destino,
        tipo_assento=tipo_assento,
        capacidade_classe=10,
        _quantity=2,
    )

    # nao duplicado na mesma rota
    trecho_vendido = baker.make(TrechoVendido, rota=rota)
    previous_updated_at = trecho_vendido.updated_at

    call_command("remove_trechos_vendidos_duplicados")
    assert TrechoVendido.objects.filter(rota=rota, origem=origem, destino=destino).count() == 1

    # verifica se o não duplicado não foi alterado
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.updated_at == previous_updated_at

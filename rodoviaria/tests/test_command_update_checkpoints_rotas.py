import json

from django.core.management import call_command
from model_bakery import baker

from rodoviaria.models.core import Checkpoint, LocalEmbarque


def test_update_checkpoints_rotas(rota_totalbus, totalbus_company, totalbus_login, totalbus_locais):
    totalbus_locais.origem.id_external = json.loads(rota_totalbus.provider_data)[0]["localidade"]["id"]
    totalbus_locais.origem.save()
    cps_antigos = list(Checkpoint.objects.filter(rota=rota_totalbus).order_by("idx"))
    cps_antigos_ids = [cp.id for cp in cps_antigos]
    local_embarque_errado = baker.make(LocalEmbarque)
    cps_antigos[0].local = local_embarque_errado
    cps_antigos[0].save()
    call_command(
        "update_checkpoints_rotas",
        rotas_ids=[rota_totalbus.id],
        company_internal_id=rota_totalbus.company.company_internal_id,
    )
    cps_atuais = list(Checkpoint.objects.filter(rota=rota_totalbus).order_by("idx"))
    cps_atuais_ids = [cp.id for cp in cps_atuais]
    assert cps_atuais[0].local == totalbus_locais.origem
    for cp_id in cps_antigos_ids:
        assert cp_id not in cps_atuais_ids

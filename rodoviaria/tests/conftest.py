import json
import logging
import os
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal as D
from types import SimpleNamespace
from unittest import mock

import pytest
import responses
from constance import config as constance_config
from constance.test import override_config
from django.core.cache import caches
from django.utils import timezone
from model_bakery import baker
from ninja.testing import TestClient

import rodoviaria.api.praxio.endpoints as endpoints_praxio
import rodoviaria.api.totalbus.endpoints as endpoints_totalbus
import rodoviaria.api.vexado.endpoints as endpoints_vexado
from commons import redis
from commons.dateutils import to_default_tz, today_midnight
from core import models_company, models_grupo, models_rota
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.forms.grupo_trechoclasse_form import CidadeInfos, TrechoClasseInternoInfos
from rodoviaria.forms.staff_forms import RemanejaPassageiroForm
from rodoviaria.models import CidadeInternal, GuichepassLogin, Integra<PERSON>o, <PERSON><PERSON>, SmartbusLogin, TiSistemasLogin
from rodoviaria.models.core import Company, TaskStatus
from rodoviaria.models.praxio import PraxioLogin
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.tests.eulabs import conftest as eulabs_mock
from rodoviaria.tests.eulabs.conftest import eulabs_api
from rodoviaria.tests.guichepass import conftest as guiche_mock
from rodoviaria.tests.guichepass.conftest import guichepass_api
from rodoviaria.tests.praxio import conftest as praxio_mock
from rodoviaria.tests.praxio import mocker as mocker_praxio
from rodoviaria.tests.praxio.conftest import praxio_api
from rodoviaria.tests.ti_sistemas import conftest as ti_sistemas_mock
from rodoviaria.tests.ti_sistemas.conftest import ti_sistemas_api
from rodoviaria.tests.ti_sistemas.mock_data_response import mock_corridas
from rodoviaria.tests.totalbus import conftest as totalbus_mock
from rodoviaria.tests.totalbus import mocker as mocker_totalbus
from rodoviaria.tests.totalbus.conftest import totalbus_api
from rodoviaria.tests.vexado import conftest as vexado_mock
from rodoviaria.tests.vexado import mocker as mocker_vexado
from rodoviaria.tests.vexado.conftest import vexado_api
from rodoviaria.views import api

ninja_client = TestClient(api)


def pytest_configure():
    from django.test import TestCase

    TestCase.multi_db = True
    TestCase.databases = {"default", "rodoviaria"}


@pytest.fixture(autouse=True)
def use_dummy_cache_backend(settings):
    settings.CONSTANCE_BACKEND = "constance.backends.memory.MemoryBackend"


@pytest.fixture(autouse=True)
def ninja_skip_registry(mocker):
    mocker.patch.dict(os.environ, {"NINJA_SKIP_REGISTRY": "yes"})


@pytest.fixture(scope="session", autouse=True)
def override_constance_configs():
    with override_config(
        USE_HTTP_ASYNC=True,
    ):
        yield


@pytest.fixture(scope="session")
def celery_config():
    return {"broker_url": "amqp://", "result_backend": "redis://"}


def _adapter_httpx_mock(httpx_mock):
    """Adapts HTTPXMock to be compatible with requests_mock."""

    def add_adapter(method, url, json=None, status=200):
        httpx_mock.add_response(
            method=method,
            json=json,
            url=url,
            status_code=status,
        )

    def assert_call_count(url, count):
        assert len([r for r in httpx_mock.get_requests() if r.url == url]) == count
        return True

    httpx_mock.add = add_adapter
    httpx_mock.assert_call_count = assert_call_count
    return httpx_mock


@pytest.fixture
def http_mock(httpx_mock, requests_mock):
    """Fixture to mock HTTP requests.

    Use httpx_mock if `USE_HTTP_ASYNC` is True, otherwise uses requests_mock.
    """
    ...
    if constance_config.USE_HTTP_ASYNC:
        yield _adapter_httpx_mock(httpx_mock)
    else:
        yield requests_mock


@pytest.fixture
def get_request_params():
    def func(request, key, default=None):
        if "param" in request.__dict__ and request.param.get(key):
            return request.param.get(key, default)
        raise ValueError(
            f"{request.fixturename} espera receber o parametro '{key}' via @pytest.mark.parametrize(..., indirect=True)"
        )

    return func


guichepass_api = guichepass_api
praxio_api = praxio_api
totalbus_api = totalbus_api
vexado_api = vexado_api
ti_sistemas_api = ti_sistemas_api
eulabs_api = eulabs_api

praxio_mock_login = praxio_mock.mock_login
praxio_mock_unauthorized_login = praxio_mock.mock_unauthorized_login
praxio_mock_get_poltronas_livres = praxio_mock.mock_get_poltronas_livres
praxio_mock_poltronas_insuficientes = praxio_mock.mock_poltronas_insuficientes
praxio_mock_compra = praxio_mock.mock_compra
praxio_mock_bloqueia_poltrona = praxio_mock.mock_bloqueia_poltrona
praxio_mock_desbloqueia_poltrona = praxio_mock.mock_desbloqueia_poltrona
praxio_mock_cancela = praxio_mock.mock_cancela
praxio_mock_atualiza_origens = praxio_mock.mock_atualiza_origens
praxio_mock_lista_cidades = praxio_mock.mock_lista_cidades
praxio_mock_buscar_itinerario = praxio_mock.mock_lista_partidas_tfo

guiche_mock_login = guiche_mock.mock_guichepass_login
guiche_mock_get_poltronas_livres = guiche_mock.mock_get_poltronas_livres
guiche_mock_poltronas_insuficientes = guiche_mock.mock_poltronas_insuficientes
guiche_mock_cancela_venda = guiche_mock.mock_cancela_venda
guiche_mock_bloquear_poltrona = guiche_mock.mock_bloquear_poltrona
guiche_mock_confirma_venda = guiche_mock.mock_confirma_venda
guiche_mock_consultar_empresas = guiche_mock.mock_guichepass_consultar_empresas

totalbus_mock_login = totalbus_mock.mock_login
totalbus_mock_unauthorized_login = totalbus_mock.mock_unauthorized_login
totalbus_mock_get_poltronas_livres = totalbus_mock.mock_get_poltronas_livres
totalbus_mock_poltronas_insuficientes = totalbus_mock.mock_poltronas_insuficientes
totalbus_mock_comprar = totalbus_mock.mock_comprar
totalbus_mock_cancela_venda = totalbus_mock.mock_cancela_venda
totalbus_mock_cancela_venda_login_inativo = totalbus_mock.mock_cancela_venda_login_inativo
totalbus_mock_cancela_venda_missing_config = totalbus_mock.mock_cancela_venda_missing_config
totalbus_mock_cancela_venda_tempo_excedido = totalbus_mock.mock_cancela_venda_tempo_excedido
totalbus_mock_cancela_venda_considerado_embarcado = totalbus_mock.mock_cancela_venda_considerado_embarcado
totalbus_mock_cancela_venda_erro_poltrona_trocada = totalbus_mock.mock_cancela_bilhete_raise_poltronatrocada
totalbus_mock_bloquear_poltrona_erro_poltrona_selecionada = (
    totalbus_mock.mock_bloquear_poltrona_erro_poltrona_selecionada
)
totalbus_mock_bloquear_poltrona_erro_venda_bloqueada = totalbus_mock.mock_bloquear_poltrona_erro_venda_bloqueada
totalbus_mock_bloquear_poltrona_venda_normal = totalbus_mock.mock_bloquear_poltrona_venda_normal
totalbus_mock_buscar_itinerario = totalbus_mock.mock_buscar_itinerario
totalbus_mock_buscar_itinerario_variable = totalbus_mock.mock_buscar_itinerario_variable
totalbus_mock_buscar_itinerario_nao_localizado = totalbus_mock.mock_buscar_itinerario_nao_localizado
totalbus_mock_buscar_todos_servicos = totalbus_mock.mock_buscar_todos_servicos
totalbus_mock_busca_origem_processando_cache = totalbus_mock.mock_busca_origem_processando_cache
totalbus_mock_consultar_empresas = totalbus_mock.mock_consultar_empresas
totalbus_mock_buscar_formas_pagamento = totalbus_mock.mock_buscar_formas_pagamento
totalbus_mock_buscar_corridas = totalbus_mock.mock_buscar_servico

vexado_mock_recuperar_itinerario = vexado_mock.mock_recuperar_itinerario

ti_sistemas_mock_comprar = ti_sistemas_mock.mock_efetuar_compra_duas_poltronas_sucesso
ti_sistemas_mock_bloquear_duas_poltronas = ti_sistemas_mock.mock_bloquear_duas_poltronas
ti_sistemas_mock_cancelar = ti_sistemas_mock.mock_cancelar_compra_sucesso

eulabs_mock_buscar_corridas = eulabs_mock.mock_buscar_corridas


@pytest.fixture(autouse=True)
def db_autouse(db):
    pass


@pytest.fixture
def requests_mock():
    with responses.RequestsMock() as rsps:
        yield rsps


@pytest.fixture(autouse=True)
def cache_mock():
    clear_cache()
    yield
    clear_cache()


@pytest.fixture(autouse=True)
def praxio_request_is_not_running_on_celery():
    with mock.patch("rodoviaria.api.praxio.api.is_running_on_celery") as mock_on_celery:
        mock_on_celery.return_value = False
        yield mock_on_celery


def clear_cache():
    caches["default"].clear()
    caches["pymemcache"].clear()
    redis.get_master_client().flushall()


@pytest.fixture
def caplog(caplog):
    logger = logging.getLogger("rodoviaria")
    original = logger.propagate
    # caplog only captures records on the root logger
    logger.propagate = True
    yield caplog
    logger.propagate = original


@pytest.fixture
def clean_orchestrator_pool():
    OrchestrateRodoviaria._pool = {}


@pytest.fixture
def cidades_internal(buser_cidades):
    cidade_goiania = baker.make(
        CidadeInternal,
        pk=buser_cidades.cidade_goiania.id,
        timezone="America/Sao_Paulo",
        name="Goiânia",
        city_code_ibge=5208707,
    )
    cidade_inhumas = baker.make(
        CidadeInternal,
        timezone="America/Sao_Paulo",
        name="Inhumas",
        city_code_ibge=5210000,
    )
    yield SimpleNamespace(cidade_goiania=cidade_goiania, cidade_inhumas=cidade_inhumas)
    cidade_goiania.delete()
    cidade_inhumas.delete()


@pytest.fixture
def buser_cidades():
    cidade_goiania = baker.make(
        models_rota.Cidade,
        timezone="America/Sao_Paulo",
        name="Goiânia",
        city_code_ibge=5208707,
    )
    cidade_inhumas = baker.make(
        models_rota.Cidade,
        timezone="America/Sao_Paulo",
        name="Inhumas",
        city_code_ibge=5210000,
    )
    return SimpleNamespace(cidade_goiania=cidade_goiania, cidade_inhumas=cidade_inhumas)


@pytest.fixture
def buser_locais(buser_cidades):
    local_go_terminal = baker.make(models_rota.LocalEmbarque, cidade=buser_cidades.cidade_goiania)
    local_inh_terminal = baker.make(models_rota.LocalEmbarque, cidade=buser_cidades.cidade_inhumas)
    return SimpleNamespace(local_go_terminal=local_go_terminal, local_inh_terminal=local_inh_terminal)


@pytest.fixture
def buser_rotas(buser_locais):
    origem = buser_locais.local_go_terminal
    destino = buser_locais.local_inh_terminal

    def buser_rota(origem, destino):
        rota = baker.make(models_rota.Rota, origem=origem, destino=destino)
        trechovendido = baker.make(models_rota.TrechoVendido, rota=rota, origem=origem, destino=destino)
        return SimpleNamespace(rota=rota, trechovendido=trechovendido)

    ida = buser_rota(origem, destino)
    volta = buser_rota(destino, origem)
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.rota.delete()
    volta.rota.delete()


def buser_company():
    return models_company.Company.objects.create()


@pytest.fixture
def buser_company_expresso():
    company_expresso = buser_company()
    yield company_expresso
    company_expresso.delete()


@pytest.fixture
def buser_grupos(buser_rotas, buser_company_expresso):
    def buser_grupo(buser_rota):
        rota = buser_rota.rota
        trechovendido = buser_rota.trechovendido
        preco_rodoviaria = trechovendido.preco_rodoviaria
        tipo_assento = "leito"
        pessoas = 0
        datetime_ida = to_default_tz(timezone.now() + timedelta(hours=4))
        duracao_ida = timedelta(hours=1)
        max_split_value = round(D("0.65") * preco_rodoviaria)
        max_split_value = max_split_value - max_split_value % 10 - 1 if max_split_value % 10 < 2 else max_split_value
        grupo = baker.make(
            models_grupo.Grupo,
            rota=rota,
            datetime_ida=datetime_ida,
            status="travel_confirmed",
            confirming_probability="high",
            modelo_venda="marketplace",
            company=buser_company_expresso,
        )
        gc = baker.make(
            models_grupo.GrupoClasse,
            grupo=grupo,
            tipo_assento=tipo_assento,
            capacidade=24,
            pessoas=pessoas,
            closed=False,
        )
        tc = baker.make(
            models_grupo.TrechoClasse,
            grupo=grupo,
            datetime_ida=datetime_ida,
            grupo_classe=gc,
            trecho_vendido=trechovendido,
            preco_rodoviaria=preco_rodoviaria,
            max_split_value=max_split_value,
            ref_split_value=max_split_value,
            duracao_ida=duracao_ida,
            pessoas=pessoas,
            vagas=gc.capacidade,
        )
        return SimpleNamespace(grupo=grupo, grupoclasse=gc, trechoclasse=tc)

    ida = buser_grupo(buser_rotas.ida)
    volta = buser_grupo(buser_rotas.volta)
    unlinked_ida = buser_grupo(buser_rotas.ida)
    unlinked_volta = buser_grupo(buser_rotas.volta)
    yield SimpleNamespace(ida=ida, volta=volta, unlinked_ida=unlinked_ida, unlinked_volta=unlinked_volta)
    ida.grupo.delete()
    ida.grupoclasse.delete()
    ida.trechoclasse.delete()
    volta.grupo.delete()
    volta.grupoclasse.delete()
    volta.trechoclasse.delete()
    unlinked_ida.grupo.delete()
    unlinked_ida.grupoclasse.delete()
    unlinked_ida.trechoclasse.delete()
    unlinked_volta.grupo.delete()
    unlinked_volta.grupoclasse.delete()
    unlinked_volta.trechoclasse.delete()


@pytest.fixture
def buser_buseiro_id():
    return 1337


@pytest.fixture
def buser_travel_ida_id():
    return 2048


@pytest.fixture
def buser_travel_volta_id():
    return 2049


@pytest.fixture
def passagem_ze(buser_buseiro_id, buser_travel_ida_id, totalbus_trechoclasses):
    preco_rodoviaria = D("230.31")
    valor_cheio = D("169.61")
    desconto = preco_rodoviaria - valor_cheio
    passagem = Passagem.objects.create(
        trechoclasse_integracao=totalbus_trechoclasses.ida,
        company_integracao=totalbus_trechoclasses.ida.grupo.company_integracao,
        buseiro_internal_id=buser_buseiro_id,
        poltrona_external_id=10,
        travel_internal_id=buser_travel_ida_id,
        localizador="010000079011",
        numero_passagem="12233",
        pedido_external_id="TRANSACAO_TOTALBUS_PASSAGEM_ZE",
        status=Passagem.Status.CONFIRMADA,
        valor_cheio=valor_cheio,
        plataforma="21",
        bpe_qrcode="https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31200641550112006304630550000132511509753679&tpAmb=2",
        bpe_monitriip_code="312006415501120063046305500001325115097536790000132510MARINHO202006180600000100000119170000000000000000002565000072",
        preco_base=D("224.82"),
        taxa_embarque=D("5.49"),
        seguro=D("0.00"),
        pedagio=D("0.00"),
        outras_taxas=D("0.00"),
        outros_tributos="ICMS:12,30 (10,00%) OUTROS TRIB:13,53 (11,00%)",
        preco_rodoviaria=preco_rodoviaria,
        chave_bpe="41210891873372000692630010000037231014912221",
        numero_bpe="3723",
        serie_bpe="001",
        protocolo_autorizacao="141210006048505",
        numero_bilhete="10000000053566",
        data_autorizacao=timezone.now(),
        prefixo="16015831",
        linha="São Bernardo -> Fortaleza",
        desconto=desconto,
        cnpj="12.402.506/0001-06",
        endereco_empresa="Rua do papagaio 1999, sala 15, Quinta das Frutas, São Paulo-SP, CEP: 12900-500",
        nome_agencia="BUSER 1234",
        inscricao_estadual="*********",
        tipo_emissao="NORMAL",
    )
    yield passagem
    passagem.delete()


@pytest.fixture
def praxio_integracao():
    record = baker.make("rodoviaria.Integracao", name="praxio")
    yield record
    record.delete()


@pytest.fixture
def praxio_company(praxio_integracao, clean_orchestrator_pool):
    record = baker.make(
        "rodoviaria.Company",
        company_internal_id=112,
        integracao_id=praxio_integracao.id,
        url_base=PraxioLogin.DEFAULT_URL_BASE,
        name="Expresso Turismo",
        features=["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"],
        company_external_id=27,
    )
    yield record
    record.delete()


@pytest.fixture
def praxio_login(praxio_company):
    record = baker.make(
        "rodoviaria.PraxioLogin",
        company=praxio_company,
        name="buser.expresso",
        password="1234567",
        sistema="WINVR.EXE",
        tipo_bd=0,
        empresa="AUTUMN",
        cliente="MOREIRA_VR",
        tipo_aplicacao=0,
    )
    yield record
    record.delete()


@pytest.fixture
def praxio_cidades(buser_cidades, praxio_company):
    cidade_goiania = baker.make(
        "rodoviaria.Cidade",
        id_external="5208707",
        cidade_internal_id=buser_cidades.cidade_goiania.id,
        name="Goiânia",
        company=praxio_company,
        timezone="America/Sao_Paulo",
    )
    cidade_inhumas = baker.make(
        "rodoviaria.Cidade",
        id_external="5210000",
        cidade_internal_id=buser_cidades.cidade_inhumas.id,
        name="Inhumas",
        company=praxio_company,
        timezone="America/Cuiaba",
    )
    yield SimpleNamespace(cidade_goiania=cidade_goiania, cidade_inhumas=cidade_inhumas)
    cidade_goiania.delete()
    cidade_inhumas.delete()


@pytest.fixture
def praxio_locais(praxio_cidades, buser_locais):
    origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="2",
        cidade=praxio_cidades.cidade_goiania,
        local_embarque_internal_id=buser_locais.local_go_terminal.id,
        nickname="Terminal Rodoviario de Goiania",
    )
    destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="5",
        cidade=praxio_cidades.cidade_inhumas,
        local_embarque_internal_id=buser_locais.local_inh_terminal.id,
        nickname="Terminal Rodoviário de Inhumas",
    )
    return SimpleNamespace(origem=origem, destino=destino)


@pytest.fixture
def buser_grupos_mockado():
    def factory(company_mock):
        datetime_ida = to_default_tz(timezone.now() + timedelta(hours=4))
        duracao = timedelta(hours=1)
        grupo_ida = SimpleNamespace(
            id=7895,
            datetime_ida=datetime_ida,
            modelo_venda=Company.ModeloVenda.MARKETPLACE,
            company_id=company_mock.company_internal_id,
            company=company_mock,
            rotina_onibus_id=None,
        )
        grupo_volta = SimpleNamespace(
            id=5987,
            datetime_ida=datetime_ida,
            modelo_venda=Company.ModeloVenda.MARKETPLACE,
            company_id=company_mock.company_internal_id,
            company=company_mock,
            rotina_onibus_id=None,
        )
        cidade_origem = SimpleNamespace(
            id=832,
            name="cidade_origem",
            timezone="America/Sao_Paulo",
            city_code_ibge=321344,
        )
        cidade_destino = SimpleNamespace(id=291, name="cidade_destino", timezone="America/Bahia", city_code_ibge=542323)
        local_origem = SimpleNamespace(id=356, cidade=cidade_origem, nickname="local_origem")
        local_destino = SimpleNamespace(id=357, cidade=cidade_destino, nickname="local_destino")
        trecho_vendido_ida = SimpleNamespace(id=747, origem=local_origem, destino=local_destino)
        trecho_vendido_volta = SimpleNamespace(id=151, origem=local_destino, destino=local_origem)
        grupo_classe = SimpleNamespace(id=111, tipo_assento="leito")
        trechoclasse_ida = SimpleNamespace(
            id=555,
            grupo=grupo_ida,
            preco_rodoviaria=D("80.00"),
            grupo_classe=grupo_classe,
            datetime_ida=datetime_ida,
            max_split_value=D("80.00"),
            trecho_vendido=trecho_vendido_ida,
        )
        trechoclasse_volta = SimpleNamespace(
            id=444,
            grupo=grupo_volta,
            preco_rodoviaria=D("80.00"),
            grupo_classe=grupo_classe,
            datetime_ida=(datetime_ida + duracao),
            max_split_value=D("80.00"),
            trecho_vendido=trecho_vendido_volta,
        )
        cidade_origem_infos = CidadeInfos(
            id=cidade_origem.id,
            timezone=cidade_origem.timezone,
            name=cidade_origem.name,
            city_code_ibge=cidade_origem.city_code_ibge,
        )

        cidade_destino_infos = CidadeInfos(
            id=cidade_destino.id,
            timezone=cidade_destino.timezone,
            name=cidade_destino.name,
            city_code_ibge=cidade_destino.city_code_ibge,
        )
        trecho_classe_infos_ida = TrechoClasseInternoInfos(
            trechoclasse_id=trechoclasse_ida.id,
            localembarque_origem_id=local_origem.id,
            cidade_origem=cidade_origem_infos,
            localembarque_destino_id=local_destino.id,
            cidade_destino=cidade_destino_infos,
            trecho_datetime_ida=trechoclasse_ida.datetime_ida,
            grupo_id=grupo_ida.id,
            grupo_datetime_ida=grupo_ida.datetime_ida,
            grupoclasse_id=grupo_classe.id,
            tipo_assento=grupo_classe.tipo_assento,
        )
        ida = SimpleNamespace(
            grupo=grupo_ida,
            trechoclasse=trechoclasse_ida,
            trecho_classe_infos=trecho_classe_infos_ida,
        )
        volta = SimpleNamespace(grupo=grupo_volta, trechoclasse=trechoclasse_volta)
        return SimpleNamespace(ida=ida, volta=volta)

    return factory


@pytest.fixture
def eulabs_grupos_mockado(eulabs_company, buser_grupos_mockado):
    return buser_grupos_mockado(eulabs_company)


@pytest.fixture
def guichepass_grupos_mockado(guiche_company, buser_grupos_mockado):
    return buser_grupos_mockado(guiche_company)


@pytest.fixture
def praxio_grupos_mockado(praxio_company, buser_grupos_mockado):
    return buser_grupos_mockado(praxio_company)


@pytest.fixture
def smartbus_grupos_mockado(smartbus_company, buser_grupos_mockado):
    return buser_grupos_mockado(smartbus_company)


@pytest.fixture
def ti_sistemas_grupos_mockado(ti_sistemas_company, buser_grupos_mockado):
    return buser_grupos_mockado(ti_sistemas_company)


@pytest.fixture
def totalbus_grupos_mockado(totalbus_company, buser_grupos_mockado):
    return buser_grupos_mockado(totalbus_company)


@pytest.fixture
def vexado_grupos_mockado(vexado_company, buser_grupos_mockado):
    return buser_grupos_mockado(vexado_company)


@pytest.fixture
def praxio_grupos(praxio_company, praxio_grupos_mockado):
    def praxio_grupo(grupo):
        record = baker.make(
            "rodoviaria.Grupo",
            grupo_internal_id=grupo.id,
            company_integracao_id=praxio_company.id,
            datetime_ida=to_default_tz(grupo.datetime_ida),
        )
        return record

    ida = praxio_grupo(praxio_grupos_mockado.ida.grupo)
    volta = praxio_grupo(praxio_grupos_mockado.volta.grupo)
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def praxio_trechoclasses(praxio_company, praxio_grupos, praxio_locais, praxio_grupos_mockado):
    def praxio_trechoclasse(trechoclasse_internal_id, grupo, external_id, origem, destino, preco_rodoviaria):
        provider_data = json.dumps(mocker_praxio.MockBuscarServico.response_for_only_one_service()["ListaPartidas"][0])
        record = baker.make(
            "rodoviaria.TrechoClasse",
            trechoclasse_internal_id=trechoclasse_internal_id,
            provider_data=provider_data,
            grupo=grupo,
            external_id=external_id,
            origem=origem,
            destino=destino,
            datetime_ida=grupo.datetime_ida,
            preco_rodoviaria=preco_rodoviaria,
            external_id_tipo_veiculo=2,
            external_id_desconto=123,
            desconto=10,
        )
        return record

    ida = praxio_trechoclasse(
        praxio_grupos_mockado.ida.trechoclasse.id,
        praxio_grupos.ida,
        "16149",
        praxio_locais.origem,
        praxio_locais.destino,
        praxio_grupos_mockado.ida.trechoclasse.preco_rodoviaria,
    )
    volta = praxio_trechoclasse(
        praxio_grupos_mockado.volta.trechoclasse.id,
        praxio_grupos.volta,
        "16150",
        praxio_locais.destino,
        praxio_locais.origem,
        praxio_grupos_mockado.volta.trechoclasse.preco_rodoviaria,
    )
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def mock_praxio_comprar(praxio_mock_login, praxio_mock_compra, praxio_mock_bloqueia_poltrona):
    pass


@pytest.fixture
def mock_praxio_get_poltronas(praxio_mock_login, praxio_mock_get_poltronas_livres):
    pass


@pytest.fixture
def mock_praxio_passagem_confirmada_antiga_com_localizador(buser_travel_ida_id, praxio_trechoclasses):
    record = baker.make(
        "rodoviaria.Passagem",
        buseiro_internal_id=1,
        travel_internal_id=buser_travel_ida_id,
        trechoclasse_integracao_id=praxio_trechoclasses.ida.id,
        poltrona_external_id=3,
        localizador="1234-1234@4001",
        valor_cheio="123",
        status="confirmada",
    )
    yield record
    record.delete()


@pytest.fixture
def mock_praxio_passagem_confirmada(buser_travel_ida_id, praxio_trechoclasses):
    record = baker.make(
        "rodoviaria.Passagem",
        buseiro_internal_id=1,
        travel_internal_id=buser_travel_ida_id,
        trechoclasse_integracao_id=praxio_trechoclasses.ida.id,
        poltrona_external_id=3,
        numero_passagem="1234",
        serie_bpe="4001",
        valor_cheio="123",
        status="confirmada",
        preco_rodoviaria=D("100"),
    )
    yield record
    record.delete()


@pytest.fixture
def mock_praxio_poltronas_insuficientes(praxio_mock_login, praxio_mock_poltronas_insuficientes):
    pass


@pytest.fixture
@responses.activate
def mock_praxio_erro_confirmar_venda(praxio_mock_login, praxio_mock_bloqueia_poltrona):
    pass


@pytest.fixture
def mock_praxio_cancelar(praxio_mock_login, praxio_mock_cancela, praxio_mock_desbloqueia_poltrona):
    pass


@pytest.fixture
@responses.activate
def mock_praxio_connection_error_antes_pagamento(praxio_mock_login):
    pass


@pytest.fixture
def mock_map_cidades_praxio(praxio_mock_login, praxio_mock_atualiza_origens):
    pass


@pytest.fixture
def totalbus_integracao():
    record = baker.make("rodoviaria.Integracao", name="totalbus")
    yield record
    record.delete()


@pytest.fixture
def totalbus_company(totalbus_integracao, clean_orchestrator_pool):
    record = baker.make(
        "rodoviaria.Company",
        company_internal_id=116,
        integracao=totalbus_integracao,
        url_base="http://totalbus.base",
        name="Totalbus Transporte",
        features=["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"],
    )
    yield record
    record.delete()


@pytest.fixture
def totalbus_login(totalbus_company):
    record = baker.make(
        "rodoviaria.TotalbusLogin",
        company=totalbus_company,
        user="buser",
        password="123456",
        tenant_id="499fb577-4025-4ace-82e6-462e19540dcb",
        id_forma_pagamento=23,
        forma_pagamento="BUSER",
        validar_multa=True,
    )
    yield record
    record.delete()


@pytest.fixture
def cidades_factory(cidades_internal):
    def factory(company):
        cidade_goiania = baker.make(
            "rodoviaria.Cidade",
            id_external="1",
            cidade_internal=cidades_internal.cidade_goiania,
            company=company,
            name="Goiânia",
            timezone="America/Sao_Paulo",
        )
        cidade_inhumas = baker.make(
            "rodoviaria.Cidade",
            id_external="2",
            cidade_internal=cidades_internal.cidade_inhumas,
            company=company,
            name="Inhumas",
            timezone="America/Bahia",
        )
        return SimpleNamespace(cidade_goiania=cidade_goiania, cidade_inhumas=cidade_inhumas)

    return factory


@pytest.fixture
def totalbus_cidades(cidades_factory, totalbus_company):
    return cidades_factory(totalbus_company)


@pytest.fixture
def eulabs_cidades(cidades_factory, eulabs_company):
    return cidades_factory(eulabs_company)


@pytest.fixture
def locais_factory(buser_locais):
    def factory(cidades):
        origem = baker.make(
            "rodoviaria.LocalEmbarque",
            id_external="1",
            cidade=cidades.cidade_goiania,
            local_embarque_internal_id=buser_locais.local_go_terminal.id,
            nickname="Terminal Rodoviario de Goiania",
        )
        destino = baker.make(
            "rodoviaria.LocalEmbarque",
            id_external="2",
            cidade=cidades.cidade_inhumas,
            local_embarque_internal_id=buser_locais.local_inh_terminal.id,
            nickname="Terminal Rodoviário de Inhumas",
        )
        return SimpleNamespace(origem=origem, destino=destino)

    return factory


@pytest.fixture
def totalbus_locais(totalbus_cidades, locais_factory):
    return locais_factory(totalbus_cidades)


@pytest.fixture
def grupos_factory(buser_grupos_mockado):
    def factory(company):
        grupos = buser_grupos_mockado(company)
        grupo_ida = grupos.ida.grupo
        grupo_volta = grupos.volta.grupo
        ida = baker.make(
            "rodoviaria.Grupo",
            grupo_internal_id=grupo_ida.id,
            company_integracao_id=company.id,
            linha="GOIÂNIA (GO) X INHUMAS (GO)",
            datetime_ida=to_default_tz(grupo_ida.datetime_ida),
        )
        volta = baker.make(
            "rodoviaria.Grupo",
            grupo_internal_id=grupo_volta.id,
            company_integracao_id=company.id,
            linha="GOIÂNIA (GO) X INHUMAS (GO)",
            datetime_ida=to_default_tz(grupo_volta.datetime_ida),
        )
        return SimpleNamespace(ida=ida, volta=volta)

    return factory


@pytest.fixture
def totalbus_grupos(totalbus_company, grupos_factory):
    return grupos_factory(totalbus_company)


@pytest.fixture
def eulabs_grupos(eulabs_company, grupos_factory):
    return grupos_factory(eulabs_company)


@pytest.fixture
def grupoclasses_factory():
    def factory(grupos, grupos_mockado):
        buser_django_grupo_classe = grupos_mockado.ida.trechoclasse.grupo_classe
        ida = baker.make(
            "rodoviaria.GrupoClasse",
            grupoclasse_internal_id=buser_django_grupo_classe.id,
            grupo=grupos.ida,
            tipo_assento_internal=buser_django_grupo_classe.tipo_assento,
        )
        volta = baker.make(
            "rodoviaria.GrupoClasse",
            grupoclasse_internal_id=buser_django_grupo_classe.id,
            grupo=grupos.volta,
            tipo_assento_internal=buser_django_grupo_classe.tipo_assento,
        )

        return SimpleNamespace(ida=ida, volta=volta)

    return factory


@pytest.fixture
def totalbus_grupoclasses(totalbus_grupos, totalbus_grupos_mockado, grupoclasses_factory):
    return grupoclasses_factory(totalbus_grupos, totalbus_grupos_mockado)


@pytest.fixture
def eulabs_grupoclasses(eulabs_grupos, eulabs_grupos_mockado, grupoclasses_factory):
    return grupoclasses_factory(eulabs_grupos, eulabs_grupos_mockado)


@pytest.fixture
def trechoclasses_factory():
    def factory(grupos, grupoclasses, locais, grupos_mockado):
        ida = baker.make(
            "rodoviaria.TrechoClasse",
            trechoclasse_internal_id=grupos_mockado.ida.trechoclasse.id,
            provider_data=json.dumps(mocker_totalbus.BuscarServico.response_for_only_one_service()["lsServicos"][0]),
            grupo=grupos.ida,
            grupo_classe=grupoclasses.ida,
            external_id="1020",
            origem=locais.origem,
            destino=locais.destino,
            datetime_ida=to_default_tz(grupos_mockado.ida.trechoclasse.datetime_ida),
            preco_rodoviaria=grupos_mockado.ida.trechoclasse.preco_rodoviaria,
        )
        volta = baker.make(
            "rodoviaria.TrechoClasse",
            trechoclasse_internal_id=grupos_mockado.volta.trechoclasse.id,
            provider_data=json.dumps(mocker_totalbus.BuscarServico.response_for_only_one_service()["lsServicos"][0]),
            grupo=grupos.volta,
            grupo_classe=grupoclasses.volta,
            external_id="1020",
            origem=locais.origem,
            destino=locais.destino,
            datetime_ida=to_default_tz(grupos_mockado.volta.trechoclasse.datetime_ida),
            preco_rodoviaria=grupos_mockado.volta.trechoclasse.preco_rodoviaria,
        )
        return SimpleNamespace(ida=ida, volta=volta)

    return factory


@pytest.fixture
def totalbus_trechoclasses(
    totalbus_grupos, totalbus_grupoclasses, totalbus_locais, totalbus_grupos_mockado, trechoclasses_factory
):
    return trechoclasses_factory(totalbus_grupos, totalbus_grupoclasses, totalbus_locais, totalbus_grupos_mockado)


@pytest.fixture
def eulabs_trechoclasses(
    eulabs_grupos, eulabs_grupoclasses, eulabs_locais, eulabs_grupos_mockado, trechoclasses_factory
):
    tcs = trechoclasses_factory(eulabs_grupos, eulabs_grupoclasses, eulabs_locais, eulabs_grupos_mockado)
    # padrao Eulabs = 1234/5
    tcs.ida.external_id += "/2"
    tcs.volta.external_id += "/2"
    tcs.ida.save()
    tcs.volta.save()
    return tcs


@pytest.fixture
def mock_totalbus_comprar(totalbus_mock_comprar, totalbus_mock_bloquear_poltrona_venda_normal):
    pass


@pytest.fixture
def mock_totalbus_get_poltronas(totalbus_mock_get_poltronas_livres, totalbus_mock_bloquear_poltrona_venda_normal):
    pass


@pytest.fixture
def mock_totalbus_poltronas_insuficientes(totalbus_mock_poltronas_insuficientes):
    pass


@pytest.fixture
def mock_totalbus_cancelar(totalbus_mock_cancela_venda):
    pass


@pytest.fixture
def mock_totalbus_cancelar_login_inativo(totalbus_mock_cancela_venda_login_inativo):
    pass


@pytest.fixture
def mock_totalbus_cancelar_missing_config(totalbus_mock_cancela_venda_missing_config):
    pass


@pytest.fixture
def mock_totalbus_cancela_venda_considerado_embarcado(totalbus_mock_cancela_venda_considerado_embarcado):
    pass


@pytest.fixture
def mock_totalbus_cancela_venda_erro_poltrona_trocada(totalbus_mock_cancela_venda_erro_poltrona_trocada):
    pass


@pytest.fixture
@responses.activate
def mock_totalbus_connection_error_antes_pagamento(totalbus_api, requests_mock):
    pass


@pytest.fixture
@responses.activate
def mock_totalbus_login(totalbus_api):
    pass


@pytest.fixture
@responses.activate
def mock_totalbus_erro_confirmar_venda(totalbus_mock_bloquear_poltrona_venda_normal):
    pass


@pytest.fixture
def mock_totalbus_erro_venda_impedida(
    totalbus_mock_bloquear_poltrona_erro_venda_bloqueada,
):
    pass


@pytest.fixture
def mock_totalbus_erro_poltrona_selecionada(
    totalbus_mock_bloquear_poltrona_erro_poltrona_selecionada,
):
    pass


@pytest.fixture
def guichepass_integracao():
    record = baker.make("rodoviaria.Integracao", name="guichepass")
    yield record
    record.delete()


@pytest.fixture
def guiche_company(guichepass_integracao, clean_orchestrator_pool):
    record = baker.make(
        "rodoviaria.Company",
        company_internal_id=103,
        company_external_id=1,
        integracao_id=guichepass_integracao.id,
        url_base=GuichepassLogin.DEFAULT_URL_BASE,
        name="Expresso Virtual",
        features=["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"],
    )
    yield record
    record.delete()


@pytest.fixture
def guiche_login(guiche_company):
    record = baker.make(
        "rodoviaria.GuichepassLogin",
        company=guiche_company,
        client_id="WEB SALE",
        username="buser.expresso",
        password="123456",
    )
    yield record
    record.delete()


@pytest.fixture
def guiche_cidades(buser_cidades, guiche_company):
    cidade_goiania = baker.make(
        "rodoviaria.Cidade",
        id_external="1",
        cidade_internal_id=buser_cidades.cidade_goiania.id,
        name="Goiânia",
        company=guiche_company,
        timezone="America/Sao_Paulo",
    )
    cidade_inhumas = baker.make(
        "rodoviaria.Cidade",
        id_external="2",
        cidade_internal_id=buser_cidades.cidade_inhumas.id,
        name="Inhumas",
        company=guiche_company,
        timezone="America/Recife",
    )
    yield SimpleNamespace(cidade_goiania=cidade_goiania, cidade_inhumas=cidade_inhumas)
    cidade_goiania.delete()
    cidade_inhumas.delete()


@pytest.fixture
def guiche_locais(guiche_cidades, buser_locais):
    origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="1",
        cidade=guiche_cidades.cidade_goiania,
        local_embarque_internal_id=buser_locais.local_go_terminal.id,
        nickname="Terminal Rodoviario de Goiania",
    )
    destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="2",
        cidade=guiche_cidades.cidade_inhumas,
        local_embarque_internal_id=buser_locais.local_inh_terminal.id,
        nickname="Terminal Rodoviário de Inhumas",
    )
    yield SimpleNamespace(origem=origem, destino=destino)
    origem.delete()
    destino.delete()


@pytest.fixture
def guiche_grupos(guiche_company, guichepass_grupos_mockado):
    def guiche_grupo(grupo):
        record = baker.make(
            "rodoviaria.Grupo",
            grupo_internal_id=grupo.id,
            company_integracao_id=guiche_company.id,
        )
        return record

    ida = guiche_grupo(guichepass_grupos_mockado.ida.grupo)
    volta = guiche_grupo(guichepass_grupos_mockado.volta.grupo)
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def guiche_trechoclasses(guiche_grupos, guiche_locais, guichepass_grupos_mockado):
    def guiche_trechoclasse(trechoclasse_internal_id, grupo, origem, destino):
        record = baker.make(
            "rodoviaria.TrechoClasse",
            trechoclasse_internal_id=trechoclasse_internal_id,
            grupo=grupo,
            external_id="870-1",
            origem=origem,
            destino=destino,
            datetime_ida=to_default_tz(grupo.datetime_ida),
            preco_rodoviaria=100,
        )
        return record

    ida = guiche_trechoclasse(
        guichepass_grupos_mockado.ida.trechoclasse.id,
        guiche_grupos.ida,
        guiche_locais.origem,
        guiche_locais.destino,
    )
    volta = guiche_trechoclasse(
        guichepass_grupos_mockado.volta.trechoclasse.id,
        guiche_grupos.volta,
        guiche_locais.destino,
        guiche_locais.origem,
    )
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def mock_guiche_comprar(guiche_mock_login, guiche_mock_confirma_venda):
    pass


@pytest.fixture
def mock_guiche_get_poltronas(guiche_mock_login, guiche_mock_get_poltronas_livres):
    pass


@pytest.fixture
def mock_guiche_bloquear_poltrona(guiche_mock_login, guiche_mock_bloquear_poltrona):
    pass


@pytest.fixture
def mock_guiche_poltronas_insuficientes(guiche_mock_login, guiche_mock_poltronas_insuficientes):
    pass


@pytest.fixture
def mock_guiche_cancelar(guiche_mock_login, guiche_mock_cancela_venda):
    pass


@pytest.fixture
@responses.activate
def mock_guiche_connection_error_antes_pagamento(guiche_mock_login):
    pass


@pytest.fixture
def mock_praxio_login(requests_mock, praxio_api):
    yield requests_mock.add(
        responses.POST,
        endpoints_praxio.EfetuaLoginConfig(praxio_api.login).url,
        json=mocker_praxio.MockLogin.response(),
    )
    requests_mock.remove(responses.POST, endpoints_praxio.EfetuaLoginConfig(praxio_api.login).url)


@pytest.fixture
def mock_vexado_login(requests_mock, vexado_api):
    yield requests_mock.add(
        responses.POST,
        f"{vexado_api.base_url}/{endpoints_vexado.GetToken.path}",
        json=mocker_vexado.MockLogin.response(),
    )
    requests_mock.remove(responses.POST, f"{vexado_api.base_url}/{endpoints_vexado.GetToken.path}")


@pytest.fixture
def mock_buscar_servico_praxio(requests_mock, praxio_api, praxio_login):
    requests_mock.add(
        responses.POST,
        endpoints_praxio.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker_praxio.MockBuscarServico.response(),
    )


@pytest.fixture
def mock_buscar_servico_praxio_sem_servico(requests_mock, praxio_api, praxio_login):
    requests_mock.add(
        responses.POST,
        endpoints_praxio.BuscarServicoConfig(praxio_api.login, with_auth=False).url,
        json=mocker_praxio.MockBuscarServico.response_nenhum_servico(),
    )


@pytest.fixture
def mock_buscar_servico_totalbus(request, totalbus_login, requests_mock):
    saida = None
    try:
        saida = request.param.strftime("%Y-%m-%d %H:%M")
    except AttributeError:
        pass
    requests_mock.add(
        responses.POST,
        endpoints_totalbus.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker_totalbus.BuscarServico.response(saida),
    )


@pytest.fixture
def mock_buscar_servico_totalbus_sem_servico(request, totalbus_login, requests_mock):
    requests_mock.add(
        responses.POST,
        endpoints_totalbus.BuscarCorridasRequestConfig(totalbus_login).url,
        json=mocker_totalbus.BuscarServico.response_sem_servico(),
    )


@pytest.fixture
def mock_buscar_servico_totalbus_nao_localizado(totalbus_api, requests_mock):
    requests_mock.add(
        responses.POST,
        f"{totalbus_api.base_url}/{endpoints_totalbus.BuscarCorridas.path}",
        json=mocker_totalbus.BuscarServico.response_trecho_nao_localizado(),
        status=400,
    )


@pytest.fixture
def mock_buscar_servico_vexado(requests_mock, vexado_api):
    url = f"{vexado_api.base_url}/{endpoints_vexado.BuscarServico.path}".format(
        origem=19000, destino=21000, data_ida="2021-05-18"
    )
    requests_mock.add(responses.GET, url, json=mocker_vexado.MockBuscarServico.response_for_factory())


@pytest.fixture
def mock_recuperar_itinerario_vexado(vexado_api, requests_mock, mock_vexado_login):
    requests_mock.add(
        responses.GET,
        f"{vexado_api.base_url}/{endpoints_vexado.BuscarItinerarioCorrida.path}".format(itinerario=23123, id_empresa=5),
        json=mocker_vexado.MockRecuperarItinerario.response(),
    )


@pytest.fixture
def mock_create_veiculos_api(requests_mock, mock_vexado_login, vexado_api):
    endpoint = endpoints_vexado.CadastrarVeiculo.path
    url_cadastrar_veiculo = f"{vexado_api.base_url}/{endpoint}"
    requests_mock.add(responses.POST, url_cadastrar_veiculo)

    endpoint = endpoints_vexado.ListarVeiculos.path.format(company_external_id=vexado_api.company.company_external_id)
    url_listar_veiculos = f"{vexado_api.base_url}/{endpoint}?size=1000"
    requests_mock.add(
        responses.GET,
        url_listar_veiculos,
        json=mocker_vexado.MockListarVeiculos.response(),
    )
    yield requests_mock


@pytest.fixture
def mock_locais_do_response_de_recuperar_itinerario_vexado(vexado_api, vexado_company):
    cidade = baker.make("rodoviaria.Cidade", id_external=6036, name="Mock", company=vexado_company)
    yield [
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=2916,
            nickname="Mirabela - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=2932,
            nickname="Montes Claros - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=3,
            nickname="Taguatinga - DF",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=102,
            nickname="Cristalina - GO",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=2832,
            nickname="Japonvar - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=2983,
            nickname="Paracatu - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=166,
            nickname="Luziânia - GO",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=272,
            nickname="Valparaíso de Goiás - GO",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=3006,
            nickname="Pedras de Maria da Cruz - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=2843,
            nickname="João Pinheiro - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=2830,
            nickname="Januária - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=2796,
            nickname="Itacarambi - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=1,
            nickname="Brasília - DF",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=3031,
            nickname="Pirapora - MG",
            cidade=cidade,
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=2874,
            nickname="Lontra - MG",
            cidade=cidade,
        ),
    ]


@pytest.fixture
def mock_passagem_to_remove_totalbus(totalbus_company):
    cidade = baker.make("rodoviaria.Cidade", timezone="America/Sao_Paulo")
    origem = baker.make("rodoviaria.LocalEmbarque", cidade=cidade)
    trecho_classe = baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=4558441,
        origem=origem,
        datetime_ida=to_default_tz(datetime(2021, 9, 22, 10, 0)),
        grupo__company_integracao=totalbus_company,
    )
    passagem = baker.make(
        "rodoviaria.Passagem",
        status=Passagem.Status.CONFIRMADA,
        buseiro_internal_id=1972911,
        travel_internal_id=4558441,
        trechoclasse_integracao=trecho_classe,
        pedido_external_id="transacoaisdjaosidjoasidj",
        localizador="0100000123",
        numero_passagem="123456",
    )
    yield passagem
    passagem.delete()
    trecho_classe.delete()


@pytest.fixture
def empresa_ativa():
    empresa = baker.make(
        "rodoviaria.Company",
        company_internal_id=33333,
        company_external_id=42,
        name="Test",
        features=["active"],
        url_base="http://example.com",
        integracao__name="integracao1",
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    yield empresa
    empresa.delete()


@pytest.fixture
def remanejamento_base_params():
    return RemanejaPassageiroForm(
        travel_id=1,
        travel_destino_id=2,
        reservation_code="STARK1",
        travel_max_split_value=81.5,
        trechoclasse_origem_id=1,
        trechoclasse_destino_id=2,
        company_origem_id=1,
        modelo_venda_origem=Company.ModeloVenda.MARKETPLACE,
        company_destino_id=2,
        modelo_venda_destino=Company.ModeloVenda.MARKETPLACE,
        passengers=[],
        poltronas_destino=[1],
    )


@pytest.fixture
def paradas_mock_com_id_internal():
    yield [
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=10,
            local_embarque_internal_id=10,
            nickname="SAO PAULO - SP",
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=15,
            local_embarque_internal_id=15,
            nickname="SAO JOSE DOS CAMPOS - SP",
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=20,
            local_embarque_internal_id=20,
            nickname="TAUBATE - SP",
        ),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=25,
            local_embarque_internal_id=25,
            nickname="RIO DE JANEIRO - RJ",
        ),
    ]


@pytest.fixture
def paradas_mock():
    yield [
        baker.make("rodoviaria.LocalEmbarque", id_external=10, nickname="SAO PAULO - SP"),
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=15,
            nickname="SAO JOSE DOS CAMPOS - SP",
        ),
        baker.make("rodoviaria.LocalEmbarque", id_external=20, nickname="TAUBATE - SP"),
        baker.make("rodoviaria.LocalEmbarque", id_external=25, nickname="RIO DE JANEIRO - RJ"),
    ]


@pytest.fixture
def rota_mock(paradas_mock):
    rota = baker.make(
        "rodoviaria.Rota",
        id_hash="123abc456def789ghi",
        id_external=1,
        provider_data=json.dumps(
            [
                {
                    "localidade": {
                        "cidade": parada.nickname,
                        "id": parada.id_external,
                        "uf": parada.nickname.partition("-")[2].lstrip(),
                    }
                }
                for parada in paradas_mock
            ]
        ),
    )
    yield rota
    rota.delete()


@pytest.fixture
def rotina_mock(rota_mock):
    rotina = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        ativo=True,
        datetime_ida=to_default_tz(datetime(2021, 12, 1, 22)),
    )
    return rotina


@pytest.fixture
def vexado_integracao():
    record = baker.make("rodoviaria.Integracao", name="vexado")
    yield record
    record.delete()


@pytest.fixture
def vexado_company(vexado_integracao, clean_orchestrator_pool):
    record = baker.make(
        "rodoviaria.Company",
        company_internal_id=118,
        company_external_id=5,
        integracao_id=vexado_integracao.id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
        url_base="http://vexado.com.br",
        name="Giro Turismo",
        features=[
            "buscar_servico",
            "add_pax_staff",
            "bpe",
            "itinerario",
            "motorista",
            "active",
            "escalar_veiculos",
        ],
    )
    yield record
    record.delete()


@pytest.fixture
def vexado_company_multimodelo(vexado_company):
    company_mkp = baker.make(
        Company,
        company_internal_id=vexado_company.company_internal_id,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    yield vexado_company
    company_mkp.delete()


@pytest.fixture
def vexado_login(vexado_company):
    record = baker.make(
        "rodoviaria.VexadoLogin",
        company=vexado_company,
        user="buser",
        password="1234567",
        site="adminVexado",
    )
    yield record
    record.delete()


@pytest.fixture
def mock_add_multiple_pax(request):
    sp = baker.make("rodoviaria.LocalEmbarque", local_embarque_internal_id=450, id_external=1)
    rio = baker.make("rodoviaria.LocalEmbarque", local_embarque_internal_id=205, id_external=2932)
    trecho_classe = baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=8976,
        external_id=21136,
        origem=sp,
        destino=rio,
        provider_data=json.dumps({"tipoVeiculo": "Semi Leito"}),
        preco_rodoviaria=D("323.12"),
        datetime_ida=timezone.now() - timedelta(hours=2),
    )
    yield {
        "trechoclasse_id": trecho_classe.trechoclasse_internal_id,
        "valor_por_buseiro": "290.00",
        "id_origem": sp.local_embarque_internal_id,
        "id_destino": rio.local_embarque_internal_id,
        "travels": [
            {
                "travel_id": 10,
                "valor_por_buseiro": 323.12,
                "buseiros": [
                    {
                        "id": 25,
                        "name": "Fulano",
                        "cpf": "90590807005",
                        "rg_number": "*********",
                        "phone": "12912122121",
                        "buyer_cpf": "12912122121",
                    },
                    {
                        "id": 50,
                        "name": "Ciclano",
                        "cpf": "77183388000",
                        "rg_number": "*********",
                        "phone": "12912122121",
                        "buyer_cpf": "",
                    },
                ],
            },
            {
                "travel_id": 20,
                "valor_por_buseiro": 349.12,
                "buseiros": [
                    {
                        "id": 75,
                        "name": "Armando",
                        "cpf": "47131859060",
                        "rg_number": "*********",
                        "phone": "12912122121",
                        "buyer_cpf": "12912122121",
                    }
                ],
            },
        ],
    }


@pytest.fixture
def mock_grupo_trecho_classe(rota_mock, totalbus_integracao, totalbus_company):
    grupo = baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=27,
        rota=rota_mock,
        company_integracao=totalbus_company,
    )
    trecho_classe = baker.make(
        "rodoviaria.TrechoClasse",
        grupo=grupo,
        external_id=120,
        origem=baker.make(
            "rodoviaria.LocalEmbarque",
            cidade=baker.make("rodoviaria.Cidade", timezone="America/Cuiaba"),
        ),
    )
    yield SimpleNamespace(grupo=grupo, trecho_classe=trecho_classe, rota=rota_mock, company=totalbus_company)


@pytest.fixture
def mock_itinerario(rota_mock):
    checkpoints_map = {}
    locais = []
    for i in range(3):
        local = SimpleNamespace(
            nome_cidade=f"Local{i}",
            external_local_id=10 + i,
            uf="SP",
            external_cidade_id=20 + i,
            descricao=f"Local{i} - SP",
            id_cidade_ibge=f"32312{i}",
            first_cp_shift=timedelta(seconds=3600 * i),
        )
        checkpoints_map[str(10 + i)] = local
        locais.append(local)
    origem = SimpleNamespace(local=locais[0], datetime_ida=datetime(2021, 12, 1, 22))
    parada = SimpleNamespace(local=locais[1], datetime_ida=datetime(2021, 12, 1, 22, 30))
    destino = SimpleNamespace(local=locais[2], datetime_ida=datetime(2021, 12, 1, 23, 30))
    yield SimpleNamespace(checkpoints=[origem, parada, destino], map=checkpoints_map)


@pytest.fixture
def mocked_map_cidades_destinos(mock_itinerario):
    return {
        c.local.external_local_id: [ckp.local.external_local_id for ckp in mock_itinerario.checkpoints]
        for c in mock_itinerario.checkpoints
    }


@pytest.fixture
def empresas_mock():
    empresa1 = baker.make(
        "rodoviaria.Company",
        pk=1,
        name="banana corp",
        features=["active"],
        modelo_venda="marketplace",
    )
    empresa2 = baker.make(
        "rodoviaria.Company",
        pk=2,
        name="abacate ltda",
        modelo_venda="marketplace",
        company_internal_id=18,
    )
    empresa3 = baker.make(
        "rodoviaria.Company",
        pk=3,
        name="abobora bus",
        features=["active"],
        modelo_venda="hibrido",
    )
    yield empresa1, empresa2, empresa3
    empresa1.delete()
    empresa2.delete()
    empresa3.delete()


@pytest.fixture
def rota_totalbus(totalbus_company, buser_rotas, totalbus_locais):
    totalbus_provider_data = [
        {
            "localidade": {"id": 321, "cidade": "PENAPOLIS - SP", "uf": "SP"},
            "distancia": "108.8",
            "permanencia": "00:00",
            "data": "2021-11-25",
            "hora": "07:30",
        },
        {
            "localidade": {
                "id": 1231,
                "cidade": "SAO JOSE DO RIO PRETO - SP",
                "uf": "SP",
            },
            "distancia": "1000.00",
            "permanencia": "00:00",
            "data": "2021-11-25",
            "hora": "09:15",
        },
        {
            "localidade": {
                "id": 11,
                "cidade": "SAO JOSE DO RIO PARDO - SP",
                "uf": "SP",
            },
            "distancia": "0.00",
            "permanencia": "00:00",
            "data": "2021-11-25",
            "hora": "16:00",
        },
    ]
    rota = baker.make(
        "rodoviaria.Rota",
        provider_data=json.dumps(totalbus_provider_data),
        id_hash="206320630bac9d997fd3e6512d618615ee7b7889",
        id_external="12",
        id_internal=buser_rotas.ida.rota.id,
        company=totalbus_company,
        data_limite=to_default_tz(datetime.now() + timedelta(days=90)).date(),
    )
    origem = totalbus_locais.origem

    departure_str = f"{totalbus_provider_data[0]['data']} {totalbus_provider_data[0]['hora']}"
    departure_dt = to_default_tz(datetime.strptime(departure_str, "%Y-%m-%d %H:%M"))
    baker.make(
        "rodoviaria.Checkpoint",
        rota=rota,
        idx=0,
        local=origem,
        id_external=origem.id_external,
        duracao=timedelta(seconds=0),
        tempo_embarque=timedelta(seconds=0),
        departure=departure_dt,
    )
    destino = totalbus_locais.destino
    baker.make(
        "rodoviaria.Checkpoint",
        rota=rota,
        idx=1,
        local=destino,
        id_external=destino.id_external,
        departure=departure_dt + timedelta(hours=2) + timedelta(minutes=10),
        arrival=departure_dt + timedelta(hours=2),
        duracao=timedelta(hours=2),
        tempo_embarque=timedelta(minutes=10),
    )
    baker.make(
        "rodoviaria.Rotina",
        rota=rota,
        datetime_ida=departure_dt,
    )
    baker.make(
        "rodoviaria.Rotina",
        rota=rota,
        datetime_ida=to_default_tz(datetime.now() + timedelta(days=30)),
    )
    return rota


@pytest.fixture
def rota_praxio(praxio_company, buser_rotas):
    praxio_provider_data = [
        {
            "IDViagem": 1592,
            "DataPartida": "2021-06-05T02:30:00",
            "Plataforma": "",
            "Localidade": {
                "IDLocalidade": 176,
                "Descricao": "SAUIPE (BA)",
                "Sigla": "SAU",
                "Uf": "BA",
                "IdCidade": 0,
                "Codigo": 0,
            },
        },
        {
            "IDViagem": 1592,
            "DataPartida": "2021-06-05T04:30:00",
            "Plataforma": "D",
            "Localidade": {
                "IDLocalidade": 13,
                "Descricao": "FEIRA DE SANTANA (BA)",
                "Sigla": "FEI",
                "Uf": "BA",
                "IdCidade": 2910800,
                "Codigo": 4941,
            },
        },
        {
            "IDViagem": 1592,
            "DataPartida": "2021-06-05T05:50:00",
            "Plataforma": "",
            "Localidade": {
                "IDLocalidade": 193,
                "Descricao": "CRUZ DAS ALMAS (BA)",
                "Sigla": "CRU",
                "Uf": "BA",
                "IdCidade": 2909802,
                "Codigo": 4825,
            },
        },
        {
            "IDViagem": 1592,
            "DataPartida": "2021-06-05T06:40:00",
            "Plataforma": "",
            "Localidade": {
                "IDLocalidade": 177,
                "Descricao": "SANTO ANTONIO DE JESUS (BA)",
                "Sigla": "SAN",
                "Uf": "BA",
                "IdCidade": 2928703,
                "Codigo": 4819,
            },
        },
    ]

    rota = baker.make(
        "rodoviaria.Rota",
        provider_data=json.dumps(praxio_provider_data),
        id_hash="206320630bac9d997fd3e6512d618615ee7b7889",
        id_external="12",
        id_internal=buser_rotas.ida.rota.id,
        company=praxio_company,
    )
    return rota


@pytest.fixture
def alterar_grupo_request(vexado_company, vexado_login):
    return {
        "company_id": vexado_company.company_internal_id,
        "grupos_classe_ids_antigos": [1165],
        "grupo": {
            "data_partida": "2022-04-28",
            "hora_saida": "16:00",
            "veiculo_placa": "LMB2022",
            "veiculo_internal_id": 10,
            "grupo_id": 459,
            "classes": [
                {
                    "grupo_classe_id": 1165,
                    "tipo": "leito cama individual",
                    "capacidade": 12,
                }
            ],
        },
        "passageiros": [
            {
                "trechoclasse_id": 1703,
                "valor_por_buseiro": 51.9,
                "id_origem": 11,
                "id_destino": 1,
                "travels": [
                    {
                        "travel_id": 535,
                        "valor_por_buseiro": 45.9,
                        "buseiros": [
                            {
                                "id": 37,
                                "name": "Passageiro Um",
                                "cpf": "92213629064",
                                "rg_number": "*********",
                                "phone": "12912122121",
                            },
                            {
                                "id": 39,
                                "name": "Passageiro Tres",
                                "cpf": "",
                                "rg_number": "*********",
                                "phone": "12912122121",
                            },
                            {
                                "id": 41,
                                "name": "Passageiro Cinco",
                                "cpf": "",
                                "rg_number": "XM112455",
                                "phone": "12912122121",
                            },
                        ],
                    }
                ],
            },
            {
                "trechoclasse_id": 8398,
                "valor_por_buseiro": 51.9,
                "id_origem": 12,
                "id_destino": 2,
                "travels": [
                    {
                        "travel_id": 542,
                        "valor_por_buseiro": 45.9,
                        "buseiros": [
                            {
                                "id": 1423,
                                "name": "Passageiro Quatro",
                                "cpf": "92213629064",
                                "rg_number": "*********",
                                "phone": "12912122121",
                            }
                        ],
                    }
                ],
            },
        ],
    }


@pytest.fixture
def mock_buscar_todos_servicos_totalbus_response(rota_totalbus):
    numservico = rota_totalbus.id_external
    first_local = rota_totalbus.first_checkpoint().local
    last_local = rota_totalbus.last_checkpoint().local
    origem_external_id = int(first_local.id_external)
    destino_external_id = int(last_local.id_external)
    origem_nickname = first_local.nickname
    destino_nickname = last_local.nickname
    return SimpleNamespace(
        cleaned=[
            {
                "numservico": numservico,
                "horaServico": "06:10:00",
                "origem": {"id": origem_external_id, "descripcion": origem_nickname},
                "destino": {"id": destino_external_id, "descripcion": destino_nickname},
            }
        ]
    )


@pytest.fixture
def totalbus_login_mock():
    empresa = baker.make("rodoviaria.Company", company_internal_id=18, modelo_venda="marketplace")
    totalbus_login = baker.make(
        "rodoviaria.TotalbusLogin",
        company=empresa,
        user="admin",
        password="senha",
        tenant_id="ffffffff-ffff-ffff-ffff-ffffffffffff",
        id_forma_pagamento=2,
        forma_pagamento="BUSER",
        validar_multa=False,
    )
    yield SimpleNamespace(totalbus_login=totalbus_login, empresa=empresa)
    totalbus_login.delete()
    empresa.delete()


@pytest.fixture
def empresa_totalbus_repetida():
    empresa = baker.make("rodoviaria.Company", company_internal_id=79, modelo_venda="marketplace")
    yield SimpleNamespace(empresa_repetida=empresa)
    empresa.delete()


@pytest.fixture
def eulabs_integracao():
    record = baker.make("rodoviaria.Integracao", name="eulabs")
    yield record
    record.delete()


@pytest.fixture
def eulabs_company(eulabs_integracao, clean_orchestrator_pool):
    record = baker.make(
        "rodoviaria.Company",
        company_internal_id=101,
        company_external_id=88126,
        integracao_id=eulabs_integracao.id,
        url_base="https://prod-url-eucatur.com.br",
        name="Eucatur",
        features=["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"],
    )
    yield record
    record.delete()


@pytest.fixture
def eulabs_login(eulabs_company):
    record = baker.make(
        "rodoviaria.EulabsLogin",
        company=eulabs_company,
        api_id="1234",
        api_key="147d2414-5902-1a81-d1e8-d0ccu7098189",
    )
    yield record
    record.delete()


@pytest.fixture
def eulabs_locais(eulabs_cidades, buser_locais):
    origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="1",
        cidade=eulabs_cidades.cidade_goiania,
        local_embarque_internal_id=buser_locais.local_go_terminal.id,
        nickname="Terminal Rodoviario de Goiania",
    )
    destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="2",
        cidade=eulabs_cidades.cidade_inhumas,
        local_embarque_internal_id=buser_locais.local_inh_terminal.id,
        nickname="Terminal Rodoviário de Inhumas",
    )
    yield SimpleNamespace(origem=origem, destino=destino)
    origem.delete()
    destino.delete()


@pytest.fixture
def mock_rotinas(rota_mock):
    r1 = baker.make(
        "rodoviaria.rotina",
        pk=1,
        rota_id=rota_mock.pk,
        datetime_ida=to_default_tz(datetime(2022, 7, 20, 0)),
    )
    r2 = baker.make(
        "rodoviaria.rotina",
        pk=2,
        rota_id=rota_mock.pk,
        datetime_ida=to_default_tz(datetime(2022, 7, 20, 1, 15)),
    )
    r3 = baker.make(
        "rodoviaria.rotina",
        pk=3,
        rota_id=rota_mock.pk,
        datetime_ida=to_default_tz(datetime(2022, 7, 22, 1)),
    )
    yield SimpleNamespace(rotinas=(r1, r2, r3))
    r1.delete()
    r2.delete()
    r3.delete()


@pytest.fixture
def mock_locais_itinerario(mock_itinerario):
    [origem, parada, destino] = mock_itinerario.checkpoints
    local_origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=origem.local.external_local_id,
        local_embarque_internal_id=1,
    )
    local_parada = baker.make(
        "rodoviaria.LocaLEmbarque",
        id_external=parada.local.external_local_id,
        local_embarque_internal_id=2,
    )
    local_destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=destino.local.external_local_id,
        local_embarque_internal_id=3,
    )
    yield [local_origem, local_parada, local_destino]


@pytest.fixture
def mock_trechos_vendidos(rota_mock, mock_locais_itinerario):
    rota = rota_mock

    [local_origem, local_parada, local_destino] = mock_locais_itinerario

    t1 = baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota,
        origem=local_origem,
        destino=local_destino,
        classe="CONVENCIONAL",
        capacidade_classe=23,
        preco=D("12.99"),
        id_internal=1,
        tipo_assento__tipo_assento_buser_preferencial="convencional",
    )
    t2 = baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota,
        origem=local_origem,
        destino=local_parada,
        classe="CONVENCIONAL",
        capacidade_classe=24,
        preco=D("14.99"),
        id_internal=2,
        tipo_assento__tipo_assento_buser_preferencial="convencional",
    )
    t3 = baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota,
        origem=local_parada,
        destino=local_destino,
        classe="CONVENCIONAL",
        capacidade_classe=25,
        preco=D("24.99"),
        id_internal=3,
        tipo_assento__tipo_assento_buser_preferencial="convencional",
    )

    yield SimpleNamespace(trechos=(t1, t2, t3))
    t1.delete()
    t2.delete()
    t3.delete()


@pytest.fixture
def mock_rotinatrechos(mock_trechos_vendidos, mock_rotinas, mock_itinerario):
    trechos = mock_trechos_vendidos.trechos
    rotinas = mock_rotinas.rotinas

    rt1 = baker.make(
        "rodoviaria.RotinaTrechoVendido",
        trecho_vendido=trechos[0],
        rotina=rotinas[0],
        datetime_ida_trecho_vendido=rotinas[0].datetime_ida,
    )
    rt2 = baker.make(
        "rodoviaria.RotinaTrechoVendido",
        trecho_vendido=trechos[1],
        rotina=rotinas[1],
        datetime_ida_trecho_vendido=rotinas[1].datetime_ida,
    )
    rt3 = baker.make(
        "rodoviaria.RotinaTrechoVendido",
        trecho_vendido=trechos[2],
        rotina=rotinas[2],
        datetime_ida_trecho_vendido=rotinas[2].datetime_ida
        + timedelta(seconds=mock_itinerario.map["11"].first_cp_shift.total_seconds()),
    )
    rt4 = baker.make(
        "rodoviaria.RotinaTrechoVendido",
        trecho_vendido=trechos[1],
        rotina=rotinas[2],
        datetime_ida_trecho_vendido=rotinas[1].datetime_ida,
    )
    yield SimpleNamespace(rt=(rt1, rt2, rt3, rt4))


@pytest.fixture
def mock_task_pending_fetch_trechos_vendidos(mock_grupo_trecho_classe):
    task_status = baker.make(
        TaskStatus,
        rota_id=mock_grupo_trecho_classe.rota.id,
        company_id=mock_grupo_trecho_classe.rota.company_id,
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
        task_id="*********",
        status=TaskStatus.Status.PENDING,
    )
    yield task_status
    task_status.delete()


@pytest.fixture
def mock_task_pending_fetch_rotinas(mock_grupo_trecho_classe):
    task_status = baker.make(
        TaskStatus,
        rota_id=mock_grupo_trecho_classe.rota.id,
        company_id=mock_grupo_trecho_classe.rota.company_id,
        task_name=TaskStatus.Name.FETCH_ROTINAS,
        task_id="12481241923123",
        status=TaskStatus.Status.PENDING,
    )
    yield task_status
    task_status.delete()


@pytest.fixture
def mock_task_pending_descobrir_rotas(mock_grupo_trecho_classe):
    task_status = baker.make(
        TaskStatus,
        company_id=mock_grupo_trecho_classe.rota.company_id,
        task_name=TaskStatus.Name.DESCOBRIR_ROTAS,
        task_id="*********",
        status=TaskStatus.Status.PENDING,
    )
    yield task_status
    task_status.delete()


@pytest.fixture
def smartbus_integracao():
    record = baker.make(Integracao, name="smartbus")
    yield record
    record.delete()


@pytest.fixture
def smartbus_company(smartbus_integracao, clean_orchestrator_pool):
    record = baker.make(
        Company,
        company_internal_id=115,
        integracao_id=smartbus_integracao.id,
        name="Viacao Busao-Inteligente",
        features=["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"],
    )
    yield record
    record.delete()


@pytest.fixture
def smartbus_login(smartbus_company):
    record = baker.make(
        SmartbusLogin,
        company=smartbus_company,
        cliente="main",
        username="buser",
        password="k:wuq|)C#1",
    )
    yield record
    record.delete()


@pytest.fixture
def ti_sistemas_integracao():
    record = baker.make(Integracao, name="ti_sistemas")
    yield record


@pytest.fixture
def ti_sistemas_company(ti_sistemas_integracao, clean_orchestrator_pool):
    record = baker.make(
        Company,
        company_internal_id=116105,
        company_external_id=141,
        integracao_id=ti_sistemas_integracao.id,
        url_base=TiSistemasLogin.DEFAULT_URL_BASE,
        name="Viacao Vrum Vrum",
        features=["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"],
    )
    yield record


@pytest.fixture
def ti_sistemas_login(ti_sistemas_company):
    record = baker.make(TiSistemasLogin, company=ti_sistemas_company, auth_key="auth_key_teste")
    yield record


@pytest.fixture
def ti_sistemas_cidades(cidades_internal, ti_sistemas_company):
    cidade_goiania = baker.make(
        "rodoviaria.Cidade",
        id_external="1",
        cidade_internal=cidades_internal.cidade_goiania,
        company=ti_sistemas_company,
        name="Goiânia",
        timezone="America/Sao_Paulo",
    )
    cidade_inhumas = baker.make(
        "rodoviaria.Cidade",
        id_external="2",
        cidade_internal=cidades_internal.cidade_inhumas,
        company=ti_sistemas_company,
        name="Inhumas",
        timezone="America/Bahia",
    )
    yield SimpleNamespace(cidade_goiania=cidade_goiania, cidade_inhumas=cidade_inhumas)
    cidade_goiania.delete()
    cidade_inhumas.delete()


@pytest.fixture
def ti_sistemas_locais(ti_sistemas_cidades, buser_locais):
    origem = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="1",
        cidade=ti_sistemas_cidades.cidade_goiania,
        local_embarque_internal_id=buser_locais.local_go_terminal.id,
        nickname="Terminal Rodoviario de Goiania",
    )
    destino = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="2",
        cidade=ti_sistemas_cidades.cidade_inhumas,
        local_embarque_internal_id=buser_locais.local_inh_terminal.id,
        nickname="Terminal Rodoviário de Inhumas",
    )
    yield SimpleNamespace(origem=origem, destino=destino)
    origem.delete()
    destino.delete()


@pytest.fixture
def ti_sistemas_grupos(ti_sistemas_company, ti_sistemas_grupos_mockado):
    def ti_sistemas_grupo(grupo):
        record = baker.make(
            "rodoviaria.Grupo",
            grupo_internal_id=grupo.id,
            company_integracao_id=ti_sistemas_company.id,
            linha="GOIÂNIA (GO) X INHUMAS (GO)",
            datetime_ida=to_default_tz(grupo.datetime_ida),
        )
        return record

    ida = ti_sistemas_grupo(ti_sistemas_grupos_mockado.ida.grupo)
    volta = ti_sistemas_grupo(ti_sistemas_grupos_mockado.volta.grupo)
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def ti_sistemas_grupoclasses(ti_sistemas_grupos, ti_sistemas_locais, ti_sistemas_grupos_mockado):
    def ti_sistemas_grupoclasse(grupoclasse_internal_id, grupo, tipo_assento_internal):
        return baker.make(
            "rodoviaria.GrupoClasse",
            grupoclasse_internal_id=grupoclasse_internal_id,
            grupo=grupo,
            tipo_assento_internal=tipo_assento_internal,
        )

    buser_django_grupo_classe = ti_sistemas_grupos_mockado.ida.trechoclasse.grupo_classe
    ida = ti_sistemas_grupoclasse(
        buser_django_grupo_classe.id,
        ti_sistemas_grupos.ida,
        buser_django_grupo_classe.tipo_assento,
    )
    volta = ti_sistemas_grupoclasse(
        buser_django_grupo_classe.id,
        ti_sistemas_grupos.volta,
        buser_django_grupo_classe.tipo_assento,
    )
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def ti_sistemas_trechoclasses(
    ti_sistemas_grupos, ti_sistemas_grupoclasses, ti_sistemas_locais, ti_sistemas_grupos_mockado
):
    def ti_sistemas_trechoclasse(internal_trechoclasse, grupo, grupo_classe, origem, destino, preco_rodoviaria):
        return baker.make(
            "rodoviaria.TrechoClasse",
            trechoclasse_internal_id=internal_trechoclasse.id,
            provider_data=json.dumps(mock_corridas.corridas[0]),
            grupo=grupo,
            grupo_classe=grupo_classe,
            external_id="1020",
            origem=origem,
            destino=destino,
            datetime_ida=to_default_tz(internal_trechoclasse.datetime_ida),
            preco_rodoviaria=preco_rodoviaria,
        )

    ida = ti_sistemas_trechoclasse(
        ti_sistemas_grupos_mockado.ida.trechoclasse,
        ti_sistemas_grupos.ida,
        ti_sistemas_grupoclasses.ida,
        ti_sistemas_locais.origem,
        ti_sistemas_locais.destino,
        ti_sistemas_grupos_mockado.ida.trechoclasse.preco_rodoviaria,
    )
    volta = ti_sistemas_trechoclasse(
        ti_sistemas_grupos_mockado.volta.trechoclasse,
        ti_sistemas_grupos.volta,
        ti_sistemas_grupoclasses.ida,
        ti_sistemas_locais.destino,
        ti_sistemas_locais.origem,
        ti_sistemas_grupos_mockado.volta.trechoclasse.preco_rodoviaria,
    )
    yield SimpleNamespace(ida=ida, volta=volta)
    ida.delete()
    volta.delete()


@pytest.fixture
def mock_rotinas_fetch_trechos(mock_grupo_trecho_classe):
    for x in range(1, 8):
        baker.make(
            "rodoviaria.Rotina",
            rota=mock_grupo_trecho_classe.rota,
            datetime_ida=to_default_tz(today_midnight() + timedelta(days=x, hours=21, minutes=30)),
        )


@pytest.fixture
def mock_ti_sistemas_comprar(ti_sistemas_mock_comprar, ti_sistemas_mock_bloquear_duas_poltronas):
    pass


@pytest.fixture
def mock_ti_sistemas_cancelar(ti_sistemas_mock_cancelar):
    pass


@pytest.fixture
def clean_cache_cached_buscar_corridas():
    caches["redis"].clear()
    yield
    caches["redis"].clear()


@pytest.fixture
def mock_dispara_atualizacao_trecho():
    with mock.patch.object(CompraRodoviariaSVC, "_dispara_atualizacao_trecho") as mock_dispara_atualizacao_trecho:
        yield
    mock_dispara_atualizacao_trecho.assert_called()

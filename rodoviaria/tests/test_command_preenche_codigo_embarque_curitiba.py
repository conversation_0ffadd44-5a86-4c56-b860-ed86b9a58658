from datetime import timedelta

from django.core.management import call_command
from django.utils import timezone
from model_bakery import baker

from rodoviaria.models.core import (
    Integracao,
    Passagem,
)


def test_busca_precos_trechos_vendidos_sem_nenhum_trecho_vendido_pendente(mocker):
    praxio = baker.make(Integracao, name=Integracao.API.PRAXIO)
    passagem_curitiba_praxio_com_codigo = baker.make(
        Passagem,
        company_integracao__integracao=praxio,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao__origem__nickname="Curitiba - PR",
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(days=1),
        tipo_taxa_embarque=None,
        numero_bilhete_embarque=None,
        provider_data={"ListaPassagem": {"CodigoBarrasCuritiba": "123456"}},
    )
    passagem_curitiba_praxio_sem_codigo = baker.make(
        Passage<PERSON>,
        company_integracao__integracao=praxio,
        status=Passagem.Status.CONFIRMADA,
        trechoclasse_integracao__origem__nickname="Curitiba - PR",
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(days=1),
        tipo_taxa_embarque=None,
        numero_bilhete_embarque=None,
        provider_data={"ListaPassagem": {"CodigoBarrasCuritiba": ""}},
    )
    call_command("preenche_codigo_embarque_curitiba")
    passagem_curitiba_praxio_com_codigo.refresh_from_db()
    assert passagem_curitiba_praxio_com_codigo.numero_bilhete_embarque == "123456"
    assert passagem_curitiba_praxio_com_codigo.tipo_taxa_embarque == Passagem.TipoTaxaEmbarque.QRCODE
    passagem_curitiba_praxio_sem_codigo.refresh_from_db()
    assert passagem_curitiba_praxio_sem_codigo.numero_bilhete_embarque is None
    assert passagem_curitiba_praxio_sem_codigo.tipo_taxa_embarque is None

from celery import group
from model_bakery import baker

from rodoviaria.service import escalar_veiculo_cancelado_svc, veiculos_svc


def test_build_trocar_onibus_tasks_com_veiculo():
    company_id = 48392
    company = baker.make("rodoviaria.Company", company_internal_id=company_id)
    veiculo = baker.make("rodoviaria.Veiculo", company=company, id_internal=999999, id_external=1943)
    grupo_classe = baker.make("rodoviaria.GrupoClasse")
    grupo_classe_id_map = [
        baker.make(
            "rodoviaria.VexadoGrupoClasse",
            grupo_classe_external_id=123,
            grupo_classe=grupo_classe,
        )
    ]
    tasks = escalar_veiculo_cancelado_svc.escalar_veiculo_cancelado_tasks(company_id, grupo_classe_id_map)
    assert tasks == group(
        [
            veiculos_svc.trocar_onibus_viagem_task.si(
                123, veiculo.id_external, veiculo.id, 1, company_id, grupo_classe.id
            )
        ]
    )


def test_build_trocar_onibus_tasks_sem_veiculo():
    company_id = 8420
    grupo_classe_id_map = {123: 456}
    tasks = escalar_veiculo_cancelado_svc.escalar_veiculo_cancelado_tasks(company_id, grupo_classe_id_map)
    assert tasks == group([])

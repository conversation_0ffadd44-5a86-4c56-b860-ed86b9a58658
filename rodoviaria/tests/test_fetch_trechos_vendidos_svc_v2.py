from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from unittest import mock

import pytest
import time_machine
from celery.exceptions import Retry
from celery.result import AsyncResult
from django.db import connections
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.circuit_breaker import MyCircuitBreakerError
from commons.dateutils import to_default_tz, to_tz, today_midnight
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import (
    Company,
    RotinaTrechoVendido,
    TaskStatus,
    TipoAssento,
    TrechoVendido,
)
from rodoviaria.service import fetch_trechos_vendidos_svc_v2
from rodoviaria.service.create_local_embarque_cidade_svc import local_to_json
from rodoviaria.service.exceptions import RodoviariaTooManyRequestsError


def test_async_fetch_trechos_vendidos(
    mock_itinerario,
    totalbus_api,
    mock_grupo_trecho_classe,
    mock_rotinas_fetch_trechos,
    mocked_map_cidades_destinos,
):
    rota = mock_grupo_trecho_classe.rota
    task_id = "123"
    with (
        mock.patch.object(
            OrchestrateRodoviaria,
            "map_cidades_destinos",
            return_value=mocked_map_cidades_destinos,
        ),
        mock.patch(
            "rodoviaria.service.fetch_trechos_vendidos_svc_v2.create_cidades_and_locais_from_itinerario",
            return_value={
                str(c.local.external_local_id): c.local.external_local_id + 1 for c in mock_itinerario.checkpoints
            },
        ),
        mock.patch("rodoviaria.service.fetch_trechos_vendidos_svc_v2.chord.from_dict") as mock_chord_from_dict,
    ):
        mock_chord_from_dict.return_value.return_value.parent.id = task_id
        mock_chord_from_dict.return_value.return_value.mock_add_spec(AsyncResult)
        fetch_trechos_vendidos_svc_v2.async_fetch_trechos_vendidos(
            {},
            mock_itinerario.checkpoints,
            mock_grupo_trecho_classe.rota,
            mock_grupo_trecho_classe.company,
            "America/Manaus",
            return_task_object=False,
        )
    assert (
        mock_chord_from_dict.call_args.args[0]["kwargs"]["header"][0]["task"]
        == "rodoviaria.service.fetch_trechos_vendidos_svc_v2.fetch_trecho_vendido_task"
    )
    assert (
        mock_chord_from_dict.call_args.args[0]["kwargs"]["body"]["task"]
        == "rodoviaria.service.fetch_trechos_vendidos_svc_v2.fetch_trechos_vendidos_finisher_on_success"
    )
    assert mock_chord_from_dict.call_args.args[0]["kwargs"]["body"]["args"] == [rota.id]
    mock_chord_from_dict.return_value.assert_called_with()
    task_status = TaskStatus.objects.get(
        rota_id=mock_grupo_trecho_classe.rota.id,
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
    )
    assert task_status.task_id == task_id


def test_async_fetch_trechos_vendidos_skip_trechos(
    mock_itinerario,
    totalbus_api,
    mock_grupo_trecho_classe,
    mock_rotinas_fetch_trechos,
    mocked_map_cidades_destinos,
):
    rota = mock_grupo_trecho_classe.rota
    task_id = "123"
    with (
        mock.patch.object(
            OrchestrateRodoviaria,
            "map_cidades_destinos",
            return_value=mocked_map_cidades_destinos,
        ),
        mock.patch(
            "rodoviaria.service.fetch_trechos_vendidos_svc_v2.create_cidades_and_locais_from_itinerario",
            return_value={
                str(c.local.external_local_id): c.local.external_local_id + 1 for c in mock_itinerario.checkpoints
            },
        ),
        mock.patch("rodoviaria.service.fetch_trechos_vendidos_svc_v2.chord.from_dict") as mock_chord_from_dict,
    ):
        mock_chord_from_dict.return_value.return_value.parent.id = task_id
        mock_chord_from_dict.return_value.return_value.mock_add_spec(AsyncResult)
        fetch_trechos_vendidos_svc_v2.async_fetch_trechos_vendidos(
            {},
            mock_itinerario.checkpoints,
            mock_grupo_trecho_classe.rota,
            mock_grupo_trecho_classe.company,
            "America/Manaus",
            return_task_object=False,
        )
    assert (
        mock_chord_from_dict.call_args.args[0]["kwargs"]["header"][0]["task"]
        == "rodoviaria.service.fetch_trechos_vendidos_svc_v2.fetch_trecho_vendido_task"
    )
    assert (
        mock_chord_from_dict.call_args.args[0]["kwargs"]["body"]["task"]
        == "rodoviaria.service.fetch_trechos_vendidos_svc_v2.fetch_trechos_vendidos_finisher_on_success"
    )
    assert mock_chord_from_dict.call_args.args[0]["kwargs"]["body"]["args"] == [rota.id]
    mock_chord_from_dict.return_value.assert_called_with()
    task_status = TaskStatus.objects.get(
        rota_id=mock_grupo_trecho_classe.rota.id,
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
    )
    assert task_status.task_id == task_id


def test_async_fetch_trechos_vendidos_return_task_object(
    mock_itinerario,
    totalbus_api,
    mock_grupo_trecho_classe,
    mock_rotinas_fetch_trechos,
    mocked_map_cidades_destinos,
):
    rota = mock_grupo_trecho_classe.rota
    with (
        mock.patch("rodoviaria.service.fetch_trechos_vendidos_svc_v2.group") as mock_group,
        mock.patch.object(OrchestrateRodoviaria, "map_cidades_destinos", return_value={}),
        mock.patch(
            "rodoviaria.service.fetch_trechos_vendidos_svc_v2.create_cidades_and_locais_from_itinerario",
            return_value={
                str(c.local.external_local_id): c.local.external_local_id + 1 for c in mock_itinerario.checkpoints
            },
        ),
        mock.patch("rodoviaria.service.fetch_trechos_vendidos_svc_v2.chain") as mock_chain,
        mock.patch(
            "rodoviaria.service.fetch_trechos_vendidos_svc_v2._get_map_origem_destinos",
            return_value={},
        ),
    ):
        fetch_trechos_vendidos_svc_v2.async_fetch_trechos_vendidos(
            {},
            mock_itinerario.checkpoints,
            mock_grupo_trecho_classe.rota,
            mock_grupo_trecho_classe.company,
            "America/Manaus",
            return_task_object=True,
        )
    assert (
        mock_group.call_args.args[0].task
        == "rodoviaria.service.fetch_trechos_vendidos_svc_v2.fetch_trecho_vendido_task"
    )
    assert mock_chain.call_args[0][0] == mock_group()
    assert (
        str(mock_chain.call_args[0][1])
        == f"rodoviaria.service.fetch_trechos_vendidos_svc_v2.fetch_trechos_vendidos_finisher_on_success({rota.id})"
    )


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_fetch_trechos_vendidos_finisher_on_success(rota_mock):
    task = baker.make(
        TaskStatus,
        rota=rota_mock,
        company=rota_mock.company,
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
        status=TaskStatus.Status.PENDING,
    )
    mocked_now = datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo"))
    tv_novo = baker.make(TrechoVendido, rota=rota_mock, ativo=True)
    tv_novo.updated_at = mocked_now - timedelta(days=1)
    tv_antigo = baker.make(TrechoVendido, rota=rota_mock, ativo=True)
    tv_antigo.updated_at = mocked_now - timedelta(days=4)
    TrechoVendido.objects.bulk_update([tv_novo, tv_antigo], ["updated_at"])
    fetch_trechos_vendidos_svc_v2.fetch_trechos_vendidos_finisher_on_success(rota_mock.id)
    tv_novo.refresh_from_db()
    tv_antigo.refresh_from_db()
    task.refresh_from_db()
    assert tv_novo.ativo is True
    assert tv_antigo.ativo is False
    assert task.status == TaskStatus.Status.SUCCESS
    assert to_default_tz(task.last_success_at) == mocked_now


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_fetch_trechos_vendidos_finisher_on_error(rota_mock, caplog):
    task = baker.make(
        TaskStatus,
        rota=rota_mock,
        company=rota_mock.company,
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
        status=TaskStatus.Status.PENDING,
    )
    mocked_now = datetime.now(tz=ZoneInfo("America/Sao_Paulo"))
    tv_novo = baker.make(TrechoVendido, rota=rota_mock, ativo=True)
    tv_novo.updated_at = mocked_now - timedelta(days=1)
    tv_antigo = baker.make(TrechoVendido, rota=rota_mock, ativo=True)
    tv_antigo.updated_at = mocked_now - timedelta(days=4)
    TrechoVendido.objects.bulk_update([tv_novo, tv_antigo], ["updated_at"])
    fetch_trechos_vendidos_svc_v2.fetch_trechos_vendidos_finisher_on_error(None, ConnectionError, None, rota_mock.id)
    tv_novo.refresh_from_db()
    tv_antigo.refresh_from_db()
    task.refresh_from_db()
    assert tv_novo.ativo is True
    assert tv_antigo.ativo is False
    assert task.status == TaskStatus.Status.FAILURE
    assert caplog.records[-1].message == f"Erro ao finalizar fetch trechos vendidos da rota {rota_mock.id}"
    assert caplog.records[-1].levelname == "ERROR"
    assert to_default_tz(task.last_success_at) == mocked_now


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_fetch_trechos_vendidos_finisher_on_success_inativa_rota_sem_trecho_vendido(
    rota_mock,
):
    task = baker.make(
        TaskStatus,
        rota=rota_mock,
        company=rota_mock.company,
        task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS,
        status=TaskStatus.Status.PENDING,
    )
    mocked_now = datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo"))
    tv_inativo = baker.make(TrechoVendido, rota=rota_mock, ativo=False)
    tv_inativo.updated_at = mocked_now - timedelta(days=1)
    TrechoVendido.objects.bulk_update([tv_inativo], ["updated_at"])
    fetch_trechos_vendidos_svc_v2.fetch_trechos_vendidos_finisher_on_success(rota_mock.id)
    rota_mock.refresh_from_db()
    assert rota_mock.ativo is False
    task.refresh_from_db()
    assert task.status == TaskStatus.Status.SUCCESS


def test_get_datetime_rotinas_to_find_trechos(rota_mock):
    baker.make("rodoviaria.Rotina", rota=rota_mock, datetime_ida=to_tz(today_midnight(), "UTC"))
    r1 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=1, seconds=1), "UTC"),
    )
    r2 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=3), "UTC"),
    )
    r3 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=6), "UTC"),
    )
    r4 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=7), "UTC"),
    )
    r5 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=12), "UTC"),
    )
    rotinas = [r1, r2, r3, r4]
    expected_rotinas_list = [(r.id, to_tz(r.datetime_ida, "America/Manaus")) for r in rotinas]
    rotinas_list = fetch_trechos_vendidos_svc_v2._get_datetime_rotinas_to_find_trechos(rota_mock.id, "America/Manaus")
    assert expected_rotinas_list == rotinas_list
    rotinas = [r1, r2, r3, r4, r5]
    expected_rotinas_list = [(r.id, to_tz(r.datetime_ida, "America/Manaus")) for r in rotinas]
    rotinas_list = fetch_trechos_vendidos_svc_v2._get_datetime_rotinas_to_find_trechos(
        rota_mock.id, "America/Manaus", fetch_further=True
    )
    assert expected_rotinas_list == rotinas_list


def test_get_datetime_rotinas_to_find_trechos_rota_apenas_com_rotinas_no_futuro(rota_mock):
    baker.make("rodoviaria.Rotina", rota=rota_mock, datetime_ida=to_tz(today_midnight(), "UTC"))
    r1 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=8), "UTC"),
    )
    r2 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=9), "UTC"),
    )
    r3 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=10), "UTC"),
    )
    r4 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=17), "UTC"),
    )
    r5 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=18), "UTC"),
    )
    rotinas = [r1, r2, r3]
    expected_rotinas_list = [(r.id, to_tz(r.datetime_ida, "America/Manaus")) for r in rotinas]
    rotinas_list = fetch_trechos_vendidos_svc_v2._get_datetime_rotinas_to_find_trechos(rota_mock.id, "America/Manaus")
    assert expected_rotinas_list == rotinas_list
    rotinas = [r1, r2, r3, r4, r5]
    expected_rotinas_list = [(r.id, to_tz(r.datetime_ida, "America/Manaus")) for r in rotinas]
    rotinas_list = fetch_trechos_vendidos_svc_v2._get_datetime_rotinas_to_find_trechos(
        rota_mock.id, "America/Manaus", fetch_further=True
    )
    assert expected_rotinas_list == rotinas_list


def test_get_datetime_rotinas_to_find_trechos_rota_apenas_com_rotinas_no_futuro_em_horario_diferente(rota_mock):
    baker.make("rodoviaria.Rotina", rota=rota_mock, datetime_ida=to_tz(today_midnight(), "UTC"))
    r1 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=8), "UTC"),
    )
    r2 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=17, minutes=30), "UTC"),
    )
    r3 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=18, minutes=30), "UTC"),
    )
    r4 = baker.make(
        "rodoviaria.Rotina",
        rota=rota_mock,
        datetime_ida=to_tz(today_midnight() + timedelta(days=30, minutes=30), "UTC"),
    )
    rotinas = [r1, r2, r3]
    expected_rotinas_list = [(r.id, to_tz(r.datetime_ida, "America/Manaus")) for r in rotinas]
    rotinas_list = fetch_trechos_vendidos_svc_v2._get_datetime_rotinas_to_find_trechos(rota_mock.id, "America/Manaus")
    assert expected_rotinas_list == rotinas_list
    rotinas = [r1, r2, r3, r4]
    expected_rotinas_list = [(r.id, to_tz(r.datetime_ida, "America/Manaus")) for r in rotinas]
    rotinas_list = fetch_trechos_vendidos_svc_v2._get_datetime_rotinas_to_find_trechos(
        rota_mock.id, "America/Manaus", fetch_further=True
    )
    assert expected_rotinas_list == rotinas_list


def test_get_datetime_sem_rotinas(rota_mock):
    rotina_map = fetch_trechos_vendidos_svc_v2._get_datetime_rotinas_to_find_trechos(rota_mock.id, "America/Manaus")
    assert [] == rotina_map


def test_get_map_origem_destinos_todos_de_uma_vez(mock_itinerario):
    company = baker.make(Company)
    [origem, parada, destino] = mock_itinerario.checkpoints
    with mock.patch.object(OrchestrateRodoviaria, "map_cidades_destinos") as mock_map_cidades_destinos:
        mock_map_cidades_destinos.return_value = {
            origem.local.external_local_id: [],
            parada.local.external_local_id: [destino.local.external_local_id],
        }
        map_cidades_destinos = fetch_trechos_vendidos_svc_v2._get_map_origem_destinos(
            company.company_internal_id, company.modelo_venda
        )
    assert map_cidades_destinos == {
        origem.local.external_local_id: [],
        parada.local.external_local_id: [destino.local.external_local_id],
    }


@pytest.fixture
def task_form(rota_mock, mock_itinerario, mock_rotinas, mock_locais_itinerario):
    [local_origem, _, local_destino] = mock_locais_itinerario
    checkpoints_map = {}
    for i in mock_itinerario.map:
        checkpoints_map[i] = local_to_json(mock_itinerario.map[i])
        checkpoints_map[i]["first_cp_shift"] = mock_itinerario.map[i].first_cp_shift.total_seconds()
    expected_datetime_partidas_map = {
        to_tz(r.datetime_ida, "America/Sao_Paulo").strftime("%Y-%m-%dT%H:%M:%S"): r.id for r in mock_rotinas.rotinas
    }
    [origem, _, destino] = mock_itinerario.checkpoints
    return fetch_trechos_vendidos_svc_v2.FetchTrechoVendidoTaskForm(
        company_internal_id=rota_mock.company.company_internal_id,
        modelo_venda=rota_mock.company.modelo_venda,
        origem_local_id=origem.local.external_local_id,
        destino_local_id=destino.local.external_local_id,
        data_str="2024-01-10",
        timezone_origem="America/Sao_Paulo",
        timezone_destino="America/Sao_Paulo",
        rota_id=rota_mock.id,
        expected_datetime_partidas_map=expected_datetime_partidas_map,
        origem_id=local_origem.id,
        destino_id=local_destino.id,
    )


def test_find_trecho_vendido_circuit_breaker(task_form):
    with mock.patch.object(OrchestrateRodoviaria, "find_trecho_vendido") as mock_find_trecho_vendido:
        fetch_trechos_vendidos_svc_v2.find_trecho_vendido_circuit_breaker(task_form)
    mock_find_trecho_vendido.assert_called_once_with(
        task_form.origem_local_id,
        task_form.destino_local_id,
        task_form.data_str,
        task_form.timezone_origem,
        task_form.timezone_destino,
        task_form.rota_id,
        list(task_form.expected_datetime_partidas_map.keys()),
        fetch_trechos_vendidos_svc_v2.task_logger,
    )


def test_fetch_trecho_vendido_task(rota_mock, task_form):
    rota_mock.company = baker.make("rodoviaria.Company", company_internal_id=23212)
    with (
        mock.patch(
            "rodoviaria.service.fetch_trechos_vendidos_svc_v2.find_trecho_vendido_circuit_breaker"
        ) as mock_find_trecho_vendido_circuit_breaker,
        mock.patch(
            "rodoviaria.service.fetch_trechos_vendidos_svc_v2._create_trechos_vendidos"
        ) as mock_create_trechos_vendidos,
    ):
        fetch_trechos_vendidos_svc_v2.fetch_trecho_vendido_task(**task_form.dict())
    mock_create_trechos_vendidos.assert_called_with(
        task_form,
        mock_find_trecho_vendido_circuit_breaker.return_value,
    )
    mock_find_trecho_vendido_circuit_breaker.assert_called_once_with(task_form)


def test_fetch_trecho_vendido_task_to_many_requests(rota_mock, task_form):
    rota_mock.company = baker.make("rodoviaria.Company", company_internal_id=23212)
    with (
        mock.patch(
            "rodoviaria.service.fetch_trechos_vendidos_svc_v2.find_trecho_vendido_circuit_breaker"
        ) as mock_find_trecho_vendido_circuit_breaker,
        pytest.raises(Retry),
    ):
        mock_find_trecho_vendido_circuit_breaker.side_effect = RodoviariaTooManyRequestsError("erro")
        fetch_trechos_vendidos_svc_v2.fetch_trecho_vendido_task(**task_form.dict())
    mock_find_trecho_vendido_circuit_breaker.assert_called_once_with(task_form)


def test_fetch_trecho_vendido_task_circuit_breaker_error(rota_mock, task_form):
    rota_mock.company = baker.make("rodoviaria.Company", company_internal_id=23212)
    with (
        mock.patch(
            "rodoviaria.service.fetch_trechos_vendidos_svc_v2.find_trecho_vendido_circuit_breaker"
        ) as mock_find_trecho_vendido_circuit_breaker,
        pytest.raises(Retry),
    ):
        mock_find_trecho_vendido_circuit_breaker.side_effect = MyCircuitBreakerError(
            "menssagem qualquer", remaining_seconds=10
        )
        fetch_trechos_vendidos_svc_v2.fetch_trecho_vendido_task(**task_form.dict())
    mock_find_trecho_vendido_circuit_breaker.assert_called_once_with(task_form)


@pytest.fixture
def trechos_vendidos_api(mock_itinerario, mock_rotinas):
    [origem, _, destino] = mock_itinerario.checkpoints
    trecho_vendido_api = {
        "external_origem_id": origem.local.external_local_id,
        "external_destino_id": destino.local.external_local_id,
        "classe": "CONVENCIONAL",
        "capacidade_classe": 5,
        "duracao": 4200,
        "preco": Decimal("142.52"),
        "distancia": 223,
        "datetime_ida": datetime.strftime(
            to_default_tz(
                mock_rotinas.rotinas[0].datetime_ida,
            ),
            "%Y-%m-%dT%H:%M:%S%z",
        ),
    }
    trecho_vendido_api_2 = {
        "external_origem_id": origem.local.external_local_id,
        "external_destino_id": destino.local.external_local_id,
        "classe": "CONVENCIONAL",
        "capacidade_classe": 7,
        "duracao": 4200,
        "preco": Decimal("142.52"),
        "distancia": 223,
        "datetime_ida": datetime.strftime(
            to_default_tz(
                mock_rotinas.rotinas[1].datetime_ida,
            ),
            "%Y-%m-%dT%H:%M:%S%z",
        ),
    }
    return [trecho_vendido_api, trecho_vendido_api_2]


def test_create_trechos_vendidos_trecho_vendido_api_repetido(
    rota_mock, task_form, trechos_vendidos_api, django_assert_num_queries
):
    # assert de sanidade
    assert TrechoVendido.objects.filter(rota_id=rota_mock.id).count() == 0
    assert RotinaTrechoVendido.objects.filter(trecho_vendido__rota_id=rota_mock.id).count() == 0

    trechos_vendidos_api[0]["classe"] = "LEITO"
    trechos_vendidos_api[1]["classe"] = "LEITO"
    # get trechos vendidos, create TrechoVendido, create RotinaTrechoVendido
    with django_assert_num_queries(5, connection=connections["rodoviaria"]):
        response = fetch_trechos_vendidos_svc_v2._create_trechos_vendidos(
            task_form, trechos_vendidos_api + trechos_vendidos_api
        )
    assert response is None
    # verifica se criou TrechoVendido
    trechos_vendidos = TrechoVendido.objects.filter(rota_id=rota_mock.id)
    assert trechos_vendidos.count() == 1
    trecho_vendido_atualizado = trechos_vendidos.first()
    assert trecho_vendido_atualizado.capacidade_classe == 7
    # verifica se criou RotinaTrechoVendido
    rotinas_tv = RotinaTrechoVendido.objects.filter(trecho_vendido__rota_id=rota_mock.id).order_by(
        "rotina__datetime_ida"
    )
    assert rotinas_tv.count() == 2
    for rotina_tv, tv_api in zip(rotinas_tv, trechos_vendidos_api):
        assert rotina_tv.rotina.datetime_ida == to_default_tz(tv_api["datetime_ida"])


def test_create_trechos_vendidos_trecho_vendido_nao_existe(
    rota_mock, task_form, trechos_vendidos_api, django_assert_num_queries
):
    # assert de sanidade
    assert TrechoVendido.objects.filter(rota_id=rota_mock.id).count() == 0
    assert RotinaTrechoVendido.objects.filter(trecho_vendido__rota_id=rota_mock.id).count() == 0

    trechos_vendidos_api[0]["classe"] = "LEITO"
    trechos_vendidos_api[1]["classe"] = "LEITO"
    # get trechos vendidos, create TrechoVendido, create RotinaTrechoVendido
    with django_assert_num_queries(5, connection=connections["rodoviaria"]):
        response = fetch_trechos_vendidos_svc_v2._create_trechos_vendidos(task_form, trechos_vendidos_api)
    assert response is None
    # verifica se criou TrechoVendido
    trechos_vendidos = TrechoVendido.objects.filter(rota_id=rota_mock.id)
    assert trechos_vendidos.count() == 1
    trecho_vendido_atualizado = trechos_vendidos.first()
    assert trecho_vendido_atualizado.capacidade_classe == 7
    # verifica se criou RotinaTrechoVendido
    rotinas_tv = RotinaTrechoVendido.objects.filter(trecho_vendido__rota_id=rota_mock.id).order_by(
        "rotina__datetime_ida"
    )
    assert rotinas_tv.count() == 2
    for rotina_tv, tv_api in zip(rotinas_tv, trechos_vendidos_api):
        assert rotina_tv.rotina.datetime_ida == to_default_tz(tv_api["datetime_ida"])


def test_create_trechos_vendidos_trecho_vendido_existe_e_rotina_trecho_vendido_tbm(
    rota_mock, mock_rotinas, task_form, trechos_vendidos_api, django_assert_num_queries
):
    tz_sp = ZoneInfo("America/Sao_Paulo")
    updated_at = datetime(2022, 10, 13, 23, 20, tzinfo=tz_sp)
    with time_machine.travel(updated_at, tick=False):
        tv = baker.make(
            TrechoVendido,
            capacidade_classe=trechos_vendidos_api[0]["capacidade_classe"],
            rota=rota_mock,
            classe=trechos_vendidos_api[0]["classe"],
            origem_id=task_form.origem_id,
            destino_id=task_form.destino_id,
        )
    baker.make(RotinaTrechoVendido, trecho_vendido=tv, rotina=mock_rotinas.rotinas[0])

    # assert de sanidade
    tv.refresh_from_db()
    assert tv.updated_at == updated_at
    assert TrechoVendido.objects.filter(rota_id=rota_mock.id).count() == 1
    assert RotinaTrechoVendido.objects.filter(trecho_vendido__rota_id=rota_mock.id).count() == 1

    # get trechos vendidos, update TrechoVendido, create RotinaTrechoVendido (ignore connflit)
    new_updated_at = datetime(2024, 1, 29, 15, 30, tzinfo=tz_sp)
    with (
        django_assert_num_queries(5, connection=connections["rodoviaria"]),
        time_machine.travel(new_updated_at, tick=False),
    ):
        response = fetch_trechos_vendidos_svc_v2._create_trechos_vendidos(task_form, trechos_vendidos_api)
    assert response is None
    # verifica se não criou TrechoVendido
    trechos_vendidos = TrechoVendido.objects.filter(rota_id=rota_mock.id)
    assert trechos_vendidos.count() == 1
    # verifica se atualizou TrechoVendido
    tv.refresh_from_db()
    assert tv.updated_at == new_updated_at
    assert tv.capacidade_classe == 7
    # verifica se não criou RotinaTrechoVendido
    rotinas_tv = RotinaTrechoVendido.objects.filter(trecho_vendido__rota_id=rota_mock.id).order_by(
        "rotina__datetime_ida"
    )
    assert rotinas_tv.count() == 2
    for rotina_tv, tv_api in zip(rotinas_tv, trechos_vendidos_api):
        assert rotina_tv.rotina.datetime_ida == to_default_tz(tv_api["datetime_ida"])


def test_create_trechos_vendidos_trecho_vendido_existe_e_rotina_trecho_vendido_nao(
    rota_mock, task_form, trechos_vendidos_api, django_assert_num_queries
):
    tz_sp = ZoneInfo("America/Sao_Paulo")
    updated_at = datetime(2022, 10, 13, 23, 20, 54, tzinfo=tz_sp)
    with time_machine.travel(updated_at, tick=False):
        tv = baker.make(
            TrechoVendido,
            capacidade_classe=trechos_vendidos_api[0]["capacidade_classe"],
            rota=rota_mock,
            classe=trechos_vendidos_api[0]["classe"],
            origem_id=task_form.origem_id,
            destino_id=task_form.destino_id,
        )

    # assert de sanidade
    tv.refresh_from_db()
    assert tv.updated_at == updated_at
    assert TrechoVendido.objects.filter(rota_id=rota_mock.id).count() == 1
    assert RotinaTrechoVendido.objects.filter(trecho_vendido__rota_id=rota_mock.id).count() == 0

    # get trechos vendidos, update TrechoVendido, create RotinaTrechoVendido
    new_updated_at = datetime(2022, 10, 20, 16, 43, tzinfo=tz_sp)
    with (
        django_assert_num_queries(5, connection=connections["rodoviaria"]),
        time_machine.travel(new_updated_at, tick=False),
    ):
        response = fetch_trechos_vendidos_svc_v2._create_trechos_vendidos(task_form, trechos_vendidos_api)
    assert response is None
    # verifica se não criou TrechoVendido
    trechos_vendidos = TrechoVendido.objects.filter(rota_id=rota_mock.id)
    assert trechos_vendidos.count() == 1
    # verifica se atualizou TrechoVendido
    tv.refresh_from_db()
    assert tv.updated_at == new_updated_at
    # verifica se criou RotinaTrechoVendido
    rotinas_tv = RotinaTrechoVendido.objects.filter(trecho_vendido__rota_id=rota_mock.id).order_by(
        "rotina__datetime_ida"
    )
    assert rotinas_tv.count() == 2
    for rotina_tv, tv_api in zip(rotinas_tv, trechos_vendidos_api):
        assert rotina_tv.rotina.datetime_ida == to_default_tz(tv_api["datetime_ida"])


def test_dispara_tasks_async_result_sem_parent(rota_mock):
    with mock.patch("rodoviaria.service.fetch_trechos_vendidos_svc_v2.chord.from_dict") as mocked_chord:
        mocked_task = mocked_chord.return_value
        mocked_result = mocked_task.return_value
        mocked_result.mock_add_spec(AsyncResult)
        mocked_result.parent = None
        mocked_result.id = "123"
        fetch_trechos_vendidos_svc_v2.dispara_tasks({"rota_id": rota_mock.id, "company_id": rota_mock.company_id})

    task_status = TaskStatus.objects.get(rota_id=rota_mock.id, task_name=TaskStatus.Name.FETCH_TRECHOS_VENDIDOS)
    assert task_status.task_id == "123"


def test_associa_tipos_assentos_aos_trechos_vendidos(rota_mock):
    tv = baker.make(
        TrechoVendido,
        capacidade_classe=12,
        rota=rota_mock,
        classe="LEITO c",
    )
    fetch_trechos_vendidos_svc_v2._associa_tipos_assentos_aos_trechos_vendidos(rota_mock.company_id, rota_mock.id)
    assert tv.tipo_assento is None
    tv.refresh_from_db()
    assert tv.tipo_assento.tipo_assento_parceiro == "LEITO c"


def test_associa_tipos_assentos_aos_trechos_vendidos_tipo_assento_ja_existe(rota_mock):
    ta = baker.make(TipoAssento, company_id=rota_mock.company_id, tipo_assento_parceiro="LEITO c")
    tv = baker.make(
        TrechoVendido,
        capacidade_classe=12,
        rota=rota_mock,
        classe="LEITO c",
    )
    fetch_trechos_vendidos_svc_v2._associa_tipos_assentos_aos_trechos_vendidos(rota_mock.company_id, rota_mock.id)
    assert tv.tipo_assento is None
    tv.refresh_from_db()
    assert tv.tipo_assento_id == ta.id


def test_associa_tipos_assentos_aos_trechos_vendidos_dois_trechos_mesmo_tipo_assento(
    rota_mock,
):
    tv = baker.make(
        TrechoVendido,
        capacidade_classe=12,
        rota=rota_mock,
        classe="LEITO c",
    )
    tv2 = baker.make(
        TrechoVendido,
        capacidade_classe=12,
        rota=rota_mock,
        classe="LEITO c",
    )
    fetch_trechos_vendidos_svc_v2._associa_tipos_assentos_aos_trechos_vendidos(rota_mock.company_id, rota_mock.id)
    assert tv.tipo_assento is None
    assert tv2.tipo_assento is None
    tv.refresh_from_db()
    tv2.refresh_from_db()
    assert tv.tipo_assento.tipo_assento_parceiro == "LEITO c"
    assert tv2.tipo_assento.tipo_assento_parceiro == "LEITO c"
    assert tv2.tipo_assento_id == tv.tipo_assento_id

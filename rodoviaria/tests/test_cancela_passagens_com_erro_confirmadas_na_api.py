from datetime import datetime, timedelta
from unittest import mock

import time_machine
from django.core.management import call_command
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from rodoviaria.api.eulabs.api import EulabsAPI
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Passagem


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancela_passagens_com_erro_empresa_hibrido(eulabs_company, eulabs_login):
    eulabs_company.modelo_venda = Company.ModeloVenda.HIBRIDO
    eulabs_company.save()
    p = baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
    )
    p.tags.add("passagem_com_erro_confirmada_na_api")
    with mock.patch.object(
        OrchestrateRodoviaria, "cancelar_reservas_por_pedido_id"
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    mock_cancelar_reservas_por_pedido_id.assert_not_called()


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancela_passagens_com_erro_ja_cancelado(eulabs_company, eulabs_login):
    passagem = baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
    )
    passagem.tags.add("passagem_com_erro_cancelada_pelo_cron")
    with mock.patch.object(
        OrchestrateRodoviaria, "cancelar_reservas_por_pedido_id"
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    mock_cancelar_reservas_por_pedido_id.assert_not_called()


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancela_passagens_com_erro_marcado_nao_autorizado(eulabs_company, eulabs_login):
    passagem = baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
    )
    passagem.tags.add("cancelamento_nao_autorizado")
    with mock.patch.object(
        OrchestrateRodoviaria, "cancelar_reservas_por_pedido_id"
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    mock_cancelar_reservas_por_pedido_id.assert_not_called()


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancela_passagens_com_erro_sem_a_tag(eulabs_company, eulabs_login):
    baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
    )
    with mock.patch.object(
        OrchestrateRodoviaria, "cancelar_reservas_por_pedido_id"
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    mock_cancelar_reservas_por_pedido_id.assert_not_called()


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancela_passagens_com_erro_not_implemented(totalbus_company, totalbus_login):
    passagem = baker.make(
        Passagem,
        company_integracao=totalbus_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
    )
    passagem.tags.add("passagem_com_erro_confirmada_na_api")
    call_command("cancela_passagens_com_erro_confirmadas_na_api")
    passagem.refresh_from_db()
    assert passagem.erro_cancelamento is None
    assert passagem.status == Passagem.Status.ERRO


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_cancela_passagens_com_erro_erro_no_cancelamento(eulabs_company, eulabs_login):
    passagem = baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
        poltrona_external_id=28,
    )
    passagem.tags.add("passagem_com_erro_confirmada_na_api")
    with mock.patch.object(
        EulabsAPI,
        "cancelar_reservas_por_pedido_id",
        return_value=[
            {
                "poltrona": 28,
                "numero_passagem": "VIB-00001",
                "error": "algum erro na API",
            }
        ],
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    passagem.refresh_from_db()
    mock_cancelar_reservas_por_pedido_id.assert_called_once_with("4231")
    assert passagem.erro_cancelamento == "algum erro na API"
    assert passagem.datetime_cancelamento == timezone.now()
    assert passagem.status == Passagem.Status.ERRO
    assert passagem.numero_passagem == "VIB-00001"


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancela_passagens_com_erro_erro_no_cancelamento_passagem_nao_cadastrada(eulabs_company, eulabs_login):
    passagem = baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
        poltrona_external_id=32,
    )
    passagem.tags.add("passagem_com_erro_confirmada_na_api")
    with mock.patch.object(
        EulabsAPI,
        "cancelar_reservas_por_pedido_id",
        return_value=[
            {
                "poltrona": 28,
                "numero_passagem": "VIB-00001",
                "error": "algum erro na API",
            }
        ],
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    passagem.refresh_from_db()
    mock_cancelar_reservas_por_pedido_id.assert_called_once_with("4231")
    assert passagem.erro_cancelamento is None
    assert passagem.datetime_cancelamento is None
    assert passagem.status == Passagem.Status.ERRO


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_cancela_passagens_com_erro_erro_no_cancelamento_nao_autorizado(eulabs_company, eulabs_login):
    passagem = baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
        poltrona_external_id=28,
    )
    passagem.tags.add("passagem_com_erro_confirmada_na_api")
    with mock.patch.object(
        EulabsAPI,
        "cancelar_reservas_por_pedido_id",
        return_value=[
            {
                "poltrona": 28,
                "numero_passagem": "VIB-00001",
                "error": "algum erro na API",
                "error_type": "nao_autorizado",
            }
        ],
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    passagem.refresh_from_db()
    mock_cancelar_reservas_por_pedido_id.assert_called_once_with("4231")
    assert passagem.erro_cancelamento == "algum erro na API"
    assert passagem.status == Passagem.Status.ERRO
    assert passagem.numero_passagem == "VIB-00001"
    assert passagem.tags_set() == {
        "cancelamento_nao_autorizado",
        "passagem_com_erro_confirmada_na_api",
    }


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_cancela_passagens_com_erro_sucesso_com_numero_passagem(eulabs_company, eulabs_login):
    passagem = baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
        poltrona_external_id=28,
    )
    passagem.tags.add("passagem_com_erro_confirmada_na_api")
    with mock.patch.object(
        EulabsAPI,
        "cancelar_reservas_por_pedido_id",
        return_value=[
            {
                "poltrona": 28,
                "numero_passagem": "VIB-00001",
                "cancel_response": {"sucesso": "passagem cancelada"},
            }
        ],
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    passagem.refresh_from_db()
    mock_cancelar_reservas_por_pedido_id.assert_called_once_with("4231")
    assert passagem.erro_cancelamento is None
    assert passagem.status == Passagem.Status.ERRO
    assert passagem.datetime_cancelamento == timezone.now()
    assert passagem.localizador is None
    assert passagem.numero_passagem == "VIB-00001"
    assert passagem.tags_set() == {"passagem_com_erro_cancelada_pelo_cron"}


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_cancela_passagens_com_erro_sucesso_com_localizador(eulabs_company, eulabs_login):
    passagem = baker.make(
        Passagem,
        company_integracao=eulabs_company,
        trechoclasse_integracao__datetime_ida=timezone.now() + timedelta(hours=4),
        status=Passagem.Status.ERRO,
        pedido_external_id=4231,
        poltrona_external_id=28,
    )
    passagem.tags.add("passagem_com_erro_confirmada_na_api")
    with mock.patch.object(
        EulabsAPI,
        "cancelar_reservas_por_pedido_id",
        return_value=[{"poltrona": 28, "localizador": "2412312"}],
    ) as mock_cancelar_reservas_por_pedido_id:
        call_command("cancela_passagens_com_erro_confirmadas_na_api")
    passagem.refresh_from_db()
    mock_cancelar_reservas_por_pedido_id.assert_called_once_with("4231")
    assert passagem.erro_cancelamento is None
    assert passagem.status == Passagem.Status.ERRO
    assert passagem.datetime_cancelamento == timezone.now()
    assert passagem.localizador == "2412312"
    assert passagem.numero_passagem is None
    assert passagem.tags_set() == {"passagem_com_erro_cancelada_pelo_cron"}

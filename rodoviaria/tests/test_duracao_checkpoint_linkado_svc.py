from datetime import timedelta

import pytest
from django.db import connections
from model_bakery import baker

from rodoviaria.service.auto_integra_operacao.duracao_checkpoint_linkado_svc import (
    get_duracao_e_timezone_primeiro_checkpoint_linkado_por_rota,
)


def _create_local_timezone(timezone):
    cidade_internal = baker.make("rodoviaria.CidadeInternal", timezone=timezone)
    cidade = baker.make("rodoviaria.Cidade", cidade_internal=cidade_internal)
    local = baker.make("rodoviaria.LocalEmbarque", cidade=cidade, local_embarque_internal_id=2)
    return local


@pytest.fixture
def _mock_rotas():
    local_cuiaba = _create_local_timezone("America/Cuiaba")
    local_cuiaba.local_embarque_internal_id = None
    local_cuiaba.save()
    local_sao_paulo = _create_local_timezone("America/Sao_Paulo")
    rota = baker.make("rodoviaria.Rota")
    rota2 = baker.make("rodoviaria.Rota")
    rota3 = baker.make("rodoviaria.Rota")
    baker.make(
        "rodoviaria.Checkpoint",
        rota_id=rota.id,
        idx=0,
        local=local_cuiaba,
        duracao=timedelta(hours=0),
        tempo_embarque=timedelta(hours=0),
    )
    baker.make(
        "rodoviaria.Checkpoint",
        rota_id=rota.id,
        idx=1,
        local=local_sao_paulo,
        duracao=timedelta(hours=2),
        tempo_embarque=timedelta(minutes=10),
    )
    baker.make(
        "rodoviaria.Checkpoint",
        rota_id=rota2.id,
        idx=0,
        local=local_sao_paulo,
        duracao=timedelta(hours=0),
        tempo_embarque=timedelta(hours=0),
    )
    return [rota.id, rota2.id, rota3.id]


def test_get_duracao_e_timezone_primeiro_checkpoint_linkado_por_rota(django_assert_num_queries, _mock_rotas):
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        opa = get_duracao_e_timezone_primeiro_checkpoint_linkado_por_rota(_mock_rotas)

    # Rota com primeiro checkpoint não linkado e com timezone diferente
    # Além dos timezones, deve retornar a duração entre os 2
    assert opa.first_timezone_rota[_mock_rotas[0]] == "America/Cuiaba"
    assert opa.first_timezone_linkado[_mock_rotas[0]] == "America/Sao_Paulo"
    assert opa.duracao_ate_linkado[_mock_rotas[0]] == timedelta(hours=2, minutes=10)

    # Rota com primeiro checkpoint linkado
    # first_timezone_rota e first_timezone_linkado devem permanecer o mesmo
    # duracao_ate_linkado deve ser 0
    assert opa.first_timezone_rota[_mock_rotas[1]] == "America/Sao_Paulo"
    assert opa.first_timezone_linkado[_mock_rotas[1]] == "America/Sao_Paulo"
    assert opa.duracao_ate_linkado[_mock_rotas[1]] == timedelta(seconds=0)

    # Rota sem checkpoints, não retorna nada
    assert not opa.first_timezone_rota.get(_mock_rotas[2])
    assert not opa.first_timezone_linkado.get(_mock_rotas[2])
    assert not opa.duracao_ate_linkado.get(_mock_rotas[2])

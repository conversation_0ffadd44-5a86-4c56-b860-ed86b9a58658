from types import SimpleNamespace

from model_bakery import baker

from rodoviaria.models.core import Cidade, CidadeInternal, Company, LocalEmbarque
from rodoviaria.service import create_local_embarque_cidade_svc


def _assert_count_changed(parada, company, cidade_count_change, locais_count_change):
    locais_count = LocalEmbarque.objects.count()
    cidade_count = Cidade.objects.count()
    create_local_embarque_cidade_svc.get_or_create_local_embarque(parada, company)
    assert Cidade.objects.count() == cidade_count + cidade_count_change
    assert LocalEmbarque.objects.count() == locais_count + locais_count_change


def test_get_or_create_local_embarque():
    company_id = 43
    company = baker.make("rodoviaria.Company", company_internal_id=company_id)
    cidade = baker.make("rodoviaria.Cidade", id_external=322, company=company)
    baker.make("rodoviaria.LocalEmbarque", id_external=512, cidade=cidade)

    # com cidade e com local
    parada = {
        "nome_cidade": "Taubate",
        "external_cidade_id": 322,
        "external_local_id": 512,
        "uf": "SP",
        "descricao": "Taubate 1 - SP",
    }
    _assert_count_changed(parada, company, 0, 0)

    # com cidade e sem local
    parada = {
        "nome_cidade": "Taubate",
        "external_cidade_id": 322,
        "external_local_id": 513,
        "uf": "SP",
        "descricao": "Taubate 2 - SP",
    }
    _assert_count_changed(parada, company, 0, 1)

    # sem cidade e sem local
    parada = {
        "nome_cidade": "Sao jose",
        "external_cidade_id": 323,
        "external_local_id": 514,
        "uf": "SP",
        "descricao": "Sao Jose - SP",
    }
    _assert_count_changed(parada, company, 1, 1)


def test_local_to_json():
    local = SimpleNamespace(
        nome_cidade="Taubate",
        external_cidade_id=322,
        external_local_id=513,
        uf="SP",
        descricao="Taubate 2 - SP",
        id_cidade_ibge=423123,
    )
    local_json = create_local_embarque_cidade_svc.local_to_json(local)
    assert local_json == {
        "nome_cidade": local.nome_cidade,
        "external_local_id": local.external_local_id,
        "uf": local.uf,
        "external_cidade_id": local.external_cidade_id,
        "descricao": local.descricao,
        "id_cidade_ibge": local.id_cidade_ibge,
    }


def test_create_cidades_and_locais_from_itinerario_create_cidade_and_local(mock_itinerario):
    company = baker.make(Company)
    cidade_internal_origem = baker.make(CidadeInternal, name=mock_itinerario.checkpoints[0].local.nome_cidade)
    create_local_embarque_cidade_svc.create_cidades_and_locais_from_itinerario(mock_itinerario.checkpoints, company.id)
    locais_embarque = LocalEmbarque.objects.filter(cidade__company=company)
    assert sorted([le.id_external for le in locais_embarque]) == sorted(
        [str(cp.local.external_local_id) for cp in mock_itinerario.checkpoints]
    )
    assert (
        Cidade.objects.get(id_external=mock_itinerario.checkpoints[0].local.external_cidade_id).cidade_internal
        == cidade_internal_origem
    )


def test_create_cidades_and_locais_from_itinerario_cidade_and_local_already_exists(mock_itinerario):
    company = baker.make(Company)
    for cp in mock_itinerario.checkpoints:
        c = baker.make(Cidade, company=company, id_external=cp.local.external_cidade_id)
        baker.make(LocalEmbarque, cidade=c, id_external=cp.local.external_local_id)
    count_cidades = Cidade.objects.filter(company=company).count()
    count_locais = LocalEmbarque.objects.filter(cidade__company=company).count()
    create_local_embarque_cidade_svc.create_cidades_and_locais_from_itinerario(mock_itinerario.checkpoints, company.id)
    assert count_cidades == Cidade.objects.filter(company=company).count()
    assert count_locais == LocalEmbarque.objects.filter(cidade__company=company).count()


def test_lista_cidades_por_empresa():
    company = baker.make(Company)
    cidade = baker.make(Cidade, id_external=10, company=company)
    cidades = create_local_embarque_cidade_svc._lista_cidades_por_empresa(company.id)
    assert cidades == {"10": cidade.id}


def test_lista_locais_embarque_por_empresa():
    company = baker.make(Company)
    local = baker.make(LocalEmbarque, id_external=30, cidade__company=company)
    locais = create_local_embarque_cidade_svc._lista_locais_embarque_por_empresa(company.id)
    assert locais == {"30": local.id}

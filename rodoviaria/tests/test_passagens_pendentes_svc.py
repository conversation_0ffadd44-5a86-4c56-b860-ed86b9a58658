from datetime import datetime, timed<PERSON><PERSON>
from unittest import mock

from model_bakery import baker

from commons.celery_utils import DefaultQueueNames
from commons.dateutils import to_default_tz
from rodoviaria.forms.staff_forms import CheckPaxForm
from rodoviaria.models.core import Company, Grupo, Passagem, TrechoClasse
from rodoviaria.service import passagens_pendentes_svc
from rodoviaria.views_schemas import AddMultiplePaxAsyncParams, PassagensConfirmadasPorEmpresaParams


def test_lista_passagens_confirmadas_por_empresa():
    company_internal_id = 8322
    company = baker.make(Company, company_internal_id=company_internal_id)
    grupo = baker.make(Grupo, company_integracao=company)
    datetime_ida = to_default_tz(datetime(2022, 4, 10, 15, 45))
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=datetime_ida)
    p1 = baker.make(
        Passagem,
        travel_internal_id=4231,
        buseiro_internal_id=8423,
        trechoclasse_integracao=trecho_classe,
        company_integracao=company,
        status=Passagem.Status.CONFIRMADA,
    )
    p2 = baker.make(
        Passagem,
        travel_internal_id=5832,
        buseiro_internal_id=9423,
        trechoclasse_integracao=trecho_classe,
        company_integracao=company,
        status=Passagem.Status.CONFIRMADA,
    )
    passagens = passagens_pendentes_svc.lista_passagens_confirmadas_por_empresa(
        PassagensConfirmadasPorEmpresaParams.parse_obj(
            {
                "companies_ids": [company_internal_id],
                "data_inicial": (datetime_ida - timedelta(days=2)).isoformat(),
                "data_final": (datetime_ida + timedelta(days=2)).isoformat(),
            }
        )
    )
    assert {
        "travel_id": p1.travel_internal_id,
        "buseiro_id": p1.buseiro_internal_id,
        "passagem_id": p1.id,
    } in passagens
    assert {
        "travel_id": p2.travel_internal_id,
        "buseiro_id": p2.buseiro_internal_id,
        "passagem_id": p2.id,
    } in passagens


def test_emitir_passagens_pendentes_async():
    add_pax_async_params = AddMultiplePaxAsyncParams(
        passagens=[
            CheckPaxForm.parse_obj(
                {
                    "id_destino": 514,
                    "id_origem": 638,
                    "passenger": {
                        "buseiro_id": 341475,
                        "cpf": "11975923774",
                        "name": "Nome Teste",
                        "phone": "21969200000",
                        "rg_number": "8885000",
                    },
                    "travel_id": 5409394,
                    "trechoclasse_id": 13984203,
                    "valor_por_buseiro": 85.0,
                }
            )
        ],
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    with mock.patch("rodoviaria.service.reserva_svc.add_pax_na_lista") as mock_add_pax_na_lista:
        passagens_pendentes_svc.emitir_passagens_pendentes_async(
            add_pax_async_params.passagens, add_pax_async_params.modelo_venda
        )
    mock_add_pax_na_lista.assert_called_once_with(add_pax_async_params.passagens[0])


def test_emitir_passagens_pendentes_async_marketplace():
    add_pax_async_params = AddMultiplePaxAsyncParams(
        passagens=[
            CheckPaxForm.parse_obj(
                {
                    "id_destino": 514,
                    "id_origem": 638,
                    "passenger": {
                        "buseiro_id": 341475,
                        "cpf": "11975923774",
                        "name": "Nome Teste",
                        "phone": "21969200000",
                        "rg_number": "8885000",
                    },
                    "travel_id": 5409394,
                    "trechoclasse_id": 13984203,
                    "valor_por_buseiro": 85.0,
                }
            )
        ],
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    with mock.patch("rodoviaria.service.passagens_pendentes_svc.emitir_async.s") as mock_emitir_async:
        passagens_pendentes_svc.emitir_passagens_pendentes_async(
            add_pax_async_params.passagens, add_pax_async_params.modelo_venda
        )
    mock_emitir_async.return_value.set.assert_called_once_with(queue=DefaultQueueNames.ADD_PAX)


def test_emitir_passagens_pendentes_async_hibrido():
    add_pax_async_params = AddMultiplePaxAsyncParams(
        passagens=[
            CheckPaxForm.parse_obj(
                {
                    "id_destino": 514,
                    "id_origem": 638,
                    "passenger": {
                        "buseiro_id": 341475,
                        "cpf": "11975923774",
                        "name": "Nome Teste",
                        "phone": "21969200000",
                        "rg_number": "8885000",
                    },
                    "travel_id": 5409394,
                    "trechoclasse_id": 13984203,
                    "valor_por_buseiro": 85.0,
                }
            )
        ],
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    with mock.patch("rodoviaria.service.passagens_pendentes_svc.emitir_async_hibrido.s") as mock_emitir_async_hibrido:
        passagens_pendentes_svc.emitir_passagens_pendentes_async(
            add_pax_async_params.passagens, add_pax_async_params.modelo_venda
        )
    mock_emitir_async_hibrido.return_value.set.assert_called_once_with(queue=DefaultQueueNames.ADD_PAX_HIBRIDO)


def test_emitir_passagens_pendentes_async_ja_existente():
    baker.make(
        Passagem,
        travel_internal_id=5409394,
        buseiro_internal_id=341475,
        status=Passagem.Status.CONFIRMADA,
    )
    add_pax_async_params = AddMultiplePaxAsyncParams(
        passagens=[
            CheckPaxForm.parse_obj(
                {
                    "id_destino": 514,
                    "id_origem": 638,
                    "passenger": {
                        "buseiro_id": 341475,
                        "cpf": "11975923774",
                        "name": "Nome Teste",
                        "phone": "21969200000",
                        "rg_number": "8885000",
                    },
                    "travel_id": 5409394,
                    "trechoclasse_id": 13984203,
                    "valor_por_buseiro": 85.0,
                }
            )
        ],
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    with mock.patch("rodoviaria.service.reserva_svc.add_pax_na_lista") as mock_add_pax_na_lista:
        passagens_pendentes_svc.emitir_passagens_pendentes_async(
            add_pax_async_params.passagens, add_pax_async_params.modelo_venda
        )
    mock_add_pax_na_lista.assert_not_called()

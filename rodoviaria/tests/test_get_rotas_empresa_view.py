import json
from datetime import datetime, timedelta
from types import SimpleNamespace
from unittest import mock

import pytest
from django.utils import timezone
from model_bakery import baker

from commons.dateutils import to_default_tz
from rodoviaria import views
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, Integracao
from rodoviaria.service.rota_svc import _create_or_update_checkpoints_por_rota


def _rota(company, model, itinerario):
    rota = baker.make(
        model,
        company=company,
        provider_data=json.dumps(itinerario),
    )
    _create_or_update_checkpoints_por_rota(
        OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda),
        rota.id,
    )
    company.features = ["itinerario"]
    company.save()
    return SimpleNamespace(company=company, rota=rota)


@pytest.fixture
def rota_praxio(praxio_login, praxio_company):
    return _rota(praxio_company, "rodoviaria.RotaPraxio", itinerario_praxio)


@pytest.fixture
def rota_praxio_pontos_embarque_integrados(praxio_login, praxio_company):
    itinerario_praxio_2 = itinerario_praxio
    itinerario_praxio_2[0]["Localidade"]["IDLocalidade"] += 23
    rota_praxio = _rota(praxio_company, "rodoviaria.RotaPraxio", itinerario_praxio_2)
    cidade_internal = baker.make("rodoviaria.CidadeInternal", name="TESTE", uf="UF")
    cidade = baker.make(
        "rodoviaria.Cidade",
        name="TESTE",
        cidade_internal=cidade_internal,
        company=praxio_company,
    )
    local_external_ids = [x["Localidade"]["IDLocalidade"] for x in itinerario_praxio_2]
    locais_descricao = [x["Localidade"]["Descricao"] for x in itinerario_praxio_2]
    locais = []
    locais.append(
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=local_external_ids[0],
            nickname=locais_descricao[0],
            cidade=cidade,
            local_embarque_internal_id=1,
        )
    )
    locais.append(
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=local_external_ids[1],
            nickname=locais_descricao[1],
            cidade=cidade,
            local_embarque_internal_id=2,
        )
    )
    locais.append(
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=local_external_ids[2],
            nickname=locais_descricao[2],
            cidade=cidade,
            local_embarque_internal_id=3,
        )
    )
    locais.append(
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=local_external_ids[3],
            nickname=locais_descricao[3],
            cidade=cidade,
            local_embarque_internal_id=4,
        )
    )

    checkpoints = rota_praxio.rota.get_itinerario()
    for check in checkpoints:
        check.local_id = locais[0].id
        check.save()
    return SimpleNamespace(rota=rota_praxio, locais=locais)


@pytest.fixture
def rota_praxio_pontos_embarque_e_rotina_cadastrados(
    rota_praxio_pontos_embarque_integrados,
):
    rota_praxio = rota_praxio_pontos_embarque_integrados.rota
    baker.make(
        "rodoviaria.Rotina",
        rota_id=rota_praxio.rota.id,
        datetime_ida=to_default_tz(datetime.now() + timedelta(days=3)),
    )
    return rota_praxio


@pytest.fixture
def rota_praxio_pontos_embarque_e_trechos_vendidos_cadastrados(
    rota_praxio_pontos_embarque_integrados,
):
    rota_praxio = rota_praxio_pontos_embarque_integrados.rota
    locais = rota_praxio_pontos_embarque_integrados.locais
    tv = []
    tv.append(
        baker.make(
            "rodoviaria.TrechoVendido",
            rota_id=rota_praxio.rota.id,
            origem=locais[0],
            destino=locais[1],
            preco=12,
        )
    )
    tv.append(
        baker.make(
            "rodoviaria.TrechoVendido",
            rota_id=rota_praxio.rota.id,
            origem=locais[1],
            destino=locais[2],
            preco=12,
        )
    )
    tv.append(
        baker.make(
            "rodoviaria.TrechoVendido",
            rota_id=rota_praxio.rota.id,
            origem=locais[2],
            destino=locais[3],
            preco=12,
        )
    )
    return SimpleNamespace(rota=rota_praxio, locais=locais, tv=tv)


@pytest.fixture
def rotina_praxio(rota_praxio):
    return baker.make(
        "rodoviaria.Rotina",
        rota=rota_praxio.rota,
        datetime_ida=to_default_tz(datetime.now()),
    )


@pytest.fixture
def rota_totalbus(totalbus_login, totalbus_company):
    return _rota(totalbus_company, "rodoviaria.RotaTotalBus", itinerario_totalbus)


@pytest.fixture
def rota_totalbus_pontos_embarque_integrados(totalbus_login, totalbus_company):
    itinerario_totalbus_2 = itinerario_totalbus
    itinerario_totalbus_2[0]["localidade"]["id"] += 23
    rota_totalbus = _rota(totalbus_company, "rodoviaria.RotaTotalBus", itinerario_totalbus_2)
    cidade_internal = baker.make("rodoviaria.CidadeInternal", name="TESTE", uf="UF")
    cidade = baker.make(
        "rodoviaria.Cidade",
        name="TESTE",
        cidade_internal=cidade_internal,
        company=rota_totalbus.company,
        timezone="America/Sao_Paulo",
    )
    local_external_ids = [x["localidade"]["id"] for x in itinerario_totalbus_2]
    locais_descricao = [x["localidade"]["cidade"] for x in itinerario_totalbus_2]
    locais = []
    locais.append(
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=local_external_ids[0],
            nickname=locais_descricao[0],
            cidade=cidade,
            local_embarque_internal_id=1,
        )
    )
    locais.append(
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=local_external_ids[1],
            nickname=locais_descricao[1],
            cidade=cidade,
            local_embarque_internal_id=2,
        )
    )
    locais.append(
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=local_external_ids[2],
            nickname=locais_descricao[2],
            cidade=cidade,
            local_embarque_internal_id=3,
        )
    )
    locais.append(
        baker.make(
            "rodoviaria.LocalEmbarque",
            id_external=local_external_ids[3],
            nickname=locais_descricao[3],
            cidade=cidade,
            local_embarque_internal_id=4,
        )
    )

    checkpoints = rota_totalbus.rota.get_itinerario()
    for check in checkpoints:
        check.local_id = locais[0].id
        check.save()

    baker.make(
        "rodoviaria.Rotina",
        rota=rota_totalbus.rota,
        datetime_ida=to_default_tz(datetime.now() + timedelta(days=3)),
    )
    return SimpleNamespace(rota=rota_totalbus, locais=locais)


@pytest.fixture
def rotas_praxio_integrada_e_nao_integrada(praxio_login, praxio_company):
    rota = baker.make(
        "rodoviaria.RotaPraxio",
        company=praxio_company,
        provider_data=json.dumps(itinerario_praxio),
        id_internal=1233,
    )
    rota2 = baker.make(
        "rodoviaria.RotaPraxio",
        company=praxio_company,
        provider_data=json.dumps(itinerario_praxio),
    )
    cria_mock_checkpoints_itinerario_praxio(rota)
    cria_mock_checkpoints_itinerario_praxio(rota2)
    praxio_company.features = ["itinerario"]
    praxio_company.save()
    return SimpleNamespace(company=praxio_company, rota_integrada=rota, rota_nao_integrada=rota2)


def test_get_rotas_empresa(rf, praxio_login, rota_praxio_pontos_embarque_integrados):
    last_success_at = timezone.now()
    baker.make(
        "rodoviaria.TaskStatus",
        rota_id=rota_praxio_pontos_embarque_integrados.rota.rota.id,
        company_id=rota_praxio_pontos_embarque_integrados.rota.company.id,
        task_name="fetch_trechos_vendidos",
        task_id="*********",
        status="SUCCESS",
        last_success_at=last_success_at,
    )
    filters = {
        "filters": json.dumps(
            {
                "search": "SAUIPE",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "checkpoints__local__nickname",
                },
            }
        )
    }

    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, praxio_login.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert (
        resp["items"][0]["checkpoints"][0]["local_id"]
        == rota_praxio_pontos_embarque_integrados.locais[0].local_embarque_internal_id
    )
    assert (
        resp["items"][0]["checkpoints"][0]["cidade_id"]
        == rota_praxio_pontos_embarque_integrados.locais[0].cidade.cidade_internal_id
    )
    assert resp["items"][0]["checkpoints"][0]["local"]["name"] == "TESTE"
    assert resp["items"][0]["checkpoints"][0]["distancia_km"] is None
    assert resp["items"][0]["checkpoints"][1]["distancia_km"] == "110.00"
    assert resp["items"][0]["todos_locais_integrados"]
    assert resp["items"][0]["rotina"] == {}
    assert resp["items"][0]["trechos_last_update"] == to_default_tz(last_success_at).strftime("%d/%m/%Y às %H:%M")


def test_get_rotas_empresa_totalbus(rf, totalbus_login, rota_totalbus_pontos_embarque_integrados):
    filters = {
        "filters": json.dumps(
            {
                "search": "",
                "paginator": {
                    "descending": True,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "checkpoints__local__nickname",
                },
            }
        )
    }

    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, totalbus_login.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["checkpoints"][0]["local"]["name"] == "TESTE"
    assert resp["items"][0]["checkpoints"][0]["local"]["uf"] == "UF"
    assert resp["items"][0]["checkpoints"][0]["distancia_km"] == "0.00"
    assert resp["items"][0]["todos_locais_integrados"]
    assert any(resp["items"][0]["rotina"].values())


def test_get_rotas_empresa_apenas_nao_integradas(rf, rotas_praxio_integrada_e_nao_integrada):
    filters = {
        "filters": json.dumps(
            {
                "search": "",
                "integradas": "nao_integradas",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "checkpoints__local__nickname",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rotas_praxio_integrada_e_nao_integrada.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["rodoviaria_rota_id"] == rotas_praxio_integrada_e_nao_integrada.rota_nao_integrada.id
    assert resp["items"][0]["rotina"] == {}
    assert resp["items"][0]["id_internal"] is None


def test_get_rotas_empresa_apenas_nao_ativas(rf, rota_praxio):
    rota_praxio.rota.ativo = False
    rota_praxio.rota.save()
    filters = {
        "filters": json.dumps(
            {
                "search": "",
                "integradas": "não_integradas",
                "ativas": "inativas",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "checkpoints__local__nickname",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota_praxio.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["rodoviaria_rota_id"] == rota_praxio.rota.id
    assert resp["items"][0]["rotina"] == {}
    assert not resp["items"][0]["ativo"]


def test_get_rotas_empresa_sorted_by_id(rf, rota_praxio):
    filters = {
        "filters": json.dumps(
            {
                "search": "SAUIPE",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "rodoviaria_rota_id",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota_praxio.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["checkpoints"][0]["local"]["name"] == "Sauipe - BA - (ANP)"
    assert resp["items"][0]["checkpoints"][1]["local"]["name"] == "Feira De Santana - BA"
    assert resp["items"][0]["rotina"] == {}
    assert resp["items"][0]["id_internal"] is None


def test_get_rotas_empresa_sorted_by_id_internal(rf, rota_praxio):
    filters = {
        "filters": json.dumps(
            {
                "search": "SAUIPE",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "id_internal",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)
    with mock.patch(
        "rodoviaria.service.rota_svc._get_filtered_get_rotas_empresa"
    ) as _get_filtered_get_rotas_empresa_mock:
        _get_filtered_get_rotas_empresa_mock.return_value = [
            {
                "id_internal": 12,
            },
            {
                "id_internal": None,
            },
        ]
        resp = views.get_rotas_empresa(req, rota_praxio.company.id)
        assert resp.status_code == 200
        resp = json.loads(resp.content)
        assert resp["items"]
        assert resp["items"][0]["id_internal"] is None


def test_get_rotas_empresa_sorted_by_pontos_embarque(rf, rota_praxio, rota_praxio_pontos_embarque_integrados):
    rota = rota_praxio_pontos_embarque_integrados.rota
    filters = {
        "filters": json.dumps(
            {
                "search": "SAUIPE",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "todos_locais_integrados",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["checkpoints"][0]["local"]["name"] == "Sauipe - BA - (ANP)"
    assert resp["items"][0]["rotina"] == {}


def test_get_rotas_empresa_sorted_by_trechos_vendidos(
    rf, rota_praxio, rota_praxio_pontos_embarque_e_trechos_vendidos_cadastrados
):
    rota = rota_praxio_pontos_embarque_e_trechos_vendidos_cadastrados.rota
    filters = {
        "filters": json.dumps(
            {
                "search": "SAUIPE",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "possui_trechos_vendidos",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["checkpoints"][0]["local"]["name"] == "Sauipe - BA - (ANP)"
    assert resp["items"][0]["rotina"] == {}
    assert "possui_trechos_vendidos" in resp["items"][0]


def test_get_rotas_empresa_sorted_by_rotina(rf, rota_praxio, rota_praxio_pontos_embarque_e_rotina_cadastrados):
    rota = rota_praxio_pontos_embarque_e_rotina_cadastrados
    filters = {
        "filters": json.dumps(
            {
                "search": "SAUIPE",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "rotina",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert len(json.dumps(resp["items"][0]["rotina"])) < len(json.dumps(resp["items"][1]["rotina"]))


def test_get_rotas_empresa_not_sorted_by(rf, rota_praxio):
    filters = {
        "filters": json.dumps(
            {
                "search": "SAUIPE",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": None,
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota_praxio.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["checkpoints"][0]["local"]["name"] == "Sauipe - BA - (ANP)"
    assert resp["items"][0]["rotina"] == {}


def test_get_rotas_empresa_nenhum_checkpoint_com_nome_filtrado(rf, rota_praxio):
    filters = {
        "filters": json.dumps(
            {
                "search": "PAÇOQUINHA",
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "checkpoints__local__nickname",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota_praxio.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert not resp["items"]


def test_get_rotas_empresa_sem_paginacao(rf, rota_praxio):
    filters = {"filters": json.dumps({"search": "SAUIPE"})}
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota_praxio.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["checkpoints"][0]["local"]["name"] == "Sauipe - BA - (ANP)"
    assert resp["items"][0]["rotina"] == {}


def test_get_rotas_empresa_search_por_id(rf, rota_praxio):
    filters = {
        "filters": json.dumps(
            {
                "search": rota_praxio.rota.id,
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "checkpoints__local__nickname",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota_praxio.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["rodoviaria_rota_id"] == rota_praxio.rota.id
    assert resp["items"][0]["rotina"] == {}


def test_get_rotas_empresa_com_rotinas_sem_checkpoint_linkado(rf, rota_praxio, rotina_praxio):
    filters = {
        "filters": json.dumps(
            {
                "search": rota_praxio.rota.id,
                "paginator": {
                    "descending": False,
                    "page": 1,
                    "rowsPerPage": 2,
                    "sortBy": "checkpoints__local__nickname",
                },
            }
        )
    }
    req = rf.get("/get_rotas_empresa", filters)

    resp = views.get_rotas_empresa(req, rota_praxio.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["rodoviaria_rota_id"] == rota_praxio.rota.id
    assert any(resp["items"][0]["rotina"].values())


def test_get_rotas_empresa_sem_filtros(rf, rota_praxio):
    req = rf.get("/get_rotas_empresa")

    resp = views.get_rotas_empresa(req, rota_praxio.company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["items"]
    assert resp["items"][0]["checkpoints"][0]["local"]["name"] == "Sauipe - BA - (ANP)"
    assert resp["items"][0]["rotina"] == {}


def test_get_rotas_empresa_sem_rotas_cadastradas(rf, praxio_login, praxio_company):
    praxio_company.features = ["itinerario"]
    praxio_company.save()
    req = rf.get("/get_rotas_empresa")
    resp = views.get_rotas_empresa(req, praxio_company.id)
    assert resp.status_code == 200
    resp = json.loads(resp.content)
    assert resp["count"] == 0


def test_get_rotas_empresa_erro_empresa_nao_existe(rf):
    req = rf.get("/get_rotas_empresa")
    resp = views.get_rotas_empresa(req, -999)
    assert resp.status_code == 422
    resp = json.loads(resp.content)
    assert resp["mensagem"] == "Não foi possível encontrar empresa com o ID -999"


def test_get_rotas_empresa_erro_integracao_nao_mapeada(rf):
    integracao_nao_mapeada = baker.make(Integracao, name="nao_mapeada")
    company = baker.make(Company, integracao=integracao_nao_mapeada)
    req = rf.get("/get_rotas_empresa")
    resp = views.get_rotas_empresa(req, company.id)
    assert resp.status_code == 422
    resp = json.loads(resp.content)
    assert resp["mensagem"] == "Integração não implementada para empresa"


def cria_mock_checkpoints_itinerario_praxio(rota):
    baker.make(
        "rodoviaria.Checkpoint",
        idx=0,
        rota=rota,
        name="SAUIPE (BA)",
        nickname="SAUIPE (BA)",
    )
    baker.make(
        "rodoviaria.Checkpoint",
        idx=1,
        rota=rota,
        name="FEIRA DE SANTANA (BA)",
        nickname="SAUIPE (BA)",
    )
    baker.make(
        "rodoviaria.Checkpoint",
        idx=2,
        rota=rota,
        name="CRUZ DAS ALMAS (BA)",
        nickname="SAUIPE (BA)",
    )
    baker.make(
        "rodoviaria.Checkpoint",
        idx=3,
        rota=rota,
        name="SANTO ANTONIO DE JESUS (BA)",
        nickname="SAUIPE (BA)",
    )


itinerario_praxio = [
    {
        "IDViagem": 1592,
        "DataPartida": "2021-06-05T02:30:00",
        "Plataforma": "",
        "Localidade": {
            "IDLocalidade": 176,
            "Descricao": "SAUIPE (BA) (ANP)",
            "Sigla": "SAU",
            "Uf": "BA",
            "IdCidade": 0,
            "Codigo": 0,
        },
    },
    {
        "IDViagem": 1592,
        "DataPartida": "2021-06-05T04:30:00",
        "Plataforma": "D",
        "Localidade": {
            "IDLocalidade": 13,
            "Descricao": "FEIRA DE SANTANA (BA)",
            "Sigla": "FEI",
            "Uf": "BA",
            "IdCidade": 2910800,
            "Codigo": 4941,
        },
    },
    {
        "IDViagem": 1592,
        "DataPartida": "2021-06-05T05:50:00",
        "Plataforma": "",
        "Localidade": {
            "IDLocalidade": 193,
            "Descricao": "CRUZ DAS ALMAS (BA)",
            "Sigla": "CRU",
            "Uf": "BA",
            "IdCidade": 2909802,
            "Codigo": 4825,
        },
    },
    {
        "IDViagem": 1592,
        "DataPartida": "2021-06-05T06:40:00",
        "Plataforma": "",
        "Localidade": {
            "IDLocalidade": 177,
            "Descricao": "SANTO ANTONIO DE JESUS (BA)",
            "Sigla": "SAN",
            "Uf": "BA",
            "IdCidade": 2928703,
            "Codigo": 4819,
        },
    },
]


itinerario_totalbus = [
    {
        "localidade": {"id": 17674, "cidade": "BRUSQUE - SC", "uf": "SC"},
        "distancia": "27.0",
        "permanencia": "00:00",
        "data": "2021-07-09",
        "hora": "18:00",
    },
    {
        "localidade": {"id": 17676, "cidade": "GASPAR - SC", "uf": "SC"},
        "distancia": "16.0",
        "permanencia": "00:00",
        "data": "2021-07-09",
        "hora": "18:35",
    },
    {
        "localidade": {"id": 17672, "cidade": "BLUMENAU - SC", "uf": "SC"},
        "distancia": "64.0",
        "permanencia": "00:00",
        "data": "2021-07-09",
        "hora": "19:10",
    },
    {
        "localidade": {"id": 12723, "cidade": "FOZ DO IGUACU - PR", "uf": "PR"},
        "distancia": "0.00",
        "permanencia": "00:00",
        "data": "2021-07-10",
        "hora": "08:50",
    },
]

from model_bakery import baker

from rodoviaria.models.core import Company
from rodoviaria.service.transbrasil_company_factory import cria_company_transbrasil


def test_cria_company_transbrasil(vexado_integracao):
    company_existente = baker.make(
        "rodoviaria.Company", integracao=vexado_integracao, modelo_venda=Company.ModeloVenda.HIBRIDO
    )
    baker.make("rodoviaria.VexadoLogin", user="zxc", password="asd", company=company_existente)
    company = cria_company_transbrasil(888, "Nova Company", 399)
    assert company.features == [
        "itinerario",
        "buscar_servico",
        "add_pax_staff",
        "bpe",
        "motorista",
        "atualizar_preco",
        "active",
        "escalar_veiculos",
    ]

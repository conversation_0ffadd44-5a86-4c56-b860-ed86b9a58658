import datetime as dt
import json
from unittest import mock

import pytest
from model_bakery import baker
from zoneinfo import ZoneInfo

from rodoviaria import views, views_schemas
from rodoviaria.forms.staff_forms import (
    CreateRodoviariaCompanyForm,
    GuichepassLoginForm,
    PraxioLoginForm,
    TotalbusNoCompanyLogin,
    VexadoLoginForm,
)
from rodoviaria.models.core import Company, Integracao
from rodoviaria.models.vexado import VexadoLogin
from rodoviaria.service import company_svc
from rodoviaria.service.exceptions import (
    RodoviariaCompanyExistenteException,
    RodoviariaCompanyNotFoundException,
    RodoviariaCompanyNotIntegratedError,
    RodoviariaException,
    RodoviariaIntegracaoNotFoundException,
    RodoviariaLoginNotFoundException,
    RodoviariaUnableHardStop,
    RodoviariaUnableRevertHardStop,
    RodoviariaUnauthorizedError,
)
from rodoviaria.service.map_marketplace_cidades_svc import MapMarketplaceCidadesSVC


@pytest.fixture
def integracoes_mock():
    totalbus = baker.make("rodoviaria.Integracao", name="totalbus")
    praxio = baker.make("rodoviaria.Integracao", name="praxio")
    vexado = baker.make("rodoviaria.Integracao", name="vexado")
    guichepass = baker.make("rodoviaria.Integracao", name="guichepass")
    yield totalbus, praxio, vexado, guichepass
    totalbus.delete()
    praxio.delete()
    vexado.delete()
    guichepass.delete()


def test_get_companies_paginator_name(empresas_mock):
    name = "aNan"
    status = None
    modelo_venda = None
    rows_per_page = 10
    page = 1
    order_by = "name"

    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "banana corp"
    assert response["count"] == 1
    assert response["num_pages"] == 1

    name = "ab"
    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abacate ltda"
    assert response["empresas"][1]["nome"] == "abobora bus"
    assert response["count"] == 2
    assert response["num_pages"] == 1


def test_get_companies_paginator_modelo_venda(empresas_mock):
    name = None
    status = None
    modelo_venda = "marketplace"
    rows_per_page = 10
    page = 1
    order_by = "name"

    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abacate ltda"
    assert response["empresas"][1]["nome"] == "banana corp"
    assert response["count"] == 2
    assert response["num_pages"] == 1

    modelo_venda = "hibrido"
    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abobora bus"
    assert response["count"] == 1
    assert response["num_pages"] == 1


def test_get_companies_paginator_status(empresas_mock):
    name = None
    status = "ativas"
    modelo_venda = None
    rows_per_page = 10
    page = 1
    order_by = "name"

    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )
    assert response["empresas"][0]["nome"] == "abobora bus"
    assert response["empresas"][1]["nome"] == "banana corp"
    assert response["count"] == 2
    assert response["num_pages"] == 1

    status = "inativas"
    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abacate ltda"
    assert response["count"] == 1
    assert response["num_pages"] == 1


def test_get_companies_no_filter(empresas_mock):
    name = None
    status = None
    modelo_venda = None
    rows_per_page = None
    page = None
    order_by = None

    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert sorted([empresa["nome"] for empresa in response["empresas"]]) == [
        "abacate ltda",
        "abobora bus",
        "banana corp",
    ]
    assert response["count"] == 3
    assert response["num_pages"] == 1


def test_get_companies_name(empresas_mock):
    name = "aNan"
    status = None
    modelo_venda = None
    rows_per_page = None
    page = None
    order_by = "name"

    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "banana corp"
    assert response["count"] == 1
    assert response["num_pages"] == 1

    name = "ab"
    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abacate ltda"
    assert response["empresas"][1]["nome"] == "abobora bus"
    assert response["count"] == 2
    assert response["num_pages"] == 1


def test_get_companies_modelo_venda(empresas_mock):
    name = None
    status = None
    modelo_venda = "marketplace"
    rows_per_page = None
    page = None
    order_by = "-name"

    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "banana corp"
    assert response["empresas"][1]["nome"] == "abacate ltda"
    assert response["count"] == 2
    assert response["num_pages"] == 1

    modelo_venda = "hibrido"
    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abobora bus"
    assert response["count"] == 1
    assert response["num_pages"] == 1


def test_get_companies_multimodelo(empresas_mock):
    name = None
    status = None
    modelo_venda = "marketplace"
    rows_per_page = None
    page = None
    order_by = None

    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "banana corp"
    assert response["empresas"][1]["nome"] == "abacate ltda"
    assert response["count"] == 2
    assert response["num_pages"] == 1

    modelo_venda = "hibrido"
    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abobora bus"
    assert response["count"] == 1
    assert response["num_pages"] == 1


def test_get_companies_status():
    company_internal_id = 3923
    baker.make(
        Company,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        company_internal_id=company_internal_id,
        name="MULTIMODELO",
    )
    baker.make(
        Company,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
        company_internal_id=company_internal_id,
        name="MULTIMODELO",
    )
    baker.make(
        Company,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
        company_internal_id=9312,
        name="UNIMODELO",
    )
    empresas = company_svc.get_empresas()

    empresas = sorted(empresas, key=lambda e: e["nome"])
    assert empresas[0]["nome"] == "MULTIMODELO (Hibrido)"
    assert empresas[1]["nome"] == "MULTIMODELO (Marketplace)"
    assert empresas[2]["nome"] == "UNIMODELO"


def test_get_companies_filter_by_integracao(totalbus_company, praxio_company, ti_sistemas_company, vexado_company, rf):
    integracoes = ["praxio", "totalbus"]
    params = views_schemas.CompanyPaginatorParams(integracoes=integracoes)

    request = rf.get("/v1/empresas")
    response = views.empresas(request, params)

    assert response.status_code == 200
    empresas = json.loads(response.content)["empresas"]
    assert integracoes == sorted([c["integracao"] for c in empresas])


@pytest.mark.parametrize(
    "day,expected_trigger_day",
    [("6,2", "terça-feira e sábado"), ("5", "sexta-feira"), ("1,2,3", "segunda-feira,terça-feira e quarta-feira")],
)
def test_format_trigger_time(day, expected_trigger_day):
    assert company_svc._format_trigger_time("25", "7", day) == f"{expected_trigger_day} às 07:25h"


def test_get_companies_order_by(empresas_mock):
    name = None
    status = None
    modelo_venda = None
    rows_per_page = None
    page = None
    order_by = "pk"

    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "banana corp"
    assert response["empresas"][1]["nome"] == "abacate ltda"
    assert response["empresas"][2]["nome"] == "abobora bus"
    assert response["count"] == 3
    assert response["num_pages"] == 1

    order_by = "-pk"
    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abobora bus"
    assert response["empresas"][1]["nome"] == "abacate ltda"
    assert response["empresas"][2]["nome"] == "banana corp"
    assert response["count"] == 3
    assert response["num_pages"] == 1

    order_by = "name"
    response = company_svc.companies_paginator(
        name=name,
        status=status,
        modelo_venda=modelo_venda,
        rows_per_page=rows_per_page,
        page=page,
        order_by=order_by,
    )

    assert response["empresas"][0]["nome"] == "abacate ltda"
    assert response["empresas"][1]["nome"] == "abobora bus"
    assert response["empresas"][2]["nome"] == "banana corp"
    assert response["count"] == 3
    assert response["num_pages"] == 1


def test_create_empresa_totalbus(integracoes_mock):
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "company_external_id": -1,
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": 79,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "max_percentual_divergencia": 105,
        "integracao": "totalbus",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)

    with mock.patch("rodoviaria.service.company_svc.update_or_create_login") as mock_create_login, mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
        return_value=True,
    ):
        result = company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )
        mock_create_login.assert_called_once()

    assert result == {"success": True}
    company = Company.objects.select_related("integracao").get(
        company_internal_id=data.company_internal_id, modelo_venda=data.modelo_venda
    )
    assert company.max_percentual_divergencia == data.max_percentual_divergencia
    assert company.integracao.name == "totalbus"


def test_create_empresa_praxio(integracoes_mock):
    praxio_login = {
        "name": "admin",
        "password": "senha",
        "cliente": "CLIENT_VR",
        "desconto_manual": True,
        "max_percentual_divergencia": 105,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": 79,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": praxio_login,
        "integracao": "praxio",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)

    with mock.patch("rodoviaria.service.company_svc.update_or_create_login") as mock_create_login, mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
        return_value=True,
    ):
        result = company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )
        mock_create_login.assert_called_once()

    assert result == {"success": True}
    company = Company.objects.select_related("integracao").get(
        company_internal_id=data.company_internal_id, modelo_venda=data.modelo_venda
    )
    assert company.max_percentual_divergencia == data.max_percentual_divergencia
    assert company.integracao.name == "praxio"


def test_create_empresa_vexado(integracoes_mock):
    vexado_login = {"user": "admin", "password": "senha", "company_external_id": 18}
    body = {
        "name": "busao ltda",
        "company_internal_id": 79,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": vexado_login,
        "integracao": "vexado",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)

    with mock.patch("rodoviaria.service.company_svc.update_or_create_login") as mock_create_login, mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
        return_value=True,
    ):
        result = company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )
        mock_create_login.assert_called_once()

    assert result == {"success": True}
    company = Company.objects.select_related("integracao").get(
        company_internal_id=data.company_internal_id, modelo_venda=data.modelo_venda
    )
    assert company.max_percentual_divergencia == data.max_percentual_divergencia
    assert company.integracao.name == "vexado"


def test_create_empresa_guichepass(integracoes_mock):
    guichepass_login = {
        "url_base": "http://api-gravataense.buson.com.br",
        "username": "admin",
        "password": "senha",
        "client_id": "WEB_SALE",
        "company_external_id": 2,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": 79,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": guichepass_login,
        "integracao": "guichepass",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)

    with mock.patch(
        "rodoviaria.service.company_svc.update_or_create_login", return_value=True
    ) as mock_create_login, mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
    ):
        result = company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )
        mock_create_login.assert_called_once()

    assert result == {"success": True}
    company = Company.objects.select_related("integracao").get(
        company_internal_id=data.company_internal_id, modelo_venda=data.modelo_venda
    )
    assert company.max_percentual_divergencia == data.max_percentual_divergencia
    assert company.integracao.name == "guichepass"


@pytest.mark.django_db(transaction=True)
def test_create_empresa_exception(integracoes_mock, totalbus_login_mock):
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": 18,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "max_percentual_divergencia": 105,
        "integracao": "totalbus",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)
    assert data.company_internal_id == totalbus_login_mock.empresa.company_internal_id
    assert data.modelo_venda == totalbus_login_mock.empresa.modelo_venda

    with pytest.raises(RodoviariaCompanyExistenteException) as exc:
        company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )
    assert (
        str(exc.value) == "Empresa já existente com "
        f"(modelo_venda={data.modelo_venda}, company_internal_id={data.company_internal_id})"
    )


@pytest.mark.django_db(transaction=True)
def test_create_empresa_fluxo_login_invalido_401(integracoes_mock):
    company_internal_id = 79
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "company_external_id": 2,
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": company_internal_id,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "max_percentual_divergencia": 105,
        "integracao": "totalbus",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)

    with mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
        side_effect=RodoviariaUnauthorizedError,
    ), pytest.raises(RodoviariaUnauthorizedError):
        company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )

    # Deleta a empresa caso seu login esteja incorreto (401 error)
    assert Company.objects.filter(company_internal_id=company_internal_id).exists() is False


@pytest.mark.django_db(transaction=True)
def test_create_empresa_fluxo_processando_cache_423(integracoes_mock):
    company_internal_id = 79
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "company_external_id": -1,
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": company_internal_id,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "max_percentual_divergencia": 105,
        "integracao": "totalbus",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)

    with mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
        side_effect=RodoviariaException,
    ), pytest.raises(RodoviariaException):
        company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )

    # Deleta a empresa caso os dados da operação não sejam carregados (423 error)
    assert Company.objects.filter(company_internal_id=company_internal_id).exists() is False


@pytest.mark.django_db(transaction=True)
def test_create_empresa_fluxo_erro_buscar_servicos(integracoes_mock):
    company_internal_id = 79
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "company_external_id": 2,
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": company_internal_id,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "max_percentual_divergencia": 105,
        "integracao": "totalbus",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)

    with mock.patch(
        "rodoviaria.service.company_svc.fetch_operacao_empresa_marketplace",
        side_effect=RodoviariaException,
    ), pytest.raises(RodoviariaException):
        company_svc.create_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )

    # Deleta a empresa caso os dados da operação não sejam carregados (buscar servicos error)
    assert Company.objects.filter(company_internal_id=company_internal_id).exists() is False


def test_create_login_totalbus():
    company = baker.make("rodoviaria.Company", pk=100, name="bus corp")
    api = "totalbus"
    totalbus_login = {
        "user": "admin",
        "password": "senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "company_external_id": 2,
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": False,
    }
    login_data = TotalbusNoCompanyLogin.parse_obj(totalbus_login).dict()
    result = company_svc.update_or_create_login(empresa=company, api=api, login_data=login_data)

    assert result is True


def test_create_login_praxio():
    company = baker.make("rodoviaria.Company", pk=100, name="bus corp")
    api = "praxio"
    praxio_login = {
        "name": "admin",
        "password": "senha",
        "cliente": "CLIENT_VR",
        "desconto_manual": True,
    }
    login_data = PraxioLoginForm.parse_obj(praxio_login).dict()
    result = company_svc.update_or_create_login(empresa=company, api=api, login_data=login_data)

    assert result is True


def test_create_login_vexado():
    login_base = baker.make(
        VexadoLogin,
        user="admin",
        password="senha",
        company=baker.make(Company, modelo_venda=Company.ModeloVenda.MARKETPLACE),
    )
    company = baker.make(
        "rodoviaria.Company",
        pk=100,
        name="bus corp",
        integracao=baker.make(Integracao, name="vexado"),
    )
    api = "vexado"
    vexado_login = {"company_external_id": 18}
    login_data = VexadoLoginForm.parse_obj(vexado_login).dict()
    result = company_svc.update_or_create_login(empresa=company, api=api, login_data=login_data)

    assert result is True
    company.refresh_from_db()
    assert company.url_base == VexadoLogin.DEFAULT_URL_BASE
    login = VexadoLogin.objects.get(company=company)
    assert login.user == login_base.user
    assert login.password == login_base.password


def test_create_login_guichepass():
    company = baker.make("rodoviaria.Company", pk=100, name="bus corp")
    api = "guichepass"
    guichepass_login = {
        "url_base": "http://api-gravataense.buson.com.br",
        "username": "admin",
        "password": "senha",
        "client_id": "WEB_SALE",
        "company_external_id": 2,
    }
    login_data = GuichepassLoginForm.parse_obj(guichepass_login).dict()
    result = company_svc.update_or_create_login(empresa=company, api=api, login_data=login_data)

    assert result is True


def test_create_login_atualiza(totalbus_login_mock):
    totalbus_login = {
        "user": "novo_admin",
        "password": "nova_senha",
        "tenant_id": "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 2,
        "forma_pagamento": "BUSER",
        "validar_multa": True,
    }
    login_data = TotalbusNoCompanyLogin.parse_obj(totalbus_login).dict()
    company = totalbus_login_mock.empresa
    result = company_svc.update_or_create_login(empresa=company, api="totalbus", login_data=login_data)

    assert result is False


def test_get_login_totalbus(totalbus_login):
    integracao = "totalbus"
    company_id = totalbus_login.company.pk
    modelo_venda = totalbus_login.company.modelo_venda

    result = company_svc.get_login(company_id=company_id, integracao=integracao, modelo_venda=modelo_venda)

    assert result["user"] == totalbus_login.user
    assert result["password"] == totalbus_login.password
    assert result["tenant_id"] == totalbus_login.tenant_id
    assert result["id_forma_pagamento"] == totalbus_login.id_forma_pagamento
    assert result["forma_pagamento"] == totalbus_login.forma_pagamento
    assert result["validar_multa"] == totalbus_login.validar_multa


def test_get_login_praxio(praxio_login):
    integracao = "praxio"
    company_id = praxio_login.company.pk
    modelo_venda = praxio_login.company.modelo_venda

    result = company_svc.get_login(company_id=company_id, integracao=integracao, modelo_venda=modelo_venda)

    assert result["name"] == praxio_login.name
    assert result["password"] == praxio_login.password
    assert result["cliente"] == praxio_login.cliente
    assert result["desconto_manual"] == praxio_login.desconto_manual


def test_get_login_vexado(vexado_login):
    integracao = "vexado"
    company_id = vexado_login.company.pk
    modelo_venda = vexado_login.company.modelo_venda

    result = company_svc.get_login(company_id=company_id, integracao=integracao, modelo_venda=modelo_venda)

    assert result["user"] == vexado_login.user
    assert result["password"] == vexado_login.password
    assert result["company_external_id"] == vexado_login.company.company_external_id


def test_get_login_guichepass(guiche_login):
    integracao = "guichepass"
    company_id = guiche_login.company.pk
    modelo_venda = "marketplace"

    result = company_svc.get_login(company_id=company_id, integracao=integracao, modelo_venda=modelo_venda)

    assert result["username"] == guiche_login.username
    assert result["password"] == guiche_login.password
    assert result["client_id"] == guiche_login.client_id


def test_get_login_ti_sistemas(ti_sistemas_login):
    integracao = "ti_sistemas"
    company_id = ti_sistemas_login.company.pk
    modelo_venda = "marketplace"

    result = company_svc.get_login(company_id=company_id, integracao=integracao, modelo_venda=modelo_venda)

    assert result["auth_key"] == ti_sistemas_login.auth_key
    assert result["company_external_id"] == ti_sistemas_login.company.company_external_id


def test_get_login_not_found(totalbus_login):
    integracao = "totalbus"
    company_id = 8976
    modelo_venda = "marketplace"

    assert company_id != totalbus_login.company.pk

    with pytest.raises(RodoviariaLoginNotFoundException) as exc:
        company_svc.get_login(company_id=company_id, integracao=integracao, modelo_venda=modelo_venda)

    assert (
        str(exc.value)
        == f"Login {integracao} não encontrado para (company_pk={company_id}, modelo_venda={modelo_venda})"
    )


def test_get_login_integracao_not_found(totalbus_login):
    integracao = "totabs"
    company_id = 8976
    modelo_venda = "marketplace"

    assert company_id != totalbus_login.company.pk

    with pytest.raises(RodoviariaIntegracaoNotFoundException) as exc:
        company_svc.get_login(company_id=company_id, integracao=integracao, modelo_venda=modelo_venda)

    assert str(exc.value) == f"Integracao {integracao} não encontrada"


def test_update_empresa(totalbus_login_mock, integracoes_mock):
    totalbus_login = {
        "user": "novo_admin",
        "password": "nova_senha",
        "tenant_id": "ffffferf-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 5,
        "forma_pagamento": "BUSER_VOUCHER",
        "validar_multa": True,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": totalbus_login_mock.empresa.company_internal_id,
        "modelo_venda": totalbus_login_mock.empresa.modelo_venda,
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "max_percentual_divergencia": 50,
        "integracao": "totalbus",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)
    assert body["features"] != totalbus_login_mock.empresa.features
    assert body["login"] != totalbus_login_mock
    assert body["max_percentual_divergencia"] != totalbus_login_mock.empresa.max_percentual_divergencia

    with mock.patch("rodoviaria.service.company_svc.update_or_create_login", return_value=True) as mock_create_login:
        company_svc.update_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )
        mock_create_login.assert_called_once()

    totalbus_login_mock.empresa.refresh_from_db()
    assert body["max_percentual_divergencia"] == totalbus_login_mock.empresa.max_percentual_divergencia


def test_update_empresa_exception(totalbus_login_mock, integracoes_mock):
    totalbus_login = {
        "user": "novo_admin",
        "password": "nova_senha",
        "tenant_id": "ffffferf-ffff-ffff-ffff-ffffffffffff",
        "id_forma_pagamento": 5,
        "forma_pagamento": "BUSER_VOUCHER",
        "validar_multa": True,
    }
    body = {
        "name": "busao ltda",
        "company_internal_id": 987,
        "modelo_venda": totalbus_login_mock.empresa.modelo_venda,
        "features": ["active", "bpe"],
        "login": totalbus_login,
        "max_percentual_divergencia": 105,
        "integracao": "totalbus",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)
    assert body["company_internal_id"] != totalbus_login_mock.empresa.company_internal_id

    with pytest.raises(RodoviariaCompanyNotFoundException) as exc:
        company_svc.update_empresa(
            name=data.name,
            company_internal_id=data.company_internal_id,
            modelo_venda=data.modelo_venda,
            features=data.features,
            login_data=data.login,
            api=data.integracao,
            max_percentual_divergencia=data.max_percentual_divergencia,
        )

    assert str(exc.value) == "Empresa não encontrada"


def test_hard_stop_empresa(totalbus_company, mocker):
    data_hora_congelada = dt.datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("UTC"))
    mocker.patch("rodoviaria.service.company_svc.timezone.now", return_value=data_hora_congelada)

    features_company_totalbus = totalbus_company.features
    company_svc.hard_stop_empresa(
        company_internal_id=totalbus_company.company_internal_id,
        modelo_venda=totalbus_company.modelo_venda,
    )

    totalbus_company.refresh_from_db()
    assert totalbus_company.features == []
    assert totalbus_company.previous_features_updated_at == data_hora_congelada
    assert totalbus_company.previous_features == features_company_totalbus


def test_get_all_possible_features():
    result = company_svc.get_all_possible_features()
    assert result == Company.Feature.values


def test_get_all_possible_features_staff():
    result = company_svc.get_all_possible_features(is_staff=True)
    assert result == [
        Company.Feature.ACTIVE,
        Company.Feature.BPE,
        Company.Feature.ATUALIZAR_PRECO_CHECKOUT,
        Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ,
        Company.Feature.AUTO_INTEGRA_OPERACAO,
        Company.Feature.RISK_SHARE,
        Company.Feature.ASSENTOS_OCIOSOS,
    ]


def test_fetch_operacao_empresa_marketplace(totalbus_login):
    company_internal_id = totalbus_login.company.company_internal_id

    with mock.patch.object(MapMarketplaceCidadesSVC, "execute"), mock.patch(
        "rodoviaria.service.company_svc.descobrir_rotas_svc.descobrir_rotas_proximos_dias",
        spec=True,
    ) as mock_descobrir_rotas, mock.patch(
        "rodoviaria.service.company_svc.fetch_trechos_vendidos_svc.fetch_trechos_by_company_id.si",
        spec=True,
    ) as mock_fetch_trechos_by_company_id, mock.patch(
        "rodoviaria.service.company_svc.rotina_svc.fetch_rotinas_empresa.si", spec=True
    ) as mock_fetch_rotinas_empresa, mock.patch(
        "rodoviaria.service.company_svc.fetch_data_limite_rotas_svc.fetch_data_limite_rotas.si",
        spec=True,
    ) as mock_fetch_data_limite_rotas:
        company_svc.fetch_operacao_empresa_marketplace(company_internal_id)

        mock_descobrir_rotas.assert_called_once()
        mock_fetch_trechos_by_company_id.assert_called_once()
        mock_fetch_rotinas_empresa.assert_called_once()
        mock_fetch_data_limite_rotas.assert_called_once()


def test_fetch_operacao_empresa_marketplace_hibrido(totalbus_login):
    totalbus_login.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_login.company.save()

    company_internal_id = totalbus_login.company.company_internal_id
    with pytest.raises(Company.DoesNotExist):
        company_svc.fetch_operacao_empresa_marketplace(company_internal_id)


def test_fetch_operacao_empresa_marketplace_error_login_invalido_401(totalbus_login, totalbus_mock_unauthorized_login):
    company_internal_id = totalbus_login.company.company_internal_id

    with pytest.raises(RodoviariaUnauthorizedError) as exc:
        company_svc.fetch_operacao_empresa_marketplace(company_internal_id)

    assert str(exc.value) == "Usuário/Senha inválido"


def test_fetch_operacao_empresa_marketplace_error_buscar_servicos(totalbus_login):
    company_internal_id = totalbus_login.company.company_internal_id

    with mock.patch.object(MapMarketplaceCidadesSVC, "execute"), mock.patch(
        "rodoviaria.service.company_svc.descobrir_rotas_svc.descobrir_rotas_proximos_dias",
        side_effect=RodoviariaException,
    ), pytest.raises(RodoviariaException):
        company_svc.fetch_operacao_empresa_marketplace(company_internal_id)


def test_fetch_operacao_empresa_marketplace_empresa_marketplace_nao_encontrada(
    vexado_login,
):
    company_internal_id = vexado_login.company.company_internal_id
    with pytest.raises(Company.DoesNotExist):
        company_svc.fetch_operacao_empresa_marketplace(company_internal_id)


def test_fetch_operacao_empresa_marketplace_descobrir_rotas_nao_implementado(vexado_login):
    vexado_login.company.modelo_venda = Company.ModeloVenda.MARKETPLACE
    vexado_login.company.save()
    company_internal_id = vexado_login.company.company_internal_id
    with mock.patch.object(MapMarketplaceCidadesSVC, "execute"), mock.patch(
        "rodoviaria.service.descobrir_rotas_svc.descobrir_rotas_proximos_dias", side_effect=NotImplementedError
    ):
        assert company_svc.fetch_operacao_empresa_marketplace(company_internal_id) is None


def test_integracao_conhecida_validador_CreateRodoviariaCompanyForm():
    baker.make(VexadoLogin, user="admin", password="senha", company__modelo_venda=Company.ModeloVenda.MARKETPLACE)
    vexado_login = {"user": "admin", "password": "senha", "company_external_id": 18}
    body = {
        "name": "busao ltda",
        "company_internal_id": 79,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "login": vexado_login,
        "integracao": "vexado",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)
    assert isinstance(data.login, VexadoLoginForm)
    assert data.integracao == "vexado"


def test_integracao_nova_validador_CreateRodoviariaCompanyForm():
    body = {
        "name": "busao ltda",
        "company_internal_id": 79,
        "modelo_venda": "marketplace",
        "features": ["active", "bpe"],
        "integracao": "Buser",
    }
    data = CreateRodoviariaCompanyForm.parse_obj(body)
    assert isinstance(data.login, dict)
    assert data.login == {}
    assert data.integracao == "buser"


def test_revert_hard_stop_empresa(totalbus_integracao):
    previous_features_updated_at = dt.datetime(2022, 10, 19, 16, 40, tzinfo=ZoneInfo("America/Recife"))
    company = baker.make(
        "rodoviaria.Company",
        integracao=totalbus_integracao,
        url_base="http://totalbus.base",
        name="Totalbus Transporte",
        features=[],
        previous_features=["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"],
        previous_features_updated_at=previous_features_updated_at,
    )

    company_svc.revert_hard_stop_empresa(
        company_internal_id=company.company_internal_id,
        modelo_venda=company.modelo_venda,
    )

    company.refresh_from_db()
    assert company.features == ["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"]
    assert company.previous_features == []
    assert company.previous_features_updated_at != previous_features_updated_at


def test_revert_hard_stop_empresa_sem_feature(totalbus_company):
    with pytest.raises(
        RodoviariaUnableRevertHardStop, match=r"A operação da empresa Totalbus Transporte não está em hardStop"
    ):
        company_svc.revert_hard_stop_empresa(
            company_internal_id=totalbus_company.company_internal_id, modelo_venda=totalbus_company.modelo_venda
        )


def test_create_execute_revert_hard_stop_empresa(totalbus_company):
    company_svc.hard_stop_empresa(
        company_internal_id=totalbus_company.company_internal_id,
        modelo_venda=totalbus_company.modelo_venda,
    )
    totalbus_company.refresh_from_db()
    with pytest.raises(
        RodoviariaUnableHardStop, match=r"A operação da empresa Totalbus Transporte já está em hardStop"
    ):
        company_svc.hard_stop_empresa(
            company_internal_id=totalbus_company.company_internal_id,
            modelo_venda=totalbus_company.modelo_venda,
        )

    company_svc.revert_hard_stop_empresa(
        company_internal_id=totalbus_company.company_internal_id,
        modelo_venda=totalbus_company.modelo_venda,
    )
    totalbus_company.refresh_from_db()
    assert totalbus_company.features == ["buscar_servico", "add_pax_staff", "bpe", "itinerario", "active"]
    assert totalbus_company.previous_features == []


def test_raise_if_unable_hard_stop_ok():
    company = baker.make(
        Company,
        name="Buser",
        company_internal_id=1,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        previous_features=["active"],
        features=[],
    )
    response = company_svc.raise_if_unable_hard_stop(company)
    assert response is True


def test_verifica_reverthardstop_empresa_nao_integrada():
    company = baker.make(
        Company,
        name="Buser",
        company_internal_id=1,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        previous_features=[],
        features=[Company.Feature.ATUALIZAR_PRECO_CHECKOUT],
    )
    with pytest.raises(RodoviariaCompanyNotIntegratedError, match=r"A empresa Buser não está integrada"):
        company_svc.raise_if_unable_hard_stop(company)


def test_verifica_reverthardstop_empresa_inabil_ao_revert():
    company = baker.make(
        Company,
        name="Buser",
        company_internal_id=1,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        previous_features=[],
        features=["active"],
    )
    with pytest.raises(RodoviariaUnableRevertHardStop, match=r" operação da empresa Buser não está em hardStop"):
        company_svc.raise_if_unable_hard_stop(company)

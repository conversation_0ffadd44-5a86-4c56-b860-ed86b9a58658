from datetime import date, datetime, timedelta
from decimal import Decimal as D

from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.forms.compra_rodoviaria_forms import ComprarForm, PassageiroForm
from rodoviaria.models.core import Passagem


def _comprar_params(
    trechoclasse_id,
    quantidade_passageiros=1,
    categoria_especial=Passagem.CategoriaEspecial.NORMAL,
    valor_cheio=D("100.00"),
):
    return ComprarForm(
        trechoclasse_id=trechoclasse_id,
        travel_id=1521,
        valor_cheio=valor_cheio,
        preco_rodoviaria=D("100.00"),
        poltronas=[10 + i for i in range(quantidade_passageiros)],
        categoria_especial=categoria_especial,
        passageiros=[
            PassageiroForm(
                id=15 + i,
                name=f"Fulano de Tal {i}",
                rg_number="*********",
                phone="12912121212",
                cpf="10101001090",
                birthday=date(1950, 10, 10),
            )
            for i in range(quantidade_passageiros)
        ],
    )


def mock_buscar_corridas_response(external_id=712, quantity=1, rota_external_id=94, time_diff=0):
    servicos = [
        ServicoForm(
            linha=62,
            external_datetime_ida=datetime.isoformat(datetime(2021, 5, 14, 20, 5) + timedelta(minutes=time_diff * i)),
            external_id=external_id * i,
            preco=142.41,
            vagas=6,
            provider_data={},
            external_datetime_chegada=datetime.isoformat(
                datetime(2021, 5, 15, 0, 25) + timedelta(minutes=time_diff * i)
            ),
            classe="LEITO CAMA",
            capacidade_classe=23,
            distancia=352.0,
            rota_external_id=rota_external_id * i,
        )
        for i in range(1, quantity + 1)
    ]

    return BuscarServicoForm(found=True, servicos=servicos)

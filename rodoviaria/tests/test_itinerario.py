import datetime
from types import SimpleNamespace
from unittest import mock

import pytest
from model_bakery import baker

from commons.dateutils import to_default_tz, to_tz
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.service.rota_svc import _create_or_update_checkpoints_por_rota, itinerario

STANDARDIZED_DATETIME_IDA = to_default_tz(datetime.datetime(2021, 6, 5, 2, 30))
DIFFERENT_DATETIME_IDA = to_default_tz(datetime.datetime(2021, 6, 12, 10, 30))


@pytest.fixture
def company_feature_itinerario(praxio_login, praxio_company):
    praxio_company.features = ["itinerario"]
    praxio_company.save()
    return praxio_company


@pytest.fixture
def mocked_parsed_itinerario(company_feature_itinerario):
    local_feira_de_santana = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="13",
        cidade=baker.make(
            "rodoviaria.Cidade",
            name="Feira de Santana - BA",
            timezone="America/Cuiaba",
            company=company_feature_itinerario,
        ),
    )
    local_cruz_das_almas = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external="193",
        cidade=baker.make(
            "rodoviaria.Cidade",
            name="Cruz das Almas",
            company=company_feature_itinerario,
        ),
    )
    return [
        # usa timezone default para primeira cidade
        SimpleNamespace(
            local=SimpleNamespace(
                descricao="SAUIPE (BA)",
                external_local_id="176",
                external_cidade_id="0",
                uf="BA",
            ),
            datetime_ida=STANDARDIZED_DATETIME_IDA,
            duracao=0,
            tempo_embarque=0,
            distancia=0,
        ),
        # LocalEmbarque cadastrado com timezone - usa timezone do local cadastrada
        SimpleNamespace(
            local=SimpleNamespace(
                descricao="FEIRA DE SANTANA (BA)",
                external_local_id=local_feira_de_santana.id_external,
                external_cidade_id="2910800",
                uf="BA",
            ),
            datetime_ida=datetime.datetime(2021, 6, 5, 4, 30),
            duracao=6600,
            tempo_embarque=600,
            distancia=0,
        ),
        # LocalEmbarque cadastrado sem timezone - usa o timezone da cidade anterior
        SimpleNamespace(
            local=SimpleNamespace(
                descricao="CRUZ DAS ALMAS (BA)",
                external_local_id=local_cruz_das_almas.id_external,
                external_cidade_id="2909802",
                uf="BA",
            ),
            datetime_ida=datetime.datetime(2021, 6, 5, 5, 50),
            duracao=4200,
            tempo_embarque=600,
            distancia=0,
        ),
        # LocalEmbarque nao cadastrado - usa o timezone da cidade anterior
        SimpleNamespace(
            local=SimpleNamespace(
                descricao="SANTO ANTONIO DE JESUS (BA)",
                external_local_id="177",
                external_cidade_id="2928703",
                uf="BA",
            ),
            datetime_ida=datetime.datetime(2021, 6, 5, 6, 40),
            duracao=3000,
            tempo_embarque=0,
            distancia=0,
        ),
    ]


def criar_mocks_itinerario(
    mocked_parsed_itinerario,
    company_feature_itinerario,
    external_datetime_ida,
    grupo_internal_id=12,
):
    company_id = company_feature_itinerario.company_internal_id
    modelo_venda = company_feature_itinerario.modelo_venda
    orchestrator = OrchestrateRodoviaria(company_id, modelo_venda)
    rota = baker.make("rodoviaria.Rota")
    grupo = baker.make(
        "rodoviaria.Grupo",
        company_integracao=company_feature_itinerario,
        external_datetime_ida=external_datetime_ida,
        grupo_internal_id=grupo_internal_id,
        rota=rota,
    )
    with mock.patch("rodoviaria.service.rota_svc._parsed_itinerario") as mock__parsed_itinerario:
        mocked_parsed_itinerario[0].datetime_ida = external_datetime_ida or STANDARDIZED_DATETIME_IDA
        mock__parsed_itinerario.return_value = grupo, mocked_parsed_itinerario
        _create_or_update_checkpoints_por_rota(orchestrator, rota.id)
    return SimpleNamespace(orchestrator=orchestrator, grupo=grupo)


def test_orchestrate_itinerario(mocked_parsed_itinerario, company_feature_itinerario):
    mocks = criar_mocks_itinerario(mocked_parsed_itinerario, company_feature_itinerario, STANDARDIZED_DATETIME_IDA)

    response = itinerario(mocks.orchestrator, mocks.grupo.grupo_internal_id)
    timezones = [
        "America/Sao_Paulo",
        "America/Cuiaba",
        "America/Cuiaba",
        "America/Cuiaba",
    ]
    locais_names = [
        "SAUIPE (BA)",
        "Feira de Santana",
        "Cruz das Almas",
        "SANTO ANTONIO DE JESUS (BA)",
    ]
    assert response[0]["departure"] == STANDARDIZED_DATETIME_IDA
    for index, checkpoint in enumerate(response):
        arrival_or_departure = checkpoint["arrival"] or checkpoint["departure"]
        assert arrival_or_departure.tzinfo.key == timezones[index]
        assert checkpoint["local"]["name"] == locais_names[index]


def test_orchestrate_itinerario_grupos_irmaos(mocked_parsed_itinerario, company_feature_itinerario):
    mocks = criar_mocks_itinerario(mocked_parsed_itinerario, company_feature_itinerario, STANDARDIZED_DATETIME_IDA)
    external_datetime_ida_irmao = STANDARDIZED_DATETIME_IDA + datetime.timedelta(days=1)
    mocks_irmao = criar_mocks_itinerario(
        mocked_parsed_itinerario,
        company_feature_itinerario,
        external_datetime_ida_irmao,
        grupo_internal_id=13,
    )
    response = itinerario(mocks.orchestrator, mocks.grupo.grupo_internal_id)
    response_irmao = itinerario(mocks_irmao.orchestrator, mocks_irmao.grupo.grupo_internal_id)
    assert response[0]["departure"] == STANDARDIZED_DATETIME_IDA
    assert response_irmao[0]["departure"] == external_datetime_ida_irmao


def test_orchestrate_itinerario_primeiro_checkpoint_timezone_cuiaba(
    mocked_parsed_itinerario, company_feature_itinerario
):
    baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=mocked_parsed_itinerario[0].local.external_local_id,
        cidade=baker.make(
            "rodoviaria.Cidade",
            name="SAUIPE - BA",
            timezone="America/Cuiaba",
            company=company_feature_itinerario,
        ),
    )
    CUIABA_DATETIME_IDA = to_tz(datetime.datetime(2021, 6, 5, 2, 30), "America/Cuiaba")
    mocks = criar_mocks_itinerario(mocked_parsed_itinerario, company_feature_itinerario, CUIABA_DATETIME_IDA)
    response = itinerario(mocks.orchestrator, mocks.grupo.grupo_internal_id)
    timezones = [
        "America/Cuiaba",
        "America/Cuiaba",
        "America/Cuiaba",
        "America/Cuiaba",
    ]
    locais_names = [
        "SAUIPE",
        "Feira de Santana",
        "Cruz das Almas",
        "SANTO ANTONIO DE JESUS (BA)",
    ]
    assert response[0]["departure"] == CUIABA_DATETIME_IDA
    for index, checkpoint in enumerate(response):
        arrival_or_departure = checkpoint["arrival"] or checkpoint["departure"]
        assert arrival_or_departure.tzinfo.key == timezones[index]
        assert checkpoint["local"]["name"] == locais_names[index]


def test_orchestrate_itinerario_primeiro_checkpoint_timezone_cuiaba_e_grupo_datetime_timezone_sao_saulo(
    mocked_parsed_itinerario, company_feature_itinerario
):
    baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=mocked_parsed_itinerario[0].local.external_local_id,
        cidade=baker.make(
            "rodoviaria.Cidade",
            name="SAUIPE - BA",
            timezone="America/Cuiaba",
            company=company_feature_itinerario,
        ),
    )
    mocks = criar_mocks_itinerario(mocked_parsed_itinerario, company_feature_itinerario, STANDARDIZED_DATETIME_IDA)
    response = itinerario(mocks.orchestrator, mocks.grupo.grupo_internal_id)
    timezones = [
        "America/Cuiaba",
        "America/Cuiaba",
        "America/Cuiaba",
        "America/Cuiaba",
    ]
    locais_names = [
        "SAUIPE",
        "Feira de Santana",
        "Cruz das Almas",
        "SANTO ANTONIO DE JESUS (BA)",
    ]
    assert response[0]["departure"] == to_tz(STANDARDIZED_DATETIME_IDA, "America/Cuiaba")
    for index, checkpoint in enumerate(response):
        arrival_or_departure = checkpoint["arrival"] or checkpoint["departure"]
        assert arrival_or_departure.tzinfo.key == timezones[index]
        assert checkpoint["local"]["name"] == locais_names[index]


def test_orchestrate_itinerario_grupo_without_external_datetime_ida(
    mocked_parsed_itinerario, company_feature_itinerario
):
    mocks = criar_mocks_itinerario(mocked_parsed_itinerario, company_feature_itinerario, None)
    response = itinerario(mocks.orchestrator, mocks.grupo.grupo_internal_id)
    assert response[0]["departure"] == to_tz(STANDARDIZED_DATETIME_IDA, "America/Sao_Paulo")


def test_orchestrate_itinerario_external_datetime_ida_different_from_checkpoint_datetime_ida(
    mocked_parsed_itinerario, company_feature_itinerario
):
    mocks = criar_mocks_itinerario(mocked_parsed_itinerario, company_feature_itinerario, DIFFERENT_DATETIME_IDA)
    response = itinerario(mocks.orchestrator, mocks.grupo.grupo_internal_id)
    assert response[0]["departure"] == to_tz(DIFFERENT_DATETIME_IDA, "America/Sao_Paulo")


def test_orchestrate_no_itinerario(company_feature_itinerario):
    company_id = company_feature_itinerario.company_internal_id
    modelo_venda = company_feature_itinerario.modelo_venda
    with mock.patch("rodoviaria.service.rota_svc._parsed_itinerario") as mock__parsed_itinerario:
        mock__parsed_itinerario.return_value = {}, []
        response = itinerario(OrchestrateRodoviaria(company_id, modelo_venda), {})
    assert response is None

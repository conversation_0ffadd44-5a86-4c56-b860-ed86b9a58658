import pytest
from pydantic import BaseModel

from rodoviaria.forms.types import DigitsOnlyStr


class MyModel(BaseModel):
    val: DigitsOnlyStr


def test_digits_only_field_value():
    model = MyModel(val="()abcd1234+-!? ")
    assert model.val == "1234"


class MyFieldMin(DigitsOnlyStr):
    min_length = 3


class MyModelMin(BaseModel):
    val: MyFieldMin


def test_digits_only_field_min_length_ok():
    model = MyModelMin(val="abc 123 def")
    assert model.val == "123"


def test_digits_only_field_min_length_error():
    with pytest.raises(ValueError) as exc_info:
        MyModelMin(val="abc 12 def")
    assert exc_info.value.errors() == [
        {
            "loc": ("val",),
            "msg": "certifique-se de que este valor tenha pelo menos 3 dígitos",
            "type": "value_error",
        }
    ]


class MyFieldMax(DigitsOnlyStr):
    max_length = 5


class MyModelMax(BaseModel):
    val: MyFieldMax


def test_digits_only_field_max_length_ok():
    model = MyModelMax(val="abc 12345 def")
    assert model.val == "12345"


def test_digits_only_field_max_length_error():
    with pytest.raises(ValueError) as exc_info:
        MyModelMax(val="abc 123456 def")
    assert exc_info.value.errors() == [
        {
            "loc": ("val",),
            "msg": "certifique-se de que este valor tenha no máximo 5 dígitos",
            "type": "value_error",
        }
    ]

import json
from datetime import datetime, time

import pytest
import time_machine
from model_bakery import baker
from zoneinfo import ZoneInfo

from rodoviaria import views
from rodoviaria.models.core import Company, CronogramaAtualizacaoOperacao, Integracao
from rodoviaria.service import atualiza_operacao_empresa_svc


def test_has_feature_without_features():
    features = []
    feature = Company.Feature.BUSCAR_SERVICO
    assert atualiza_operacao_empresa_svc.has_feature(features, feature) is False


def test_not_has_feature():
    features = [Company.Feature.ITINERARIO, Company.Feature.ADD_PAX_STAFF]
    feature = Company.Feature.BUSCAR_SERVICO
    assert atualiza_operacao_empresa_svc.has_feature(features, feature) is False


def test_has_feature():
    features = [
        Company.Feature.ITINERARIO,
        Company.Feature.ADD_PAX_STAFF,
        Company.Feature.BUSCAR_SERVICO,
    ]
    feature = Company.Feature.BUSCAR_SERVICO
    assert atualiza_operacao_empresa_svc.has_feature(features, feature) is True


def test_ops_trigger_time_company_internal_id_hard_coded():
    company_internal_id = 6247
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    minute, hour, day = atualiza_operacao_empresa_svc.ops_trigger_time(company_internal_id, modelo_venda)
    assert (minute, hour, day) == ("0", "8", "3")


def test_ops_trigger_time_without_company_internal_id():
    company_internal_id = None
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    minute, hour, day = atualiza_operacao_empresa_svc.ops_trigger_time(company_internal_id, modelo_venda)
    assert (minute, hour, day) == (None, None, None)


def test_ops_trigger_time_modelo_venda_hibrido():
    company_internal_id = 842
    modelo_venda = Company.ModeloVenda.HIBRIDO
    minute, hour, day = atualiza_operacao_empresa_svc.ops_trigger_time(company_internal_id, modelo_venda)
    assert (minute, hour, day) == (None, None, None)


def test_ops_trigger_time():
    company_internal_id = 842
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    minute, hour, day = atualiza_operacao_empresa_svc.ops_trigger_time(company_internal_id, modelo_venda)
    assert (minute, hour, day) == ("30", "18", "4")


@pytest.mark.parametrize(
    "features,expected_trigger_time,integracao_name",
    [
        pytest.param(
            (
                Company.Feature.ADD_PAX_STAFF,
                Company.Feature.BUSCAR_SERVICO,
                Company.Feature.ITINERARIO,
            ),
            ("32", "18", "0"),
            Integracao.API.TOTALBUS,
            id="atualizacao_normal",
        ),
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO),
            (None, None, None),
            Integracao.API.TOTALBUS,
            id="atualizacao_sem_feature_itinerario",
        ),
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            (None, None, None),
            Integracao.API.VEXADO,
            id="atualizacao_com_integracao_buscar_operacao_completa",
        ),
        pytest.param(
            (
                Company.Feature.ADD_PAX_STAFF,
                Company.Feature.BUSCAR_SERVICO,
                Company.Feature.ITINERARIO,
                Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ,
            ),
            ("32", "18", "0,3"),
            Integracao.API.TOTALBUS,
            id="atualizacao_alta_frequencia",
        ),
    ],
)
def test_descobrir_rotas_trigger_time(features, expected_trigger_time, integracao_name):
    company_internal_id = 238
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    minute, hour, day = atualiza_operacao_empresa_svc.descobrir_rotas_trigger_time(
        company_internal_id, modelo_venda, features, integracao_name
    )
    assert (minute, hour, day) == expected_trigger_time


@pytest.mark.parametrize(
    "features,expected_trigger_time,integracao_name",
    [
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            ("49", "5", "3"),
            Integracao.API.TOTALBUS,
            id="atualizacao_normal",
        ),
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO),
            (None, None, None),
            Integracao.API.TOTALBUS,
            id="atualizacao_sem_feature_itinerario",
        ),
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            (None, None, None),
            Integracao.API.VEXADO,
            id="atualizacao_com_integracao_buscar_operacao_completa",
        ),
        pytest.param(
            (
                Company.Feature.ADD_PAX_STAFF,
                Company.Feature.BUSCAR_SERVICO,
                Company.Feature.ITINERARIO,
                Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ,
            ),
            ("49", "5", "3,6"),
            Integracao.API.TOTALBUS,
            id="atualizacao_alta_frequencia",
        ),
    ],
)
def test_fetch_rotas_trigger_time(features, expected_trigger_time, integracao_name):
    company_internal_id = 711
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    minute, hour, day = atualiza_operacao_empresa_svc.fetch_rotas_trigger_time(
        company_internal_id, modelo_venda, features, integracao_name
    )
    assert (minute, hour, day) == expected_trigger_time


@pytest.mark.parametrize(
    "features,expected_trigger_time,integracao_name",
    [
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            ("49", "3", "4"),
            Integracao.API.TOTALBUS,
            id="atualizacao_normal",
        ),
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.ITINERARIO),
            (None, None, None),
            Integracao.API.TOTALBUS,
            id="atualizacao_sem_feature_buscar_servico",
        ),
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            (None, None, None),
            Integracao.API.VEXADO,
            id="atualizacao_com_integracao_buscar_operacao_completa",
        ),
        pytest.param(
            (
                Company.Feature.ADD_PAX_STAFF,
                Company.Feature.BUSCAR_SERVICO,
                Company.Feature.ITINERARIO,
                Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ,
            ),
            ("49", "3", "4,0"),
            Integracao.API.TOTALBUS,
            id="atualizacao_alta_frequencia",
        ),
    ],
)
def test_fetch_trechos_vendidos_trigger_time(features, expected_trigger_time, integracao_name):
    company_internal_id = 640
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    (
        minute,
        hour,
        day,
    ) = atualiza_operacao_empresa_svc.fetch_trechos_vendidos_trigger_time(
        company_internal_id, modelo_venda, features, integracao_name
    )
    assert (minute, hour, day) == expected_trigger_time


@pytest.mark.parametrize(
    "features,expected_trigger_time,integracao_name",
    [
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            (None, None, None),
            Integracao.API.TOTALBUS,
            id="atualizacao_normal",
        ),
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            ("49", "3", "*"),
            Integracao.API.VEXADO,
            id="atualizacao_com_integracao_buscar_operacao_completa",
        ),
    ],
)
def test_fetch_all_operation_trigger_time(features, expected_trigger_time, integracao_name):
    company_internal_id = 640
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    (
        minute,
        hour,
        day,
    ) = atualiza_operacao_empresa_svc.fetch_all_operation_trigger_time(
        company_internal_id, modelo_venda, features, integracao_name
    )
    assert (minute, hour, day) == expected_trigger_time


@pytest.mark.parametrize(
    "features,expected_trigger_time,integracao_name",
    [
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            ("6", "0", "1"),
            Integracao.API.TOTALBUS,
            id="atualizacao_normal",
        ),
        pytest.param(
            (Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO, Company.Feature.ITINERARIO),
            (None, None, None),
            Integracao.API.VEXADO,
            id="atualizacao_com_integracao_buscar_operacao_completa",
        ),
        pytest.param(
            (
                Company.Feature.ADD_PAX_STAFF,
                Company.Feature.BUSCAR_SERVICO,
                Company.Feature.ITINERARIO,
                Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ,
            ),
            ("6", "0", "1,4"),
            Integracao.API.TOTALBUS,
            id="atualizacao_alta_frequencia",
        ),
    ],
)
def test_fetch_data_limite_rotas_trigger_time(features, expected_trigger_time, integracao_name):
    company_internal_id = 320
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    (
        minute,
        hour,
        day,
    ) = atualiza_operacao_empresa_svc.fetch_trechos_vendidos_trigger_time(
        company_internal_id, modelo_venda, features, integracao_name
    )
    assert (minute, hour, day) == expected_trigger_time


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_empresas_integracao_automatica_rodar_hoje():
    baker.make(
        "rodoviaria.Company",
        company_internal_id=304,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        features=[Company.Feature.ACTIVE, Company.Feature.AUTO_INTEGRA_OPERACAO],
    )
    baker.make(
        "rodoviaria.Company",
        company_internal_id=321,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        features=[Company.Feature.ACTIVE, Company.Feature.AUTO_INTEGRA_OPERACAO],
    )
    baker.make(
        "rodoviaria.Company",
        company_internal_id=310,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
        features=[Company.Feature.ACTIVE, Company.Feature.AUTO_INTEGRA_OPERACAO],
    )
    baker.make(
        "rodoviaria.Company",
        company_internal_id=300,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        features=[Company.Feature.ACTIVE],
    )
    baker.make(
        "rodoviaria.Company",
        company_internal_id=307,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        features=[
            Company.Feature.ACTIVE,
            Company.Feature.AUTO_INTEGRA_OPERACAO,
            Company.Feature.ATUALIZA_OPERACAO_HIGH_FREQ,
        ],
    )
    retorno = atualiza_operacao_empresa_svc.get_empresas_integracao_automatica_rodar_hoje()
    assert retorno == [304, 307]


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, 1020, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_dia_semana_e_margem_horarios():
    assert atualiza_operacao_empresa_svc.get_dia_semana_e_margem_horarios() == (
        3,
        time(16, 40),
        time(16, 49, 59),
    )


@time_machine.travel(datetime(2022, 10, 19, 23, 55, 34, 1020, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_dia_semana_e_margem_horarios_logo_antes_meia_noite():
    assert atualiza_operacao_empresa_svc.get_dia_semana_e_margem_horarios() == (
        3,
        time(23, 50),
        time(23, 59, 59),
    )


@time_machine.travel(datetime(2022, 10, 20, 0, 5, 34, 1020, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_dia_semana_e_margem_horarios_logo_depois_meia_noite():
    assert atualiza_operacao_empresa_svc.get_dia_semana_e_margem_horarios() == (
        4,
        time(0, 0),
        time(0, 9, 59),
    )


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_empresas_integracao_automatica_agora():
    baker.make(  # roda_mesmo_dia_em_outro_horario
        CronogramaAtualizacaoOperacao,
        dia_semana=3,
        horario=time(16, 10),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA,
        company__features=[Company.Feature.AUTO_INTEGRA_OPERACAO],
    )
    baker.make(  # roda_outro_dia_no_mesmo_horario
        CronogramaAtualizacaoOperacao,
        dia_semana=4,
        horario=time(16, 45),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA,
        company__features=[Company.Feature.AUTO_INTEGRA_OPERACAO],
    )
    roda_mesmo_dia_no_mesmo_horario = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=3,
        horario=time(16, 45),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA,
        company__features=[Company.Feature.AUTO_INTEGRA_OPERACAO],
    )
    assert atualiza_operacao_empresa_svc.get_empresas_integracao_automatica_agora() == [
        roda_mesmo_dia_no_mesmo_horario.company.company_internal_id
    ]


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_empresas_integracao_automatica_agora_empresa_sem_feature():
    baker.make(  # roda_mesmo_dia_no_mesmo_horario
        CronogramaAtualizacaoOperacao,
        dia_semana=3,
        horario=time(16, 45),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA,
        company__features=[],
    )
    assert atualiza_operacao_empresa_svc.get_empresas_integracao_automatica_agora() == []


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_view_get_empresas_integracao_automatica_agora(rf):
    request = rf.get("/v1/auto_integra_operacao/get_ids_empresas_auto_integra_operacao_agora")
    roda_mesmo_dia_no_mesmo_horario = baker.make(
        CronogramaAtualizacaoOperacao,
        dia_semana=3,
        horario=time(16, 45),
        tipo_atualizacao=CronogramaAtualizacaoOperacao.Tipos.INTEGRACAO_AUTOMATICA,
        company__features=[Company.Feature.AUTO_INTEGRA_OPERACAO],
    )
    resp = views.get_ids_empresas_auto_integra_operacao_agora(request)
    assert json.loads(resp.content) == [roda_mesmo_dia_no_mesmo_horario.company.company_internal_id]

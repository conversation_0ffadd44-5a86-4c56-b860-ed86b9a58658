import logging

import pytest
from pybreaker import STAT<PERSON>_CLOSED, STATE_OPEN

from commons.circuit_breaker import MyCircuitBreakerError, _integracao_circuit_breaker, circuit_method
from rodoviaria.service.exceptions import RodoviariaConnectionError


def test_circuit_breaker_loga_mudanca_de_estado(caplog):
    circuit_breaker = _integracao_circuit_breaker("meu_circuit_breaker")
    with caplog.at_level(logging.INFO):
        circuit_breaker.open()
    assert caplog.records[0].message == "circuitbreaker meu_circuit_breaker state changed: closed -> open"


@pytest.mark.parametrize("exception,expected_fail_count", [(RodoviariaConnectionError, 1), (ValueError, 0)])
def test_circuit_breaker_exclude(exception, expected_fail_count):
    circuit_breaker = _integracao_circuit_breaker("meu_outro_circuit_breaker")

    @circuit_breaker
    def raise_exception():
        raise exception("falhou!")

    try:
        raise_exception()
    except exception:
        pass
    assert circuit_breaker.fail_counter == expected_fail_count


def test_circuit_method_decorator(time_machine):
    time_machine.move_to("2022-10-20 16:43:34", tick=False)

    class MinhaClasse:
        def __repr__(self):
            return "classuda"

        @circuit_method(fail_max=1, expected_exceptions=(ValueError,))
        def meu_metodo(self, arg):
            if arg == "marte":
                raise ValueError("argumento não aceito")
            return f"ola {arg}"

    circuit_breaker = _integracao_circuit_breaker("classuda_meu_metodo")
    meu_obj = MinhaClasse()
    resp = meu_obj.meu_metodo("mundo")
    # com o circuito fechado, a resposta do método é normal
    assert resp == "ola mundo"
    assert circuit_breaker.state.name == STATE_CLOSED
    # em caso de ValueError, o circuito abre
    with pytest.raises(MyCircuitBreakerError) as ex:
        meu_obj.meu_metodo("marte")
    assert ex.value.remaining_seconds == 20
    assert circuit_breaker.state.name == STATE_OPEN

    # com o circuito aberto, é retornado um CircuitBreakerError ao chamar o método
    time_machine.move_to("2022-10-20 16:43:44", tick=False)
    with pytest.raises(MyCircuitBreakerError) as ex:
        meu_obj.meu_metodo("plutão")
    assert ex.value.remaining_seconds == 10

from datetime import datetime, timedelta
from unittest import mock

import time_machine
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.forms.staff_forms import PaxForm, RemanejaPassageiroForm
from rodoviaria.models.core import Company, Passagem, Remanejamento
from rodoviaria.service import remanejamento_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import (
    HibridoEmissaoForaDaData,
    PassengerNotRegistered,
    RodoviariaException,
    RodoviariaTrechoBloqueadoException,
)
from rodoviaria.tests.totalbus import mocker as mocker_totalbus
from rodoviaria.views_schemas import RemanejaPassageirosAsyncParams


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_remaneja_passageiro(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    mock_totalbus_cancelar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is True
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    assert (
        Passagem.objects.get(
            travel_internal_id=buser_travel_ida_id,
            trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_origem.id,
        ).status
        == Passagem.Status.CANCELADA
    )
    assert (
        Passagem.objects.get(
            travel_internal_id=buser_travel_volta_id,
            trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_destino.id,
        ).status
        == Passagem.Status.CONFIRMADA
    )
    remanejamento = Remanejamento.objects.get(
        travel_destino_internal_id=params.travel_destino_id,
        travel_origem_internal_id=params.travel_id,
    )
    assert remanejamento.status == Remanejamento.Status.COMPLETO
    assert remanejamento.updated_at == timezone.now()


def test_remaneja_passageiro_compra_ok_cancelamento_fora_prazo(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    rodoviaria_trechoclasse_origem = totalbus_trechoclasses.ida
    rodoviaria_trechoclasse_origem.datetime_ida -= timedelta(hours=2)
    rodoviaria_trechoclasse_origem.save()
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    assert "cancelamento_pendente" in (
        Passagem.objects.get(
            travel_internal_id=buser_travel_volta_id,
            trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_destino.id,
            status=Passagem.Status.CONFIRMADA,
        ).tags_set()
    )
    assert (
        Passagem.objects.get(
            travel_internal_id=buser_travel_ida_id,
            trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_origem.id,
        ).status
        == Passagem.Status.CONFIRMADA
    )
    remanejamento = Remanejamento.objects.get(
        travel_destino_internal_id=params.travel_destino_id,
        travel_origem_internal_id=params.travel_id,
    )
    assert remanejamento.status == Remanejamento.Status.ERRO
    assert remanejamento.etapa_erro == Remanejamento.Etapas.CANCELAMENTO
    assert (
        remanejamento.erro
        == f"{remanejamento_svc.REMANEJAMENTO_LOG_TAG} O remanejamento só é possível pelo menos 3h antes do embarque do"
        " trecho do grupo de origem"
    )


def test_remaneja_passageiro_connection_error_na_compra_destino(
    mocker,
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    totalbus_login,
    requests_mock,
):
    mock_desbloquear_poltronas = mocker.patch.object(TotalbusAPI, "desbloquear_poltronas")
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=trechoclasse_origem.max_split_value,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=1,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    mock_desbloquear_poltronas.assert_called_once_with(trechoclasse_destino.id, [2])
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert "não movida externamente, por perda de conexão com a rodoviária" in resp["external_error_reason"]
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is True
    assert resp["blocked_travel_on_purchase"] is False
    remanejamento = Remanejamento.objects.get(
        travel_destino_internal_id=params.travel_destino_id,
        travel_origem_internal_id=params.travel_id,
    )
    assert remanejamento.status == Remanejamento.Status.ERRO
    assert remanejamento.etapa_erro == Remanejamento.Etapas.EMISSAO
    assert (
        remanejamento.erro
        == f"{remanejamento_svc.REMANEJAMENTO_LOG_TAG} Remanejamento de travel 2048->2049 não movida externamente, por"
        " perda de conexão com a rodoviária"
    )


def test_remaneja_passageiro_trecho_bloqueado_pelo_parceiro(
    mocker,
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    totalbus_login,
    requests_mock,
):
    mock_desbloquear_poltronas = mocker.patch.object(TotalbusAPI, "desbloquear_poltronas")
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=trechoclasse_origem.max_split_value,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=1,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    with mock.patch.object(
        CompraRodoviariaSVC,
        "efetua_compra",
    ) as mock_efetua_compra:
        mock_efetua_compra.side_effect = RodoviariaTrechoBloqueadoException
        resp = remanejamento_svc.remaneja_passageiro_sync(params)
    mock_desbloquear_poltronas.assert_called_once_with(trechoclasse_destino.id, [2])
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert "não movida externamente, por trecho bloqueado pelo parceiro" in resp["external_error_reason"]
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is True


def test_remaneja_passageiro_exception_no_cancelamento_origem(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert "não movida externamente" in resp["external_error_reason"]
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    assert "cancelamento_pendente" in (
        Passagem.objects.get(
            travel_internal_id=buser_travel_volta_id,
            trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_destino.id,
            status=Passagem.Status.CONFIRMADA,
        ).tags_set()
    )


def test_remaneja_passageiro_exception_generica_no_cancelamento_origem(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    with mock.patch(
        "rodoviaria.service.reserva_svc.efetua_cancelamento",
        side_effect=Exception("algum erro de código"),
    ):
        resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert (
        "não movida externamente, pela ocorrência de uma exception: algum erro de código"
        in resp["external_error_reason"]
    )
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    assert "cancelamento_pendente" in (
        Passagem.objects.get(
            travel_internal_id=buser_travel_volta_id,
            trechoclasse_integracao__trechoclasse_internal_id=trechoclasse_destino.id,
            status=Passagem.Status.CONFIRMADA,
        ).tags_set()
    )


def test_remaneja_passageiro_cancelamento_passenger_not_registered(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    with mock.patch(
        "rodoviaria.service.reserva_svc.efetua_cancelamento",
    ) as mock_efetua_cancelamento:
        mock_efetua_cancelamento.side_effect = PassengerNotRegistered
        resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is True
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_cancelamento_trecho_classe_origem_nao_encontrado(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id + 423,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    with mock.patch(
        "rodoviaria.service.reserva_svc.efetua_cancelamento",
    ) as mock_efetua_cancelamento:
        mock_efetua_cancelamento.side_effect = PassengerNotRegistered
        resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is True
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_empresa_destino_inexistente(totalbus_login, remanejamento_base_params):
    reservation_code = remanejamento_base_params.reservation_code
    resp = remanejamento_svc.remaneja_passageiro_sync(remanejamento_base_params)
    assert resp["reservation_code"] == reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is False
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_empresa_origem_inexistente(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    company_origem_id = 921
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=company_origem_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_empresa_destino_inativa(
    totalbus_trechoclasses, totalbus_login, empresa_ativa, remanejamento_base_params
):
    reservation_code = remanejamento_base_params.reservation_code
    rodoviaria_trechoclasse_destino = totalbus_trechoclasses.volta
    remanejamento_base_params.trechoclasse_destino_id = rodoviaria_trechoclasse_destino.trechoclasse_internal_id
    remanejamento_base_params.poltronas_destino = None
    rodoviaria_company_destino = rodoviaria_trechoclasse_destino.grupo.company_integracao
    rodoviaria_company_destino.features.remove("active")
    rodoviaria_company_destino.save()
    resp = remanejamento_svc.remaneja_passageiro_sync(remanejamento_base_params)
    assert resp["reservation_code"] == reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_empresa_origem_inativa(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    company_origem = baker.make(Company, company_internal_id=902)
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=company_origem.company_internal_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_empresa_origem_nao_integrada(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    modelo_venda_origem = Company.ModeloVenda.HIBRIDO
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=modelo_venda_origem,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_empresa_origem_hibrido(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    vexado_login,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    trechoclasse_origem.grupo.company_id = vexado_login.company.company_internal_id
    passagem_ze.company_integracao = vexado_login.company
    passagem_ze.save()
    modelo_venda_origem = Company.ModeloVenda.HIBRIDO
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=modelo_venda_origem,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is True
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    passagem_ze.refresh_from_db()
    assert passagem_ze.status == Passagem.Status.CANCELADA


def test_remaneja_passageiro_sem_empresa_origem(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=None,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=[2],
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_empresa_destino_nao_integrada(
    totalbus_trechoclasses, totalbus_login, empresa_ativa, remanejamento_base_params
):
    reservation_code = remanejamento_base_params.reservation_code
    rodoviaria_trechoclasse_destino = totalbus_trechoclasses.volta
    remanejamento_base_params.trechoclasse_destino_id = rodoviaria_trechoclasse_destino.trechoclasse_internal_id
    remanejamento_base_params.modelo_venda_destino = Company.ModeloVenda.HIBRIDO
    resp = remanejamento_svc.remaneja_passageiro_sync(remanejamento_base_params)
    assert resp["reservation_code"] == reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_empresa_destino_hibrido(
    totalbus_trechoclasses, empresa_ativa, remanejamento_base_params, vexado_login, mock_dispara_atualizacao_trecho
):
    vexado_trechosclasses = totalbus_trechoclasses
    vexado_trechosclasses.volta.grupo.company_integracao = vexado_login.company
    vexado_trechosclasses.volta.grupo.save()
    reservation_code = remanejamento_base_params.reservation_code
    rodoviaria_trechoclasse_destino = vexado_trechosclasses.volta
    remanejamento_base_params.trechoclasse_destino_id = rodoviaria_trechoclasse_destino.trechoclasse_internal_id
    remanejamento_base_params.modelo_venda_destino = Company.ModeloVenda.HIBRIDO
    remanejamento_base_params.company_destino_id = vexado_login.company.company_internal_id
    resp = remanejamento_svc.remaneja_passageiro_sync(remanejamento_base_params)
    assert resp["reservation_code"] == reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_exception_compra_destino(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    totalbus_login,
    empresa_ativa,
    remanejamento_base_params,
):
    remanejamento_base_params.company_destino_id = empresa_ativa.company_internal_id
    rodoviaria_trechoclasse_origem = totalbus_trechoclasses.ida
    rodoviaria_trechoclasse_origem.active = False
    rodoviaria_trechoclasse_origem.save()
    rodoviaria_trechoclasse_destino = totalbus_trechoclasses.volta
    remanejamento_base_params.trechoclasse_destino_id = rodoviaria_trechoclasse_destino.trechoclasse_internal_id
    reservation_code = remanejamento_base_params.reservation_code
    with mock.patch.object(CompraRodoviariaSVC, "efetua_compra") as mock_efetua_compra, mock.patch(
        "rodoviaria.service.staff_rodoviaria_svc.async_desbloquear_poltrona"
    ) as mock_async_desbloquear_poltrona:
        mock_efetua_compra.side_effect = RodoviariaException("algum erro")
        resp = remanejamento_svc.remaneja_passageiro_sync(remanejamento_base_params)
    assert resp["reservation_code"] == reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert (
        "não movida externamente, pela ocorrência de uma exception no serviço da rodoviária: algum erro"
        in resp["external_error_reason"]
    )
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    mock_async_desbloquear_poltrona.delay.assert_called_once_with(
        remanejamento_base_params.trechoclasse_destino_id,
        remanejamento_base_params.poltronas_destino,
    )


def test_remaneja_passageiro_emissao_hibrido_fora_da_data(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    totalbus_login,
    empresa_ativa,
    remanejamento_base_params,
):
    remanejamento_base_params.company_destino_id = empresa_ativa.company_internal_id
    rodoviaria_trechoclasse_origem = totalbus_trechoclasses.ida
    rodoviaria_trechoclasse_origem.active = False
    rodoviaria_trechoclasse_origem.save()
    rodoviaria_trechoclasse_destino = totalbus_trechoclasses.volta
    remanejamento_base_params.trechoclasse_destino_id = rodoviaria_trechoclasse_destino.trechoclasse_internal_id
    reservation_code = remanejamento_base_params.reservation_code
    with mock.patch.object(CompraRodoviariaSVC, "efetua_compra") as mock_efetua_compra:
        mock_efetua_compra.side_effect = HibridoEmissaoForaDaData
        resp = remanejamento_svc.remaneja_passageiro_sync(remanejamento_base_params)
    assert resp["reservation_code"] == reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is False
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False


def test_remaneja_passageiro_exception_generica_na_compra(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    totalbus_login,
    empresa_ativa,
    remanejamento_base_params,
):
    remanejamento_base_params.company_destino_id = empresa_ativa.company_internal_id
    rodoviaria_trechoclasse_origem = totalbus_trechoclasses.ida
    rodoviaria_trechoclasse_origem.active = False
    rodoviaria_trechoclasse_origem.save()
    rodoviaria_trechoclasse_destino = totalbus_trechoclasses.volta
    remanejamento_base_params.trechoclasse_destino_id = rodoviaria_trechoclasse_destino.trechoclasse_internal_id
    reservation_code = remanejamento_base_params.reservation_code
    with mock.patch.object(CompraRodoviariaSVC, "efetua_compra") as mock_efetua_compra, mock.patch(
        "rodoviaria.service.staff_rodoviaria_svc.async_desbloquear_poltrona"
    ) as mock_async_desbloquear_poltrona:
        mock_efetua_compra.side_effect = Exception("algum erro de código")
        resp = remanejamento_svc.remaneja_passageiro_sync(remanejamento_base_params)
    assert resp["reservation_code"] == reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert (
        "não movida externamente, pela ocorrência de uma exception: algum erro de código"
        in resp["external_error_reason"]
    )
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    mock_async_desbloquear_poltrona.delay.assert_called_once_with(
        remanejamento_base_params.trechoclasse_destino_id,
        remanejamento_base_params.poltronas_destino,
    )


def test_remaneja_passageiro_unlinked_trechoclasses(
    mocker,
    totalbus_trechoclasses,
    totalbus_login,
    empresa_ativa,
    remanejamento_base_params,
):
    baker.make(
        Company,
        company_internal_id=remanejamento_base_params.company_destino_id,
        features=[Company.Feature.ACTIVE],
    )
    remanejamento_base_params.company_origem_id = empresa_ativa.company_internal_id
    mock_desbloquear_poltronas = mocker.patch.object(TotalbusAPI, "desbloquear_poltronas")
    resp = remanejamento_svc.remaneja_passageiro_sync(remanejamento_base_params)
    assert resp["reservation_code"] == remanejamento_base_params.reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert "não movida externamente" in resp["external_error_reason"]
    assert resp["is_trechoclasse_origem_integrado"] is False
    assert resp["is_trechoclasse_destino_integrado"] is False
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    mock_desbloquear_poltronas.assert_not_called()


def test_remaneja_passageiro_sem_poltrona_destino(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_comprar,
    mock_totalbus_cancelar,
    mock_totalbus_get_poltronas,
    totalbus_login,
    passagem_ze,
    mock_dispara_atualizacao_trecho,
):
    preco = mocker_totalbus.BloquearPoltrona.response()["preco"]["preco"]
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=preco,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            )
        ],
        poltronas_destino=None,
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is True
    assert resp["external_cancelation_success"] is True
    assert resp["external_error_reason"] == ""
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is True
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    remanejamento = Remanejamento.objects.get(
        travel_destino_internal_id=params.travel_destino_id,
        travel_origem_internal_id=params.travel_id,
    )
    assert remanejamento.status == Remanejamento.Status.COMPLETO


def test_remaneja_passageiro_sem_poltrona_destino_overbooking(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    mock_totalbus_poltronas_insuficientes,
    totalbus_login,
    passagem_ze,
):
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=trechoclasse_origem.max_split_value,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            ),
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            ),
        ],
        poltronas_destino=None,
    )
    resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert "não movida externamente, por overbooking no trecho destino" in resp["external_error_reason"]
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is True
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is False
    remanejamento = Remanejamento.objects.get(
        travel_destino_internal_id=params.travel_destino_id,
        travel_origem_internal_id=params.travel_id,
    )
    assert remanejamento.status == Remanejamento.Status.ERRO
    assert remanejamento.etapa_erro == Remanejamento.Etapas.EMISSAO


def test_remaneja_passageiro_sem_poltrona_destino_erro_na_verificao_poltrona(
    totalbus_trechoclasses,
    totalbus_grupos_mockado,
    buser_buseiro_id,
    buser_travel_ida_id,
    buser_travel_volta_id,
    totalbus_login,
    requests_mock,
):
    trechoclasse_origem = totalbus_grupos_mockado.ida.trechoclasse
    trechoclasse_destino = totalbus_grupos_mockado.volta.trechoclasse
    params = RemanejaPassageiroForm(
        travel_id=buser_travel_ida_id,
        travel_destino_id=buser_travel_volta_id,
        reservation_code="STARK1",
        travel_max_split_value=trechoclasse_origem.max_split_value,
        trechoclasse_origem_id=trechoclasse_origem.id,
        trechoclasse_destino_id=trechoclasse_destino.id,
        company_origem_id=trechoclasse_origem.grupo.company_id,
        modelo_venda_origem=trechoclasse_origem.grupo.modelo_venda,
        company_destino_id=trechoclasse_destino.grupo.company_id,
        modelo_venda_destino=trechoclasse_destino.grupo.modelo_venda,
        passengers=[
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            ),
            PaxForm(
                id=buser_buseiro_id,
                name="Ze",
                rg_number="*********",
                cpf="11122233345",
                phone="(12)*********",
                tipo_documento="RG",
            ),
        ],
        poltronas_destino=None,
    )
    with mock.patch.object(
        CompraRodoviariaSVC,
        "verifica_poltrona",
    ) as mock_verifica_poltrona:
        mock_verifica_poltrona.side_effect = RodoviariaTrechoBloqueadoException
        resp = remanejamento_svc.remaneja_passageiro_sync(params)
    assert resp["reservation_code"] == params.reservation_code
    assert resp["external_purchase_success"] is False
    assert resp["external_cancelation_success"] is False
    assert "não movida externamente, por trecho bloqueado pelo parceiro" in resp["external_error_reason"]
    assert resp["is_trechoclasse_origem_integrado"] is True
    assert resp["is_trechoclasse_destino_integrado"] is True
    assert resp["internal_relocation_permission"] is False
    assert resp["overbooking_on_purchase"] is False
    assert resp["connection_error_on_purchase"] is False
    assert resp["blocked_travel_on_purchase"] is True
    remanejamento = Remanejamento.objects.get(
        travel_destino_internal_id=params.travel_destino_id,
        travel_origem_internal_id=params.travel_id,
    )
    assert remanejamento.status == Remanejamento.Status.ERRO
    assert remanejamento.etapa_erro == Remanejamento.Etapas.EMISSAO


def test_remaneja_passageiros_async(remanejamento_base_params):
    params = RemanejaPassageirosAsyncParams(remanejamentos=[remanejamento_base_params])
    with mock.patch("rodoviaria.service.remanejamento_svc._remaneja_passageiro") as mock_remaneja_passageiro:
        response = remanejamento_svc.remaneja_passageiros_async(params)
    remanejamento = Remanejamento.objects.get(
        travel_destino_internal_id=remanejamento_base_params.travel_destino_id,
        travel_origem_internal_id=remanejamento_base_params.travel_id,
    )
    mock_remaneja_passageiro.assert_called_once_with(remanejamento_base_params, remanejamento)
    assert remanejamento.status == Remanejamento.Status.PENDENTE
    assert response == {
        "message": "tasks de remanejamento disparadas.",
        "remanejamentos_ids": [remanejamento.id],
    }


def test_remanejamentos_pendentes_ou_completos_recentes_lista_vazia():
    grupo_id = 83219
    remanejamentos_pendentes_ou_completos_recentes = remanejamento_svc.remanejamentos_pendentes_ou_completos_recentes(
        grupo_id
    )
    assert remanejamentos_pendentes_ou_completos_recentes == {"remanejamentos": []}


def test_remanejamentos_pendentes_ou_completos_recentes():
    grupo_id = 83219
    remanejamento_pendente_1 = baker.make(
        Remanejamento,
        travel_destino_internal_id=3123,
        travel_origem_internal_id=8421,
        grupo_destino_internal_id=grupo_id,
        count_seats=1,
        status=Remanejamento.Status.PENDENTE,
    )
    remanejamento_pendente_2 = baker.make(
        Remanejamento,
        travel_destino_internal_id=1892,
        travel_origem_internal_id=6082,
        grupo_destino_internal_id=grupo_id,
        count_seats=2,
        status=Remanejamento.Status.PENDENTE,
    )
    remanejamento_completo_recente = baker.make(
        Remanejamento,
        travel_destino_internal_id=8127,
        travel_origem_internal_id=8432,
        grupo_destino_internal_id=grupo_id,
        count_seats=3,
        status=Remanejamento.Status.COMPLETO,
    )
    Remanejamento.objects.filter(pk=remanejamento_completo_recente.id).update(
        updated_at=timezone.now() - timedelta(minutes=2)
    )
    remanejamento_completo_antigo = baker.make(
        Remanejamento,
        travel_destino_internal_id=2456,
        travel_origem_internal_id=4213,
        grupo_destino_internal_id=grupo_id,
        count_seats=3,
        status=Remanejamento.Status.COMPLETO,
    )
    Remanejamento.objects.filter(pk=remanejamento_completo_antigo.id).update(
        updated_at=timezone.now() - timedelta(minutes=10)
    )
    remanejamentos_pendentes_ou_completos_recentes = remanejamento_svc.remanejamentos_pendentes_ou_completos_recentes(
        grupo_id
    )
    assert sorted(
        remanejamentos_pendentes_ou_completos_recentes["remanejamentos"], key=lambda k: k["count_seats"]
    ) == sorted(
        [
            {
                "travel_destino_internal_id": remanejamento_pendente_1.travel_destino_internal_id,
                "travel_origem_internal_id": remanejamento_pendente_1.travel_origem_internal_id,
                "status": remanejamento_pendente_1.status,
                "grupo_destino_internal_id": remanejamento_pendente_1.grupo_destino_internal_id,
                "count_seats": remanejamento_pendente_1.count_seats,
            },
            {
                "travel_destino_internal_id": remanejamento_pendente_2.travel_destino_internal_id,
                "travel_origem_internal_id": remanejamento_pendente_2.travel_origem_internal_id,
                "status": remanejamento_pendente_2.status,
                "grupo_destino_internal_id": remanejamento_pendente_2.grupo_destino_internal_id,
                "count_seats": remanejamento_pendente_2.count_seats,
            },
            {
                "travel_destino_internal_id": remanejamento_completo_recente.travel_destino_internal_id,
                "travel_origem_internal_id": remanejamento_completo_recente.travel_origem_internal_id,
                "status": remanejamento_completo_recente.status,
                "grupo_destino_internal_id": remanejamento_completo_recente.grupo_destino_internal_id,
                "count_seats": remanejamento_completo_recente.count_seats,
            },
        ],
        key=lambda k: k["count_seats"],
    )

from datetime import datetime, timedelta
from unittest import mock

from model_bakery import baker

from commons.dateutils import replace_timezone, to_default_tz
from rodoviaria.models.core import Company, Rotina
from rodoviaria.service import rotina_totalbus_async_svc


def test_fetch_rotina_async(
    totalbus_api,
    rota_totalbus,
    mock_buscar_todos_servicos_totalbus_response,
    totalbus_mock_buscar_itinerario_variable,
):
    with mock.patch("rodoviaria.service.rotina_totalbus_async_svc._fetch_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        first_day = to_default_tz(datetime.now()) - timedelta(days=2)
        to_be_inactivated_r = baker.make(
            Rotina,
            rota=rota_totalbus,
            datetime_ida=to_default_tz(datetime(2020, 5, 27, 5, 30, 0)),
        )  # rotina a ser inativada
        company_internal_id = totalbus_api.company.company_internal_id
        modelo_venda = Company.ModeloVenda.MARKETPLACE
        resp = rotina_totalbus_async_svc.fetch(
            company_internal_id,
            modelo_venda,
            rota=rota_totalbus,
            next_days=14,
            first_day=first_day,
        )
        rotinas_ativas = Rotina.objects.filter(rota_id=rota_totalbus.id, ativo=True).values_list(
            "datetime_ida", flat=True
        )
        assert len(rotinas_ativas) == 4
        to_be_inactivated_r.refresh_from_db()
        assert to_be_inactivated_r.ativo is False
        assert replace_timezone(datetime(2020, 5, 26, 8, 30), "UTC") in rotinas_ativas
        assert replace_timezone(datetime(2021, 11, 25, 10, 30), "UTC") in rotinas_ativas
        assert resp.status == "SUCCESS"


def test_fetch_rotina_async_with_queue(
    totalbus_api,
    rota_totalbus,
    mock_buscar_todos_servicos_totalbus_response,
    totalbus_mock_buscar_itinerario,
):
    company_interal_id = totalbus_api.company.company_internal_id
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    with mock.patch("rodoviaria.service.rotina_totalbus_async_svc._fetch_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        first_day = to_default_tz(datetime.now())
        rotina_totalbus_async_svc.fetch(
            company_interal_id,
            modelo_venda,
            rota=rota_totalbus,
            next_days=14,
            first_day=first_day,
            queue_name="my_queue",
        )
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 3


def test_fetch_parsed_itinerario(totalbus_api, totalbus_mock_buscar_itinerario):
    servico = "12345"
    data_viagem = "2022-05-05"
    company_internal_id = totalbus_api.company.company_internal_id
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    itinerario_parsed = rotina_totalbus_async_svc._fetch_parsed_itinerario(
        company_internal_id, modelo_venda, servico, data_viagem
    )
    assert itinerario_parsed[0].local.nome_cidade


def test_fetch_todos_servicos(totalbus_api, totalbus_mock_buscar_todos_servicos):
    company_internal_id = totalbus_api.company.company_internal_id
    modelo_venda = Company.ModeloVenda.MARKETPLACE
    todos_servicos_cleaned = rotina_totalbus_async_svc._fetch_todos_servicos(company_internal_id, modelo_venda)
    assert todos_servicos_cleaned[0]["numservico"]


def test_fetch_rotinas_empresa(
    totalbus_api,
    rota_totalbus,
    mock_buscar_todos_servicos_totalbus_response,
    totalbus_mock_buscar_itinerario,
):
    with mock.patch("rodoviaria.service.rotina_totalbus_async_svc._fetch_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        resp = rotina_totalbus_async_svc.fetch_rotinas_empresa(
            client=totalbus_api.login,
            company_id=rota_totalbus.company_id,
            next_days=14,
            first_day=to_default_tz(datetime.now()),
        )
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 3
        assert replace_timezone(datetime(2020, 5, 26, 8, 30), "UTC") in rotinas
        assert replace_timezone(datetime(2021, 11, 25, 10, 30), "UTC") in rotinas
        assert resp[0].status == "SUCCESS"


def test_fetch_rotinas_empresa_rota_inativa(totalbus_api, rota_totalbus, mock_buscar_todos_servicos_totalbus_response):
    rota_totalbus.ativo = False
    rota_totalbus.save()
    with mock.patch("rodoviaria.service.rotina_totalbus_async_svc._fetch_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id, ativo=True).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 2
        resp = rotina_totalbus_async_svc.fetch_rotinas_empresa(
            client=totalbus_api.login,
            company_id=rota_totalbus.company_id,
            next_days=14,
            first_day=to_default_tz(datetime.now()),
        )
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id, ativo=True).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 2
        assert len(resp) == 0


def test_fetch_rotinas_empresa_rota_sem_rotinas_encontradas(
    totalbus_api,
    rota_totalbus,
    mock_buscar_todos_servicos_totalbus_response,
    totalbus_mock_buscar_itinerario_nao_localizado,
):
    with mock.patch("rodoviaria.service.rotina_totalbus_async_svc._fetch_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id, ativo=True).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 2
        resp = rotina_totalbus_async_svc.fetch_rotinas_empresa(
            client=totalbus_api.login,
            company_id=rota_totalbus.company_id,
            next_days=14,
            first_day=to_default_tz(datetime.now()),
        )
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id, ativo=True).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 1
        assert len(resp) == 1
        rota_totalbus.refresh_from_db()
        assert rota_totalbus.ativo is False


def test_fetch_rotinas_empresa_rota_sem_rotinas_encontradas_mas_nao_inativa_rota(
    totalbus_api,
    rota_totalbus,
    mock_buscar_todos_servicos_totalbus_response,
    totalbus_mock_buscar_itinerario_nao_localizado,
):
    with mock.patch("rodoviaria.service.rotina_totalbus_async_svc._fetch_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id, ativo=True).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 2
        resp = rotina_totalbus_async_svc.fetch_rotinas_empresa(
            client=totalbus_api.login,
            company_id=rota_totalbus.company_id,
            next_days=7,
            first_day=to_default_tz(datetime.now()),
        )
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id, ativo=True).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 2
        assert len(resp) == 1
        rota_totalbus.refresh_from_db()
        assert rota_totalbus.ativo


def test_fetch_rotinas_empresa_with_queue(
    totalbus_api,
    rota_totalbus,
    mock_buscar_todos_servicos_totalbus_response,
    totalbus_mock_buscar_itinerario,
):
    with mock.patch("rodoviaria.service.rotina_totalbus_async_svc._fetch_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotina_totalbus_async_svc.fetch_rotinas_empresa(
            client=totalbus_api.login,
            company_id=rota_totalbus.company_id,
            next_days=14,
            first_day=to_default_tz(datetime.now()),
            queue_name="my_queue",
        )
        rotinas = Rotina.objects.filter(rota_id=rota_totalbus.id).values_list("datetime_ida", flat=True)
        assert len(rotinas) == 3

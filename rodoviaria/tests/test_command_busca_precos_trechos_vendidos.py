from datetime import datetime
from decimal import Decimal

import pytest
import time_machine
from celery.exceptions import Retry
from django.core.management import call_command
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz
from rodoviaria.models.core import (
    RotinaTrechoVendido,
    TrechoVendido,
)
from rodoviaria.service.busca_precos_trechos_vendidos_svc import busca_preco_trecho_vendido
from rodoviaria.tests.eulabs.mock_data_response import mock_travels


def test_busca_precos_trechos_vendidos_sem_nenhum_trecho_vendido_pendente(mocker):
    mock_task = mocker.patch("rodoviaria.service.busca_precos_trechos_vendidos_svc.busca_preco_trecho_vendido")
    call_command("busca_precos_trechos_vendidos")
    mock_task.delay.assert_not_called()


def test_busca_precos_trechos_vendidos_sem_rotina_trecho_vendido(eulabs_company):
    tv = baker.make(
        TrechoVendido,
        origem__id_external="1",
        destino__id_external="2",
        status_preco=TrechoVendido.StatusPreco.PENDENTE,
        rota__company=eulabs_company,
        ativo=False,
    )
    call_command("busca_precos_trechos_vendidos")
    tv.refresh_from_db()
    assert tv.ativo is False
    assert tv.status_preco == TrechoVendido.StatusPreco.IMPOSSIVEL_BUSCAR


@time_machine.travel(datetime(2022, 10, 10, 0, 0, 0, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_busca_precos_trechos_vendidos_connection_error(requests_mock, eulabs_api):
    viagem_api = mock_travels.corridas[0]
    tv = baker.make(
        TrechoVendido,
        origem__id_external="1",
        destino__id_external="2",
        status_preco=TrechoVendido.StatusPreco.PENDENTE,
        rota__company=eulabs_api.company,
        classe=viagem_api["items"][0]["tariffs"][0]["category"]["description"],
        ativo=False,
    )
    datetime_ida = to_default_tz(datetime(2024, 10, 15, 19, 0))
    baker.make(RotinaTrechoVendido, trecho_vendido=tv, datetime_ida_trecho_vendido=datetime_ida)
    with pytest.raises(Retry):
        call_command("busca_precos_trechos_vendidos")
    tv.refresh_from_db()
    assert tv.ativo is False
    assert tv.status_preco == TrechoVendido.StatusPreco.PENDENTE


@time_machine.travel(datetime(2022, 10, 10, 0, 0, 0, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_busca_precos_trechos_vendidos_nao_encontra_trecho_por_horario(eulabs_mock_buscar_corridas, eulabs_api):
    viagem_api = mock_travels.corridas[0]
    tv = baker.make(
        TrechoVendido,
        origem__id_external="1",
        destino__id_external="2",
        status_preco=TrechoVendido.StatusPreco.PENDENTE,
        rota__company=eulabs_api.company,
        classe=viagem_api["items"][0]["tariffs"][0]["category"]["description"],
        ativo=False,
    )
    datetime_ida = to_default_tz(datetime(2024, 10, 15, 19, 0))
    baker.make(RotinaTrechoVendido, trecho_vendido=tv, datetime_ida_trecho_vendido=datetime_ida)
    call_command("busca_precos_trechos_vendidos")
    tv.refresh_from_db()
    assert tv.ativo is False
    assert tv.status_preco == TrechoVendido.StatusPreco.NAO_ENCONTRADO


@time_machine.travel(datetime(2021, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_busca_precos_trechos_vendidos_nao_encontra_trecho_por_classe(eulabs_mock_buscar_corridas, eulabs_api):
    viagem_api = mock_travels.corridas[0]
    tv = baker.make(
        TrechoVendido,
        origem__id_external="1",
        destino__id_external="2",
        status_preco=TrechoVendido.StatusPreco.PENDENTE,
        rota__company=eulabs_api.company,
        classe="NAO_EXISTENTE",
        ativo=False,
    )
    datetime_ida = to_default_tz(datetime.strptime(viagem_api["items"][0]["datetime_departure"], "%Y-%m-%d %H:%M:%S"))
    baker.make(RotinaTrechoVendido, trecho_vendido=tv, datetime_ida_trecho_vendido=datetime_ida)
    call_command("busca_precos_trechos_vendidos")
    tv.refresh_from_db()
    assert tv.ativo is False
    assert tv.status_preco == TrechoVendido.StatusPreco.NAO_ENCONTRADO


@time_machine.travel(datetime(2021, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_busca_precos_trechos_vendidos_encontra_preco(eulabs_mock_buscar_corridas, eulabs_api):
    viagem_api = mock_travels.corridas[0]
    tv = baker.make(
        TrechoVendido,
        origem__id_external="1",
        destino__id_external="2",
        status_preco=TrechoVendido.StatusPreco.PENDENTE,
        rota__company=eulabs_api.company,
        classe=viagem_api["items"][0]["tariffs"][0]["category"]["description"],
        origem__cidade__cidade_internal__timezone="America/Sao_Paulo",
        ativo=False,
    )
    datetime_ida = to_default_tz(datetime.strptime(viagem_api["items"][0]["datetime_departure"], "%Y-%m-%d %H:%M:%S"))
    baker.make(RotinaTrechoVendido, trecho_vendido=tv, datetime_ida_trecho_vendido=datetime_ida)
    call_command("busca_precos_trechos_vendidos")
    tv.refresh_from_db()
    assert tv.ativo is True
    assert tv.status_preco == TrechoVendido.StatusPreco.OK
    assert tv.preco == Decimal(str(viagem_api["items"][0]["tariffs"][0]["amount"]))


def test_busca_preco_trecho_vendido_com_preco_nao_pendente():
    trecho_vendido = baker.make(TrechoVendido, status_preco=TrechoVendido.StatusPreco.NAO_ENCONTRADO)
    assert busca_preco_trecho_vendido(trecho_vendido.id) is None
    trecho_vendido.refresh_from_db()
    assert trecho_vendido.status_preco == TrechoVendido.StatusPreco.NAO_ENCONTRADO

import json
import random
from datetime import datetime, timedelta
from decimal import Decimal as D
from itertools import cycle
from types import SimpleNamespace
from unittest import mock

import pytest
from django.db import connections
from model_bakery import baker

from commons.dateutils import to_default_tz
from rodoviaria.api.forms import CheckpointsForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.models.core import Checkpoint, Company, LocalEmbarque, Rotina, TrechoVendido
from rodoviaria.service.exceptions import RodoviariaUnsupportedFeatureException
from rodoviaria.service.rota_svc import (
    _create_or_update_checkpoints_por_rota,
    _get_checkpoints_por_grupo,
    _get_map_trechos_vendidos_por_rota,
    _parsed_itinerario,
    atualizar_checkpoints_rotas_por_empresa,
    create_or_update_rota,
    filtrar_apenas_checkpoints_integrados,
    get_detalhes_para_integrar_uma_rota,
    get_ids_rotas_ativas_empresa,
    get_ids_rotas_integradas_inativas,
    get_rotas_ativas_para_reintegrar,
    get_rotas_novas_empresas_para_integrar,
    get_timezone_inicio_itinerario,
    itinerario,
    itinerario_parsed_data,
    update_trechos_vendidos_internal_ids,
)


@pytest.fixture
def company_unbounded():
    return baker.make("rodoviaria.Company")


@pytest.fixture
def orchestrator_mock(company_unbounded):
    orchestrator = OrchestrateRodoviaria(company_unbounded.company_internal_id, company_unbounded.modelo_venda)
    orchestrator._provider = mock.Mock(company=company_unbounded)
    return orchestrator


@pytest.fixture
def itinerario_vexado():
    return SimpleNamespace(
        parsed=SimpleNamespace(hash="Adk23sad2sd231SdkmSDas2"),
        cleaned=[
            {
                "cidade": {
                    "id": 6036,
                    "uf": "BA",
                    "descricaoUf": "Bahia",
                    "nome": "Salvador",
                    "nomeComUf": "Salvador - BA",
                    "codigoIbge": "2927408",
                },
                "ordem": 1,
                "duracao": "00:00",
                "datetime_ida": "2021-09-03 17:00",
            },
            {
                "cidade": {
                    "id": 6037,
                    "uf": "BA",
                    "descricaoUf": "Bahia",
                    "nome": "Ilheus",
                    "nomeComUf": "Ilheus - BA",
                    "codigoIbge": "2913606",
                },
                "ordem": 2,
                "duracao": "10:00",
                "datetime_ida": "2021-09-03 21:00",
            },
        ],
    )


itinerario_totalbus_data_errada = [
    {
        "localidade": {"id": 17674, "cidade": "BRUSQUE - SC", "uf": "SC"},
        "distancia": "27.0",
        "permanencia": "00:00",
        "data": "2021-07-09",
        "hora": "18:00",
    },
    {
        "localidade": {"id": 17676, "cidade": "GASPAR - SC", "uf": "SC"},
        "distancia": "16.0",
        "permanencia": "00:00",
        "data": "2021-06-09",
        "hora": "18:35",
    },
]

itinerario_totalbus_hora_errada = [
    {
        "localidade": {"id": 17674, "cidade": "BRUSQUE - SC", "uf": "SC"},
        "distancia": "27.0",
        "permanencia": "00:00",
        "data": "2021-06-09",
        "hora": "18:00",
    },
    {
        "localidade": {"id": 17676, "cidade": "GASPAR - SC", "uf": "SC"},
        "distancia": "16.0",
        "permanencia": "00:00",
        "data": "2021-06-09",
        "hora": "18:35",
    },
    {
        "localidade": {"id": 17678, "cidade": "BLUMENAU - SC", "uf": "SC"},
        "distancia": "16.0",
        "permanencia": "00:00",
        "data": "2021-06-09",
        "hora": "18:05",
    },
    {
        "localidade": {"id": 17680, "cidade": "INDAIAL - SC", "uf": "SC"},
        "distancia": "16.0",
        "permanencia": "00:00",
        "data": "2021-06-09",
        "hora": "19:35",
    },
]

itinerario_totalbus_parada_rapida = [
    {
        "localidade": {"id": 17674, "cidade": "BRUSQUE - SC", "uf": "SC"},
        "distancia": "27.0",
        "permanencia": "00:00",
        "data": "2021-07-09",
        "hora": "18:00",
    },
    {
        "localidade": {"id": 17676, "cidade": "GASPAR - SC", "uf": "SC"},
        "distancia": "16.0",
        "permanencia": "00:00",
        "data": "2021-07-09",
        "hora": "18:10",
    },
    {
        "localidade": {"id": 12723, "cidade": "FOZ DO IGUACU - PR", "uf": "PR"},
        "distancia": "0.00",
        "permanencia": "00:00",
        "data": "2021-07-10",
        "hora": "18:50",
    },
]


def test_create_or_update_rota_sem_id_external(vexado_company, vexado_login, itinerario_vexado):
    orchestrator = OrchestrateRodoviaria(vexado_company.company_internal_id, vexado_company.modelo_venda)

    with mock.patch.object(VexadoAPI, "rota_id_from_trecho_classe") as mock_rota_id_from_trecho_classe:
        mock_rota_id_from_trecho_classe.return_value = "Abc a Def"
        rota, _ = create_or_update_rota(
            orchestrator,
            vexado_company.id,
            itinerario_vexado,
            trecho_classe=SimpleNamespace(external_id=92123),
        )
    assert rota.id_external == "Abc a Def"
    rotinas = Rotina.objects.filter(rota_id=rota.id).all()
    assert len(rotinas) == 1
    assert rotinas[0].ativo


def test_create_or_update_rota_com_id_external(vexado_company, vexado_login, itinerario_vexado):
    orchestrator = OrchestrateRodoviaria(vexado_company.company_internal_id, vexado_company.modelo_venda)

    rota, _ = create_or_update_rota(
        orchestrator,
        vexado_company.id,
        itinerario_vexado,
        id_external=92123,
    )
    assert rota.id_external == 92123
    rotinas = Rotina.objects.filter(rota_id=rota.id).all()
    assert len(rotinas) == 1
    assert rotinas[0].ativo


def test_create_or_update_rota_rotina_inativa(vexado_company, vexado_login, itinerario_vexado):
    orchestrator = OrchestrateRodoviaria(vexado_company.company_internal_id, vexado_company.modelo_venda)

    rota, _ = create_or_update_rota(
        orchestrator,
        vexado_company.id,
        itinerario_vexado,
    )
    rotina = Rotina.objects.get(rota_id=rota.id)
    rotina.ativo = False
    rotina.save()
    # ao buscar uma rota, se encontrar uma rotina existente no bd, reativa.
    create_or_update_rota(
        orchestrator,
        vexado_company.id,
        itinerario_vexado,
    )
    rotina.refresh_from_db()
    assert rotina.ativo is True


def test_create_or_update_checkpoints_por_rota(totalbus_company, totalbus_login, totalbus_trechoclasses):
    orchestrator = OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda)
    cidade1 = baker.make(
        "rodoviaria.Cidade",
        id_external=18852,
        name="PENAPOLIS",
        company=totalbus_company,
    )
    cidade2 = baker.make(
        "rodoviaria.Cidade",
        id_external=19105,
        name="SAO JOSE DO RIO PRETO",
        company=totalbus_company,
    )
    local1 = baker.make(
        "rodoviaria.LocalEmbarque",
        nickname="PENAPOLIS - SP",
        id_external=18852,
        local_embarque_internal_id=123,
        cidade=cidade1,
    )
    local2 = baker.make(
        "rodoviaria.LocalEmbarque",
        nickname="SAO JOSE DO RIO PRETO - SP",
        id_external=19105,
        cidade=cidade2,
    )
    provider_data = [
        {
            "localidade": {
                "id": local1.id_external,
                "cidade": "PENAPOLIS - SP",
                "uf": "SP",
            },
            "distancia": "108.8",
            "permanencia": "00:00",
            "data": "2021-11-25",
            "hora": "10:30",
        },
        {
            "localidade": {
                "id": local2.id_external,
                "cidade": "SAO JOSE DO RIO PRETO - SP",
                "uf": "SP",
            },
            "distancia": "0.00",
            "permanencia": "00:00",
            "data": "2021-11-25",
            "hora": "12:15",
        },
    ]

    rota = baker.make("rodoviaria.Rota", provider_data=json.dumps(provider_data))

    checkpoints = _create_or_update_checkpoints_por_rota(orchestrator, rota.id)

    assert checkpoints
    assert checkpoints[0].rota_id == rota.id
    assert checkpoints[0].local_id == local1.id
    assert checkpoints[0].id_external == local1.id_external

    assert checkpoints[1].rota_id == rota.id
    assert checkpoints[1].local_id == local2.id
    assert checkpoints[1].id_external == local2.id_external

    assert checkpoints[0].id != checkpoints[1].id


def test_create_or_update_checkpoints_por_rota_com_data_errada(rf, totalbus_company, totalbus_login):
    orchestrator = OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda)
    rota = baker.make(
        "rodoviaria.RotaTotalBus",
        company=totalbus_company,
        provider_data=json.dumps(itinerario_totalbus_data_errada),
    )
    totalbus_company.features = ["itinerario"]
    totalbus_company.save()

    checkpoints = _create_or_update_checkpoints_por_rota(orchestrator, rota.id)

    assert checkpoints
    assert checkpoints[1].duracao > timedelta(days=0)
    # assert resp['items']
    # assert resp['items'][0]['checkpoints'][1]['duracao'] > 0


def test_create_or_update_checkpoints_por_rota_com_hora_errada(rf, totalbus_company, totalbus_login):
    orchestrator = OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda)
    rota = baker.make(
        "rodoviaria.RotaTotalBus",
        company=totalbus_company,
        provider_data=json.dumps(itinerario_totalbus_hora_errada),
    )
    totalbus_company.features = ["itinerario"]
    totalbus_company.save()

    checkpoints = _create_or_update_checkpoints_por_rota(orchestrator, rota.id)

    assert checkpoints
    assert checkpoints[2].duracao == timedelta(seconds=1200)
    # assert resp['items']
    # assert resp['items'][0]['checkpoints'][1]['duracao'] > 0


def test_create_or_update_checkpoints_por_rota_parada_rapida_deveria_add_tempo_embarque(
    rf, totalbus_company, totalbus_login
):
    orchestrator = OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda)
    rota = baker.make(
        "rodoviaria.RotaTotalBus",
        company=totalbus_company,
        provider_data=json.dumps(itinerario_totalbus_parada_rapida),
    )
    totalbus_company.features = ["itinerario"]
    totalbus_company.save()

    checkpoints = _create_or_update_checkpoints_por_rota(orchestrator, rota.id)

    assert checkpoints
    assert checkpoints[1].tempo_embarque == timedelta(milliseconds=60000)
    assert checkpoints[1].duracao == timedelta(milliseconds=540000)


def test_get_checkpoints_from_rota_comparacao_dicts_totalbus(totalbus_company, totalbus_login, rota_totalbus):
    _test_get_checkpoints_from_rota_comparacao_dicts(totalbus_company, rota_totalbus)


def test_get_checkpoints_from_rota_comparacao_dicts_praxio(praxio_company, praxio_login, rota_praxio):
    _test_get_checkpoints_from_rota_comparacao_dicts(praxio_company, rota_praxio)


def _test_get_checkpoints_from_rota_comparacao_dicts(company, rota):
    orchestrator = OrchestrateRodoviaria(company.company_internal_id, company.modelo_venda)

    _create_or_update_checkpoints_por_rota(orchestrator, rota.id)

    checkpoints_from_db = rota.get_itinerario()
    checkpoints_from_provider_data = CheckpointsForm.from_itinerario(
        company.id, itinerario_parsed_data(orchestrator, rota_id=rota.id)
    )

    from_db = [c.to_dict_json() for c in checkpoints_from_db]

    # o from_db já converte pra millisegundos. from_provider_data precisa de tratamento adicional
    from_provider_data = []
    for c in checkpoints_from_provider_data:
        c.duracao = c.duracao * 1000
        c.tempo_embarque = c.tempo_embarque * 1000
        from_provider_data.append(c.dict())

    assert_compare_dicts_from_db_and_provider_data(from_db, from_provider_data)


def assert_compare_dicts_from_db_and_provider_data(from_db, from_provider_data):
    assert len(from_db) == len(from_provider_data)
    for i in range(1, 3):
        assert from_db[i]["local_id"] == from_provider_data[i]["local_id"]
        assert from_db[i]["cidade_id"] == from_provider_data[i]["cidade_id"]
        assert from_db[i]["arrival"] == from_provider_data[i]["arrival"]
        assert from_db[i]["departure"] == from_provider_data[i]["departure"]
        assert from_db[i]["duracao"] == from_provider_data[i]["duracao"]
        assert from_db[i]["distancia_km"] == from_provider_data[i]["distancia_km"]
        assert from_db[i]["tempo_embarque"] == from_provider_data[i]["tempo_embarque"]
        assert from_db[i]["local"]["name"] == from_provider_data[i]["local"]["name"]
        assert from_db[i]["local"]["nickname"] == from_provider_data[i]["local"]["nickname"]
        assert from_db[i]["local"]["uf"] == from_provider_data[i]["local"]["uf"]
        assert from_db[i]["local"]["id_external"] == str(from_provider_data[i]["local"]["id_external"])


def test_get_checkpoints_por_grupo(totalbus_company, totalbus_login, rota_totalbus):
    orchestrator = OrchestrateRodoviaria(totalbus_company.company_internal_id, totalbus_company.modelo_venda)
    _create_or_update_checkpoints_por_rota(orchestrator, rota_totalbus.id)
    grupo = baker.make("rodoviaria.Grupo", rota_id=rota_totalbus.id, grupo_internal_id=99)

    checkpoints = list(_get_checkpoints_por_grupo(grupo.grupo_internal_id))
    from_db = [c.to_dict_json() for c in checkpoints]
    assert "PENAPOLIS" in from_db[0]["local"]["name"]
    assert from_db[0]["local"]["id_external"] == "321"


def test_itinerario_buscando_da_tabela_checkpoint(praxio_grupos, praxio_login, praxio_company, rota_praxio):
    orchestrator = OrchestrateRodoviaria(praxio_company.company_internal_id, praxio_company.modelo_venda)
    praxio_company.features = ["itinerario"]
    praxio_company.save()
    praxio_grupos.ida.rota = rota_praxio
    praxio_grupos.ida.save()
    _create_or_update_checkpoints_por_rota(orchestrator, rota_praxio.id)
    result_for_mock = _parsed_itinerario(orchestrator, praxio_grupos.ida.grupo_internal_id)

    with mock.patch("rodoviaria.service.rota_svc._parsed_itinerario") as _parsed_itinerario_mock:
        _parsed_itinerario_mock.return_value = result_for_mock
        iti = list(itinerario(orchestrator, praxio_grupos.ida.grupo_internal_id))

        assert _parsed_itinerario_mock.call_count == 0
        assert iti[0]["local"]["name"] == "Sauipe - BA"


def test_atualizar_checkpoints_rotas_por_empresa(totalbus_login, rota_totalbus):
    rota_totalbus.company = totalbus_login.company
    rota_totalbus.save()

    orchestrator = OrchestrateRodoviaria(rota_totalbus.company.company_internal_id, rota_totalbus.company.modelo_venda)

    cks = _create_or_update_checkpoints_por_rota(orchestrator, rota_totalbus.id)

    assert cks[0].nickname == "PENAPOLIS - SP"
    assert cks[0].local is None

    local = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=cks[0].id_external,
        cidade=baker.make(
            "rodoviaria.Cidade",
            company=rota_totalbus.company,
            name="TEsTE",
        ),
        nickname="PENAPOLIS - SP",
    )

    atualizar_checkpoints_rotas_por_empresa(
        totalbus_login.company.company_internal_id, totalbus_login.company.modelo_venda
    )

    cks[0].refresh_from_db()

    assert cks[0].nickname == "PENAPOLIS - SP"
    assert cks[0].local is not None
    assert cks[0].local_id == local.id


def test_atualizar_checkpoints_rotas_por_empresa_empresa_hibrido(totalbus_login, rota_totalbus):
    totalbus_login.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_login.company.save()
    rota_totalbus.company = totalbus_login.company
    rota_totalbus.save()
    with pytest.raises(Company.DoesNotExist):
        atualizar_checkpoints_rotas_por_empresa(
            totalbus_login.company.company_internal_id, Company.ModeloVenda.MARKETPLACE
        )


def test_atualizar_checkpoints_rotas_por_empresa_sem_rotas(totalbus_login):
    atualizar_checkpoints_rotas_por_empresa(
        totalbus_login.company.company_internal_id, totalbus_login.company.modelo_venda
    )
    all = Checkpoint.objects.all()
    assert len(all) == 0


def test_company_supported_feature(orchestrator_mock, company_unbounded):
    company_unbounded.features = ["itinerario"]
    # Executa a função mas não tem grupo -1.
    assert itinerario(orchestrator_mock, -1) is None


def test_company_unsupported_feature(orchestrator_mock, company_unbounded):
    with pytest.raises(RodoviariaUnsupportedFeatureException):
        itinerario(orchestrator_mock, 1)


def criar_cidades_rodoviaria(company_id):
    cidades = []
    ufs = ["RS", "SC", "PR", "MT"]
    for idx in range(1, 5):
        cidade_internal = baker.make(
            "rodoviaria.CidadeInternal",
            name="NAME",
            sigla="SIG",
            city_code_ibge=random.randint(1, 9000) + idx,
            uf=ufs[idx - 1],
            timezone="America/Sao_Paulo",
        )
        cidades.append(
            baker.make(
                "rodoviaria.Cidade",
                company_id=company_id,
                name=cidade_internal.name,
                cidade_internal=cidade_internal,
                timezone=cidade_internal.timezone,
            )
        )
    return cidades


def criar_locais_embarque_rodoviaria(company_id):
    cidades_rodoviaria = criar_cidades_rodoviaria(company_id)
    locais = []
    for idx, cidade in enumerate(cidades_rodoviaria):
        locais.append(
            baker.make(
                "rodoviaria.LocalEmbarque",
                cidade=cidade,
                id_external=9000 + idx,
                local_embarque_internal_id=idx + 8000,
            )
        )

    return list(locais)


def criar_rota_rodoviaria(company_id, id_internal):
    locais_embarque_rodoviaria = criar_locais_embarque_rodoviaria(company_id)
    provider_data = []
    data_padrao = to_default_tz(datetime(2022, 7, 9, 0, 0, 0))
    for idx, local in enumerate(locais_embarque_rodoviaria):
        data_padrao = data_padrao + timedelta(minutes=idx * 2)
        provider_data.append(
            {
                "localidade": {
                    "id": local.id_external,
                    "cidade": local.cidade.cidade_internal.name,
                    "uf": local.cidade.cidade_internal.uf,
                },
                "distancia": idx * 2 + 10,
                "permanencia": "00:00",
                "data": data_padrao.strftime("%Y-%m-%d"),
                "hora": data_padrao.strftime("%H:%M"),
            }
        )
    rota_rodoviaria = baker.make(
        "rodoviaria.Rota",
        company_id=company_id,
        id_internal=id_internal,
        provider_data=json.dumps(provider_data),
    )

    return rota_rodoviaria, locais_embarque_rodoviaria


@pytest.fixture
def rota_rodoviaria_completa_integrada(totalbus_login):
    return criar_rota_rodoviaria_completa(totalbus_login, rota_id_internal=12)


@pytest.fixture
def rota_rodoviaria_completa_nao_integrada(totalbus_login):
    return criar_rota_rodoviaria_completa(totalbus_login, rota_id_internal=None)


def criar_rota_rodoviaria_completa(totalbus_login, rota_id_internal=None):
    rota, locais = criar_rota_rodoviaria(totalbus_login.company.id, rota_id_internal)
    orchestrator = OrchestrateRodoviaria(
        totalbus_login.company.company_internal_id, totalbus_login.company.modelo_venda
    )
    _create_or_update_checkpoints_por_rota(orchestrator, rota.id)

    tc = baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota,
        origem=locais[0],
        destino=locais[1],
        distancia=D("123.2"),
        classe="LEITO CAMA",
        duracao=timedelta(seconds=7200),
        preco=D("65.99"),
    )
    tc2 = baker.make(
        "rodoviaria.TrechoVendido",
        rota=rota,
        origem=locais[0],
        destino=locais[2],
        distancia=D("123.2"),
        classe="LEITO CAMA",
        duracao=timedelta(seconds=7200),
        preco=D("65.99"),
    )
    return rota, locais, tc, tc2


def test_get_rota(totalbus_login):
    rota, locais, tc, _ = criar_rota_rodoviaria_completa(totalbus_login, 1)

    retorno = get_detalhes_para_integrar_uma_rota(rota.id)
    assert retorno["rodoviaria_rota_id"] == rota.id
    assert retorno["checkpoints"][0]["local_id"] == locais[0].local_embarque_internal_id
    assert retorno["trechos_vendidos"][0]["rodoviaria_trecho_vendido_id"] == tc.id


def test_get_rota_sem_checkpoints(totalbus_login):
    rota, locais = criar_rota_rodoviaria(totalbus_login.company.id, 1)

    retorno = get_detalhes_para_integrar_uma_rota(rota.id)
    assert retorno == {}


def test_filtrar_apenas_checkpoints_integrados():
    local = SimpleNamespace(
        local_embarque_internal_id=1,
        cidade=SimpleNamespace(cidade_internal=SimpleNamespace()),
    )
    local_sem_cidade_internal = SimpleNamespace(
        local_embarque_internal_id=1, cidade=SimpleNamespace(cidade_internal=None)
    )
    local_sem_cidade = SimpleNamespace(local_embarque_internal_id=1, cidade=None)
    chks = [
        SimpleNamespace(
            tempo_embarque=timedelta(minutes=0),
            duracao=timedelta(hours=0),
            local=local_sem_cidade,
        ),
        SimpleNamespace(
            tempo_embarque=timedelta(minutes=10),
            duracao=timedelta(hours=2),
            local=local,
        ),
        SimpleNamespace(
            tempo_embarque=timedelta(minutes=10),
            duracao=timedelta(hours=2),
            local=local_sem_cidade,
        ),
        SimpleNamespace(
            tempo_embarque=timedelta(minutes=10),
            duracao=timedelta(hours=2),
            local=local_sem_cidade_internal,
        ),
        SimpleNamespace(tempo_embarque=timedelta(minutes=10), duracao=timedelta(hours=2), local=None),
        SimpleNamespace(
            tempo_embarque=timedelta(minutes=10),
            duracao=timedelta(hours=2),
            local=local,
        ),
    ]

    filtrados = filtrar_apenas_checkpoints_integrados(chks)

    assert len(filtrados) == 2
    # duracao do primeiro checkpoint deve sempre ser 0
    assert filtrados[0].duracao == timedelta(hours=0)
    assert filtrados[0].tempo_embarque == timedelta(minutes=0)
    # duracao de um checkpoint filtrado é a soma da duracao dele + duracao e tempo_embarque dos checkpoints
    # nao integrados anteriores
    assert filtrados[1].duracao == timedelta(hours=8, minutes=30)
    assert filtrados[1].tempo_embarque == timedelta(minutes=10)


def test_get_timezone_inicio_itinerario_rota_id(totalbus_company):
    rota = baker.make(
        "rodoviaria.Rota",
        id_hash="206320630bac9d997fd3e6512d618615ee7b7889",
        id_external="12",
        company=totalbus_company,
    )
    cidade = baker.make(
        "rodoviaria.Cidade",
        company=totalbus_company,
        name="Opa",
        timezone="America/Cuiaba",
    )
    local = baker.make("rodoviaria.LocalEmbarque", cidade=cidade)
    baker.make("rodoviaria.Checkpoint", rota=rota, local=local, idx=0)

    timezone_inicio = get_timezone_inicio_itinerario(rota.id)

    assert cidade.timezone == timezone_inicio


def test_get_timezone_inicio_itinerario_grupo_id(totalbus_company):
    rota = baker.make(
        "rodoviaria.Rota",
        id_hash="206320630bac9d997fd3e6512d618615ee7b7889",
        id_external="12",
        company=totalbus_company,
    )
    cidade = baker.make(
        "rodoviaria.Cidade",
        company=totalbus_company,
        name="Opa",
        timezone="America/Cuiaba",
    )
    local = baker.make("rodoviaria.LocalEmbarque", cidade=cidade)
    baker.make("rodoviaria.Checkpoint", rota=rota, local=local, idx=0)
    grupo = baker.make("rodoviaria.Grupo", rota=rota, grupo_internal_id=9000)

    timezone_inicio = get_timezone_inicio_itinerario(grupo_internal_id=grupo.grupo_internal_id)

    assert cidade.timezone == timezone_inicio


def test_get_timezone_inicio_itinerario_grupo_id_nao_existe(totalbus_company):
    rota = baker.make(
        "rodoviaria.Rota",
        id_hash="206320630bac9d997fd3e6512d618615ee7b7889",
        id_external="12",
        company=totalbus_company,
    )
    cidade = baker.make(
        "rodoviaria.Cidade",
        company=totalbus_company,
        name="Opa",
        timezone="America/Cuiaba",
    )
    local = baker.make("rodoviaria.LocalEmbarque", cidade=cidade)
    baker.make("rodoviaria.Checkpoint", rota=rota, local=local, idx=0)

    timezone_inicio = get_timezone_inicio_itinerario(grupo_internal_id=32473)

    assert "America/Sao_Paulo" == timezone_inicio


def test_get_timezone_inicio_itinerario_erro_rota_e_grupo_none():
    with pytest.raises(ValueError) as exc:
        get_timezone_inicio_itinerario(None, None)
        assert "É obrigatório passar rota_id ou grupo_internal_id" in str(exc.value)


def test_update_trechos_vendidos_internal_ids(rota_mock, mock_trechos_vendidos, django_assert_num_queries):
    rota_internal_id = rota_mock.id_internal
    tv0 = mock_trechos_vendidos.trechos[0]
    tv1 = mock_trechos_vendidos.trechos[1]
    tv2 = mock_trechos_vendidos.trechos[2]
    new_tv0_id_internal = 10
    new_tv1_id_internal = 20
    trechos_internal_ids_map = {
        str(tv0.id_internal): new_tv0_id_internal,
        str(tv1.id_internal): new_tv1_id_internal,
    }
    num_queries = 2  # 1select + 1update

    assert tv2.id_internal is not None

    with django_assert_num_queries(num_queries, connection=connections["rodoviaria"]):
        update_trechos_vendidos_internal_ids(rota_internal_id, trechos_internal_ids_map)

    assert TrechoVendido.objects.get(pk=tv2.pk).id_internal is None
    assert TrechoVendido.objects.get(pk=tv0.pk).id_internal == new_tv0_id_internal
    assert TrechoVendido.objects.get(pk=tv1.pk).id_internal == new_tv1_id_internal


def test_get_ids_rotas_ativas_empresa(rota_mock):
    rota_mock.id_internal = 1
    rota_mock.data_limite = datetime(2022, 2, 2)
    rota_mock.save()
    resp = get_ids_rotas_ativas_empresa(rota_mock.company.company_internal_id, rota_mock.company.modelo_venda)
    assert resp[rota_mock.id_internal] == [rota_mock.id]


def test_get_rotas_novas_empresas_para_integrar(rota_rodoviaria_completa_nao_integrada, django_assert_num_queries):
    rota = rota_rodoviaria_completa_nao_integrada[0]
    locais = rota_rodoviaria_completa_nao_integrada[1]
    tc = rota_rodoviaria_completa_nao_integrada[2]

    with django_assert_num_queries(4, connection=connections["rodoviaria"]):
        rotas = get_rotas_novas_empresas_para_integrar([rota.company.company_internal_id])
    assert rotas
    retorno = rotas[0]
    assert retorno["rodoviaria_rota_id"] == rota.id
    assert retorno["itinerario"][0]["local_id"] == locais[0].local_embarque_internal_id
    assert retorno["trechos_vendidos"][0]["rodoviaria_trecho_vendido_id"] == tc.id
    # Só existe 1 trecho vendido indo de RS para SC, entretanto a rota inteira percorre RS, SC, PR, MT
    # Onde os UFs intermediarios são os do meio
    assert retorno["ufs_intermediarios"] == "SC,PR"


def test_get_rotas_novas_empresas_para_integrar_1_checkpoint_integrado(
    rota_rodoviaria_completa_nao_integrada,
):
    rota = rota_rodoviaria_completa_nao_integrada[0]
    locais = rota_rodoviaria_completa_nao_integrada[1]
    for local in locais[1:]:
        local.local_embarque_internal_id = None
        local.save()

    rotas = get_rotas_novas_empresas_para_integrar([rota.company.company_internal_id])
    assert not rotas


def test_get_rotas_novas_empresas_para_integrar_1_checkpoint_nao_integrado(
    rota_rodoviaria_completa_nao_integrada,
):
    rota, locais, tc, _ = rota_rodoviaria_completa_nao_integrada
    locais[2].local_embarque_internal_id = None
    locais[2].save()

    rotas = get_rotas_novas_empresas_para_integrar([rota.company.company_internal_id])
    assert rotas


def test_get_rotas_novas_empresas_para_integrar_nenhum_checkpoint_integrado(
    rota_rodoviaria_completa_nao_integrada,
):
    rota = rota_rodoviaria_completa_nao_integrada[0]
    locais = rota_rodoviaria_completa_nao_integrada[1]
    for local in locais:
        local.local_embarque_internal_id = None
        local.save()

    rotas = get_rotas_novas_empresas_para_integrar([rota.company.company_internal_id])
    assert not rotas


def test_get_rotas_novas_empresas_para_integrar_sem_trechos_vendidos_para_checkpoints_integrados(
    rota_rodoviaria_completa_nao_integrada,
):
    rota = rota_rodoviaria_completa_nao_integrada[0]
    locais = rota_rodoviaria_completa_nao_integrada[1]
    locais[0].local_embarque_internal_id = None
    locais[0].save()

    rotas = get_rotas_novas_empresas_para_integrar([rota.company.company_internal_id])
    assert not rotas


def test_get_rotas_ativas_para_reintegrar_assert_num_queries(
    rota_rodoviaria_completa_integrada, django_assert_num_queries
):
    rota = rota_rodoviaria_completa_integrada[0]

    with django_assert_num_queries(5, connection=connections["rodoviaria"]):
        rotas = get_rotas_ativas_para_reintegrar([rota.company.company_internal_id])
    assert rotas


def test_get_rotas_ativas_para_reintegrar(rota_rodoviaria_completa_integrada):
    rota = rota_rodoviaria_completa_integrada[0]
    tc = rota_rodoviaria_completa_integrada[2]
    tc2 = rota_rodoviaria_completa_integrada[3]

    expected_response = [
        {
            "rodoviaria_rota_id": rota.id,
            "id_internal": rota.id_internal,
            "itinerario": [r.to_dict_json(local=False) for r in rota.itinerario.all()],
            "trechos_vendidos": [
                {
                    "rodoviaria_trecho_vendido_id": tc.id,
                    "origem_id": tc.origem.local_embarque_internal_id,
                    "destino_id": tc.destino.local_embarque_internal_id,
                    "preco_rodoviaria": tc.preco,
                },
                {
                    "rodoviaria_trecho_vendido_id": tc2.id,
                    "origem_id": tc2.origem.local_embarque_internal_id,
                    "destino_id": tc2.destino.local_embarque_internal_id,
                    "preco_rodoviaria": tc2.preco,
                },
            ],
            # Só existe 1 trecho vendido indo de RS para SC, entretanto a rota inteira percorre RS, SC, PR, MT
            # Onde os UFs intermediarios são os do meio
            "ufs_intermediarios": "SC,PR",
        }
    ]

    rotas = get_rotas_ativas_para_reintegrar([rota.company.company_internal_id])
    assert rotas == expected_response


def test_get_rotas_ativas_para_reintegrar_1_checkpoint_integrado(
    rota_rodoviaria_completa_integrada,
):
    rota = rota_rodoviaria_completa_integrada[0]
    locais = rota_rodoviaria_completa_integrada[1]
    for local in locais[1:]:
        local.local_embarque_internal_id = None
        local.save()

    rotas = get_rotas_ativas_para_reintegrar([rota.company.company_internal_id])
    assert not rotas


def test_get_rotas_ativas_para_reintegrar_1_checkpoint_nao_integrado(
    rota_rodoviaria_completa_integrada,
):
    rota, locais, tv, _ = rota_rodoviaria_completa_integrada
    tv.id_internal = 123
    tv.save()
    locais[2].local_embarque_internal_id = None
    locais[2].save()

    rotas = get_rotas_ativas_para_reintegrar([rota.company.company_internal_id])
    assert not rotas


def test_get_rotas_ativas_para_reintegrar_nenhum_checkpoint_integrado(
    rota_rodoviaria_completa_integrada,
):
    rota = rota_rodoviaria_completa_integrada[0]
    locais = rota_rodoviaria_completa_integrada[1]
    for local in locais:
        local.local_embarque_internal_id = None
    LocalEmbarque.objects.bulk_update(locais, ["local_embarque_internal_id", "updated_at"])

    rotas = get_rotas_ativas_para_reintegrar([rota.company.company_internal_id])
    assert not rotas


def test_get_rotas_ativas_para_reintegrar_sem_trechos_vendidos_para_checkpoints_integrados(
    rota_rodoviaria_completa_integrada,
):
    rota = rota_rodoviaria_completa_integrada[0]
    locais = rota_rodoviaria_completa_integrada[1]
    locais[0].local_embarque_internal_id = None
    locais[0].save()

    rotas = get_rotas_ativas_para_reintegrar([rota.company.company_internal_id])
    assert not rotas


def test_get_rotas_ativas_para_reintegrar_forcar_reintegracao(rota_rodoviaria_completa_integrada, override_config):
    rota = rota_rodoviaria_completa_integrada[0]
    rota_rodoviaria_completa_integrada[2].id_internal = 1
    rota_rodoviaria_completa_integrada[2].save()
    rota_rodoviaria_completa_integrada[3].id_internal = 2
    rota_rodoviaria_completa_integrada[3].save()

    tv = rota_rodoviaria_completa_integrada[2]
    tv.id_internal = 1
    tv.save()
    tv2 = rota_rodoviaria_completa_integrada[3]
    tv2.id_internal = 2
    tv.save()

    expected_response = [
        {
            "rodoviaria_rota_id": rota.id,
            "id_internal": rota.id_internal,
            "itinerario": [r.to_dict_json(local=False) for r in rota.itinerario.all()],
            "trechos_vendidos": [
                {
                    "rodoviaria_trecho_vendido_id": tv.id,
                    "origem_id": tv.origem.local_embarque_internal_id,
                    "destino_id": tv.destino.local_embarque_internal_id,
                    "preco_rodoviaria": tv.preco,
                },
                {
                    "rodoviaria_trecho_vendido_id": tv2.id,
                    "origem_id": tv2.origem.local_embarque_internal_id,
                    "destino_id": tv2.destino.local_embarque_internal_id,
                    "preco_rodoviaria": tv2.preco,
                },
            ],
            # Só existe 1 trecho vendido indo de RS para SC, entretanto a rota inteira percorre RS, SC, PR, MT
            # Onde os UFs intermediarios são os do meio
            "ufs_intermediarios": "SC,PR",
        }
    ]

    with override_config(ROTAS_PARA_REINTEGRAR=f"{rota.id}"):
        rotas = get_rotas_ativas_para_reintegrar([rota.company.company_internal_id])
    assert rotas == expected_response


def test_get_rotas_ativas_para_reintegrar_forcar_reintegracao_nao_pega_rota_inativa(
    rota_rodoviaria_completa_integrada, override_config
):
    rota = rota_rodoviaria_completa_integrada[0]
    rota.ativo = False
    rota.save()
    rota_rodoviaria_completa_integrada[2].id_internal = 1
    rota_rodoviaria_completa_integrada[2].save()
    rota_rodoviaria_completa_integrada[3].id_internal = 2
    rota_rodoviaria_completa_integrada[3].save()

    tv = rota_rodoviaria_completa_integrada[2]
    tv.id_internal = 1
    tv.save()
    tv2 = rota_rodoviaria_completa_integrada[3]
    tv2.id_internal = 2
    tv.save()

    expected_response = []

    with override_config(ROTAS_PARA_REINTEGRAR=f"{rota.id}"):
        rotas = get_rotas_ativas_para_reintegrar([rota.company.company_internal_id])
    assert rotas == expected_response


def test_get_rotas_ativas_para_reintegrar_forcar_reintegracao_nao_pega_de_outra_empresa(
    rota_rodoviaria_completa_integrada, override_config
):
    rota = rota_rodoviaria_completa_integrada[0]
    rota_rodoviaria_completa_integrada[2].id_internal = 1
    rota_rodoviaria_completa_integrada[2].save()
    rota_rodoviaria_completa_integrada[3].id_internal = 2
    rota_rodoviaria_completa_integrada[3].save()

    tv = rota_rodoviaria_completa_integrada[2]
    tv.id_internal = 1
    tv.save()
    tv2 = rota_rodoviaria_completa_integrada[3]
    tv2.id_internal = 2
    tv.save()

    expected_response = []

    with override_config(ROTAS_PARA_REINTEGRAR=f"{rota.id}"):
        rotas = get_rotas_ativas_para_reintegrar([9999])
    assert rotas == expected_response


def test_get_ids_rotas_integradas_inativas():
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=123999,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    company_hibrido = baker.make(
        "rodoviaria.Company",
        company_internal_id=123999,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    r = baker.make("rodoviaria.Rota", id_internal=1, ativo=False, company=company)
    baker.make("rodoviaria.Rota", id_internal=None, ativo=False, company=company)
    baker.make("rodoviaria.Rota", id_internal=2, ativo=True, company=company)
    baker.make("rodoviaria.Rota", id_internal=1, ativo=False, company=company_hibrido)
    result = get_ids_rotas_integradas_inativas([r.company.company_internal_id])
    assert len(result) == 1
    assert result[0] == r.id_internal


def test_get_ids_rotas_integradas_inativas_nao_retorna_se_houver_alguma_true():
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=123999,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    # dada que uma rota do buser_django pode estar associado à mais de uma rota do rodoviaria
    baker.make(
        "rodoviaria.Rota",
        id_internal=71046,
        id_external=cycle([111116, 112384, 112116]),
        company=company,
        ativo=False,
        _quantity=3,
    )
    # se houver ao menos uma das rotas do rodoviária ativo=True
    baker.make("rodoviaria.Rota", id_internal=71046, id_external=111116, company=company, ativo=True)
    # essa rota não deve ser retornada como inativa
    result = get_ids_rotas_integradas_inativas([company.company_internal_id])
    assert result == []


def test_get_map_trechos_vendidos_por_rota():
    company = baker.make("rodoviaria.Company")
    rota1 = baker.make("rodoviaria.Rota", company=company)
    rota2 = baker.make("rodoviaria.Rota", company=company)
    le_ori = baker.make("rodoviaria.LocalEmbarque", local_embarque_internal_id=1)
    le_dest = baker.make("rodoviaria.LocalEmbarque", local_embarque_internal_id=2)
    trecho_rota1 = baker.make(
        "rodoviaria.TrechoVendido",
        origem=le_ori,
        destino=le_dest,
        ativo=True,
        rota=rota1,
        preco=D("12.00"),
        classe="Leito pro max deluxe",
    )
    # Como o map de trechos vendidos é para criar uma rota no buser_django, ignoramos trechos vendidos que
    # tenham origem e destino repetidos. No caso, esse trecho não deve retornar como resultado
    baker.make(
        "rodoviaria.TrechoVendido",
        origem=le_ori,
        destino=le_dest,
        ativo=True,
        rota=rota1,
        preco=D("12.00"),
        classe="Executivo",
    )
    trecho_rota2 = baker.make(
        "rodoviaria.TrechoVendido", origem=le_ori, destino=le_dest, ativo=True, rota=rota2, preco=D("12.00")
    )
    resultado_esperado = {
        rota1.id: [
            {
                "rodoviaria_trecho_vendido_id": trecho_rota1.id,
                "origem_id": le_ori.local_embarque_internal_id,
                "destino_id": le_dest.local_embarque_internal_id,
                "preco_rodoviaria": trecho_rota1.preco,
            }
        ],
        rota2.id: [
            {
                "rodoviaria_trecho_vendido_id": trecho_rota2.id,
                "origem_id": le_ori.local_embarque_internal_id,
                "destino_id": le_dest.local_embarque_internal_id,
                "preco_rodoviaria": trecho_rota2.preco,
            }
        ],
    }
    resultado = _get_map_trechos_vendidos_por_rota([rota1.id, rota2.id])
    assert len(resultado) == 2
    assert resultado == resultado_esperado

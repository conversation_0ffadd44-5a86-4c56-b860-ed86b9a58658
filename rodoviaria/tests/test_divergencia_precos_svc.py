from datetime import datetime, timed<PERSON>ta
from decimal import Decimal as D

import time_machine
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from rodoviaria.models.core import CidadeInternal, Company, Passagem, TrechoClasse
from rodoviaria.service import divergencia_precos_svc

time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))


def test_trechos_empresa_com_maiores_divergencias_de_preco():
    company = baker.make(Company, features=[Company.Feature.ACTIVE], company_internal_id=999)
    trechos = [
        baker.make(
            TrechoClasse,
            origem__cidade__cidade_internal=baker.make(CidadeInternal),
            destino__cidade__cidade_internal=baker.make(CidadeInternal),
        )
        for i in range(3)
    ]
    for i in range(9):
        trecho = trechos[i % 3]
        baker.make(
            <PERSON><PERSON>,
            valor_cheio=100,
            preco_rodoviaria=100 + 10 * i,
            trechoclasse_integracao=trecho,
            company_integracao=company,
            status=Passagem.Status.CONFIRMADA,
        )
    resposta = divergencia_precos_svc.trechos_empresa_com_maiores_divergencias_de_preco(days_behind=2, quantity=2)
    assert resposta == [
        {
            "order": 1,
            "origem_id": trechos[2].origem.cidade.cidade_internal_id,
            "destino_id": trechos[2].destino.cidade.cidade_internal_id,
            "company_id": company.company_internal_id,
            "divergencia": D("150"),
        },
        {
            "order": 2,
            "origem_id": trechos[1].origem.cidade.cidade_internal_id,
            "destino_id": trechos[1].destino.cidade.cidade_internal_id,
            "company_id": company.company_internal_id,
            "divergencia": D("120"),
        },
    ]


time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))


def test_trechos_empresa_com_maiores_divergencias_de_preco_company_sem_feature():
    company = baker.make(Company)
    trecho = baker.make(
        TrechoClasse,
        origem__cidade__cidade_internal=baker.make(CidadeInternal),
        destino__cidade__cidade_internal=baker.make(CidadeInternal),
    )
    baker.make(
        Passagem,
        valor_cheio=100,
        preco_rodoviaria=150,
        trechoclasse_integracao=trecho,
        company_integracao=company,
        status=Passagem.Status.CONFIRMADA,
    )
    resposta = divergencia_precos_svc.trechos_empresa_com_maiores_divergencias_de_preco(days_behind=2, quantity=2)
    assert resposta == []


time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))


def test_trechos_empresa_com_maiores_divergencias_de_preco_fora_da_data():
    company = baker.make(Company, features=[Company.Feature.ACTIVE])
    trecho = baker.make(
        TrechoClasse,
        origem__cidade__cidade_internal=baker.make(CidadeInternal),
        destino__cidade__cidade_internal=baker.make(CidadeInternal),
    )
    p = baker.make(
        Passagem,
        valor_cheio=100,
        preco_rodoviaria=150,
        trechoclasse_integracao=trecho,
        company_integracao=company,
        status=Passagem.Status.CONFIRMADA,
    )
    p.created_at = timezone.now() - timedelta(5)
    p.save()
    resposta = divergencia_precos_svc.trechos_empresa_com_maiores_divergencias_de_preco(days_behind=2, quantity=2)
    assert resposta == []


time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))


def test_trechos_empresa_com_maiores_divergencias_de_preco_passagem_cancelada():
    company = baker.make(Company, features=[Company.Feature.ACTIVE])
    trecho = baker.make(
        TrechoClasse,
        origem__cidade__cidade_internal=baker.make(CidadeInternal),
        destino__cidade__cidade_internal=baker.make(CidadeInternal),
    )
    baker.make(
        Passagem,
        valor_cheio=100,
        preco_rodoviaria=150,
        trechoclasse_integracao=trecho,
        company_integracao=company,
        status=Passagem.Status.CANCELADA,
    )
    resposta = divergencia_precos_svc.trechos_empresa_com_maiores_divergencias_de_preco(days_behind=2, quantity=2)
    assert resposta == []


time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))


def test_trechos_empresa_com_maiores_divergencias_de_preco_separa_por_empresa():
    trecho = baker.make(
        TrechoClasse,
        origem__cidade__cidade_internal=baker.make(CidadeInternal),
        destino__cidade__cidade_internal=baker.make(CidadeInternal),
    )
    company_1 = baker.make(Company, features=[Company.Feature.ACTIVE], company_internal_id=3124)
    baker.make(
        Passagem,
        valor_cheio=100,
        preco_rodoviaria=140,
        trechoclasse_integracao=trecho,
        company_integracao=company_1,
        status=Passagem.Status.CONFIRMADA,
    )
    company_2 = baker.make(Company, features=[Company.Feature.ACTIVE], company_internal_id=8723)
    baker.make(
        Passagem,
        valor_cheio=100,
        preco_rodoviaria=150,
        trechoclasse_integracao=trecho,
        company_integracao=company_2,
        status=Passagem.Status.CONFIRMADA,
    )

    resposta = divergencia_precos_svc.trechos_empresa_com_maiores_divergencias_de_preco(days_behind=2, quantity=2)

    assert resposta == [
        {
            "order": 1,
            "origem_id": trecho.origem.cidade.cidade_internal_id,
            "destino_id": trecho.destino.cidade.cidade_internal_id,
            "company_id": company_2.company_internal_id,
            "divergencia": D("50"),
        },
        {
            "order": 2,
            "origem_id": trecho.origem.cidade.cidade_internal_id,
            "destino_id": trecho.destino.cidade.cidade_internal_id,
            "company_id": company_1.company_internal_id,
            "divergencia": D("40"),
        },
    ]

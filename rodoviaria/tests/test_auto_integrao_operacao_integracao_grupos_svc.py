from datetime import datetime, timedelta
from decimal import Decimal
from types import SimpleNamespace

import pytest
import time_machine
from django.db import connections
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import midnight, replace_timezone, to_default_tz, to_tz, today_midnight
from rodoviaria.models.core import Rotina, RotinaTrechoVendido
from rodoviaria.service.auto_integra_operacao import auto_integra_operacao_svc, integracao_grupos_svc
from rodoviaria.service.exceptions import RodoviariaException


def cria_rotinas(
    rota_totalbus, base_date, classes, qtd_rotinas, qtd_trechos_por_rotina, asc=True, with_trecho_vendido=True
):
    rotinas = []
    r = range(qtd_rotinas)
    if not asc:
        r = range(qtd_rotinas, 0, -1)
    for x in r:
        try:
            rotina = Rotina.objects.get(
                rota=rota_totalbus,
                datetime_ida=to_default_tz(base_date + timedelta(days=(7 * x))),
            )
        except Exception:
            rotina = baker.make(
                "rodoviaria.Rotina",
                rota=rota_totalbus,
                datetime_ida=to_default_tz(base_date + timedelta(days=(7 * x))),
                ativo=True,
            )
        trechos = []
        dt_tc = rotina.datetime_ida
        if not with_trecho_vendido:
            continue
        for y in range(qtd_trechos_por_rotina):
            for c in classes:
                trecho = baker.make(
                    "rodoviaria.RotinaTrechoVendido",
                    rotina=rotina,
                    datetime_ida_trecho_vendido=dt_tc,
                    trecho_vendido=baker.make(
                        "rodoviaria.TrechoVendido",
                        rota=rota_totalbus,
                        id_internal=y,
                        classe=c,
                        tipo_assento=baker.make(
                            "rodoviaria.TipoAssento", tipo_assento_parceiro=c, tipo_assento_buser_preferencial=c
                        ),
                        capacidade_classe=12,
                        preco=60.00,
                        origem=baker.make(
                            "rodoviaria.LocalEmbarque",
                            local_embarque_internal_id=12,
                            cidade=baker.make("rodoviaria.Cidade", timezone="America/Sao_Paulo"),
                        ),
                        destino=baker.make(
                            "rodoviaria.LocalEmbarque",
                            local_embarque_internal_id=13,
                            cidade=baker.make("rodoviaria.Cidade", timezone="America/Sao_Paulo"),
                        ),
                    ),
                )
                trechos.append(trecho)
            dt_tc = dt_tc + timedelta(hours=1, minutes=30)
        rotinas.append((rotina, trechos))

    dia = base_date.strftime("%a")
    hora = base_date.strftime("%H:%M")
    return dia, hora, rotinas


def test_get_datas_rotinas_com_trechos_da_api(rota_totalbus, django_assert_num_queries):
    date_now = datetime.now()
    leito_convencional_dia, leito_convencional_hora, _ = cria_rotinas(
        rota_totalbus, date_now, ["leito", "convencional"], 2, 3
    )
    with django_assert_num_queries(2, connection=connections["rodoviaria"]):
        info_checkpoints = integracao_grupos_svc._get_checkpoint_info_por_rota([rota_totalbus.id])
        result_por_rota = integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api(
            [rota_totalbus.id],
            {rota_totalbus.id: [0, 1, 2]},
            info_checkpoints.duracao_ate_linkado,
            info_checkpoints.first_timezone_rota,
        )
    assert len(result_por_rota) == 1
    assert len(result_por_rota[rota_totalbus.id]) == 1
    result = list(result_por_rota[rota_totalbus.id].values())
    assert result[0]["horario"] == leito_convencional_hora
    assert result[0]["dia_semana"] == leito_convencional_dia
    assert len(result[0]["datas"]) == 2
    assert result[0]["datas"][0]["data"] == date_now.date()


def test_get_datas_rotinas_com_trechos_da_api_primeiros_trechos_nao_integrados(
    rota_totalbus, django_assert_num_queries
):
    date_now = datetime.now()
    _, _, rotinas = cria_rotinas(rota_totalbus, date_now, ["leito"], 2, 3)

    # remove integracao do primeiro trecho
    rotinas[0][1][0].trecho_vendido.origem.local_embarque_internal_id = None
    rotinas[0][1][0].trecho_vendido.origem.save()
    rotinas[1][1][0].trecho_vendido.origem.local_embarque_internal_id = None
    rotinas[1][1][0].trecho_vendido.origem.save()

    with django_assert_num_queries(2, connection=connections["rodoviaria"]):
        info_checkpoints = integracao_grupos_svc._get_checkpoint_info_por_rota([rota_totalbus.id])
        result_por_rota = integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api(
            [rota_totalbus.id],
            {rota_totalbus.id: [0, 1, 2]},
            info_checkpoints.duracao_ate_linkado,
            info_checkpoints.first_timezone_rota,
        )
    # deve pegar rotina com o horario do proximo trecho vendido
    date_assert = date_now
    assert len(result_por_rota) == 1
    assert len(result_por_rota[rota_totalbus.id]) == 1
    result = list(result_por_rota[rota_totalbus.id].values())
    assert result[0]["horario"] == date_assert.strftime("%H:%M")
    assert len(result[0]["datas"]) == 2
    assert result[0]["datas"][0]["data"] == date_assert.date()

    # remove integracao do segundo trecho tambem
    rotinas[0][1][1].trecho_vendido.origem.local_embarque_internal_id = None
    rotinas[0][1][1].trecho_vendido.origem.save()
    rotinas[1][1][1].trecho_vendido.origem.local_embarque_internal_id = None
    rotinas[1][1][1].trecho_vendido.origem.save()

    with django_assert_num_queries(2, connection=connections["rodoviaria"]):
        info_checkpoints = integracao_grupos_svc._get_checkpoint_info_por_rota([rota_totalbus.id])
        result_por_rota = integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api(
            [rota_totalbus.id],
            {rota_totalbus.id: [0, 1, 2]},
            info_checkpoints.duracao_ate_linkado,
            info_checkpoints.first_timezone_rota,
        )
    date_assert = date_now
    assert len(result_por_rota) == 1
    assert len(result_por_rota[rota_totalbus.id]) == 1
    result = list(result_por_rota[rota_totalbus.id].values())
    assert result[0]["horario"] == date_assert.strftime("%H:%M")
    assert len(result[0]["datas"]) == 2
    assert result[0]["datas"][0]["data"] == date_assert.date()


def test_get_datas_rotinas_com_trechos_da_api_timezone_diferentes(rota_totalbus):
    date_now = datetime.now()
    _, _, rotinas = cria_rotinas(rota_totalbus, date_now, ["leito"], 1, 3)

    rotinas[0][1][1].trecho_vendido.origem.cidade.timezone = "America/Campo_Grande"
    rotinas[0][1][1].trecho_vendido.origem.cidade.save()
    rotinas[0][1][2].trecho_vendido.origem.cidade.timezone = "America/Belem"
    rotinas[0][1][2].trecho_vendido.origem.cidade.save()

    info_checkpoints = integracao_grupos_svc._get_checkpoint_info_por_rota([rota_totalbus.id])
    result_por_rota = integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api(
        [rota_totalbus.id],
        {rota_totalbus.id: [0, 1, 2]},
        info_checkpoints.duracao_ate_linkado,
        info_checkpoints.first_timezone_rota,
    )
    assert len(result_por_rota) == 1
    assert len(result_por_rota[rota_totalbus.id]) == 1
    result = list(result_por_rota[rota_totalbus.id].values())
    assert result[0]["horario"] == date_now.strftime("%H:%M")
    assert len(result[0]["datas"]) == 1


def test_get_detalhes_rotinas(rota_totalbus):
    date_now = datetime.now()
    (
        leito_convencional_dia,
        leito_convencional_hora,
        leito_convencional_rotinas,
    ) = cria_rotinas(rota_totalbus, date_now, ["leito", "convencional"], 3, 3)
    executivo_dia, executivo_hora, _ = cria_rotinas(
        rota_totalbus, date_now + timedelta(minutes=10), ["executivo"], 3, 1
    )

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_totalbus.id],
        {rota_totalbus.id: [0, 1, 2]},
        {
            rota_totalbus.id: [
                leito_convencional_rotinas[-3][0].datetime_ida,
                leito_convencional_rotinas[-2][0].datetime_ida,
            ]
        },
    )
    hits = hits_por_rota[rota_totalbus.id]
    assert len(hits) == 2

    leito_convencional = [
        rotina
        for rotina in hits
        if len(rotina["classes"]) == 2 and rotina["classes"][0]["tipo_assento"] in ["leito", "convencional"]
    ][0]
    assert leito_convencional["horario"] == leito_convencional_hora
    assert leito_convencional["dia_semana"] == leito_convencional_dia
    assert len([data for data in leito_convencional["datas"] if data.get("from_api")]) == 1
    assert len([data for data in leito_convencional["datas"] if not data.get("from_api")]) == 10
    assert len(leito_convencional["trechos_vendidos"]) == 3
    assert leito_convencional["classes"][0]["id"]

    executivo = [
        rotina
        for rotina in hits
        if len(rotina["classes"]) == 1 and rotina["classes"][0]["tipo_assento"] in ["executivo"]
    ][0]
    assert executivo["horario"] == executivo_hora
    assert executivo["dia_semana"] == executivo_dia
    assert executivo["classes"][0]["id"]
    assert len([data for data in executivo["datas"] if data.get("from_api")]) == 3
    assert len([data for data in executivo["datas"] if not data.get("from_api")]) == 10
    assert len(executivo["trechos_vendidos"]) == 1


def test_get_detalhes_rotinas_not_extend_dates(rota_totalbus):
    date_now = datetime.now()
    (
        leito_convencional_dia,
        leito_convencional_hora,
        leito_convencional_rotinas,
    ) = cria_rotinas(rota_totalbus, date_now, ["leito", "convencional"], 2, 3)

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]}, {}, extend_dates=False
    )
    hits = hits_por_rota[rota_totalbus.id]
    assert len(hits) == 1

    leito_convencional = [
        rotina
        for rotina in hits
        if len(rotina["classes"]) == 2 and rotina["classes"][0]["tipo_assento"] in ["leito", "convencional"]
    ][0]
    assert leito_convencional["horario"] == leito_convencional_hora
    assert leito_convencional["dia_semana"] == leito_convencional_dia
    assert len([data for data in leito_convencional["datas"] if data.get("from_api")]) == 2
    assert len([data for data in leito_convencional["datas"] if not data.get("from_api")]) == 0
    assert len(leito_convencional["trechos_vendidos"]) == 3
    assert leito_convencional["classes"][0]["id"]


def test_get_detalhes_rotinas_bypass_data_limite(rota_totalbus):
    date_now = datetime.now()
    (
        leito_convencional_dia,
        leito_convencional_hora,
        leito_convencional_rotinas,
    ) = cria_rotinas(rota_totalbus, date_now, ["leito", "convencional"], 20, 3)

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]}, {}, extend_dates=False
    )
    hits = hits_por_rota[rota_totalbus.id]
    assert len(hits) == 1

    leito_convencional = [
        rotina
        for rotina in hits
        if len(rotina["classes"]) == 2 and rotina["classes"][0]["tipo_assento"] in ["leito", "convencional"]
    ][0]
    assert leito_convencional["horario"] == leito_convencional_hora
    assert leito_convencional["dia_semana"] == leito_convencional_dia
    assert len([data for data in leito_convencional["datas"] if data.get("from_api")]) == 20
    assert len([data for data in leito_convencional["datas"] if not data.get("from_api")]) == 0
    assert len(leito_convencional["trechos_vendidos"]) == 3
    assert leito_convencional["classes"][0]["id"]


def test_get_detalhes_rotinas_considera_rotinas_ativas_sem_rotina_trecho_vendido(rota_totalbus):
    date_now = datetime.now()
    (
        leito_convencional_dia,
        leito_convencional_hora,
        leito_convencional_rotinas,
    ) = cria_rotinas(rota_totalbus, date_now, ["leito", "convencional"], 7, 3)
    # cria rotinas sem trechos vendidos no mesmo dia da semana
    cria_rotinas(
        rota_totalbus,
        leito_convencional_rotinas[-1][0].datetime_ida + timedelta(days=7),
        ["leito", "convencional"],
        7,
        3,
        with_trecho_vendido=False,
    )

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]}, {}, extend_dates=False
    )
    hits = hits_por_rota[rota_totalbus.id]
    assert len(hits) == 1

    leito_convencional = [
        rotina
        for rotina in hits
        if len(rotina["classes"]) == 2 and rotina["classes"][0]["tipo_assento"] in ["leito", "convencional"]
    ][0]
    assert leito_convencional["horario"] == leito_convencional_hora
    assert leito_convencional["dia_semana"] == leito_convencional_dia
    assert len([data for data in leito_convencional["datas"] if data.get("from_api")]) == 14
    assert len([data for data in leito_convencional["datas"] if not data.get("from_api")]) == 0


def test_get_detalhes_rotinas_nao_considera_rotinas_ativas_sem_rotina_trecho_vendido_em_dias_diferentes(rota_totalbus):
    date_now = datetime.now()
    (
        leito_convencional_dia_1,
        leito_convencional_hora_1,
        leito_convencional_rotinas_1,
    ) = cria_rotinas(rota_totalbus, date_now, ["leito", "convencional"], 7, 3)
    # cria rotinas sem trechos vendidos em outro dia da semana
    cria_rotinas(
        rota_totalbus,
        leito_convencional_rotinas_1[-1][0].datetime_ida + timedelta(days=3),
        ["leito", "convencional"],
        7,
        3,
        with_trecho_vendido=False,
    )

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]}, {}, extend_dates=False
    )
    hits = hits_por_rota[rota_totalbus.id]
    assert len(hits) == 1

    leito_convencional = [
        rotina
        for rotina in hits
        if len(rotina["classes"]) == 2 and rotina["classes"][0]["tipo_assento"] in ["leito", "convencional"]
    ][0]
    assert leito_convencional["horario"] == leito_convencional_hora_1
    assert leito_convencional["dia_semana"] == leito_convencional_dia_1
    assert len([data for data in leito_convencional["datas"] if data.get("from_api")]) == 7
    assert len([data for data in leito_convencional["datas"] if not data.get("from_api")]) == 0


@time_machine.travel(datetime(2022, 7, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_detalhes_rotinas_com_rotinas_ativas_sem_rotina_trecho_vendido_com_primeiro_cp_nao_linkado(rota_totalbus):
    first_checkpoint = rota_totalbus.first_checkpoint()
    first_checkpoint.local.local_embarque_internal_id = None
    first_checkpoint.local.save()
    first_checkpoint_linkado = rota_totalbus.itinerario.all()[1]
    duracao_ate_primeiro_linkado = first_checkpoint_linkado.duracao + first_checkpoint_linkado.tempo_embarque
    date_now = datetime.now()
    (
        leito_convencional_dia_1,
        leito_convencional_hora_1,
        leito_convencional_rotinas_1,
    ) = cria_rotinas(rota_totalbus, date_now, ["leito", "convencional"], 7, 3)
    hora_first_checkpoint = datetime.strptime(leito_convencional_hora_1, "%H:%M")
    # cria rotinas sem trechos vendidos em outro dia da semana
    cria_rotinas(
        rota_totalbus,
        leito_convencional_rotinas_1[-1][0].datetime_ida + timedelta(days=7),
        ["leito", "convencional"],
        7,
        3,
        with_trecho_vendido=False,
    )

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]}, {}, extend_dates=False
    )
    hits = hits_por_rota[rota_totalbus.id]
    assert len(hits) == 1

    leito_convencional = [
        rotina
        for rotina in hits
        if len(rotina["classes"]) == 2 and rotina["classes"][0]["tipo_assento"] in ["leito", "convencional"]
    ][0]
    hora_first_checkpoint_linkado = (hora_first_checkpoint + duracao_ate_primeiro_linkado).strftime("%H:%M")
    assert leito_convencional["horario"] == hora_first_checkpoint_linkado
    assert leito_convencional["dia_semana"] == leito_convencional_dia_1
    assert len([data for data in leito_convencional["datas"] if data.get("from_api")]) == 14
    assert len([data for data in leito_convencional["datas"] if not data.get("from_api")]) == 0


def test_get_detalhes_rotinas_sem_rotinas(rota_totalbus):
    hits = integracao_grupos_svc.get_detalhes_rotinas([rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]})
    assert hits == {}


def test_get_detalhes_rotinas_sem_trechos(rota_totalbus):
    cria_rotinas(rota_totalbus, datetime.now(), ["leito", "convencional"], 3, 0)

    hits = integracao_grupos_svc.get_detalhes_rotinas([rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]})
    assert hits == {}


def test_get_detalhes_rotinas_apenas_rotinas_data_ate_sete_dias_para_tras(rota_totalbus):
    date_now = datetime.now()
    cria_rotinas(rota_totalbus, date_now - timedelta(days=23), ["executivo"], 2, 1)

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas([rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]})
    assert hits_por_rota == {}


@time_machine.travel(datetime(2022, 7, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_detalhes_rotinas_pega_rotinas_do_passado(rota_totalbus):
    datetime_now = datetime.now()
    date_now = datetime_now.date()
    cria_rotinas(rota_totalbus, datetime_now - timedelta(days=4), ["leito"], 1, 1)
    cria_rotinas(rota_totalbus, datetime_now - timedelta(days=2), ["executivo"], 1, 1)
    cria_rotinas(rota_totalbus, datetime_now + timedelta(days=1), ["leito", "leito individual"], 1, 1)

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas([rota_totalbus.id], {rota_totalbus.id: [0, 1, 2]})
    expected_dow = ["Sat", "Mon", "Thu"]
    expected_classes = [{"leito"}, {"executivo"}, {"leito", "leito individual"}]
    for index, hits in enumerate(hits_por_rota[rota_totalbus.id]):
        # assert se retornou apenas datas futuras
        for data in hits["datas"]:
            assert data["data"] > date_now
        assert hits["horario"] == datetime_now.strftime("%H:%M")
        assert hits["dia_semana"] == expected_dow[index]
        assert {c["tipo_assento"] for c in hits["classes"]} == expected_classes[index]


def test_get_detalhes_rotinas_com_filtro_rotinas_trechos_inexistente(rota_totalbus):
    date_now = datetime.now()
    cria_rotinas(rota_totalbus, date_now, ["leito", "convencional"], 3, 3)
    cria_rotinas(rota_totalbus, date_now + timedelta(minutes=10), ["executivo"], 3, 1)

    hits = integracao_grupos_svc.get_detalhes_rotinas([rota_totalbus.id], {rota_totalbus.id: [99, 80]})
    assert hits == {}


@time_machine.travel(datetime(2022, 7, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_classes_trechos_vendidos_e_datetime_ida_por_rotas_e_rotinas(rota_mock, mock_rotinatrechos):
    rtv1 = mock_rotinatrechos.rt[1]
    rtv2 = mock_rotinatrechos.rt[3]
    tv1 = rtv1.trecho_vendido
    tv2 = rtv2.trecho_vendido
    (
        rota_resp,
        dates_rotinas,
    ) = integracao_grupos_svc.get_classes_trechos_vendidos_e_datetime_ida_por_rotas_e_rotinas(
        [rota_mock.id], {rota_mock.id: [tv1.id_internal, tv2.id_internal]}
    )
    assert len(rota_resp) == 1
    assert len(rota_resp[rota_mock.id]) == 2

    map_rotina1 = rota_resp[rota_mock.id][rtv1.rotina_id]
    assert len(map_rotina1["classes"]) == 1
    assert map_rotina1["classes"][0]["tipo_assento"] == tv1.classe_buser
    assert map_rotina1["classes"][0]["max_capacity"] == tv1.capacidade_classe

    assert map_rotina1["trechos_classes"][tv1.id_internal][tv2.classe_buser] == Decimal("14.99")

    map_rotina2 = rota_resp[rota_mock.id][rtv2.rotina_id]
    assert len(map_rotina2["classes"]) == 1
    assert map_rotina2["classes"][0]["tipo_assento"] == tv2.classe_buser
    assert map_rotina2["classes"][0]["max_capacity"] == max(tv1.capacidade_classe, tv2.capacidade_classe)

    assert map_rotina2["trechos_classes"][tv1.id_internal][tv2.classe_buser] == Decimal("14.99")
    assert map_rotina2["trechos_classes"][tv2.id_internal][tv2.classe_buser] == Decimal("14.99")

    assert len(dates_rotinas) == 1
    assert len(dates_rotinas[rota_mock.id]) == 2
    assert dates_rotinas[rota_mock.id][rtv1.rotina.id] == rtv1.rotina.datetime_ida
    assert dates_rotinas[rota_mock.id][rtv2.rotina.id] == rtv2.rotina.datetime_ida


@time_machine.travel(datetime(2022, 7, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_get_classes_trechos_vendidos_e_datetime_ida_por_rotas_e_rotinas_sem_classe(rota_mock, mock_rotinatrechos):
    rtv1 = mock_rotinatrechos.rt[0]
    rtv2 = mock_rotinatrechos.rt[2]
    tv1 = rtv1.trecho_vendido
    tv2 = rtv2.trecho_vendido
    tv2.tipo_assento.tipo_assento_buser_preferencial = None
    tv2.tipo_assento.save()
    (
        rota_resp,
        dates_rotinas,
    ) = integracao_grupos_svc.get_classes_trechos_vendidos_e_datetime_ida_por_rotas_e_rotinas(
        [rota_mock.id], {rota_mock.id: [tv1.id_internal, tv2.id_internal]}
    )
    assert len(rota_resp) == 1
    assert len(rota_resp[rota_mock.id]) == 1

    map_rotina1 = rota_resp[rota_mock.id][rtv1.rotina_id]
    assert len(map_rotina1["classes"]) == 1
    assert map_rotina1["classes"][0]["tipo_assento"] == tv1.classe_buser
    assert map_rotina1["classes"][0]["max_capacity"] == tv1.capacidade_classe

    assert map_rotina1["trechos_classes"][tv1.id_internal][tv1.classe_buser] == Decimal("12.99")

    assert len(dates_rotinas) == 1
    assert len(dates_rotinas[rota_mock.id]) == 1
    assert dates_rotinas[rota_mock.id][rtv1.rotina.id] == rtv1.rotina.datetime_ida


@time_machine.travel("2022-01-01 12:30:00")
def test_get_data_limite_rotas():
    rotas = [
        baker.make("rodoviaria.Rota", data_limite=datetime(2022, 3, 1)),
        baker.make("rodoviaria.Rota", data_limite=datetime(2022, 8, 1)),
        baker.make("rodoviaria.Rota"),
    ]
    result = integracao_grupos_svc._get_data_limite_rotas([r.id for r in rotas])
    assert result[rotas[0].id] == datetime(2022, 3, 1).date()
    assert result[rotas[1].id] == datetime(2022, 5, 31).date()
    assert result[rotas[2].id] == datetime(2022, 5, 31).date()


def test_map_datetimes_a_ignorar_por_hora():
    datetimes_a_ignorar_por_rota_id = {
        1: [to_tz(datetime(2023, 1, 1), "UTC")],
        2: [to_tz(datetime(2023, 1, 1), "UTC")],
    }
    first_timezone_linkado = {1: "America/Sao_Paulo", 2: "America/Campo_Grande"}
    map = integracao_grupos_svc._map_datetimes_a_ignorar_por_hora(
        datetimes_a_ignorar_por_rota_id, first_timezone_linkado
    )
    assert map[1]["21:00"] == [datetime(2022, 12, 31).date()]
    assert map[2]["20:00"] == [datetime(2022, 12, 31).date()]


def test_get_detalhes_rotinas_first_chkpoint_sem_trecho_vendido(rota_completa, rotina_trecho_vendido):
    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_completa.rota.id],
        {rota_completa.rota.id: [rotina_trecho_vendido.trecho_vendido.id_internal]},
    )
    hits = hits_por_rota[rota_completa.rota.id]
    assert len(hits) == 1
    assert hits[0]["horario"] == rota_completa.rotina.datetime_ida.strftime("%H:%M")


def test_get_detalhes_rotinas_first_chkpoint_nao_integrado(rota_completa, rotina_trecho_vendido):
    rota_completa.checkpoints[0].local.local_embarque_internal_id = None
    rota_completa.checkpoints[0].local.save()

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_completa.rota.id],
        {rota_completa.rota.id: [rotina_trecho_vendido.trecho_vendido.id_internal]},
    )
    hits = hits_por_rota[rota_completa.rota.id]
    assert len(hits) == 1
    tz_partida = rota_completa.checkpoints[0].local.cidade.cidade_internal.timezone
    data_partida = to_tz(
        (
            rota_completa.rotina.datetime_ida
            + rota_completa.checkpoints[1].duracao
            + rota_completa.checkpoints[1].tempo_embarque
        ),
        tz_partida,
    )
    assert hits[0]["horario"] == data_partida.strftime("%H:%M")
    assert len(hits[0]["datas"]) == 13


def test_get_detalhes_rotinas_first_chkpoint_nao_integrado_filtro_datetime_a_ignorar(
    rota_completa, rotina_trecho_vendido
):
    rota_completa.checkpoints[0].local.local_embarque_internal_id = None
    rota_completa.checkpoints[0].local.save()
    tz_partida = rota_completa.checkpoints[0].local.cidade.cidade_internal.timezone
    tz_staff = rota_completa.checkpoints[1].local.cidade.cidade_internal.timezone

    # data_partida tera o valor real da saida na API considerando a partida do checkpoint[1]
    # mais detalhes do porque do calculo ser assim em: rota_svc.atualizar_horarios_e_duracoes_por_timezone()
    data_partida = to_tz(
        (
            rota_completa.rotina.datetime_ida
            + rota_completa.checkpoints[1].duracao
            + rota_completa.checkpoints[1].tempo_embarque
        ),
        tz_partida,
    )
    datetimes_a_ignorar = []
    dt = today_midnight() - timedelta(days=7)
    for _d in range(0, 12):
        dt = dt + timedelta(days=7)
        dt_ignorar = datetime(dt.year, dt.month, dt.day, data_partida.hour, data_partida.minute)
        # Faz replace do timezone para pegar o horario e tz como está no Staff
        dt_ignorar = replace_timezone(dt_ignorar, tz_staff)
        datetimes_a_ignorar.append(to_tz(dt_ignorar, "UTC"))

    hits_por_rota = integracao_grupos_svc.get_detalhes_rotinas(
        [rota_completa.rota.id],
        {rota_completa.rota.id: [rotina_trecho_vendido.trecho_vendido.id_internal]},
        {rota_completa.rota.id: datetimes_a_ignorar},
    )
    hits = hits_por_rota[rota_completa.rota.id]
    assert len(hits) == 1
    assert hits[0]["horario"] == to_tz(
        data_partida,
        rota_completa.checkpoints[0].local.cidade.cidade_internal.timezone,
    ).strftime("%H:%M")
    # deixa uma só pra vir a rotina
    assert len(hits[0]["datas"]) == 1


def test_filtra_datas_pelo_inicio_e_fim():
    start = datetime(2000, 1, 1).date()
    end = datetime(2000, 3, 1).date()
    rotina = {
        "datas": [
            {"data": datetime(1999, 12, 31).date()},
            {"data": datetime(2000, 1, 1).date()},
            {"data": datetime(2000, 3, 1).date()},
            {"data": datetime(2000, 3, 2).date()},
        ]
    }
    integracao_grupos_svc._filtra_datas_pelo_inicio_e_fim(start, end, rotina)
    assert rotina
    assert rotina["datas"]
    assert len(rotina["datas"]) == 2
    assert rotina["datas"][0]["data"] == datetime(2000, 1, 1).date()
    assert rotina["datas"][1]["data"] == datetime(2000, 3, 1).date()


def test_remove_datas_vetadas():
    datas_pra_remover_por_rota_id = {
        1: {
            "08:00": [datetime(2000, 1, 1).date(), datetime(2000, 3, 1).date()],
            # horario diferente, não deve remover da rotina das 08:00
            "09:00": [datetime(1999, 12, 31).date(), datetime(2000, 3, 2).date()],
        }
    }
    rotina = {
        "horario": "08:00",
        "datas": [
            {"data": datetime(1999, 12, 31).date()},
            {"data": datetime(2000, 1, 1).date()},
            {"data": datetime(2000, 3, 1).date()},
            {"data": datetime(2000, 3, 2).date()},
        ],
    }

    integracao_grupos_svc._remove_datas_vetadas(datas_pra_remover_por_rota_id, 1, rotina)
    assert rotina
    assert rotina["datas"]
    assert len(rotina["datas"]) == 2
    assert rotina["datas"][0]["data"] == datetime(1999, 12, 31).date()
    assert rotina["datas"][1]["data"] == datetime(2000, 3, 2).date()


@pytest.fixture
def rota_completa(totalbus_company):
    inicio = baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=12,
        cidade=baker.make(
            "rodoviaria.Cidade",
            timezone="America/Sao_Paulo",
            cidade_internal=baker.make("rodoviaria.CidadeInternal", timezone="America/Sao_Paulo"),
        ),
    )
    meio = baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=11,
        cidade=baker.make(
            "rodoviaria.Cidade",
            timezone="America/Cuiaba",
            cidade_internal=baker.make("rodoviaria.CidadeInternal", timezone="America/Cuiaba"),
        ),
    )
    fim = baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=10,
        cidade=baker.make(
            "rodoviaria.Cidade",
            timezone="America/Sao_Paulo",
            cidade_internal=baker.make("rodoviaria.CidadeInternal", timezone="America/Sao_Paulo"),
        ),
    )
    rota = baker.make(
        "rodoviaria.Rota",
        id_hash="2063206d0bac9dv97vd3ev51vd6v86v5ev7bv88v",
        id_external="12",
        id_internal=1,
        company=totalbus_company,
        data_limite=to_default_tz(datetime.now() + timedelta(days=90)).date(),
    )
    checkpoints = [
        baker.make(
            "rodoviaria.Checkpoint",
            rota=rota,
            idx=0,
            local=inicio,
            id_external=inicio.id_external,
            duracao=timedelta(hours=0),
            tempo_embarque=timedelta(minutes=0),
        ),
        baker.make(
            "rodoviaria.Checkpoint",
            rota=rota,
            idx=1,
            local=meio,
            id_external=meio.id_external,
            duracao=timedelta(hours=10),
            tempo_embarque=timedelta(minutes=10),
        ),
        baker.make(
            "rodoviaria.Checkpoint",
            rota=rota,
            idx=2,
            local=fim,
            id_external=fim.id_external,
            duracao=timedelta(hours=5),
            tempo_embarque=timedelta(minutes=10),
        ),
    ]
    rotina = baker.make(
        "rodoviaria.Rotina",
        rota=rota,
        datetime_ida=to_default_tz(today_midnight() + timedelta(hours=8, minutes=30)),
    )
    return SimpleNamespace(rota=rota, checkpoints=checkpoints, rotina=rotina)


@pytest.fixture
def rotina_trecho_vendido(rota_completa):
    return baker.make(
        "rodoviaria.RotinaTrechoVendido",
        rotina=rota_completa.rotina,
        datetime_ida_trecho_vendido=(
            rota_completa.rotina.datetime_ida
            + rota_completa.checkpoints[1].duracao
            + rota_completa.checkpoints[1].tempo_embarque
        ),
        trecho_vendido=baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_completa.rota,
            id_internal=12,
            classe="executivo",
            capacidade_classe=12,
            preco=60.00,
            origem=rota_completa.checkpoints[1].local,
            destino=rota_completa.checkpoints[2].local,
            tipo_assento__tipo_assento_buser_preferencial="executivo",
        ),
    )


def test_get_detalhes_rotinas_uma_rota(rota_totalbus):
    date_now = datetime.now()
    leito_convencional_dia, leito_convencional_hora, _ = cria_rotinas(
        rota_totalbus, date_now, ["leito", "convencional"], 3, 3
    )
    executivo_dia, executivo_hora, _ = cria_rotinas(
        rota_totalbus, date_now + timedelta(minutes=10), ["executivo"], 3, 1
    )

    hits = integracao_grupos_svc.get_detalhes_rotinas_uma_rota(
        rota_totalbus.id,
        [0, 1, 2],
        None,
        midnight(date_now).date(),
        midnight(date_now + timedelta(days=90)).date(),
    )
    assert len(hits) == 2

    leito_convencional = [
        x for x in hits if len(x["classes"]) == 2 and x["classes"][0]["tipo_assento"] in ["leito", "convencional"]
    ][0]
    assert leito_convencional["horario"] == leito_convencional_hora
    assert leito_convencional["dia_semana"] == leito_convencional_dia
    assert len([x for x in leito_convencional["datas"] if x.get("from_api")]) == 3
    assert len([x for x in leito_convencional["datas"] if not x.get("from_api")]) == 10
    assert len(leito_convencional["trechos_vendidos"]) == 3
    assert leito_convencional["classes"][0]["id"]

    executivo = [x for x in hits if len(x["classes"]) == 1 and x["classes"][0]["tipo_assento"] in ["executivo"]][0]
    assert executivo["horario"] == executivo_hora
    assert executivo["dia_semana"] == executivo_dia
    assert executivo["classes"][0]["id"]
    assert len([x for x in executivo["datas"] if x.get("from_api")]) == 3
    assert len([x for x in executivo["datas"] if not x.get("from_api")]) == 10
    assert len(executivo["trechos_vendidos"]) == 1


def test_get_detalhes_rotinas_uma_rota_sem_rotinas(rota_totalbus):
    date_now = datetime.now()
    hits = integracao_grupos_svc.get_detalhes_rotinas_uma_rota(
        rota_totalbus.id,
        [0, 1, 2],
        None,
        midnight(date_now).date(),
        midnight(date_now + timedelta(days=90)).date(),
    )
    assert hits == []


def test_get_detalhes_rotinas_uma_rota_sem_trechos(rota_totalbus):
    date_now = datetime.now()
    cria_rotinas(rota_totalbus, datetime.now(), ["leito", "convencional"], 3, 0)

    hits = integracao_grupos_svc.get_detalhes_rotinas_uma_rota(
        rota_totalbus.id,
        [0, 1, 2],
        None,
        midnight(date_now).date(),
        midnight(date_now + timedelta(days=90)).date(),
    )
    assert hits == []


def test_get_detalhes_rotinas_uma_rota_com_filtro_start_maior_que_datas_api(rota_totalbus):
    date_now = datetime.now()
    leito_convencional_dia, leito_convencional_hora, _ = cria_rotinas(
        rota_totalbus, date_now, ["leito", "convencional"], 2, 3
    )
    executivo_dia, executivo_hora, _ = cria_rotinas(
        rota_totalbus, date_now + timedelta(minutes=10), ["executivo"], 2, 1
    )

    hits = integracao_grupos_svc.get_detalhes_rotinas_uma_rota(
        rota_totalbus.id,
        [0, 1, 2],
        None,
        midnight(date_now + timedelta(days=10)).date(),
        midnight(date_now + timedelta(days=90)).date(),
    )
    assert len(hits) == 2

    leito_convencional = [
        x for x in hits if len(x["classes"]) == 2 and x["classes"][0]["tipo_assento"] in ["leito", "convencional"]
    ][0]
    assert leito_convencional["horario"] == leito_convencional_hora
    assert leito_convencional["dia_semana"] == leito_convencional_dia
    assert len([x for x in leito_convencional["datas"] if x.get("from_api")]) == 0
    assert len([x for x in leito_convencional["datas"] if not x.get("from_api")]) == 11
    assert len(leito_convencional["trechos_vendidos"]) == 3

    executivo = [x for x in hits if len(x["classes"]) == 1 and x["classes"][0]["tipo_assento"] in ["executivo"]][0]
    assert executivo["horario"] == executivo_hora
    assert executivo["dia_semana"] == executivo_dia
    assert len([x for x in executivo["datas"] if x.get("from_api")]) == 0
    assert len([x for x in executivo["datas"] if not x.get("from_api")]) == 11
    assert len(executivo["trechos_vendidos"]) == 1


def test_get_detalhes_rotinas_uma_rota_com_filtro_rotinas_trechos_inexistente(rota_totalbus):
    date_now = datetime.now()
    cria_rotinas(rota_totalbus, date_now, ["leito", "convencional"], 3, 3)
    cria_rotinas(rota_totalbus, date_now + timedelta(minutes=10), ["executivo"], 3, 1)

    hits = integracao_grupos_svc.get_detalhes_rotinas_uma_rota(
        rota_totalbus.id,
        [99, 80],
        None,
        midnight(date_now).date(),
        midnight(date_now + timedelta(days=90)).date(),
    )
    assert hits == []


def test_get_datas_rotinas_com_trechos_da_api_criacao_desordenada(rota_totalbus, django_assert_num_queries):
    date_now = datetime.now()
    leito_convencional_dia, leito_convencional_hora, _ = cria_rotinas(
        rota_totalbus, date_now, ["leito", "convencional"], 2, 3, asc=False
    )
    with django_assert_num_queries(1, connection=connections["rodoviaria"]):
        result = list(
            integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api(
                [rota_totalbus.id],
                {rota_totalbus.id: [0, 1, 2]},
                {rota_totalbus.id: timedelta(0)},
                {rota_totalbus.id: "America/Sao_Paulo"},
            )[rota_totalbus.id].values()
        )
    assert len(result) == 1
    assert result[0]["horario"] == leito_convencional_hora
    assert result[0]["dia_semana"] == leito_convencional_dia
    assert len(result[0]["datas"]) == 2
    assert result[0]["datas"][0]["data"] == date_now.date() + timedelta(days=7)


def test_rotinas_semanais_com_menos_trechos(rota_totalbus):
    date_now = datetime.now()
    leito_convencional_dia, leito_convencional_hora, rotinas = cria_rotinas(
        rota_totalbus, date_now, ["convencional"], 2, 3, asc=False
    )
    rotina_trecho_vendido = rotinas[0][1][0]
    trecho_vendido_id_internal = rotina_trecho_vendido.trecho_vendido.id_internal
    # remove a RotinaTrechoVendido de uma das rotinas
    rotina_trecho_vendido.delete()

    # teste de sanidade. Uma das rotinas deve ter um trecho a menos, mas ao final do teste deve retornar 3
    assert RotinaTrechoVendido.objects.filter(rotina_id=rotinas[0][0].id).count() == 2
    assert RotinaTrechoVendido.objects.filter(rotina_id=rotinas[1][0].id).count() == 3

    result = integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api(
        [rota_totalbus.id],
        {rota_totalbus.id: [0, 1, 2]},
        {rota_totalbus.id: timedelta(0)},
        {rota_totalbus.id: "America/Sao_Paulo"},
    )[rota_totalbus.id]
    result = list(result.values())

    assert len(result) == 1
    assert result[0]["horario"] == leito_convencional_hora
    assert result[0]["dia_semana"] == leito_convencional_dia
    assert len(result[0]["datas"]) == 2
    assert result[0]["datas"][0]["data"] == date_now.date() + timedelta(days=7)

    # valida se o id_internal do trecho_vendido vem na lista, mesmo uma das rotinas não tendo
    assert trecho_vendido_id_internal in result[0]["trechos_vendidos"]
    assert len(result[0]["trechos_vendidos"]) == 3


def test_rotinas_semanais_com_trechos_diferentes(rota_totalbus):
    date_now = datetime.now()
    leito_convencional_dia, leito_convencional_hora, rotinas = cria_rotinas(
        rota_totalbus, date_now, ["convencional"], 2, 3, asc=False
    )
    # Como o codigo serve pra enviar dados pro buser_django, pra todos os efeitos,
    # mudar o id_internal é como se fosse um trecho novo
    trecho_vendido_id_internal = 4
    rotinas[0][1][0].trecho_vendido.id_internal = trecho_vendido_id_internal
    rotinas[0][1][0].trecho_vendido.save()

    # teste de sanidade. Mesma quantidade de trechos, mas como existe diferença entre os trechos, ao final deve trazer 4
    assert RotinaTrechoVendido.objects.filter(rotina_id=rotinas[0][0].id).count() == 3
    assert RotinaTrechoVendido.objects.filter(rotina_id=rotinas[1][0].id).count() == 3

    result = integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api(
        [rota_totalbus.id],
        {rota_totalbus.id: [0, 1, 2, trecho_vendido_id_internal]},
        {rota_totalbus.id: timedelta(0)},
        {rota_totalbus.id: "America/Sao_Paulo"},
    )[rota_totalbus.id]
    result = list(result.values())

    assert len(result) == 1
    assert result[0]["horario"] == leito_convencional_hora
    assert result[0]["dia_semana"] == leito_convencional_dia
    assert len(result[0]["datas"]) == 2
    assert result[0]["datas"][0]["data"] == date_now.date() + timedelta(days=7)

    # valida se o id_internal do trecho_vendido vem na lista, mesmo uma das rotinas não tendo
    assert trecho_vendido_id_internal in result[0]["trechos_vendidos"]
    assert len(result[0]["trechos_vendidos"]) == 4


def test_rotinas_diferentes_com_trechos_diferentes_nao_devem_ter_trecho_uma_da_outra(rota_totalbus):
    date_now = datetime.now()
    leito_convencional_dia, leito_convencional_hora, rotinas = cria_rotinas(
        rota_totalbus, date_now, ["convencional"], 2, 3, asc=False
    )
    # Como o codigo serve pra enviar dados pro buser_django, pra todos os efeitos,
    # mudar o id_internal é como se fosse um trecho novo
    trecho_vendido_id_internal = 4
    rotinas[0][1][0].trecho_vendido.id_internal = trecho_vendido_id_internal
    rotinas[0][1][0].trecho_vendido.save()

    # muda dia da semana da rotina, deve considerar como rotina distinta
    rotinas[0][0].datetime_ida = rotinas[0][0].datetime_ida + timedelta(days=1)
    novo_dia_semana = rotinas[0][0].datetime_ida.strftime("%a")
    rotinas[0][0].save()

    # teste de sanidade. Mesma quantidade de trechos, diferença entre os trechos, mas como são dias diferentes,
    # ele não deveria considerar
    assert RotinaTrechoVendido.objects.filter(rotina_id=rotinas[0][0].id).count() == 3
    assert RotinaTrechoVendido.objects.filter(rotina_id=rotinas[1][0].id).count() == 3

    result = integracao_grupos_svc._get_datas_rotinas_com_trechos_da_api(
        [rota_totalbus.id],
        {rota_totalbus.id: [0, 1, 2, trecho_vendido_id_internal]},
        {rota_totalbus.id: timedelta(0)},
        {rota_totalbus.id: "America/Sao_Paulo"},
    )[rota_totalbus.id]
    result = list(result.values())

    assert len(result) == 2
    rotina_novo_dia_semana = next(r for r in result if r["dia_semana"] == novo_dia_semana)
    assert rotina_novo_dia_semana["horario"] == leito_convencional_hora

    # valida se o id_internal do trecho_vendido vem na lista, mesmo uma das rotinas não tendou
    assert trecho_vendido_id_internal in rotina_novo_dia_semana["trechos_vendidos"]
    assert len(rotina_novo_dia_semana["trechos_vendidos"]) == 3

    rotina_antigo_dia_semana = next(r for r in result if r["dia_semana"] != novo_dia_semana)
    assert rotina_antigo_dia_semana["horario"] == leito_convencional_hora
    assert rotina_antigo_dia_semana["dia_semana"] == leito_convencional_dia

    # valida se o id_internal do trecho_vendido vem na lista, mesmo uma das rotinas não tendou
    assert trecho_vendido_id_internal not in rotina_antigo_dia_semana["trechos_vendidos"]
    assert len(rotina_antigo_dia_semana["trechos_vendidos"]) == 3


def test_add_datas_rotinas_sem_trechos_vendidos():
    rota_id = 1
    rotinas_datetime_ida = [
        datetime(2024, 7, 1, 19),
        datetime(2024, 7, 8, 19),
        datetime(2024, 7, 15, 19),
        datetime(2024, 7, 22, 19),
    ]
    rotinas_por_hora_dia_classes = {
        ("19:00", "Mon", "semi leito"): {
            "horario": "19:00",
            "dia_semana": "Mon",
            "datas": [
                {"data": datetime(2024, 7, 15).date(), "from_api": True},
            ],
        },
        ("19:00", "Mon", "semi leito,leito"): {
            "horario": "19:00",
            "dia_semana": "Mon",
            "datas": [
                {"data": datetime(2024, 7, 1).date(), "from_api": True},
                {"data": datetime(2024, 7, 22).date(), "from_api": True},
            ],
        },
    }
    timezone = "America/Sao_Paulo"
    duracao_ate_linkado = timedelta(minutes=0)
    opa = integracao_grupos_svc._add_datas_rotinas_sem_trechos_vendidos(
        rota_id,
        rotinas_datetime_ida,
        rotinas_por_hora_dia_classes,
        timezone,
        duracao_ate_linkado,
    )
    opa = list(opa.values())
    assert opa[0]["datas"] == [
        {"data": datetime(2024, 7, 15).date(), "from_api": True},
    ]
    assert opa[1]["datas"] == [
        {"data": datetime(2024, 7, 1).date(), "from_api": True},
        {"data": datetime(2024, 7, 8).date(), "from_api": True},
        {"data": datetime(2024, 7, 22).date(), "from_api": True},
    ]


def test_verificar_elegibilidade_auto_integra_operacao():
    # dada uma empresa com a qtd min de rotas para integração automática
    c = baker.make(
        "rodoviaria.Company",
        company_internal_id=2,
        features=["auto_integra_operacao"],
        auto_integra_rotas_min=4,
        margem_dias_busca_operacao=10,
    )
    baker.make("rodoviaria.Rotina", rota__company=c, datetime_ida=timezone.now() + timedelta(days=4), _quantity=4)
    elegivel = auto_integra_operacao_svc.verificar_elegibilidade_auto_integra_operacao(c.company_internal_id)
    # a requisição deve retornar que ela é elegível
    assert elegivel is True


def test_verificar_elegibilidade_auto_integra_operacao_sem_rotas_suficientes():
    # dada uma empresa com 1 rota e qtd min para ser elegível para integração automatica igual a 4
    c = baker.make(
        "rodoviaria.Company",
        company_internal_id=3,
        features=["auto_integra_operacao"],
        auto_integra_rotas_min=4,
    )
    baker.make("rodoviaria.Rota", company=c)
    # não deve ser elegível para a integração automática
    with pytest.raises(RodoviariaException, match=r"A empresa de company_internal_id=\d"):
        auto_integra_operacao_svc.verificar_elegibilidade_auto_integra_operacao(c.company_internal_id)


def test_verificar_elegibilidade_auto_integra_operacao_sem_rotas_ativas_suficientes():
    # dada uma empresa com 6 rotas, sendo apenas 3 ativas
    # e com restrição para criar grupos somente qd houver mais de 4 rotas ativas
    c = baker.make(
        "rodoviaria.Company",
        company_internal_id=5,
        features=["auto_integra_operacao"],
        margem_dias_busca_operacao=10,
        auto_integra_rotas_min=4,
    )
    baker.make(
        "rodoviaria.Rotina",
        rota__company=c,
        rota__ativo=False,
        datetime_ida=timezone.now() + timedelta(days=4),
        _quantity=3,
    )
    baker.make(
        "rodoviaria.Rotina",
        rota__company=c,
        rota__ativo=True,
        datetime_ida=timezone.now() + timedelta(days=4),
        _quantity=3,
    )
    # não deve ser elegível para a integração automática
    with pytest.raises(RodoviariaException, match=r"A empresa de company_internal_id=\d"):
        auto_integra_operacao_svc.verificar_elegibilidade_auto_integra_operacao(c.company_internal_id)


def test_verificar_elegibilidade_auto_integra_operacao_sem_rotas_suficientes_considerando_rotinas_dentro_da_margem():
    # dada uma empresa com 6 rotas, sendo apenas 3 ativas
    # e com restrição para criar grupos somente qd houver mais de 4 rotas ativas
    c = baker.make(
        "rodoviaria.Company",
        company_internal_id=1,
        features=["auto_integra_operacao"],
        auto_integra_rotas_min=4,
        margem_dias_busca_operacao=10,
    )
    baker.make(
        "rodoviaria.Rotina",
        rota__company=c,
        rota__ativo=True,
        datetime_ida=timezone.now() + timedelta(days=15),
        _quantity=3,
    )
    baker.make(
        "rodoviaria.Rotina",
        rota__company=c,
        rota__ativo=True,
        datetime_ida=timezone.now() + timedelta(days=4),
        _quantity=3,
    )
    # não deve ser elegível para a integração automática
    with pytest.raises(RodoviariaException, match=r"A empresa de company_internal_id=\d"):
        auto_integra_operacao_svc.verificar_elegibilidade_auto_integra_operacao(c.company_internal_id)

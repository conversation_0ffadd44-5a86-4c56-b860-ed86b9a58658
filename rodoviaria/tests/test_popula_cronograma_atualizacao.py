from datetime import time

from django.core.management import call_command
from model_bakery import baker

from rodoviaria.models.core import Company, CronogramaAtualizacaoOperacao


def test_popula_cronograma_atualizacao_horario_definido_manualmente():
    company = baker.make(
        Company,
        company_internal_id=6247,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        features=[Company.Feature.ITINERARIO, Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO],
    )
    call_command("popula_cronograma_atualizacao")
    horarios = list(CronogramaAtualizacaoOperacao.objects.filter(company=company).order_by("tipo_atualizacao"))
    assert len(horarios) == 3
    assert horarios[0].tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS
    assert horarios[0].dia_semana == 3
    assert horarios[0].horario == time(8, 0)
    assert horarios[1].tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE
    assert horarios[1].dia_semana == 4
    assert horarios[1].horario == time(8, 0)
    assert horarios[2].tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS
    assert horarios[2].dia_semana == 5
    assert horarios[2].horario == time(8, 0)


def test_popula_cronograma_atualizacao_modelo_hibrido():
    company = baker.make(
        Company,
        company_internal_id=6247,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
        features=[Company.Feature.ITINERARIO, Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO],
    )
    call_command("popula_cronograma_atualizacao")
    horarios = list(CronogramaAtualizacaoOperacao.objects.filter(company=company).order_by("tipo_atualizacao"))
    assert len(horarios) == 0


def test_popula_cronograma_atualizacao_empresa_sem_features():
    company = baker.make(Company, company_internal_id=6247, modelo_venda=Company.ModeloVenda.HIBRIDO, features=[])
    call_command("popula_cronograma_atualizacao")
    horarios = list(CronogramaAtualizacaoOperacao.objects.filter(company=company).order_by("tipo_atualizacao"))
    assert len(horarios) == 0


def test_popula_cronograma_atualizacao_horario_automatico():
    company = baker.make(
        Company,
        company_internal_id=1,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        features=[Company.Feature.ITINERARIO, Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO],
    )
    call_command("popula_cronograma_atualizacao")
    horarios = list(CronogramaAtualizacaoOperacao.objects.filter(company=company).order_by("tipo_atualizacao"))
    assert len(horarios) == 3
    assert horarios[0].tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_ROTAS
    assert horarios[0].dia_semana == 2
    assert horarios[0].horario == time(18, 31)
    assert horarios[1].tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.FETCH_DATA_LIMITE
    assert horarios[1].dia_semana == 3
    assert horarios[1].horario == time(18, 31)
    assert horarios[2].tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.FETCH_TRECHOS_VENDIDOS
    assert horarios[2].dia_semana == 4
    assert horarios[2].horario == time(18, 31)


def test_popula_cronograma_atualizacao_atualiza_operacao():
    company = baker.make(
        Company,
        company_internal_id=1,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
        features=[Company.Feature.ITINERARIO, Company.Feature.ADD_PAX_STAFF, Company.Feature.BUSCAR_SERVICO],
        integracao__name="ti_sistemas",
    )
    call_command("popula_cronograma_atualizacao")
    horarios = list(
        CronogramaAtualizacaoOperacao.objects.filter(company=company).order_by("tipo_atualizacao", "dia_semana")
    )
    assert len(horarios) == 7
    for index, h in enumerate(horarios):
        assert h.tipo_atualizacao == CronogramaAtualizacaoOperacao.Tipos.DESCOBRIR_OPERACAO
        assert h.dia_semana == index + 1
        assert h.horario == time(18, 31)

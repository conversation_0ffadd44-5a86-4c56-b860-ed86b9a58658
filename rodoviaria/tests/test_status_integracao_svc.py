from datetime import datetime, timed<PERSON>ta
from decimal import Decimal as D
from types import SimpleNamespace
from unittest import mock

import pytest
import time_machine
from django.db import connections
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.forms.grupo_trechoclasse_form import CidadeInfos, TrechoClasseInternoInfos
from rodoviaria.models.core import Company, TrechoClasse, TrechoClasseError
from rodoviaria.service import status_integracao_svc
from rodoviaria.service.exceptions import RodoviariaException, RodoviariaTrechoclasseFactoryException
from rodoviaria.service.status_integracao_svc import StatusIntegracaoSVC
from rodoviaria.views_schemas import UpdateGruposClasseLink


def test_status_integracao_sem_dados():
    with pytest.raises(Exception) as exc:
        StatusIntegracaoSVC()
    assert "Precisa passar internal_grupo_id ou internal_trecho_classe_id" in str(exc.value)


def test_status_integracao_trecho_classe_ok(buser_grupos, praxio_grupos):
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        preco_rodoviaria=150,
        vagas=5,
        grupo_classe__tipo_assento_external="Leito",
        external_datetime_ida=datetime(2024, 1, 1, 10, 30),
    )
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_OK,
            "preco_rodoviaria": 150,
            "vagas": 5,
            "external_tipo_assento": "Leito",
            "external_datetime_ida": to_default_tz(datetime(2024, 1, 1, 10, 30)),
        }
    }


def test_status_integracao_trecho_classe_caso_trecho_possua_registro_ativo_inativo_deve_dar_preferencia_ao_ativo(
    buser_grupos, praxio_grupos
):
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        preco_rodoviaria=500,
        vagas=500,
        active=True,
        grupo_classe__tipo_assento_external="Leito",
        external_datetime_ida=datetime(2024, 1, 1, 10, 30),
    )
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        preco_rodoviaria=222,
        vagas=222,
        active=False,
    )
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_OK,
            "preco_rodoviaria": 500,
            "vagas": 500,
            "external_tipo_assento": "Leito",
            "external_datetime_ida": to_default_tz(datetime(2024, 1, 1, 10, 30)),
        }
    }


def test_status_integracao_trecho_classe_error(buser_grupos, praxio_grupos):
    baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
    )
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_NAO_ENCONTRADA,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


def test_status_integracao_trecho_classe_error_com_motivo_fechamento(buser_grupos, praxio_grupos):
    baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        motivo=TrechoClasseError.Motivo.MISMATCH_DE_CLASSE,
        servicos_proximos_parseados=[("16:00", "semi leito")],
    )
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_NAO_ENCONTRADA,
            "preco_rodoviaria": None,
            "vagas": None,
            "error": f"[{TrechoClasseError.Motivo.MISMATCH_DE_CLASSE}] [['16:00', 'semi leito']]",
        }
    }


def test_status_integracao_trecho_classe_error_e_normal_mais_recente(buser_grupos, praxio_grupos):
    tc_error = baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
    )
    TrechoClasseError.objects.filter(pk=tc_error.id).update(updated_at=timezone.now() - timedelta(days=1))
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        preco_rodoviaria=150,
        vagas=10,
        grupo_classe__tipo_assento_external="Leito",
        external_datetime_ida=datetime(2024, 1, 1, 10, 30),
    )
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_OK,
            "preco_rodoviaria": 150,
            "vagas": 10,
            "external_tipo_assento": "Leito",
            "external_datetime_ida": to_default_tz(datetime(2024, 1, 1, 10, 30)),
        }
    }


def test_status_integracao_trecho_classe_normal_e_error_mais_recente(buser_grupos, praxio_grupos):
    baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
    )
    tc_normal = baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        preco_rodoviaria=150,
        vagas=10,
    )
    TrechoClasse.objects.filter(pk=tc_normal.id).update(updated_at=timezone.now() - timedelta(days=1))
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_NAO_ENCONTRADA,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")))
def test_status_integracao_trecho_classe_error_e_normal_e_error_mais_recente(buser_grupos, praxio_grupos):
    datetime_now = to_default_tz(datetime(2022, 10, 20, 16, 43, 34))
    tc_error_antigo = baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
    )
    tc_normal = baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        preco_rodoviaria=150,
        vagas=10,
    )
    TrechoClasse.objects.filter(pk=tc_error_antigo.id).update(updated_at=datetime_now - timedelta(days=2))
    TrechoClasse.objects.filter(pk=tc_normal.id).update(updated_at=datetime_now - timedelta(days=1))
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_NAO_ENCONTRADA,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


def test_status_integracao_trecho_classe_inexistente(buser_grupos, praxio_grupos):
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_PENDENTE,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


def test_status_integracao_trecho_classe_inativa(buser_grupos):
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        active=False,
        preco_rodoviaria=139,
        vagas=5,
    )
    result = StatusIntegracaoSVC(internal_trecho_classe_id=buser_grupos.ida.trechoclasse.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_PENDENTE,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


def test_status_integracao_grupo_error(buser_grupos, praxio_grupos):
    baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
    )
    result = StatusIntegracaoSVC(internal_grupo_id=buser_grupos.ida.grupo.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_NAO_ENCONTRADA,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


def test_status_integracao_grupo_error_e_normal_mais_recente(buser_grupos, praxio_grupos):
    tc_error = baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
    )
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        preco_rodoviaria=150,
        vagas=10,
    )
    TrechoClasseError.objects.filter(pk=tc_error.id).update(updated_at=timezone.now() - timedelta(days=1))
    result = StatusIntegracaoSVC(internal_grupo_id=buser_grupos.ida.grupo.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_OK,
            "preco_rodoviaria": 150,
            "vagas": 10,
        }
    }


def test_status_integracao_grupo_normal_e_error_mais_recente(buser_grupos, praxio_grupos):
    baker.make(
        "rodoviaria.TrechoClasseError",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
    )
    tc_normal = baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        preco_rodoviaria=150,
        vagas=10,
    )
    TrechoClasse.objects.filter(pk=tc_normal.id).update(updated_at=timezone.now() - timedelta(days=1))
    result = StatusIntegracaoSVC(internal_grupo_id=buser_grupos.ida.grupo.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_NAO_ENCONTRADA,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


def test_status_integracao_grupo_sem_trecho_classe(buser_grupos, praxio_grupos):
    result = StatusIntegracaoSVC(internal_grupo_id=buser_grupos.ida.grupo.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_PENDENTE,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


def test_status_integracao_grupo_integracao_ok(django_assert_num_queries, buser_grupos, praxio_grupos):
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=buser_grupos.ida.grupo.id)
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        active=True,
        grupo=grupo,
        preco_rodoviaria=120,
        vagas=5,
    )
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.volta.trechoclasse.id,
        active=True,
        grupo=grupo,
        preco_rodoviaria=120,
        vagas=5,
    )
    with django_assert_num_queries(2, connection=connections["rodoviaria"]):
        result = StatusIntegracaoSVC(internal_grupo_id=buser_grupos.ida.grupo.id).verifica()
        assert result == {
            buser_grupos.ida.trechoclasse.id: {
                "status": StatusIntegracaoSVC.INTEGRACAO_OK,
                "preco_rodoviaria": 120,
                "vagas": 5,
            }
        }


def test_status_integracao_grupo_integracao_parcial(buser_grupos, praxio_grupos):
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=buser_grupos.ida.grupo.id)
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        active=True,
        grupo=grupo,
        preco_rodoviaria=150,
        vagas=5,
    )
    result = StatusIntegracaoSVC(internal_grupo_id=buser_grupos.ida.grupo.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_OK,
            "preco_rodoviaria": 150,
            "vagas": 5,
        }
    }


def test_status_integracao_grupo_integracao_pendente(buser_grupos, praxio_grupos):
    result = StatusIntegracaoSVC(internal_grupo_id=buser_grupos.ida.grupo.id).verifica()
    assert result == {
        buser_grupos.ida.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_PENDENTE,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }


def test_status_integracao_grupo_caso_trecho_possua_registro_ativo_inativo_deve_dar_preferencia_ao_ativo(
    django_assert_num_queries, buser_grupos, praxio_grupos
):
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=buser_grupos.ida.grupo.id)
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        active=True,
        grupo=grupo,
        preco_rodoviaria=500,
        vagas=500,
    )
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=buser_grupos.ida.trechoclasse.id,
        active=False,
        grupo=grupo,
        preco_rodoviaria=333,
        vagas=333,
    )
    with django_assert_num_queries(2, connection=connections["rodoviaria"]):
        result = StatusIntegracaoSVC(internal_grupo_id=buser_grupos.ida.grupo.id).verifica()
        assert result == {
            buser_grupos.ida.trechoclasse.id: {
                "status": StatusIntegracaoSVC.INTEGRACAO_OK,
                "preco_rodoviaria": 500,
                "vagas": 500,
            }
        }


@pytest.fixture
def mock_update_status_integracao(buser_grupos, mock_praxio_login, praxio_login):
    grupo = buser_grupos.volta
    result = StatusIntegracaoSVC(internal_trecho_classe_id=grupo.trechoclasse.id).verifica()
    assert result == {
        grupo.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_PENDENTE,
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }

    grupo.grupo.company_id = praxio_login.company.company_internal_id
    grupo.grupo.save()
    cidade = baker.make("rodoviaria.Cidade", company=praxio_login.company)
    baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=grupo.trechoclasse.trecho_vendido.origem_id,
        id_external=97,
        cidade=cidade,
    )
    baker.make(
        "rodoviaria.LocalEmbarque",
        local_embarque_internal_id=grupo.trechoclasse.trecho_vendido.destino_id,
        id_external=58,
        cidade=cidade,
    )

    grupo_classe = grupo.trechoclasse.grupo_classe
    grupo_classe.tipo_assento = "leito"
    grupo_classe.save()

    grupo.trechoclasse.datetime_ida = to_default_tz(datetime.strptime("2021-05-18 23:00", "%Y-%m-%d %H:%M"))
    grupo.trechoclasse.save()
    return SimpleNamespace(grupo=grupo)


def test_update_status_integracao(mock_update_status_integracao, mock_buscar_servico_praxio):
    grupo = mock_update_status_integracao.grupo
    result = StatusIntegracaoSVC(internal_trecho_classe_id=grupo.trechoclasse.id).atualiza()
    assert result == {
        grupo.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_OK,
            "preco_rodoviaria": D("215.00"),
            "vagas": 0,
            "external_tipo_assento": "LEITO DUPLO",
            "external_datetime_ida": to_default_tz(datetime(2021, 5, 18, 23, 0)),
            "last_update": None,
        }
    }


def test_update_status_integracao_hibrido_integrado_por_rotas(
    override_config, mock_update_status_integracao, mock_buscar_servico_praxio, praxio_login
):
    praxio_login.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    praxio_login.company.company_internal_id = 313
    praxio_login.company.save()
    grupo = mock_update_status_integracao.grupo
    grupo.grupo.rotina_onibus_id = 10
    grupo.grupo.modelo_venda = Company.ModeloVenda.HIBRIDO
    grupo.grupo.company_id = None
    grupo.grupo.save()
    with override_config(ROTINAS_INTEGRADAS_RODEROTAS="10,14"):
        result = StatusIntegracaoSVC(internal_trecho_classe_id=grupo.trechoclasse.id).atualiza()
    assert result == {
        grupo.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_OK,
            "preco_rodoviaria": D("215.00"),
            "vagas": 0,
            "external_tipo_assento": "LEITO DUPLO",
            "external_datetime_ida": to_default_tz(datetime(2021, 5, 18, 23, 0)),
            "last_update": None,
        }
    }
    tc_rodoviaria = TrechoClasse.objects.get(trechoclasse_internal_id=grupo.trechoclasse.id)
    assert tc_rodoviaria.grupo.company_integracao_id == praxio_login.company.id


def test_update_status_integracao_batch(mock_update_status_integracao, mock_buscar_servico_praxio):
    grupo = mock_update_status_integracao.grupo
    result = StatusIntegracaoSVC(batch_trecho_classe_ids=[grupo.trechoclasse.id]).atualiza()
    assert result == {
        grupo.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_OK,
            "preco_rodoviaria": D("215.00"),
            "vagas": 0,
            "external_tipo_assento": "LEITO DUPLO",
            "external_datetime_ida": to_default_tz(datetime(2021, 5, 18, 23, 0)),
            "last_update": None,
        }
    }


def test_atualiza_async_default_rate_limit(praxio_grupos_mockado, praxio_company):
    grupo_internal_id = 89321
    trechos_classe = [praxio_grupos_mockado.ida.trechoclasse]

    with mock.patch(
        "rodoviaria.service.status_integracao_svc.trecho_classe_infos",
        return_value=praxio_grupos_mockado.ida.trecho_classe_infos,
    ), mock.patch("rodoviaria.service.status_integracao_svc.group") as group_mock:
        result = StatusIntegracaoSVC(internal_grupo_id=grupo_internal_id).atualiza_async(trechos_classe, praxio_company)
    group_mock.assert_called_once_with([status_integracao_svc.update_trecho_classe_link_task.s(mock.ANY, mock.ANY)])
    group_mock.return_value.apply_async.assert_called_once()
    assert result == {"message": "Os links estão sendo atualizados"}


def test_update_status_integracao_grupo(mock_update_status_integracao, mock_buscar_servico_praxio):
    grupo = mock_update_status_integracao.grupo
    grupo_rodoviaria = baker.make("rodoviaria.Grupo", grupo_internal_id=grupo.grupo.id)
    tc = baker.make("rodoviaria.TrechoClasse", grupo=grupo_rodoviaria, trechoclasse_internal_id=grupo.trechoclasse.id)

    with mock.patch("bp.buserdjango_celery.atualiza_trecho_unico.delay") as mock_atualiza_trecho:
        result = StatusIntegracaoSVC(internal_grupo_id=grupo.grupo.id).atualiza()
    assert result == {"message": "Os links estão sendo atualizados"}
    tc.refresh_from_db()
    mock_atualiza_trecho.assert_called_once_with(
        trecho_classe_id=tc.trechoclasse_internal_id, preco=tc.preco_rodoviaria, vagas=tc.vagas, to_close=False
    )


def test_update_status_integracao_grupo_assert_num_queries(
    buser_grupos, mock_update_status_integracao, django_assert_num_queries
):
    grupo = mock_update_status_integracao.grupo.grupo
    buser_grupos.ida.trechoclasse.grupo = grupo
    buser_grupos.ida.trechoclasse.grupo.save()
    buser_grupos.volta.trechoclasse.grupo = grupo
    buser_grupos.volta.trechoclasse.grupo.save()
    with mock.patch("rodoviaria.service.status_integracao_svc.group") as mock_group, django_assert_num_queries(
        1, connection=connections["rodoviaria"]
    ), django_assert_num_queries(1, connection=connections["default"]):
        result = StatusIntegracaoSVC(internal_grupo_id=grupo.id).atualiza()
    assert result == {"message": "Os links estão sendo atualizados"}
    mock_group.return_value.apply_async.assert_called_once()


def test_update_status_integracao_grupo_nao_encontrado(
    mock_update_status_integracao, mock_buscar_servico_praxio_sem_servico
):
    grupo = mock_update_status_integracao.grupo
    grupo_rodoviaria = baker.make("rodoviaria.Grupo", grupo_internal_id=grupo.grupo.id)
    tc = baker.make("rodoviaria.TrechoClasse", grupo=grupo_rodoviaria, trechoclasse_internal_id=grupo.trechoclasse.id)

    with mock.patch("bp.buserdjango_celery.atualiza_trecho_unico.delay") as mock_atualiza_trecho:
        result = StatusIntegracaoSVC(internal_grupo_id=grupo.grupo.id).atualiza()
    assert result == {"message": "Os links estão sendo atualizados"}
    tc.refresh_from_db()
    mock_atualiza_trecho.assert_called_once_with(
        trecho_classe_id=tc.trechoclasse_internal_id,
        to_close=True,
        motivo_unmatch=f"[{TrechoClasseError.Motivo.SEM_SERVICO}]",
    )


def test_update_status_integracao_grupo_hibrido_nao_adiciona_tag(mock_update_status_integracao, praxio_login):
    praxio_login.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    praxio_login.company.save()
    grupo = mock_update_status_integracao.grupo
    grupo.grupo.modelo_venda = Company.ModeloVenda.HIBRIDO
    grupo.grupo.save()
    grupo_rodoviaria = baker.make("rodoviaria.Grupo", grupo_internal_id=grupo.grupo.id)
    baker.make("rodoviaria.TrechoClasse", grupo=grupo_rodoviaria)

    result = StatusIntegracaoSVC(internal_grupo_id=grupo.grupo.id).atualiza()
    assert not TrechoClasse.objects.filter(tags__name="to_be_updated_in_django", grupo_id=grupo_rodoviaria.id).exists()
    assert result == {"message": "Os links estão sendo atualizados"}


def test_update_trecho_classe_link_task_error():
    company_id = 8532
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    trechoclasse_from_buser_django = TrechoClasseInternoInfos(
        trechoclasse_id=542,
        localembarque_origem_id=599,
        cidade_origem=CidadeInfos(
            id=942,
            timezone="America/Sao_Paulo",
            name="Sao Paulo",
            city_code_ibge=5343403,
        ),
        localembarque_destino_id=1242,
        cidade_destino=CidadeInfos(
            id=5783,
            timezone="America/Sao_Paulo",
            name="Taubate",
            city_code_ibge=4930293,
        ),
        trecho_datetime_ida=datetime(2022, 1, 30, 15, 45),
        grupo_id=95,
        grupo_datetime_ida=datetime(2022, 1, 30, 15, 45),
        grupoclasse_id=150,
        tipo_assento="leito",
    )
    with mock.patch("rodoviaria.service.status_integracao_svc.GrupoTrechoClasseFactory") as mock_factory:
        mock_factory.side_effect = RodoviariaTrechoclasseFactoryException(trechoclasse_id=542)
        status_integracao_svc.update_trecho_classe_link_task(company.id, trechoclasse_from_buser_django.json())


def test_update_grupos_classe_link():
    gc1 = baker.make("rodoviaria.GrupoClasse", grupoclasse_internal_id=8594)
    gc2 = baker.make("rodoviaria.GrupoClasse", grupoclasse_internal_id=2109)
    grupos_classe_to_update = UpdateGruposClasseLink.parse_obj(
        {
            "updates": [
                {"new_id": 10020, "old_id": 8594},
                {"new_id": 9993, "old_id": 2109},
            ]
        }
    )
    status_integracao_svc.update_grupos_classe_link(grupos_classe_to_update)
    gc1.refresh_from_db()
    gc2.refresh_from_db()
    assert gc1.grupoclasse_internal_id == 10020
    assert gc2.grupoclasse_internal_id == 9993


def test_update_status_integracao_servico_nao_encontrado(
    mock_update_status_integracao, mock_buscar_servico_praxio_sem_servico
):
    grupo = mock_update_status_integracao.grupo
    result = StatusIntegracaoSVC(internal_trecho_classe_id=grupo.trechoclasse.id).atualiza()
    assert result == {
        grupo.trechoclasse.id: {
            "status": StatusIntegracaoSVC.INTEGRACAO_NAO_ENCONTRADA,
            "preco_rodoviaria": None,
            "vagas": None,
            "error": f"Serviço não encontrado na API: [{TrechoClasseError.Motivo.SEM_SERVICO}]",
            "last_update": None,
        }
    }


def test_update_status_integracao_connection_error(mock_update_status_integracao, requests_mock):
    grupo = mock_update_status_integracao.grupo
    result = StatusIntegracaoSVC(internal_trecho_classe_id=grupo.trechoclasse.id).atualiza()
    assert result == {
        grupo.trechoclasse.id: {
            "status": StatusIntegracaoSVC.ERRO_API_NAO_RESPONDEU,
            "preco_rodoviaria": None,
            "vagas": None,
            "error": "Erro de conexão com a API do parceiro",
            "last_update": None,
        }
    }


def test_update_status_integracao_erro_desconhecido(mock_update_status_integracao):
    grupo = mock_update_status_integracao.grupo
    with mock.patch.object(PraxioAPI, "_buscar_corridas", side_effect=RodoviariaException("Erro na API")):
        result = StatusIntegracaoSVC(internal_trecho_classe_id=grupo.trechoclasse.id).atualiza()
    assert result == {
        grupo.trechoclasse.id: {
            "status": StatusIntegracaoSVC.ERRO_INESPERADO,
            "preco_rodoviaria": None,
            "vagas": None,
            "error": "Erro na API",
            "last_update": None,
        }
    }

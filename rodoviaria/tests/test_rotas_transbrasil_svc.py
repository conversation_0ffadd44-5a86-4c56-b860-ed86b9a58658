from decimal import Decimal
from types import SimpleNamespace
from unittest import mock

import pytest
from model_bakery import baker

from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.models import VexadoRota
from rodoviaria.models.core import Cidade, CidadeInternal, Company
from rodoviaria.service import rotas_transbrasil_svc
from rodoviaria.tests.vexado import mocker
from rodoviaria.views_schemas import (
    AtualizarLocaisBatchParams,
    AtualizarLocaisParams,
    CadastrarCheckpointParams,
    CadastrarItinerarioParams,
    CadastrarOuEditarRotaHibridoParams,
    CadastrarRotaHibridoParams,
    CadastrarTrechosParams,
    EditarRotaHibridoParams,
    VerificaLinkRotasParams,
)


def test_cadastrar_trechos(vexado_api, vexado_company_multimodelo, vexado_login):
    baker.make("rodoviaria.CidadeInternal", id=1)
    baker.make("rodoviaria.CidadeInternal", id=2)
    baker.make("rodoviaria.CidadeInternal", id=3)
    baker.make(
        "rodoviaria.Cidade",
        cidade_internal_id=1,
        id_external=1,
        company=vexado_company_multimodelo,
    )
    baker.make(
        "rodoviaria.Cidade",
        cidade_internal_id=2,
        id_external=2,
        company=vexado_company_multimodelo,
    )
    baker.make(
        "rodoviaria.Cidade",
        cidade_internal_id=3,
        id_external=3,
        company=vexado_company_multimodelo,
    )
    baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=1234,
        company_integracao=vexado_company_multimodelo,
    )
    baker.make(
        "rodoviaria.Grupo",
        grupo_internal_id=1234,
        company_integracao=vexado_company_multimodelo,
    )
    with mock.patch.object(VexadoAPI, "cadastrar_preco") as mock_cadastrar_trechos, mock.patch.object(
        VexadoAPI, "match_local_de_embarque"
    ) as mock_local_embarque:
        mock_cadastrar_trechos.return_value = True
        mock_local_embarque.return_value = True
        trechos = CadastrarTrechosParams.parse_obj(
            {
                "company_id": vexado_company_multimodelo.company_internal_id,
                "trechos": [
                    {
                        "cidade_origem_id": 1,
                        "local_origem_id": 1,
                        "cidade_destino_id": 2,
                        "local_destino_id": 2,
                        "classe": "Leito",
                        "max_split_value": 15.50,
                    },
                    {
                        "cidade_origem_id": 1,
                        "local_origem_id": 1,
                        "cidade_destino_id": 3,
                        "local_destino_id": 3,
                        "classe": "leito cama",
                        "max_split_value": 17.50,
                    },
                ],
            }
        )
        rotas_transbrasil_svc.cadastrar_trechos(trechos.company_id, trechos.trechos)
    assert mock_cadastrar_trechos.call_count == 2
    mock_cadastrar_trechos.assert_called_with(1, 3, "LEITO_ESPECIAL", Decimal("17.50"))


def test_criar_itinerario(vexado_api, vexado_company_multimodelo, vexado_login):
    baker.make(
        "rodoviaria.Cidade",
        cidade_internal_id=1,
        id_external=777,
        company=vexado_company_multimodelo,
    )
    params = CadastrarItinerarioParams.parse_obj(
        {
            "company_id": vexado_company_multimodelo.company_internal_id,
            "id_rota_external": 1,
            "id_rota_internal": 1,
            "checkpoints": [
                CadastrarCheckpointParams.parse_obj(
                    {
                        "local_embarque_id": 1,
                        "cidade_destino_id": 1,
                        "duracao": "01:30",
                        "ponto_embarque": "Posto da Gruta",
                        "km": 123,
                    }
                )
            ],
        }
    )
    with mock.patch.object(VexadoAPI, "cadastrar_checkpoint") as mock_cadastrar_checkpoint:
        rotas_transbrasil_svc.criar_itinerario(params)
    mock_cadastrar_checkpoint.assert_called_once()


def test_atualizar_locais_embarque(vexado_api, vexado_company_multimodelo, vexado_login):
    baker.make("rodoviaria.CidadeInternal", id=1, name="João Pessoa")
    params = AtualizarLocaisParams.parse_obj(
        {
            "company_id": vexado_company_multimodelo.company_internal_id,
            "id_rota_external": 1,
            "id_rota_internal": 1,
            "checkpoints": [
                CadastrarCheckpointParams.parse_obj(
                    {
                        "cidade_destino_id": 1,
                        "duracao": "2:30",
                        "ponto_embarque": "Nova descrição",
                        "km": 1,
                        "id_rota_external": 1,
                        "local_embarque_id": 1,
                    }
                )
            ],
        }
    )
    with mock.patch.object(VexadoAPI, "atualizar_checkpoint") as mock_atualizar_checkpoint, mock.patch.object(
        VexadoAPI, "listar_trechos", return_value=mocker.MockListarTrechos.response()
    ) as mock_listar_trechos, mock.patch.object(VexadoAPI, "editar_rota") as mock_editar_rota, mock.patch.object(
        VexadoAPI,
        "buscar_rotas",
        return_value=[
            {
                "id": 1,
                "cidade_origem_id": 4,
                "cidade_destino_id": 83,
                "prefixo": "123-456",
            }
        ],
    ) as mock_buscar_rotas:
        rotas_transbrasil_svc.atualizar_locais_embarque(params)
    mock_buscar_rotas.assert_called_once()
    mock_editar_rota.assert_called_once_with(1, 1, 4, 83, "123-456")
    mock_listar_trechos.assert_called_once()
    mock_atualizar_checkpoint.assert_called()


def test_atualizar_locais_embarque_batch(vexado_api, vexado_company_multimodelo, vexado_login):
    baker.make(
        "rodoviaria.VexadoRota",
        rota_internal_id=555,
        rota_external_id=981,
        company=vexado_company_multimodelo,
    )
    baker.make("rodoviaria.CidadeInternal", id=1, name="João Pessoa")
    params = AtualizarLocaisBatchParams.parse_obj(
        {
            "id_rota_internal": 555,
            "old_rota_id": 555,
            "checkpoints": [
                CadastrarCheckpointParams.parse_obj(
                    {
                        "cidade_destino_id": 1,
                        "duracao": "2:30",
                        "ponto_embarque": "Nova descrição",
                        "km": 1,
                        "local_embarque_id": 1,
                    }
                )
            ],
        }
    )
    with mock.patch.object(VexadoAPI, "atualizar_checkpoint") as mock_atualizar_checkpoint, mock.patch.object(
        VexadoAPI, "listar_trechos", return_value=mocker.MockListarTrechos.response()
    ) as mock_listar_trechos, mock.patch.object(VexadoAPI, "editar_rota") as mock_editar_rota, mock.patch.object(
        VexadoAPI,
        "buscar_rotas",
        return_value=[
            {
                "id": 981,
                "cidade_origem_id": 4,
                "cidade_destino_id": 83,
                "prefixo": "123-456",
            }
        ],
    ) as mock_buscar_rotas:
        rotas_transbrasil_svc.atualizar_locais_embarque_batch(params)
    mock_buscar_rotas.assert_called_once()
    mock_editar_rota.assert_called_once_with(981, 555, 4, 83, "123-456")
    mock_listar_trechos.assert_called_once()
    mock_atualizar_checkpoint.assert_called()


def test_atualizar_locais_embarque_adiciona_local(vexado_api, vexado_company_multimodelo, vexado_login):
    rota_external_id = 1
    rota_internal_id = 5
    baker.make("rodoviaria.CidadeInternal", id=1, name="João Pessoa")
    cidade_internal = baker.make("rodoviaria.CidadeInternal", id=2, name="Salvador")
    cidade_destino_external_id = 3123
    baker.make(
        "rodoviaria.Cidade",
        company=vexado_company_multimodelo,
        cidade_internal_id=cidade_internal.id,
        id_external=cidade_destino_external_id,
    )
    checkpoint = CadastrarCheckpointParams.parse_obj(
        {
            "cidade_destino_id": 2,
            "duracao": "1:30",
            "ponto_embarque": "Ponto nao cadaastrado",
            "km": 50,
            "id_rota_external": rota_external_id,
            "local_embarque_id": 3,
        }
    )
    params = AtualizarLocaisParams.parse_obj(
        {
            "company_id": vexado_company_multimodelo.company_internal_id,
            "id_rota_external": rota_external_id,
            "id_rota_internal": rota_internal_id,
            "checkpoints": [checkpoint],
        }
    )
    with mock.patch.object(VexadoAPI, "atualizar_checkpoint") as mock_atualizar_checkpoint, mock.patch.object(
        VexadoAPI, "listar_trechos", return_value=mocker.MockListarTrechos.response()
    ) as mock_listar_trechos, mock.patch.object(
        VexadoAPI, "cadastrar_checkpoint"
    ) as mock_cadastrar_checkpoint, mock.patch.object(
        VexadoAPI, "mover_posicao_checkpoint_para_baixo"
    ) as mock_mover_posicao_checkpoint_para_baixo, mock.patch(
        "rodoviaria.service.rotas_transbrasil_svc.atualizar_delimitacao_rota"
    ) as mock_atualizar_delimitacao_rota:
        rotas_transbrasil_svc.atualizar_locais_embarque(params)
    checkpoint.cidade_destino_id = str(cidade_destino_external_id)
    mock_cadastrar_checkpoint.assert_called_once_with(checkpoint)
    mock_mover_posicao_checkpoint_para_baixo.assert_called_with(mocker.MockListarTrechos.response()[0]["id"])
    assert mock_mover_posicao_checkpoint_para_baixo.call_count == 3
    assert mock_listar_trechos.call_count == 2
    mock_atualizar_checkpoint.assert_called()
    mock_atualizar_delimitacao_rota.assert_called_once_with(
        vexado_company_multimodelo.company_internal_id,
        rota_internal_id,
        rota_external_id,
    )


def test_get_trecho_id_and_n_shifts():
    cidade_anterior = "recife"
    external_checkpoints = mocker.MockListarTrechos.response()
    n, trecho_id = rotas_transbrasil_svc.get_trecho_id_and_n_shifts(external_checkpoints, cidade_anterior)
    assert n == 1
    assert trecho_id == 19480
    n, trecho_id = rotas_transbrasil_svc.get_trecho_id_and_n_shifts(external_checkpoints, None)
    assert n == len(external_checkpoints) - 2
    assert trecho_id == 19480


def test_get_cidade_id_tem_cidade(vexado_company_multimodelo):
    id_external = 9832
    id_internal = 584
    baker.make(
        "rodoviaria.Cidade",
        cidade_internal_id=id_internal,
        company=vexado_company_multimodelo,
        id_external=id_external,
    )
    cidade_external_id = rotas_transbrasil_svc.get_cidade_id(
        vexado_company_multimodelo.company_internal_id, id_internal, 423
    )
    assert cidade_external_id == str(id_external)


def test_get_cidade_id_cadastra_cidade(vexado_company_multimodelo):
    id_external = 8329
    id_internal = 443
    id_local_embarque = 423
    with mock.patch(
        "rodoviaria.service.rotas_transbrasil_svc.cadastrar_cidade",
        return_value=SimpleNamespace(id_external=str(id_external)),
    ) as mock_cadastrar_cidade:
        cidade_external_id = rotas_transbrasil_svc.get_cidade_id(
            vexado_company_multimodelo.company_internal_id,
            id_internal,
            id_local_embarque,
        )
    assert cidade_external_id == str(id_external)
    mock_cadastrar_cidade.assert_called_once_with(vexado_company_multimodelo, id_internal, id_local_embarque)


def test_cadastrar_rota(vexado_api, vexado_company_multimodelo, vexado_login):
    params = CadastrarRotaHibridoParams.parse_obj(
        {
            "company_id": vexado_company_multimodelo.company_internal_id,
            "cidade_destino_id": 1,
            "id_rota_internal": 1,
            "cidade_origem_id": 2,
            "prefixo": "19.02.2022",
            "checkpoints": [
                CadastrarCheckpointParams.parse_obj(
                    {
                        "local_embarque_id": 1,
                        "cidade_destino_id": 1,
                        "duracao": "01:30",
                        "ponto_embarque": "Posto da Gruta",
                        "km": 123,
                    }
                )
            ],
        }
    )

    retorno_buscar_rotas = [
        {
            "descricao": "08-9433-00: São Bernardo do Campo-SP -> Fortaleza-CE / Ponto Quatro Turismo",
            "cidade_origem": "São Bernardo do Campo",
            "cidade_destino": "Fortaleza",
            "id": 1473,
            "delimitacao": "9432",
        }
    ]

    baker.make(
        "rodoviaria.Cidade",
        cidade_internal_id=1,
        id_external=777,
        company=vexado_company_multimodelo,
    )
    baker.make(
        "rodoviaria.Cidade",
        cidade_internal_id=2,
        id_external=778,
        company=vexado_company_multimodelo,
    )

    with mock.patch.object(VexadoAPI, "cadastrar_rota", return_value=True) as mock_cadastrar_rota, mock.patch.object(
        VexadoAPI, "buscar_rotas", return_value=retorno_buscar_rotas
    ) as mock_buscar_rotas, mock.patch.object(VexadoAPI, "cadastrar_checkpoint") as mock_cadastrar_checkpoint:
        rotas_transbrasil_svc.cadastrar_rota(params)
    mock_cadastrar_rota.assert_called_once()
    mock_buscar_rotas.assert_called_once()
    assert mock_cadastrar_checkpoint.call_count == 2


def test_editar_rota(vexado_api, vexado_company_multimodelo, vexado_login):
    baker.make("rodoviaria.CidadeInternal", id=1, name="São Paulo", uf="SP")
    baker.make("rodoviaria.CidadeInternal", id=2, name="São José dos Campos", uf="SP")
    baker.make("rodoviaria.Cidade", cidade_internal_id=1, company=vexado_company_multimodelo)
    baker.make("rodoviaria.Cidade", cidade_internal_id=2, company=vexado_company_multimodelo)
    grupo = baker.make("rodoviaria.Grupo", linha="Linha 1", grupo_internal_id=888)
    trecho = baker.make("rodoviaria.TrechoClasse", grupo=grupo)
    passagem = baker.make("rodoviaria.Passagem", trechoclasse_integracao=trecho)
    params = EditarRotaHibridoParams.parse_obj(
        {
            "company_id": vexado_company_multimodelo.company_internal_id,
            "id_rota_external": 1,
            "id_rota_internal": 1,
            "cidade_destino_id": 1,
            "cidade_origem_id": 2,
            "prefixo": "novo prefixo",
            "grupo_ids": [888],
        }
    )
    with mock.patch.object(VexadoAPI, "editar_rota") as mock_editar_rota:
        rotas_transbrasil_svc.editar_rota(params)
    mock_editar_rota.assert_called_once()
    grupo.refresh_from_db()
    passagem.refresh_from_db()
    assert grupo.linha == "São José dos Campos - SP à São Paulo - SP"
    assert passagem.prefixo == "novo prefixo"
    assert VexadoRota.objects.count() == 1


def test_verifica_link_rotas():
    company_id, rota_internal_id = 9323, 24242
    company = baker.make("rodoviaria.Company", company_internal_id=company_id)
    baker.make("rodoviaria.VexadoRota", rota_internal_id=rota_internal_id, company=company)
    rotas_internas = VerificaLinkRotasParams.parse_obj(
        {
            "rotas": [
                {"company_id": company_id, "rota_id": rota_internal_id},
                {"company_id": 3829, "rota_id": 48230},
            ]
        }
    ).rotas
    response = rotas_transbrasil_svc.verifica_link_rotas(rotas_internas)
    assert response == {
        str((rota_internal_id, company_id)): "ok",
        "(48230, 3829)": "link nao cadastrado",
    }


def test_cadastrar_ou_editar_rota_create_rota():
    params = CadastrarOuEditarRotaHibridoParams.parse_obj(
        {
            "grupo_ids": [1, 3],
            "checkpoints": [],
            "company_id": 10,
            "cidade_destino_id": 283,
            "cidade_origem_id": 8593,
            "prefixo": "123",
            "id_rota_internal": 8,
            "id_rota_external": None,
        }
    )
    with mock.patch(
        f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.match_or_create_rota"
    ) as mock_match_or_create_rota:
        response = rotas_transbrasil_svc.cadastrar_ou_editar_rota(params)
    mock_match_or_create_rota.assert_called_with(params)
    assert response == mock_match_or_create_rota.return_value


def test_cadastrar_ou_editar_rota_edit_selected_rota():
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=781,
        modelo_venda=rotas_transbrasil_svc.DEFAULT_MODELO_VENDA,
    )
    params = CadastrarOuEditarRotaHibridoParams.parse_obj(
        {
            "grupo_ids": [1, 3],
            "checkpoints": [],
            "company_id": company.company_internal_id,
            "cidade_destino_id": 283,
            "cidade_origem_id": 8593,
            "prefixo": "123",
            "id_rota_internal": 8,
            "id_rota_external": 1283,
        }
    )
    with mock.patch(f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.editar_rota") as mock_editar_rota:
        response = rotas_transbrasil_svc.cadastrar_ou_editar_rota(params)
    mock_editar_rota.assert_called_with(params)
    assert response == mock_editar_rota.return_value
    assert VexadoRota.objects.filter(company=company, rota_external_id=1283, rota_internal_id=8).exists()


def test_cadastrar_ou_editar_rota_edit_rota():
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=781,
        modelo_venda=rotas_transbrasil_svc.DEFAULT_MODELO_VENDA,
    )
    baker.make(
        "rodoviaria.VexadoRota",
        company=company,
        rota_external_id=1283,
        rota_internal_id=8,
    )
    params = CadastrarOuEditarRotaHibridoParams.parse_obj(
        {
            "grupo_ids": [1, 3],
            "checkpoints": [],
            "company_id": company.company_internal_id,
            "cidade_destino_id": 283,
            "cidade_origem_id": 8593,
            "prefixo": "123",
            "id_rota_internal": 8,
            "id_rota_external": None,
        }
    )
    with mock.patch(f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.editar_rota") as mock_editar_rota:
        response = rotas_transbrasil_svc.cadastrar_ou_editar_rota(params)
    params.id_rota_external = 1283
    mock_editar_rota.assert_called_with(params)
    assert response == mock_editar_rota.return_value


def test_match_or_create_rota_match(vexado_company_multimodelo, vexado_login):
    rota_internal_id = 8
    cidades_internas = baker.make(CidadeInternal, _quantity=4)
    cidade_1 = baker.make(
        Cidade,
        company=vexado_company_multimodelo,
        cidade_internal=cidades_internas[0],
        id_external=111,
    )
    cidade_2 = baker.make(
        Cidade,
        company=vexado_company_multimodelo,
        cidade_internal=cidades_internas[1],
        id_external=222,
    )
    cidade_3 = baker.make(
        Cidade,
        company=vexado_company_multimodelo,
        cidade_internal=cidades_internas[2],
        id_external=333,
    )
    cidade_4 = baker.make(
        Cidade,
        company=vexado_company_multimodelo,
        cidade_internal=cidades_internas[3],
        id_external=444,
    )
    params = CadastrarOuEditarRotaHibridoParams.parse_obj(
        {
            "grupo_ids": [1, 3],
            "checkpoints": [
                {
                    "cidade_destino_id": cidade_1.cidade_internal_id,
                    "ponto_embarque": "Posto",
                    "local_embarque_id": 432,
                },
                {
                    "cidade_destino_id": cidade_2.cidade_internal_id,
                    "ponto_embarque": "Shopping",
                    "local_embarque_id": 382,
                },
            ],
            "company_id": vexado_company_multimodelo.company_internal_id,
            "cidade_destino_id": cidade_3.cidade_internal_id,
            "cidade_origem_id": cidade_4.cidade_internal_id,
            "prefixo": "123",
            "id_rota_internal": rota_internal_id,
            "id_rota_external": None,
        }
    )
    with mock.patch(
        f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.editar_rota"
    ) as mock_editar_rota, mock.patch.object(VexadoAPI, "buscar_rotas") as mock_buscar_rotas:
        mock_buscar_rotas.return_value = [
            {
                "id": 8372,
                "cidade_origem_id": cidade_4.id_external,
                "cidade_destino_id": cidade_3.id_external,
                "prefixo": "123",
                "first_checkpoint_id": cidade_1.id_external,
                "last_checkpoint_id": cidade_2.id_external,
                "delimitacao": rota_internal_id,
            }
        ]
        response = rotas_transbrasil_svc.match_or_create_rota(params)
    params.id_rota_external = 8372
    mock_editar_rota.assert_called_with(params)
    assert response == mock_editar_rota.return_value


def test_match_or_create_rota_create(vexado_company_multimodelo, vexado_login):
    rota_internal_id = 94
    cidades_internas = baker.make(CidadeInternal, _quantity=4)
    cidade_1 = baker.make(
        Cidade,
        company=vexado_company_multimodelo,
        cidade_internal=cidades_internas[0],
        id_external=111,
    )
    cidade_2 = baker.make(
        Cidade,
        company=vexado_company_multimodelo,
        cidade_internal=cidades_internas[1],
        id_external=222,
    )
    cidade_3 = baker.make(
        Cidade,
        company=vexado_company_multimodelo,
        cidade_internal=cidades_internas[2],
        id_external=333,
    )
    cidade_4 = baker.make(
        Cidade,
        company=vexado_company_multimodelo,
        cidade_internal=cidades_internas[3],
        id_external=444,
    )
    params = CadastrarOuEditarRotaHibridoParams.parse_obj(
        {
            "grupo_ids": [1, 3],
            "checkpoints": [
                {
                    "cidade_destino_id": cidade_1.cidade_internal_id,
                    "ponto_embarque": "Posto",
                    "local_embarque_id": 432,
                },
                {
                    "cidade_destino_id": cidade_2.cidade_internal_id,
                    "ponto_embarque": "Shopping",
                    "local_embarque_id": 382,
                },
            ],
            "company_id": vexado_company_multimodelo.company_internal_id,
            "cidade_destino_id": cidade_3.cidade_internal_id,
            "cidade_origem_id": cidade_4.cidade_internal_id,
            "prefixo": "123",
            "id_rota_internal": rota_internal_id,
            "id_rota_external": None,
        }
    )
    with mock.patch(
        f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.cadastrar_rota"
    ) as mock_cadastrar_rota, mock.patch.object(VexadoAPI, "buscar_rotas") as mock_buscar_rotas:
        mock_buscar_rotas.return_value = [
            {
                "id": 8372,
                "cidade_origem_id": cidade_4.id_external,
                "cidade_destino_id": cidade_3.id_external,
                "prefixo": "123",
                "first_checkpoint_id": 52315,
                "last_checkpoint_id": 87312,
                "delimitacao": rota_internal_id + 10,
            }
        ]
        response = rotas_transbrasil_svc.match_or_create_rota(params)
    mock_cadastrar_rota.assert_called_with(params)
    assert response == mock_cadastrar_rota.return_value


@pytest.fixture
def mock_sincronizar_rota(vexado_company, vexado_login):
    rota_internal_id = 94
    cidades_internas = baker.make(CidadeInternal, _quantity=4)
    cidade_1 = baker.make(
        Cidade,
        company=vexado_company,
        cidade_internal=cidades_internas[0],
        id_external=111,
    )
    cidade_2 = baker.make(
        Cidade,
        company=vexado_company,
        cidade_internal=cidades_internas[1],
        id_external=222,
    )
    cidade_3 = baker.make(
        Cidade,
        company=vexado_company,
        cidade_internal=cidades_internas[2],
        id_external=333,
    )
    cidade_4 = baker.make(
        Cidade,
        company=vexado_company,
        cidade_internal=cidades_internas[3],
        id_external=444,
    )
    params = CadastrarOuEditarRotaHibridoParams.parse_obj(
        {
            "grupo_ids": [1, 3],
            "checkpoints": [
                {
                    "cidade_destino_id": cidade_1.cidade_internal_id,
                    "ponto_embarque": "Posto",
                    "local_embarque_id": 432,
                    "duracao": "05:10",
                    "tempo_total": "05:10",
                    "km": "300",
                },
                {
                    "cidade_destino_id": cidade_2.cidade_internal_id,
                    "ponto_embarque": "Shopping",
                    "local_embarque_id": 382,
                    "duracao": "02:10",
                    "tempo_total": "02:10",
                    "km": "130",
                },
            ],
            "company_id": vexado_company.company_internal_id,
            "cidade_destino_id": cidade_3.cidade_internal_id,
            "cidade_origem_id": cidade_4.cidade_internal_id,
            "prefixo": "123",
            "id_rota_internal": rota_internal_id,
            "id_rota_external": None,
        }
    )
    return SimpleNamespace(
        params=params,
        cidade_origem=cidade_4,
        cidade_destino=cidade_3,
        checkpoints=[cidade_1, cidade_2],
    )


def test_sincronizar_rota_nao_encontra_pela_delimitacao(mocker, mock_sincronizar_rota, vexado_company):
    mock_buscar_rotas = mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[{"delimitacao": 849231}])
    mock_cadastrar_rota = mocker.patch(f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.cadastrar_rota")
    response = rotas_transbrasil_svc.sincronizar_rota(mock_sincronizar_rota.params)
    mock_buscar_rotas.assert_called_once()
    mock_cadastrar_rota.assert_called_with(mock_sincronizar_rota.params)
    assert response == mock_cadastrar_rota.return_value


def test_sincronizar_rota_retorna_false_empresa_nao_vexado(mock_sincronizar_rota, totalbus_company):
    # dada uma company não existente
    totalbus_company.company_internal_id = 999  # trocando id pra unique pq já existe company vexado com esse id
    totalbus_company.save()
    params = mock_sincronizar_rota.params
    params.company_id = totalbus_company.company_internal_id
    # ao sincronizar rota
    response = rotas_transbrasil_svc.sincronizar_rota(params)
    # nenhuma ação é realizada
    assert response is False

    # dada uma company existente (modelo hibrido)
    totalbus_company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_company.save()
    # se a company nao tiver integracao vexado
    response = rotas_transbrasil_svc.sincronizar_rota(params)
    # nenhuma ação é realizada
    assert response is False


def test_sincronizar_rota_nao_da_match(mocker, mock_sincronizar_rota, vexado_company):
    rota_api = {
        "delimitacao": mock_sincronizar_rota.params.id_rota_internal,
        "id": 1231,
        "cidade_origem_id": 1,
        "cidade_destino_id": 2,
        "prefixo": "2812-02",
    }
    mock_buscar_rotas = mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[rota_api])
    mock_verificar_e_atualizar_rota = mocker.patch(
        f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.verificar_e_atualizar_rota",
        return_value=False,
    )
    mock_inativar_rota = mocker.patch.object(VexadoAPI, "inativar_rota")
    mock_cadastrar_rota = mocker.patch(f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.cadastrar_rota")
    response = rotas_transbrasil_svc.sincronizar_rota(mock_sincronizar_rota.params)
    mock_buscar_rotas.assert_called_once()
    mock_verificar_e_atualizar_rota.assert_called_once_with(mock_sincronizar_rota.params, rota_api)
    mock_inativar_rota.assert_called_once_with(1231, 1, 2, "2812-02")
    mock_cadastrar_rota.assert_called_with(mock_sincronizar_rota.params)
    assert response == mock_cadastrar_rota.return_value


def test_sincronizar_rota_encontra_rota(mocker, mock_sincronizar_rota, vexado_company):
    rota_api = {"delimitacao": mock_sincronizar_rota.params.id_rota_internal}
    mock_buscar_rotas = mocker.patch.object(VexadoAPI, "buscar_rotas", return_value=[rota_api])
    mock_verificar_e_atualizar_rota = mocker.patch(
        f"{rotas_transbrasil_svc.__package__}.rotas_transbrasil_svc.verificar_e_atualizar_rota",
        return_value=True,
    )
    response = rotas_transbrasil_svc.sincronizar_rota(mock_sincronizar_rota.params)
    mock_buscar_rotas.assert_called_once()
    mock_verificar_e_atualizar_rota.assert_called_once_with(mock_sincronizar_rota.params, rota_api)
    assert response == mock_verificar_e_atualizar_rota.return_value


@pytest.fixture
def mocked_rota_api(mock_sincronizar_rota):
    checkpoints_api = []
    cidade_origem = mock_sincronizar_rota.cidade_origem
    cidade_destino = mock_sincronizar_rota.cidade_destino
    checkpoints_buser = mock_sincronizar_rota.params.checkpoints
    cidades_checkpoints = mock_sincronizar_rota.checkpoints
    checkpoints_api += [
        {
            "cidadeDestino": {
                "nome": cidades_checkpoints[0].name,
                "id": cidades_checkpoints[0].id_external,
            },
            "ordem": 2,
            "pontoEmbarque": "*",
            "quilometragem": f"{checkpoints_buser[0].km},00",
            "duracao": checkpoints_buser[0].duracao,
        },
        {
            "cidadeDestino": {
                "nome": cidades_checkpoints[1].name,
                "id": cidades_checkpoints[1].id_external,
            },
            "ordem": 3,
            "pontoEmbarque": checkpoints_buser[0].ponto_embarque,
            "quilometragem": f"{checkpoints_buser[1].km},00",
            "duracao": checkpoints_buser[1].duracao,
        },
    ]
    checkpoints_api.append(
        {
            "cidadeDestino": {
                "nome": cidade_origem.name,
                "id": cidade_origem.id_external,
            },
            "ordem": 1,
            "pontoEmbarque": "*",
            "quilometragem": "00,00",
            "duracao": "00:00",
        }
    )
    checkpoints_api.append(
        {
            "cidadeDestino": {
                "nome": cidade_destino.name,
                "id": cidade_destino.id_external,
            },
            "ordem": 4,
            "pontoEmbarque": checkpoints_buser[1].ponto_embarque,
            "quilometragem": "00,00",
            "duracao": "00:00",
        }
    )
    yield {"id": 4332, "delimitacao": "9422", "itinerario": checkpoints_api}


def test_verificar_e_atualizar_rota_da_match(mock_sincronizar_rota, mocked_rota_api):
    assert rotas_transbrasil_svc.verificar_e_atualizar_rota(mock_sincronizar_rota.params, mocked_rota_api) is True


def test_verificar_e_atualizar_rota_adiciona_local_de_destino_e_da_match(mock_sincronizar_rota, mocked_rota_api):
    rota_completa = mocked_rota_api["itinerario"]
    mocked_rota_api["itinerario"] = mocked_rota_api["itinerario"][:-1]
    with mock.patch.object(VexadoAPI, "cadastrar_checkpoint") as mock_criar_checkpoint, mock.patch.object(
        VexadoAPI, "listar_trechos"
    ) as mock_listar_trechos:
        mock_listar_trechos.return_value = rota_completa
        assert rotas_transbrasil_svc.verificar_e_atualizar_rota(mock_sincronizar_rota.params, mocked_rota_api) is True
    mock_criar_checkpoint.assert_called_once_with(
        CadastrarCheckpointParams(
            cidade_destino_id=rota_completa[-1]["cidadeDestino"]["id"],
            ponto_embarque=mock_sincronizar_rota.params.checkpoints[-1].ponto_embarque,
            id_rota_external=mocked_rota_api["id"],
            local_embarque_id=0,
        )
    )


def test_verificar_e_atualizar_rota_com_tamanhos_diferentes(mock_sincronizar_rota, mocked_rota_api):
    mocked_rota_api["itinerario"] = mocked_rota_api["itinerario"][:-2]
    assert rotas_transbrasil_svc.verificar_e_atualizar_rota(mock_sincronizar_rota.params, mocked_rota_api) is False


def test_verificar_e_atualizar_rota_nao_adiciona_local_de_destino_e_origem_nao_bate(
    mock_sincronizar_rota, mocked_rota_api, vexado_company
):
    cidade_diferente = baker.make(
        Cidade,
        company=vexado_company,
        cidade_internal=baker.make(CidadeInternal),
        id_external=555,
    )
    mock_sincronizar_rota.params.cidade_origem_id = cidade_diferente.cidade_internal_id
    assert rotas_transbrasil_svc.verificar_e_atualizar_rota(mock_sincronizar_rota.params, mocked_rota_api) is False


def test_verificar_e_atualizar_rota_nao_adiciona_local_de_destino_e_destino_nao_bate(
    mock_sincronizar_rota, mocked_rota_api, vexado_company
):
    cidade_diferente = baker.make(
        Cidade,
        company=vexado_company,
        cidade_internal=baker.make(CidadeInternal),
        id_external=555,
    )
    mock_sincronizar_rota.params.cidade_destino_id = cidade_diferente.cidade_internal_id
    assert rotas_transbrasil_svc.verificar_e_atualizar_rota(mock_sincronizar_rota.params, mocked_rota_api) is False


def test_verificar_e_atualizar_rota_nao_adiciona_local_de_destino_e_checkpoints_nao_batem(
    mock_sincronizar_rota, mocked_rota_api, vexado_company
):
    cidade_diferente = baker.make(
        Cidade,
        company=vexado_company,
        cidade_internal=baker.make(CidadeInternal),
        id_external=555,
    )
    mock_sincronizar_rota.params.checkpoints[1].cidade_destino_id = cidade_diferente.cidade_internal_id
    assert rotas_transbrasil_svc.verificar_e_atualizar_rota(mock_sincronizar_rota.params, mocked_rota_api) is False


def test_verificar_e_atualizar_rota_da_match_atualizando_um_trecho(mock_sincronizar_rota, mocked_rota_api):
    mock_sincronizar_rota.params.checkpoints[0].ponto_embarque = "Rodoviaria"
    with mock.patch.object(VexadoAPI, "atualizar_checkpoint") as mock_atualizar_checkpoint:
        assert rotas_transbrasil_svc.verificar_e_atualizar_rota(mock_sincronizar_rota.params, mocked_rota_api) is True
    mock_atualizar_checkpoint.assert_called_once_with(
        mocked_rota_api["itinerario"][1],
        "Rodoviaria",
        rotas_transbrasil_svc._tempo_atualizado(mock_sincronizar_rota.params.checkpoints[1]),
        int(mock_sincronizar_rota.params.checkpoints[1].km or 0),
    )


def test_verificar_e_atualizar_rota_da_match_atualizando_o_trecho_final(mock_sincronizar_rota, mocked_rota_api):
    mock_sincronizar_rota.params.checkpoints[-1].ponto_embarque = "Rodoviaria"
    with mock.patch.object(VexadoAPI, "atualizar_checkpoint") as mock_atualizar_checkpoint:
        assert rotas_transbrasil_svc.verificar_e_atualizar_rota(mock_sincronizar_rota.params, mocked_rota_api) is True
    mock_atualizar_checkpoint.assert_called_once_with(mocked_rota_api["itinerario"][3], "Rodoviaria", "00:00", 0)

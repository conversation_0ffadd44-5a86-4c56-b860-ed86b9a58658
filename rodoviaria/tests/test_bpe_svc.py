from unittest import mock

from model_bakery import baker

from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.models.core import Grupo, Passagem, TrechoClasse
from rodoviaria.service import bpe_svc


def test_get_status_bpe_ja_emitido(totalbus_company):
    passagem = baker.make(
        Passagem,
        bpe_em_contingencia=False,
        bpe_qrcode="bpe.com/qrcode",
        company_integracao=totalbus_company,
        outros_tributos="ICMS 6.83 (12,00%)",
    )
    result = bpe_svc.get_status_bpe(passagem.id)
    assert result == {
        "em_contingencia": False,
        "url": "bpe.com/qrcode",
        "chave": passagem.chave_bpe,
        "numero": passagem.numero_bpe,
        "serie": passagem.serie_bpe,
        "tributos": passagem.outros_tributos,
    }


def test_get_status_bpe_nao_emitido_totalbus(totalbus_company, totalbus_login):
    passagem = baker.make(
        Passagem,
        bpe_em_contingencia=True,
        bpe_qrcode="bpe.com/qrcode",
        company_integracao=totalbus_company,
    )
    result = bpe_svc.get_status_bpe(passagem.id)
    assert result == {
        "em_contingencia": True,
        "url": "bpe.com/qrcode",
        "chave": passagem.chave_bpe,
        "numero": passagem.numero_bpe,
        "serie": passagem.serie_bpe,
        "tributos": passagem.outros_tributos,
    }


def test_get_status_bpe_nao_emitido_vexado(vexado_company, vexado_login):
    passagem = baker.make(
        Passagem,
        bpe_em_contingencia=True,
        bpe_qrcode="bpe.com/qrcode",
        company_integracao=vexado_company,
    )
    passagem_atualizada = baker.make(
        Passagem,
        bpe_em_contingencia=False,
        bpe_qrcode="qrcode",
        company_integracao=vexado_company,
    )
    with mock.patch.object(
        VexadoAPI, "update_bpe_passagem", return_value=passagem_atualizada
    ) as mock_update_bpe_passagem:
        result = bpe_svc.get_status_bpe(passagem.id)
    mock_update_bpe_passagem.assert_called_once_with(passagem)
    assert result == {
        "em_contingencia": False,
        "url": "qrcode",
        "chave": passagem_atualizada.chave_bpe,
        "numero": passagem_atualizada.numero_bpe,
        "serie": passagem_atualizada.serie_bpe,
        "tributos": passagem.outros_tributos,
    }


def test_get_status_bpe_nao_emitido_vexado_passagem_sem_company(vexado_company, vexado_login):
    trecho_classe = baker.make(TrechoClasse, grupo=baker.make(Grupo, company_integracao=vexado_company))
    passagem = baker.make(
        Passagem,
        bpe_em_contingencia=True,
        bpe_qrcode="bpe.com/qrcode",
        trechoclasse_integracao=trecho_classe,
    )
    passagem_atualizada = baker.make(
        Passagem,
        bpe_em_contingencia=False,
        bpe_qrcode="qrcode",
        trechoclasse_integracao=trecho_classe,
    )
    with mock.patch.object(
        VexadoAPI, "update_bpe_passagem", return_value=passagem_atualizada
    ) as mock_update_bpe_passagem:
        result = bpe_svc.get_status_bpe(passagem.id)
    mock_update_bpe_passagem.assert_called_once_with(passagem)
    assert result == {
        "em_contingencia": False,
        "url": "qrcode",
        "chave": passagem_atualizada.chave_bpe,
        "numero": passagem_atualizada.numero_bpe,
        "serie": passagem_atualizada.serie_bpe,
        "tributos": passagem.outros_tributos,
    }


def test_dados_bpe_passagem_nao_contingencia(passagem_ze):
    travel_id = passagem_ze.travel_internal_id
    passagem_ze.bpe_em_contingencia = False
    passagem_ze.save()
    dados_passagem = bpe_svc.dados_bpe_passagem(travel_id)
    assert dados_passagem["passagens"][0]["bpe_em_contingencia"] is False
    assert dados_passagem["passagens"][0]["passagem_id"] == passagem_ze.id


def test_dados_bpe_passagem_em_contingencia(passagem_ze):
    travel_id = passagem_ze.travel_internal_id
    passagem_ze.bpe_em_contingencia = True
    passagem_ze.save()
    dados_passagem = bpe_svc.dados_bpe_passagem(travel_id)
    assert dados_passagem["passagens"][0]["bpe_em_contingencia"] is True
    assert dados_passagem["passagens"][0]["passagem_id"] == passagem_ze.id

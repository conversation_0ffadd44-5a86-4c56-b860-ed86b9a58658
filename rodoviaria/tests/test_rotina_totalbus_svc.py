from datetime import datetime, timed<PERSON>ta
from unittest import mock

from commons.dateutils import to_default_tz
from rodoviaria.models import Rotina
from rodoviaria.service.rotina_totalbus_svc import RotinaTotalbus
from rodoviaria.tests.totalbus import mocker


def test_fetch_rotina(
    totalbus_api,
    mock_buscar_todos_servicos_totalbus_response,
    rota_totalbus,
    totalbus_mock_buscar_itinerario,
):
    next_days = 7
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        first_day = to_default_tz(datetime.now() - timedelta(days=3))
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotina = RotinaTotalbus(totalbus_api).fetch_rotina(rota=rota_totalbus, next_days=next_days, first_day=first_day)
        data_ida = mocker.BuscarItinerarioCorrida.response()["lsParadas"][0]["data"]
        hora_ida = mocker.BuscarItinerarioCorrida.response()["lsParadas"][0]["hora"]
        datetime_ida_str = f"{data_ida} {hora_ida}"
        rotina_dt = rotina.pop()
        assert datetime_ida_str == rotina_dt.strftime("%Y-%m-%d %H:%M")
        assert len(rotina) == 0


def test_fetch_rotina_inativa_no_db(
    totalbus_api,
    mock_buscar_todos_servicos_totalbus_response,
    rota_totalbus,
    totalbus_mock_buscar_itinerario,
):
    next_days = 7
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        first_day = to_default_tz(datetime.now() - timedelta(days=3))
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotina_datetime_set = RotinaTotalbus(totalbus_api).fetch_rotina(
            rota=rota_totalbus, next_days=next_days, first_day=first_day
        )
        rotina_datetime_ida = rotina_datetime_set.pop()
        rotina = Rotina.objects.get(rota=rota_totalbus, datetime_ida=rotina_datetime_ida)
        rotina.ativo = False
        rotina.save()

        # Se a rotina estiver inativa, reativar se o fetch_rotina encontrar uma identica no BD
        RotinaTotalbus(totalbus_api).fetch_rotina(rota=rota_totalbus, next_days=next_days, first_day=first_day)
        rotina.refresh_from_db()
        assert rotina.ativo is True


def test_fetch_servico_nao_localizado(totalbus_api, rota_totalbus):
    next_days = 7
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = []
        rotina = RotinaTotalbus(totalbus_api).fetch_rotina(
            rota=rota_totalbus,
            next_days=next_days,
            first_day=to_default_tz(datetime.now() + timedelta(days=2)),
        )
        assert len(rotina) == 0


def test_fetch_rotina_nao_localizada(
    totalbus_api,
    rota_totalbus,
    mock_buscar_todos_servicos_totalbus_response,
    totalbus_mock_buscar_itinerario_nao_localizado,
):
    next_days = 7
    with mock.patch("rodoviaria.api.totalbus.api.TotalbusAPI.buscar_todos_servicos") as mock_buscar_todos_servicos:
        mock_buscar_todos_servicos.return_value = mock_buscar_todos_servicos_totalbus_response.cleaned
        rotina = RotinaTotalbus(totalbus_api).fetch_rotina(rota=rota_totalbus, next_days=next_days)
        assert len(rotina) == 0

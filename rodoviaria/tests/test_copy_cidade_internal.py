from datetime import datetime

import time_machine
from django.core.management import call_command
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_tz
from core.models_commons import Cidade as CoreCidade
from rodoviaria.models.core import CidadeInternal


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_copy_cidade_internal_create():
    cidade_buser = baker.make(
        CoreCidade,
        id=12345,
        name="Cidade ABC",
        uf="UF",
        sigla="ABC",
        timezone="America/Rio_Branco",
        city_code_ibge=8882812,
        uf_code_ibge=54,
        updated_at=to_tz(datetime(2022, 10, 13, 23, 20, 54), "America/Rio_Branco"),
    )
    assert not CidadeInternal.objects.filter(pk=12345).exists()

    call_command("copy_cidade_internal")
    cidade_internal = CidadeInternal.objects.get(pk=12345)

    assert cidade_internal.id == cidade_buser.id
    assert cidade_internal.name == cidade_buser.name
    assert cidade_internal.uf == cidade_buser.uf
    assert cidade_internal.sigla == cidade_buser.sigla
    assert cidade_internal.timezone == cidade_buser.timezone
    assert cidade_internal.city_code_ibge == cidade_buser.city_code_ibge
    assert cidade_internal.uf_code_ibge == cidade_buser.uf_code_ibge
    assert cidade_internal.updated_at == timezone.now()


@time_machine.travel(datetime(2022, 10, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_copy_cidade_internal_update():
    baker.make(
        CidadeInternal,
        id=12345,
        name="Cidade ABC",
        uf="UF",
        sigla="ABC",
        timezone="America/Sao_Paulo",
        city_code_ibge=842313,
        uf_code_ibge=54,
        updated_at=to_tz(datetime(2022, 10, 10, 12, 43, 27), "America/Sao_Paulo"),
    )
    cidade_buser = baker.make(
        CoreCidade,
        id=12345,
        name="Cidade ABC",
        uf="UF",
        sigla="ABC",
        timezone="America/Rio_Branco",
        city_code_ibge=8882812,
        uf_code_ibge=54,
        updated_at=to_tz(datetime(2022, 10, 13, 23, 20, 54), "America/Rio_Branco"),
    )

    call_command("copy_cidade_internal")

    updated_cidade_internal = CidadeInternal.objects.get(pk=12345)
    assert updated_cidade_internal.id == cidade_buser.id
    assert updated_cidade_internal.name == cidade_buser.name
    assert updated_cidade_internal.uf == cidade_buser.uf
    assert updated_cidade_internal.sigla == cidade_buser.sigla
    assert updated_cidade_internal.timezone == cidade_buser.timezone
    assert updated_cidade_internal.city_code_ibge == cidade_buser.city_code_ibge
    assert updated_cidade_internal.uf_code_ibge == cidade_buser.uf_code_ibge
    assert updated_cidade_internal.updated_at == timezone.now()

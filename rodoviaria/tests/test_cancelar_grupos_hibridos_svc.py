from unittest import mock

from celery import group
from model_bakery import baker

from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.models.core import Company
from rodoviaria.service import cancelar_grupos_hibridos_svc

DEFAULT_MODELO_VENDA = Company.ModeloVenda.HIBRIDO


def refresh_items(items):
    for item in items:
        item.refresh_from_db()


def test_cancelar_grupos_classe(vexado_company_multimodelo, vexado_login):
    grupos_classe_ids = [1111, 2222]
    grupos_classe_external_ids = [9999, 8888]
    company_id = vexado_company_multimodelo.company_internal_id
    grupo = baker.make("rodoviaria.Grupo", company_integracao=vexado_company_multimodelo)
    grupo_classe_1 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=grupos_classe_ids[0],
        grupo=grupo,
    )
    grupo_classe_2 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=grupos_classe_ids[1],
        grupo=grupo,
    )
    trecho_classe_1 = baker.make(
        "rodoviaria.TrechoClasse",
        grupo_classe=grupo_classe_1,
        grupo=grupo,
        external_id=grupos_classe_external_ids[0],
        active=True,
    )
    trecho_classe_2 = baker.make(
        "rodoviaria.TrechoClasse",
        grupo_classe=grupo_classe_2,
        grupo=grupo,
        external_id=grupos_classe_external_ids[1],
        active=True,
    )
    vexado_grupo_classe_1 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_1,
        grupo_classe_external_id=9999,
        status="created",
    )
    vexado_grupo_classe_2 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_2,
        grupo_classe_external_id=8888,
        status="created",
    )
    passagem_1 = baker.make(
        "rodoviaria.Passagem",
        trechoclasse_integracao=trecho_classe_1,
        status="confirmada",
    )
    passagem_2 = baker.make(
        "rodoviaria.Passagem",
        trechoclasse_integracao=trecho_classe_2,
        status="confirmada",
    )
    with mock.patch(
        "rodoviaria.service.cancela_passagens_pendentes_svc.cancelar_passagens"
    ) as mock_cancela_passagens, mock.patch.object(VexadoAPI, "inativar_grupo_classe") as mock_inativar_grupo_classe:
        (
            grupos_classe_cancelados,
            trechos_classe_ids,
        ) = cancelar_grupos_hibridos_svc.cancelar_grupos_classe(company_id, grupos_classe_ids)
    mock_cancela_passagens.assert_called_once_with([passagem_1, passagem_2])
    assert sorted([call_arg[0][0] for call_arg in mock_inativar_grupo_classe.call_args_list]) == sorted(
        [
            vexado_grupo_classe_1.grupo_classe_external_id,
            vexado_grupo_classe_2.grupo_classe_external_id,
        ]
    )
    refresh_items([vexado_grupo_classe_1, vexado_grupo_classe_2, trecho_classe_1, trecho_classe_2])
    assert trecho_classe_1.active is False
    assert trecho_classe_2.active is False
    assert vexado_grupo_classe_1.status == "canceled"
    assert vexado_grupo_classe_2.status == "canceled"
    assert sorted(trechos_classe_ids) == [trecho_classe_1.id, trecho_classe_2.id]
    assert sorted(grupos_classe_cancelados, key=lambda k: k.id) == [
        vexado_grupo_classe_1,
        vexado_grupo_classe_2,
    ]


def test_cancelar_grupos_classe_do_not_call_task():
    grupos_classe_ids = [1111, 2222]
    grupos_classe_external_ids = [9999, 8888]
    company_id = 55
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    grupo = baker.make("rodoviaria.Grupo", company_integracao=company)
    grupo_classe_1 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=grupos_classe_ids[0],
        grupo=grupo,
    )
    grupo_classe_2 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=grupos_classe_ids[1],
        grupo=grupo,
    )
    trecho_classe_1 = baker.make(
        "rodoviaria.TrechoClasse",
        grupo_classe=grupo_classe_1,
        grupo=grupo,
        external_id=grupos_classe_external_ids[0],
        active=True,
    )
    trecho_classe_2 = baker.make(
        "rodoviaria.TrechoClasse",
        grupo_classe=grupo_classe_2,
        grupo=grupo,
        external_id=grupos_classe_external_ids[1],
        active=True,
    )
    vexado_grupo_classe_1 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_1,
        grupo_classe_external_id=9999,
        status="created",
    )
    vexado_grupo_classe_2 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_2,
        grupo_classe_external_id=8888,
        status="created",
    )
    baker.make(
        "rodoviaria.Passagem",
        trechoclasse_integracao=trecho_classe_1,
        status="confirmada",
    )
    baker.make(
        "rodoviaria.Passagem",
        trechoclasse_integracao=trecho_classe_2,
        status="confirmada",
    )
    (
        grupos_classe_cancelados,
        trechos_classe_rodoviaria_ids,
    ) = cancelar_grupos_hibridos_svc.cancelar_grupos_classe(company_id, grupos_classe_ids, call_task_cancelar=False)
    refresh_items([vexado_grupo_classe_1, vexado_grupo_classe_2, trecho_classe_1, trecho_classe_2])
    assert vexado_grupo_classe_1.status == "canceled"
    assert vexado_grupo_classe_2.status == "canceled"
    assert trecho_classe_1.active
    assert trecho_classe_2.active
    assert sorted(grupos_classe_cancelados, key=lambda k: k.id) == [
        vexado_grupo_classe_1,
        vexado_grupo_classe_2,
    ]
    assert sorted(trechos_classe_rodoviaria_ids) == sorted([trecho_classe_1.id, trecho_classe_2.id])


def test_cancelar_grupos_classe_ja_cancelado():
    grupos_classe_id = 3333
    company_id = 55
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    grupo = baker.make("rodoviaria.Grupo", company_integracao=company)
    grupo_classe_1 = baker.make("rodoviaria.GrupoClasse", grupoclasse_internal_id=grupos_classe_id, grupo=grupo)
    vexado_grupo_classe_1 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_1,
        grupo_classe_external_id=9999,
        status="canceled_double_check",
    )
    with mock.patch("rodoviaria.service.cancela_passagens_pendentes_svc.cancelar_passagens") as mock_cancelar_passagens:
        (
            grupos_classe_cancelados,
            trechos_classe_ids,
        ) = cancelar_grupos_hibridos_svc.cancelar_grupos_classe(company_id, [grupos_classe_id])
    mock_cancelar_passagens.assert_called_once()
    vexado_grupo_classe_1.refresh_from_db()
    assert vexado_grupo_classe_1.status == "canceled_double_check"
    assert grupos_classe_cancelados == [vexado_grupo_classe_1]
    assert trechos_classe_ids == []


def test_cancelar_grupos_classe_sem_grupo_classe():
    response = cancelar_grupos_hibridos_svc.cancelar_grupos_classe(5, [3949, 9430])
    assert response == (None, None)


def test_cancela_passagens_e_inativar_trechos_task():
    passagem = baker.make("rodoviaria.Passagem")
    trecho_classe = baker.make("rodoviaria.TrechoClasse", active=True)
    with mock.patch("rodoviaria.service.cancela_passagens_pendentes_svc.cancelar_passagens") as mock_cancelar_passagens:
        cancelar_grupos_hibridos_svc.cancela_passagens_e_inativar_trechos_task([passagem.id], [trecho_classe.id])
    mock_cancelar_passagens.assert_called_once_with([passagem])
    trecho_classe.refresh_from_db()
    assert trecho_classe.active is False


def test_dispara_tasks_nenhuma():
    cancelar_grupos_hibridos_svc._dispara_tasks(
        call_task_cancelar=False, cancelar_passagens_task="", inativar_grupos_tasks=[]
    )


def test_dispara_tasks_cancelar():
    passagem = baker.make("rodoviaria.Passagem")
    cancelar_task = cancelar_grupos_hibridos_svc.cancela_passagens_e_inativar_trechos_task.s([passagem.id], [])
    with mock.patch("rodoviaria.service.cancela_passagens_pendentes_svc.cancelar_passagens") as mock_cancelar_passagens:
        cancelar_grupos_hibridos_svc._dispara_tasks(
            call_task_cancelar=True,
            cancelar_passagens_task=cancelar_task,
            inativar_grupos_tasks=[],
        )
    mock_cancelar_passagens.assert_called_once_with([passagem])


def test_dispara_tasks_cancelar_e_trocar_onibus():
    inativar_grupos_tasks = mock.MagicMock()
    cancelar_task = mock.MagicMock()
    with mock.patch("rodoviaria.service.cancelar_grupos_hibridos_svc.chain") as mock_celery_chain:
        cancelar_grupos_hibridos_svc._dispara_tasks(
            call_task_cancelar=True,
            cancelar_passagens_task=cancelar_task,
            inativar_grupos_tasks=inativar_grupos_tasks,
        )
    mock_celery_chain.assert_called_once()
    mock_celery_chain.return_value.apply_async.assert_called_once()


def test_fechar_grupos_classe():
    grupos_classe_ids = [3333, 4444]
    company_id = 35
    company = baker.make(
        "rodoviaria.Company",
        company_internal_id=company_id,
        modelo_venda=DEFAULT_MODELO_VENDA,
    )
    grupo = baker.make("rodoviaria.Grupo", company_integracao=company)
    grupo_classe_1 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=grupos_classe_ids[0],
        grupo=grupo,
    )
    grupo_classe_2 = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=grupos_classe_ids[1],
        grupo=grupo,
    )
    vexado_grupo_classe_1 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_1,
        grupo_classe_external_id=9999,
        status="created",
    )
    vexado_grupo_classe_2 = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=grupo_classe_2,
        grupo_classe_external_id=8888,
        status="created",
    )
    response = cancelar_grupos_hibridos_svc.fechar_grupos_classe(company_id, grupos_classe_ids)
    refresh_items([vexado_grupo_classe_1, vexado_grupo_classe_2])
    assert vexado_grupo_classe_1.status == "closed"
    assert vexado_grupo_classe_2.status == "closed"
    assert response == {"grupos_fechados": 2}


def test_fechar_grupos_classe_sem_grupos_classe():
    grupos_classe_ids = [3333, 4444]
    company_id = 35
    response = cancelar_grupos_hibridos_svc.fechar_grupos_classe(company_id, grupos_classe_ids)
    assert response == {"grupos_fechados": 0}


def test_inativar_grupos_classe_group_tasks():
    company_id = 328
    grupo_classe_external_id = 4932
    grupos_classe_external_ids = [grupo_classe_external_id]
    group_task = cancelar_grupos_hibridos_svc.inativar_grupos_classe_group_tasks(company_id, grupos_classe_external_ids)
    assert group_task == group(
        cancelar_grupos_hibridos_svc.inativar_grupo_classe_task.si(company_id, grupo_classe_external_id)
    )


def test_inativar_grupo_classe_task(vexado_company_multimodelo, vexado_login):
    with mock.patch.object(VexadoAPI, "inativar_grupo_classe") as mock_inativar_grupo_classe:
        cancelar_grupos_hibridos_svc.inativar_grupo_classe_task(vexado_company_multimodelo.company_internal_id, 7382)
    mock_inativar_grupo_classe.assert_called_once_with(7382)


def test_inativar_grupo_classe_task_sem_grupo_classe_external_id(
    vexado_company_multimodelo,
):
    with mock.patch.object(VexadoAPI, "inativar_grupo_classe") as mock_inativar_grupo_classe:
        cancelar_grupos_hibridos_svc.inativar_grupo_classe_task(vexado_company_multimodelo.company_internal_id, None)
    mock_inativar_grupo_classe.assert_not_called()

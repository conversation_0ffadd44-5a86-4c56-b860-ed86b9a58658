from model_bakery import baker

from rodoviaria.models import Integracao
from rodoviaria.service.sistema_integracao_empresa_svc import get_integracoes_empresa


def test_get_integracoes_empresa():
    _integracao = baker.make(
        Integracao,
        name="totalbus",
    )

    integracoes = get_integracoes_empresa()
    assert any(integracao["name"] == "totalbus" and integracao["id"] == _integracao.id for integracao in integracoes)

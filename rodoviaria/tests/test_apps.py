import logging

from constance import config

from commons.utils import RegexEqual


def test_constance_update_generates_a_log_message(caplog):
    with caplog.at_level(logging.INFO, logger="rodoviaria.apps"):
        config.BUCKET_SIZE_PRAXIO = 42

        assert caplog.record_tuples == [
            (
                "rodoviaria.apps",
                logging.INFO,
                RegexEqual(r"constance_config_changed: key=BUCKET_SIZE_PRAXIO old_value=(None|[0-9]+) new_value=42"),
            )
        ]

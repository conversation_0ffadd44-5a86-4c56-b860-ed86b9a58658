from datetime import timedelta
from unittest import mock

from django.db import connections
from django.utils import timezone
from model_bakery import baker

from commons.dateutils import today_midnight
from rodoviaria.models.core import Rota, Rotina
from rodoviaria.service.inativa_rota_rotinas_svc import _inativar_rotas, _inativar_rotinas


def test_inativar_rotinas(django_assert_num_queries):
    # setup
    timezone_now = timezone.now()
    datetime_ida = timezone_now + timedelta(days=1)
    rota = baker.make(Rota)
    with mock.patch("django.utils.timezone.now", return_value=today_midnight() - timedelta(days=10)):
        rotina_para_inativar = baker.make(Rotina, ativo=True, datetime_ida=datetime_ida, rota=rota)
    with mock.patch("django.utils.timezone.now", return_value=today_midnight() - timedelta(days=9)):
        rotina_ativa = baker.make(Rot<PERSON>, ativo=True, datetime_ida=datetime_ida + timedelta(minutes=1), rota=rota)

    # call
    with django_assert_num_queries(2, connection=connections["rodoviaria"]), mock.patch(
        "django.utils.timezone.now", return_value=timezone_now
    ):
        _inativar_rotinas()

    # assert
    rotina_para_inativar.refresh_from_db()
    assert not rotina_para_inativar.ativo
    assert rotina_para_inativar.updated_at == timezone_now

    rotina_ativa.refresh_from_db()
    assert rotina_ativa.ativo
    assert rotina_ativa.updated_at == today_midnight() - timedelta(days=9)


def test_inativar_rotas(django_assert_num_queries):
    # setup
    timezone_now = timezone.now()
    datetime_ida = timezone_now + timedelta(days=1)
    rota_para_inativar = baker.make(Rota, ativo=True)
    baker.make(Rotina, ativo=False, datetime_ida=datetime_ida, rota=rota_para_inativar)
    rota_para_inativar_sem_rotinas = baker.make(Rota, ativo=True)

    with mock.patch("django.utils.timezone.now", return_value=timezone_now - timedelta(minutes=1)):
        rota_ativa = baker.make(Rota, ativo=True)
    baker.make(Rotina, ativo=False, datetime_ida=datetime_ida + timedelta(minutes=1), rota=rota_ativa)
    baker.make(Rotina, ativo=True, datetime_ida=datetime_ida + timedelta(minutes=2), rota=rota_ativa)

    # call
    with django_assert_num_queries(3, connection=connections["rodoviaria"]), mock.patch(
        "django.utils.timezone.now", return_value=timezone_now
    ):
        _inativar_rotas()

    # assert
    rota_para_inativar.refresh_from_db()
    assert not rota_para_inativar.ativo
    assert rota_para_inativar.updated_at == timezone_now

    rota_para_inativar_sem_rotinas.refresh_from_db()
    assert not rota_para_inativar_sem_rotinas.ativo
    assert rota_para_inativar_sem_rotinas.updated_at == timezone_now

    rota_ativa.refresh_from_db()
    assert rota_ativa.ativo
    assert rota_ativa.updated_at == timezone_now - timedelta(minutes=1)

import pytest
from model_bakery import baker

from rodoviaria.forms.staff_forms import RodoviariaListarTiposAssentosParams
from rodoviaria.models.core import Company, TipoAssento
from rodoviaria.service import class_match_svc


# fmt: off
def test_does_class_match_regras_excecao(totalbus_company):
    company_adamantina = baker.make(Company, company_internal_id=282, modelo_venda="marketplace")
    company_levare = baker.make(Company, company_internal_id=44, modelo_venda="marketplace")
    company_luxor = baker.make(Company, company_internal_id=13, modelo_venda="marketplace")
    company_primar = baker.make(Company, company_internal_id=70, modelo_venda="marketplace")
    company_guerino = baker.make(Company, company_internal_id=445, modelo_venda="marketplace")
    company_soares = baker.make(Company, company_internal_id=134, modelo_venda="marketplace")
    company_catedral = baker.make(Company, company_internal_id=6247, modelo_venda="marketplace")
    company_itapemirim = baker.make(Company, company_internal_id=1190, modelo_venda="marketplace")

    assert class_match_svc.does_class_match(company_adamantina, "semi leito", "convencional")
    assert class_match_svc.does_class_match(company_levare, "cama premium", "LEITO CAMA")
    assert class_match_svc.does_class_match(company_levare, "cama premium", "LEITO-CAMA MISTO")
    assert class_match_svc.does_class_match(company_levare, "cama premium", "CONVENCIONAL C/AR")
    assert class_match_svc.does_class_match(company_levare, "cama premium individual", "CAMA INDIVIDUAL SUP.")
    assert class_match_svc.does_class_match(company_levare, "cama premium individual", "CAMA INDIVIDUAL INF.")
    assert class_match_svc.does_class_match(company_luxor, "cama premium", "LEITO CAMA")
    assert class_match_svc.does_class_match(company_luxor, "cama premium", "LEITO-CAMA MISTO")
    assert class_match_svc.does_class_match(company_luxor, "cama premium", "CONVENCIONAL C/AR")
    assert class_match_svc.does_class_match(company_luxor, "cama premium individual", "CAMA INDIVIDUAL SUP.")
    assert class_match_svc.does_class_match(company_luxor, "cama premium individual", "CAMA INDIVIDUAL INF.")
    assert class_match_svc.does_class_match(company_primar, "semi leito", "CONVENCIONAL")
    assert class_match_svc.does_class_match(company_primar, "executivo", "CONVENCIONAL")
    assert class_match_svc.does_class_match(company_guerino, "executivo", "EXECUTIVO")
    assert class_match_svc.does_class_match(company_guerino, "executivo", "CONVENCIONAL") is False
    assert class_match_svc.does_class_match(company_guerino, "convencional", "CONVENCIONAL")
    assert class_match_svc.does_class_match(company_guerino, "convencional", "EXECUTIVO") is False
    assert class_match_svc.does_class_match(company_soares, "cama premium", "LEITO_CAMA")
    assert class_match_svc.does_class_match(company_catedral,  "semi leito", "DD - Double Decker Bus")
    assert class_match_svc.does_class_match(company_itapemirim,  "semi leito", "executivo")

    # regra de excecao generica
    assert class_match_svc.does_class_match(totalbus_company, "executivo", "convencional")
# fmt: on


@pytest.mark.parametrize(
    "tipo_assento_buser, tipo_assento_parceiro, assert_boolean",
    [
        ("semi leito", "semi-leito", True),
        ("semi leito", "semileito", True),
        ("semi leito", "semi leito", True),
        ("semi leito", "", True),
        ("semi leito", "SEMI LEITO", True),
        ("leito individual", "leito individual", True),
        ("leito individual", "leito_individual", True),
        ("leito individual", "leito_especial", True),
        ("leito", "leito duplo", True),
        ("leito", "leito", True),
        ("leito", "LEITO TOTAL", True),
        ("leito", "LEITO TOTAL.", True),
        ("leito", "LEITO TOTAL - CONJUGADO", True),
        ("leito", "LEITO TOTAL - NON-STOP", True),
        ("leito", "leito com ar condicionado", True),
        ("cama premium", "cama_premium", True),
        ("cama premium", "cama premium", True),
        ("cama premium", "LEITO_CAMA_ESPECIAL_1", True),
        ("cama premium individual", "LEITO_CAMA_ESPECIAL_2", True),
        ("leito cama", "leito_cama", True),
        ("leito cama", "leito_especial", True),
        ("leito cama", "leito cama", True),
        ("leito cama", "leito-cama", True),
        ("leito cama", "DD - LEITO-CAMA G8", True),
        ("cama premium", "", True),
        ("convencional", "", True),
        ("convencional", "convencional", True),
        ("executivo", "executivo", True),
        ("leito cama", "CAMA", True),
        ("leito cama individual", "leito_cama_individual", True),
        ("leito cama individual", "leito-cama individual", True),
        ("leito cama individual", "leito cama individual", True),
        ("leito individual", "LEITO - INDIVIDUAL", True),
        ("executivo", "DD - DOUBLE-DECKER", True),
        ("leito individual", "SPACE INDIVIDUAL SEMI-LEITO", True),
        ("leito", "SPACE SEMI-LEITO", True),
        ("cama premium", "cama diamante", False),
        ("leito", "DD - DOUBLE-DECKER", False),
        ("leito", "DD - Double Decker Bus", False),
    ],
)
def test_does_class_match(totalbus_company, tipo_assento_buser, tipo_assento_parceiro, assert_boolean):
    assert (
        class_match_svc.does_class_match(totalbus_company, tipo_assento_buser, tipo_assento_parceiro) is assert_boolean
    )


@pytest.mark.parametrize(
    "tipo_assento_buser, tipo_assento_parceiro",
    [
        ("semi leito", "semi-leito"),
        ("semi leito", "semileito"),
        ("semi leito", "semi leito"),
        ("leito individual", "leito individual"),
        ("leito", "leito duplo"),
        ("leito", "leito"),
        ("leito cama", "CAMA"),
        ("leito cama", "leito cama"),
        ("leito cama", "leito_cama"),
        ("leito cama", "leito-cama"),
        ("cama premium", "LEITO_CAMA_ESPECIAL_1"),
        ("cama premium individual", "LEITO_CAMA_ESPECIAL_2"),
        ("leito cama", "DD - LEITO-CAMA G8"),
        ("cama premium", "cama premium"),
        ("cama premium", "cama_premium"),
        ("", ""),
        ("executivo", "executivo"),
        (class_match_svc.CLASSE_NAO_MAPEADA, "cama diamante"),
        ("convencional", "convencional"),
        ("leito individual", "LEITO - INDIVIDUAL"),
        ("executivo", "Conv - PLUS"),
        ("executivo", "Conv. PLUS"),
        ("executivo", "Conv. - PLUS"),
        ("executivo", "Conv. - PLUS"),
        ("executivo", "CONV. PLUS"),
        ("executivo", "Conv. DOUBLE-DECKER BUS"),
        ("executivo", "CONV -PLUS"),
        ("executivo", "DD - Double Decker Bus"),
        ("executivo", "DD - DOUBLE-DECKER"),
        ("executivo", "DD- DOUBLE DECK"),
        ("leito", "DOUBLE DECK - LEITO"),
        ("leito", "DD - LEITO"),
        ("leito", "LEITO TOTAL"),
        ("leito", "LEITO TOTAL."),
        ("leito", "LEITO TOTAL - CONJUGADO"),
        ("leito", "LEITO TOTAL - NON-STOP"),
        ("leito", "leito com ar condicionado"),
        ("leito individual", "space individual semi-leito"),
        ("leito", "space semi-leito"),
    ],
)
def test_get_buser_class_by_company(totalbus_company, tipo_assento_buser, tipo_assento_parceiro):
    assert class_match_svc.get_buser_class_by_company(totalbus_company, tipo_assento_parceiro) == tipo_assento_buser


@pytest.fixture
def tipos_assentos(totalbus_company):
    return [
        baker.make(
            TipoAssento,
            company=totalbus_company,
            tipo_assento_parceiro="DOUBLE DECK - LEITO",
            tipo_assento_buser_preferencial="leito",
            tipos_assentos_buser_match=["leito", "leito individual"],
        ),
        baker.make(
            TipoAssento,
            company=totalbus_company,
            tipo_assento_parceiro="DD - LEITO",
            tipo_assento_buser_preferencial="leito",
            tipos_assentos_buser_match=["leito", "leito individual"],
        ),
        baker.make(
            TipoAssento,
            company=totalbus_company,
            tipo_assento_parceiro="DD - Oxitona",
            tipo_assento_buser_preferencial="cama premium",
            tipos_assentos_buser_match=["leito"],
        ),
        baker.make(
            TipoAssento,
            company=totalbus_company,
            tipo_assento_parceiro="Conv - PLUS",
            tipo_assento_buser_preferencial="executivo",
            tipos_assentos_buser_match=["executivo", "convencional"],
        ),
        baker.make(
            TipoAssento,
            company=totalbus_company,
            tipo_assento_parceiro="LEITO_CAMA_ESPECIAL_1",
            tipo_assento_buser_preferencial="cama premium",
            tipos_assentos_buser_match=["leito", "leito individual"],
        ),
        baker.make(
            TipoAssento,
            company=totalbus_company,
            tipo_assento_parceiro="LEITO_CAMA_ESPECIAL_",
            tipo_assento_buser_preferencial=None,
            tipos_assentos_buser_match=None,
        ),
    ]


def test_listar_tipos_assentos(tipos_assentos):
    params = RodoviariaListarTiposAssentosParams()
    result, _, _ = class_match_svc.listar_tipos_assentos(params)
    assert len(result) == 6


def test_listar_tipos_assentos_search_company(tipos_assentos):
    company = baker.make(
        "rodoviaria.Company",
        name="Fama, riqueza, poder. Um homem conquistou tudo que o mundo tinha a oferecer",
        features=[Company.Feature.ACTIVE],
    )
    baker.make(TipoAssento, company=company, tipo_assento_parceiro="O rei dos piratas, Gold Roger")
    params = RodoviariaListarTiposAssentosParams()
    params.search_company = "OfeReCER"
    result, _, _ = class_match_svc.listar_tipos_assentos(params)
    assert len(result) == 1


def test_listar_tipos_assentos_search_tipo_assento(tipos_assentos):
    params = RodoviariaListarTiposAssentosParams()
    params.search_tipo_assento = "Conv"
    result, _, _ = class_match_svc.listar_tipos_assentos(params)
    assert len(result) == 1


def test_listar_tipos_assentos_search_tipo_assento_deve_procurar_nas_tres_colunas_de_classe(tipos_assentos):
    params = RodoviariaListarTiposAssentosParams()
    params.search_tipo_assento = "leito"
    result, _, _ = class_match_svc.listar_tipos_assentos(params)
    assert len(result) == 5


def test_listar_tipos_assentos_search_not_linked_only(tipos_assentos):
    params = RodoviariaListarTiposAssentosParams()
    params.search_not_linked_only = True
    result, _, _ = class_match_svc.listar_tipos_assentos(params)
    assert len(result) == 1


def test_listar_tipos_assentos_search_active_companies_only(tipos_assentos):
    tipos_assentos[0].company.features = []
    tipos_assentos[0].company.save()
    params = RodoviariaListarTiposAssentosParams()
    params.search_active_companies_only = True
    result, _, _ = class_match_svc.listar_tipos_assentos(params)
    assert len(result) == 0


def test_linkar_tipo_assento(tipos_assentos):
    id_assento = tipos_assentos[0].id
    class_match_svc.linkar_tipo_assento(
        id_assento, "leito individual", ["leito", "leito cama", "leito cama individual", "cama premium"]
    )
    tipos_assentos[0].refresh_from_db()
    assert tipos_assentos[0].tipo_assento_buser_preferencial == "leito individual"
    assert tipos_assentos[0].tipos_assentos_buser_match == [
        "leito",
        "leito cama",
        "leito cama individual",
        "cama premium",
    ]


def test_get_buser_class_by_company_registro_existe_sem_assento_preferencial_salvo(
    totalbus_company,
):
    ta = baker.make(
        TipoAssento,
        company=totalbus_company,
        tipo_assento_parceiro="Leito",
        tipo_assento_buser_preferencial=None,
    )
    assert "leito" == class_match_svc.get_buser_class_by_company(totalbus_company, "Leito")
    ta.refresh_from_db()
    assert ta.tipo_assento_buser_preferencial is None


def test_get_buser_class_by_company_registro_existe_e_classe_nao_mapeada(
    totalbus_company,
):
    ta = baker.make(
        TipoAssento,
        company=totalbus_company,
        tipo_assento_parceiro="Século Perdido",
        tipo_assento_buser_preferencial=None,
    )
    assert class_match_svc.CLASSE_NAO_MAPEADA == class_match_svc.get_buser_class_by_company(
        totalbus_company, "Século Perdido"
    )
    assert ta.tipo_assento_buser_preferencial is None
    ta.refresh_from_db()
    assert ta.tipo_assento_buser_preferencial is None


def test_get_buser_class_by_company_registro_nao_existe(
    totalbus_company,
):
    assert "leito" == class_match_svc.get_buser_class_by_company(totalbus_company, "Leito")
    ta = TipoAssento.objects.filter(
        company=totalbus_company,
        tipo_assento_parceiro="Leito",
    ).first()
    assert ta is None


def test_get_buser_class_by_company_registro_nao_existe_e_classe_nao_mapeada(
    totalbus_company,
):
    assert class_match_svc.CLASSE_NAO_MAPEADA == class_match_svc.get_buser_class_by_company(
        totalbus_company, "Nika, o Deus do Sol"
    )
    ta = TipoAssento.objects.filter(
        company=totalbus_company,
        tipo_assento_parceiro="Nika, o Deus do Sol",
    ).first()
    assert ta is None


def test_does_class_match_registro_existe_match_pelo_preferencial(totalbus_company):
    baker.make(
        TipoAssento,
        company=totalbus_company,
        tipo_assento_parceiro="Elbaf",
        tipo_assento_buser_preferencial="A terra dos gigantes",
        tipos_assentos_buser_match=["convencional"],
    )
    assert class_match_svc.does_class_match(totalbus_company, "A terra dos gigantes", "Elbaf")


def test_does_class_match_registro_existe_match(totalbus_company):
    baker.make(
        TipoAssento,
        company=totalbus_company,
        tipo_assento_parceiro="A Terra Sagrada de Mary Geoise",
        tipo_assento_buser_preferencial="onde vivem os Nobres Mundiais",
        tipos_assentos_buser_match=["leito"],
    )
    assert class_match_svc.does_class_match(totalbus_company, "leito", "A Terra Sagrada de Mary Geoise")


def test_does_class_match_registro_nao_existe(totalbus_company):
    assert class_match_svc.does_class_match(totalbus_company, "leito", "LEiTo")
    ta = TipoAssento.objects.filter(
        company=totalbus_company,
        tipo_assento_parceiro="LEiTo",
    ).first()
    assert ta is not None
    assert ta.tipo_assento_buser_preferencial is None
    assert ta.tipos_assentos_buser_match is None


def test_does_class_match_registro_nao_existe_regra_excecao(totalbus_company):
    totalbus_company.company_internal_id = 282
    totalbus_company.save()
    assert class_match_svc.does_class_match(totalbus_company, "semi leito", "Skypeia, A ilha do céu")
    ta = TipoAssento.objects.filter(
        company=totalbus_company,
        tipo_assento_parceiro="Skypeia, A ilha do céu",
    ).first()
    assert ta is not None
    assert ta.tipo_assento_buser_preferencial is None
    assert ta.tipos_assentos_buser_match is None


def test_does_class_match_registro_existe_mas_classe_nao_esta_na_lista_de_match(totalbus_company):
    ta = baker.make(
        TipoAssento,
        company=totalbus_company,
        tipo_assento_parceiro="LEiTo",
        tipo_assento_buser_preferencial=None,
        tipos_assentos_buser_match=["convencional"],
    )
    assert class_match_svc.does_class_match(totalbus_company, "leito", "LEiTo")

    ta.refresh_from_db()
    assert ta.tipo_assento_parceiro == "LEiTo"
    assert ta.tipo_assento_buser_preferencial is None
    assert ta.tipos_assentos_buser_match == ["convencional"]


def test_does_class_match_registro_existe_mas_classe_nao_esta_na_lista_de_match_regra_excecao(totalbus_company):
    totalbus_company.company_internal_id = 282
    totalbus_company.save()
    ta = baker.make(
        TipoAssento,
        company=totalbus_company,
        tipo_assento_parceiro="Joyboy",
        tipo_assento_buser_preferencial=None,
        tipos_assentos_buser_match=["convencional"],
    )
    assert class_match_svc.does_class_match(totalbus_company, "semi leito", "Joyboy")
    ta.refresh_from_db()
    assert ta.tipo_assento_parceiro == "Joyboy"
    assert ta.tipo_assento_buser_preferencial is None
    assert ta.tipos_assentos_buser_match == ["convencional"]


def test_get_map_tipo_assento_empresa(totalbus_company):
    baker.make(
        TipoAssento,
        company=totalbus_company,
        tipo_assento_parceiro="CAMA ESPECIAL RODIZIO PIZZA",
        tipo_assento_buser_preferencial="cama premium",
    )
    baker.make(
        TipoAssento,
        company=totalbus_company,
        tipo_assento_parceiro="CAMA ESPECIAL RODIZIO CHURRASCO",
        tipo_assento_buser_preferencial=None,
    )
    mapa = class_match_svc.get_map_tipo_assento_empresa(totalbus_company.id)
    assert mapa == {"CAMA ESPECIAL RODIZIO PIZZA": "cama premium"}

from model_bakery import baker

from rodoviaria.models.core import Passagem, TaggedPassagem
from rodoviaria.service import solicita_cancelamento_svc


def test_solicita_cancelamento():
    travel_id = 3
    passagens = [
        baker.make("rodoviaria.Passagem", status=Passagem.Status.CONFIRMADA, travel_internal_id=travel_id),
        baker.make("rodoviaria.Passagem", status=Passagem.Status.ERRO, travel_internal_id=travel_id),
    ]
    solicita_cancelamento_svc.solicita_cancelamento(travel_id)
    passagens[0].refresh_from_db()
    passagens[1].refresh_from_db()
    assert passagens[0].status == Passagem.Status.CONFIRMADA
    assert passagens[0].tags_set() == {"cancelamento_pendente"}
    assert passagens[1].tags_set() == set()


def test_solicita_cancelamento_com_buseiro_internal_id():
    travel_id = 3
    passagem = baker.make("rodoviaria.Passagem", status=Passagem.Status.CONFIRMADA, travel_internal_id=travel_id)
    solicita_cancelamento_svc.solicita_cancelamento(travel_id)
    passagem.refresh_from_db()
    assert passagem.status == Passagem.Status.CONFIRMADA
    assert passagem.tags_set() == {"cancelamento_pendente"}


def test_solicita_cancelamento_travels():
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=987)
    trechoclasse = baker.make("rodoviaria.Trechoclasse", trechoclasse_internal_id=5, grupo=grupo)
    travel_id = 3
    passagens = [
        baker.make(
            "rodoviaria.Passagem",
            status=Passagem.Status.CONFIRMADA,
            travel_internal_id=travel_id,
            trechoclasse_integracao=trechoclasse,
        ),
        baker.make(
            "rodoviaria.Passagem",
            status=Passagem.Status.ERRO,
            travel_internal_id=travel_id,
            trechoclasse_integracao=trechoclasse,
        ),
    ]
    solicita_cancelamento_svc.solicita_cancelamento_travels([travel_id])
    passagens[0].refresh_from_db()
    assert passagens[0].status == Passagem.Status.CONFIRMADA
    tags = TaggedPassagem.objects.filter(content_object=passagens[0])
    assert tags.count() == 1
    assert tags.first().tag.name == "cancelamento_pendente"


def test_solicita_cancelamento_passagens():
    passagens = [baker.make("rodoviaria.Passagem"), baker.make("rodoviaria.Passagem")]
    solicita_cancelamento_svc.solicita_cancelamento_passagens(passagens)
    for p in passagens:
        p.refresh_from_db()
        assert p.tags_set() == {"cancelamento_pendente"}


def test_solicita_cancelamento_por_passagens_ids():
    status_confirmada = Passagem.Status.CONFIRMADA
    passagens = baker.make("rodoviaria.Passagem", status=status_confirmada, _quantity=2)
    passagens_ids = [p.id for p in passagens]
    solicita_cancelamento_svc.solicita_cancelamento_por_passagens_ids(passagens_ids)
    for p in passagens:
        p.refresh_from_db()
        assert p.tags_set() == {"cancelamento_pendente"}

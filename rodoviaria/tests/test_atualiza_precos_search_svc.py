import json
from datetime import datetime
from decimal import Decimal

import pytest
import time_machine
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz
from rodoviaria import views
from rodoviaria.api.forms import BuscarServicoForm, ServicoForm
from rodoviaria.api.praxio.api import PraxioAPI
from rodoviaria.api.totalbus.api import TotalbusAPI
from rodoviaria.models.core import (
    CidadeInternal,
    LocalEmbarque,
    RotinaAtualizacaoTrecho,
    TrechoVendido,
    ViagemAPILogger,
)
from rodoviaria.service import atualiza_precos_search_svc
from rodoviaria.service.exceptions import RodoviariaTrechoNotFoundException
from rodoviaria.views_schemas import BuscarViagensAPIParams, BuscarViagensTodasEmpresasAPIParams


@pytest.fixture(scope="module", autouse=True)
def de_lorean():
    with time_machine.travel(datetime(2024, 6, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo"))):
        yield


def test_buscar_viagens_api_duas_integracoes_diferentes(mocker, totalbus_api, praxio_api):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    cidade_internal_destino = baker.make(CidadeInternal)
    local_embarque_origem_totalbus = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        local_embarque_internal_id=1,
        cidade__company=totalbus_api.company,
    )
    local_embarque_destino_totalbus = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        local_embarque_internal_id=2,
        cidade__company=totalbus_api.company,
    )
    local_embarque_origem_praxio = baker.make(
        LocalEmbarque,
        id_external="3",
        cidade__cidade_internal=cidade_internal_origem,
        local_embarque_internal_id=3,
        cidade__company=praxio_api.company,
    )
    local_embarque_destino_praxio = baker.make(
        LocalEmbarque,
        id_external="4",
        cidade__cidade_internal=cidade_internal_destino,
        local_embarque_internal_id=4,
        cidade__company=praxio_api.company,
    )

    # mock buscar corridas
    datetime_ida_corrida_totalbus = datetime(2022, 6, 10, 14, 30)
    corridas_totalbus = BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                tipo_veiculo=2,
                external_id="83123",
                external_datetime_ida=datetime_ida_corrida_totalbus,
                preco=120,
                vagas=21,
                provider_data={"id": "9323"},
                linha="SÃO PAULO X RIO DE JANEIRO",
                veiculo_andar="1",
                veiculo_id=43213,
                rota_external_id="3123",
                classe="leito_especial",
            )
        ],
    )
    datetime_ida_corrida_praxio = to_default_tz(datetime(2024, 5, 15, 22, 45))
    corridas_praxio = BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                external_id="2140017/3",
                preco=Decimal("248.27"),
                external_datetime_ida=datetime_ida_corrida_praxio,
                external_datetime_chegada=None,
                vagas=1,
                classe="LEITO",
                capacidade_classe=None,
                distancia=None,
                classe_reduzida=None,
                key=None,
                external_company_id=None,
            )
        ],
    )
    mock_buscar_corridas_totalbus = mocker.patch.object(TotalbusAPI, "buscar_corridas", return_value=corridas_totalbus)
    mock_buscar_corridas_praxio = mocker.patch.object(PraxioAPI, "buscar_corridas", return_value=corridas_praxio)

    # busca viagens na API
    data_busca = "2024-06-24"
    corridas_dict = atualiza_precos_search_svc.buscar_viagens_api_na_search(
        [totalbus_api.company.company_internal_id, praxio_api.company.company_internal_id],
        cidade_internal_origem.id,
        cidade_internal_destino.id,
        data_busca=data_busca,
    )

    # verifica que parseou e concatenou o resultado das duas buscas
    assert sorted(corridas_dict, key=lambda k: k["company_rodoviaria_id"]) == [
        {
            "company_rodoviaria_id": totalbus_api.company.id,
            "origem_rodoviaria_id": local_embarque_origem_totalbus.id,
            "destino_rodoviaria_id": local_embarque_destino_totalbus.id,
            "datetime_ida": to_default_tz(datetime_ida_corrida_totalbus),
            "tipo_assento_parceiro": "leito_especial",
            "preco": Decimal("120"),
            "id_external": "83123",
        },
        {
            "company_rodoviaria_id": praxio_api.company.id,
            "origem_rodoviaria_id": local_embarque_origem_praxio.id,
            "destino_rodoviaria_id": local_embarque_destino_praxio.id,
            "datetime_ida": to_default_tz(datetime_ida_corrida_praxio),
            "tipo_assento_parceiro": "LEITO",
            "preco": Decimal("248.27"),
            "id_external": "2140017/3",
        },
    ]

    # verifica se chamou as APIs com os parâmetros corretos
    mock_buscar_corridas_totalbus.assert_called_once_with(
        {
            "origem": local_embarque_origem_totalbus.id_external,
            "destino": local_embarque_destino_totalbus.id_external,
            "data": data_busca,
        },
        None,
    )
    mock_buscar_corridas_praxio.assert_called_once_with(
        {
            "origem": local_embarque_origem_praxio.id_external,
            "destino": local_embarque_destino_praxio.id_external,
            "data": data_busca,
        },
        None,
    )


def test_buscar_viagens_api_sem_local_embarque_linkado(totalbus_api, mocker):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    cidade_internal_destino = baker.make(CidadeInternal)
    local_embarque_origem_totalbus = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        cidade__company=totalbus_api.company,
    )
    local_embarque_destino_totalbus = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        cidade__company=totalbus_api.company,
    )

    # mock buscar corridas
    datetime_ida_corrida_totalbus = datetime(2022, 6, 10, 14, 30)
    corridas_totalbus = BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                tipo_veiculo=2,
                external_id="83123",
                external_datetime_ida=datetime_ida_corrida_totalbus,
                preco=120,
                vagas=21,
                provider_data={"id": "9323"},
                linha="SÃO PAULO X RIO DE JANEIRO",
                veiculo_andar="1",
                veiculo_id=43213,
                rota_external_id="3123",
                classe="leito_especial",
            )
        ],
    )
    mock_buscar_corridas_totalbus = mocker.patch.object(TotalbusAPI, "buscar_corridas", return_value=corridas_totalbus)

    # busca viagens na API
    data_busca = "2024-06-24"
    corridas_dict = atualiza_precos_search_svc.buscar_viagens_api_na_search(
        [totalbus_api.company.company_internal_id],
        cidade_internal_origem.id,
        cidade_internal_destino.id,
        data_busca=data_busca,
    )

    # verifica se retornou as viagens sem origem_internal_id
    assert corridas_dict == [
        {
            "company_rodoviaria_id": totalbus_api.company.id,
            "origem_rodoviaria_id": local_embarque_origem_totalbus.id,
            "destino_rodoviaria_id": local_embarque_destino_totalbus.id,
            "datetime_ida": to_default_tz(datetime_ida_corrida_totalbus),
            "tipo_assento_parceiro": "leito_especial",
            "preco": Decimal("120"),
            "id_external": "83123",
        },
    ]

    # verifica se chamou a API com os parâmetros corretos
    mock_buscar_corridas_totalbus.assert_called_once_with(
        {
            "origem": local_embarque_origem_totalbus.id_external,
            "destino": local_embarque_destino_totalbus.id_external,
            "data": data_busca,
        },
        None,
    )


def test_buscar_viagens_api_RodoviariaTrechoNotFoundException(totalbus_api, mocker):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    cidade_internal_destino = baker.make(CidadeInternal)
    local_embarque_origem_totalbus = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        cidade__company=totalbus_api.company,
    )
    local_embarque_destino_totalbus = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        cidade__company=totalbus_api.company,
    )

    # mock buscar corridas
    mock_buscar_corridas_totalbus = mocker.patch.object(
        TotalbusAPI, "buscar_corridas", side_effect=RodoviariaTrechoNotFoundException("trecho_nao_encontrado")
    )

    # busca viagens na API
    data_busca = "2024-06-24"
    corridas_dict = atualiza_precos_search_svc.buscar_viagens_api_na_search(
        [totalbus_api.company.company_internal_id],
        cidade_internal_origem.id,
        cidade_internal_destino.id,
        data_busca=data_busca,
    )

    # verifica se retornou as viagens sem origem_internal_id
    assert corridas_dict == []

    # verifica se chamou a API com os parâmetros corretos
    mock_buscar_corridas_totalbus.assert_called_once_with(
        {
            "origem": local_embarque_origem_totalbus.id_external,
            "destino": local_embarque_destino_totalbus.id_external,
            "data": data_busca,
        },
        None,
    )


def test_buscar_viagens_api_origem_e_destino_iguais(totalbus_api, mocker):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        cidade__company=totalbus_api.company,
    )

    # mock buscar corridas
    mock_buscar_corridas_totalbus = mocker.patch.object(TotalbusAPI, "buscar_corridas")

    # busca viagens na API
    data_busca = "2024-06-24"
    corridas_dict = atualiza_precos_search_svc.buscar_viagens_api_na_search(
        [totalbus_api.company.company_internal_id],
        cidade_internal_origem.id,
        cidade_internal_origem.id,
        data_busca=data_busca,
    )

    # verifica se retornou as viagens sem origem_internal_id
    assert corridas_dict == []

    # verifica se não chamou a API
    mock_buscar_corridas_totalbus.assert_not_called()


def test_buscar_viagens_todas_empresas_api_na_search_sem_local_embarque_linkado(totalbus_api, mocker):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    cidade_internal_destino = baker.make(CidadeInternal)
    rotina_atualizacao = baker.make(RotinaAtualizacaoTrecho)
    local_embarque_origem_totalbus = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        cidade__company=totalbus_api.company,
    )
    local_embarque_destino_totalbus = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        cidade__company=totalbus_api.company,
    )
    baker.make(
        TrechoVendido,
        origem=local_embarque_origem_totalbus,
        destino=local_embarque_destino_totalbus,
        ativo=True,
        rota__ativo=True,
        rota__company=totalbus_api.company,
    )

    # mock buscar corridas
    datetime_ida_corrida_totalbus = datetime(2022, 6, 10, 14, 30)
    corridas_totalbus = BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                tipo_veiculo=2,
                external_id="83123",
                external_datetime_ida=datetime_ida_corrida_totalbus,
                preco=120,
                vagas=21,
                provider_data={"id": "9323"},
                linha="SÃO PAULO X RIO DE JANEIRO",
                veiculo_andar="1",
                veiculo_id=43213,
                rota_external_id="3123",
                classe="leito_especial",
            )
        ],
    )
    mock_buscar_corridas_totalbus = mocker.patch.object(TotalbusAPI, "buscar_corridas", return_value=corridas_totalbus)

    # busca viagens na API
    data_busca = "2024-06-24"
    corridas_dict = atualiza_precos_search_svc.buscar_viagens_todas_empresas_api_na_search(
        cidade_internal_origem.id,
        cidade_internal_destino.id,
        data_busca=data_busca,
        rotina_atualizacao_id=rotina_atualizacao.id,
    )

    assert ViagemAPILogger.objects.filter(
        company=totalbus_api.company,
        origem=local_embarque_origem_totalbus,
        destino=local_embarque_destino_totalbus,
        datetime_ida=to_default_tz(datetime_ida_corrida_totalbus),
        tipo_assento_parceiro="leito_especial",
        rotina_atualizacao=rotina_atualizacao,
    ).exists()

    # verifica se retornou as viagens sem origem_internal_id
    assert corridas_dict == [
        {
            "company_rodoviaria_id": totalbus_api.company.id,
            "origem_rodoviaria_id": local_embarque_origem_totalbus.id,
            "destino_rodoviaria_id": local_embarque_destino_totalbus.id,
            "datetime_ida": to_default_tz(datetime_ida_corrida_totalbus),
            "tipo_assento_parceiro": "leito_especial",
            "preco": Decimal("120"),
            "id_external": "83123",
        },
    ]

    # verifica se chamou a API com os parâmetros corretos
    mock_buscar_corridas_totalbus.assert_called_once_with(
        {
            "origem": local_embarque_origem_totalbus.id_external,
            "destino": local_embarque_destino_totalbus.id_external,
            "data": data_busca,
        },
        None,
    )


def test_buscar_viagens_todas_empresas_api_na_search_com_vagas_negativas(totalbus_api, mocker):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    cidade_internal_destino = baker.make(CidadeInternal)
    rotina_atualizacao = baker.make(RotinaAtualizacaoTrecho)
    local_embarque_origem_totalbus = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        cidade__company=totalbus_api.company,
    )
    local_embarque_destino_totalbus = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        cidade__company=totalbus_api.company,
    )
    baker.make(
        TrechoVendido,
        origem=local_embarque_origem_totalbus,
        destino=local_embarque_destino_totalbus,
        ativo=True,
        rota__ativo=True,
        rota__company=totalbus_api.company,
    )

    # mock buscar corridas
    datetime_ida_corrida_totalbus = datetime(2022, 6, 10, 14, 30)
    corridas_totalbus = BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                tipo_veiculo=2,
                external_id="83123",
                external_datetime_ida=datetime_ida_corrida_totalbus,
                preco=120,
                vagas=-4,
                provider_data={"id": "9323"},
                linha="SÃO PAULO X RIO DE JANEIRO",
                veiculo_andar="1",
                veiculo_id=43213,
                rota_external_id="3123",
                classe="leito_especial",
            )
        ],
    )
    mocker.patch.object(TotalbusAPI, "buscar_corridas", return_value=corridas_totalbus)

    # busca viagens na API
    data_busca = "2024-06-24"
    corridas_dict = atualiza_precos_search_svc.buscar_viagens_todas_empresas_api_na_search(
        cidade_internal_origem.id,
        cidade_internal_destino.id,
        data_busca=data_busca,
        rotina_atualizacao_id=rotina_atualizacao.id,
    )

    assert not ViagemAPILogger.objects.filter(company=totalbus_api.company).exists()

    # verifica se retornou as viagens sem origem_internal_id
    assert corridas_dict == []


def test_busca_viagems_api_log(caplog, totalbus_api, mocker):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    cidade_internal_destino = baker.make(CidadeInternal)
    origem = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        cidade__company=totalbus_api.company,
    )
    destino = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        cidade__company=totalbus_api.company,
    )

    # mock buscar corridas
    datetime_ida_corrida_totalbus = datetime(2022, 6, 10, 14, 30)
    corridas_totalbus = BuscarServicoForm(
        found=True,
        servicos=[
            ServicoForm(
                tipo_veiculo=2,
                external_id="83123",
                external_datetime_ida=datetime_ida_corrida_totalbus,
                preco=120,
                vagas=21,
                provider_data={"id": "9323"},
                linha="SÃO PAULO X RIO DE JANEIRO",
                veiculo_andar="1",
                veiculo_id=43213,
                rota_external_id="3123",
                classe="leito_especial",
            )
        ],
    )
    mocker.patch.object(TotalbusAPI, "buscar_corridas", return_value=corridas_totalbus)

    # busca viagens na API
    data_busca = "2024-06-24"
    atualiza_precos_search_svc.buscar_viagens_api_na_search(
        [totalbus_api.company.company_internal_id],
        cidade_internal_origem.id,
        cidade_internal_destino.id,
        data_busca=data_busca,
    )
    assert ViagemAPILogger.objects.filter(
        company=totalbus_api.company,
        origem=origem,
        destino=destino,
        datetime_ida=to_default_tz(datetime_ida_corrida_totalbus),
        tipo_assento_parceiro="leito_especial",
        vagas=21,
        preco=Decimal("120"),
    ).exists()


def test_fluxo_completo_buscar_viagens_api(rf, totalbus_api, totalbus_mock_buscar_corridas):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    cidade_internal_destino = baker.make(CidadeInternal)
    origem = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        local_embarque_internal_id=1,
        cidade__company=totalbus_api.company,
    )
    destino = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        local_embarque_internal_id=2,
        cidade__company=totalbus_api.company,
    )

    # busca viagens na API
    params = BuscarViagensAPIParams.parse_obj(
        {
            "companies_ids": [totalbus_api.company.company_internal_id],
            "origem_id": cidade_internal_origem.id,
            "destino_id": cidade_internal_destino.id,
            "data_busca": "2024-06-24",
            "execute_async": False,
        }
    )
    request = rf.get("/v1/buscar-viagens-api-search")
    corridas_dict = views.buscar_viagens_api_na_search(request, params)

    # verifica se retornou as viagens mockadas da API
    assert json.loads(corridas_dict.content)[0] == {
        "company_rodoviaria_id": totalbus_api.company.id,
        "origem_rodoviaria_id": origem.id,
        "destino_rodoviaria_id": destino.id,
        "datetime_ida": "2021-05-14T20:05:00-03:00",
        "tipo_assento_parceiro": "LEITO CAMA",
        "preco": "142.41",
        "id_external": "712",
    }


def test_fluxo_completo_buscar_viagens_api_execute_async(rf, mocker):
    # mock chamada async
    mock_buscar_viagens_api = mocker.patch("rodoviaria.service.atualiza_precos_search_svc.buscar_viagens_api_na_search")

    # busca viagens na API
    params = BuscarViagensAPIParams.parse_obj(
        {"companies_ids": [1], "origem_id": 1, "destino_id": 2, "data_busca": "2024-06-24", "execute_async": True}
    )
    request = rf.get("/v1/buscar-viagens-api-search")
    resp = views.buscar_viagens_api_na_search(request, params)

    # verifica se fez a chamada asyn com os parâmetros corretos
    mock_buscar_viagens_api.s.assert_called_once_with([1], 1, 2, "2024-06-24")
    mock_buscar_viagens_api.s.return_value.apply_async.assert_called_once()
    # verifica resposta
    assert json.loads(resp.content) == {"message": "busca iniciada"}


def test_fluxo_completo_buscar_viagens_todas_empresas_api_na_search(rf, totalbus_api, totalbus_mock_buscar_corridas):
    # cria locais de embarque de cada empresa
    timezone = "America/Sao_Paulo"
    cidade_internal_origem = baker.make(CidadeInternal, timezone=timezone)
    cidade_internal_destino = baker.make(CidadeInternal)
    origem = baker.make(
        LocalEmbarque,
        id_external="1",
        cidade__cidade_internal=cidade_internal_origem,
        local_embarque_internal_id=1,
        cidade__company=totalbus_api.company,
    )
    destino = baker.make(
        LocalEmbarque,
        id_external="2",
        cidade__cidade_internal=cidade_internal_destino,
        local_embarque_internal_id=2,
        cidade__company=totalbus_api.company,
    )
    baker.make(
        TrechoVendido,
        origem=origem,
        destino=destino,
        ativo=True,
        rota__ativo=True,
        rota__company=totalbus_api.company,
    )

    # busca viagens na API
    params = BuscarViagensTodasEmpresasAPIParams.parse_obj(
        {
            "origem_id": cidade_internal_origem.id,
            "destino_id": cidade_internal_destino.id,
            "data_busca": "2024-06-24",
            "execute_async": False,
        }
    )
    request = rf.get("/v1/buscar-viagens-todas-empresas-api-search")
    corridas_dict = views.buscar_viagens_todas_empresas_api_na_search(request, params)

    # verifica se retornou as viagens mockadas da API
    assert json.loads(corridas_dict.content)[0] == {
        "company_rodoviaria_id": totalbus_api.company.id,
        "origem_rodoviaria_id": origem.id,
        "destino_rodoviaria_id": destino.id,
        "datetime_ida": "2021-05-14T20:05:00-03:00",
        "tipo_assento_parceiro": "LEITO CAMA",
        "preco": "142.41",
        "id_external": "712",
    }


def test_fluxo_completo_buscar_viagens_todas_empresas_api_na_search_execute_async(rf, mocker):
    # mock chamada async
    mock_buscar_viagens_api = mocker.patch(
        "rodoviaria.service.atualiza_precos_search_svc.buscar_viagens_todas_empresas_api_na_search"
    )

    # busca viagens na API
    params = BuscarViagensTodasEmpresasAPIParams.parse_obj(
        {"origem_id": 1, "destino_id": 2, "data_busca": "2024-06-24", "execute_async": True}
    )
    request = rf.get("/v1/buscar-viagens-todas-empresas-api-search")
    resp = views.buscar_viagens_todas_empresas_api_na_search(request, params)

    # verifica se fez a chamada asyn com os parâmetros corretos
    mock_buscar_viagens_api.s.assert_called_once_with(1, 2, "2024-06-24")
    mock_buscar_viagens_api.s.return_value.apply_async.assert_called_once()
    # verifica resposta
    assert json.loads(resp.content) == {"message": "busca iniciada"}

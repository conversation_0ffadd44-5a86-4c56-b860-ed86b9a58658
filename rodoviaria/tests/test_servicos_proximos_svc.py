from datetime import datetime, timedelta

from commons.dateutils import to_tz
from rodoviaria.api.forms import ServicoForm
from rodoviaria.models.core import TrechoClasseError
from rodoviaria.service.servicos_proximos_svc import motivo_servico_nao_encontrado


def test_motivo_servico_nao_encontrado_servico_fora_do_horario_dentro_da_margem(totalbus_company):
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime(2023, 7, 2, 19, 21), timezone)
    datetime_ida_servico_proximo = datetime_ida + timedelta(minutes=40)
    datetime_ida_servico_proximo_2 = datetime_ida - timedelta(minutes=5)
    datetime_ida_servico_distante = datetime_ida + timedelta(hours=4)
    servico_proximo = ServicoForm.parse_obj(
        {
            "external_id": "31923",
            "preco": "29.90",
            "external_datetime_ida": datetime_ida_servico_proximo.isoformat(),
            "classe": "semi leito",
            "external_company_id": 9,
            "vagas": 12,
            "provider_data": {},
        }
    )
    servico_proximo_2 = ServicoForm.parse_obj(
        {
            "external_id": "31923",
            "preco": "29.90",
            "external_datetime_ida": datetime_ida_servico_proximo_2.isoformat(),
            "classe": "leito",
            "external_company_id": 9,
            "vagas": 12,
            "provider_data": {},
        }
    )
    servico_distante = ServicoForm.parse_obj(
        {
            "external_id": "31923",
            "preco": "29.90",
            "external_datetime_ida": datetime_ida_servico_distante.isoformat(),
            "classe": "leito",
            "external_company_id": 9,
            "vagas": 12,
            "provider_data": {},
        }
    )
    tipo_assento = "semi leito"
    servicos_proximos = [servico_proximo.dict(), servico_proximo_2.dict(), servico_distante.dict()]

    motivo, trechos_possiveis = motivo_servico_nao_encontrado(
        totalbus_company, datetime_ida, timezone, tipo_assento, servicos_proximos
    )

    assert motivo == TrechoClasseError.Motivo.MISMATCH_DE_HORARIO
    assert trechos_possiveis == [
        (datetime_ida_servico_proximo_2.strftime("%H:%M"), "leito"),
        (datetime_ida_servico_proximo.strftime("%H:%M"), "semi leito"),
    ]


def test_motivo_servico_nao_encontrado_servico_fora_do_horario_fora_da_margem(totalbus_company):
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime(2023, 7, 2, 19, 21), timezone)
    datetime_ida_servico_proximo = datetime_ida + timedelta(minutes=200)
    datetime_ida_servico_proximo_2 = datetime_ida - timedelta(minutes=200)
    servico_proximo = ServicoForm.parse_obj(
        {
            "external_id": "31923",
            "preco": "29.90",
            "external_datetime_ida": datetime_ida_servico_proximo.isoformat(),
            "classe": "semi leito",
            "external_company_id": 9,
            "vagas": 12,
            "provider_data": {},
        }
    )
    servico_proximo_2 = ServicoForm.parse_obj(
        {
            "external_id": "31923",
            "preco": "29.90",
            "external_datetime_ida": datetime_ida_servico_proximo_2.isoformat(),
            "classe": "leito",
            "external_company_id": 9,
            "vagas": 12,
            "provider_data": {},
        }
    )
    tipo_assento = "semi leito"
    servicos_proximos = [servico_proximo.dict(), servico_proximo_2.dict()]

    motivo, trechos_possiveis = motivo_servico_nao_encontrado(
        totalbus_company, datetime_ida, timezone, tipo_assento, servicos_proximos
    )

    assert motivo == TrechoClasseError.Motivo.SEM_SERVICO
    assert trechos_possiveis == [
        (datetime_ida_servico_proximo_2.strftime("%H:%M"), "leito"),
        (datetime_ida_servico_proximo.strftime("%H:%M"), "semi leito"),
    ]


def test_motivo_servico_nao_encontrado_servico_classe_diferente(totalbus_company):
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime(2023, 7, 2, 19, 21), timezone)
    datetime_ida_servico_proximo = datetime_ida + timedelta(minutes=15)
    servico_proximo = ServicoForm.parse_obj(
        {
            "external_id": "31923",
            "preco": "29.90",
            "external_datetime_ida": datetime_ida_servico_proximo.isoformat(),
            "classe": "semi leito",
            "external_company_id": 9,
            "vagas": 12,
            "provider_data": {},
        }
    )
    servico_distante = ServicoForm.parse_obj(
        {
            "external_id": "31923",
            "preco": "29.90",
            "external_datetime_ida": (datetime_ida_servico_proximo + timedelta(hours=6)).isoformat(),
            "classe": "semi leito",
            "external_company_id": 9,
            "vagas": 12,
            "provider_data": {},
        }
    )
    tipo_assento = "leito"
    servicos_proximos = [servico_proximo.dict(), servico_distante.dict()]

    motivo, trechos_possiveis = motivo_servico_nao_encontrado(
        totalbus_company, datetime_ida, timezone, tipo_assento, servicos_proximos
    )

    assert motivo == TrechoClasseError.Motivo.MISMATCH_DE_CLASSE
    assert trechos_possiveis == [(datetime_ida_servico_proximo.strftime("%H:%M"), "semi leito")]


def test_motivo_servico_nao_encontrado_sem_servicos_proximos(totalbus_company):
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime(2023, 7, 2, 19, 21), timezone)
    tipo_assento = "leito"
    servicos_proximos = []

    motivo, trechos_possiveis = motivo_servico_nao_encontrado(
        totalbus_company, datetime_ida, timezone, tipo_assento, servicos_proximos
    )

    assert motivo == TrechoClasseError.Motivo.SEM_SERVICO
    assert trechos_possiveis == []

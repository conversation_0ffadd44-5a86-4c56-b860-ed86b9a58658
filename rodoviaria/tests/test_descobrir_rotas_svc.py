from unittest import mock

import pytest
from celery.result import GroupResult
from django.core.management import call_command
from model_bakery import baker

from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Company, TaskStatus
from rodoviaria.service import descobrir_rotas_svc, exceptions


@pytest.fixture
def group_task_object():
    class Group(GroupResult):
        items = []
        _index = 0

        def __bool__(self):
            return True

        def __iter__(self):
            return self

        def __next__(self):
            if self._index < len(self.items):
                return self.items[self._index]
            raise StopIteration

        def __getitem__(self, i):
            return self.items[i]

        def __len__(self):
            return len(self.items)

        def ready(self):
            return True

    return Group("123")


def test_descobrir_rotas_proximos_dias(totalbus_login, group_task_object):
    company_internal_id = totalbus_login.company.company_internal_id
    with mock.patch.object(OrchestrateRodoviaria, "descobrir_rotas_async") as mock_descobrir_rotas_async:
        mock_descobrir_rotas_async.return_value = group_task_object
        task = descobrir_rotas_svc.descobrir_rotas_proximos_dias(totalbus_login.company, next_days=14, shift_days=3)
    assert task["task_id"]
    assert task["mensagem"] == f"Descobre rotas da empresa {company_internal_id} nos próximos 14 dias"
    assert (
        TaskStatus.objects.get(company=totalbus_login.company, task_name=TaskStatus.Name.DESCOBRIR_ROTAS).status
        == TaskStatus.Status.PENDING
    )


def test_descobrir_rotas_proximos_dias_failure(totalbus_login, group_task_object):
    with mock.patch.object(OrchestrateRodoviaria, "descobrir_rotas_async") as mock_descobrir_rotas_async, pytest.raises(
        Exception, match="Algum erro"
    ):
        mock_descobrir_rotas_async.side_effect = Exception("Algum erro")
        descobrir_rotas_svc.descobrir_rotas_proximos_dias(totalbus_login.company, next_days=14, shift_days=3)
    assert (
        TaskStatus.objects.get(company=totalbus_login.company, task_name=TaskStatus.Name.DESCOBRIR_ROTAS).status
        == TaskStatus.Status.NOT_STARTED
    )


def test_command_descobrir_rotas(rf):
    company = baker.make(Company, company_internal_id=231, margem_dias_busca_operacao=140)
    with mock.patch(
        "rodoviaria.service.descobrir_rotas_svc.descobrir_rotas_proximos_dias", autospec=True
    ) as mock_descobrir_rotas:
        call_command("descobrir_rotas", company_internal_id=231, modelo_venda=Company.ModeloVenda.MARKETPLACE)
    mock_descobrir_rotas.assert_called_once_with(company=company, next_days=140)


def test_descobrir_rotas_proximos_dias_empresa_hibrido(totalbus_login):
    totalbus_login.company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_login.company.save()
    with pytest.raises(exceptions.HibridoNotAllowedException):
        descobrir_rotas_svc.descobrir_rotas_proximos_dias(totalbus_login.company, next_days=14, shift_days=3)


def test_descobrir_rotas_proximos_dias_return_task(totalbus_login, group_task_object):
    with mock.patch.object(OrchestrateRodoviaria, "descobrir_rotas_async") as mock_descobrir_rotas_async:
        mock_descobrir_rotas_async.return_value = group_task_object
        task = descobrir_rotas_svc.descobrir_rotas_proximos_dias(
            totalbus_login.company,
            next_days=14,
            shift_days=3,
            return_task_object=True,
        )
        assert task == mock_descobrir_rotas_async.return_value

from datetime import datetime, timedelta
from unittest import mock

from django.core.management import call_command
from model_bakery import baker

from commons.dateutils import to_default_tz
from rodoviaria import views
from rodoviaria.service import link_grupos_classe_svc
from rodoviaria.service.status_integracao_svc import StatusIntegracaoSVC


def test_update_status_integracao_task():
    with mock.patch.object(StatusIntegracaoSVC, "atualiza") as mock_atualiza_status:
        link_grupos_classe_svc._update_status_integracao_task(10)
    mock_atualiza_status.assert_called_once_with()


def test_link_grupos_classe_nao_linkados_grupo_ja_linkado():
    datetime_now = to_default_tz(datetime(2022, 2, 10, 14, 30))
    buser_company = baker.make("core.Company")
    buser_grupo = baker.make(
        "core.Grupo",
        company=buser_company,
        datetime_ida=datetime_now + timedelta(days=10),
        modelo_venda="hibrido",
    )
    buser_grupo_classe = baker.make("core.GrupoClasse", grupo=buser_grupo)
    rodov_company = baker.make("rodoviaria.Company", modelo_venda="hibrido")
    rodov_grupo = baker.make(
        "rodoviaria.Grupo",
        datetime_ida=datetime_now + timedelta(days=10),
        company_integracao=rodov_company,
    )
    baker.make(
        "rodoviaria.GrupoClasse",
        grupo=rodov_grupo,
        grupoclasse_internal_id=buser_grupo_classe.id,
    )
    with mock.patch("rodoviaria.service.link_grupos_classe_svc.timezone.now") as mock_now:
        mock_now.return_value = datetime_now
        response = link_grupos_classe_svc.link_grupos_classe_nao_linkados()
    assert response == {"trechos_classe_atualizados": 0}


def test_link_grupos_classe_nao_linkados_grupo_passado():
    datetime_now = to_default_tz(datetime(2022, 2, 10, 14, 30))
    buser_company = baker.make("core.Company")
    buser_grupo = baker.make(
        "core.Grupo",
        company=buser_company,
        datetime_ida=datetime_now - timedelta(days=10),
        modelo_venda="hibrido",
    )
    baker.make("core.GrupoClasse", grupo=buser_grupo)
    with mock.patch("rodoviaria.service.link_grupos_classe_svc.timezone.now") as mock_now:
        mock_now.return_value = datetime_now
        response = link_grupos_classe_svc.link_grupos_classe_nao_linkados()
    assert response == {"trechos_classe_atualizados": 0}


def test_link_grupos_classe_nao_linkados_grupo_sem_empresa():
    datetime_now = to_default_tz(datetime(2022, 2, 10, 14, 30))
    buser_grupo = baker.make(
        "core.Grupo",
        datetime_ida=datetime_now + timedelta(days=10),
        modelo_venda="hibrido",
    )
    baker.make("core.GrupoClasse", grupo=buser_grupo)
    with mock.patch("rodoviaria.service.link_grupos_classe_svc.timezone.now") as mock_now:
        mock_now.return_value = datetime_now
        response = link_grupos_classe_svc.link_grupos_classe_nao_linkados()
    assert response == {"trechos_classe_atualizados": 0}


def test_link_grupos_classe_nao_linkados_grupo_sem_trechoclasse():
    datetime_now = to_default_tz(datetime(2022, 2, 10, 14, 30))
    buser_company = baker.make("core.Company")
    buser_grupo = baker.make(
        "core.Grupo",
        company=buser_company,
        datetime_ida=datetime_now + timedelta(days=10),
        modelo_venda="hibrido",
    )
    baker.make("core.GrupoClasse", grupo=buser_grupo)
    with mock.patch("rodoviaria.service.link_grupos_classe_svc.timezone.now") as mock_now:
        mock_now.return_value = datetime_now
        response = link_grupos_classe_svc.link_grupos_classe_nao_linkados()
    assert response == {"trechos_classe_atualizados": 0}


def test_link_grupos_classe_nao_linkados():
    datetime_now = to_default_tz(datetime(2022, 2, 10, 14, 30))
    buser_origem = baker.make("core.LocalEmbarque")
    buser_rota = baker.make("core.Rota", origem=buser_origem)
    buser_trecho_vendido = baker.make("core.TrechoVendido", origem=buser_origem)
    buser_company = baker.make("core.Company")
    buser_grupo = baker.make(
        "core.Grupo",
        company=buser_company,
        datetime_ida=datetime_now + timedelta(days=10),
        modelo_venda="hibrido",
        rota=buser_rota,
    )
    buser_grupo_classe = baker.make("core.GrupoClasse", grupo=buser_grupo)
    buser_trecho_classe = baker.make(
        "core.TrechoClasse",
        grupo_classe=buser_grupo_classe,
        trecho_vendido=buser_trecho_vendido,
    )
    with mock.patch("rodoviaria.service.link_grupos_classe_svc.timezone.now") as mock_now, mock.patch(
        "rodoviaria.service.link_grupos_classe_svc.StatusIntegracaoSVC"
    ) as mock_StatusIntegracaoSVC:
        mock_now.return_value = datetime_now
        response = link_grupos_classe_svc.link_grupos_classe_nao_linkados()
    assert response == {"trechos_classe_atualizados": 1}
    mock_StatusIntegracaoSVC.assert_called_once_with(internal_trecho_classe_id=buser_trecho_classe.id)
    mock_StatusIntegracaoSVC.return_value.atualiza.assert_called_once_with()


def test_link_grupos_classe_nao_linkados_command():
    with mock.patch(
        "rodoviaria.service.link_grupos_classe_svc.link_grupos_classe_nao_linkados"
    ) as mock_link_grupos_classe_nao_linkados:
        call_command("link_grupos_classe_nao_linkados")
    mock_link_grupos_classe_nao_linkados.assert_called_once_with()


def test_link_grupos_classe_nao_linkados_endpoint(rf):
    with mock.patch(
        "rodoviaria.service.link_grupos_classe_svc.link_grupos_classe_nao_linkados"
    ) as mock_link_grupos_classe_nao_linkados:
        req = rf.post("/v1/hibrido/link-grupos-classe")
        views.link_grupos_classe_hibridos(req)
    mock_link_grupos_classe_nao_linkados.assert_called_once_with()


def test_cancela_grupos_classe_com_link_invalido_command():
    with mock.patch(
        "rodoviaria.service.link_grupos_classe_svc.cancela_grupos_classe_com_link_invalido"
    ) as mock_cancela_grupos_classe_com_link_invalido:
        call_command("cancela_grupos_classe_com_link_invalido")
    mock_cancela_grupos_classe_com_link_invalido.assert_called_once_with()


def test_cancela_grupos_classe_com_link_invalido_endpoint(rf):
    with mock.patch(
        "rodoviaria.service.link_grupos_classe_svc.cancela_grupos_classe_com_link_invalido"
    ) as mock_cancela_grupos_classe_com_link_invalido:
        req = rf.post("/v1/hibrido/cancela-grupos-classe-invalidos")
        views.cancela_grupos_classe_invalidos(req)
    mock_cancela_grupos_classe_com_link_invalido.assert_called_once_with()


def test_cancela_grupo_classe_task():
    with mock.patch(
        "rodoviaria.service.cancelar_grupos_hibridos_svc.cancelar_grupos_classe"
    ) as mock_cancelar_grupos_classe:
        link_grupos_classe_svc._cancela_grupo_classe_task(10, 40)
    mock_cancelar_grupos_classe.assert_called_once_with(10, [40])


def test_cancela_grupos_classe_com_link_invalido_grupo_com_link():
    datetime_now = to_default_tz(datetime(2022, 2, 10, 14, 30))
    buser_grupo_classe = baker.make("core.GrupoClasse")
    rodov_company = baker.make("rodoviaria.Company", modelo_venda="hibrido")
    rodov_grupo = baker.make(
        "rodoviaria.Grupo",
        company_integracao=rodov_company,
        datetime_ida=datetime_now + timedelta(days=5),
    )
    rodov_grupo_classe = baker.make(
        "rodoviaria.GrupoClasse",
        grupoclasse_internal_id=buser_grupo_classe.id,
        grupo=rodov_grupo,
    )
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=rodov_grupo_classe,
        status="created",
    )
    with mock.patch("rodoviaria.service.link_grupos_classe_svc.timezone.now") as mock_now:
        mock_now.return_value = datetime_now
        response = link_grupos_classe_svc.cancela_grupos_classe_com_link_invalido()
    assert response == {"grupos_classe_to_cancel": 0}


def test_cancela_grupos_classe_com_link_invalido_grupo_sem_link():
    datetime_now = to_default_tz(datetime(2022, 2, 10, 14, 30))
    rodov_company = baker.make("rodoviaria.Company", modelo_venda="hibrido", company_internal_id=738)
    rodov_grupo = baker.make(
        "rodoviaria.Grupo",
        company_integracao=rodov_company,
        datetime_ida=datetime_now + timedelta(days=5),
    )
    rodov_grupo_classe = baker.make("rodoviaria.GrupoClasse", grupoclasse_internal_id=832934, grupo=rodov_grupo)
    baker.make(
        "rodoviaria.VexadoGrupoClasse",
        grupo_classe=rodov_grupo_classe,
        status="created",
    )
    with mock.patch("rodoviaria.service.link_grupos_classe_svc.timezone.now") as mock_now, mock.patch(
        "rodoviaria.service.cancelar_grupos_hibridos_svc.cancelar_grupos_classe"
    ) as mock_cancelar_grupos_classe:
        mock_now.return_value = datetime_now
        response = link_grupos_classe_svc.cancela_grupos_classe_com_link_invalido()
    assert response == {"grupos_classe_to_cancel": 1}
    mock_cancelar_grupos_classe.assert_called_once_with(738, [832934])

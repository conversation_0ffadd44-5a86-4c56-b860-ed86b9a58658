import json

from django.db.models.query_utils import Q

from rodoviaria import views
from rodoviaria.models.core import Cidade, Company, LocalEmbarque
from rodoviaria.service.map_marketplace_cidades_svc import MapMarketplaceCidadesSVC
from rodoviaria.tests.totalbus.conftest import mock_atualiza_origens

mock_atualiza_origens = mock_atualiza_origens


def test_map_marketplace_cidades_praxio(cidades_internal, praxio_company, mock_map_cidades_praxio):
    locais = LocalEmbarque.objects.count()
    assert locais == 0
    count = MapMarketplaceCidadesSVC(praxio_company.company_internal_id).execute()
    assert count == 6
    assert Cidade.objects.filter(~Q(cidade_internal_id=None)).count() == 2


def test_map_marketplace_cidades_totalbus(cidades_internal, totalbus_company, mock_atualiza_origens):
    locais = LocalEmbarque.objects.count()
    assert locais == 0
    count = MapMarketplaceCidadesSVC(totalbus_company.company_internal_id).execute()
    assert count == 2
    assert Cidade.objects.filter(~Q(cidade_internal_id=None)).count() == 1


def test_endpoint_map_cidades_default_modelo_venda(rf, cidades_internal, totalbus_company, mock_atualiza_origens):
    company_rodoviaria_id = totalbus_company.id
    request_body = {"company_rodoviaria_id": company_rodoviaria_id}
    req = rf.post("/map_cidade_e_locais", request_body, content_type="application/json")
    resp = views.map_cidade_e_locais(req)
    assert resp.status_code == 200
    respj = json.loads(resp.content)
    assert respj["count"] == 2


def test_endpoint_map_cidades_empresa_nao_encontrada(rf):
    company_rodoviaria_id = 8932
    request_body = {"company_rodoviaria_id": company_rodoviaria_id}
    req = rf.post("/map_cidade_e_locais", request_body, content_type="application/json")
    resp = views.map_cidade_e_locais(req)
    assert resp.status_code == 404
    respj = json.loads(resp.content)
    assert respj == {"error": "Empresa não encontrada"}


def test_endpoint_map_cidades_hibrido(rf, cidades_internal, totalbus_company, mock_atualiza_origens):
    totalbus_company.modelo_venda = Company.ModeloVenda.HIBRIDO
    totalbus_company.save()
    request_body = {"company_rodoviaria_id": totalbus_company.id}
    req = rf.post("/map_cidade_e_locais", request_body, content_type="application/json")
    resp = views.map_cidade_e_locais(req)
    assert resp.status_code == 200
    respj = json.loads(resp.content)
    assert respj["count"] == 2

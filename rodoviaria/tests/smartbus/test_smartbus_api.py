import copy
import json
from datetime import datetime
from decimal import Decimal as D
from unittest import mock

import pytest
import time_machine
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz
from rodoviaria.api.forms import Localidade, ServicoForm
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.api.smartbus.api import async_desbloquear_poltronas, fetch_formas_pagamento
from rodoviaria.api.smartbus.exceptions import SmartbusAPIError
from rodoviaria.forms.cancela_rodoviaria_forms import CancelaVendaForm
from rodoviaria.forms.compra_rodoviaria_forms import VerificarPoltronaForm
from rodoviaria.forms.staff_forms import SmartbusLoginForm
from rodoviaria.models.core import Cidade, LocalEmbarque, Passagem, TrechoClasse
from rodoviaria.service import class_match_svc, reserva_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC
from rodoviaria.service.exceptions import RodoviariaOverbookingException, RodoviariaUnauthorizedError
from rodoviaria.tests.smartbus.mock_data_response import mock_confirm_booking, mock_trips
from rodoviaria.tests.utils_testes import _comprar_params


def test_atualiza_origens(mock_login, mock_buscar_origens, smartbus_company):
    orchestrator = OrchestrateRodoviaria(smartbus_company.company_internal_id)

    origens = orchestrator.atualiza_origens()

    assert len(origens) == 3
    assert isinstance(origens, list)
    assert all(isinstance(local, Localidade) for local in origens)


def test_buscar_corridas(mock_login, mock_buscar_corridas, smartbus_api):
    request_params = {"origem": 5, "destino": 1, "data": "2023-11-03"}
    corridas = smartbus_api.buscar_corridas(request_params).servicos
    assert len(corridas) == 3
    mock_trips.trips["data"][0]["data_saida"] = "2023-11-03"
    assert corridas[0] == ServicoForm.parse_obj(
        {
            "external_id": mock_trips.trips["data"][0]["controlNumber"],
            "duracao": 22500,
            "preco": D(str(mock_trips.trips["data"][0]["priceValue"])),
            "provider_data": mock_trips.trips["data"][0],
            "external_company_id": mock_trips.trips["data"][0]["idCompany"],
            "has_connection": mock_trips.trips["data"][0]["hasConnection"],
            "vagas": mock_trips.trips["data"][0]["availableSeats"],
            "rota_external_id": mock_trips.trips["data"][0]["idSchedule"],
            "classe": mock_trips.trips["data"][0]["classOfServiceName"],
            "external_datetime_ida": datetime(2023, 11, 3, 11, 30, 0),
            "external_datetime_chegada": datetime(2023, 11, 3, 17, 45, 0),
        }
    )


def test_buscar_corridas_nenhuma_viagem(mock_login, mock_buscar_corridas_sem_servico, smartbus_api):
    request_params = {"origem": 5, "destino": 1, "data": "2023-11-03"}
    corridas = smartbus_api.buscar_corridas(request_params).servicos
    assert corridas == []


def test_buscar_servico(smartbus_api, mock_login, mock_buscar_corridas):
    expected_servico = mock_trips.trips["data"][2]
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(
        datetime.strptime(f"2023-05-10 {expected_servico['departureTime']}", "%Y-%m-%d %H:%M:%S"),
        timezone,
    )
    tipo_assento = class_match_svc.buser_class(expected_servico["classOfServiceName"])
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 1, "destino": 5, "data": datetime_ida.strftime("%Y-%m-%d")}
    servicos_form = smartbus_api.buscar_corridas(request_params, match_params)
    servico_form = servicos_form.servicos[0]
    assert servicos_form.found
    assert servico_form.provider_data["company_external_id"] == expected_servico["idCompany"]
    assert servico_form.preco == D(str(expected_servico["priceValue"]))
    assert servico_form.vagas == expected_servico["availableSeats"]
    assert servico_form.external_id == expected_servico["controlNumber"]


def test_buscar_servico_sem_match(smartbus_api, mock_login, mock_buscar_corridas):
    timezone = "America/Sao_Paulo"
    datetime_ida = to_tz(datetime(2022, 1, 10, 10, 40), timezone)
    tipo_assento = "semi leito"
    match_params = {"datetime_ida": datetime_ida, "timezone": timezone, "tipo_assento": tipo_assento}
    request_params = {"origem": 5, "destino": 10, "data": datetime_ida.strftime("%Y-%m-%d")}
    servicos_form = smartbus_api.buscar_corridas(request_params, match_params)
    expected_servico_proximo = mock_trips.trips["data"][0]
    expected_provider_data = mock_trips.trips["data"][0].copy()
    expected_provider_data["data_saida"] = "2022-01-10"
    assert not servicos_form.found
    assert len(servicos_form.servicos) == 3
    assert servicos_form.servicos[0] == ServicoForm.parse_obj(
        {
            "external_id": expected_servico_proximo["controlNumber"],
            "preco": D(str(expected_servico_proximo["priceValue"])),
            "external_datetime_ida": to_tz(
                datetime.strptime(
                    f"{datetime_ida.strftime('%Y-%m-%d')} {expected_servico_proximo['departureTime']}",
                    "%Y-%m-%d %H:%M:%S",
                ),
                timezone,
            ),
            "classe": expected_servico_proximo["classOfServiceName"],
            "external_company_id": str(expected_servico_proximo["idCompany"]),
            "vagas": expected_servico_proximo["availableSeats"],
            "provider_data": expected_provider_data,
        }
    )


@pytest.fixture
def trecho_classe_mock(smartbus_company):
    trecho_classe_id = 173942
    cidade_origem = baker.make(Cidade, timezone="America/Sao_Paulo")
    origem = baker.make(LocalEmbarque, id_external=382, cidade=cidade_origem)
    destino = baker.make(LocalEmbarque, id_external=983)
    datetime_ida = to_default_tz(datetime(2022, 5, 10, 14, 30))
    yield baker.make(
        TrechoClasse,
        trechoclasse_internal_id=trecho_classe_id,
        external_id="99103704",
        origem=origem,
        destino=destino,
        datetime_ida=datetime_ida,
        preco_rodoviaria=D("130"),
        grupo__company_integracao=smartbus_company,
    )


@pytest.mark.parametrize("mock_bloquear_poltronas", [{"poltronas": [1]}], indirect=True)
def test_verifica_poltrona_uma_poltrona(
    smartbus_api, trecho_classe_mock, mock_login, mock_detalhes_corrida, mock_bloquear_poltronas
):
    poltronas = [1]
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    assert (
        smartbus_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=1))
        == poltronas
    )
    for p in poltronas:
        assert smartbus_api.cache.get_poltrona_key_cache(trecho_classe_id, p)


@pytest.mark.parametrize("mock_bloquear_poltronas", [{"poltronas": [3, 4]}], indirect=True)
def test_verifica_poltrona_duas_poltrona_juntas(
    smartbus_api, trecho_classe_mock, mock_login, mock_detalhes_corrida, mock_bloquear_poltronas
):
    poltronas = [3, 4]
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    assert (
        smartbus_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2))
        == poltronas
    )
    for p in poltronas:
        assert smartbus_api.cache.get_poltrona_key_cache(trecho_classe_id, p)


@pytest.mark.parametrize("mock_bloquear_poltronas", [{"poltronas": [1, 3, 4]}], indirect=True)
def test_verifica_poltrona_duas_poltrona_juntas_e_uma_separada(
    smartbus_api, trecho_classe_mock, mock_login, mock_detalhes_corrida, mock_bloquear_poltronas
):
    poltronas = [1, 3, 4]
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    assert (
        smartbus_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=3))
        == poltronas
    )
    for p in poltronas:
        assert smartbus_api.cache.get_poltrona_key_cache(trecho_classe_id, p)


def test_get_map_poltronas(smartbus_api, trecho_classe_mock, mock_login, mock_detalhes_corrida):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    baker.make(
        Passagem, trechoclasse_integracao=trecho_classe_mock, poltrona_external_id=1, status=Passagem.Status.CONFIRMADA
    )
    map_poltronas = smartbus_api.get_map_poltronas(trecho_classe_id)
    assert map_poltronas == {
        "01": "ocupada",
        "02": "ocupada",
        "03": "livre",
        "04": "livre",
        "05": "ocupada",
    }


def test_verifica_poltrona_overbooking(smartbus_api, trecho_classe_mock, mock_login, mock_detalhes_corrida):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    with pytest.raises(RodoviariaOverbookingException):
        smartbus_api.verifica_poltrona(VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=4))


def test_desbloquear_poltronas(smartbus_api, mock_login, trecho_classe_mock, mock_desbloquear_poltronas):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    poltronas = [3, 4]
    bloqueios = [{"transacao": "transacao_1", "poltrona": 3}, {"transacao": "transacao_2", "poltrona": 4}]
    for b in bloqueios:
        smartbus_api.cache.set_poltrona_key_cache(trecho_classe_id, b["poltrona"], b)
        assert smartbus_api.cache.get_poltrona_key_cache(trecho_classe_id, b["poltrona"])  # teste de sanidade
    smartbus_api.desbloquear_poltronas(trecho_classe_id, poltronas)
    for b in bloqueios:
        assert smartbus_api.cache.get_poltrona_key_cache(trecho_classe_id, b["poltrona"]) is None


@pytest.mark.parametrize("mock_comprar", [{"poltronas": [3, 4]}], indirect=True)
def test_comprar_poltrona_ja_bloqueada(smartbus_api, mock_login, mock_comprar, trecho_classe_mock):
    params = _comprar_params(trecho_classe_mock.trechoclasse_internal_id, 2)
    poltronas = [3, 4]
    params.poltronas = poltronas
    for index, poltrona in enumerate(params.poltronas):
        smartbus_api.cache.set_poltrona_key_cache(
            trecho_classe_mock.trechoclasse_internal_id,
            poltrona,
            {"transacao": f"transactionIdentifier_{index}", "poltrona": poltrona},
        )
    smartbus_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao=trecho_classe_mock).order_by("poltrona_external_id")
    assert len(passagens) == 2
    for index, passagem in enumerate(passagens):
        assert str(passagem.poltrona_external_id) == str(poltronas[index])
        assert passagem.status == Passagem.Status.CONFIRMADA


@pytest.mark.parametrize("mock_bloquear_poltronas", [{"poltronas": [1, 9]}], indirect=True)
@pytest.mark.parametrize("mock_comprar", [{"poltronas": [1, 9]}], indirect=True)
def test_comprar_poltrona_assert_reservations(
    smartbus_api, mock_login, mock_comprar, mock_bloquear_poltronas, trecho_classe_mock
):
    params = _comprar_params(trecho_classe_mock.trechoclasse_internal_id, 2)
    poltronas = [1, 9]
    params.poltronas = poltronas
    smartbus_api.comprar(params)
    request_params = json.loads(mock_comprar.calls[-1].request.body)
    reservations = [(int(p["seatIdentifier"]), p["passengerName"]) for p in request_params["params"]]
    passageiros_map = {p.id: p for p in params.passageiros}
    passagens = Passagem.objects.filter(trechoclasse_integracao=trecho_classe_mock).order_by("poltrona_external_id")
    for p in passagens:
        passageiro = passageiros_map[p.buseiro_internal_id]
        assert (p.poltrona_external_id, passageiro.name) in reservations


def test_comprar_com_erro_na_compra(
    smartbus_api, mock_login, mock_comprar_com_erro, mock_desbloquear_poltronas, trecho_classe_mock
):
    params = _comprar_params(trecho_classe_mock.trechoclasse_internal_id, 2)
    poltronas = [3, 4]
    params.poltronas = poltronas
    bloqueios = [
        {"transacao": "transacao_1", "poltrona": poltronas[0]},
        {"transacao": "transacao_2", "poltrona": poltronas[1]},
    ]
    for b in bloqueios:
        smartbus_api.cache.set_poltrona_key_cache(params.trechoclasse_id, b["poltrona"], b)

    with pytest.raises(SmartbusAPIError, match="Erro ao confirmar reserva! Refaça a reserva e tente novamente!"):
        smartbus_api.comprar(params)

    passagens = Passagem.objects.filter(trechoclasse_integracao=trecho_classe_mock).order_by("poltrona_external_id")
    assert len(passagens) == 2
    for index, passagem in enumerate(passagens):
        assert str(passagem.poltrona_external_id) == str(poltronas[index])
        assert passagem.status == Passagem.Status.ERRO
        assert passagem.erro == "Erro ao confirmar reserva! Refaça a reserva e tente novamente!"
    for b in bloqueios:
        assert smartbus_api.cache.get_poltrona_key_cache(params.trechoclasse_id, b["poltrona"]) is None


@pytest.mark.parametrize("mock_bloquear_poltronas", [{"poltronas": [6, 9]}], indirect=True)
@pytest.mark.parametrize("mock_comprar", [{"poltronas": [6, 9]}], indirect=True)
def test_comprar_bloqueia_poltrona(smartbus_api, mock_login, mock_comprar, mock_bloquear_poltronas, trecho_classe_mock):
    params = _comprar_params(trecho_classe_mock.trechoclasse_internal_id, 2)
    poltronas = [6, 9]
    params.poltronas = poltronas
    smartbus_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao=trecho_classe_mock).order_by("poltrona_external_id")
    assert len(passagens) == 2
    for index, passagem in enumerate(passagens):
        assert str(passagem.poltrona_external_id) == str(poltronas[index])
        assert passagem.status == Passagem.Status.CONFIRMADA


def test_async_desbloquear_poltronas(smartbus_api, mock_login, mock_desbloquear_poltronas, trecho_classe_mock):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    poltronas = [3, 4]
    bloqueios = [{"transacao": "transacao_1", "poltrona": 3}, {"transacao": "transacao_2", "poltrona": 4}]
    for b in bloqueios:
        smartbus_api.cache.set_poltrona_key_cache(trecho_classe_id, b["poltrona"], b)
        assert smartbus_api.cache.get_poltrona_key_cache(trecho_classe_id, b["poltrona"])  # teste de sanidade
    async_desbloquear_poltronas(smartbus_api.company.id, trecho_classe_id, poltronas)
    for b in bloqueios:
        assert smartbus_api.cache.get_poltrona_key_cache(trecho_classe_id, b["poltrona"]) is None


@pytest.fixture
def passagens_para_cancelar():
    travel_id = 31231
    passagens = [
        baker.make(
            Passagem,
            poltrona_external_id=1,
            localizador="transacao_1",
            travel_internal_id=travel_id,
            status=Passagem.Status.CONFIRMADA,
        ),
        baker.make(
            Passagem,
            poltrona_external_id=2,
            localizador="transacao_2",
            travel_internal_id=travel_id,
            status=Passagem.Status.CONFIRMADA,
        ),
    ]
    return passagens


def test_cancela_venda_cancelar_passagem(
    smartbus_api, mock_login, mock_cancelar_passagem, trecho_classe_mock, passagens_para_cancelar, mocker
):
    mocked_now = datetime(2022, 7, 20, 16, 43, tzinfo=ZoneInfo("America/Sao_Paulo"))
    mocker.patch("rodoviaria.models.core.timezone.now", return_value=mocked_now)

    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    travel_id = passagens_para_cancelar[0].travel_internal_id
    cancelar_params = CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": trecho_classe_id})
    smartbus_api.cancela_venda(cancelar_params)
    for p in passagens_para_cancelar:
        p.refresh_from_db()
        assert p.status == Passagem.Status.CANCELADA
        assert p.datetime_cancelamento == mocked_now


@time_machine.travel(datetime(2022, 7, 20, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_cancela_venda_devolver_passagem(
    smartbus_api,
    mock_login,
    mock_cancelar_passagem_error,
    mock_devolver_passagem,
    trecho_classe_mock,
    passagens_para_cancelar,
):
    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    travel_id = passagens_para_cancelar[0].travel_internal_id
    cancelar_params = CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": trecho_classe_id})
    smartbus_api.cancela_venda(cancelar_params)
    for p in passagens_para_cancelar:
        p.refresh_from_db()
        assert p.status == Passagem.Status.CANCELADA
        assert p.datetime_cancelamento == timezone.now()


def test_cancela_venda_error(
    smartbus_api,
    mock_login,
    mock_cancelar_passagem_error,
    mock_devolver_passagem_error,
    trecho_classe_mock,
    passagens_para_cancelar,
    mocker,
):
    mocked_now = datetime(2022, 7, 20, 16, 43, tzinfo=ZoneInfo("America/Sao_Paulo"))
    mocker.patch("rodoviaria.models.core.timezone.now", return_value=mocked_now)

    trecho_classe_id = trecho_classe_mock.trechoclasse_internal_id
    travel_id = passagens_para_cancelar[0].travel_internal_id
    cancelar_params = CancelaVendaForm.parse_obj({"travel_id": travel_id, "trechoclasse_id": trecho_classe_id})

    with pytest.raises(SmartbusAPIError, match="O status atual do bilhete não permite realizar essa operação."):
        smartbus_api.cancela_venda(cancelar_params)

    for p in passagens_para_cancelar:
        p.refresh_from_db()
        assert p.status == Passagem.Status.CONFIRMADA
        assert p.datetime_cancelamento == mocked_now
        assert p.erro_cancelamento == "O status atual do bilhete não permite realizar essa operação."


@pytest.fixture
def mock_fluxo_compra(smartbus_api, smartbus_grupos_mockado):
    grupo_buser_django = smartbus_grupos_mockado.ida.grupo
    grupo_buser_django.company_id = smartbus_api.company.company_internal_id
    trecho_classe_buser_django_infos = smartbus_grupos_mockado.ida.trecho_classe_infos
    trecho_classe_id = 482739
    expected_servico = mock_trips.trips["data"][2]
    timezone = trecho_classe_buser_django_infos.cidade_origem.timezone
    trecho_classe_buser_django_infos.trechoclasse_id = trecho_classe_id
    trecho_classe_buser_django_infos.trecho_datetime_ida = to_tz(
        datetime.strptime(f"2023-05-10 {expected_servico['departureTime']}", "%Y-%m-%d %H:%M:%S"),
        timezone,
    )
    trecho_classe_buser_django_infos.tipo_assento = class_match_svc.buser_class(expected_servico["classOfServiceName"])

    cidade_origem, cidade_destino = baker.make(Cidade, company=smartbus_api.company, _quantity=2, timezone=timezone)
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_origem_id,
        cidade=cidade_origem,
        id_external=2312,
    )
    baker.make(
        LocalEmbarque,
        local_embarque_internal_id=trecho_classe_buser_django_infos.localembarque_destino_id,
        cidade=cidade_destino,
        id_external=93282,
    )

    yield grupo_buser_django, trecho_classe_buser_django_infos, trecho_classe_id


@pytest.mark.parametrize("mock_comprar", [{"poltronas": [3, 4]}], indirect=True)
@pytest.mark.parametrize("mock_bloquear_poltronas", [{"poltronas": [3, 4]}], indirect=True)
def test_compra_fluxo_completo(
    requests_mock,
    smartbus_api,
    mock_login,
    mock_buscar_corridas,
    mock_detalhes_corrida,
    mock_bloquear_poltronas,
    mock_comprar,
    mock_cancelar_passagem,
    mock_fluxo_compra,
    mock_dispara_atualizacao_trecho,
):
    (
        grupo_buser_django,
        trecho_classe_buser_django_infos,
        trecho_classe_id,
    ) = mock_fluxo_compra
    with mock.patch.object(CompraRodoviariaSVC, "_get_internal_grupo", return_value=grupo_buser_django), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
        return_value=trecho_classe_buser_django_infos,
    ):
        params = VerificarPoltronaForm(trechoclasse_id=trecho_classe_id, passageiros=2)
        poltronas = CompraRodoviariaSVC(params).verifica_poltrona(params)
    assert poltronas == [3, 4]
    comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
    comprar_params.poltronas = poltronas
    CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    passagens_compradas = Passagem.objects.filter(trechoclasse_integracao__trechoclasse_internal_id=trecho_classe_id)
    assert passagens_compradas.count() == 2
    for p in passagens_compradas:
        assert p.status == Passagem.Status.CONFIRMADA
        assert p.company_integracao_id == smartbus_api.company.id
    reserva_svc.efetua_cancelamento(
        travel_id=comprar_params.travel_id,
        buseiro_id=passagens_compradas[0].buseiro_internal_id,
    )
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CONFIRMADA
    reserva_svc.efetua_cancelamento(travel_id=comprar_params.travel_id)
    for p in passagens_compradas:
        p.refresh_from_db()
    assert passagens_compradas[0].status == Passagem.Status.CANCELADA
    assert passagens_compradas[1].status == Passagem.Status.CANCELADA
    assert sum([1 for c in requests_mock.calls if "OAuth" in c[0].url]) == 1  # só faz login uma vez


def test_fetch_formas_pagamento(smartbus_api, mock_login, mock_buscar_formas_pagamento):
    login_params = SmartbusLoginForm(
        username=smartbus_api.login.username,
        password=smartbus_api.login.password,
        cliente=smartbus_api.login.cliente,
    )
    formas_pagamento = fetch_formas_pagamento(login_params)
    assert formas_pagamento == [
        {"id": 2, "descricao": "DINHEIRO"},
        {"id": 4, "descricao": "CARTÃO DE CRÉDITO"},
        {"id": 5, "descricao": "CARTÃO MANUAL"},
    ]


def test_fetch_formas_pagamento_login_invalido(smartbus_api, mock_unauthorized_login):
    login_params = SmartbusLoginForm(
        username=smartbus_api.login.username,
        password=smartbus_api.login.password,
        cliente=smartbus_api.login.cliente,
    )
    with pytest.raises(RodoviariaUnauthorizedError):
        fetch_formas_pagamento(login_params)


def test_itinerario(smartbus_api, mock_login, mock_detalhes_rota, trecho_classe_mock):
    itinerario = smartbus_api.itinerario(trecho_classe_mock.external_id, trecho_classe_mock.datetime_ida)
    assert itinerario.parsed.hash == "416cc5c32fb4df7d5043b9b629c1eef4b7"


@pytest.mark.parametrize("mock_comprar", [{"poltronas": [3, 4]}], indirect=True)
def test_salva_provider_da_compra(smartbus_api, mock_login, mock_comprar, trecho_classe_mock):
    params = _comprar_params(trecho_classe_mock.trechoclasse_internal_id, 2)
    poltronas = [3, 4]
    params.poltronas = poltronas
    for index, poltrona in enumerate(params.poltronas):
        smartbus_api.cache.set_poltrona_key_cache(
            trecho_classe_mock.trechoclasse_internal_id,
            poltrona,
            {"transacao": f"transactionIdentifier_{index}", "poltrona": poltrona},
        )
    smartbus_api.comprar(params)
    passagens = Passagem.objects.filter(trechoclasse_integracao=trecho_classe_mock).order_by("poltrona_external_id")
    mock_response_conftest = copy.deepcopy(mock_confirm_booking.compra)
    dict_info_passagem = {}

    # Verifica se comprou duas passagens
    assert len(passagens) == 2
    passagem_um = Passagem.objects.get(numero_passagem=passagens[0].numero_passagem)
    passagem_dois = Passagem.objects.get(numero_passagem=passagens[1].numero_passagem)

    # Verifica se os dados específicos da passagens constam apenas na passagem do pax
    assert passagem_um.provider_data != passagem_dois.provider_data
    lista_de_passagens = mock_response_conftest["data"].pop("data")
    dict_info_passagem[lista_de_passagens[0]["ticketNumber"]] = lista_de_passagens[0]
    dict_info_passagem[lista_de_passagens[1]["ticketNumber"]] = lista_de_passagens[1]
    assert (
        passagem_um.provider_data["reserva"] == dict_info_passagem[passagem_um.provider_data["reserva"]["ticketNumber"]]
    )
    assert (
        passagem_dois.provider_data["reserva"]
        == dict_info_passagem[passagem_dois.provider_data["reserva"]["ticketNumber"]]
    )

    # Verifica se os dados gerais do provider_data são comuns a ambas as passagens
    passagem_um.provider_data.pop("reserva")
    passagem_dois.provider_data.pop("reserva")
    assert passagem_um.provider_data == mock_response_conftest
    assert passagem_dois.provider_data == mock_response_conftest
    assert passagem_um.provider_data == passagem_dois.provider_data

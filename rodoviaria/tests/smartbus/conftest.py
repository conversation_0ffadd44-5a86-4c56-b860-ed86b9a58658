import pytest
import responses

from rodoviaria.api.smartbus import endpoints
from rodoviaria.api.smartbus.api import SmartbusAPI
from rodoviaria.api.smartbus.services import LOGIN_PATH
from rodoviaria.tests.smartbus.mock_data_response import (
    mock_book_seat,
    mock_cancel_booking,
    mock_cancel_ticket,
    mock_confirm_booking,
    mock_form_of_payment,
    mock_locations,
    mock_login_response,
    mock_route_details,
    mock_trip_details,
    mock_trips,
)


def get_request_params(request, key):
    if hasattr(request, "param") and request.param.get(key):
        return request.param[key]
    raise ValueError(
        f"{request.fixturename} espera receber o parametro '{key}' via @pytest.mark.parametrize(..., indirect=True)"
    )


@pytest.fixture
def smartbus_api(smartbus_company, smartbus_login):
    return SmartbusAPI(smartbus_company)


@pytest.fixture
def mock_login(requests_mock, smartbus_api):
    yield requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{LOGIN_PATH}",
        json=mock_login_response.sucesso,
        status=200,
    )


@pytest.fixture
def mock_unauthorized_login(requests_mock, smartbus_api):
    yield requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{LOGIN_PATH}",
        json=mock_login_response.nao_autorizado,
        status=400,
    )


@pytest.fixture
def mock_unauthorized_buscar_corridas(requests_mock, smartbus_api):
    yield requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.BuscarCorridas.path}",
        json=mock_login_response.request_nao_autorizada,
        status=401,
    )


@pytest.fixture
def mock_buscar_origens(requests_mock, smartbus_api):
    yield requests_mock.add(
        responses.GET,
        f"{smartbus_api.url_base}/{endpoints.BuscarOrigens.path}",
        json=mock_locations.locais,
        status=200,
    )


@pytest.fixture
def mock_buscar_corridas(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.BuscarCorridas.path}",
        json=mock_trips.trips,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_buscar_corridas_sem_servico(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.BuscarCorridas.path}",
        json=mock_trips.nenhuma_viagem,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_detalhes_corrida(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.DetalhesCorrida.path}",
        json=mock_trip_details.details,
        status=200,
    )


@pytest.fixture
def mock_detalhes_rota(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.DetalhesRota.path}",
        json=mock_route_details.details,
        status=200,
    )


@pytest.fixture
def mock_bloquear_poltronas(request, requests_mock, smartbus_api):
    poltronas = get_request_params(request, "poltronas")
    response = mock_book_seat.bloqueio.copy()
    for i, p in enumerate(poltronas):
        response["data"][i]["seatIdentifier"] = str(p)
    response["data"] = response["data"][: len(poltronas)]
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.BloqueiaPoltronas.path}",
        json=response,
        status=200,
    )


@pytest.fixture
def mock_desbloquear_poltronas(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.DesbloqueiaPoltronas.path}",
        json=mock_cancel_booking.desbloqueio,
        status=200,
    )


@pytest.fixture
def mock_comprar(request, requests_mock, smartbus_api):
    poltronas = get_request_params(request, "poltronas")
    response = mock_confirm_booking.compra.copy()
    for i, p in enumerate(poltronas):
        response["data"]["data"][i]["seatIdentifier"] = str(p)
    response["data"]["data"] = response["data"]["data"][: len(poltronas)]
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.EfetuarCompra.path}",
        json=response,
        status=200,
    )
    yield requests_mock


@pytest.fixture
def mock_comprar_com_erro(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.EfetuarCompra.path}",
        json=mock_confirm_booking.erro,
        status=200,
    )


@pytest.fixture
def mock_cancelar_passagem(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.CancelaPassagem.path}",
        json=mock_cancel_ticket.cancelamento,
        status=200,
    )


@pytest.fixture
def mock_cancelar_passagem_error(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.CancelaPassagem.path}",
        json=mock_cancel_ticket.erro,
        status=200,
    )


@pytest.fixture
def mock_devolver_passagem(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.DevolvePassagem.path}",
        json=mock_cancel_ticket.cancelamento,
        status=200,
    )


@pytest.fixture
def mock_devolver_passagem_error(requests_mock, smartbus_api):
    requests_mock.add(
        responses.POST,
        f"{smartbus_api.url_base}/{endpoints.DevolvePassagem.path}",
        json=mock_cancel_ticket.erro,
        status=200,
    )


@pytest.fixture
def mock_buscar_formas_pagamento(requests_mock, smartbus_api):
    requests_mock.add(
        responses.GET,
        f"{smartbus_api.url_base}/{endpoints.BuscarFormasPagamento.path}",
        json=mock_form_of_payment.forms_of_payment,
        status=200,
    )

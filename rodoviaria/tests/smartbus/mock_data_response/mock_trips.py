trips = {
    "data": [
        {
            "controlNumber": "99101943",
            "departureTime": "11:30:00",
            "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
            "arrivalTime": "17:45:00",
            "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
            "arrivalDay": 0,
            "idCompany": 1,
            "companyName": "SMART TRAVEL",
            "priceValue": 70.15,
            "prices": [
                {
                    "originalPriceValue": 124.69,
                    "priceValue": 13.62,
                    "passengerType": 3,
                    "passengerTypeName": "IDOSO 100%",
                    "avaliableSeats": 2,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 124.69,
                    "priceValue": 114.58,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 39,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 114.58,
                    "priceValue": 70.15,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 12,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 114.58,
                    "priceValue": 92.37,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 38,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
            ],
            "classOfServiceName": "CONVENCIONAL",
            "bpe": True,
            "hasConnection": False,
            "availableSeats": 41,
            "connectionRoutes": [
                {
                    "controlNumber": "101943",
                    "departureTime": "11:30:00",
                    "arrivalTime": "17:45:00",
                    "arrivalDay": 0,
                    "idCompany": 1,
                    "companyName": "SMART TRAVEL",
                    "classOfServiceName": "CONVENCIONAL",
                    "bpe": True,
                    "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
                    "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
                }
            ],
            "currencyCode": "BRL",
            "duration": "06:15:00",
            "idDailySchedule": 53636,
            "idSchedule": 2,
            "scheduleControlNumber": "990001",
            "controlNumberDailySchedule": "99100005",
        },
        {
            "controlNumber": "99102132",
            "departureTime": "11:30:00",
            "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
            "arrivalTime": "17:45:00",
            "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
            "arrivalDay": 0,
            "idCompany": 1,
            "companyName": "SMART TRAVEL",
            "priceValue": 49.13,
            "prices": [
                {
                    "originalPriceValue": 124.69,
                    "priceValue": 13.62,
                    "passengerType": 3,
                    "passengerTypeName": "IDOSO 100%",
                    "avaliableSeats": 2,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 124.69,
                    "priceValue": 114.58,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 42,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 114.58,
                    "priceValue": 49.13,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 2,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 114.58,
                    "priceValue": 70.15,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 15,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 114.58,
                    "priceValue": 92.37,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 41,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
            ],
            "classOfServiceName": "SEMI LEITO",
            "bpe": True,
            "hasConnection": False,
            "availableSeats": 44,
            "connectionRoutes": [
                {
                    "controlNumber": "102132",
                    "departureTime": "11:30:00",
                    "arrivalTime": "17:45:00",
                    "arrivalDay": 0,
                    "idCompany": 1,
                    "companyName": "SMART TRAVEL",
                    "classOfServiceName": "CONVENCIONAL",
                    "bpe": True,
                    "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
                    "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
                }
            ],
            "currencyCode": "BRL",
            "duration": "06:15:00",
            "idDailySchedule": 53685,
            "idSchedule": 2,
            "scheduleControlNumber": "990001",
            "controlNumberDailySchedule": "99100005",
        },
        {
            "controlNumber": "9995651",
            "departureTime": "08:45:00",
            "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
            "arrivalTime": "15:00:00",
            "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
            "arrivalDay": 0,
            "idCompany": 1,
            "companyName": "SMART TRAVEL",
            "priceValue": 59.25,
            "prices": [
                {
                    "originalPriceValue": 152.96,
                    "priceValue": 13.62,
                    "passengerType": 3,
                    "passengerTypeName": "IDOSO 100%",
                    "avaliableSeats": 2,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 152.96,
                    "priceValue": 142.85,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 46,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 142.85,
                    "priceValue": 59.25,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 5,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 142.85,
                    "priceValue": 87.11,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 19,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
                {
                    "originalPriceValue": 142.85,
                    "priceValue": 114.98,
                    "passengerType": 1,
                    "passengerTypeName": "NORMAL",
                    "avaliableSeats": 46,
                    "promoCodeError": False,
                    "promoCodeMessage": None,
                },
            ],
            "classOfServiceName": "EXECUTIVO",
            "bpe": True,
            "hasConnection": False,
            "availableSeats": 48,
            "connectionRoutes": [
                {
                    "controlNumber": "95651",
                    "departureTime": "08:45:00",
                    "arrivalTime": "15:00:00",
                    "arrivalDay": 0,
                    "idCompany": 1,
                    "companyName": "SMART TRAVEL",
                    "classOfServiceName": "EXECUTIVO",
                    "bpe": True,
                    "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
                    "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
                }
            ],
            "currencyCode": "BRL",
            "duration": "06:15:00",
            "idDailySchedule": 51472,
            "idSchedule": 2,
            "scheduleControlNumber": "990001",
            "controlNumberDailySchedule": "99200055",
        },
    ],
    "success": True,
}

nenhuma_viagem = {"data": None, "success": True}

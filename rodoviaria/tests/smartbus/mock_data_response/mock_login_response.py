sucesso = {
    "access_token": "test_access_token",
    "token_type": "bearer",
    "expires_in": 24 * 60 * 60,
    "name": "BUSER",
    "idGateway": 3008,
    "userName": "buser",
    ".issued": "Thu, 16 Mar 2023 16:46:38 GMT",
    ".expires": "Fri, 15 Mar 2024 16:46:38 GMT",
}


request_nao_autorizada = {"message": "Authorization has been denied for this request."}

nao_autorizado = {"error": "invalid_grant", "error_description": "<PERSON><PERSON><PERSON><PERSON> ou senha inválidos."}

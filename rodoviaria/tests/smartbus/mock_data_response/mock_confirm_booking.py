compra = {
    "data": {
        "recordLocator": "000ND9",
        "pointOfSaleFiscalName": "SMART TRAVEL S/A",
        "pointOfSaleDocument1": "32607069000174",
        "pointOfSaleDocument2": None,
        "pointOfSalePhone": "2125629592",
        "pointOfSaleAddress1": "RUA CEARÁ",
        "pointOfSaleAddress2": "PRAÇA DA BANDEIRA",
        "pointOfSaleAddress3": "145",
        "pointOfSaleCityName": "RIO DE JANEIRO - RJ",
        "pointOfSaleStateCode": "RJ",
        "pointOfSaleZipCode": "20270160",
        "companyFiscalName": "SMART TRAVEL S/A",
        "companyDocument1": "32607069000174",
        "companyDocument2": "",
        "companyPhone": "2125629592",
        "companyAddress1": "RUA CEARÁ",
        "companyAddress2": "PRAÇA DA BANDEIRA",
        "companyAddress3": "145",
        "companyCityName": "RIO DE JANEIRO - RJ",
        "companyStateCode": "RJ",
        "companyZipCode": "20270160",
        "formOfPaymentValue": 140.3,
        "valueToPay": 0.0,
        "totalValue": 140.3,
        "data": [
            {
                "transactionIdentifier": "e8f5dddf-2b2b-47a8-8958-7d99b347a566|69c437f8-0291-41eb-98d8-ffb08814ffd0",
                "ticketNumber": "001411",
                "seatIdentifier": "3",
                "isExtraService": "ORDINÁRIO",
                "classOfServiceName": "CONVENCIONAL",
                "bpeKey": None,
                "departureLocationGrantingAgencyCode": None,
                "arrivalLocationGrantingAgencyCode": "71",
                "monitriipCode": None,
                "isNotFiscal": False,
                "bpeAuthorizationDateTime": "",
                "ticketFormOfPayment": "DINHEIRO",
                "scheduleName": "RIBEIRÃO PRETO X RIO DE JANEIRO",
                "bpeAuthorizationNumber": None,
                "bpeSystemNumber": "",
                "otherTaxes": None,
                "boardingPlatform": "",
                "ticketBoardingCode": "",
                "priceDiscount": 0.0,
                "priceOther": 0.0,
                "priceToll": 3.51,
                "priceMandatoryInsurance": 0.0,
                "priceOptionalInsurance": 0.0,
                "priceFare": 66.64,
                "priceBoardingFee": 0.0,
                "convenienceFee": None,
                "classOfServiceControlNumber": "1",
                "bpeAuthorizationProtocol": None,
                "bpeQrCode": None,
                "bpeSerie": None,
                "discountType": "Tarifa Promocional",
                "surplusValue": 0.0,
                "seatPrice": None,
                "miscTicketNumberSeat": None,
                "currencyCode": "BRL",
                "miscExtras": None,
                "originalPrice": 0.0,
                "promoCodeError": False,
                "promoCodeErrorMessage": None,
                "idbookingCoupon": 22561,
            },
            {
                "transactionIdentifier": "ade28bb2-baaf-416e-a07c-4a9195c49cef|e2138e06-5a30-44d4-8c20-25fdd9098b88",
                "ticketNumber": "001421",
                "seatIdentifier": "4",
                "isExtraService": "ORDINÁRIO",
                "classOfServiceName": "CONVENCIONAL",
                "bpeKey": None,
                "departureLocationGrantingAgencyCode": None,
                "arrivalLocationGrantingAgencyCode": "71",
                "monitriipCode": None,
                "isNotFiscal": False,
                "bpeAuthorizationDateTime": "",
                "ticketFormOfPayment": "DINHEIRO",
                "scheduleName": "RIBEIRÃO PRETO X RIO DE JANEIRO",
                "bpeAuthorizationNumber": None,
                "bpeSystemNumber": "",
                "otherTaxes": None,
                "boardingPlatform": "",
                "ticketBoardingCode": "",
                "priceDiscount": 0.0,
                "priceOther": 0.0,
                "priceToll": 3.51,
                "priceMandatoryInsurance": 0.0,
                "priceOptionalInsurance": 0.0,
                "priceFare": 66.64,
                "priceBoardingFee": 0.0,
                "convenienceFee": None,
                "classOfServiceControlNumber": "1",
                "bpeAuthorizationProtocol": None,
                "bpeQrCode": None,
                "bpeSerie": None,
                "discountType": "Tarifa Promocional",
                "surplusValue": 0.0,
                "seatPrice": None,
                "miscTicketNumberSeat": None,
                "currencyCode": "BRL",
                "miscExtras": None,
                "originalPrice": 0.0,
                "promoCodeError": False,
                "promoCodeErrorMessage": None,
                "idbookingCoupon": 22562,
            },
        ],
    },
    "success": True,
}


erro = {
    "success": False,
    "errorCode": 0,
    "errorName": None,
    "errorDetails": [
        {
            "property": "/externalsale/confirmBooking",
            "message": "Erro ao confirmar reserva! Refaça a reserva e tente novamente!",
        }
    ],
}

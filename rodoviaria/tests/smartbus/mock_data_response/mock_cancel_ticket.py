cancelamento = {
    "data": [
        {
            "departureLocation": 5,
            "arrivalLocation": 1,
            "departureDate": "2023-05-30T00:00:00",
            "controlNumber": "99105690",
            "seatIdentifier": "3",
        },
        {
            "departureLocation": 5,
            "arrivalLocation": 1,
            "departureDate": "2023-05-30T00:00:00",
            "controlNumber": "99105690",
            "seatIdentifier": "4",
        },
    ],
    "success": True,
}

erro = {
    "success": False,
    "errorCode": 80020,
    "errorName": "We were unable to process your request. Please try again later.",
    "errorDetails": [
        {
            "property": "/externalsale/cancelTicket",
            "message": "O status atual do bilhete não permite realizar essa operação.",
        }
    ],
}

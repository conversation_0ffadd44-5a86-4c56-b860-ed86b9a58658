details = {
    "data": {
        "controlNumber": "99103704",
        "departureTime": "11:30:00",
        "arrivalTime": "17:45:00",
        "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
        "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
        "arrivalDay": 0,
        "idCompany": 1,
        "companyName": "SMART TRAVEL",
        "priceValue": 49.13,
        "classOfServiceName": "CONVENCIONAL",
        "serviceType": 0,
        "currencyCode": "BRL",
        "promoCodeErrorMessage": None,
        "promoCodeError": False,
        "routes": [
            {
                "controlNumber": "99103704",
                "departureTime": "11:30:00",
                "arrivalTime": "17:45:00",
                "arrivalDay": 0,
                "idCompany": 1,
                "companyName": "SMART TRAVEL",
                "classOfServiceName": "CONVENCIONAL",
                "duration": "06:15:00",
                "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
                "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
                "priceValue": 49.13,
                "originalPrice": 114.58,
                "promoCodeErrorMessage": None,
                "promoCodeError": False,
                "serviceType": 0,
                "seats": [
                    {
                        "seatIdentifier": "1",
                        "row": 0,
                        "cell": 0,
                        "level": 1,
                        "isUnavailable": False,
                        "pricingIdentifier": None,
                        "seatPrice": None,
                        "isQuota": False,
                        "quotaType": None,
                        "quotaTypeDesc": None,
                        "quotaPassengerType": None,
                        "quotaPassengerTypeDesc": None,
                    },
                    {
                        "seatIdentifier": "2",
                        "row": 0,
                        "cell": 1,
                        "level": 1,
                        "isUnavailable": True,
                        "pricingIdentifier": None,
                        "seatPrice": None,
                        "isQuota": False,
                        "quotaType": None,
                        "quotaTypeDesc": None,
                        "quotaPassengerType": None,
                        "quotaPassengerTypeDesc": None,
                    },
                    {
                        "seatIdentifier": "3",
                        "row": 0,
                        "cell": 4,
                        "level": 1,
                        "isUnavailable": False,
                        "pricingIdentifier": None,
                        "seatPrice": None,
                        "isQuota": False,
                        "quotaType": None,
                        "quotaTypeDesc": None,
                        "quotaPassengerType": None,
                        "quotaPassengerTypeDesc": None,
                    },
                    {
                        "seatIdentifier": "4",
                        "row": 0,
                        "cell": 3,
                        "level": 1,
                        "isUnavailable": False,
                        "pricingIdentifier": None,
                        "seatPrice": None,
                        "isQuota": False,
                        "quotaType": None,
                        "quotaTypeDesc": None,
                        "quotaPassengerType": None,
                        "quotaPassengerTypeDesc": None,
                    },
                    {
                        "seatIdentifier": "5",
                        "row": 1,
                        "cell": 0,
                        "level": 1,
                        "isUnavailable": True,
                        "pricingIdentifier": None,
                        "seatPrice": None,
                        "isQuota": False,
                        "quotaType": None,
                        "quotaTypeDesc": None,
                        "quotaPassengerType": None,
                        "quotaPassengerTypeDesc": None,
                    },
                ],
                "extras": [],
                "currencyCode": "",
                "sections": [
                    {
                        "departureTime": "09:00:00",
                        "arrivalTime": "11:30:00",
                        "duration": "02:30:00",
                        "arrivalDay": 0,
                        "departureLocation": {"id": 4, "code": "RAO", "name": "RIBEIRÃO PRETO - SP"},
                        "arrivalLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
                    },
                    {
                        "departureTime": "11:30:00",
                        "arrivalTime": "12:45:00",
                        "duration": "01:15:00",
                        "arrivalDay": 0,
                        "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
                        "arrivalLocation": {"id": 2, "code": "SAO", "name": "SÃO PAULO - SP"},
                    },
                    {
                        "departureTime": "12:45:00",
                        "arrivalTime": "14:45:00",
                        "duration": "02:00:00",
                        "arrivalDay": 0,
                        "departureLocation": {"id": 2, "code": "SAO", "name": "SÃO PAULO - SP"},
                        "arrivalLocation": {"id": 3, "code": "3", "name": "SÃO JOSÉ DOS CAMPOS - SP"},
                    },
                    {
                        "departureTime": "14:45:00",
                        "arrivalTime": "17:45:00",
                        "duration": "03:00:00",
                        "arrivalDay": 0,
                        "departureLocation": {"id": 3, "code": "3", "name": "SÃO JOSÉ DOS CAMPOS - SP"},
                        "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
                    },
                ],
                "idDailySchedule": 0,
                "idSchedule": 0,
                "scheduleControlNumber": None,
                "controlNumberDailySchedule": None,
            }
        ],
        "duration": "06:15:00",
        "idDailySchedule": 54137,
        "idSchedule": 2,
        "scheduleControlNumber": "990001",
        "controlNumberDailySchedule": "99100005",
    },
    "success": True,
}

details = {
    "data": {
        "controlNumber": "99109849",
        "departureTime": "20:30:00",
        "arrivalTime": "02:45:00",
        "arrivalDay": 1,
        "idCompany": 1,
        "companyName": "SMART TRAVEL",
        "classOfServiceName": "EXECUTIVO",
        "duration": "06:15:00",
        "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
        "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
        "priceValue": 59.25,
        "originalPrice": 142.85,
        "promoCodeErrorMessage": None,
        "promoCodeError": False,
        "serviceType": 0,
        "seats": [
            {
                "seatIdentifier": "1",
                "row": 0,
                "cell": 0,
                "level": 1,
                "isUnavailable": False,
                "pricingIdentifier": None,
                "seatPrice": None,
                "isQuota": False,
                "quotaType": None,
                "quotaTypeDesc": None,
                "quotaPassengerType": None,
                "quotaPassengerTypeDesc": None,
            }
        ],
        "extras": [],
        "currencyCode": "BRL",
        "sections": [
            {
                "departureTime": "18:00:00",
                "arrivalTime": "20:30:00",
                "duration": "02:30:00",
                "arrivalDay": 0,
                "departureLocation": {"id": 4, "code": "RAO", "name": "RIBEIRÃO PRETO - SP"},
                "arrivalLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
            },
            {
                "departureTime": "20:30:00",
                "arrivalTime": "21:45:00",
                "duration": "01:15:00",
                "arrivalDay": 0,
                "departureLocation": {"id": 5, "code": "005", "name": "CAMPINAS - SP"},
                "arrivalLocation": {"id": 2, "code": "SAO", "name": "SÃO PAULO - SP"},
            },
            {
                "departureTime": "21:45:00",
                "arrivalTime": "00:45:00",
                "duration": "03:00:00",
                "arrivalDay": 1,
                "departureLocation": {"id": 2, "code": "SAO", "name": "SÃO PAULO - SP"},
                "arrivalLocation": {"id": 3, "code": "3", "name": "SÃO JOSÉ DOS CAMPOS - SP"},
            },
            {
                "departureTime": "00:45:00",
                "arrivalTime": "03:45:00",
                "duration": "03:00:00",
                "arrivalDay": 1,
                "departureLocation": {"id": 3, "code": "3", "name": "SÃO JOSÉ DOS CAMPOS - SP"},
                "arrivalLocation": {"id": 1, "code": "RIO", "name": "RIO DE JANEIRO - RJ"},
            },
        ],
        "idDailySchedule": 55933,
        "idSchedule": 2,
        "scheduleControlNumber": "990001",
        "controlNumberDailySchedule": "99200015",
    },
    "success": True,
}

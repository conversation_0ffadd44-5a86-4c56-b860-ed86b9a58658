from datetime import datetime

import pytest

from rodoviaria.api.smartbus import endpoints as endpoints
from rodoviaria.api.smartbus import models as models
from rodoviaria.api.smartbus.services import LOGIN_PATH
from rodoviaria.service.exceptions import RodoviariaUnauthorizedError
from rodoviaria.tests.smartbus.mock_data_response import mock_trips


def test_buscar_corridas_unauthorized_retry(
    smartbus_api, requests_mock, mock_login, mock_unauthorized_buscar_corridas, mock_buscar_corridas
):
    origem = 5
    destino = 1
    data = "2023-05-10"
    corridas = endpoints.BuscarCorridas(smartbus_api.login).send(origem, destino, data)
    assert isinstance(corridas.parsed, models.CorridasResponse)
    assert LOGIN_PATH in requests_mock.calls[0][0].url  # primeiro login
    assert endpoints.BuscarCorridas.path in requests_mock.calls[1][0].url  # recebe 401
    assert LOGIN_PATH in requests_mock.calls[2][0].url  # refaz login
    assert endpoints.BuscarCorridas.path in requests_mock.calls[3][0].url  # recebe 200


def test_buscar_corridas_unauthorized_tenta_duas_vezes_apenas(
    smartbus_api, requests_mock, mock_login, mock_unauthorized_buscar_corridas
):
    origem = 5
    destino = 1
    data = "2023-05-10"
    with pytest.raises(RodoviariaUnauthorizedError):
        endpoints.BuscarCorridas(smartbus_api.login).send(origem, destino, data)
    assert LOGIN_PATH in requests_mock.calls[0][0].url  # primeiro login
    assert endpoints.BuscarCorridas.path in requests_mock.calls[1][0].url  # recebe 401
    assert LOGIN_PATH in requests_mock.calls[2][0].url  # refaz login
    assert endpoints.BuscarCorridas.path in requests_mock.calls[3][0].url  # recebe 401


def test_buscar_corridas(smartbus_api, mock_login, mock_buscar_corridas):
    origem = 5
    destino = 1
    data = "2023-05-10"
    corridas = endpoints.BuscarCorridas(smartbus_api.login).send(origem, destino, data)
    assert isinstance(corridas.parsed, models.CorridasResponse)
    assert len(corridas.parsed) == 3
    assert corridas.parsed[0].datetime_ida == datetime(2023, 5, 10, 11, 30, 0)
    assert corridas.parsed[0].datetime_chegada == datetime(2023, 5, 10, 17, 45, 0)
    assert corridas.parsed[0].normalized_dict()["datetime_ida"] == datetime(2023, 5, 10, 11, 30, 0)
    for index, c in enumerate(corridas.parsed):
        assert c.data_saida == data
        assert c.duracao_segundos == 22500
        mock_trips.trips["data"][index]["data_saida"] = data
        assert c.provider_data == mock_trips.trips["data"][index]
    assert mock_buscar_corridas.calls[1][0].headers["Authorization"] == "Bearer test_access_token"


def test_detalhes_corridas(smartbus_api, mock_login, mock_detalhes_corrida):
    origem = 5
    destino = 1
    data = "2023-05-20"
    external_id = "99103704"
    detalhes_corrida = endpoints.DetalhesCorrida(smartbus_api.login).send(origem, destino, data, external_id)
    assert isinstance(detalhes_corrida.parsed, models.DetalhesCorridaResponse)
    assert len(detalhes_corrida.parsed.poltronas) == 5
    assert detalhes_corrida.parsed.poltronas.vagas_disponiveis() == 3
    assert detalhes_corrida.parsed.poltronas.map() == {
        "01": "livre",
        "02": "ocupada",
        "03": "livre",
        "04": "livre",
        "05": "ocupada",
    }


def test_detalhes_rota(smartbus_api, mock_login, mock_detalhes_rota):
    origem = 5
    destino = 1
    data = "2023-05-20"
    external_id = "99103704"
    detalhes_corrida = endpoints.DetalhesRota(smartbus_api.login).send(origem, destino, data, external_id)
    assert isinstance(detalhes_corrida.parsed, models.DetalhesRotaResponse)
    assert len(detalhes_corrida.parsed) == 5
    assert detalhes_corrida.parsed.hash == "416cc5c32fb4df7d5043b9b629c1eef4b7"


@pytest.mark.parametrize("mock_bloquear_poltronas", [{"poltronas": [10, 11]}], indirect=True)
def test_bloqueia_poltronas(smartbus_api, mock_login, mock_bloquear_poltronas):
    origem = 5
    destino = 1
    data = "2023-05-20"
    external_id = "99103704"
    poltronas = [10, 11]
    bloqueio_poltronas = endpoints.BloqueiaPoltronas(smartbus_api.login).send(
        origem, destino, data, external_id, poltronas
    )
    assert all(isinstance(b, models.PoltronaBloqueada) for b in bloqueio_poltronas.parsed)
    assert len(bloqueio_poltronas.parsed) == 2


@pytest.mark.parametrize("mock_comprar", [{"poltronas": [6]}], indirect=True)
def test_efetua_compra(smartbus_api, mock_login, mock_comprar):
    origem = 5
    destino = 1
    data = "2023-05-20"
    external_id = "99103704"
    request_params = {
        "params": [
            {
                "departureLocation": origem,
                "arrivalLocation": destino,
                "departureDate": data,
                "controlNumber": external_id,
                "passengerType": 1,
                "seatIdentifier": 6,
                "passengerName": "passageiro teste",
                "passengerDocument": "123456789",
                "transactionIdentifier": "e8f5dddf-2b2b-47a8-8958-7d99b347a566|69c437f8-0291-41eb-98d8-ffb08814ffd0",
            }
        ],
        "fiscalDocument": "123456789",
        "customerName": "passageiro teste",
        "payments": [{"idFormOfPayment": 2, "value": 70.15}],
    }
    compra = endpoints.EfetuarCompra(smartbus_api.login).send(request_params)
    assert isinstance(compra.parsed, models.CompraResponse)


def test_desbloqueia_poltrona(smartbus_api, mock_login, mock_desbloquear_poltronas):
    origem = 5
    destino = 1
    data = "2023-05-20"
    external_id = "99103704"
    bloqueios = [{"poltrona": 1, "transacao": "transacao_1"}, {"poltrona": 2, "transacao": "transacao_2"}]
    desbloqueio = endpoints.DesbloqueiaPoltronas(smartbus_api.login).send(origem, destino, data, external_id, bloqueios)
    assert all(isinstance(c, models.Cancelamento) for c in desbloqueio.parsed)


def test_cancela_passagem(smartbus_api, mock_login, mock_cancelar_passagem):
    origem = 5
    destino = 1
    data = "2023-05-20"
    external_id = "99103704"
    passagens = [
        {"poltrona_external_id": 1, "localizador": "transacao_1"},
        {"poltrona_external_id": 2, "localizador": "transacao_2"},
    ]
    cancelamento = endpoints.CancelaPassagem(smartbus_api.login).send(origem, destino, data, external_id, passagens)
    assert all(isinstance(c, models.Cancelamento) for c in cancelamento.parsed)


def test_devolve_passagem(smartbus_api, mock_login, mock_devolver_passagem):
    origem = 5
    destino = 1
    data = "2023-05-20"
    external_id = "99103704"
    passagens = [
        {"poltrona_external_id": 1, "localizador": "transacao_1"},
        {"poltrona_external_id": 2, "localizador": "transacao_2"},
    ]
    cancelamento = endpoints.DevolvePassagem(smartbus_api.login).send(origem, destino, data, external_id, passagens)
    assert all(isinstance(c, models.Cancelamento) for c in cancelamento.parsed)


def test_buscar_formas_pagamento(smartbus_api, mock_login, mock_buscar_formas_pagamento):
    formas_pagamento = endpoints.BuscarFormasPagamento(smartbus_api.login).send()
    assert all(isinstance(fg, models.FormaPagamento) for fg in formas_pagamento.parsed)

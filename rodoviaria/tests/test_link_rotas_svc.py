from unittest import mock

from django.core.management import call_command
from model_bakery import baker

from rodoviaria.models.core import Company, Grupo, Rota
from rodoviaria.service import link_rotas_svc


def test_atualizar_id_internal_rotas_empresa(totalbus_grupos, buser_grupos, rota_mock):
    rodoviaria_grupo = totalbus_grupos.ida
    rodoviaria_grupo.grupo_internal_id = buser_grupos.ida.grupo.id
    rodoviaria_grupo.rota = rota_mock
    rodoviaria_grupo.save()

    rota = Rota.objects.get(pk=rota_mock.id)
    assert rota
    assert rota.id_internal is None

    link_rotas_svc.atualizar_id_internal_rotas_empresa(totalbus_grupos.ida.company_integracao.company_internal_id)

    rota = Rota.objects.get(pk=rota_mock.id)
    assert rota
    assert rota.id_internal == buser_grupos.ida.grupo.rota_id


def test__get_grupos_rotas_rodoviaria(totalbus_grupos, buser_grupos, rota_mock):
    rodoviaria_grupo = totalbus_grupos.ida
    rodoviaria_grupo.grupo_internal_id = buser_grupos.ida.grupo.id
    rodoviaria_grupo.rota = rota_mock
    rodoviaria_grupo.save()

    # cria novos grupos pra testar se o select obtem o registro do grupo mais recente
    core_grupo2 = buser_grupos.ida.grupo
    core_grupo2.pk = None
    core_grupo2.save()

    rodoviaria_grupo2 = rodoviaria_grupo
    rodoviaria_grupo2.grupo_internal_id = core_grupo2.id
    rodoviaria_grupo2.pk = None
    rodoviaria_grupo2.save()

    result = link_rotas_svc._get_grupos_rotas_rodoviaria(totalbus_grupos.ida.company_integracao.company_internal_id)

    assert result[0]["grupo_internal_id"] == core_grupo2.id
    assert result[0]["rota"] == rota_mock.id


def test__get_grupos_rotas_rodoviaria_apenas_marketplace():
    company_internal_id = 853
    company_marketplace = baker.make(
        Company,
        company_internal_id=company_internal_id,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    company_hibrido = baker.make(
        Company,
        company_internal_id=company_internal_id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    rota_marketplace = baker.make(Rota, company=company_marketplace)
    rota_hibrido = baker.make(Rota, company=company_hibrido)
    grupo_marketplace = baker.make(
        Grupo,
        grupo_internal_id=4231,
        rota=rota_marketplace,
        company_integracao=company_marketplace,
    )
    baker.make(
        Grupo,
        grupo_internal_id=4231,
        rota=rota_hibrido,
        company_integracao=company_hibrido,
    )

    result = link_rotas_svc._get_grupos_rotas_rodoviaria(company_internal_id)

    assert len(result) == 1
    assert result[0]["grupo_internal_id"] == grupo_marketplace.grupo_internal_id
    assert result[0]["rota"] == rota_marketplace.id


def test_fetch_trechos_vendidos_command():
    baker.make("rodoviaria.Company", company_internal_id=123, features=["active", "itinerario"])
    baker.make("rodoviaria.Company", company_internal_id=124, features=["active", "itinerario"])
    with mock.patch("rodoviaria.service.link_rotas_svc.atualizar_id_internal_rotas_empresa") as link_rotas_svc_mock:
        call_command("link_rotas")
    assert link_rotas_svc_mock.call_count == 2


def test_fetch_trechos_vendidos_command_nao_chama_company_sem_feature_itinerario():
    baker.make("rodoviaria.Company", company_internal_id=123, features=["active"])
    with mock.patch("rodoviaria.service.link_rotas_svc.atualizar_id_internal_rotas_empresa") as link_rotas_svc_mock:
        call_command("link_rotas")
    link_rotas_svc_mock.assert_not_called()


def test_atualizar_id_internal_rota_por_rodoviaria_id():
    rota = baker.make("rodoviaria.Rota")
    assert rota.id_internal is None

    link_rotas_svc.atualizar_id_internal_rota_por_rodoviaria_id(2, rota.id)

    rota.refresh_from_db()
    assert rota.id_internal == 2


def test_atualizar_id_internal_rota_por_rodoviaria_id_nao_existente():
    rota = baker.make("rodoviaria.Rota")
    assert rota.id_internal is None
    link_rotas_svc.atualizar_id_internal_rota_por_rodoviaria_id(2, rota.id + 9909)

    rota2 = Rota.objects.filter(pk=rota.id + 9909).first()
    rota.refresh_from_db()
    assert rota.id_internal is None
    assert rota2 is None


def test_atualizar_id_internal_rota_por_internal_id_antigo():
    rota = baker.make("rodoviaria.Rota", id_internal=444)
    assert rota.id_internal == 444

    link_rotas_svc.atualizar_id_internal_rota_por_internal_id_antigo(rota.id_internal, 2)

    rota.refresh_from_db()
    assert rota.id_internal == 2


def test_atualizar_id_internal_rota_por_internal_id_antigo_2_rotas():
    rota = baker.make("rodoviaria.Rota", id_internal=444)
    rota2 = baker.make("rodoviaria.Rota", id_internal=444)
    qtde = link_rotas_svc.atualizar_id_internal_rota_por_internal_id_antigo(rota.id_internal, 2)
    assert qtde == 2

    rotas_id_internal = list(Rota.objects.filter(id__in=[rota.id, rota2.id]).values_list("id_internal", flat=True))
    assert rotas_id_internal == [2, 2]


def test_atualizar_id_internal_rota_por_internal_id_antigo_nao_existente():
    rota = baker.make("rodoviaria.Rota", id_internal=444)
    assert rota.id_internal == 444

    link_rotas_svc.atualizar_id_internal_rota_por_internal_id_antigo(rota.id_internal + 89, 2)

    rota2 = Rota.objects.filter(id_internal=rota.id_internal + 89).first()
    rota.refresh_from_db()
    assert rota.id_internal == 444
    assert rota2 is None


def test_atualizar_id_internal_rota_por_internal_id_antigo__id_internal_none():
    rota = baker.make("rodoviaria.Rota")
    assert rota.id_internal is None

    qtde = link_rotas_svc.atualizar_id_internal_rota_por_internal_id_antigo(rota.id_internal, 2)

    rota.refresh_from_db()
    assert qtde is None
    assert rota.id_internal is None


def test_get_total_rotas_integradas_apenas_marketplace():
    company_internal_id = 853
    company_marketplace = baker.make(
        Company,
        company_internal_id=company_internal_id,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    company_hibrido = baker.make(
        Company,
        company_internal_id=company_internal_id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    baker.make(Rota, company=company_marketplace)
    baker.make(Rota, company=company_hibrido)
    rotas = link_rotas_svc.get_total_rotas_integradas([company_internal_id])
    assert len(rotas) == 1
    assert rotas[0]["company_internal_id"] == company_internal_id
    assert rotas[0]["total"] == 1
    assert rotas[0]["integrado"] == 0

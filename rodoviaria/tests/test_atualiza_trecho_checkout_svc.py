from datetime import datetime
from unittest import mock

import pytest
from model_bakery import baker
from redis.exceptions import LockNotOwnedError
from requests import HTTPError

from commons.redis import get_key, set_key
from rodoviaria.models.core import Company
from rodoviaria.service import atualiza_trecho_checkout_svc
from rodoviaria.service.status_integracao_svc import StatusIntegracaoSVC
from rodoviaria.views_schemas import AtualizacaoCheckoutAsyncParams


@pytest.fixture(autouse=True)
def clean_cache():
    atualiza_trecho_checkout_svc._atualiza_trecho_api.delete_memoized()
    yield


@pytest.fixture
def company_with_feature():
    company_internal_id = 123
    c = baker.make(
        Company,
        company_internal_id=company_internal_id,
        max_percentual_divergencia=5,
        features=[Company.Feature.ATUALIZAR_PRECO_CHECKOUT],
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    yield c


@pytest.fixture
def atualizacao_async_params(company_with_feature):
    data_obj = {
        "trechos": [
            {"company_id": company_with_feature.company_internal_id, "trecho_classe_id": 74832, "preco_atual": 240},
            {"company_id": company_with_feature.company_internal_id, "trecho_classe_id": 74833, "preco_atual": 250},
        ]
    }
    return AtualizacaoCheckoutAsyncParams.parse_obj(data_obj)


def test_atualiza_trecho_empresa_sem_feature(company_with_feature):
    company_id = company_with_feature.company_internal_id
    trecho_classe_id = 74832
    preco_atual = 240
    company_with_feature.features = []
    company_with_feature.save()
    response = atualiza_trecho_checkout_svc.atualiza_trecho(company_id, trecho_classe_id, preco_atual)
    assert response["atualiza"] is False
    assert not response.get("trecho")
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["mensagem"] == "Empresa sem feature de atualização no checkout"


def test_atualiza_trecho(company_with_feature):
    company_id = company_with_feature.company_internal_id
    trecho_classe_id = 74832
    preco_atual = 240
    trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 240}
    atualiza_status_mock_response = {trecho_classe_id: trecho_integracao}

    with mock.patch.object(StatusIntegracaoSVC, "atualiza", return_value=atualiza_status_mock_response), mock.patch(
        "rodoviaria.service.atualiza_trecho_checkout_svc._atualizacao_checkout_retorno"
    ) as mock_atualizacao_checkout_retorno:
        response = atualiza_trecho_checkout_svc.atualiza_trecho(company_id, trecho_classe_id, preco_atual)
    assert response == mock_atualizacao_checkout_retorno.return_value
    mock_atualizacao_checkout_retorno.assert_called_once_with(
        trecho_classe_id, preco_atual, company_with_feature, trecho_integracao, trecho_integracao
    )


def test_atualiza_trecho_nao_atualiza_sem_feature(company_with_feature):
    trecho_classe_id = 74832
    preco_atual = 240
    company_with_feature.features = []
    company_with_feature.save()
    mock_trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 250}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}

    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )

    assert response["atualiza"] is False
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_nao_atualiza_preco_igual(company_with_feature):
    trecho_classe_id = 74832
    preco_atual = 240
    mock_trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 240}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}

    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )

    assert response["atualiza"] is False
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_nao_atualiza_integracao_nao_encontrada(company_with_feature):
    trecho_classe_id = 74832
    preco_atual = 240
    mock_trecho_integracao = {"status": "integracao_nao_encontrada"}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}

    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )

    assert response["atualiza"] is False
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_nao_atualiza_divergencia_menor_que_max(company_with_feature):
    trecho_classe_id = 74832
    preco_atual = 240
    mock_trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 241}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}
    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )
    assert response["atualiza"] is False
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_nao_atualiza_max_divergencia_nao_cadastrada(
    company_with_feature,
):
    trecho_classe_id = 74832
    preco_atual = 240
    company_with_feature.max_percentual_divergencia = None
    company_with_feature.save()
    mock_trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 250}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}

    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )

    assert response["atualiza"] is True
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_atualiza_max_divergencia_igual_a_zero(company_with_feature):
    trecho_classe_id = 74832
    preco_atual = 240
    company_with_feature.max_percentual_divergencia = 0
    company_with_feature.save()
    mock_trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 241}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}

    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )

    assert response["atualiza"] is True
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_atualiza_valor_diminuiu(company_with_feature):
    trecho_classe_id = 74832
    preco_atual = 240
    mock_trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 220}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}

    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )

    assert response["atualiza"] is True
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_nao_atualiza_valor_diminuiu_menos_que_max(
    company_with_feature,
):
    trecho_classe_id = 74832
    preco_atual = 240
    mock_trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 239}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}

    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )

    assert response["atualiza"] is False
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_atualiza_valor_aumentou_acima_do_max(company_with_feature):
    trecho_classe_id = 74832
    preco_atual = 240
    mock_trecho_integracao = {"status": "integracao_ok", "preco_rodoviaria": 300}
    dados_atualizacao = {"last_update": datetime(2024, 1, 1, 10, 30, 0)}

    response = atualiza_trecho_checkout_svc._atualizacao_checkout_retorno(
        trecho_classe_id, preco_atual, company_with_feature, mock_trecho_integracao, dados_atualizacao
    )

    assert response["atualiza"] is True
    assert response["trecho"] == mock_trecho_integracao
    assert response["trecho_classe_id"] == trecho_classe_id
    assert response["dados_atualizacao"] == dados_atualizacao


def test_atualiza_trecho_atualiza_valor_aumentou_acima_do_max_memoize(
    company_with_feature,
):
    company_id = company_with_feature.company_internal_id
    trecho_classe_id = 74832
    preco_atual = 240
    atualiza_status_mock_response = {trecho_classe_id: {"status": "integracao_ok", "preco_rodoviaria": 300}}

    with mock.patch.object(
        StatusIntegracaoSVC, "atualiza", return_value=atualiza_status_mock_response
    ) as mock_atualiza:
        first_response = atualiza_trecho_checkout_svc.atualiza_trecho(company_id, trecho_classe_id, preco_atual)
        second_response = atualiza_trecho_checkout_svc.atualiza_trecho(company_id, trecho_classe_id, preco_atual)

    assert first_response["atualiza"] is True
    assert first_response["trecho"] == atualiza_status_mock_response[trecho_classe_id]
    assert first_response["trecho_classe_id"] == trecho_classe_id
    assert second_response == first_response
    mock_atualiza.assert_called_once()


def test_atualiza_trecho_async_empresa_sem_feature(company_with_feature, atualizacao_async_params):
    company_with_feature.features = []
    company_with_feature.save()
    response = atualiza_trecho_checkout_svc.atualiza_trecho_async(atualizacao_async_params)
    for r in response:
        assert r["atualizacao_iniciada"] is False
        assert not r.get("trecho")
        assert r["mensagem"] == "Empresa sem feature de atualização no checkout"


def test_atualiza_trecho_async(company_with_feature, atualizacao_async_params):
    with mock.patch("rodoviaria.service.atualiza_trecho_checkout_svc._atualiza_trecho_api") as mock_atualiza_trecho_api:
        mock_atualiza_trecho_api.return_value = {
            t.trecho_classe_id: {"last_update": datetime(2024, 1, 1, 10, 30, 0)}
            for t in atualizacao_async_params.trechos
        }
        response = atualiza_trecho_checkout_svc.atualiza_trecho_async(atualizacao_async_params)
    assert mock_atualiza_trecho_api.call_count == 2
    for r in response:
        assert r["mensagem"] == "Atualizacao async iniciada"
        assert r["atualizacao_iniciada"] is True


def test_atualiza_trecho_async_lock_not_owned(company_with_feature, atualizacao_async_params):
    with mock.patch(
        "rodoviaria.service.atualiza_trecho_checkout_svc._locked_atualiza_trecho_api", side_effect=LockNotOwnedError
    ) as mock_locked_atualiza_trecho_api:
        response = atualiza_trecho_checkout_svc.atualiza_trecho_async(atualizacao_async_params)
    assert mock_locked_atualiza_trecho_api.call_count == 2
    for r in response:
        assert r["mensagem"] == "Atualizacao async iniciada"
        assert r["atualizacao_iniciada"] is True


def test_atualiza_trecho_async_HTTPError(company_with_feature, atualizacao_async_params):
    with mock.patch(
        "rodoviaria.service.atualiza_trecho_checkout_svc._locked_atualiza_trecho_api",
        side_effect=HTTPError("Aqui a mensagem de erro"),
    ) as mock_locked_atualiza_trecho_api:
        response = atualiza_trecho_checkout_svc.atualiza_trecho_async(atualizacao_async_params)
    assert mock_locked_atualiza_trecho_api.call_count == 2
    for r in response:
        assert r["mensagem"] == "Atualizacao async iniciada"
        assert r["atualizacao_iniciada"] is True
    trecho_classe_id_1 = atualizacao_async_params.trechos[0].trecho_classe_id
    trecho_classe_id_2 = atualizacao_async_params.trechos[1].trecho_classe_id
    assert get_key(f"erro_atualizacao_checkout_{trecho_classe_id_1}") == {"error": "Aqui a mensagem de erro"}
    assert get_key(f"erro_atualizacao_checkout_{trecho_classe_id_2}") == {"error": "Aqui a mensagem de erro"}


def test_verifica_atualizacao_task_em_execucao(company_with_feature, atualizacao_async_params):
    trecho_classe_id_1 = atualizacao_async_params.trechos[0].trecho_classe_id
    trecho_classe_id_2 = atualizacao_async_params.trechos[1].trecho_classe_id
    set_key(f"atualizacao_checkout_{trecho_classe_id_1}", "na_fila", timeout=8)
    set_key(f"atualizacao_checkout_{trecho_classe_id_2}", "na_fila", timeout=8)
    response = atualiza_trecho_checkout_svc.verifica_atualizacao(atualizacao_async_params)
    for r in response:
        assert r["atualizacao_finalizada"] is False


def test_verifica_atualizacao(company_with_feature, atualizacao_async_params):
    trecho_classe_id_1 = atualizacao_async_params.trechos[0].trecho_classe_id
    trecho_classe_id_2 = atualizacao_async_params.trechos[1].trecho_classe_id
    atualiza_status_mock_responses = [
        {trecho_classe_id_1: {"status": "integracao_ok", "preco_rodoviaria": 240}},
        {trecho_classe_id_2: {"status": "integracao_ok", "preco_rodoviaria": 260}},
    ]

    with mock.patch.object(StatusIntegracaoSVC, "verifica", side_effect=atualiza_status_mock_responses), mock.patch(
        "rodoviaria.service.atualiza_trecho_checkout_svc._atualizacao_checkout_retorno"
    ) as mock_atualizacao_checkout_retorno:
        response = atualiza_trecho_checkout_svc.verifica_atualizacao(atualizacao_async_params)
    assert response == [mock_atualizacao_checkout_retorno.return_value, mock_atualizacao_checkout_retorno.return_value]


def test_verifica_atualizacao_com_erro(company_with_feature, atualizacao_async_params):
    trecho_classe_id_1 = atualizacao_async_params.trechos[0].trecho_classe_id
    trecho_classe_id_2 = atualizacao_async_params.trechos[1].trecho_classe_id
    set_key(f"erro_atualizacao_checkout_{trecho_classe_id_1}", {"error": "Aqui a mensagem de erro"}, timeout=10)
    set_key(f"erro_atualizacao_checkout_{trecho_classe_id_2}", {"error": "Aqui a mensagem de erro"}, timeout=10)
    response = atualiza_trecho_checkout_svc.verifica_atualizacao(atualizacao_async_params)
    assert response == [
        {"error": "Aqui a mensagem de erro", "trecho_classe_id": trecho_classe_id_1, "atualizacao_finalizada": True},
        {"error": "Aqui a mensagem de erro", "trecho_classe_id": trecho_classe_id_2, "atualizacao_finalizada": True},
    ]

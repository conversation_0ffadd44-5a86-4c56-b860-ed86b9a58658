from unittest import mock

import pytest
from django.core.management import call_command
from model_bakery import baker

from rodoviaria.models.core import Company, LocalEmbarque, Rota, TrechoVendido
from rodoviaria.service import link_trechos_vendidos_svc


@pytest.fixture
def trechos_vendidos_buser_django(paradas_mock_com_id_internal):
    rota = baker.make("core.Rota", id=9999)
    for p in paradas_mock_com_id_internal:
        baker.make("core.LocalEmbarque", id=p.local_embarque_internal_id)

    trechos_vendidos = [
        baker.make(
            "core.TrechoVendido",
            rota=rota,
            origem_id=paradas_mock_com_id_internal[0].local_embarque_internal_id,
            destino_id=paradas_mock_com_id_internal[1].local_embarque_internal_id,
        ),
        baker.make(
            "core.TrechoVendido",
            rota=rota,
            origem_id=paradas_mock_com_id_internal[0].local_embarque_internal_id,
            destino_id=paradas_mock_com_id_internal[2].local_embarque_internal_id,
        ),
    ]
    return trechos_vendidos


@pytest.fixture
def trechos_vendidos_com_rota_e_paradas_linkadas(rota_mock, paradas_mock_com_id_internal):
    rota_mock.id_internal = 9999
    rota_mock.save()

    trechos_vendidos = [
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem=paradas_mock_com_id_internal[0],
            destino=paradas_mock_com_id_internal[1],
            classe="leito cama",
        ),
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem=paradas_mock_com_id_internal[0],
            destino=paradas_mock_com_id_internal[2],
            classe="executivo",
        ),
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem=paradas_mock_com_id_internal[0],
            destino=paradas_mock_com_id_internal[1],
            classe="executivo",
        ),
        baker.make(
            "rodoviaria.TrechoVendido",
            rota=rota_mock,
            origem=paradas_mock_com_id_internal[2],
            destino=paradas_mock_com_id_internal[3],
            classe="executivo",
        ),
    ]
    return trechos_vendidos


def test_atualizar_id_internal_trechos_vendidos_company(
    totalbus_login,
    rota_mock,
    trechos_vendidos_com_rota_e_paradas_linkadas,
    trechos_vendidos_buser_django,
):
    rota_mock.id_internal = 9999
    rota_mock.company = totalbus_login.company
    rota_mock.save()
    assert trechos_vendidos_com_rota_e_paradas_linkadas[0].id_internal is None

    count = link_trechos_vendidos_svc.atualizar_id_internal_trechos_vendidos_company(
        totalbus_login.company.company_internal_id
    )

    trechos_vendidos_com_rota_e_paradas_linkadas[0].refresh_from_db()
    assert trechos_vendidos_com_rota_e_paradas_linkadas[0].id_internal is not None
    assert count == 3


def test__get_trechos_vendidos_company_rodoviaria(totalbus_login):
    company_internal_id = 853
    company_marketplace = baker.make(
        Company,
        company_internal_id=company_internal_id,
        modelo_venda=Company.ModeloVenda.MARKETPLACE,
    )
    company_hibrido = baker.make(
        Company,
        company_internal_id=company_internal_id,
        modelo_venda=Company.ModeloVenda.HIBRIDO,
    )
    rota_marketplace = baker.make(Rota, id_internal=9423, company=company_marketplace)
    rota_hibrido = baker.make(Rota, id_internal=8931, company=company_hibrido)
    origem = baker.make(LocalEmbarque, local_embarque_internal_id=3123)
    destino = baker.make(LocalEmbarque, local_embarque_internal_id=8942)
    trecho_vendido_marketplace = baker.make(TrechoVendido, origem=origem, destino=destino, rota=rota_marketplace)
    baker.make(TrechoVendido, origem=origem, destino=destino, rota=rota_hibrido)
    result = list(link_trechos_vendidos_svc._get_trechos_vendidos_company_rodoviaria(company_internal_id))
    assert result == [trecho_vendido_marketplace]


def test__get_trechos_vendidos_company_rodoviaria_apenas_marketplace(
    totalbus_login, rota_mock, trechos_vendidos_com_rota_e_paradas_linkadas
):
    rota_mock.company = totalbus_login.company
    rota_mock.save()
    result = list(
        link_trechos_vendidos_svc._get_trechos_vendidos_company_rodoviaria(totalbus_login.company.company_internal_id)
    )
    assert len(result) == 4
    assert result[0].rota.id_internal == 9999
    assert result[0].origem.local_embarque_internal_id == 10
    assert result[0].destino.local_embarque_internal_id == 15


def test__get_trechos_vendidos_company_rodoviaria_sem_rotas(totalbus_login):
    result = list(
        link_trechos_vendidos_svc._get_trechos_vendidos_company_rodoviaria(totalbus_login.company.company_internal_id)
    )
    assert len(result) == 0


def test__get_trechos_vendidos_buser_django(
    totalbus_login,
    rota_mock,
    trechos_vendidos_com_rota_e_paradas_linkadas,
    trechos_vendidos_buser_django,
):
    rota_mock.company = totalbus_login.company
    rota_mock.save()
    tv_rodo = link_trechos_vendidos_svc._get_trechos_vendidos_company_rodoviaria(
        totalbus_login.company.company_internal_id
    )
    result = link_trechos_vendidos_svc._get_trechos_vendidos_buser_django(tv_rodo)
    assert len(result) == 2


def test__get_trechos_vendidos_buser_django_sem_trechos_rodoviaria(
    totalbus_login,
    rota_mock,
    trechos_vendidos_com_rota_e_paradas_linkadas,
    trechos_vendidos_buser_django,
):
    tv_rodo = link_trechos_vendidos_svc._get_trechos_vendidos_company_rodoviaria(
        totalbus_login.company.company_internal_id
    )
    result = link_trechos_vendidos_svc._get_trechos_vendidos_buser_django(tv_rodo)
    assert len(result) == 0


def test__get_trechos_vendidos_buser_django_sem_trechos_buser_django(
    totalbus_login, rota_mock, trechos_vendidos_com_rota_e_paradas_linkadas
):
    rota_mock.company = totalbus_login.company
    rota_mock.save()
    tv_rodo = link_trechos_vendidos_svc._get_trechos_vendidos_company_rodoviaria(
        totalbus_login.company.company_internal_id
    )
    result = link_trechos_vendidos_svc._get_trechos_vendidos_buser_django(tv_rodo)
    assert len(result) == 0


def test_fetch_trechos_vendidos_command():
    baker.make("rodoviaria.Company", company_internal_id=123, features=["active", "itinerario"])
    baker.make("rodoviaria.Company", company_internal_id=124, features=["active", "itinerario"])
    with mock.patch(
        "rodoviaria.service.link_trechos_vendidos_svc.atualizar_id_internal_trechos_vendidos_company"
    ) as link_trechos_vendidos_svc_mock:
        call_command("link_trechos_vendidos")
    assert link_trechos_vendidos_svc_mock.call_count == 2


def test_fetch_trechos_vendidos_command_nao_chama_company_sem_feature_itinerario():
    baker.make("rodoviaria.Company", company_internal_id=123, features=["active"])
    with mock.patch(
        "rodoviaria.service.link_trechos_vendidos_svc.atualizar_id_internal_trechos_vendidos_company"
    ) as link_trechos_vendidos_svc_mock:
        call_command("link_trechos_vendidos")
    link_trechos_vendidos_svc_mock.assert_not_called()


def test_atualizar_id_internal_dict_trechos_vendidos(
    trechos_vendidos_com_rota_e_paradas_linkadas,
):
    trechos_vendidos = {
        str(trechos_vendidos_com_rota_e_paradas_linkadas[0].id): 10,
        str(trechos_vendidos_com_rota_e_paradas_linkadas[1].id): 20,
        str(trechos_vendidos_com_rota_e_paradas_linkadas[3].id): 30,
    }
    count = link_trechos_vendidos_svc.atualizar_id_internal_dict_trechos_vendidos(trechos_vendidos)
    tc_ids = [tc.id for tc in trechos_vendidos_com_rota_e_paradas_linkadas]
    ids_internal = list(
        TrechoVendido.objects.filter(id__in=tc_ids).values_list("id_internal", flat=True).order_by("id_internal")
    )
    assert ids_internal == [10, 10, 20, 30]
    assert count == 4


def test_atualizar_id_internal_dict_trechos_vendidos_nao_existente(
    trechos_vendidos_com_rota_e_paradas_linkadas,
):
    trechos_vendidos = {
        "99123": 10,
        "-9121": 20,
    }
    count = link_trechos_vendidos_svc.atualizar_id_internal_dict_trechos_vendidos(trechos_vendidos)

    trechos_vendidos_com_rota_e_paradas_linkadas[0].refresh_from_db()
    assert trechos_vendidos_com_rota_e_paradas_linkadas[0].id_internal is None
    assert count == 0

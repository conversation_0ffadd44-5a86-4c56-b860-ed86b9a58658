from datetime import date, datetime, timedelta
from decimal import Decimal as D
from types import SimpleNamespace

import pytest
import time_machine
from django.utils import timezone
from model_bakery import baker
from zoneinfo import ZoneInfo

from commons.dateutils import to_default_tz, to_tz
from rodoviaria.api.eulabs import models
from rodoviaria.api.eulabs.api import order_itinerario_by_datetime
from rodoviaria.api.ti_sistemas.models import BuscarTrechosVendidosOutput
from rodoviaria.models.core import (
    Checkpoint,
    Company,
    LocalEmbarque,
    Rota,
    Rotina,
    RotinaTrechoVendido,
    TaskStatus,
    TipoAssento,
    TrechoVendido,
)
from rodoviaria.service import atualiza_operacao_utils
from rodoviaria.tests.eulabs.mock_data_response import mock_summary


def test_atualizar_ou_criar_trechos_vendidos():
    origem, destino = baker.make(LocalEmbarque, _quantity=2)
    rota = baker.make(Rota)
    rotina = baker.make(Rotina, rota=rota)
    tipo_assento = baker.make(TipoAssento)
    tv_existente = baker.make(
        TrechoVendido, origem=origem, destino=destino, rota=rota, classe="LEITO", capacidade_classe=15, ativo=False
    )
    rtv_existente = baker.make(
        RotinaTrechoVendido,
        rotina=rotina,
        trecho_vendido=tv_existente,
        datetime_ida_trecho_vendido=to_default_tz(datetime(2024, 10, 4, 10, 40)),
    )
    tv_to_create = TrechoVendido(
        rota=rota,
        origem_id=origem.id,
        destino_id=destino.id,
        classe="SEMI LEITO",
        capacidade_classe=42,
        distancia=10,
        duracao=timedelta(seconds=3600),
        preco=D("100"),
        ativo=True,
        tipo_assento_id=tipo_assento.id,
    )
    rtv_to_create = RotinaTrechoVendido(
        rotina=rotina,
        trecho_vendido=tv_to_create,
        datetime_ida_trecho_vendido=to_default_tz(datetime(2024, 9, 10, 22, 30)),
    )
    tv_to_update = TrechoVendido(
        rota=rota,
        origem_id=origem.id,
        destino_id=destino.id,
        classe=tv_existente.classe,
        capacidade_classe=tv_existente.capacidade_classe,
        distancia=10,
        duracao=timedelta(seconds=3600),
        preco=D("50"),
        ativo=True,
        tipo_assento_id=tipo_assento.id,
        updated_at=to_default_tz(datetime(2024, 1, 10, 12, 0, 0)),
    )
    rtv_to_update = RotinaTrechoVendido(
        rotina=rotina,
        trecho_vendido=tv_to_update,
        datetime_ida_trecho_vendido=to_default_tz(datetime(2024, 9, 10, 22, 30)),
    )
    assert TrechoVendido.objects.filter(rota=rota).count() == 1
    atualiza_operacao_utils.create_or_update_trechos_vendidos(
        rota.id, [tv_to_create, tv_to_update], [rtv_to_create, rtv_to_update]
    )
    assert TrechoVendido.objects.filter(rota=rota).count() == 2
    tv_existente.refresh_from_db()
    assert tv_existente.ativo is True  # atualizou
    assert tv_existente.updated_at == to_default_tz(datetime(2024, 1, 10, 12, 0, 0))
    tv_criado = TrechoVendido.objects.get(
        rota=rota, origem=origem, destino=destino, classe="SEMI LEITO", capacidade_classe=42
    )
    assert tv_criado.ativo is True
    assert RotinaTrechoVendido.objects.get(rotina=rotina, trecho_vendido=tv_criado)
    rtv_existente.refresh_from_db()
    assert rtv_existente.datetime_ida_trecho_vendido == to_default_tz(datetime(2024, 9, 10, 22, 30))


def test_atualizar_ou_criar_trechos_vendidos_por_origem_destino():
    origem, destino = baker.make(LocalEmbarque, _quantity=2)
    rota = baker.make(Rota)
    rotina = baker.make(Rotina, rota=rota)
    tipo_assento = baker.make(TipoAssento)
    tv_existente = baker.make(
        TrechoVendido, origem=origem, destino=destino, rota=rota, classe="LEITO", capacidade_classe=15, ativo=False
    )
    tv_to_create = TrechoVendido(
        rota=rota,
        origem_id=origem.id,
        destino_id=destino.id,
        classe="SEMI LEITO",
        capacidade_classe=42,
        distancia=10,
        duracao=timedelta(seconds=3600),
        preco=D("100"),
        ativo=True,
        tipo_assento_id=tipo_assento.id,
    )
    tv_to_update = TrechoVendido(
        rota=rota,
        origem_id=origem.id,
        destino_id=destino.id,
        classe=tv_existente.classe,
        capacidade_classe=tv_existente.capacidade_classe,
        distancia=10,
        duracao=timedelta(seconds=3600),
        preco=D("50"),
        ativo=True,
        tipo_assento_id=tipo_assento.id,
        updated_at=to_default_tz(datetime(2024, 1, 10, 12, 0, 0)),
    )
    rotina_trecho_vendido_to_create = RotinaTrechoVendido(
        rotina=rotina,
        trecho_vendido=tv_to_update,
        datetime_ida_trecho_vendido=to_default_tz(datetime(2024, 10, 10, 15, 30)),
    )
    assert TrechoVendido.objects.filter(rota=rota).count() == 1
    atualiza_operacao_utils.create_or_update_trechos_vendidos_por_origem_e_destino(
        rota.id,
        [tv_to_create, tv_to_update],
        [rotina_trecho_vendido_to_create],
        origem_id=origem.id,
        destino_id=destino.id,
    )
    assert TrechoVendido.objects.filter(rota=rota).count() == 2
    tv_existente.refresh_from_db()
    assert tv_existente.ativo is True  # atualizou
    assert tv_existente.updated_at == to_default_tz(datetime(2024, 1, 10, 12, 0, 0))
    assert (
        TrechoVendido.objects.get(
            rota=rota, origem=origem, destino=destino, classe="SEMI LEITO", capacidade_classe=42
        ).ativo
        is True
    )
    assert RotinaTrechoVendido.objects.filter(trecho_vendido=tv_to_update, rotina=rotina).exists()


def test_atualizar_ou_criar_trechos_vendidos_nao_duplica_trechos_vendidos_na_criacao():
    origem, destino = baker.make(LocalEmbarque, _quantity=2)
    rota = baker.make(Rota)
    tipo_assento = baker.make(TipoAssento)

    tv_to_update = baker.prepare(
        TrechoVendido,
        rota=rota,
        origem_id=origem.id,
        destino_id=destino.id,
        classe="semi-leito",
        capacidade_classe=42,
        distancia=10,
        duracao=timedelta(seconds=3600),
        preco=D("50"),
        ativo=True,
        tipo_assento_id=tipo_assento.id,
        updated_at=to_default_tz(datetime(2024, 1, 10, 12, 0, 0)),
    )
    assert TrechoVendido.objects.filter(rota=rota).count() == 0
    atualiza_operacao_utils.create_or_update_trechos_vendidos(rota.id, [tv_to_update, tv_to_update], [])
    assert TrechoVendido.objects.filter(rota=rota).count() == 1


def test_atualizar_ou_criar_trechos_vendidos_update_rotina_trecho_vendido():
    company = baker.make(Company)
    response_cleaned = order_itinerario_by_datetime(mock_summary.itinerario)
    itinerario_parsed = models.Itinerario.parse_obj(response_cleaned)

    locais_embarque = [
        baker.make(LocalEmbarque, cidade__company=company, id_external=cp.local.external_local_id)
        for cp in itinerario_parsed
    ]
    rota = baker.make(Rota, company=company)
    rotina = baker.make(Rotina, rota=rota, datetime_ida=datetime.now())
    tv_existente = baker.make(
        TrechoVendido,
        origem=locais_embarque[1],
        destino=locais_embarque[2],
        rota=rota,
        classe="LEITO",
        capacidade_classe=15,
        ativo=False,
    )
    rtv = baker.make(RotinaTrechoVendido, rotina=rotina, trecho_vendido=tv_existente)
    tv_api = BuscarTrechosVendidosOutput(
        id=1,
        origemId=itinerario_parsed[1].local.external_local_id,
        destinoId=itinerario_parsed[2].local.external_local_id,
        vagas=4,
        classe="LEITO",
        capacidade=15,
        preco=D("110"),
    )
    atualiza_operacao_utils.atualizar_ou_criar_trechos_vendidos(company, rotina, [tv_api], itinerario_parsed)
    rtv.refresh_from_db()
    assert rtv.datetime_ida_trecho_vendido == to_default_tz(itinerario_parsed[1].datetime_ida)


def test_atualizar_ou_criar_rotina():
    rota = baker.make(Rota)
    datetime_ida = datetime(2024, 4, 10, 13, 0, 0)
    rotina = atualiza_operacao_utils.atualizar_ou_criar_rotina(rota, datetime_ida, id_viagem=123)
    assert rotina.id_external == 123
    rotina2 = atualiza_operacao_utils.atualizar_ou_criar_rotina(rota, datetime_ida, id_viagem=321)
    rotina.refresh_from_db()
    assert rotina2.pk == rotina.pk
    assert rotina.id_external == "321"


def test_atualizar_ou_criar_rotina_com_timezone_datetime_tz_naive():
    rota = baker.make(Rota)
    baker.make(Checkpoint, rota=rota, idx=0, local__cidade__timezone="America/Manaus")

    datetime_ida = datetime(2024, 4, 10, 13, 0, 0)
    rotina = atualiza_operacao_utils.atualizar_ou_criar_rotina(rota, datetime_ida, id_viagem=321)
    rotina.refresh_from_db()
    assert to_tz(rotina.datetime_ida, "America/Manaus") == to_tz(datetime_ida, "America/Manaus")


def test_atualizar_ou_criar_rotina_com_timezone_datetime_tz_aware():
    rota = baker.make(Rota)
    baker.make(Checkpoint, rota=rota, idx=0, local__cidade__timezone="America/Manaus")

    datetime_ida = datetime(2024, 4, 10, 13, 0, 0)
    datetime_ida = to_tz(datetime_ida, "America/Sao_Paulo")
    rotina = atualiza_operacao_utils.atualizar_ou_criar_rotina(rota, datetime_ida, id_viagem=321)
    rotina.refresh_from_db()
    assert to_tz(rotina.datetime_ida, "America/Manaus") == to_tz(datetime_ida, "America/Manaus")


def test_atualizar_ou_criar_rota_create_checkpoints_and_locais():
    company = baker.make(Company)
    itinerario_parsed = models.Itinerario.parse_obj(mock_summary.itinerario)
    locais_ids = [cp.local.external_local_id for cp in itinerario_parsed]
    itinerario = SimpleNamespace(parsed=itinerario_parsed, cleaned=mock_summary.itinerario)
    rota = atualiza_operacao_utils.atualizar_ou_criar_rota(company.id, itinerario, id_viagem=123, create_locais=True)
    assert LocalEmbarque.objects.filter(cidade__company=company, id_external__in=locais_ids).count() == 5
    assert rota.itinerario.count() == 5


def test_atualizar_ou_criar_rota_update():
    company = baker.make(Company)
    itinerario_parsed = models.Itinerario.parse_obj(mock_summary.itinerario)
    itinerario = SimpleNamespace(parsed=itinerario_parsed, cleaned=mock_summary.itinerario)
    rota = baker.make(Rota, company=company, id_hash=itinerario_parsed.hash, id_external=123)
    atualiza_operacao_utils.atualizar_ou_criar_rota(company.id, itinerario, id_viagem=321)
    rota.refresh_from_db()
    assert rota.id_external == "321"


def test_inativa_rotas_e_rotinas_empresa():
    company = baker.make(Company, company_internal_id=999)
    data_inicial = datetime(2023, 1, 20, 10, 0, 0)
    data_final = datetime(2023, 1, 22, 10, 0, 0)
    rota_com_rotina_na_margem = baker.make(Rota, company=company)
    rotina_dentro_da_margem = baker.make(
        Rotina, rota=rota_com_rotina_na_margem, datetime_ida=datetime(2023, 1, 21, 10, 0, 0)
    )
    trecho_vendido_com_rotina_na_margem = baker.make(TrechoVendido, rota=rota_com_rotina_na_margem)
    rota_com_rotina_fora_da_margem = baker.make(Rota, company=company)
    rotina_fora_da_margem = baker.make(
        Rotina, rota=rota_com_rotina_fora_da_margem, datetime_ida=datetime(2023, 1, 23, 10, 0, 0)
    )
    trecho_vendido_com_rotina_fora_da_margem = baker.make(TrechoVendido, rota=rota_com_rotina_fora_da_margem)

    atualiza_operacao_utils.inativa_rotas_e_rotinas_empresa(company.id, data_inicial, data_final)

    # rota, rotina e trecho_vendido com rotinas dentro da margem devem ser inativados
    rota_com_rotina_na_margem.refresh_from_db()
    rotina_dentro_da_margem.refresh_from_db()
    trecho_vendido_com_rotina_na_margem.refresh_from_db()
    assert rota_com_rotina_na_margem.ativo is False
    assert rotina_dentro_da_margem.ativo is False
    assert trecho_vendido_com_rotina_na_margem.ativo is False

    # rota, rotina e trecho_vendido com rotinas fora da margem não devem ser inativados
    rota_com_rotina_fora_da_margem.refresh_from_db()
    rotina_fora_da_margem.refresh_from_db()
    trecho_vendido_com_rotina_fora_da_margem.refresh_from_db()
    assert rota_com_rotina_fora_da_margem.ativo is True
    assert rotina_fora_da_margem.ativo is True
    assert trecho_vendido_com_rotina_fora_da_margem.ativo is True


def test_pre_inicia_task_status():
    company = baker.make(Company)
    atualiza_operacao_utils.pre_inicia_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company.id)
    assert (
        TaskStatus.objects.get(company=company, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO).status
        == TaskStatus.Status.NOT_STARTED
    )


def test_inicia_task_status():
    company = baker.make(Company)
    atualiza_operacao_utils.inicia_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company.id)
    assert (
        TaskStatus.objects.get(company=company, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO).status
        == TaskStatus.Status.PENDING
    )


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_finaliza_task_status():
    company = baker.make(Company)
    atualiza_operacao_utils.finaliza_task_status(task_name=TaskStatus.Name.DESCOBRIR_OPERACAO, company_id=company.id)
    task = TaskStatus.objects.get(company=company, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO)
    assert task.status == TaskStatus.Status.SUCCESS
    assert task.last_success_at == timezone.now()


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_finisher_descobrir_operacao():
    company = baker.make(Company)
    atualiza_operacao_utils.finisher_descobrir_operacao(company.id)
    task = TaskStatus.objects.get(company=company, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO)
    assert task.status == TaskStatus.Status.SUCCESS
    assert task.last_success_at == timezone.now()


@time_machine.travel(datetime(2022, 10, 19, 16, 43, 34, tzinfo=ZoneInfo("America/Sao_Paulo")), tick=False)
def test_finisher_descobrir_operacao_on_error():
    company = baker.make(Company)
    atualiza_operacao_utils.finisher_descobrir_operacao_on_error(None, None, None, company.id)
    task = TaskStatus.objects.get(company=company, task_name=TaskStatus.Name.DESCOBRIR_OPERACAO)
    assert task.status == TaskStatus.Status.FAILURE
    assert task.updated_at == timezone.now()


@pytest.mark.parametrize(
    "data_inicial,data_final,max_search_range,result",
    [
        (date(2024, 1, 10), date(2024, 1, 20), 30, [(date(2024, 1, 10), date(2024, 1, 20))]),
        (
            date(2024, 1, 10),
            date(2024, 3, 20),
            30,
            [
                (date(2024, 1, 10), date(2024, 2, 9)),
                (date(2024, 2, 10), date(2024, 3, 10)),
                (date(2024, 3, 11), date(2024, 3, 20)),
            ],
        ),
        (
            date(2024, 1, 10),
            date(2024, 3, 10),
            30,
            [
                (date(2024, 1, 10), date(2024, 2, 9)),
                (date(2024, 2, 10), date(2024, 3, 10)),
            ],
        ),
        (
            date(2024, 1, 10),
            date(2024, 3, 10),
            15,
            [
                (date(2024, 1, 10), date(2024, 1, 25)),
                (date(2024, 1, 26), date(2024, 2, 9)),
                (date(2024, 2, 10), date(2024, 2, 24)),
                (date(2024, 2, 25), date(2024, 3, 10)),
            ],
        ),
    ],
)
def test_get_chunked_range_buscas(data_inicial, data_final, max_search_range, result):
    assert atualiza_operacao_utils.get_chunked_range_buscas(data_inicial, data_final, max_search_range) == result

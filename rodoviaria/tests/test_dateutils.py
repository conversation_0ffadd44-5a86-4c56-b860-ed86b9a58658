from datetime import datetime, timedelta

import pytest
from django.conf import settings
from zoneinfo import ZoneInfo

from commons import dateutils

DEFAULT_TIME_ZONE = settings.TIME_ZONE


def test__default_timezone():
    default_timezone = dateutils._default_timezone()
    assert default_timezone.key == DEFAULT_TIME_ZONE


def test_to_default_tz():
    d = datetime.now()
    d_tz = dateutils.to_default_tz(d)
    assert d_tz.tzinfo.key == DEFAULT_TIME_ZONE


def test_to_tz():
    d = datetime.now()
    tz = "America/Cuiaba"
    d_tz = dateutils.to_tz(d, tz)
    assert d_tz.tzinfo.key == tz


def test_timedelta_to_miliseconds():
    td = timedelta(seconds=1)
    td_ml = dateutils.timedelta_to_milliseconds(td)
    assert td_ml == 1000


def test_replace_timezone():
    d = datetime.now()
    tz = "America/Sao_Paulo"
    d_tz = dateutils.to_tz(d, tz)
    d_tz_replaced = dateutils.replace_timezone(d_tz, tz)
    assert d_tz == d_tz_replaced
    assert dateutils.replace_timezone(None, tz) is None
    assert dateutils.replace_timezone(d, None) == d


@pytest.mark.parametrize(
    "orig_datetime",
    [
        datetime.now(tz=ZoneInfo("UTC")),
        datetime.now(tz=ZoneInfo("America/Sao_Paulo")),
    ],
)
@pytest.mark.parametrize("new_tz", ["UTC", "America/Sao_Paulo"])
def test_replace_timezone_tz_igual_e_outro(orig_datetime, new_tz):
    expected = orig_datetime.replace(tzinfo=ZoneInfo(new_tz))
    replaced = dateutils.replace_timezone(orig_datetime, new_tz)
    assert replaced == expected


@pytest.mark.parametrize(
    "orig_datetime",
    [
        datetime.now(tz=ZoneInfo("UTC")),
        datetime.now(tz=ZoneInfo("America/Sao_Paulo")),
    ],
)
def test_replace_timezone_no_tz(orig_datetime):
    expected = orig_datetime.replace(tzinfo=None)
    replaced = dateutils.replace_timezone(orig_datetime, None)
    assert replaced == expected


def test_today_midnight():
    d = dateutils.today_midnight()
    assert d.hour == 0
    assert d.minute == 0
    assert d.second == 0
    assert d.microsecond == 0


def test_midinight():
    d = dateutils.midnight(datetime.now())
    assert d.hour == 0
    assert d.minute == 0
    assert d.second == 0
    assert d.microsecond == 0

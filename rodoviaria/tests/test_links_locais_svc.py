from types import SimpleNamespace
from unittest import mock
from unittest.mock import Mock

import pytest
from model_bakery import baker

from commons.dateutils import to_tz
from rodoviaria.models.core import Checkpoint, Rota
from rodoviaria.service import links_locais_embarque_svc


@pytest.fixture
def locais_embarque():
    company = baker.make("rodoviaria.Company", company_internal_id=9999)
    cidade1 = baker.make("rodoviaria.Cidade", company=company, name="Goiânia", cidade_internal_id=18)
    cidade2 = baker.make("rodoviaria.Cidade", company=company)
    cidade_internal = baker.make("rodoviaria.CidadeInternal", pk=7218, timezone="America/Manaus")
    local1 = baker.make("rodoviaria.LocalEmbarque", id_external=9999, cidade=cidade1)
    local2 = baker.make(
        "rodoviaria.LocalEmbarque",
        id_external=8888,
        cidade=cidade2,
        local_embarque_internal_id=9999,
    )
    yield SimpleNamespace(company=company, local1=local1, local2=local2, cidade_internal=cidade_internal)
    company.delete()
    cidade1.delete()
    cidade2.delete()
    cidade_internal.delete()
    local1.delete()
    local2.delete()


@pytest.fixture
def unlinked_rota(totalbus_locais, totalbus_company):
    rota1 = baker.make("rodoviaria.Rota", company=totalbus_company)
    rota2 = baker.make("rodoviaria.Rota", company=totalbus_company)
    rotina1 = baker.make("rodoviaria.Rotina", rota=rota1)
    rotina2 = baker.make("rodoviaria.Rotina", rota=rota2)
    checkpoint_origem1 = baker.make(
        "rodoviaria.Checkpoint",
        rota=rota1,
        idx=0,
        local=totalbus_locais.origem,
        id_external=totalbus_locais.origem.id_external,
    )
    checkpoint_origem2 = baker.make(
        "rodoviaria.Checkpoint",
        rota=rota2,
        idx=0,
        local=totalbus_locais.origem,
        id_external=totalbus_locais.origem.id_external,
    )
    checkpoint_destino1 = baker.make(
        "rodoviaria.Checkpoint",
        rota=rota1,
        idx=1,
        id_external=totalbus_locais.destino.id_external,
    )
    checkpoint_destino2 = baker.make(
        "rodoviaria.Checkpoint",
        rota=rota2,
        idx=1,
        id_external=totalbus_locais.destino.id_external,
    )
    yield SimpleNamespace(
        rota1=rota1,
        rota2=rota2,
        rotina1=rotina1,
        rotina2=rotina2,
        cp_origem1=checkpoint_origem1,
        cp_origem2=checkpoint_origem2,
        cp_destino1=checkpoint_destino1,
        cp_destino2=checkpoint_origem2,
    )
    rota1.delete()
    rota2.delete()
    rotina1.delete()
    rotina2.delete()
    checkpoint_origem1.delete()
    checkpoint_origem2.delete()
    checkpoint_destino1.delete()
    checkpoint_destino2.delete()


def test_list_links(locais_embarque):
    company = locais_embarque.company
    paginator = SimpleNamespace(descending=False, page=1, rows_per_page=50, sortBy=["nickname"])
    params = SimpleNamespace(
        search=None,
        empresa_id_filter=company.id,
        order_by=["-id"],
        has_pagination=False,
        associado_rota=None,
        linkado_buser=None,
    )
    links = links_locais_embarque_svc.list_filtered_locais(params)["items"]
    assert len(links) == 2
    assert links[0]["id"] > links[1]["id"]
    for link in links:
        assert "localidade_external" in link
        assert "local_embarque_buser" in link
        assert "buser_cidade_id" in link
        assert link["empresa_id"] == company.company_internal_id
        assert link["empresa_rodoviaria_id"] == company.id
    params = SimpleNamespace(
        search="Goiânia",
        empresa_id_filter=company.id,
        order_by=["-id"],
        has_pagination=True,
        paginator=paginator,
        associado_rota=None,
        linkado_buser=None,
    )
    links = links_locais_embarque_svc.list_filtered_locais(params)["items"]
    assert len(links) == 1
    assert "Goiânia" in links[0].get("cidade_external")


def test_list_links_associado_rota(locais_embarque):
    rota = baker.make(Rota, ativo=False)
    baker.make(
        "rodoviaria.Checkpoint",
        idx=0,
        rota=rota,
        local=locais_embarque.local1,
    )
    rota2 = baker.make(Rota, ativo=False)
    baker.make(
        "rodoviaria.Checkpoint",
        idx=0,
        rota=rota2,
        local=locais_embarque.local1,
    )
    params = SimpleNamespace(
        search=None,
        empresa_id_filter=locais_embarque.company.id,
        order_by=["-id"],
        has_pagination=False,
        associado_rota=True,
        linkado_buser=None,
    )
    links = links_locais_embarque_svc.list_filtered_locais(params)["items"]
    assert len(links) == 0

    rota.ativo = True
    rota.save()
    rota2.ativo = True
    rota2.save()
    links = links_locais_embarque_svc.list_filtered_locais(params)["items"]

    assert len(links) == 1
    assert links[0]["id"] == locais_embarque.local1.id


def test_list_links_linkado_buser(locais_embarque):
    params = SimpleNamespace(
        search=None,
        empresa_id_filter=locais_embarque.company.id,
        order_by=["-id"],
        has_pagination=False,
        associado_rota=None,
        linkado_buser=True,
    )
    links = links_locais_embarque_svc.list_filtered_locais(params)["items"]
    assert len(links) == 1
    assert links[0]["id"] == locais_embarque.local2.id


def test_update_link(locais_embarque):
    unlinked_local_id = 9876
    unlinked_city_internal_id = 7218

    local = locais_embarque.local1
    assert local.local_embarque_internal_id != unlinked_local_id
    assert local.cidade.cidade_internal_id != unlinked_city_internal_id

    update = links_locais_embarque_svc.update_link_local_embarque(
        unlinked_local_id, local.id, unlinked_city_internal_id
    )
    assert update is True
    local.refresh_from_db()
    assert local.local_embarque_internal_id == unlinked_local_id
    assert local.cidade.cidade_internal_id == unlinked_city_internal_id
    assert local.cidade.timezone == locais_embarque.cidade_internal.timezone


def test_erro_local_embarque():
    update = links_locais_embarque_svc.update_link_local_embarque(None, None)
    assert "error" in update
    assert update["error"] == "Erro ao encontrar local de embarque do rodoviaria"


def test_delete_link_local_embarque(locais_embarque):
    local = locais_embarque.local2
    assert local.local_embarque_internal_id is not None
    update = links_locais_embarque_svc.update_link_local_embarque(None, local.id)
    assert update is True
    local.refresh_from_db()
    assert local.local_embarque_internal_id is None


def test_link_local_existente(locais_embarque):
    local = locais_embarque.local1
    local.local_embarque_internal_id = 6666
    local.save()
    assert local.local_embarque_internal_id is not None
    update = links_locais_embarque_svc.update_link_local_embarque(
        local_internal_id=local.local_embarque_internal_id, local_embarque_id=local.id
    )
    assert update.get("error") == f"Link já existente para esta empresa. ID = {local.id}"


def test_update_link_local_embarque_cidade_error(locais_embarque):
    local = locais_embarque.local1
    city_pk_nao_existente = 99999

    assert local.cidade.cidade_internal_id != city_pk_nao_existente

    update = links_locais_embarque_svc.update_link_local_embarque(
        local_internal_id=local.local_embarque_internal_id,
        local_embarque_id=local.id,
        cidade_internal_id=city_pk_nao_existente,
    )

    assert update["error"] == f"Cidade Interna pk = {city_pk_nao_existente} não encontrada"


def test_update_checkpoints_por_local(totalbus_company, totalbus_locais, unlinked_rota):
    link = totalbus_locais.origem
    checkpoint_origem1 = unlinked_rota.cp_origem1
    checkpoint_origem2 = unlinked_rota.cp_origem2
    checkpoint_origem1.local = None
    checkpoint_origem2.local = None
    checkpoint_origem1.save()
    checkpoint_origem2.save()
    old_tz = None

    with mock.patch(
        "rodoviaria.service.links_locais_embarque_svc._corrige_timezones_rotinas_por_local"
    ) as mock_corrige_tz_por_local:
        mock_corrige_tz_por_local.return_value = True
        update = links_locais_embarque_svc.update_checkpoints_por_local(link, old_tz)

    assert update is True
    checkpoint_origem1.refresh_from_db()
    checkpoint_origem2.refresh_from_db()
    assert checkpoint_origem1.local == link
    assert checkpoint_origem2.local == link


def test_corrige_timezones_rotinas_por_local(unlinked_rota, totalbus_locais, totalbus_company):
    def get_first_cp(rota_id, idx):
        cps = [unlinked_rota.cp_origem1, unlinked_rota.cp_origem2]
        primeiro_cp = filter(lambda cp: cp.rota_id == rota_id, cps)
        return list(primeiro_cp)[0]

    rota1 = unlinked_rota.rota1.pk
    rota2 = unlinked_rota.rota1.pk
    checkpoint_origem1 = unlinked_rota.cp_origem1
    checkpoint_origem2 = unlinked_rota.cp_origem2
    rotina1 = unlinked_rota.rotina1
    rotina2 = unlinked_rota.rotina2
    dt_ida1 = rotina1.datetime_ida
    dt_ida2 = rotina2.datetime_ida
    local = totalbus_locais.origem
    old_tz = "America/Sao_Paulo"

    checkpoints_comuns = Mock(Checkpoint.objects)
    checkpoints_comuns.values_list.return_value = [rota1, rota2]
    checkpoints_comuns.get.side_effect = get_first_cp

    update = links_locais_embarque_svc._corrige_timezones_rotinas_por_local(local, checkpoints_comuns, old_tz)

    assert update is True
    checkpoint_origem1.refresh_from_db()
    checkpoint_origem2.refresh_from_db()
    new_tz1 = checkpoint_origem1.local.cidade.timezone
    new_tz2 = checkpoint_origem2.local.cidade.timezone
    assert new_tz1 == local.cidade.timezone
    assert new_tz2 == local.cidade.timezone
    rotina1.refresh_from_db()
    rotina2.refresh_from_db()
    assert to_tz(rotina1.datetime_ida, new_tz1) == dt_ida1
    assert to_tz(rotina2.datetime_ida, new_tz2) == dt_ida2


def test_fluxo_completo_link_local_update_checkpoints_corrige_tz_rotinas(
    totalbus_company,
    totalbus_locais,
    buser_locais,
    totalbus_cidades,
    cidades_internal,
    unlinked_rota,
):
    local = totalbus_locais.origem
    local.local_embarque_internal_id = None
    local.save()
    checkpoint_origem1 = unlinked_rota.cp_origem1
    checkpoint_origem2 = unlinked_rota.cp_origem2
    checkpoint_origem1.local = None
    checkpoint_origem2.local = None
    checkpoint_origem1.save()
    checkpoint_origem2.save()
    rotina1 = unlinked_rota.rotina1
    rotina2 = unlinked_rota.rotina2
    dt_ida1 = rotina1.datetime_ida
    dt_ida2 = rotina2.datetime_ida

    update = links_locais_embarque_svc.update_link_local_embarque(
        local_internal_id=buser_locais.local_go_terminal.id,
        local_embarque_id=local.id,
        cidade_internal_id=totalbus_cidades.cidade_goiania.cidade_internal_id,
    )
    # Check se o checkpoint foi linkado
    assert update is True
    checkpoint_origem1.refresh_from_db()
    checkpoint_origem2.refresh_from_db()
    assert checkpoint_origem1.local == local
    assert checkpoint_origem2.local == local
    # Check se o tz das partidas foram corrigidas corretamente
    new_tz1 = checkpoint_origem1.local.cidade.timezone
    new_tz2 = checkpoint_origem2.local.cidade.timezone
    assert new_tz1 == local.cidade.timezone
    assert new_tz2 == local.cidade.timezone
    rotina1.refresh_from_db()
    rotina2.refresh_from_db()
    assert to_tz(rotina1.datetime_ida, new_tz1) == dt_ida1
    assert to_tz(rotina2.datetime_ida, new_tz2) == dt_ida2

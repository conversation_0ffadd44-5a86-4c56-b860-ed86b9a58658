import json
from unittest import mock

from model_bakery import baker

from rodoviaria.api.vexado.api import VexadoAPI
from rodoviaria.service import alterar_grupo_svc
from rodoviaria.service.cadastrar_grupos_hibridos_svc import CadastrarGrupoSVC
from rodoviaria.views_schemas import AlterarGrupoParams


def test_alterar_grupo_task(alterar_grupo_request):
    with mock.patch("rodoviaria.service.alterar_grupo_svc.alterar_grupo") as mock_alterar_grupo_task:
        alterar_grupo_svc.alterar_grupo_task(json.dumps(alterar_grupo_request))
    mock_alterar_grupo_task.assert_called_once_with(AlterarGrupoParams.parse_obj(alterar_grupo_request))


def test_alterar_onibus(alterar_grupo_request):
    params = AlterarGrupoParams.parse_obj(alterar_grupo_request)
    vexado_grupo_classe_cancelado = baker.make(
        "rodoviaria.VexadoGrupoClasse",
        rota_external_id=1425,
        grupo_classe_external_id=8372,
    )
    trecho_classe = baker.make("rodoviaria.TrechoClasse", active=True)

    with mock.patch(
        "rodoviaria.service.cancelar_grupos_hibridos_svc.cancelar_grupos_classe"
    ) as mock_cancelar_grupos_classe, mock.patch.object(
        CadastrarGrupoSVC, "cadastrar_grupos"
    ) as mock_cadastrar_grupos, mock.patch.object(
        VexadoAPI, "inativar_grupo_classe"
    ) as mock_inativar_grupo_classe, mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.__init__", return_value=None
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.CompraRodoviariaSVC.add_multiple_pax_na_lista_passageiros_viagem"
    ) as mock_add_multiple_pax_na_lista:
        mock_cancelar_grupos_classe.return_value = [vexado_grupo_classe_cancelado], [trecho_classe.id]
        alterar_grupo_svc.alterar_grupo(params)

    mock_cancelar_grupos_classe.assert_called_once_with(
        params.company_id, params.grupos_classe_ids_antigos, call_task_cancelar=False
    )
    mock_inativar_grupo_classe.assert_called_once_with(8372)
    mock_cadastrar_grupos.assert_called_once_with(
        [params.grupo], rota_external_id=vexado_grupo_classe_cancelado.rota_external_id
    )
    trecho_classe.refresh_from_db()
    assert trecho_classe.active is False
    mock_add_multiple_pax_na_lista.assert_not_called()


def test_alterar_grupo_nao_criado(alterar_grupo_request):
    params = AlterarGrupoParams.parse_obj(alterar_grupo_request)
    response = alterar_grupo_svc.alterar_grupo(params)
    assert response is None

from datetime import datetime, <PERSON><PERSON><PERSON>
from types import SimpleNamespace
from unittest import mock

import pytest
from django.utils.timezone import now
from model_bakery import baker

from commons.celery_utils import DefaultQueueNames
from commons.dateutils import to_default_tz
from rodoviaria.api.eulabs.api import EulabsAPI
from rodoviaria.api.orchestrator import OrchestrateRodoviaria
from rodoviaria.models.core import Grupo, LocalEmbarque, Passagem, TrechoClasse
from rodoviaria.service import compra_rodoviaria_svc
from rodoviaria.service.compra_rodoviaria_svc import CompraRodoviariaSVC, DefaultForm
from rodoviaria.service.exceptions import RodoviariaException
from rodoviaria.tests.utils_testes import _comprar_params


def test_nao_aceita_grupo_nao_cadastrado(praxio_company):
    with pytest.raises(RodoviariaException) as exc:
        compra_rodoviaria_svc.CompraRodoviariaSVC(DefaultForm(trechoclasse_id=1, travel_id=2))
    assert "Marketplace group is not registered in the buser_rodoviaria database" in str(exc.value)


def test_refaz_trecho_classe_sem_origem(praxio_company, praxio_trechoclasses):
    trecho_classe = praxio_trechoclasses.ida
    trecho_classe.origem = None
    trecho_classe.save()
    with mock.patch.object(CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria") as mock_tenta_criar_grupo:
        grupo = baker.make("rodoviaria.Grupo", company_integracao=praxio_company)
        mock_tenta_criar_grupo.return_value = grupo
        expected_form = DefaultForm(trechoclasse_id=trecho_classe.trechoclasse_internal_id, travel_id=2)
        compra_rodoviaria_svc.CompraRodoviariaSVC(expected_form)
    mock_tenta_criar_grupo.assert_called_once()


def test_aceita_grupo_cadastrado(praxio_trechoclasses):
    compra_rodoviaria = compra_rodoviaria_svc.CompraRodoviariaSVC(
        DefaultForm(trechoclasse_id=praxio_trechoclasses.ida.trechoclasse_internal_id, travel_id=2)
    )
    assert compra_rodoviaria.company_id == compra_rodoviaria.grupo.company_integracao.company_internal_id


def test_add_multiple_pax_na_lista_passageiros_viagem(vexado_company):
    origem = baker.make("rodoviaria.LocalEmbarque", cidade=baker.make("rodoviaria.Cidade"))
    destino = baker.make("rodoviaria.LocalEmbarque", cidade=baker.make("rodoviaria.Cidade"))
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=8392, company_integracao=vexado_company)
    datetime_now = to_default_tz(datetime(2021, 12, 30, 16, 30))
    baker.make(
        "rodoviaria.TrechoClasse",
        trechoclasse_internal_id=10,
        grupo=grupo,
        origem=origem,
        destino=destino,
        datetime_ida=datetime_now + timedelta(days=10),
    )
    with mock.patch("rodoviaria.service.compra_rodoviaria_svc.now") as now_mock, mock.patch.object(
        OrchestrateRodoviaria, "add_multiple_pax_na_lista_passageiros_viagem"
    ) as mock_add_multiple_pax_na_lista_passageiros_viagem:
        now_mock.return_value = to_default_tz(datetime_now)
        params = SimpleNamespace(
            trechoclasse_id=10, travels=[SimpleNamespace(travel_id=2, buseiros=[SimpleNamespace(id=1)])]
        )
        compra_rodoviaria_svc.CompraRodoviariaSVC(
            DefaultForm(trechoclasse_id=10)
        ).add_multiple_pax_na_lista_passageiros_viagem(params)
    mock_add_multiple_pax_na_lista_passageiros_viagem.assert_called_once_with(params)


def test_atualiza_link_trecho_classe_atualizado_ha_mais_de_5_minutos(totalbus_company):
    mock_trechoclasse_para_teste_5_minutos(totalbus_company, now() - timedelta(minutes=6))

    with mock.patch.object(CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria") as _tenta_criar_grupo_rodoviaria_mock:
        compra_rodoviaria_svc.CompraRodoviariaSVC(DefaultForm(trechoclasse_id=10))
    assert _tenta_criar_grupo_rodoviaria_mock.called


def test_atualiza_link_trecho_classe_force_renew_link(totalbus_company):
    mock_trechoclasse_para_teste_5_minutos(totalbus_company, now() - timedelta(minutes=6))

    with mock.patch.object(CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria") as _tenta_criar_grupo_rodoviaria_mock:
        compra_rodoviaria_svc.CompraRodoviariaSVC(DefaultForm(trechoclasse_id=10, force_renew_link=True))
    assert _tenta_criar_grupo_rodoviaria_mock.called


def test_nao_atualiza_link_trecho_classe_atualizado_ha_menos_de_5_minutos(
    totalbus_company,
):
    mock_trechoclasse_para_teste_5_minutos(totalbus_company, now())

    with mock.patch.object(CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria") as _tenta_criar_grupo_rodoviaria_mock:
        compra_rodoviaria_svc.CompraRodoviariaSVC(DefaultForm(trechoclasse_id=10))
    assert not _tenta_criar_grupo_rodoviaria_mock.called


def test_nao_atualiza_link_trecho_classe_hibrido(vexado_company):
    mock_trechoclasse_para_teste_5_minutos(vexado_company, now())

    with mock.patch.object(CompraRodoviariaSVC, "_tenta_criar_grupo_rodoviaria") as _tenta_criar_grupo_rodoviaria_mock:
        compra_rodoviaria_svc.CompraRodoviariaSVC(DefaultForm(trechoclasse_id=10))
    assert not _tenta_criar_grupo_rodoviaria_mock.called


def mock_trechoclasse_para_teste_5_minutos(company, updated_at):
    origem = baker.make("rodoviaria.LocalEmbarque", cidade=baker.make("rodoviaria.Cidade"))
    destino = baker.make("rodoviaria.LocalEmbarque", cidade=baker.make("rodoviaria.Cidade"))
    grupo = baker.make("rodoviaria.Grupo", grupo_internal_id=8392, company_integracao=company)
    with mock.patch("django.utils.timezone.now", return_value=updated_at):
        baker.make(
            "rodoviaria.TrechoClasse",
            trechoclasse_internal_id=10,
            grupo=grupo,
            origem=origem,
            destino=destino,
        )


def test_efetua_compra_dispara_task_trecho_classe_criado_na_compra(eulabs_grupos_mockado, eulabs_company, eulabs_login):
    trecho_classe_buser_django_infos = eulabs_grupos_mockado.ida.trecho_classe_infos
    grupo_rodoviaria = baker.make(Grupo, company_integracao=eulabs_company)
    with mock.patch.object(
        CompraRodoviariaSVC,
        "_get_internal_grupo",
        return_value=eulabs_grupos_mockado.ida.grupo,
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
        return_value=trecho_classe_buser_django_infos,
    ) as mock_get_trechoclasse_from_buser_django, mock.patch.object(
        EulabsAPI, "comprar"
    ) as mock_comprar_marketplace, mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.GrupoTrechoClasseFactory"
    ) as mock_factory, mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.update_trecho_classe_link_task"
    ) as mock_update_trecho_classe_link_task:
        mock_factory.return_value.create.return_value.grupo = grupo_rodoviaria
        trecho_classe_id = 482739
        comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
        CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    mock_comprar_marketplace.assert_called_once()
    mock_get_trechoclasse_from_buser_django.assert_called_once_with(trecho_classe_id)
    mock_update_trecho_classe_link_task.s.assert_called_once_with(
        eulabs_company.id, trecho_classe_buser_django_infos.json()
    )
    mock_update_trecho_classe_link_task.s.return_value.set.assert_called_once_with(
        queue=DefaultQueueNames.POS_COMPRA_UPDATE_PRICE
    )
    mock_update_trecho_classe_link_task.s.return_value.set.return_value.apply_async.assert_called_once_with()


def test_efetua_compra_dispara_task_trecho_classe_ja_existente(eulabs_grupos_mockado, eulabs_company, eulabs_login):
    trecho_classe_id = 482739
    trecho_classe_buser_django_infos = eulabs_grupos_mockado.ida.trecho_classe_infos
    baker.make(
        TrechoClasse,
        grupo__company_integracao=eulabs_company,
        trechoclasse_internal_id=trecho_classe_id,
        active=True,
        origem=baker.make(LocalEmbarque),
        destino=baker.make(LocalEmbarque),
    )
    with mock.patch.object(
        CompraRodoviariaSVC,
        "_get_internal_grupo",
        return_value=eulabs_grupos_mockado.ida.grupo,
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
        return_value=trecho_classe_buser_django_infos,
    ) as mock_get_trechoclasse_from_buser_django, mock.patch.object(
        EulabsAPI, "comprar"
    ) as mock_comprar_marketplace, mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.update_trecho_classe_link_task"
    ) as mock_update_trecho_classe_link_task:
        comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)
        CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)
    mock_comprar_marketplace.assert_called_once()
    mock_get_trechoclasse_from_buser_django.assert_called_once_with(trecho_classe_id)
    mock_update_trecho_classe_link_task.s.assert_called_once_with(
        eulabs_company.id, trecho_classe_buser_django_infos.json()
    )
    mock_update_trecho_classe_link_task.s.return_value.set.assert_called_once_with(
        queue=DefaultQueueNames.POS_COMPRA_UPDATE_PRICE
    )
    mock_update_trecho_classe_link_task.s.return_value.set.return_value.apply_async.assert_called_once_with()


def test_efetua_compra_passagem_ja_existe(eulabs_grupos_mockado, eulabs_company, eulabs_login):
    trecho_classe_id = 482739
    trecho_classe_buser_django_infos = eulabs_grupos_mockado.ida.trecho_classe_infos
    baker.make(
        TrechoClasse,
        grupo__company_integracao=eulabs_company,
        trechoclasse_internal_id=trecho_classe_id,
        active=True,
        origem=baker.make(LocalEmbarque),
        destino=baker.make(LocalEmbarque),
    )
    with mock.patch.object(
        CompraRodoviariaSVC,
        "_get_internal_grupo",
        return_value=eulabs_grupos_mockado.ida.grupo,
    ), mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.get_trechoclasse_from_buser_django",
        return_value=trecho_classe_buser_django_infos,
    ) as mock_get_trechoclasse_from_buser_django, mock.patch.object(
        EulabsAPI, "comprar"
    ) as mock_comprar_marketplace, mock.patch(
        "rodoviaria.service.compra_rodoviaria_svc.update_trecho_classe_link_task"
    ) as mock_update_trecho_classe_link_task:
        comprar_params = _comprar_params(trecho_classe_id, quantidade_passageiros=2)

        for pax in comprar_params.passageiros:
            baker.make(
                Passagem,
                buseiro_internal_id=pax.id,
                travel_internal_id=comprar_params.travel_id,
                status=Passagem.Status.CONFIRMADA,
            )

        CompraRodoviariaSVC(comprar_params).efetua_compra(comprar_params)

    mock_comprar_marketplace.assert_not_called()
    mock_get_trechoclasse_from_buser_django.assert_called_once_with(trecho_classe_id)
    mock_update_trecho_classe_link_task.s.assert_called_once_with(
        eulabs_company.id, trecho_classe_buser_django_infos.json()
    )
    mock_update_trecho_classe_link_task.s.return_value.set.assert_called_once_with(
        queue=DefaultQueueNames.POS_COMPRA_UPDATE_PRICE
    )
    mock_update_trecho_classe_link_task.s.return_value.set.return_value.apply_async.assert_called_once_with()

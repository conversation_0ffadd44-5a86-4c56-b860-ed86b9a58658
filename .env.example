# PostgreSQL (db default)
DB_HOST=localhost
DB_NAME=buser
DB_USER=buser
DB_PASS=buser

# PostgreSQL (db rodoviaria)
RODOVIARIA_DB_HOST=localhost
RODOVIARIA_DB_NAME=buser_rodoviaria
RODOVIARIA_DB_USER=buser
RODOVIARIA_DB_PASS=buser

# RabbitMQ
CELERY_BROKER_URL=pyamqp://buser:buser@localhost:5672//

# Redis
REDIS_URL=redis://localhost:6379/0

# Constance faz o papel das global settings. Localmente armazena em memória, em prod no redis.
CONSTANCE_BACKEND='constance.backends.memory.MemoryBackend'

# Result Backend
RESULT_BACKEND='redis://'

# Smartbus Sandbox
SMARTBUS_API_ENVIRONMENT=demo

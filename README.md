# Rodoviaria  ![python](https://img.shields.io/badge/Python-3.10-green)



Seja muito bem vindx ao `#squad-marketplace-dev`! :eagle:

Você está aqui para **resolver problemas**, **compartilhar conhecimentos** e **escalar nosso produto** com muita **liberdade** e **autonomia** desde o discovery com stakeholders até o acompanhamento da solução entregue pós deploy.

Só bora!

- [KPIs](#kpis)
- [Regras de negócio](#regras-de-negócio)
- [Visão de produto](#visão-de-produto)
- [Setup](#setup)
- [Sentry](#erros-em-produção-sentry)
- [Honeycomb](#análise-de-performance-honeycomb)
- [Kibana](#logs-kibana)
- [Grafana](#saúde-das-máquinas-grafana)
- [Filas](#filas-rabbitmq)
- [Fluxo de onboarding de novas empresas](#fluxo-de-onboarding-de-novas-empresas)
- [Diagrama do banco](#diagrama-do-banco)
- [Diagrama da arquitetura de código](#diagrama-da-arquitetura-de-código)
- [Diagrama da arquitetura](#diagrama-da-arquitetura)
- [Admin](#admin)
- [Documentação](#documentação)
- [Rollback](#rollback)


# KPIs
- Dashboard [Metabase](https://metabase.buser.com.br/dashboard/460-kpis-marketplace) com os principais big numbers.

# Regras de negócio
Hoje temos 3 tipos de empresas de rodoviária que vendem suas passagens no nosso site, são eles:
- Totalmente integrado: todas as vendas e cancelamentos são feitos automaticamente no sistema do parceiro. Sem interferência humana.
- Manual: as empresas param de vender X horas antes da saída dos grupos, e nesse momento espelham as vendas feitas na Buser para o próprio sistema.
- Manual do nosso lado: mesmo que o manual, mas quem faz o espelhamento é um time na Buser.

Sobre as integradas, temos [features e regras de negócio](https://docs.google.com/spreadsheets/d/1vCdefmFJDIANMaD9IL8eocpJ27OvOEZn2ZnlRhBYbeE/edit#gid=1807459523) já implementadas

# Visão de produto
- Nosso potencial vai além de apenas integrações para revenda de passagens. Saiba mais na nossa [doc](https://app.clickup.com/30953227/v/dc/xgkrb-34423/xgkrb-52563).


# Setup
## Virtual environment

Por boa prática, principalmente para usuários do Linux (já que o Python é essencial ao Linux, mudar a versão global pode gerar incompatabilidade com pacotes do sistema operacioal) utilizamos localmente um ambiente virtual para rodar o projeto. Aconselhamos usar o `pyenv` pela simplicidade de troca de versões entre diversos projetos que pode ter em sua máquina. Para instalar acesse o [pyenv-installer](https://github.com/pyenv/pyenv-installer).

Reinicie o terminal e você já deve conseguir usar o pyenv.

* Instale a versão do 3.11
```
pyenv install 3.11
```

* Crie um virtualenv com a versão instalada:
```
pyenv virtualenv 3.11 rodoviaria
```

* Instale as dependências dos projeto com o comando:

```
pip install -r requirements.txt
```

* Caso deseje adicionar uma nova dependência no projeto, o rodoviária utiliza o [pip-tools](https://github.com/jazzband/pip-tools) para gestão de dependências. Instale-o dentro do seu virtualenv com o comando. Aliáis, toda vez que alguma dependência é atualizada você deve atualizar seu ambiente virtual local com esse mesmo comando:

```
pip install pip-tools
```

* Atualize os hashes das dependências para que o projeto tenha de fato novas dependências adicionadas.

```
inv requirements
```

## Lint

Usamos o [ruff](https://docs.astral.sh/ruff/linter/) na etapa de check de lint e para formatação do código.  
Para verificar os erros de linter e formatação sem corrigir automaticamente:
```shell
inv lint
```

- Para formatar o código e corrigir automaticamente:
```shell
inv format
```

- Deixe configurado ignorar reformatações de código no `git-blame` para que o código contribuído por outro membro do time não tenha indicação sobrescrita:


```shell
git config blame.ignoreRevsFile .git-blame-ignore-revs
```

## Criando o banco buser_rodoviaria

A ideia é criar o banco do rodoviária junto ao banco do buser_django no mesmo container. Portanto, vamos precisar rodar alguns comandos pelo terminal do buser_django.

- Crie um banco postgresql com nome buser_rodoviaria localmente

No terminal do projeto do buser_django, rode o comando `inv dkdb` para subir o postgresql.

```shell
inv dkdb
```
Seu postgresql deve estar up!
- Rode o comando a seguir para criar o banco:

```shell
docker compose exec postgresql14 psql -U buser -c 'CREATE DATABASE buser_rodoviaria;'
```

Após isso volte para o projeto `rodoviaria` e rode o comando `./manage.py migrate --database rodoviaria` para aplicar as migrações do rodoviaria.

## Conectando o buser_django ao rodoviaria

- Nossas integrações precisam saber corretamente o timezone de cidades vendidas pelos nossos parceiros para que possamos vender as viagens corretamente e ainda entender mais corretamente a operação dos parceiros. Um jeito de resolver é nós termos, de antemão, no nosso banco, todos os registros de cidades a serem vendidas com seus respectivos timezones. Isso foi resolvido no buser-django exatamente dessa forma, por isso, em vez de nós duplicarmos a nossa tabela `core_cidade`, nós, em produção, temos um job async que copia os dados da tabela `core_cidade` para a tabela `rodoviaria_cidadeinternal` e esta guarda os timezones corretos.

- No seu ambiente local, porém, fizemos um comando para copiar os dados do seu projeto buser-django local. Espera-se que já tenha rodado do [devdb](https://gitlab.buser.com.br/buser/buser_django#rodando-a-aplica%C3%A7%C3%A3o) para que haja dados a serem copiados.


- Em produção, existe um job async que traz os dados, mas localmente, no projeto rodoviaria, rode:

  ```
  ./manage.py copy_cidade_internal
  ```

- Configure o buser_django para que o buser_django de fato faça requisições ao projeto rodoviaria:
  - variável de ambiente `RODOVIARIA_API_URL`
  ```shell
  RODOVIARIA_API_URL=http://localhost:8001/rodoviaria
  ```

  - Crie a variável `rodoviaria_prod: true` no modelo `GlobalSetting` via admin do buser_django

# Erros em produção (Sentry)
Para o monitoramento de erros não tratados em produção, utilizamos o [Sentry](https://sentry.io/organizations/buser/issues/?project=5872905).

# Análise de Performance (Honeycomb)
Utilizamos o honeycomb, e recomendamos fortemente que todos entrem regularmente para identificar possíveis gargalos e melhorias.

[Dataset](https://ui.honeycomb.io/buser/environments/prod/datasets/rodoviaria)

[Dashboard](https://ui.honeycomb.io/buser/environments/prod/board/ymMupYnu5cA/Buser-Rodoviaria)

# Logs (Kibana)
- [nginx](https://kibana.buser.com.br/app/kibana#/discover?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))&_a=(columns:!(_source),filters:!(),index:'64bf3a20-14b6-11ec-906b-6d3897207679',interval:auto,query:(language:kuery,query:''),sort:!()))

- [Quantidade de requisições por API dos parceiros](https://kibana.buser.com.br/app/kibana#/dashboard/f284efb0-7219-11ec-89d0-5d76a0fd1646?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))&_a=(description:'',filters:!(),fullScreenMode:!f,options:(hidePanelTitles:!f,useMargins:!t),query:(language:kuery,query:''),timeRestore:!f,title:'Requests%20Rodoviaria',viewMode:view))

- [Dashboard de erros](https://kibana.buser.com.br/app/kibana#/dashboard/765673d0-7865-11ec-89d0-5d76a0fd1646?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))&_a=(description:'',filters:!(),fullScreenMode:!f,options:(hidePanelTitles:!f,useMargins:!t),query:(language:kuery,query:''),timeRestore:!f,title:'Rodovi%C3%A1ria%20Errors%20Dashboard',viewMode:view))


# Saúde das máquinas (Grafana)
- [Dashboard](https://grafana.buser.com.br/d/o1oWE_L7k/rodoviaria-prod?orgId=1&refresh=5s)

# Filas (RabbitMQ)
- Cada empresa tem sua [fila de trabalho](https://rabbitmq-cluster.buser.com.br/#/queues) para atualização semanal da sua operação.


# Fluxo de onboarding de novas empresas
- O nosso fluxo de onboarding de novas empresa para os sistemas já integrados é independente de dev, conforme em:

![fluxo-onboarding](docs/onboarding-diagram.png)

# Diagrama do banco
![diagrama-banco](docs/rodoviaria_diagram.png)

# Diagrama da arquitetura de código
![arquitetura-codigo](docs/arq-codigo.png)

# Diagrama da arquitetura
![arquitetura](docs/arq.png)


# Admin
Temos um [subdomínio](https://rodoviaria.admin.buserdev.com.br/admin) para acessar o banco de qualquer lugar com seu @buser.

# Documentação
NinjaAPI já nos garante uma [documentação](https://rodoviaria.admin.buserdev.com.br/rodoviaria/docs).


# Rollback
Hoje a maneira mais rápida e fácil de fazer um rollback é executando o deploy de uma versão anterior a do deploy com problema.

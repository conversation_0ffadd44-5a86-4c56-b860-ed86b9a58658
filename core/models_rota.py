from datetime import timed<PERSON><PERSON>
from decimal import Decimal as D

from django.db import models

from .models_commons import Cidade


class LocalEmbarque(models.Model):
    id: int
    cidade_id: int
    cidade = models.ForeignKey(Cidade, on_delete=models.CASCADE)


class Rota(models.Model):
    id: int
    ativo = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    distancia_total = models.IntegerField(null=True, blank=True)
    duracao_total = models.DurationField(null=True, blank=True)
    ufs_intermediarios = models.CharField(max_length=128, null=True, blank=True)
    origem = models.ForeignKey(LocalEmbarque, related_name="origem", on_delete=models.CASCADE)
    destino = models.ForeignKey(LocalEmbarque, related_name="destino", on_delete=models.CASCADE)
    pedagio_por_eixo = models.DecimalField(max_digits=8, decimal_places=2, null=True)

    class Meta:
        indexes = [
            models.Index(fields=["ativo"]),
            models.Index(fields=["updated_at"]),
        ]

    def get_itinerario(self):
        return self.itinerario.select_related("local", "local__cidade").all()

    def get_itinerario_name(self):
        return " - ".join([c.local.cidade.name for c in self.get_itinerario()])

    def first_checkpoint(self):
        return self.itinerario.first()

    def last_checkpoint(self):
        return self.itinerario.last()

    def __str__(self):
        checkpoints = list(self.get_itinerario())
        return "-".join([c.local.cidade.sigla for c in checkpoints])


class Checkpoint(models.Model):
    id: int
    rota_id: int
    rota = models.ForeignKey(Rota, related_name="itinerario", on_delete=models.CASCADE)
    local_id: int
    local = models.ForeignKey(LocalEmbarque, on_delete=models.CASCADE)
    idx = models.IntegerField()
    distancia_km = models.IntegerField(null=True, blank=True)
    duracao = models.DurationField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    tempo_embarque = models.DurationField(default=timedelta(minutes=20))

    class Meta:
        unique_together = (
            "rota",
            "idx",
        )
        ordering = (
            "rota",
            "idx",
        )


class TrechoVendido(models.Model):
    id: int
    rota_id: int
    rota = models.ForeignKey(Rota, related_name="trechos_vendidos", on_delete=models.CASCADE)
    origem_id: int
    origem = models.ForeignKey(LocalEmbarque, related_name="trechos_vendidos_origem", on_delete=models.CASCADE)
    destino_id: int
    destino = models.ForeignKey(LocalEmbarque, related_name="trechos_vendidos_destino", on_delete=models.CASCADE)
    preco_rodoviaria = models.DecimalField(max_digits=12, decimal_places=2, default=D("100.00"))

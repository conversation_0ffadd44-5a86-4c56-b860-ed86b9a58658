from django.db import models

from .models_company import Company
from .models_rota import Rota, TrechoVendido


class Grupo(models.Model):
    id: int
    company_id: int
    company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.CASCADE)
    rota_id: int
    rota = models.ForeignKey(Rota, null=True, on_delete=models.CASCADE)
    datetime_ida = models.DateTimeField()
    modelo_venda = models.CharField(max_length=15, default="marketplace")
    status = models.CharField(max_length=32, default="pending")
    confirming_probability = models.CharField(max_length=32, default="very_low")
    is_extra = models.BooleanField(default=False, null=True)
    rotina_onibus_id = models.IntegerField(null=True, blank=True)


class GrupoClasse(models.Model):
    id: int
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    tipo_assento = models.CharField(max_length=64, blank=True, null=True)
    capacidade = models.IntegerField()
    pessoas = models.IntegerField(default=0, null=True)
    closed = models.BooleanField(default=False)
    closed_reason = models.TextField(null=True, blank=True)


class TrechoClasse(models.Model):
    id: int
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    grupo_classe_id: int
    grupo_classe = models.ForeignKey(GrupoClasse, on_delete=models.CASCADE)
    trecho_vendido_id: int
    trecho_vendido = models.ForeignKey(TrechoVendido, on_delete=models.CASCADE)
    datetime_ida = models.DateTimeField(null=True)
    preco_rodoviaria = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    max_split_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    ref_split_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    duracao_ida = models.DurationField(null=True)
    pessoas = models.IntegerField(default=0)
    vagas = models.IntegerField(null=True)

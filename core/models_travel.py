from django.db import models

from .models_grupo import Grupo, TrechoClasse, TrechoVendido


class Travel(models.Model):
    id: int
    grupo_id: int
    grupo = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    trecho_classe_id: int
    trecho_classe = models.ForeignKey(TrechoClasse, on_delete=models.CASCADE)
    trecho_vendido_id: int
    trecho_vendido = models.ForeignKey(TrechoVendido, on_delete=models.CASCADE)
    reservation_code = models.CharField(max_length=6, null=True, blank=True)
    status = models.CharField(max_length=32, default="pending")
    movido_em = models.DateTimeField(null=True)
    movido_de_id: int
    movido_de = models.ForeignKey(
        Grupo,
        on_delete=models.SET_NULL,
        null=True,
        related_name="travel_grupo_anterior",
    )

    class Meta:
        ordering = ["grupo__datetime_ida"]


class Buseiro(models.Model):
    id: int
    name = models.Char<PERSON>ield(max_length=512)
    cpf = models.Char<PERSON>ield(max_length=11, null=True, blank=True)
    rg_number = models.Char<PERSON>ield(max_length=64, null=True, blank=True)


class Passageiro(models.Model):
    id: int
    travel_id: int
    travel = models.ForeignKey(Travel, on_delete=models.CASCADE)
    buseiro_id: int
    buseiro = models.ForeignKey(Buseiro, on_delete=models.CASCADE)

    class Meta:
        ordering = ("id",)

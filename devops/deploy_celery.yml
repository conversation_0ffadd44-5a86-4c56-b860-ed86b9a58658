---
- hosts: all
  become: yes
  become_user: root
  vars:
    docker_image: "{{ lookup('env', 'CI_IMAGE') }}"
    company_queues: "{{ lookup('file', 'celery_company_queues.txt').splitlines() }}"
    rodoviaria_environment: "{{ lookup('env', 'RODOVIARIA_ENVIRONMENT') }}"
  tasks:
    - name: gera sufixo para o deploy
      command: python3 -c 'import random; print("{:x}".format(random.getrandbits(32)))'
      register: deploy_suffix_cmd
      become: false
      run_once: True
      delegate_to: 127.0.0.1

    - name: deploy_suffix = {{ deploy_suffix_cmd.stdout }}
      tags: [database-migration]
      set_fact:
        deploy_suffix: "{{ deploy_suffix_cmd.stdout }}"

    # remove celerybeat que já está rodando para não registrar tasks duplicadas
    - name: lista containers rodando
      community.docker.docker_host_info:
        containers: yes
      register: docker_host_info

    - name: remove celerybeat antigo
      docker_container:
        name: "{{ item.Id }}"
        state: absent
      loop: "{{ docker_host_info.containers }}"
      when: item.Names[0].startswith('/rodoviaria-celerybeat-')

    # continua com deploy normal
    - name: cria network rodoviaria
      community.general.docker_network:
        name: "rodoviaria-{{ deploy_suffix }}"
        state: present

    - name: roda container rodoviaria-celerybeat
      community.general.docker_container:
        name: "rodoviaria-celerybeat-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: celery -A bp.celery.app beat -S redbeat.RedBeatScheduler
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /tmp:/tmp
        env: &app_env_vars
          RODOVIARIA_ENVIRONMENT: "{{ rodoviaria_environment }}"
          SENTRY_RELEASE: "{{ lookup('env', 'CI_COMMIT_SHORT_SHA') }}"
          DJANGO_DEBUG: 'false'
          PGBOUNCER_HOST: "{{ pgbouncer_host }}"
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celerybeat

    - name: roda container rodoviaria-cron-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-cron-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q celery,bp_cron,bp_ping
          --autoscale=2,1
          -Ofair --prefetch-multiplier=1
          -n cron@%h
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "10"
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-staff-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-staff-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_motorista,bp_alterar_grupo,bp_cadastros_vexado,bp_desbloquear_poltrona,bp_fetch_rotas,bp_staff_update_link,bp_link_trecho_classe_hibrido,bp_cancela_grupo_classe_hibrido
          --pool=gevent
          --autoscale=20,1
          -Ofair --prefetch-multiplier=1
          -n staff@%h
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "50"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-onboarding-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-onboarding-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_descobrir_rotas,bp_trechos_vendidos,bp_rotinas,bp_fetch_data_limite,bp_commit_callback,bp_messenger,bp_descobrir_operacao,bp_buscar_preco_trecho_vendido
          --pool=gevent
          --concurrency=10
          -n onboarding@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "100"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: prod_env - roda containers para cada fila de empresa rodoviaria
      when: rodoviaria_environment == "prod"
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-{{ item[:36] | replace(',','-') | replace('bp_','') }}-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q {{ item }}
          --pool=gevent
          --concurrency=10
          -n {{ item[:36] | replace(',','-') | replace('bp_','') }}@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "50"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery
      loop: "{{ company_queues }}"

    - name: test_env - roda container para todas empresas rodoviaria
      when: rodoviaria_environment == "test"
      vars:
        split_companies: "{{company_queues | join(',')}}"
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-empresas-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q {{ split_companies }}
          --pool=gevent
          --concurrency=10
          -n empresas@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "100"
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-atualizaprecofuturo-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-atualizaprecofuturo-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_link_trechos_classes_atualiza_preco
          --pool=gevent
          --concurrency=10
          -n atualizaprecofuturo@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "20"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-atualizaprecohot-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-atualizaprecohot-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_link_trechos_classes_atualiza_preco_hot
          --pool=gevent
          --concurrency=10
          -n atualizaprecohot@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "20"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-atualizamaiordivergencia-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-atualizamaiordivergencia-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_link_trechos_classes_atualiza_top_divergencias
          --pool=gevent
          --concurrency=10
          -n atualizamaiordivergencia@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "20"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-atualizaprecocheckout-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-atualizaprecocheckout-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_atualiza_preco_checkout
          --pool=gevent
          --concurrency=50
          -n atualizaprecocheckout@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "20"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-atualizaprecosearch-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-atualizaprecosearch-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_buscar_viagens_search
          --concurrency=10
          -n atualizaprecosearch@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "20"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-atualizatrechofuturo-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-atualizatrechofuturo-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_link_trechos_classes,bp_update_price_pos_compra
          --pool=gevent
          --concurrency=10
          -n atualizatrechofuturo@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "20"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-addpax-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-addpax-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_add_pax,bp_add_pax_hibrido,bp_remanejamento
          -n addpax@%h
          --pool=gevent
          --concurrency=10
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "100"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    - name: roda container rodoviaria-rmvpax-celery
      community.general.docker_container:
        restart_policy: "always"
        name: "rodoviaria-rmvpax-celery-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        pull: true
        command: >
          celery
          -A
          bp.celery.app
          worker
          --loglevel=INFO
          --without-heartbeat
          --without-gossip
          -Q bp_cancela_passagem,bp_buscar_dados_bpe
          --pool=gevent
          --concurrency=10
          -n rmvpax@%h
          -Ofair --prefetch-multiplier=1
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        volumes:
          - /metrics:/metrics
        env:
          <<: *app_env_vars
          RODOVIARIA_HONEYCOMB_SAMPLE_RATE: "100"
          PROMETHEUS_METRICS_DIR: /metrics
          PROMETHEUS_MULTIPROC_DIR: /tmp
        pid_mode: host
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: celery

    # Remove outros containers de deploys antigos
    - name: lista containers e networks e volumes docker existentes
      community.docker.docker_host_info:
        containers: yes
        networks: yes
        volumes: yes
      register: docker_host

    - name: remove containers antigos
      docker_container:
        name: "{{ item.Id }}"
        state: absent
        force_kill: yes
      loop: "{{ docker_host.containers }}"
      when: item.Names[0].startswith('/rodoviaria-') and not item.Names[0].endswith(deploy_suffix)

    - name: remove volumes antigos
      docker_volume:
        name: "{{ item.Name }}"
        state: absent
      loop: "{{ docker_host.volumes }}"
      when: item.Name.startswith('rodoviaria-') and not item.Name.endswith(deploy_suffix)

    - name: remove networks antigas
      docker_network:
        name: "{{ item.Name }}"
        state: absent
      loop: "{{ docker_host.networks }}"
      when: item.Name.startswith('rodoviaria-') and not item.Name.endswith(deploy_suffix)

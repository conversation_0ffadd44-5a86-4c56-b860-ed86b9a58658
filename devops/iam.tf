resource "aws_iam_role" "rodoviaria" {
  name = "rodoviaria-${terraform.workspace == "default" ? "test" : "prod"}"
  assume_role_policy = data.aws_iam_policy_document.instance-assume-role-policy.json

  inline_policy {
    name   = "ecr-readonly-rodoviaria"
    policy = data.aws_iam_policy_document.ecr_readonly_rodoviaria.json
  }

  inline_policy {
    name   = "secretsmanager-readonly-rodoviaria"
    policy = data.aws_iam_policy_document.secretsmanager_readonly_rodoviaria.json
  }
}

resource "aws_iam_instance_profile" "rodoviaria_profile" {
    name = "rodoviaria-${terraform.workspace == "default" ? "test" : "prod"}-ec2-profile"
    role = aws_iam_role.rodoviaria.name
}

data "aws_iam_policy_document" "instance-assume-role-policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

resource "aws_iam_role_policy_attachment" "attach_consul_agent_policy" {
  role       = aws_iam_role.rodoviaria.name
  policy_arn = "arn:aws:iam::557869972717:policy/ConsulAgent"
}

data "aws_iam_policy_document" "ecr_readonly_rodoviaria" {
  statement {
    actions   = [
        "ecr:BatchCheckLayerAvailability",
        "ecr:GetDownloadUrlForLayer",
        "ecr:GetRepositoryPolicy",
        "ecr:DescribeRepositories",
        "ecr:ListImages",
        "ecr:DescribeImages",
        "ecr:BatchGetImage",
        "ecr:GetLifecyclePolicy",
        "ecr:GetLifecyclePolicyPreview",
        "ecr:ListTagsForResource",
        "ecr:DescribeImageScanFindings"
    ]
    resources = ["arn:aws:ecr:us-east-2:557869972717:repository/rodoviaria"]
  }

  statement {
    actions   = [
        "ecr:GetAuthorizationToken"
    ]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "secretsmanager_readonly_rodoviaria" {
  statement {
    actions   = [
        "secretsmanager:GetResourcePolicy",
        "secretsmanager:GetSecretValue",
        "secretsmanager:DescribeSecret",
        "secretsmanager:ListSecretVersionIds"
    ]
    resources = ["arn:aws:secretsmanager:*:557869972717:secret:rodoviaria/${terraform.workspace == "default" ? "test" : "prod"}*"]
  }
}


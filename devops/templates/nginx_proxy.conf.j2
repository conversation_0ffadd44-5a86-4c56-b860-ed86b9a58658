log_format enhanced '$remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent $request_length "$http_referer" "$http_user_agent" $request_time $upstream_response_time';

server {
    listen  8000 default_server;

    charset     utf-8;
    client_max_body_size 75M;   # adjust to taste
    large_client_header_buffers 4 64k;

    location / {
        proxy_pass http://localhost:{{ rodoviaria_port.stdout }};
        proxy_read_timeout 420;
        include    /etc/nginx/proxy_params;
    }

    access_log /var/log/nginx/access.log enhanced;
}

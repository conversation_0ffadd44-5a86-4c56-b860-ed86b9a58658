resource "aws_security_group" "rodoviaria" {
  name        = "rodoviaria"
  description = "Rodoviaria SG"
  vpc_id      = terraform.workspace == "default" ? "vpc-07fe340e7fb086e25" : "vpc-3b686f5f"

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    from_port = 8000
    to_port   = 8000
    protocol  = "tcp"
    security_groups = terraform.workspace == "default" ? [
      "sg-0c16fcf18ccf5ec7e" // ec2-buserdjango-test
    ] : [
      "sg-00bd56f38f75bb104" // ec2-buserdjango-prod
    ]
  }
}

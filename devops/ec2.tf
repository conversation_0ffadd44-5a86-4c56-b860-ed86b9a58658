resource "aws_instance" "rodoviaria" {
  ami           = data.aws_ami.buser_base.id
  instance_type = "t3a.small"
  key_name      = "${terraform.workspace == "default" ? "budevops" : "buser_sp" }"
  subnet_id     = terraform.workspace == "default" ? "subnet-02f939e62ed3189df" : "subnet-227fe37a"
  iam_instance_profile = aws_iam_instance_profile.rodoviaria_profile.name

  vpc_security_group_ids = terraform.workspace == "default" ? [
      aws_security_group.rodoviaria.id,
      "sg-038ab4f3cf1c26253",  // consul-cluster
      "sg-097600c5a44e9c062",  // trafego-sjc-vpn
      "sg-0de1fd712b0942aca",  // monitor
      "sg-0e672212281e7576b",  // ec2-gitlab-deploy-target
  ] : [
      aws_security_group.rodoviaria.id,
      "sg-0e13baa9be50f801d",  // consul-cluster
      "sg-00fa2bc268b5b40d8",  // trafego-sjc-vpn
      "sg-059d8af64ef816aed",  // monitor
      "sg-045f317efcc559273",  // ec2-gitlab-deploy-target
  ]

  root_block_device {
    volume_size = 80
  }

  tags = {
    Name = "rodoviaria-${terraform.workspace == "default" ? "test" : "prod"}"
  }
}

data "aws_ami" "buser_base" {
  most_recent = true

  filter {
    name   = "name"
    values = ["buser-base_*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  owners = ["557869972717"] # buserbrasil
}


---
- hosts: all
  become: yes
  become_user: root
  vars:
    docker_image: "{{ lookup('env', 'CI_IMAGE') }}"
    rodoviaria_environment: "{{ lookup('env', 'RODOVIARIA_ENVIRONMENT') }}"
  tasks:
    - name: instala scripts utilitários
      tags: [scripts]
      copy:
        src: "{{ item }}"
        dest: "/usr/local/bin/{{ item | basename }}"
        mode: '0775'
      with_items:
        - ./scripts/check-port.sh

    - name: gera sufixo para o deploy
      command: python3 -c 'import random; print("{:x}".format(random.getrandbits(32)))'
      register: deploy_suffix_cmd
      become: false
      run_once: True
      delegate_to: 127.0.0.1

    - name: deploy_suffix = {{ deploy_suffix_cmd.stdout }}
      tags: [database-migration]
      set_fact:
        deploy_suffix: "{{ deploy_suffix_cmd.stdout }}"

    # continua com deploy normal
    - name: cria network rodoviaria
      community.general.docker_network:
        name: "rodoviaria-{{ deploy_suffix }}"
        state: present

    - name: busca uma porta para o busernginx
      command: check-port.sh 7000 7900
      register: rodoviaria_port

    - name: roda container rodoviaria
      community.general.docker_container:
        name: "rodoviaria-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        sysctls:
          net.core.somaxconn: 1000
        pull: true
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        ports:
          - "{{ rodoviaria_port.stdout }}:8000"
        env: &app_env_vars
          RODOVIARIA_ENVIRONMENT: "{{ rodoviaria_environment }}"
          SENTRY_RELEASE: "{{ lookup('env', 'CI_COMMIT_SHORT_SHA') }}"
          DJANGO_DEBUG: 'false'
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: django
        restart_policy: unless-stopped

    - name: migra banco de dados
      tags: [database-migration]
      run_once: true
      docker_container:
        name: "rodoviaria-db-migration-{{ deploy_suffix }}"
        image: "{{ docker_image }}"
        command: python ./manage.py migrate --no-input --database rodoviaria
        detach: false
        cleanup: yes
        network_mode: "rodoviaria-{{ deploy_suffix }}"
        networks:
          - name: "rodoviaria-{{ deploy_suffix }}"
        container_default_behavior: no_defaults
        env:
          <<: *app_env_vars
        labels:
          namespace: rodoviaria
          environment: "{{ rodoviaria_environment }}"
          service: migratedb
        log_driver: json-file
        output_logs: yes
      register: migratedb_cmd

    - name: output da migração do banco de dados
      tags: [database-migration]
      run_once: true
      debug:
        msg: "{{ migratedb_cmd.container.Output.split('\n') }}"

    - name: testa antes de mudar o trafego
      uri:
        url: "{{ item }}"
      register: health_check_result
      retries: 5
      delay: 3
      until: health_check_result.status == 200
      loop:
        - "http://localhost:{{ rodoviaria_port.stdout }}/status"
        # - "http://localhost:{{ rodoviaria_port.stdout }}/ping"

    # nginx proxy
    - name: instala nginx
      apt:
        name: nginx
        state: present
        update_cache: yes

    - name: reconfigura nginx para redirecionar o trafego
      template:
        src: "./templates/nginx_proxy.conf.j2"
        dest: /etc/nginx/sites-enabled/default

    - name: testa configuração do nginx
      command: nginx -t

    - name: reload nginx
      service:
        name: nginx
        state: reloaded

    - name: registra buserpassagens como serviço no consul
      community.general.consul:
        service_name: buserpassagens
        token: anonymous
        service_port: 8000
        interval: 60s
        http: http://localhost:8000/status

    # Remove outros containers de deploys antigos
    - name: lista containers e networks e volumes docker existentes
      community.docker.docker_host_info:
        containers: yes
        networks: yes
        volumes: yes
      register: docker_host

    - name: remove containers antigos
      docker_container:
        name: "{{ item.Id }}"
        state: absent
        force_kill: yes
      loop: "{{ docker_host.containers }}"
      when: item.Names[0].startswith('/rodoviaria-') and not item.Names[0].endswith(deploy_suffix)

    - name: remove volumes antigos
      docker_volume:
        name: "{{ item.Name }}"
        state: absent
      loop: "{{ docker_host.volumes }}"
      when: item.Name.startswith('rodoviaria-') and not item.Name.endswith(deploy_suffix)

    - name: remove networks antigas
      docker_network:
        name: "{{ item.Name }}"
        state: absent
      loop: "{{ docker_host.networks }}"
      when: item.Name.startswith('rodoviaria-') and not item.Name.endswith(deploy_suffix)

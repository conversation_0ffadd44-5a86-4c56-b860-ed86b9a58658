#!/bin/bash

prefix=$1

start_time=$(date -d "$CI_JOB_STARTED_AT" +'%s')
end_time=$(date +'%s')
message="[$prefix] $CI_COMMIT_TITLE"
job_url=$CI_JOB_URL

curl -X POST \
  -d "{\"start_time\": $start_time, \
      \"end_time\": $end_time, \
      \"message\": \"$message\", \
      \"type\": \"deploy $CI_ENVIRONMENT_SLUG\", \
      \"url\": \"$job_url\"}" \
  -H 'Content-Type: application/json' \
  -H "X-Honeycomb-Team: $HONEYCOMB_TEAM_KEY" \
  https://api.honeycomb.io/1/markers/rodoviaria

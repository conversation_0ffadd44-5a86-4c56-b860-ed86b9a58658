[tool.ruff]
line-length = 120
extend-exclude = ["migrations"]
lint.select = [
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "E",  # pycodestyle errors
    "F",  # pyflakes
    "G",  # flake8-logging-format
    "I",  # isort
    "RUF100", # unused-noqa
    "UP",  # pyupgrade
    "W",  # pycodestyle warnings
    "ASYNC",  # Async related issues
    # "PERF",  # performance issues
    "S",  # Security
]
lint.ignore = [
    "B008",  # do not perform function calls in argument defaults
    "S101",
    "ASYNC109",  # Async function definition with a `timeout` parameter
]

[tool.ruff.lint.per-file-ignores]
"rodoviaria/tests/*" = ["S106", "S311"]
"rodoviaria/fixtures" = ["S106"]
# E501 line-lenght
"rodoviaria/tests/praxio/mocker.py" = ["E501"]
"rodoviaria/tests/totalbus/mocker.py" = ["E501"]
"rodoviaria/tests/vexado/mocker.py" = ["E501"]
"rodoviaria/tests/guichepass/mocker.py" = ["E501"]
# F401 unused imports
"rodoviaria/models/__init__.py" = ["F401"]
"instrumentation/honeycomb/__init__.py" = ["F401"]

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "bp.settings"
addopts = "--migrateci --reuse-db"
python_files = ["tests.py", "test_*.py", "*_tests.py"]
norecursedirs = [
    ".*",
    "data/postgresql",
    "dkdata",
    "devops",
    "docs",
    "docker",
    "nginx",
]
markers = [
    "slow"
]

[tool.coverage.run]
branch=true
omit = ["../.virtualenv/*"]

[tool.coverage.report]
fail_under=94.50
precision=2
skip_covered=true
show_missing=true

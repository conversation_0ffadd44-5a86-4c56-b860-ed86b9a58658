version: '3'
services:

  postgres:
    image: postgis/postgis:14-3.2-alpine
    command: ["postgres", "-c", "fsync=off", "-c", "synchronous_commit=off", "-c", "full_page_writes=off", "-c", "autovacuum=off"]
    volumes:
      - ./docker/compose/postgres/init-rodoviaria-db.sh:/docker-entrypoint-initdb.d/init-rodoviaria-db.sh:ro
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: buser
      POSTGRES_USER: buser
      POSTGRES_PASSWORD: buser
    ports:
      - 5432:5432

  memcached:
    image: memcached
    ports:
      - 11211:11211

  rabbit:
    image: rabbitmq:3.9-management-alpine
    hostname: rabbitmq
    volumes:
      - ./docker/compose/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
      - rabbit-data:/var/lib/rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: buser
      RABBITMQ_DEFAULT_PASS: buser
    ports:
      - 5672:5672
      - 15672:15672

  redis:
    image: redis:6.2.13
    volumes:
      - redis-data:/data
    ports:
      - 6379:6379

volumes:
  postgres-data:
  rabbit-data:
  redis-data:
